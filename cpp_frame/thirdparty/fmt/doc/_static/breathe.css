
/* -- breathe specific styles ----------------------------------------------- */

/* So enum value descriptions are displayed inline to the item */
.breatheenumvalues li tt + p {
  display: inline;
}

/* So parameter descriptions are displayed inline to the item */
.breatheparameterlist li tt + p {
  display: inline;
}

.container .breathe-sectiondef {
  width: inherit;
}

.github-btn {
  border: 0;
  overflow: hidden;
}

.jumbotron {
  background-size: 100% 4px;
  background-repeat: repeat-y;
  color: white;
  text-align: center;
}
