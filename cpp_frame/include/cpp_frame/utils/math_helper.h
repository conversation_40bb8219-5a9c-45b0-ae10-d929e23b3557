#include <math.h>

#include <algorithm>

#define EPSILON (1e-6)
#define DOUBLEMAX (1e16)

namespace cpp_frame {
class math_helper {
 public:
  inline static bool is_greater(double x, double y) {
    return std::isgreater(x - y, EPSILON);
  }

  inline static bool is_less(double x, double y) {
    return std::isless(x - y, EPSILON * -1);
  }

  inline static bool is_equal(double x, double y) {
    return std::abs(x - y) <= EPSILON * std::abs(x);
  }

  inline static bool is_greater_equal(double x, double y) {
    return is_greater(x, y) || is_equal(x, y);
  }

  inline static bool is_less_equal(double x, double y) {
    return is_less(x, y) || is_equal(x, y);
  }

  inline static bool is_zero(double x) { return is_equal(x, 0.0); }

  inline static bool is_too_large(double x) { return is_greater(x, DOUBLEMAX); }

  inline static bool is_valid_price(double price) {
    return !is_less_equal(price, 0.0) && !is_too_large(price);
  }

  inline static double round_up(double x, double min_increment) {
    double remainder = std::fmod(x, min_increment);
    if (remainder == 0)
      return x;
    else
      return x + min_increment - remainder;
  }

  inline static double round_down(double x, double min_increment) {
    double remainder = std::fmod(x, min_increment);
    if (remainder == 0)
      return x;
    else
      return std::floor(x / min_increment) * min_increment;
  }

  inline static double round_nearest(double x, double min_increment) {
    double up = round_up(x, min_increment);
    double down = round_down(x, min_increment);
    return (std::fabs(x - down) > std::fabs(up - x)) ? up : down;
  }
};
}  // namespace cpp_frame