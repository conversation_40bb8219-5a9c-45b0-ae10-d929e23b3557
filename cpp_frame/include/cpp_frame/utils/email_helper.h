#pragma once

#include <cpp_frame/http_client/http_client.h>

#include <functional>
#include <memory>
#include <string>
#include <vector>

namespace cpp_frame::utils {

// 邮件配置
struct email_config {
  std::string smtp_server;
  std::string smtp_port;
  std::string username;
  std::string password;
  std::string from_email;
  std::vector<std::string> to_emails;
};

// 邮件消息
struct email_message {
  std::string subject;
  std::string body;
};

// 邮件发送回调
using email_callback =
    std::function<void(bool success, const std::string& error_msg)>;

// 邮件发送工具类
class email_helper {
 public:
  email_helper(const email_config& config,
               const boost::asio::io_context::executor_type& exec);
  ~email_helper();

  // 发送邮件
  void send_email(const email_message& msg, email_callback callback = nullptr);

 private:
  // 构建邮件内容
  std::string build_email_content(const email_message& msg);

  // Base64编码
  std::string base64_encode(const std::string& input);

 private:
  email_config config_;
  boost::asio::io_context::executor_type exec_;
  std::unique_ptr<cpp_frame::http::http_client> http_client_;
};

}  // namespace cpp_frame::utils
