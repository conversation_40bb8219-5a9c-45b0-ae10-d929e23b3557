#pragma once
#include <dlfcn.h>

#include <filesystem>
#include <functional>
#include <iostream>
#include <regex>
#include <string>
#include <unordered_map>
#include <vector>

namespace cpp_frame {
typedef void *so_handle_type;
class so_loader {
 public:
  typedef std::function<void(const char *so_name)> on_loaded_so_callback;

 public:
  so_loader();
  ~so_loader();
  void load(const char *dir, const char *filter = NULL);
  int load_file(const char *file_name);

  void *create_obj(const char *so_name);
  void destroy_obj(const char *so_name, void *obj);

  template <typename CALLBACK>
  void set_on_loaded_callback(CALLBACK cb) {
    callback_ = new on_loaded_so_callback(cb);
  }

  template <typename FUNC_PTR>
  FUNC_PTR get_func_ptr(const char *so_name, const char *func_name) {
    return (FUNC_PTR)get_so_func(so_name, func_name);
  }

 private:
  void *get_so_func(so_handle_type handle, const char *func_name);
  void *get_so_func(const char *so_name, const char *func_name);

 private:
  class so_func;
  typedef std::unordered_map<std::string, so_func *> SO_FUNC_MAP;
  SO_FUNC_MAP so_funcs_;
  std::vector<so_func *> so_vector_;
  on_loaded_so_callback *callback_;
};
}  // namespace cpp_frame