#pragma once
#include <stdint.h>
#include <sys/time.h>
#include <time.h>

#include <chrono>
#include <iomanip>
#include <sstream>
#include <string>

namespace cpp_frame {
class date {
 public:
  inline static uint64_t get_current_mil_sec() {
    struct timeval tv;
    gettimeofday(&tv, nullptr);
    return tv.tv_sec * 1000 + tv.tv_usec / 1000;
  }
  inline static uint64_t get_current_macro_sec() {
    struct timeval tv;
    gettimeofday(&tv, nullptr);
    return tv.tv_sec * 1000000 + tv.tv_usec;
  }
  inline static uint64_t get_current_nano_sec() {
    timespec ts;
    ::clock_gettime(CLOCK_REALTIME, &ts);
    return ts.tv_sec * 1000000000 + ts.tv_nsec;
  }
  static char *get_timestamp(char *timestamp, int len) {
    time_t t;
    time(&t);
    struct tm *ptm = gmtime(&t);
    strftime(timestamp, len, "%FT%T.123Z", ptm);
    return timestamp;
  }
  static inline std::string ms_ts_to_readable(int64_t ms_ts) {
    using namespace std::chrono;
    system_clock::time_point tp{milliseconds(ms_ts)};
    time_t t = system_clock::to_time_t(tp);
    auto ms = ms_ts % 1000;
    std::ostringstream oss;
    oss << std::put_time(localtime(&t), "%Y-%m-%d %H:%M:%S") << "."
        << std::setfill('0') << std::setw(3) << ms;
    return oss.str();
  }
};
}  // namespace cpp_frame