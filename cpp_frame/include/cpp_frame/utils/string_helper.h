#include <string>
#include <string_view>
#include <vector>

namespace cpp_frame {
class string_helper {
 public:
  static std::vector<std::string_view> split(const std::string_view str_view,
                                             char sep) {
    return split<std::string_view>(str_view, sep);
  }
  static std::vector<std::string_view> split(const std::string &str, char sep) {
    return split<std::string>(str, sep);
  }

 private:
  template <typename T>
  static std::vector<std::string_view> split(const T &str, char sep) {
    std::vector<std::string_view> columns;
    size_t begin = 0;
    while (begin < str.size()) {
      size_t end = str.find(sep, begin);
      if (end == T::npos)
        end = str.size();

      columns.push_back(std::string_view(&str[begin], end - begin));
      begin = end + 1;
    }
    if (str.empty() || *str.rbegin() == sep)
      columns.push_back(std::string_view(&str[0] + begin, 0));

    return columns;
  }
};
}  // namespace cpp_frame