#pragma once

#include <pthread.h>
#include <stdint.h>
#include <stdlib.h>
#include <string.h>

namespace cpp_frame {
class posix_thread {
 public:
  posix_thread(const char *name = nullptr) : is_running_(false) {
    set_name(name);
  }
  template <typename Function>
  posix_thread(Function f, const char *name = nullptr) : is_running_(false) {
    set_name(name);
    start_thread(new func<Function>(f, thread_name_));
  }
  template <typename Function>
  void start(Function f) {
    start_thread(new func<Function>(f, thread_name_));
  }
  ~posix_thread();
  void set_name(const char *name);
  void join();
  const char *get_name();
  bool thread_status() { return (volatile bool)is_running_; }

  static void exit();
  static int cpu_num();
  static void bind_to(int cpu_no);
  static uint16_t threadid();
  static int thread_sysid();

  using tls_key_t = pthread_key_t;
  static int create_tls_key(tls_key_t *key, void (*desctructor)(void *));
  static int set_tls_value(tls_key_t key, const void *value);
  static void *get_tls_value(tls_key_t key);
  static int delete_tls_key(tls_key_t key);

 private:
  friend void *system_posix_thread_function(void *arg);

  class func_base {
   public:
    func_base(const char *name) : name_(name) {}
    virtual ~func_base() {}
    virtual void run() = 0;
    const char *name() const { return name_; }

   private:
    const char *name_;
  };

  template <typename Function>
  class func : public func_base {
   public:
    func(Function f, const char *name) : func_base(name), f_(f) {}
    virtual void run() { f_(); }

   private:
    Function f_;
  };

  struct auto_func_base_ptr {
    func_base *ptr;
    ~auto_func_base_ptr() { delete ptr; }
  };

  void start_thread(func_base *arg);

  ::pthread_t thread_;
  bool is_running_;
  static thread_local uint16_t thread_id_;
  static thread_local int thread_sysid_;
  char thread_name_[256];
};
}  // namespace cpp_frame
