#pragma once

#include <immintrin.h>
#include <openssl/hmac.h>
#include <string.h>
#include <zlib.h>

#include <iostream>
#include <sstream>
#include <string>
#include <string_view>

namespace cpp_frame {
class hex {
 public:
  hex();
  hex &clear();
  hex &encode(const unsigned char *data, size_t size);
  const std::string & final() const;

 private:
  std::string r_;
  __m128i table_;
  __m128i mask_;
  __m128i dup_index_;
};

class signer {
 public:
  signer(const std::string &key);
  ~signer();
  void update(const std::string_view &s);
  const std::string & final();

 private:
  ::HMAC_CTX *hmac_;
  hex hex_;
};
}  // namespace cpp_frame