#pragma once

#include <atomic>
#include <iostream>
#include <mutex>
#include <thread>
namespace cpp_frame {

class spin_lock {
 public:
  spin_lock() = default;
  spin_lock(const spin_lock &) = delete;
  spin_lock &operator=(const spin_lock) = delete;
  void lock() {  // acquire spin lock
    while (flag.test_and_set()) {
    }
  }
  void unlock() {  // release spin lock
    flag.clear();
  }

 private:
  std::atomic_flag flag;
};
}  // namespace cpp_frame
// spin_lock splock;
// std::lock_guard<spin_lock> lock(splock);
