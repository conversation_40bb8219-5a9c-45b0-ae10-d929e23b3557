#pragma once
#include <cpp_frame/http_client/http_connection.h>
#include <cpp_frame/http_client/http_request.h>

#include <boost/system/error_code.hpp>
#include <cstdint>
#include <functional>
#include <iostream>
#include <memory>
#include <optional>
#include <vector>
namespace cpp_frame::http {
struct http_result_data {
  std::shared_ptr<const http_request> request;
  boost::beast::http::response<boost::beast::http::string_body> result_data;
};

class http_content {
 public:
  http_content(const http_executor_type &exec, const std::string &host,
               const std::string &port, boost::asio::ssl::context_base::method ssl_method, uint64_t id);
  ~http_content();
  std::pair<std::string, std::uint16_t> get_host_and_port();
  void start(
      const http_request &request,
      std::function<void(const http_result_data &, boost::system::error_code)>
          callback);
  void on_http_complete(
      const boost::beast::http::response<boost::beast::http::string_body> &rsp,
      boost::system::error_code ec);
  void complete_request(const boost::system::error_code &ec);
  void finish();
  void cancel();
  void close();
  uint64_t get_id();

 private:
  http_executor_type exec_;
  std::function<void(const http_result_data &, boost::system::error_code)>
      completed_request_callback_;
  boost::asio::deadline_timer timer_;
  http_result_data result_;
  std::unique_ptr<http_connection> connection_;
  std::string host_;
  std::string port_;
  uint64_t id_;
};
}  // namespace cpp_frame::http
