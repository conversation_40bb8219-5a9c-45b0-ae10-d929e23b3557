#pragma once
#include <cpp_frame/http_client/http_client_settings.h>
#include <cpp_frame/http_client/http_connection_pool.h>
#include <cpp_frame/http_client/http_content.h>
#include <cpp_frame/http_client/request_data.h>
#include <stdio.h>
#include <syscall.h>
#include <unistd.h>

#include <map>
#include <memory>
namespace cpp_frame::http {
class request_manager {
 public:
  request_manager(const http_client_settings &settings,
                  const http_executor_type &exec, const std::string &host,
                  const std::string &port,
                  boost::asio::ssl::context_base::method ssl_method);
  ~request_manager() { std::cout << "request_manager end" << std::endl; }

  void execute_waiting_requests();
  void on_request_completed(uint64_t request_id,
                            const http_result_data &http_result_data,
                            http_content *handle, boost::system::error_code ec);
  size_t get_free_conn_cnt();
  void execute_request(request_data request);

 private:
  const http_client_settings settings_;
  http_executor_type exec_;
  http_connection_pool connection_pool_;
  std::map<uint64_t, request_data> requests_;
  uint64_t request_id_{0};
  uint64_t in_progress_request_cnt_{0};
};
}  // namespace cpp_frame::http
