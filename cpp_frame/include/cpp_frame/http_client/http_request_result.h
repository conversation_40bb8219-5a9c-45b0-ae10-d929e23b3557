#pragma once
#include <stdint.h>

#include <algorithm>
#include <boost/beast.hpp>
#include <boost/system/error_code.hpp>
#include <cassert>
#include <cctype>  // tolower
#include <chrono>
#include <iostream>
#include <memory>
#include <string>
#include <system_error>
#include <vector>

namespace cpp_frame::http {
class http_request_result {
 public:
  http_request_result(
      boost::beast::http::response<boost::beast::http::string_body> resp,
      boost::system::error_code ec)
      : response(std::move(resp)), error_code(ec) {}
  boost::beast::http::response<boost::beast::http::string_body> response;
  boost::system::error_code error_code;
};

}  // namespace cpp_frame::http
