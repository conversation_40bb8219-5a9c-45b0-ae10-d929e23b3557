#pragma once
#include <cpp_frame/http_client/http_client_settings.h>
#include <cpp_frame/http_client/http_request.h>
#include <cpp_frame/http_client/request_manager.h>
#include <cpp_frame/utils/date.h>

#include <boost/asio.hpp>
#include <boost/asio/post.hpp>
namespace cpp_frame::http {
class http_client {
 public:
  http_client(const http_client_settings &settings,
              const http_executor_type &exec, const std::string host,
              const std::string port,
              boost::asio::ssl::context_base::method ssl_method =
                  boost::asio::ssl::context::tlsv12_client)
      : exec_(exec),
        request_manager_(settings, exec_, host, port, ssl_method) {}

  ~http_client() { std::cout << "end http_client" << std::endl; }

  template <typename CompletionToken>
  auto execute_request(CompletionToken &&completion_token,
                       http_request request) {
    boost::asio::post(exec_, [this, completion_token, request]() {
      request_data new_request(request, completion_token, exec_);
      new_request.creation_time = cpp_frame::date::get_current_nano_sec();
      request_manager_.execute_request(new_request);
    });
  }

  template <typename CompletionToken>
  auto get(CompletionToken &&completion_token, std::string target,
           std::string body, http_header_type http_headers = {}) {
    http_request request(boost::beast::http::verb::get, target, body,
                         http_headers);
    return execute_request(completion_token, request);
  }

  template <typename CompletionToken>
  auto post(CompletionToken &&completion_token, std::string target,
            std::string body, http_header_type http_headers) {
    http_request request(boost::beast::http::verb::post, target, body,
                         http_headers);
    return execute_request(std::forward<CompletionToken>(completion_token),
                           request);
  }

  template <typename CompletionToken>
  auto del(CompletionToken &&completion_token, std::string target,
           std::string body, http_header_type http_headers) {
    http_request request(boost::beast::http::verb::delete_, target, body,
                         http_headers);
    return execute_request(std::forward<CompletionToken>(completion_token),
                           request);
  }

  template <typename CompletionToken>
  auto put(CompletionToken &&completion_token, std::string target,
           std::string body, http_header_type http_headers) {
    http_request request(boost::beast::http::verb::put, target, body,
                         http_headers);
    return execute_request(std::forward<CompletionToken>(completion_token),
                           request);
  }

  size_t get_free_conn_cnt() { return request_manager_.get_free_conn_cnt(); }

 private:
  http_executor_type exec_;
  request_manager request_manager_;
};
}  // namespace cpp_frame::http
