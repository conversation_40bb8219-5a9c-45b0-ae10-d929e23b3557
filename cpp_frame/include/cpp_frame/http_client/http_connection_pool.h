#pragma once
#include <cpp_frame/fast_fmt_log/fast_fmt_log.h>
#include <cpp_frame/http_client/http_content.h>

#include <cstddef>
#include <list>
#include <memory>
#include <queue>
namespace cpp_frame::http {
class http_connection_pool {
 public:
  http_connection_pool(const http_executor_type &exec, const std::string &host,
                       const std::string &port,
                       boost::asio::ssl::context_base::method ssl_method,
                       size_t max_connect)
      : exec_(exec),
        host_(host),
        port_(port),
        ssl_method_(ssl_method),
        max_connect_cnt_(max_connect) {}
  ~http_connection_pool();
  http_content *get_connection();

  void release_connection(http_content *handle, bool clean_up);
  size_t get_free_conn_cnt();

 private:
  http_executor_type exec_;
  std::string host_;
  std::string port_;
  boost::asio::ssl::context_base::method ssl_method_;
  size_t max_connect_cnt_;
  std::list<std::unique_ptr<http_content>> valid_pool_;
  std::list<std::unique_ptr<http_content>> running_pool_;
  uint64_t client_id_{0};
};
}  // namespace cpp_frame::http
