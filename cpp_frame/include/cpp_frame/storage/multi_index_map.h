#pragma once
#include <cpp_frame/utils/tuple_hash.h>

#include <tuple>
#include <unordered_map>
namespace cpp_frame::storage {

template <typename Key, typename Tp>
class multi_index_map {
 public:
  multi_index_map() = default;
  ~multi_index_map() = default;
  Tp *insert(std::pair<Key, Tp> &&v) {
    auto itr = s_.insert(v);
    return &itr.first->second;
    ;
  }
  void erase(const Key &k) { s_.erase(k); }
  Tp *get(const Key &k) {
    auto itr = s_.find(k);
    if (itr == s_.end()) {
      return nullptr;
    }
    return &itr->second;
  }
  Tp *first() {
    fetech_i_ = s_.begin();
    return next();
  }

  Tp *next() {
    if (fetech_i_ == s_.end()) {
      return nullptr;
    }
    Tp *r = &fetech_i_->second;
    fetech_i_++;
    return r;
  }
  size_t size() const { return s_.size(); }
  void clear() { return s_.clear(); }

 private:
  using UNORDERED_MAP_STORAGE = std::unordered_map<Key, Tp>;
  typename UNORDERED_MAP_STORAGE::iterator fetech_i_;
  UNORDERED_MAP_STORAGE s_;
};
}  // namespace cpp_frame::storage