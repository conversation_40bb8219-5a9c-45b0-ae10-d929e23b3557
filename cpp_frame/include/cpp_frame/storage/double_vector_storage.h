#pragma once
#include <iostream>
#include <vector>

namespace cpp_frame::storage {

template <typename T>
class double_vector_storage {
 public:
  T *find(uint32_t x, uint32_t y) {
    if (x < data_.size() && y < data_[x].size() && x < flag_.size() &&
        y < flag_[x].size() && flag_[x][y]) {
      return data_[x][y];
    }
    return nullptr;
  }

  void insert(uint32_t x, uint32_t y, T *t) {
    if (x >= data_.size()) {
      int diff = x - data_.size() + 1;
      for (int i = 0; i < diff; ++i) {
        data_.push_back(std::vector<T *>());
        flag_.push_back(std::vector<bool>());
      }
    }
    if (y >= data_[x].size()) {
      int diff = y - data_[x].size() + 1;
      for (int i = 0; i < diff; ++i) {
        data_[x].push_back(nullptr);
        flag_[x].push_back(false);
      }
    }
    flag_[x][y] = true;
    data_[x][y] = t;
  }

  void erase(uint32_t x, uint32_t y) {
    if (find(x, y) != nullptr) {
      flag_[x][y] = false;
    }
  }

  template <class Func>
  void foreach (Func &&func) {
    for (size_t i = 0; i < data_.size(); i++) {
      for (size_t j = 0; j < data_[i].size(); j++) {
        if (flag_[i][j])
          func(i, j, data_[i][j]);
      }
    }
  }

 private:
  std::vector<std::vector<T *>> data_;
  std::vector<std::vector<bool>> flag_;
};
}  // namespace cpp_frame::storage