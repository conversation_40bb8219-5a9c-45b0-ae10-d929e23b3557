#pragma once
#include <iostream>
#include <vector>
namespace cpp_frame::storage {

template <typename T>
class vector_storage {
 public:
  T *find(int x) {
    if (x < data_.size() && x < flag_.size() && flag_[x]) {
      return data_[x];
    }
    return nullptr;
  }

  void insert(int x, T *t) {
    if (x >= data_.size()) {
      int diff = x - data_.size() + 1;
      for (int i = 0; i < diff; ++i) {
        data_.push_back(nullptr);
        flag_.push_back(false);
      }
    }
    flag_[x] = true;
    data_[x] = t;
  }

  void erase(int x) {
    if (find(x) != nullptr) {
      flag_[x] = false;
    }
  }

  template <class Func>
  void foreach (Func &&func) {
    for (int i = 0; i < data_.size(); i++) {
      if (flag_[i])
        func(i, data_[i]);
    }
  }

 private:
  std::vector<T *> data_;
  std::vector<bool> flag_;
};
}  // namespace cpp_frame::storage