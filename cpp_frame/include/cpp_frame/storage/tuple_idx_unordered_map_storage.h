#pragma once
#include <unordered_map>
namespace cpp_frame::storage {

template <typename T>
class tuple_idx_unordered_map_storage {
 public:
  T *find(int x, int y) {
    auto it = data_.find(std::make_tuple(x, y));
    if (it == data_.end()) {
      return nullptr;
    }
    return it->second;
  }

  void insert(int x, int y, T *value) {
    data_.emplace(std::make_tuple(x, y), value);
  }

  void erase(int x, int y) { data_.erase(std::make_tuple(x, y)); }

  template <class Func>
  void foreach (Func &&func) {
    for (auto &[key, value] : data_) {
      auto &[x, y] = key;
      func(x, y, value);
    }
  }

 private:
  struct my_hash {
    std::size_t operator()(std::tuple<int, int> const &key) const {
      auto const &[x, y] = key;
      return (x * 2718281828) ^ (y * 3141592653);
    }
  };

  std::unordered_map<std::tuple<int, int>, T *, my_hash> data_;
};
}  // namespace cpp_frame::storage