#pragma once
#include <boost/beast/http.hpp>
#include <nlohmann_json/json.hpp>

namespace cpp_frame::http {

namespace beast = boost::beast;
namespace http = beast::http;

struct content_type {
  explicit content_type(const char *v) : value(v) {}
  std::string value;

  std::string operator()(const std::string &) const { return value; }
};
inline static const content_type text_plain{"text/plain"};
inline static const content_type text_html{"text/html"};
inline static const content_type application_json{"application/json"};
inline static const content_type image_x_icon{"image/x-icon"};
inline static const content_type image_png{"image/png"};

class response
    : public boost::beast::http::response<boost::beast::http::string_body> {
 public:
  using boost::beast::http::response<boost::beast::http::string_body>::response;
  using boost::beast::http::response<boost::beast::http::string_body>::set;
  using boost::beast::http::response<boost::beast::http::string_body>::operator=
      ;

  void set(const content_type &ct) {
    set(beast::http::field::content_type, ct.value);
  }

  void push_json(std::string &rsp) { rsp_json_.push_back(rsp); }

  void set_json(nlohmann::json &rsp) { rsp_json_ = rsp; }

  nlohmann::json &get_rsp_json() { return rsp_json_; }
  void set_header(beast::http::field name, const std::string &value) {
    set(name, value);
  }

  bool is_postponed() const { return is_postponed_; }
  void postpone() { is_postponed_ = true; }

  void done() {
    cb_();
    cb_ = [] {
    };
  }

  void on_done(std::function<void()> &&cb) { cb_ = std::move(cb); }

  http::status status() const { return result(); }
  bool is_status_ok() const { return status() == http::status::ok; }

 private:
  bool is_postponed_ = false;
  std::function<void()> cb_ = []() {
  };
  nlohmann::json rsp_json_;
};
}  // namespace cpp_frame::http