#pragma once
#include <string>
#include <unordered_map>

namespace cpp_frame::http {
class attribute {
 public:
  attribute() = default;
  explicit attribute(std::string str);
  std::string as_string(const char *default_value = "") const;
  int as_integer(int default_value = 0) const;
  double as_double(double default_value = 0.0) const;
  bool as_boolean(bool default_value = false) const;
  bool operator==(const char *other) const;
  bool operator==(const std::string &other) const;

 private:
  template <typename Type>
  Type as(const Type &default_value = {}) const {
    if (_value.empty()) {
      return default_value;
    }

    if constexpr (std::is_same_v<Type, bool>) {
      return (_value == "1" || _value == "true" || _value == "yes");
    }
    else if constexpr (std::is_floating_point_v<Type>) {
      return std::stod(_value);
    }
    else if constexpr (std::is_integral_v<Type>) {
      return std::stoi(_value);
    }
    else {
      return _value;
    }
  }

 private:
  std::string _value;
};

class attributes {
 public:
  using attribute_storage = std::unordered_map<std::string, attribute>;

  attributes() = default;
  explicit attributes(const std::string &str, char sep = '&');

  void insert(std::string key, std::string value);

  attribute_storage::const_iterator find(const std::string &key) const {
    return attributes_.find(key);
  }

  attribute_storage::const_iterator begin() const {
    return attributes_.begin();
  }
  attribute_storage::const_iterator end() const { return attributes_.end(); }

  const attribute &operator[](const std::string &key) const;

 private:
  attribute_storage attributes_;
  inline static const attribute EMPTY;
};
}  // namespace cpp_frame::http