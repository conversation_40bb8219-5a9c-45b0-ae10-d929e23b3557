#include <cpp_frame/http_server/router.h>
#include <stdint.h>

#include <functional>
#include <string>

namespace cpp_frame::http {
class http_server;
class http_acceptor;

class http_server {
  class server_route {
   public:
    server_route(http_server &s, std::string path)
        : server_(s), path_(std::move(path)) {}

    server_route &get(route_cb &&cb) {
      server_.get(path_, std::move(cb));
      return *this;
    }
    server_route &put(route_cb &&cb) {
      server_.put(path_, std::move(cb));
      return *this;
    }
    server_route &post(route_cb &&cb) {
      server_.post(path_, std::move(cb));
      return *this;
    }
    server_route &del(route_cb &&cb) {
      server_.del(path_, std::move(cb));
      return *this;
    }

   private:
    http_server &server_;
    std::string path_;
  };

 public:
  http_server(boost::asio::io_context &ioc);
  ~http_server();
  server_route add_route(const std::string &path) { return {*this, path}; }
  int register_addr(std::string_view ip, unsigned short port);

 private:
  void init_acceptor();
  void on_socket_created(boost::asio::ip::tcp::socket *socket,
                         boost::asio::ip::tcp::endpoint &endpoint,
                         beast::error_code ec);
  http_server &get(const std::string &path, route_cb &&cb);
  http_server &put(const std::string &path, route_cb &&cb);
  http_server &post(const std::string &path, route_cb &&cb);
  http_server &del(const std::string &path, route_cb &&cb);

 private:
  boost::asio::io_context &ioc_;
  router router_;
  http_acceptor *acceptor_;
  boost::asio::ip::tcp::endpoint endpoint_;
};
}  // namespace cpp_frame::http