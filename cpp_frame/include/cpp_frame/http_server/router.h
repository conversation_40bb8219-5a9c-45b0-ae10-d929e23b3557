#pragma once
#include <cpp_frame/http_server/request.h>
#include <cpp_frame/http_server/response.h>
#include <cpp_frame/http_server/router.h>

#include <unordered_map>

namespace cpp_frame::http {
using route_cb = std::function<void(const request &req, response &res)>;

class route {
 public:
  route(
      const std::string &path, route_cb &&cb = [](const auto &req, auto &res) {
      });
  bool match(request &req) const;
  void execute(const request &req, response &res) const { cb_(req, res); }
  const std::string &path() { return path_; }
  const std::vector<std::string> &segments() { return segments_; }

 private:
  std::string path_;
  std::vector<std::string> segments_;
  route_cb cb_;
};

class router {
 public:
  using routes = std::unordered_map<beast::http::verb, std::vector<route>>;
  void add_route(beast::http::verb v, route &&r) {
    routes_[v].push_back(std::move(r));
  }
  routes::const_iterator find(beast::http::verb v) const {
    return routes_.find(v);
  }
  routes::const_iterator begin() const { return routes_.begin(); }
  routes::const_iterator end() const { return routes_.end(); }

 private:
  routes routes_;
};
}  // namespace cpp_frame::http