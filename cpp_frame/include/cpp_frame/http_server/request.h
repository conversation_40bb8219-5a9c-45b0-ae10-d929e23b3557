#pragma once
#include <cpp_frame/http_server/attributes.h>

#include <boost/asio/ip/tcp.hpp>
#include <boost/beast/http.hpp>
namespace cpp_frame::http {

class request
    : public boost::beast::http::request<boost::beast::http::string_body> {
 public:
  using boost::beast::http::request<boost::beast::http::string_body>::request;
  using boost::beast::http::request<boost::beast::http::string_body>::operator=;

  attributes &get_attributes() { return attributes_; }
  const attributes &get_attributes() const { return attributes_; }
  const attribute &a(const std::string &key) const { return attributes_[key]; }

  const boost::asio::ip::tcp::endpoint &remote() const { return remote_ep_; }
  void remote(boost::asio::ip::tcp::endpoint ep) { remote_ep_ = std::move(ep); }

 private:
  attributes attributes_;
  boost::asio::ip::tcp::endpoint remote_ep_;
};
}  // namespace cpp_frame::http