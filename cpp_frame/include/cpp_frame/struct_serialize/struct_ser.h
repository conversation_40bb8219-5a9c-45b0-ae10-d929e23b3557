#pragma once
#include <cpp_frame/struct_serialize/ser_reflection.h>

#include <cstdint>
#include <sstream>
#include <string>
#include <type_traits>
#include <vector>

namespace cpp_frame::struct_serialize::j<PERSON>_helper {

template <typename T>
struct is_bounded_array : std::false_type {};
template <typename T, size_t N>
struct is_bounded_array<T[N]> : std::true_type {};

template <class T>
inline constexpr bool is_bounded_array_v = is_bounded_array<T>::value;

template <typename T>
inline static constexpr bool is_elementary_type_v =
    !std::is_enum_v<T> && !std::is_same<T, uint8_t>::value &&
    std::is_standard_layout_v<T> && std::is_trivial_v<T> &&
    !is_bounded_array_v<T>;

inline static void add_raw(std::string &data, const std::string &str) {
  data += str;
}

constexpr auto write_json_key = [](auto &s, auto i, auto &t) {
  add_raw(s, "\"");
  std::string_view name = get_name<decltype(t), decltype(i)::value>();
  add_raw(s, std::string(name));
  add_raw(s, "\"");
};

template <typename T, std::size_t n>
using CArray = T[n];
template <typename T, std::size_t n>
using StdTArray = std::array<T, n>;

template <typename T>
std::enable_if_t<is_elementary_type_v<T>> render_json_value(std::string &s,
                                                            const T &val) {
  std::stringstream ss;
  ss << std::boolalpha << val;
  add_raw(s, ss.str());
}

template <typename T>
std::enable_if_t<std::is_same_v<T, std::string>> render_json_value(
    std::string &s, const T &val) {
  add_raw(s, "\"" + val + "\"");
}

template <typename T>
std::enable_if_t<std::is_enum_v<T>> render_json_value(std::string &s,
                                                      const T &val) {
  add_raw(s, std::to_string(static_cast<int>(val)));
}

template <typename T>
std::enable_if_t<std::is_same_v<T, uint8_t>> render_json_value(std::string &s,
                                                               const T &val) {
  add_raw(s, std::to_string(static_cast<int>(val)));
}

template <typename T, std::size_t n>
void process(std::string &s, const CArray<T, n> &val) {
  if constexpr (std::is_same_v<T, char>) {
    add_raw(s, "\"" + std::string(val) + "\"");
  }
  else {
    add_raw(s, "[");
    for (std::size_t i = 0; i < n; i++) {
      render_json_value(s, val[i]);
      if (i != n - 1) {
        add_raw(s, ",");
      }
    }
    add_raw(s, "]");
  }
}

template <typename T, std::size_t n>
void process(std::string &s, const StdTArray<T, n> &val) {
  add_raw(s, "[");
  for (std::size_t i = 0; i < n; i++) {
    render_json_value(s, val[i]);
    if (i != n - 1) {
      add_raw(s, ",");
    }
  }
  add_raw(s, "]");
}

template <typename T>
void process(std::string &s, const std::vector<T> &val) {
  add_raw(s, "[");
  auto v_size = val.size();
  for (std::size_t i = 0; i < v_size; i++) {
    render_json_value(s, val[i]);
    if (i != v_size - 1) {
      add_raw(s, ",");
    }
  }
  add_raw(s, "]");
}

template <typename T>
std::enable_if_t<is_bounded_array_v<T>> render_json_value(std::string &s,
                                                          const T &val) {
  process(s, val);
}

template <typename T>
void render_json_value(std::string &s, const std::vector<T> &val) {
  process(s, val);
}

}  // namespace cpp_frame::struct_serialize::json_helper

namespace cpp_frame::struct_serialize {
template <typename T>
std::string to_json(T &&t) {
  std::string res_json;
  json_helper::add_raw(res_json, "{");
  for_each(std::forward<T>(t), [&t, &res_json](const auto &v, auto i) {
    using M = decltype(define_ser_members(std::forward<T>(t)));
    constexpr auto idx = decltype(i)::value;
    constexpr auto cnt = M::value();
    static_assert(idx < cnt);
    json_helper::write_json_key(res_json, i, t);
    json_helper::add_raw(res_json, ":");

    if constexpr (!detail::is_ser_reflection<decltype(t.*v)>::value) {
      json_helper::render_json_value(res_json, t.*v);
    }
    else {
      res_json += to_json(t.*v);
    }
    if (idx < cnt - 1)
      json_helper::add_raw(res_json, ",");
  });
  json_helper::add_raw(res_json, "}");
  return res_json;
}
}  // namespace cpp_frame::struct_serialize
