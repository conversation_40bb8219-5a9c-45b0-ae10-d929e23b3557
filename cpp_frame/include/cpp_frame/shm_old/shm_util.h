#pragma once
#include <fcntl.h>
#include <stdint.h>
#include <sys/mman.h>
#include <sys/shm.h>
#include <sys/stat.h>
#include <unistd.h>

#include <cstring>
#include <iostream>
namespace cpp_frame::shm {

// template <class T>
// static T *load_mmap_buffer(const char *filename, bool is_writer = true) {
//   // int fd = open(filename, is_writer ? (O_RDWR | O_CREAT) : (O_RDONLY |
//   // O_CREAT), 0666);
//   int fd = open(filename, (O_RDWR | O_CREAT), 0666);
//   // int fd = shm_open(filename, is_writer ? (O_RDWR | O_CREAT) : (O_RDONLY),
//   // 0666);
//   if (fd == -1) {
//     std::cout << "fd -1" << std::endl;
//     return nullptr;
//   }
//   if (ftruncate(fd, sizeof(T)) < 0) {
//     close(fd);
//     return nullptr;
//   }
//   // if (is_writer) {
//   //     if (ftruncate(fd, sizeof(T)) < 0) {
//   //         close(fd);
//   //         return nullptr;
//   //     }
//   // }
//   T *ret =
//       (T *)mmap(0, sizeof(T), is_writer ? (PROT_READ | PROT_WRITE) :
//       PROT_READ,
//                 MAP_SHARED, fd, 0);
//   if (ret == MAP_FAILED) {
//     close(fd);
//     return nullptr;
//   }
//   return ret;
// }
template <class T>
T* load_mmap_buffer(const char* filename, bool is_writer = true) {
  int shm_fd = open(filename, (O_RDWR | O_CREAT), 0666);

  // int shm_fd = shm_open(filename, O_CREAT | O_RDWR, 0666);
  if (shm_fd == -1) {
    std::cerr << "shm_open failed: " << strerror(errno) << std::endl;
    return nullptr;
  }
  if (ftruncate(shm_fd, sizeof(T))) {
    std::cerr << "ftruncate failed: " << strerror(errno) << std::endl;
    close(shm_fd);
    return nullptr;
  }
  fallocate(shm_fd, 0, 0, sizeof(T));
  T* ret = (T*)mmap(0, sizeof(T), PROT_READ | PROT_WRITE,
                    MAP_SHARED | MAP_POPULATE, shm_fd, 0);
  close(shm_fd);
  if (ret == MAP_FAILED) {
    std::cerr << "mmap failed: " << strerror(errno) << std::endl;
    return nullptr;
  }
  return ret;
}
static bool release_mmap_buffer(void* address, size_t size) {
  void* buffer = reinterpret_cast<void*>(address);
  if (munmap(buffer, size) != 0) {
    return false;
  }
  return true;
}
}  // namespace cpp_frame::shm