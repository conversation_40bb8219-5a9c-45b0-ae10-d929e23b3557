#pragma once
#include <cpp_frame/shm_old/shm_config.h>
#include <cpp_frame/shm_old/shm_writer.h>

namespace cpp_frame::shm {

class shm_publisher {
  enum { MAX_TOPIC_NUM = 0xFFFF };

 public:
  void init_topic(uint16_t topic_id, const char *topic_addr,
                  uint16_t init_pages) {
    shm_writers_[topic_id].init(topic_id, topic_addr, init_pages);
  }

  int publish(uint16_t topicid, void *__restrict buf, uint32_t size,
              uint16_t msg_id, uint8_t node_id) {
    publish(topicid, buf, size, msg_id, FLOW, node_id, 0, 1, 0);
    return 0;
  }
  int publish(uint16_t topicid, void *__restrict buf, uint32_t size,
              uint16_t msg_id, uint8_t node_id, uint16_t is_last) {
    publish(topicid, buf, size, msg_id, FLOW, node_id, 0, is_last, 0);
    return 0;
  }
  int send_request(uint16_t topicid, void *__restrict buf, uint32_t size,
                   uint16_t msg_id, uint8_t node_id, uint64_t request_id,
                   uint16_t is_last = 1) {
    publish(topicid, buf, size, msg_id, REQ, node_id, request_id, is_last, 0);
    return 0;
  }

  int send_response(uint16_t topicid, void *__restrict buf, uint32_t size,
                    uint16_t msg_id, uint8_t node_id, uint64_t request_id,
                    uint16_t is_last, uint16_t error_code) {
    publish(topicid, buf, size, msg_id, RSP, node_id, request_id, is_last,
            error_code);
    return 0;
  }

 private:
  int publish(uint16_t topicid, void *__restrict buf, uint32_t size,
              uint16_t msg_id, uint8_t msg_type, uint8_t node_id,
              uint64_t request_id, uint16_t is_last, uint16_t error_code) {
    shm_writers_[topicid].write(buf, size, msg_id, msg_type, node_id,
                                request_id, is_last, error_code);
    return 0;
  }

 private:
  shm_writer<SHM_PAGE_SIZE> shm_writers_[MAX_TOPIC_NUM];
};

}  // namespace cpp_frame::shm
