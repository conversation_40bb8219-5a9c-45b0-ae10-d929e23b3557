#pragma once
#include <cpp_frame/shm_old/shm_config.h>
#include <cpp_frame/shm_old/shm_reader.h>

#include <unordered_set>
#include <vector>

namespace cpp_frame::shm {
class shm_subcriber {
 public:
  void init_topic(uint16_t topic_id, const char *topic_addr, sub_mode mode) {
    reader reader;
    reader.init(topic_id, topic_addr, mode);
    shm_readers_.emplace_back(reader);
  }

  frame_header *read() {
    long min_nano = TIME_TO_LAST;
    frame_header *cur_header = nullptr;
    for (reader &cur : shm_readers_) {
      frame_header *header = cur.read();
      if (header != nullptr) {
        long nano = header->timestamp;
        if (min_nano == TIME_TO_LAST || nano < min_nano) {
          min_nano = nano;
          cur_header = header;
          cur_shm_reader = &cur;
        }
      }
    }
    if (cur_header != nullptr) {
      cur_shm_reader->pass_frame(cur_header->size);
      return cur_header;
    }
    else {
      return nullptr;
    }
  }

 private:
  using reader = shm_reader<SHM_PAGE_SIZE>;
  std::vector<reader> shm_readers_;
  reader *cur_shm_reader;
  const long TIME_TO_LAST = -1;
};

}  // namespace cpp_frame::shm