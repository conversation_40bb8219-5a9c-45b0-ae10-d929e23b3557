#pragma once
#include <iostream>
#include <string>

#include <cpp_frame/shm_old/shm_util.h>
#include <cpp_frame/shm_old/spmc_queue.h>

namespace cpp_frame::shm {
enum sub_mode { FromBegin = 0, FromLast = 1 };
template <uint32_t Bytes>
class shm_reader {
 public:
  shm_reader() : q_(nullptr) {}
  shm_reader(uint16_t topic, const char *topic_addr)
      : topic_(topic), topic_addr_(topic_addr) {}

  void init(uint16_t topic, const char *topic_addr, sub_mode mode) {
    topic_ = topic;
    topic_addr_ = std::string(topic_addr);
    if (mode == FromBegin)
      init_shm_queue(true);
    else
      init_shm_queue(false);
  }

  frame_header *read() {
    if (q_ == nullptr) {
      return nullptr;
    }
    auto *header = q_->read(read_idx_, res);
    if (res == ReadOK) {
      return header;
    }
    else if (res == ReadAgain) {
      return nullptr;
    }
    else if (res == ReadNextPage) {
      page_id_++;
      do {
        q_ = load_page(page_id_);
        read_idx_ = 0;
      } while (q_ == nullptr);
      return nullptr;
    }
    return nullptr;
  }

  void pass_frame(uint16_t size) { q_->pass(read_idx_, size); }

 private:
  using shm_queue = spmc_queue<Bytes>;
  shm_queue *load_page(uint16_t page_id) {
    std::string cur_path = topic_addr_ + "." + std::to_string(page_id);
    shm_queue *q = load_mmap_buffer<shm_queue>(cur_path.c_str(), false);
    return q;
  }
  bool init_shm_queue(bool is_begin) {
    page_id_ = 0;
    if (is_begin) {
      do {
        q_ = load_page(page_id_);
      } while (q_ == nullptr);
      read_idx_ = 0;
      return true;
    }
    while (1) {
      q_ = load_page(page_id_);
      if (q_ == nullptr)
        continue;
      if (q_->is_last()) {
        page_id_++;
      }
      else {
        read_idx_ = q_->get_written_idx();
        break;
      }
    }
    // FAST_LOG(INFO, "page_id:%d read_idx:%d\n", page_id_, read_idx_);
    return true;
  }

 private:
  uint16_t topic_;
  std::string topic_addr_;
  uint64_t read_idx_;
  uint16_t page_id_;
  shm_queue *q_{nullptr};
  read_res res;
};

}  // namespace cpp_frame::shm