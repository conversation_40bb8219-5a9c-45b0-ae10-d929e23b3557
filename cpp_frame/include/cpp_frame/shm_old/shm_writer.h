#pragma once
#include <cpp_frame/shm_old/shm_util.h>
#include <cpp_frame/shm_old/spmc_queue.h>
#include <cpp_frame/utils/date.h>
#include <string.h>

#include <cstddef>
#include <string>
#include <vector>

namespace cpp_frame::shm {

template <uint32_t Bytes>
class shm_writer {
 public:
  using shm_queue = spmc_queue<Bytes>;
  enum { MAX_PAGES = 0XFFFF };
  shm_writer() = default;
  ~shm_writer() = default;

  bool is_ready() { return is_ready_; }

  void init(uint16_t topicid, const char *topic_name, uint16_t init_pages) {
    topic_id_ = topicid;
    topic_name_ = std::string(topic_name);
    init_pages_ = init_pages;
    q_vector_.resize(MAX_PAGES);
    std::fill(q_vector_.begin(), q_vector_.end(), nullptr);
    for (int i = 0; i < init_pages; i++) {
      std::cout << "load:" << i << std::endl;
      if (load_page(i)) {
        std::cout << "load success" << std::endl;
      }
    }
    cur_q_ = q_vector_[page_id_];
    is_ready_ = true;
  }

  void write(void *__restrict buf, uint32_t size, uint16_t msg_id,
             uint8_t msg_type, uint8_t node_id, uint64_t request_id,
             uint16_t is_last, uint16_t error_code) {
    if (cur_q_) [[likely]] {
      auto *header = cur_q_->alloc(size);
      if (header == nullptr && page_id_ < init_pages_ - 1) {
        // release_mmap_buffer((void *)cur_q_, sizeof(shm_queue));
        page_id_++;
        if (q_vector_[page_id_] != nullptr) {
          cur_q_ = q_vector_[page_id_];
          header = cur_q_->alloc(size);
        }
      }
      else if (header == nullptr) {
        page_id_++;
        // release_mmap_buffer((void *)cur_q_, sizeof(shm_queue));
        if (load_page(page_id_)) {
          cur_q_ = q_vector_[page_id_];
          header = cur_q_->alloc(size);
        }
      }
      if(!header){
        std::cout<<"error"<<std::endl;
        return;
      }
      header->msg_id = msg_id;
      header->msg_type = msg_type;
      header->node_id = node_id;
      header->request_id = request_id;
      header->is_last = is_last;
      header->error_code = error_code;
      header->topic_id = topic_id_;
      header->timestamp = date::get_current_nano_sec();
      memcpy((void *)(header + 1), buf, size);
      cur_q_->push();
    }
  }

 private:
  bool load_page(int page_id) {
    std::string cur_path = topic_name_ + "." + std::to_string(page_id);
    shm_queue *q = load_mmap_buffer<shm_queue>(cur_path.c_str(), true);    
    q->reset();
    if (q == nullptr) {
      return false;
    }
    q_vector_[page_id] = q;
    return true;
  }

 private:
  uint16_t topic_id_;
  std::string topic_name_;
  uint16_t page_id_{0};
  std::vector<shm_queue *> q_vector_;
  shm_queue *cur_q_{nullptr};
  bool is_ready_{false};
  uint16_t init_pages_{0};
};

}  // namespace cpp_frame::shm
