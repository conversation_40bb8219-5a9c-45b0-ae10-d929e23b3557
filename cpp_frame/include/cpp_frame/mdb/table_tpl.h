#pragma once
#include <cpp_frame/storage/multi_index_map.h>
#include <stdint.h>

#include <functional>
#include <vector>

namespace cpp_frame::mdb {

class table {
 public:
  table() {}
  virtual ~table() {}
};

template <uint16_t __TABLE_ID__, typename Key, typename Tp>
class table_tpl : public table {
 public:
  enum { TABLE_ID = __TABLE_ID__ };
  using primary_key = Key;
  using field_type = Tp;
  using add_commit_trigger_callback = std::function<void(Tp *)>;
  using update_commit_trigger_callback =
      std::function<void(Tp *old_v, Tp *new_v)>;
  using add_or_update_commit_trigger_callback =
      std::function<void(Tp *old_v, Tp *new_v)>;
  using delete_commit_trigger_callback = std::function<void(Tp *)>;
  using before_update_trigger_callback =
      std::function<void(Tp *old_v, const Tp &new_v)>;

 public:
  table_tpl() = default;
  virtual ~table_tpl() = default;

  template <typename FUNCTION>
  void register_add_commit_trigger(FUNCTION f) {
    add_commit_triggers_.push_back(new add_commit_trigger_callback(f));
  }
  template <typename FUNCTION>
  void register_update_commit_trigger(FUNCTION f) {
    update_commit_triggers_.push_back(new update_commit_trigger_callback(f));
  }
  template <typename FUNCTION>
  void register_add_or_update_commit_trigger(FUNCTION f) {
    add_or_update_commit_triggers_.push_back(
        new add_or_update_commit_trigger_callback(f));
  }
  template <typename FUNCTION>
  void register_delete_commit_trigger(FUNCTION f) {
    delete_commit_triggers_.push_back(new delete_commit_trigger_callback(f));
  }
  template <typename FUNCTION>
  void register_before_update_trigger(FUNCTION f) {
    before_update_triggers_.push_back(new before_update_trigger_callback(f));
  }
  size_t size() { return s_.size(); }
  Tp *first() { return s_.first(); }
  Tp *next() { return s_.next(); }
  Tp *get(const Key &k) { return s_.get(k); }
  Tp *add(const Key &k, Tp &v) {
    Tp *r = get(k);
    if (r == nullptr) {
      r = s_.insert(std::make_pair(k, v));
      after_add(r);
    }
    return r;
  }
  Tp *add_or_update(const Key &k, Tp &v) {
    Tp *r = get(k);
    if (r == nullptr) {
      r = s_.insert(std::make_pair(k, v));
      after_add(r);
      after_add_or_update(nullptr, r);
    }
    else {
      before_update(r, v);
      Tp old_v = *r;
      *r = std::move(v);
      after_update(&old_v, r);
      after_add_or_update(&old_v, r);
    }
    return r;
  }
  Tp *add_or_update(const Key &k, Tp &&v) {
    Tp *r = get(k);
    if (r == nullptr) {
      r = s_.insert(std::make_pair(k, v));
      after_add(r);
      after_add_or_update(nullptr, r);
    }
    else {
      before_update(r, v);
      Tp old_v = *r;
      *r = std::move(v);
      after_update(&old_v, r);
      after_add_or_update(&old_v, r);
    }
    return r;
  }
  void del(const Key &k) {
    Tp *r = get(k);
    if (r != nullptr) {
      before_del(r);
      s_.erase(k);
    }
  }

 private:
  void after_add(Tp *v) {
    for (auto i : add_commit_triggers_) {
      (*i)(v);
    }
  }
  void before_update(Tp *old_v, const Tp &new_v) {
    for (auto i : before_update_triggers_) {
      (*i)(old_v, new_v);
    }
  }
  void after_update(Tp *old_v, Tp *new_v) {
    for (auto i : update_commit_triggers_) {
      (*i)(old_v, new_v);
    }
  }
  void after_add_or_update(Tp *old_v, Tp *new_v) {
    for (auto i : add_or_update_commit_triggers_) {
      (*i)(old_v, new_v);
    }
  }
  void before_del(Tp *r) {
    for (auto i : delete_commit_triggers_) {
      (*i)(r);
    }
  }

 private:
  using ADD_COMMIT_TRIGGERS = std::vector<add_commit_trigger_callback *>;
  using UPDATE_COMMIT_TRIGGERS = std::vector<update_commit_trigger_callback *>;
  using ADD_OR_UPDATE_COMMIT_TRIGGERS =
      std::vector<add_or_update_commit_trigger_callback *>;
  using DELETE_COMMIT_TRIGGERS = std::vector<delete_commit_trigger_callback *>;
  using BEFORE_UPDATE_TRIGGERS = std::vector<before_update_trigger_callback *>;
  ADD_COMMIT_TRIGGERS add_commit_triggers_;
  UPDATE_COMMIT_TRIGGERS update_commit_triggers_;
  ADD_OR_UPDATE_COMMIT_TRIGGERS add_or_update_commit_triggers_;
  DELETE_COMMIT_TRIGGERS delete_commit_triggers_;
  BEFORE_UPDATE_TRIGGERS before_update_triggers_;
  storage::multi_index_map<Key, Tp> s_;
};
}  // namespace cpp_frame::mdb