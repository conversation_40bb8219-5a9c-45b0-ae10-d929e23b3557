#pragma once
#include <string.h>

#include <iostream>
#include <cpp_frame/mdb/table_tpl.h>

namespace cpp_frame::mdb {

class tplmdb {
  enum { MAX_TABLES = 65535 };

 public:
  tplmdb() { memset(tables_, 0, sizeof(tables_)); }
  virtual ~tplmdb() {
    for (int i = 0; i < MAX_TABLES; ++i) {
      if (tables_[i] != NULL) {
        delete tables_[i];
      }
    }
  }
  template <typename TABLE>
  void register_table() {
    uint16_t id = TABLE::TABLE_ID;
    if (tables_[id] != NULL) {
      return;
    }
    tables_[id] = new TABLE();
  }
  template <typename TABLE>
  TABLE *get_table() {
    return (TABLE *)(tables_[TABLE::TABLE_ID]);
  }
  template <typename TABLE>
  void add(const typename TABLE::primary_key &k,
           const typename TABLE::field_type &v) {
    get_table<TABLE>()->add_or_update(k, v);
  }
  template <typename TABLE>
  typename TABLE::record_type *add_or_update(
      const typename TABLE::primary_key &k,
      const typename TABLE::field_type &v) {
    return get_table<TABLE>()->add_or_update(k, v);
  }

 private:
  table *tables_[MAX_TABLES];
};
}  // namespace cpp_frame::mdb