#include <cpp_frame/mdb/table_tpl.h>

#include <string>
#include <type_traits>

namespace cpp_frame::mdb {
#define MARCO_TABLE_EXPAND(...) __VA_ARGS__
#define MAKE_TABLE_ARG_LIST_1(op, arg, ...) op(arg)
#define MAKE_TABLE_ARG_LIST_2(op, arg, ...) \
  op(arg), MARCO_TABLE_EXPAND(MAKE_TABLE_ARG_LIST_1(op, __VA_ARGS__))
#define MAKE_TABLE_ARG_LIST_3(op, arg, ...) \
  op(arg), MARCO_TABLE_EXPAND(MAKE_TABLE_ARG_LIST_2(op, __VA_ARGS__))
#define MAKE_TABLE_ARG_LIST_4(op, arg, ...) \
  op(arg), MARCO_TABLE_EXPAND(MAKE_TABLE_ARG_LIST_3(op, __VA_ARGS__))
#define MAKE_TABLE_ARG_LIST_5(op, arg, ...) \
  op(arg), MARCO_TABLE_EXPAND(MAKE_TABLE_ARG_LIST_4(op, __VA_ARGS__))
#define MAKE_TABLE_ARG_LIST_6(op, arg, ...) \
  op(arg), MARCO_TABLE_EXPAND(MAKE_TABLE_ARG_LIST_5(op, __VA_ARGS__))
#define MAKE_TABLE_ARG_LIST_7(op, arg, ...) \
  op(arg), MARCO_TABLE_EXPAND(MAKE_TABLE_ARG_LIST_6(op, __VA_ARGS__))
#define MAKE_TABLE_ARG_LIST_8(op, arg, ...) \
  op(arg), MARCO_TABLE_EXPAND(MAKE_TABLE_ARG_LIST_7(op, __VA_ARGS__))
#define MAKE_TABLE_ARG_LIST_9(op, arg, ...) \
  op(arg), MARCO_TABLE_EXPAND(MAKE_TABLE_ARG_LIST_8(op, __VA_ARGS__))
#define MAKE_TABLE_ARG_LIST_10(op, arg, ...) \
  op(arg), MARCO_TABLE_EXPAND(MAKE_TABLE_ARG_LIST_9(op, __VA_ARGS__))

#define KEY_FIELD(t) t
#define MACRO_TABLE_CONCAT(A, B) MACRO_TABLE_CONCAT1(A, B)
#define MACRO_TABLE_CONCAT1(A, B) A##_##B
#define TABLE_RSEQ_N() 9, 8, 7, 6, 5, 4, 3, 2, 1, 0
#define TABLE_ARG_N(_1, _2, _3, _4, _5, _6, _7, _8, _9, N, ...) N

#define GET_TABLE_ARG_COUNT_INNER(...) \
  MARCO_TABLE_EXPAND(TABLE_ARG_N(__VA_ARGS__))
#define GET_TABLE_ARG_COUNT(...) \
  GET_TABLE_ARG_COUNT_INNER(__VA_ARGS__, TABLE_RSEQ_N())
#define MAKE_TABLE_ARG_LIST(N, op, arg, ...) \
  MACRO_TABLE_CONCAT(MAKE_TABLE_ARG_LIST, N)(op, arg, __VA_ARGS__)

#define MAKE_PRIMARY_KEY_META_IMPL(STRUCT_NAME, ...)    \
  inline auto define_key_members(STRUCT_NAME const &) { \
    struct key_members {                                \
      constexpr decltype(auto) static apply_impl() {    \
        return std::make_tuple(__VA_ARGS__);            \
      }                                                 \
    };                                                  \
    return key_members{};                               \
  }

#define MAKE_PRIMARY_KEY_MATA(STRUCT_NAME, N, ...) \
  MAKE_PRIMARY_KEY_META_IMPL(                      \
      STRUCT_NAME,                                 \
      MAKE_TABLE_ARG_LIST(N, &STRUCT_NAME::KEY_FIELD, __VA_ARGS__))
#define DEFINE_PRIMARY_KEY(STRUCT_NAME, ...)                           \
  MAKE_PRIMARY_KEY_MATA(STRUCT_NAME, GET_TABLE_ARG_COUNT(__VA_ARGS__), \
                        __VA_ARGS__)

template <class T>
struct member_pointer_type_helper;
template <class C, class T>
struct member_pointer_type_helper<T C::*> {
  using type = T;
};
template <class C>
struct member_pointer_type_helper<char * C::*> {
  using type = std::string;
};
template <class C, std::size_t N>
struct member_pointer_type_helper<char (C::*)[N]> {
  using type = std::string;
};
template <class T>
struct remove_cvref {
  using type = std::remove_cv_t<std::remove_reference_t<T>>;
};
template <class T>
struct member_type
    : member_pointer_type_helper<typename remove_cvref<T>::type> {};
template <class T>
using member_type_t = typename member_type<T>::type;

#define MAKE_TYPE_LIST(N, op, arg, ...) \
  MACRO_TABLE_CONCAT(MAKE_TYPE_LIST, N)(op, arg, __VA_ARGS__)
#define TYPE(arg) cpp_frame::mdb::member_type_t<decltype(arg)>
#define MAKE_TYPE_LIST_1(op, arg, ...) TYPE(op(arg))
#define MAKE_TYPE_LIST_2(op, arg, ...) \
  TYPE(op(arg)), MARCO_TABLE_EXPAND(MAKE_TYPE_LIST_1(op, __VA_ARGS__))
#define MAKE_TYPE_LIST_3(op, arg, ...) \
  TYPE(op(arg)), MARCO_TABLE_EXPAND(MAKE_TYPE_LIST_2(op, __VA_ARGS__))
#define MAKE_TYPE_LIST_4(op, arg, ...) \
  TYPE(op(arg)), MARCO_TABLE_EXPAND(MAKE_TYPE_LIST_3(op, __VA_ARGS__))
#define MAKE_TYPE_LIST_5(op, arg, ...) \
  TYPE(op(arg)), MARCO_TABLE_EXPAND(MAKE_TYPE_LIST_4(op, __VA_ARGS__))
#define MAKE_TYPE_LIST_6(op, arg, ...) \
  TYPE(op(arg)), MARCO_TABLE_EXPAND(MAKE_TYPE_LIST_5(op, __VA_ARGS__))
#define MAKE_TYPE_LIST_7(op, arg, ...) \
  TYPE(op(arg)), MARCO_TABLE_EXPAND(MAKE_TYPE_LIST_6(op, __VA_ARGS__))
#define MAKE_TYPE_LIST_8(op, arg, ...) \
  TYPE(op(arg)), MARCO_TABLE_EXPAND(MAKE_TYPE_LIST_7(op, __VA_ARGS__))
#define MAKE_TYPE_LIST_9(op, arg, ...) \
  TYPE(op(arg)), MARCO_TABLE_EXPAND(MAKE_TYPE_LIST_8(op, __VA_ARGS__))
#define MAKE_TYPE_LIST_10(op, arg, ...) \
  TYPE(op(arg)), MARCO_TABLE_EXPAND(MAKE_TYPE_LIST_9(op, __VA_ARGS__))

#define MAKE_TABLE_IMPL(TABLE_ID, STRUCT_NAME, ...)                         \
  using MACRO_TABLE_CONCAT(STRUCT_NAME, key) = std::tuple<__VA_ARGS__>;     \
  using MACRO_TABLE_CONCAT(STRUCT_NAME, table) = cpp_frame::mdb::table_tpl< \
      TABLE_ID, MACRO_TABLE_CONCAT(STRUCT_NAME, key), STRUCT_NAME>;

#define MAKE_TABLE(TABLE_ID, STRUCT_NAME, N, ...) \
  MAKE_TABLE_IMPL(TABLE_ID, STRUCT_NAME,          \
                  MAKE_TYPE_LIST(N, &STRUCT_NAME::KEY_FIELD, __VA_ARGS__))

#define DEFINE_TABLE(TABLE_ID, STRUCT_NAME, ...)                      \
  DEFINE_PRIMARY_KEY(STRUCT_NAME, __VA_ARGS__)                        \
  MAKE_TABLE(TABLE_ID, STRUCT_NAME, GET_TABLE_ARG_COUNT(__VA_ARGS__), \
             __VA_ARGS__)
}  // namespace cpp_frame::mdb
// template <typename T>
// using Key_members = decltype(define_key_members(std::declval<T>()));

// template <typename T>
// inline auto get_primary_key(T &&t)
// {
//     using key_members = decltype(define_key_members(std::declval<T>()));
//     // constexpr size_t key_num =
//     std::tuple_size<decltype(key_members::apply_impl())>::value; return
//     std::apply([&t](auto &&...args) { return std::make_tuple(t.*args...); },
//     key_members::apply_impl());
//     // if constexpr (key_num > 1) {
//     //     return std::apply([&t](auto &&...args) { return
//     std::make_tuple(t.*args...); }, key_members::apply_impl());
//     // } else {
//     //     return t.*std::get<0>(key_members::apply_impl());
//     // }
// }
