#pragma once

#include <fmt/core.h>
#include <fmt/chrono.h>
#include <fstream>
#include <string>
#include <mutex>
#include <ctime>
#include <iomanip>
#include <sstream>
#include <iostream>
#include <memory>
#include <filesystem>
#include <map>

namespace cpp_frame {
namespace cpp_logger {

// 日志级别枚举
enum class log_level {
    TRACE = 0,
    DEBUG = 1,
    INFO = 2,
    WARN = 3,
    ERROR = 4,
    FATAL = 5
};

// 前向声明
class logger;

// 日志管理类
class logger_manager {
private:
    // 单例实例
    static logger_manager& instance() {
        static logger_manager instance;
        return instance;
    }

    // 私有构造函数
    logger_manager() {}

    // 禁止拷贝和赋值
    logger_manager(const logger_manager&) = delete;
    logger_manager& operator=(const logger_manager&) = delete;

    // 日志实例映射表
    std::map<std::string, std::shared_ptr<logger>> loggers_;
    std::mutex mutex_;

public:
    // 获取或创建日志实例
    static std::shared_ptr<logger> get_logger(const std::string& name) {
        std::lock_guard<std::mutex> lock(instance().mutex_);
        auto& loggers = instance().loggers_;

        auto it = loggers.find(name);
        if (it != loggers.end()) {
            return it->second;
        }

        auto logger_ptr = std::make_shared<logger>(name);
        loggers[name] = logger_ptr;
        return logger_ptr;
    }

    // 获取默认日志实例
    static std::shared_ptr<logger> get_default_logger() {
        return get_logger("default");
    }
};

// 日志类
class logger {
public:
    // 构造函数
    logger(const std::string& name) : name_(name), level_(log_level::INFO), console_output_(true) {}

    // 初始化日志
    void init(const std::string& log_file, log_level level = log_level::INFO, bool console_output = true) {
        std::lock_guard<std::mutex> lock(mutex_);

        // 设置日志级别
        level_ = level;
        console_output_ = console_output;

        // 关闭之前的文件（如果有）
        if (file_.is_open()) {
            file_.close();
        }

        // 确保日志目录存在
        if (!log_file.empty()) {
            std::filesystem::path log_path(log_file);
            std::filesystem::path dir_path = log_path.parent_path();

            if (!dir_path.empty()) {
                try {
                    std::filesystem::create_directories(dir_path);
                } catch (const std::exception& e) {
                    fmt::print(stderr, "Failed to create log directory: {}, error: {}\n",
                              dir_path.string(), e.what());
                }
            }

            // 打开新文件
            file_.open(log_file, std::ios::out | std::ios::app);
            if (!file_.is_open()) {
                fmt::print(stderr, "Failed to open log file: {}\n", log_file);
            }
        }
    }

    // 设置日志级别
    void set_level(log_level level) {
        std::lock_guard<std::mutex> lock(mutex_);
        level_ = level;
    }

    // 设置日志级别（字符串形式）
    void set_level(const std::string& level_str) {
        if (level_str == "TRACE" || level_str == "trace") {
            set_level(log_level::TRACE);
        } else if (level_str == "DEBUG" || level_str == "debug") {
            set_level(log_level::DEBUG);
        } else if (level_str == "INFO" || level_str == "info") {
            set_level(log_level::INFO);
        } else if (level_str == "WARN" || level_str == "warn") {
            set_level(log_level::WARN);
        } else if (level_str == "ERROR" || level_str == "error") {
            set_level(log_level::ERROR);
        } else if (level_str == "FATAL" || level_str == "fatal") {
            set_level(log_level::FATAL);
        }
    }

    // 设置是否输出到控制台
    void set_console_output(bool console_output) {
        std::lock_guard<std::mutex> lock(mutex_);
        console_output_ = console_output;
    }

    // 关闭日志
    void close() {
        std::lock_guard<std::mutex> lock(mutex_);
        if (file_.is_open()) {
            file_.close();
        }
    }

    // 便捷日志方法
    template<typename... Args>
    void trace(const char* file, int line, const char* func, const std::string& format, Args&&... args) {
        log(log_level::TRACE, file, line, func, format, std::forward<Args>(args)...);
    }

    template<typename... Args>
    void debug(const char* file, int line, const char* func, const std::string& format, Args&&... args) {
        log(log_level::DEBUG, file, line, func, format, std::forward<Args>(args)...);
    }

    template<typename... Args>
    void info(const char* file, int line, const char* func, const std::string& format, Args&&... args) {
        log(log_level::INFO, file, line, func, format, std::forward<Args>(args)...);
    }

    template<typename... Args>
    void warn(const char* file, int line, const char* func, const std::string& format, Args&&... args) {
        log(log_level::WARN, file, line, func, format, std::forward<Args>(args)...);
    }

    template<typename... Args>
    void error(const char* file, int line, const char* func, const std::string& format, Args&&... args) {
        log(log_level::ERROR, file, line, func, format, std::forward<Args>(args)...);
    }

    template<typename... Args>
    void fatal(const char* file, int line, const char* func, const std::string& format, Args&&... args) {
        log(log_level::FATAL, file, line, func, format, std::forward<Args>(args)...);
    }

    // 日志记录函数
    template<typename... Args>
    void log(log_level level, const char* file, int line, const char* func, const std::string& format, Args&&... args) {
        if (level < level_) {
            return;
        }

        std::lock_guard<std::mutex> lock(mutex_);

        // 获取当前时间
        auto now = std::chrono::system_clock::now();
        auto time_t_now = std::chrono::system_clock::to_time_t(now);
        auto ms = std::chrono::duration_cast<std::chrono::milliseconds>(now.time_since_epoch()) % 1000;

        std::tm tm_now;
        localtime_r(&time_t_now, &tm_now);

        char time_buf[32];
        std::strftime(time_buf, sizeof(time_buf), "%Y-%m-%d %H:%M:%S", &tm_now);

        // 日志级别名称
        static const char* level_names[] = {"TRACE", "DEBUG", "INFO", "WARN", "ERROR", "FATAL"};

        // 提取文件名（不包含路径）
        const char* filename = file;
        const char* last_slash = strrchr(file, '/');
        if (last_slash) {
            filename = last_slash + 1;
        }

        // 格式化消息
        std::string message;
        try {
            message = fmt::format(fmt::runtime(format), std::forward<Args>(args)...);
        } catch (const std::exception& e) {
            message = fmt::format("Error formatting log message: {}", e.what());
        }

        // 构建完整日志行
        std::string log_line = fmt::format("[{}] [{}.{:03d}] [{}:{}] [{}] [{}] {}\n",
            level_names[static_cast<int>(level)],
            time_buf, ms.count(),
            filename, line,
            func,
            name_,
            message);

        // 输出到文件
        if (file_.is_open()) {
            file_ << log_line;
            file_.flush();
        }

        // 输出到控制台
        if (console_output_) {
            fmt::print("{}", log_line);
            fflush(stdout);
        }
    }

private:
    // 禁止拷贝和赋值
    logger(const logger&) = delete;
    logger& operator=(const logger&) = delete;

    std::string name_;
    std::ofstream file_;
    log_level level_;
    bool console_output_;
    std::mutex mutex_;
};

// 全局默认日志器
static std::shared_ptr<logger>& get_global_logger() {
    static std::shared_ptr<logger> global_logger = logger_manager::get_default_logger();
    return global_logger;
}

} // namespace cpp_logger
} // namespace cpp_frame

// 日志宏定义 - 使用全局默认日志器
#define LOG_TRACE(format, ...) \
    cpp_frame::cpp_logger::get_global_logger()->trace( \
        __FILE__, __LINE__, __func__, format, ##__VA_ARGS__)

#define LOG_DEBUG(format, ...) \
    cpp_frame::cpp_logger::get_global_logger()->debug( \
        __FILE__, __LINE__, __func__, format, ##__VA_ARGS__)

#define LOG_INFO(format, ...) \
    cpp_frame::cpp_logger::get_global_logger()->info( \
        __FILE__, __LINE__, __func__, format, ##__VA_ARGS__)

#define LOG_WARN(format, ...) \
    cpp_frame::cpp_logger::get_global_logger()->warn( \
        __FILE__, __LINE__, __func__, format, ##__VA_ARGS__)

#define LOG_ERROR(format, ...) \
    cpp_frame::cpp_logger::get_global_logger()->error( \
        __FILE__, __LINE__, __func__, format, ##__VA_ARGS__)

#define LOG_FATAL(format, ...) \
    cpp_frame::cpp_logger::get_global_logger()->fatal( \
        __FILE__, __LINE__, __func__, format, ##__VA_ARGS__)

// 模块日志宏定义 - 直接使用 logger 指针
#define MODULE_LOG_TRACE(logger, format, ...) \
    (logger)->trace(__FILE__, __LINE__, __func__, format, ##__VA_ARGS__)

#define MODULE_LOG_DEBUG(logger, format, ...) \
    (logger)->debug(__FILE__, __LINE__, __func__, format, ##__VA_ARGS__)

#define MODULE_LOG_INFO(logger, format, ...) \
    (logger)->info(__FILE__, __LINE__, __func__, format, ##__VA_ARGS__)

#define MODULE_LOG_WARN(logger, format, ...) \
    (logger)->warn(__FILE__, __LINE__, __func__, format, ##__VA_ARGS__)

#define MODULE_LOG_ERROR(logger, format, ...) \
    (logger)->error(__FILE__, __LINE__, __func__, format, ##__VA_ARGS__)

#define MODULE_LOG_FATAL(logger, format, ...) \
    (logger)->fatal(__FILE__, __LINE__, __func__, format, ##__VA_ARGS__)
