#pragma once
#include <cpp_frame/http_client/http_client_settings.h>
#include <cpp_frame/http_client/http_connection_pool_new.h>
#include <cpp_frame/http_client/http_content.h>
#include <cpp_frame/http_client/request_data.h>
#include <stdio.h>
#include <syscall.h>
#include <unistd.h>

#include <boost/asio/dispatch.hpp>
#include <map>
#include <memory>
namespace cpp_frame::http {
class request_manager : public std::enable_shared_from_this<request_manager> {
 public:
  request_manager(const http_client_settings &settings,
                  const http_executor_type &exec, const std::string &host,
                  const std::string &port);
  ~request_manager() { std::cout << "request_manager end" << std::endl; }

  void execute_request_async(request_data request) {
    async<&request_manager::execute_request>(request);
  }
  void on_request_completed_async(uint64_t request_id,
                                  http_result_data &&http_result_data,
                                  http_content_ptr &&handle,
                                  boost::system::error_code ec) {
    async<&request_manager::on_request_completed>(
        request_id, std::move(http_result_data), std::move(handle),
        std::move(ec));
  }
  size_t get_free_conn_cnt();
  
 private:
  // This is a work-around as we don't have C++20 lambdas perfect capture in
  // C++17
  template <auto F, typename... Args>
  void async(Args &&...args) {
    boost::asio::post(
        exec_, [ptr = shared_from_this(),
                args = std::make_tuple(std::forward<Args>(args)...)]() mutable {
          return std::apply(
              [ptr = ptr.get()](auto &&...args) {
                (ptr->*F)(std::move(args)...);
              },
              std::move(args));
        });
  }

  void execute_request(request_data request);
  void execute_waiting_requests();
  void on_request_completed(uint64_t request_id,
                            http_result_data &&http_result_data,
                            http_content_ptr &&handle,
                            boost::system::error_code ec);

 private:
  const http_client_settings settings_;
  http_executor_type exec_;
  http_connection_pool connection_pool_;
  std::map<uint64_t, request_data> requests_;
  uint64_t request_id_{0};
  uint64_t in_progress_request_cnt_{0};
};
}  // namespace cpp_frame::http
