// #pragma once
// #define BOOST_ASIO_HAS_CO_AWAIT

// #include <cpp_frame/http_client/http_request_result.h>

// #include <boost/asio.hpp>
// #include <coroutine>
// #include <memory>

// namespace cpp_frame::http {

// struct use_coro_t {};
// constexpr use_coro_t use_coro;
// }  // namespace cpp_frame::http

// namespace boost {
// namespace asio {
// template <typename ReturnType>
// class async_result<cpp_frame::http::use_coro_t,
//                    ReturnType(cpp_frame::http::http_request_result)> {
//   struct shared_info {
//     shared_info() : m_has_handle(false), m_has_result(false) {}
//     std::coroutine_handle<> m_coro;
//     bool m_has_handle;
//     cpp_frame::http::http_request_result m_result;
//     bool m_has_result;
//   };

//  public:
//   struct [[nodiscard]] Awaiter {
//     Awaiter(std::shared_ptr<shared_info> info) : m_shared_info(info) {}
//     std::shared_ptr<shared_info> m_shared_info;
//     bool await_ready() { return m_shared_info->m_has_result; }
//     cpp_frame::http::http_request_result await_resume() {
//       return m_shared_info->m_result;
//     }
//     void await_suspend(std::coroutine_handle<> coro) {
//       m_shared_info->m_coro = coro;
//       m_shared_info->m_has_handle = true;
//     }
//   };

//   using return_type = Awaiter;
//   std::shared_ptr<shared_info> m_shared_info;

//   struct completion_handler_type {
//     std::shared_ptr<shared_info> m_shared_info;
//     template <typename Whatever>
//     completion_handler_type(Whatever const&)
//         : m_shared_info(std::make_shared<shared_info>()) {}
//     void operator()(cpp_frame::http::http_request_result result) {
//       m_shared_info->m_result = std::move(result);
//       if (m_shared_info->m_has_handle) {
//         m_shared_info->m_coro.resume();
//       }
//     }
//   };

//   explicit async_result(completion_handler_type& completion_handler)
//       : m_shared_info(completion_handler.m_shared_info) {}

//   async_result(const async_result&) = delete;
//   async_result& operator=(const async_result&) = delete;
//   return_type get() { return Awaiter(m_shared_info); }
// };
// }  // namespace asio
// }  // namespace boost
