
#pragma once
#include <cpp_frame/http_client/http_request_result.h>
#include <cpp_frame/http_client/request_data.h>

#include <boost/asio.hpp>
namespace cpp_frame::http {
struct request_data;
class completion_handler_invoker {
 public:
  static void invoke_handler(const request_data& request_data,
                             http_request_result&& result) {
    boost::asio::post(
        request_data.completion_executor,
        [res = std::move(result), handler = request_data.handler]() mutable {
          handler(std::move(res));
        });
  }
};
}  // namespace cpp_frame::http