#pragma once
#include <cpp_frame/http_client/http_request_result.h>

#include <future>

namespace cpp_frame::http {
struct use_std_future_t {};
constexpr use_std_future_t use_std_future;
}  // namespace cpp_frame::http
namespace boost {
namespace asio {
template <typename ReturnType>
class async_result<cpp_frame::http::use_std_future_t,
                   ReturnType(cpp_frame::http::http_request_result &&)> {
 public:
  using return_type = std::future<cpp_frame::http::http_request_result>;

  struct completion_handler_type {
    std::shared_ptr<std::promise<cpp_frame::http::http_request_result>>
        m_promise;
    template <typename Whatever>
    completion_handler_type(Whatever const &)
        : m_promise(std::make_shared<
                    std::promise<cpp_frame::http::http_request_result>>()) {}
    void operator()(cpp_frame::http::http_request_result result) {
      m_promise->set_value(std::move(result));
    }
  };

  explicit async_result(completion_handler_type &completion_handler)
      : future_(completion_handler.m_promise->get_future()) {}

  async_result(const async_result &) = delete;
  async_result &operator=(const async_result &) = delete;
  return_type get() { return std::move(future_); }

 private:
  std::future<cpp_frame::http::http_request_result> future_;
};
}  // namespace asio
}  // namespace boost