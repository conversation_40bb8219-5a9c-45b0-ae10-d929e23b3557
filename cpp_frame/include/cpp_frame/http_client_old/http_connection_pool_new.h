#pragma once
#include <cpp_frame/http_client/http_content.h>

#include <cstddef>
#include <list>
#include <memory>
#include <queue>
#include <stack>

#include "cpp_frame/http_client/http_connection.h"
namespace cpp_frame::http {
class http_connection_pool {
 public:
  http_connection_pool(const http_executor_type &exec, const std::string &host,
                       const std::string &port, size_t max_connect)
      : exec_(exec), host_(host), port_(port), max_connect_cnt_(max_connect) {}
  ~http_connection_pool() {
    std::cout << "end http_connection_pool" << std::endl;
    while (!valid_pool_.empty()) {
      std::cout << "use_count:" << valid_pool_.front().use_count() << std::endl;
      valid_pool_.pop_front();
    }
    while (!running_pool_.empty()) {
      std::cout << "use_count:" << running_pool_.front().use_count()
                << std::endl;
      running_pool_.pop_front();
    }
  }
  http_content_ptr get_connection() {
    size_t all_connect = valid_pool_.size() + running_pool_.size();
    if (all_connect > max_connect_cnt_) {
      return nullptr;
    }
    // http_content_ptr client;
    if (valid_pool_.empty()) {
      running_pool_.push_back(std::make_shared<http_content>(exec_, host_, port_));
      std::cout << "client get_connection" << running_pool_.front().use_count() << std::endl;
    }
    else {
      // client = valid_pool_.front();
      // valid_pool_.pop_front();
      // running_pool_.push_back(client);
    }
    return running_pool_.front();
  }

  void release_connection(http_content_ptr handle, bool clean_up) {
    if (clean_up) {
      running_pool_.remove(handle);
      valid_pool_.remove(handle);
    }
    else {
      running_pool_.remove(handle);
      valid_pool_.push_back(handle);
    }
  }

  size_t get_free_conn_cnt() { return 0; }

 private:
  http_executor_type exec_;
  std::string host_;
  std::string port_;
  size_t max_connect_cnt_;
  http_content_ptr handle_;
  std::list<http_content_ptr> valid_pool_;
  std::list<http_content_ptr> running_pool_;
};
}  // namespace cpp_frame::http
