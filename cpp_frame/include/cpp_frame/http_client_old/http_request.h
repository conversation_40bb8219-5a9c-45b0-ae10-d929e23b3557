#pragma once
#include <stdint.h>

#include <boost/beast.hpp>
#include <string>
#include <vector>
namespace cpp_frame::http {
class http_request {
 public:
  inline static constexpr uint32_t DEFAULT_TIMEOUT_MSEC = 30 * 1000;
  http_request(boost::beast::http::verb http_method, std::string target,
               std::string body,
               std::vector<std::pair<std::string, std::string>> http_headers,
               uint32_t timeout_msec = DEFAULT_TIMEOUT_MSEC)
      : http_method_(http_method),
        target_(target),
        body_(body),
        http_headers_(http_headers),
        timeout_msec_(timeout_msec) {}

  boost::beast::http::verb get_http_method() const { return http_method_; }
  const std::string &get_target() const { return target_; }
  uint32_t get_timeout_msec() const { return timeout_msec_; }
  const std::vector<std::pair<std::string, std::string>> &get_http_headers()
      const {
    return http_headers_;
  }
  const std::string &get_body() const { return body_; }
  uint64_t start_time;

 private:
  boost::beast::http::verb http_method_;
  std::string target_;
  std::string body_;
  std::vector<std::pair<std::string, std::string>> http_headers_;
  uint32_t timeout_msec_;
};
}  // namespace cpp_frame::http
