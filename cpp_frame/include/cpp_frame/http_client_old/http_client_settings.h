#pragma once
#include <boost/asio.hpp>
#include <chrono>
#include <stdint.h>
namespace cpp_frame::http {
struct http_client_settings
{
    http_client_settings() : max_parallel_requests(25), max_attempts(5)
    {
    }
    http_client_settings(uint32_t max_parallel_requests, uint32_t max_attempts)
        : max_parallel_requests(max_parallel_requests), max_attempts(max_attempts)
    {
    }
    const uint32_t max_parallel_requests;
    const uint32_t max_attempts;
    // std::chrono::milliseconds resolve_timeout   = std::chrono::seconds(10);
    // std::chrono::milliseconds connect_timeout   = std::chrono::seconds(10);
    // std::chrono::milliseconds handshake_timeout = std::chrono::seconds(5);
};

using http_executor_type = boost::asio::io_context::executor_type;
using http_header_type   = std::vector<std::pair<std::string, std::string>>;

} // namespace http
