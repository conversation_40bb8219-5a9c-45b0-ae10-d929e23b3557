#pragma once
#include <cpp_frame/http_client/http_connection.h>
#include <cpp_frame/http_client/http_request.h>

#include <boost/system/error_code.hpp>
#include <functional>
#include <iostream>
#include <memory>
#include <optional>
#include <vector>
namespace cpp_frame::http {
struct http_result_data {
  std::shared_ptr<const http_request> request;
  boost::beast::http::response<boost::beast::http::string_body> result_data;
};

class http_content : public std::enable_shared_from_this<http_content> {
 public:
  http_content(const http_executor_type &exec, const std::string &host,
               const std::string &port);
  ~http_content() { std::cout << "end http_content" << std::endl; }
  std::pair<std::string, std::uint16_t> get_host_and_port();
  void start(std::shared_ptr<const http_request> request,
             std::function<void(http_result_data &&, boost::system::error_code)>
                 callback);
  void on_http_complete(
      const boost::beast::http::response<boost::beast::http::string_body> &rsp,
      boost::system::error_code ec);
  void complete_request(const boost::system::error_code &ec);
  void finish();

  void start_async(
      std::shared_ptr<const http_request> request,
      std::function<void(http_result_data &&, boost::system::error_code)>
          callback);
  void cancel();
  void cancel_async();
  void async_close();
  void close();
  bool is_valid();

 private:
  template <auto F, typename... Args>
  void async(Args &&...args) {
    boost::asio::dispatch(
        exec_, [ptr = this->shared_from_this(),
                args = std::make_tuple(std::forward<Args>(args)...)]() mutable {
          return std::apply(
              [ptr = ptr.get()](auto &&...args) {
                (ptr->*F)(std::move(args)...);
              },
              std::move(args));
        });
  }

 private:
  http_executor_type exec_;
  std::shared_ptr<const http_request> current_request_;
  std::function<void(http_result_data &&, boost::system::error_code)>
      completed_request_callback_;
  boost::asio::deadline_timer timer_;
  http_result_data result_;
  http_conn_ptr connection_;
  std::string host_;
  std::string port_;
  bool is_valid_{true};
};

using http_content_ptr = std::shared_ptr<http_content>;
}  // namespace cpp_frame::http
