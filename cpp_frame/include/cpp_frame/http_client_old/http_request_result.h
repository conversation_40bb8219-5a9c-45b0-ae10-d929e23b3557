#pragma once
#include <stdint.h>

#include <algorithm>
#include <boost/beast.hpp>
#include <boost/system/error_code.hpp>
#include <cassert>
#include <cctype>  // tolower
#include <chrono>
#include <iostream>
#include <memory>
#include <string>
#include <system_error>
#include <vector>

namespace cpp_frame::http {
struct http_request_stats {
  std::chrono::duration<double> name_lookup_time_s;
  std::chrono::duration<double> total_time_s;
  int64_t avg_download_speed_bps;
  int64_t avg_upload_speed_bps;
  int64_t downloaded_bytes;
  int64_t uploaded_bytes;
};

class http_request_result {
 public:
  http_request_result(
      boost::beast::http::response<boost::beast::http::string_body> resp,
      boost::system::error_code ec)
      : response(std::move(resp)), error_code(ec) {}
  boost::beast::http::response<boost::beast::http::string_body> response;
  boost::system::error_code error_code;
};

}  // namespace cpp_frame::http
