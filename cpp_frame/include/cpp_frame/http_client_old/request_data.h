#pragma once
#include <cpp_frame/http_client/http_content.h>
#include <cpp_frame/http_client/http_request.h>
#include <cpp_frame/http_client/http_request_result.h>

#include <boost/asio.hpp>
#include <functional>
#include <memory>

namespace cpp_frame::http {
enum class request_state {
  waiting_retry = 0,  // Waiting to retry after error or redirection
  waiting = 1,        // Waiting in the requests queue
  in_progress = 2     // Request being executed
};

using completion_handler = std::function<void(http_request_result)>;

struct request_data {
  request_data(std::shared_ptr<const http_request> web_request,
               completion_handler handler, http_executor_type executor)
      : cur_http_request(std::move(web_request)),
        handler(std::move(handler)),
        completion_executor(std::move(executor)) {}
  uint64_t request_id{0};
  request_state cur_request_state{request_state::waiting};
  std::shared_ptr<const http_request> cur_http_request{nullptr};
  completion_handler handler;
  http_executor_type completion_executor;
  uint64_t creation_time;
  uint64_t exec_time;
  std::uint32_t retries{0};
};
}  // namespace cpp_frame::http