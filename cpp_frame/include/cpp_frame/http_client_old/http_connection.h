#pragma once
#include <cpp_frame/http_client/http_client_settings.h>

#include <boost/asio/connect.hpp>
#include <boost/asio/io_context.hpp>
#include <boost/asio/ip/tcp.hpp>
#include <boost/asio/ssl/error.hpp>
#include <boost/asio/ssl/stream.hpp>
#include <boost/asio/strand.hpp>
#include <boost/beast/core.hpp>
#include <boost/beast/http.hpp>
#include <boost/beast/ssl.hpp>
#include <boost/beast/version.hpp>
#include <boost/optional.hpp>
#include <boost/system/error_code.hpp>
#include <functional>
#include <memory>
#include <optional>
#include <vector>
namespace cpp_frame::http {
class http_connection : public std::enable_shared_from_this<http_connection> {
 public:
  using READ_CB = std::function<void(
      const boost::beast::http::response<boost::beast::http::string_body>,
      boost::system::error_code)>;
  http_connection(const http_executor_type &exec,
                  const std::string &host,
                  const std::string &port);
  ~http_connection();
  bool is_open() { return is_open_; }
  void write(boost::beast::http::verb method, const std::string &target,
             const std::string &body,
             const std::vector<std::pair<std::string, std::string>> &header,
             READ_CB &&cb);
  void close();
  void connect();

 private:
  void read();
  void write_http();
  void on_resolve(boost::system::error_code ec,
                  boost::asio::ip::tcp::resolver::results_type results);
  void on_connect(boost::system::error_code ec,
                  const boost::asio::ip::tcp::endpoint &ep);
  void on_handshake(boost::system::error_code ec);
  void on_write(boost::system::error_code ec, size_t);
  void on_read(boost::system::error_code ec, size_t);

 private:
  http_executor_type excutor_;
  boost::asio::ssl::context ctx_{boost::asio::ssl::context::tlsv11_client};
  std::optional<boost::asio::ssl::stream<boost::beast::tcp_stream>> stream_;
  boost::asio::ip::tcp::resolver resolver_;
  boost::asio::ip::tcp::resolver::results_type resolve_results_;
  std::string host_;
  std::string port_;
  bool is_open_{false};
  READ_CB read_cb_{nullptr};

  boost::beast::flat_buffer buffer_;
  boost::beast::http::request<boost::beast::http::string_body> req_;
  boost::beast::http::response<boost::beast::http::string_body> response_;
};

using http_conn_ptr = std::shared_ptr<http_connection>;
}  // namespace cpp_frame::http
