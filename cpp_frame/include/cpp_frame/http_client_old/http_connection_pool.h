#pragma once
#include <cpp_frame/http_client/http_content.h>

#include <cstddef>
#include <stack>
namespace cpp_frame::http {
class http_connection_pool {
 public:
  http_connection_pool(const http_executor_type &exec, const std::string &host,
                       const std::string &port)
      : exec_(exec), host_(host), port_(port) {
      handle_ =  std::make_shared<http_content>(exec_, host_, port_);
  }

  http_content_ptr get_connection() {
    // http_content_ptr handle;
    // if (pool_.empty()) {
    //   handle = std::make_shared<http_content>(exec_, host_, port_);
    // }
    // else {
    //   handle = pool_.top();
    //   pool_.pop();
    // }
    return handle_;
  }
  void release_connection(http_content_ptr handle, bool clean_up) {
    // if (!clean_up) {
    //   pool_.push(handle);
    // }
  }

  size_t get_free_conn_cnt() { return pool_.size(); }

 private:
  http_executor_type exec_;
  std::string host_;
  std::string port_;
  http_content_ptr handle_;
  std::stack<http_content_ptr> pool_;
};
}  // namespace cpp_frame::http
