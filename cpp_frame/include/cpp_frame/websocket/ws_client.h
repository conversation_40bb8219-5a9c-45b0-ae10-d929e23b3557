#pragma once

#include <cpp_frame/websocket/ws_config.h>

#include <boost/asio/strand.hpp>
#include <boost/beast/core.hpp>
#include <boost/beast/ssl.hpp>
#include <boost/beast/websocket.hpp>
#include <boost/beast/websocket/ssl.hpp>
#include <cstdlib>
#include <deque>
#include <functional>
#include <iostream>
#include <memory>
#include <optional>
#include <string>

namespace cpp_frame::ws {
// using websocket_stream = std::optional<boost::beast::websocket::stream<
//     boost::asio::ssl::stream<boost::asio::ip::tcp::socket>>>;
using websocket_stream = std::optional<boost::beast::websocket::stream<
    boost::beast::ssl_stream<boost::beast::tcp_stream>>>;
class ws_client : public std::enable_shared_from_this<ws_client> {
 public:
  using CONNECT_CB = std::function<void(boost::system::error_code)>;
  using CLOSE_CB = std::function<void(boost::system::error_code)>;
  using READ_CB =
      std::function<void(std::string_view, boost::system::error_code)>;
  using SEND_CB = std::function<void(boost::system::error_code)>;
  ws_client(const boost::asio::any_io_executor &exec);
  ~ws_client();
  void init(std::string host, std::string port, std::string endpoint) {
    host_ = host;
    port_ = port;
    endpoint_ = endpoint;
  }
  void set_connect_cb(const CONNECT_CB &cb) { connect_cb_ = cb; }
  void set_close_cb(const CLOSE_CB &cb) { close_cb_ = cb; }
  void set_read_cb(const READ_CB &cb) { read_cb_ = cb; }
  void set_send_cb(const SEND_CB &cb) { send_cb_ = cb; }

  void async_connect();
  void async_close();
  void reset();
  void send(std::string msg);
  void async_read();
  bool is_open() {
    if (!ws_stream_) {
      return false;
    }
    return ws_stream_->is_open();
  }

 private:
  void async_connect_inner();
  void do_write();
  void on_resolve(boost::beast::error_code ec,
                  boost::asio::ip::tcp::resolver::results_type results);
  void on_connect(
      boost::beast::error_code ec,
      boost::asio::ip::tcp::resolver::results_type::endpoint_type ep);
  void on_ssl_handshake(const boost::system::error_code &ec);
  void on_ws_handshake(const boost::system::error_code &ec);
  void on_control_frame(boost::beast::websocket::frame_type,
                        boost::string_view);
  // void on_message_sent(const boost::system::error_code &ec, size_t n);
  void on_close(const boost::system::error_code &);

 private:
  ws_executor_type exec_;
  boost::asio::ssl::context ctx_;
  websocket_stream ws_stream_;
  boost::asio::ip::tcp::resolver resolver_;
  boost::beast::flat_buffer buffer_;
  uint64_t id_;
  std::deque<std::string> write_msgs_;

  CONNECT_CB connect_cb_;
  READ_CB read_cb_;
  CLOSE_CB close_cb_;
  SEND_CB send_cb_;
  std::string host_;
  std::string port_;
  std::string endpoint_;
};
}  // namespace cpp_frame::ws
