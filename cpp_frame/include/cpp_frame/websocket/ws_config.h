#include <boost/asio.hpp>
namespace cpp_frame::ws {

using io_executor = boost::asio::io_context::executor_type;
// #define MULTI_THREADED
#ifdef MULTI_THREADED

using io_strand = boost::asio::strand<io_executor>;

inline io_strand new_strand(io_executor const &src) {
  return boost::asio::make_strand(src);
}

inline io_strand new_strand(io_strand const &src) {
  return new_strand(src.get_inner_executor());
}

#else

using io_strand = boost::asio::io_context::executor_type;

inline io_strand new_strand(io_executor const &src) { return src; }

#endif

inline io_executor to_io_executor(boost::asio::any_io_executor const &src) {
  if (auto e = src.target<boost::asio::io_context::executor_type>()) {
    return *e;
  }
  else if (auto s = src.target<boost::asio::strand<
                        boost::asio::io_context::executor_type>>()) {
    return s->get_inner_executor();
  }
  else {
    assert(!"unknown executor type");
    std::abort();
  }
}

using ws_executor_type = io_strand;

}  // namespace cpp_frame::ws