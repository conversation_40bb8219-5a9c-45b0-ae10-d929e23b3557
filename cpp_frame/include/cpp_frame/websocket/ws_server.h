#pragma once

#include <stdint.h>
#include <boost/beast/core.hpp>
#include <boost/beast/websocket.hpp>
#include <functional>

namespace cpp_frame::ws {
class ws_acceptor;
class ws_server_session;
class ws_server {
 public:
  using SESSION_CALLBACK = std::function<void(uint32_t sessionid)>;
  using READ_CALLBACK =
      std::function<void(uint32_t sessionid, std::string_view, bool is_text)>;
  ws_server(boost::asio::io_context &ioc);
  ~ws_server();

  int register_addr(std::string_view ip, unsigned short port);
  void set_open_callback(SESSION_CALLBACK cb) { open_callback_ = cb; }
  void set_close_callback(SESSION_CALLBACK cb) { close_callback_ = cb; }
  void set_read_callback(READ_CALLBACK cb) { read_callback_ = cb; }
  int send(uint32_t sessionid, std::string &msg);
  int send(uint32_t sessionid, std::string &&msg);

 private:
  void init_acceptor();
  void on_socket_created(boost::asio::ip::tcp::socket *socket,
                         boost::asio::ip::tcp::endpoint &endpoint,
                         boost::beast::error_code ec);
  void on_open(uint32_t sessionid, boost::asio::ip::tcp::endpoint &endpoint);
  void on_close(uint32_t sessionid, boost::asio::ip::tcp::endpoint &endpoint);
  void on_read(uint32_t sessionid, std::string_view msg, bool is_text);

 private:
  ws_acceptor *acceptor_;
  boost::asio::io_context &ioc_;
  SESSION_CALLBACK open_callback_;
  SESSION_CALLBACK close_callback_;
  READ_CALLBACK read_callback_;

  using SesstionMap = std::unordered_map<uint64_t, ws_server_session *>;
  SesstionMap map_session_;
};
}  // namespace cpp_frame::ws