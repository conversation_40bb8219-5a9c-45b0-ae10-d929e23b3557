#pragma once
#include <cpp_frame/shm/shm_common.h>

namespace cpp_frame::shm {

template <class Derived>
class shm_reader_base {
 public:
  bool init(const char* topic_addr) { return cast()->init(topic_addr); }
  frame_header* read() { return cast()->read(); }
  void pass(uint16_t size) { return cast()->pass(size); }

 protected:
  inline Derived* cast() { return static_cast<Derived*>(this); }
};

template <class Derived>
class shm_writer_base {
 public:
  bool init(uint16_t topicid, const char* topic_addr, uint16_t init_pages) {
    return cast()->init(topicid, topic_addr, init_pages);
  }
  void write(void* __restrict buf, uint32_t size, uint16_t msg_id,
             uint8_t msg_type, uint8_t node_id, uint32_t request_id,
             uint8_t is_last, uint16_t error_code) {
    return cast()->write(buf, size, msg_id, msg_type, node_id, request_id,
                         is_last, error_code);
  }
 protected:
  inline Derived* cast() { return static_cast<Derived*>(this); }
};
}  // namespace cpp_frame::shm
