#pragma once

#include <cpp_frame/shm/shm_common.h>
#include <string.h>

#include <algorithm>
#include <cstdint>
#include <iostream>

namespace cpp_frame::shm {
template <uint32_t Bytes>
class shm_ring_queue {
 public:
  frame_header* alloc(uint16_t size) {
    size += sizeof(frame_header);
    uint16_t blk_sz = toBlk(size);
    uint32_t padding_sz = BLK_CNT - (written_idx % BLK_CNT);
    bool rewind = blk_sz > padding_sz;
    uint32_t advance_sz = blk_sz + (rewind ? padding_sz : 0);
    writing_idx = written_idx + advance_sz;
    if (rewind) {
      std::cout << "alloc rewind" << std::endl;
      blk[written_idx % BLK_CNT].header.size = 0;
      asm volatile("" : : "m"(blk), "m"(written_idx) :);
      written_idx += padding_sz;
    }
    frame_header& header = blk[written_idx % BLK_CNT].header;
    header.size = size;
    return &header;
  }

  void reset() {
    asm volatile("" : "=m"(written_idx) : :);
    if (written_idx == 0)
      memset(blk, 0, sizeof(blk));
  }

  void push() {
    asm volatile("" : : "m"(blk), "m"(written_idx) :);
    uint16_t blk_sz = toBlk(blk[written_idx % BLK_CNT].header.size);
    written_idx += blk_sz;
    asm volatile("" : : "m"(written_idx) :);
  }

  uint64_t get_written_idx() {
    asm volatile("" : "=m"(written_idx), "=m"(blk) : :);
    return written_idx;
  }

  frame_header* read(uint64_t& __restrict__ idx) {
    asm volatile("" : "=m"(written_idx), "=m"(blk) : :);
    if (idx >= written_idx) {
      return nullptr;
    }
    uint16_t size = blk[idx % BLK_CNT].header.size;
    uint32_t padding_sz = BLK_CNT - (idx % BLK_CNT);
    if (size == 0) {  // rewind
      asm volatile("" : "=m"(writing_idx) : :);
      if (idx + BLK_CNT < writing_idx) {
        std::cout << "lost msg" << std::endl;
        idx = written_idx;
        return nullptr;
      }
      idx += padding_sz;
      if (idx >= written_idx) {
        return nullptr;
      }
    }
    if (idx + BLK_CNT < writing_idx)[[unlikely]] {
      std::cout << "read lost msg writing_idx:" << writing_idx<<"idx:"<<idx<<std::endl;
      idx = written_idx;
      return nullptr;
    }
    return &blk[idx % BLK_CNT].header;
  }

  bool pass(uint64_t& __restrict__ idx, uint16_t size) {
    asm volatile("" : "=m"(writing_idx) : :);
    if (idx + BLK_CNT < writing_idx)[[unlikely]] {
      std::cout << "pass lost msg writing_idx:" << writing_idx<<"idx:"<<idx<<std::endl;
      idx = written_idx;
      return false;
    }
    idx += toBlk(size);
    return true;
  }

 private:
  struct Block {
    alignas(64) frame_header
        header;  // make it 64 bytes aligned, same as cache line size
  };
  static constexpr uint32_t BLK_CNT = Bytes / sizeof(Block);
  Block blk[Bytes / sizeof(Block)];
  static inline uint16_t toBlk(uint16_t bytes) {
    return (bytes + sizeof(Block) - 1) / sizeof(Block);
  }
  alignas(64) uint64_t written_idx = 0;
  uint64_t writing_idx = 0;
};

}  // namespace cpp_frame::shm
