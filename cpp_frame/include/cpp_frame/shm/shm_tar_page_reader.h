// tar .shm.tar.gz and then read support time filter and index file
#pragma once
#include <cpp_frame/shm/shm_base.h>
#include <cpp_frame/shm/shm_common.h>
#include <cpp_frame/shm/shm_page_queue.h>
#include <cpp_frame/shm/shm_util.h>

#include <algorithm>
#include <cstddef>
#include <cstdlib>
#include <filesystem>
#include <iostream>
#include <string>
#include <unordered_set>

namespace cpp_frame::shm {
template <uint32_t Bytes>
class shm_tar_page_reader {
 public:
  shm_tar_page_reader() : q_(nullptr) {}
  shm_tar_page_reader(const char *topic_addr) : topic_addr_(topic_addr) {}

  void init(const char *topic_addr) {
    topic_addr_ = std::string(topic_addr);
    size_t pos = topic_addr_.find_last_of("/");
    shm_dir_ = topic_addr_.substr(0, pos);
  }

  void set_index_filter(int begin, int end) {
    start_page_ = begin;
    end_page_ = end;
    page_id_ = start_page_;
    q_ = load_page();
    read_idx_ = 0;
  }
  // 2023-10-23 15:00:00 not support
  void set_timer_fileter(const char *begin, const char *end) {}

  frame_header *read() {
    if (q_ == nullptr) {
      return nullptr;
    }
    auto *header = q_->read(read_idx_, res);
    if (res == ReadOK) {
      return header;
    }
    else if (res == ReadAgain) {
      return nullptr;
    }
    else if (res == ReadNextPage) {
      page_id_++;
      q_ = load_page();
      read_idx_ = 0;
      return read();
    }
    return nullptr;
  }
  void pass_frame(uint16_t size) { q_->pass(read_idx_, size); }

 private:
  using shm_queue = shm_page_queue<Bytes>;

  shm_queue *load_page() {
    if (page_id_ != start_page_) {
      std::string old_path = topic_addr_ + "." + std::to_string(page_id_ - 1);
      release_mmap_buffer((void *)q_, sizeof(shm_queue));
      if (std::filesystem::exists(old_path)) {
        std::string rv_command = "rm -rf " + old_path;
        std::cout << "delete:" << rv_command << std::endl;
        system(rv_command.c_str());
      }
      if (page_id_ > end_page_) {
        return nullptr;
      }
    }
    std::string cur_path = topic_addr_ + "." + std::to_string(page_id_);
    std::string cur_tar_path =
        topic_addr_ + "." + std::to_string(page_id_) + ".tar.gz";
    std::cout << "load shm:" << cur_path << " tar:" << cur_tar_path
              << std::endl;
    if (!std::filesystem::exists(cur_tar_path)) {
      std::cout << "not exists tar file:" << cur_tar_path << std::endl;
      return nullptr;
    }
    std::string tar_command = "tar -zxvf " + cur_tar_path + " -C " + shm_dir_;
    std::cout << "unzip:" << tar_command << std::endl;
    system(tar_command.c_str());
    if (!std::filesystem::exists(cur_path)) {
      std::cout << "not exists shm file:" << cur_path << std::endl;
      return nullptr;
    }
    shm_queue *q = load_mmap_buffer<shm_queue>(cur_path.c_str(), false);
    std::cout << "load_mmap_buffer: " << cur_path << std::endl;
    return q;
  }

 private:
  std::string topic_addr_;
  std::string shm_dir_;
  uint64_t read_idx_;
  uint16_t page_id_;
  shm_queue *q_{nullptr};
  read_res res;
  int start_page_;
  int end_page_;
};
}  // namespace cpp_frame::shm