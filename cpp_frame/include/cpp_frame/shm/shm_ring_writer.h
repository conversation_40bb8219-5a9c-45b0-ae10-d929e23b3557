#include <cpp_frame/shm/shm_base.h>
#include <cpp_frame/shm/shm_ring_queue.h>
#include <cpp_frame/shm/shm_util.h>
#include <cpp_frame/utils/date.h>
#include <string.h>

#include <cstddef>
#include <string>
#include <vector>

namespace cpp_frame::shm {
template <uint32_t Bytes>
class shm_ring_writer {
 public:
  using shm_queue = shm_ring_queue<Bytes>;
  shm_ring_writer() = default;
  ~shm_ring_writer() = default;
  void init(uint16_t topicid, const char* topic_name, uint16_t init_pages) {
    topic_id_ = topicid;
    topic_name_ = std::string(topic_name);
    init_queue();
  }

  void write(void* __restrict buf, uint32_t size, uint16_t msg_id,
             uint8_t msg_type, uint8_t node_id, uint32_t request_id,
             uint16_t is_last, uint16_t error_code) {
    if (cur_q_) [[likely]] {
      frame_header* header = cur_q_->alloc(size);
      header->msg_id = msg_id;
      header->msg_type = msg_type;
      header->node_id = node_id;
      header->request_id = request_id;
      header->is_last = is_last;
      header->error_code = error_code;
      header->topic_id = topic_id_;
      header->timestamp = date::get_current_nano_sec();
      memcpy((void*)(header + 1), buf, size);
      cur_q_->push();
    }
  }

 private:
  bool init_queue() {
    shm_queue* q = load_mmap_buffer<shm_queue>(topic_name_.c_str());
    if (q == nullptr)
      return false;
    q->reset();
    cur_q_ = q;
    return true;
  }

 private:
  uint16_t topic_id_{0};
  std::string topic_name_;
  shm_queue* cur_q_{nullptr};
};
}  // namespace cpp_frame::shm