#pragma once
#include <cpp_frame/shm/shm_base.h>
#include <cpp_frame/shm/shm_common.h>
#include <cpp_frame/shm/shm_config.h>
#include <cpp_frame/shm/shm_page_reader.h>
#include <cpp_frame/shm/shm_ring_reader.h>
#include <cpp_frame/shm/shm_util.h>

#include <type_traits>
#include <unordered_set>
#include <variant>
#include <vector>

namespace cpp_frame::shm {
class shm_subcriber {
 public:
  template <shm_type type>
  void init_topic(uint16_t topic_id, const char *topic_addr, sub_mode mode) {
    if constexpr (type == shm_type::Ring) {
      shm_ring_reader<SHM_PAGE_SIZE> reader;
      reader.init(topic_addr);
      shm_readers_.emplace_back(reader);
    }
    else {
      shm_page_reader<SHM_PAGE_SIZE> reader;
      reader.init(topic_addr, mode);
      shm_readers_.emplace_back(reader);
    }
  }

  frame_header *read() {
    long min_nano = TIME_TO_LAST;
    frame_header *cur_header = nullptr;
    bool pass_res = true;
    for (auto &cur_reader : shm_readers_) {  // broadcast messages ...
      std::visit(
          [this, &min_nano, &cur_header](auto &cur) {
            frame_header *header = cur.read();
            if (header != nullptr) {
              long nano = header->timestamp;
              if (min_nano == TIME_TO_LAST || nano < min_nano) {
                min_nano = nano;
                cur_header = header;
                cur_shm_reader_ = &cur;
              }
            }
          },
          cur_reader);
    }
    if (cur_header != nullptr) {
      auto size = cur_header->size;
      std::visit(overloaded{
                     [&size](shm_page_reader<SHM_PAGE_SIZE> *arg) {
                       arg->pass_frame(size);
                     },
                     [&size, &pass_res](shm_ring_reader<SHM_PAGE_SIZE> *arg) {
                       pass_res = arg->pass_frame(size);
                     },
                 },
                 cur_shm_reader_);
    }
    if (!pass_res)[[unlikely]] {
      cur_header = nullptr;
    }
    return cur_header;
  }

 private:
  template <typename... Ts>
  using reader_variant = std::variant<Ts...>;
  using container_type = std::vector<
      reader_variant<shm_page_reader<SHM_PAGE_SIZE>, shm_ring_reader<SHM_PAGE_SIZE>>>;
  container_type shm_readers_;

  std::variant<shm_page_reader<SHM_PAGE_SIZE> *,
               shm_ring_reader<SHM_PAGE_SIZE> *>
      cur_shm_reader_;
  static constexpr long TIME_TO_LAST = -1;
};

}  // namespace cpp_frame::shm