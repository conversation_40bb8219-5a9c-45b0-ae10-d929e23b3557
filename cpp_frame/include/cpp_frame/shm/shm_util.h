#pragma once
#include <fcntl.h>
#include <stdint.h>
#include <sys/mman.h>
#include <sys/shm.h>
#include <sys/stat.h>
#include <unistd.h>
#include <algorithm>
#include <filesystem>
#include <regex>
#include <cstring>
#include <iostream>

namespace cpp_frame::shm {

template <class T>
T* load_mmap_buffer(const char* filename, bool is_writer = true) {
  // std::cout<<filename<<std::endl;
  int shm_fd = open(filename, (O_RDWR | O_CREAT), 0666);

  // int shm_fd = shm_open(filename, O_CREAT | O_RDWR, 0666);
  if (shm_fd == -1) {
    std::cerr << "shm_open failed: " << strerror(errno) << std::endl;
    return nullptr;
  }
  if (ftruncate(shm_fd, sizeof(T))) {
    std::cerr << "ftruncate failed: " << strerror(errno) << std::endl;
    close(shm_fd);
    return nullptr;
  }
  fallocate(shm_fd, 0, 0, sizeof(T));
  T* ret = (T*)mmap(0, sizeof(T), PROT_READ | PROT_WRITE,
                    MAP_SHARED | MAP_POPULATE, shm_fd, 0);
  close(shm_fd);
  if (ret == MAP_FAILED) {
    std::cerr << "mmap failed: " << strerror(errno) << std::endl;
    return nullptr;
  }
  return ret;
}
static bool release_mmap_buffer(void* address, size_t size) {
  void* buffer = reinterpret_cast<void*>(address);
  if (munmap(buffer, size) != 0) {
    return false;
  }
  return true;
}


static int get_page_num(const std::string &shm_name, const std::string &file_name) {
  std::string num_str = file_name.substr(
      shm_name.size() + 1, file_name.length() - shm_name.size() - 1);
  return atoi(num_str.c_str());
}

static std::vector<int> get_page_nums(const std::string &shm_name) {
  auto f = shm_name.find_last_of("/");
  auto shm_dir = shm_name.substr(0, f);
  auto filter = shm_name + "\\.[0-9].*";
  std::cout << "shm_dir:"<<shm_dir<<" filter:" << filter << std::endl;
  std::regex cur_filter(filter);
  std::vector<int> res;
  for (auto const &file : std::filesystem::directory_iterator(shm_dir)) {
    if (!file.is_directory() &&
        std::regex_match(file.path().string(), cur_filter)) {
      res.push_back(get_page_num(shm_name, file.path().string()));
    }
  }
  std::sort(res.begin(), res.end());
  if(res.empty()){
    res = {0};
  }
  return res;
}

template <class... Ts>
struct overloaded : Ts... {
  using Ts::operator()...;
};
template <class... Ts>
overloaded(Ts...) -> overloaded<Ts...>;
}  // namespace cpp_frame::shm