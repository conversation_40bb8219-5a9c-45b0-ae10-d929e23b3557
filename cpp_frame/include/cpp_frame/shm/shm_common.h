#ifndef SHM_COMMON
#define SHM_COMMON
#include <stdint.h>

namespace cpp_frame::shm {
struct frame_header {
  uint16_t size;
  uint16_t topic_id;
  uint16_t msg_id;
  uint8_t msg_type;
  uint8_t node_id;  // 0:to all
  uint64_t timestamp;
  uint32_t request_id;
  uint8_t is_last;  // 1: is_last 0: is not last
  uint16_t error_code;
  uint64_t guid;
  uint8_t reserved_0;  
};

enum msg_type { FLOW, REQ, RSP };

enum read_res {
  ReadOK,
  ReadAgain,
  ReadNextPage
};
enum sub_mode { FromBegin = 0, FromLast = 1 };
enum shm_type { Ring = 0, Page = 1 };

}  // namespace cpp_frame::shm

#endif