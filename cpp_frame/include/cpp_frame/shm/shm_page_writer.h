#pragma once
#include <cpp_frame/shm/shm_base.h>
#include <cpp_frame/shm/shm_page_queue.h>
#include <cpp_frame/shm/shm_util.h>
#include <cpp_frame/utils/date.h>
#include <string.h>

#include <cstddef>
#include <string>
#include <vector>

namespace cpp_frame::shm {

/*
每次从page最大的开始写
*/
template <uint32_t Bytes>
class shm_page_writer {
 public:
  using shm_queue = shm_page_queue<Bytes>;
  enum { MAX_PAGES = 0XFFFF };
  shm_page_writer() = default;
  ~shm_page_writer() = default;

  void init(uint16_t topicid, const char *topic_name) {
    topic_id_ = topicid;
    topic_name_ = std::string(topic_name);
    // 找到最大的page ./test/record.shm.0  /test/record.shm.1 /test/record.shm.2
    page_id_ = *(get_page_nums(topic_name_).end() - 1);
    if (!load_page(page_id_)) {
      std::cout << "load error" << std::endl;
      return;
    }
  }

  void write(void *__restrict buf, uint32_t size, uint16_t msg_id,
             uint8_t msg_type, uint8_t node_id, uint32_t request_id,
             uint8_t is_last, uint16_t error_code) {
    if (cur_q_) [[likely]] {
      auto *header = cur_q_->alloc(size);
      if (header == nullptr) {
        page_id_++;
        release_mmap_buffer((void *)cur_q_, sizeof(shm_queue));
        if (load_page(page_id_)) {
          header = cur_q_->alloc(size);
        }
      }
      if (!header) [[unlikely]] {
        std::cout << "error" << std::endl;
        return;
      }
      header->msg_id = msg_id;
      header->msg_type = msg_type;
      header->node_id = node_id;
      header->request_id = request_id;
      header->is_last = is_last;
      header->error_code = error_code;
      header->topic_id = topic_id_;
      header->timestamp = date::get_current_nano_sec();
      memcpy((void *)(header + 1), buf, size);
      cur_q_->push();
    }
  }

 private:
  bool load_page(int page_id) {
    std::string cur_path = topic_name_ + "." + std::to_string(page_id);
    std::cout << "load page:" << cur_path << std::endl;
    shm_queue *q = load_mmap_buffer<shm_queue>(cur_path.c_str(), true);
    if (q == nullptr) {
      return false;
    }
    cur_q_ = q;
    return true;
  }

 private:
  uint16_t topic_id_{0};
  std::string topic_name_;
  uint16_t page_id_{0};
  shm_queue *cur_q_{nullptr};
  uint16_t init_pages_{0};
};

}  // namespace cpp_frame::shm
