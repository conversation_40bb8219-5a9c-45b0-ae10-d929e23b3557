#pragma once
#include <cpp_frame/shm/shm_base.h>
#include <cpp_frame/shm/shm_common.h>
#include <cpp_frame/shm/shm_config.h>
#include <cpp_frame/shm/shm_page_writer.h>
#include <cpp_frame/shm/shm_ring_writer.h>
#include <cpp_frame/shm/shm_util.h>

#include <variant>
namespace cpp_frame::shm {

class shm_publisher {
  enum { MAX_TOPIC_NUM = 0xFFFF };

 public:
  template <shm_type type>
  void init_topic(uint16_t topic_id, const char *topic_addr,
                  uint16_t init_pages) {
    if constexpr (type == shm_type::Ring) {
      shm_ring_writer<SHM_PAGE_SIZE> writer;
      shm_writers_[topic_id] = writer;
    }
    else {
      shm_page_writer<SHM_PAGE_SIZE> writer;
      shm_writers_[topic_id] = writer;
    }
    std::visit(overloaded{
                   [topic_id, topic_addr,
                    init_pages](shm_page_writer<SHM_PAGE_SIZE> &arg) {
                     arg.init(topic_id, topic_addr);
                   },
                   [topic_id, topic_addr,
                    init_pages](shm_ring_writer<SHM_PAGE_SIZE> &arg) {
                     arg.init(topic_id, topic_addr, init_pages);
                   },
               },
               shm_writers_[topic_id]);
  }

  int publish(uint16_t topicid, void *__restrict buf, uint32_t size,
              uint16_t msg_id, uint8_t node_id) {
    publish(topicid, buf, size, msg_id, FLOW, node_id, 0, 1, 0);
    return 0;
  }
  int publish(uint16_t topicid, void *__restrict buf, uint32_t size,
              uint16_t msg_id, uint8_t node_id, uint16_t is_last) {
    publish(topicid, buf, size, msg_id, FLOW, node_id, 0, is_last, 0);
    return 0;
  }
  int send_request(uint16_t topicid, void *__restrict buf, uint32_t size,
                   uint16_t msg_id, uint8_t node_id, uint32_t request_id,
                   uint16_t is_last = 1) {
    publish(topicid, buf, size, msg_id, REQ, node_id, request_id, is_last, 0);
    return 0;
  }

  int send_response(uint16_t topicid, void *__restrict buf, uint32_t size,
                    uint16_t msg_id, uint8_t node_id, uint32_t request_id,
                    uint16_t is_last, uint16_t error_code) {
    publish(topicid, buf, size, msg_id, RSP, node_id, request_id, is_last,
            error_code);
    return 0;
  }

 private:
  int publish(uint16_t topicid, void *__restrict buf, uint32_t size,
              uint16_t msg_id, uint8_t msg_type, uint8_t node_id,
              uint32_t request_id, uint16_t is_last, uint16_t error_code) {
    std::visit(overloaded{
                   [buf, size, msg_id, msg_type, node_id, request_id, is_last,
                    error_code](shm_page_writer<SHM_PAGE_SIZE> &arg) {
                     arg.write(buf, size, msg_id, msg_type, node_id, request_id,
                               is_last, error_code);
                   },
                   [buf, size, msg_id, msg_type, node_id, request_id, is_last,
                    error_code](shm_ring_writer<SHM_PAGE_SIZE> &arg) {
                     arg.write(buf, size, msg_id, msg_type, node_id, request_id,
                               is_last, error_code);
                   },
               },
               shm_writers_[topicid]);
    return 0;
  }

 private:
  template <typename... Ts>
  using writer_variant = std::variant<Ts...>;
  using writer_type = writer_variant<shm_page_writer<SHM_PAGE_SIZE>,
                                     shm_ring_writer<SHM_PAGE_SIZE>>;
  writer_type shm_writers_[MAX_TOPIC_NUM];
};
}  // namespace cpp_frame::shm