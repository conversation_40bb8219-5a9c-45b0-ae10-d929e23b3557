#pragma once
#include <cpp_frame/shm/shm_base.h>
#include <cpp_frame/shm/shm_common.h>
#include <cpp_frame/shm/shm_page_queue.h>
#include <cpp_frame/shm/shm_util.h>

#include <algorithm>
#include <cstdlib>
#include <iostream>
#include <string>
#include <unordered_set>

namespace cpp_frame::shm {
template <uint32_t Bytes>
class shm_page_reader {
 public:
  shm_page_reader() : q_(nullptr) {}
  shm_page_reader(const char *topic_addr) : topic_addr_(topic_addr) {}

  void init(const char *topic_addr, sub_mode mode) {
    topic_addr_ = std::string(topic_addr);
    if (mode == FromBegin)
      init_shm_queue(true);
    else
      init_shm_queue(false);
  }

  frame_header *read() {
    if (q_ == nullptr) {
      return nullptr;
    }
    auto *header = q_->read(read_idx_, res);
    if (res == ReadOK) {
      return header;
    }
    else if (res == ReadAgain) {
      return nullptr;
    }
    else if (res == ReadNextPage) {
      page_id_++;
      q_ = load_page();
      read_idx_ = 0;
      return nullptr;
    }
    return nullptr;
  }

  void pass_frame(uint16_t size) { q_->pass(read_idx_, size); }

 private:
  using shm_queue = shm_page_queue<Bytes>;
  shm_queue *load_page() {
    if (old_page_nums_.size()) {
      while (1) {
        auto itr = old_page_nums_.find(page_id_);
        if (itr == old_page_nums_.end()) {
          std::cout<<"not find:"<<page_id_<<std::endl;
          page_id_++;
        }
        else {
          break;
        }
      }
      old_page_nums_.erase(page_id_);
    }
    std::string cur_path = topic_addr_ + "." + std::to_string(page_id_);
    std::cout << "load shm:" << cur_path << std::endl;
    shm_queue *q = load_mmap_buffer<shm_queue>(cur_path.c_str(), false);
    return q;
  }
  bool init_shm_queue(bool is_begin) {
    auto page_nums = get_page_nums(topic_addr_);
    if (is_begin) {
      page_id_ = *page_nums.begin();
      std::for_each(page_nums.begin(), page_nums.end(), [this](int n) {
        old_page_nums_.insert(n);
      });
      q_ = load_page();
      read_idx_ = 0;
    }
    else {
      page_id_ = *(page_nums.end() - 1);
      q_ = load_page();
      if (q_ == nullptr)
        return false;
      read_idx_ = q_->get_written_idx();
    }
    return true;
  }

 private:
  std::string topic_addr_;
  uint64_t read_idx_;
  uint16_t page_id_;
  std::unordered_set<int> old_page_nums_;
  shm_queue *q_{nullptr};
  read_res res;
};

}  // namespace cpp_frame::shm