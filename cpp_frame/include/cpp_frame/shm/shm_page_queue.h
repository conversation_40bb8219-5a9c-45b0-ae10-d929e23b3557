#pragma once
#include <cpp_frame/shm/shm_common.h>
#include <stdint.h>

#include <cstring>
#include <iostream>

namespace cpp_frame::shm {
template <uint32_t Bytes>
class shm_page_queue {
 public:
  frame_header *alloc(uint32_t size) {
    size += sizeof(frame_header);
    uint32_t blk_sz = toBlk(size);
    uint32_t padding_sz = BLK_CNT - written_idx;
    bool rewind = blk_sz > padding_sz;
    if (rewind) {
      if (written_idx == BLK_CNT) {
        return nullptr;
      }
      else {
        blk[written_idx].header.size = 0;
        asm volatile("" : : "m"(blk) :);  // memory fence
        written_idx += padding_sz;
        return nullptr;
      }
    }
    frame_header &header = blk[written_idx].header;
    header.size = size;
    return &header;
  }

  void reset() {
    for (auto i = 0; i < BLK_CNT; i++) {
      Block b;
      memcpy(&blk[i], &b, sizeof(blk[i]));
    }
  }

  uint64_t get_written_idx() {
    asm volatile("" : "=m"(written_idx) : :);  // force read memory
    return written_idx;
  }

  bool is_last() {
    asm volatile("" : "=m"(written_idx) : :);  // force read memory
    std::cout << "written_idx:" << written_idx << std::endl;
    if (written_idx == BLK_CNT) {  // rewind
      return true;
    }
    return false;
  }

  void push() {
    asm volatile("" : : "m"(blk), "m"(written_idx) :);  // memory fence
    uint32_t blk_sz = toBlk(blk[written_idx].header.size);
    written_idx += blk_sz;
    asm volatile("" : : "m"(written_idx) :);  // force write memory
  }

  frame_header *read(uint64_t &__restrict__ idx, read_res &res) {
    asm volatile("" : "=m"(written_idx), "=m"(blk) : :);  // force read memory
    if (idx >= written_idx && idx != BLK_CNT) {
      res = ReadAgain;
      return nullptr;
    }
    if (idx == BLK_CNT) {
      res = ReadNextPage;
      return nullptr;
    }
    uint32_t size = blk[idx].header.size;
    if (size == 0) {  // rewind
      res = ReadNextPage;
      return nullptr;
    }
    frame_header &header = blk[idx].header;
    // idx += toBlk(size);
    res = ReadOK;
    return &header;
  }
  void pass(uint64_t &__restrict__ idx, uint16_t size) {
    asm volatile("" : "=m"(blk) : :);  // force read memory
    idx += toBlk(size);
  }

 private:
  struct Block {
    alignas(64) frame_header
        header;  // make it 64 bytes aligned, same as cache line size
  };
  static constexpr uint32_t BLK_CNT = Bytes / sizeof(Block);
  static_assert(BLK_CNT && !(BLK_CNT & (BLK_CNT - 1)),
                "BLK_CNT must be a power of 2");
  Block blk[BLK_CNT];
  static inline uint32_t toBlk(uint32_t bytes) {
    return (bytes + sizeof(Block) - 1) / sizeof(Block);
  }
  alignas(64) uint64_t written_idx = 0;
};

}  // namespace cpp_frame::shm