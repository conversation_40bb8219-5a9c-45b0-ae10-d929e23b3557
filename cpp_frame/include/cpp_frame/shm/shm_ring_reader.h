#pragma once

#include <cpp_frame/shm/shm_common.h>
#include <cpp_frame/shm/shm_base.h>
#include <cpp_frame/shm/shm_ring_queue.h>
#include <cpp_frame/shm/shm_util.h>

#include <iostream>

namespace cpp_frame::shm {

template <uint32_t Bytes>
class shm_ring_reader  {
 public:
  shm_ring_reader() : q_(nullptr) {}
  shm_ring_reader(const char* topic_addr) : topic_addr_(topic_addr) {}

  bool init(const char* topic_addr) {
    topic_addr_ = topic_addr;
    q_ = init_queue();
    if (q_ == nullptr) {
      return false;
    }
    read_idx_ = q_->get_written_idx();
    std::cout<<"read_idx_:"<<read_idx_<<std::endl;
    return true;
  }

  frame_header* read() {
    if (q_ == nullptr)
      return nullptr;
    return q_->read(read_idx_);
  }

  bool pass_frame(uint16_t size) { return q_->pass(read_idx_, size); }

 private:
  using shm_queue = shm_ring_queue<Bytes>;

  shm_queue* init_queue() {
    shm_queue* q = load_mmap_buffer<shm_queue>(topic_addr_.c_str(), false);
    return q;
  }

 private:
  std::string topic_addr_;
  uint64_t read_idx_;
  shm_queue* q_{nullptr};
};
}  // namespace cpp_frame::shm