#pragma once

#include <cpp_frame/fast_fmt_log/static_log_info.h>
#include <cpp_frame/struct_serialize/ser_reflection.h>
#include <fmt/args.h>
#include <stdint.h>

#include <cstdint>
#include <iterator>
#include <string>
#include <string_view>
#include <system_error>
#include <type_traits>

#include "cpp_frame/struct_serialize/struct_ser.h"

#ifdef LOG_USE_INT8_SIZE
using size_type = uint8_t;
constexpr uint8_t MAX_SIZE = UINT8_MAX;
#elif LOG_USE_INT16_SIZE
using size_type = uint16_t;
constexpr uint16_t MAX_SIZE = UINT16_MAX;
#else
using size_type = uint32_t;
constexpr uint32_t MAX_SIZE = UINT32_MAX;
#endif
namespace cpp_frame::fast_fmt_log::details {

template <typename CharType, std::size_t Size>
struct string_literal : public std::array<CharType, Size + 1> {
  using base = std::array<CharType, Size + 1>;
  using value_type = typename base::value_type;
  using pointer = typename base::pointer;
  using const_pointer = typename base::const_pointer;
  using iterator = typename base::iterator;
  using const_iterator = typename base::const_iterator;
  using reference = typename base::const_pointer;
  using const_reference = typename base::const_pointer;

  constexpr string_literal() = default;
  constexpr string_literal(const CharType (&value)[Size + 1]) {
    // don't use std::copy_n here to support low version stdlibc++
    for (size_t i = 0; i < Size + 1; ++i) {
      (*this)[i] = value[i];
    }
  }

  auto operator<=>(const string_literal &) const = default;

  constexpr std::size_t size() const { return Size; }

  constexpr bool empty() const { return !Size; }

  using base::begin;

  constexpr auto end() { return base::end() - 1; }

  constexpr auto end() const { return base::end() - 1; }

  using base::data;
  using base::operator[];
  using base::at;

 private:
  using base::cbegin;
  using base::cend;
  using base::rbegin;
  using base::rend;
};

template <typename CharType, std::size_t Size>
string_literal(const CharType (&value)[Size])
    -> string_literal<CharType, Size - 1>;

template <typename CharType, size_t Len1, size_t Len2>
decltype(auto) consteval operator+(string_literal<CharType, Len1> str1,
                                   string_literal<CharType, Len2> str2) {
  auto ret = string_literal<CharType, Len1 + Len2>{};
  // don't use std::copy_n here to support low version stdlibc++
  for (size_t i = 0; i < Len1; ++i) {
    ret[i] = str1[i];
  }
  for (size_t i = Len1; i < Len1 + Len2; ++i) {
    ret[i] = str2[i - Len1];
  }
  return ret;
}

template <typename Type>
concept container = requires(Type container) {
                      typename std::remove_cvref_t<Type>::value_type;
                      container.size();
                      container.begin();
                      container.end();
                    };

template <typename Type>
concept is_char_t =
    std::is_same_v<Type, signed char> || std::is_same_v<Type, char> ||
    std::is_same_v<Type, unsigned char> || std::is_same_v<Type, wchar_t> ||
    std::is_same_v<Type, char16_t> || std::is_same_v<Type, char32_t> ||
    std::is_same_v<Type, char8_t>;

template <typename Type>
concept string =
    container<Type> &&
    requires(Type container) {
      requires is_char_t<typename std::remove_cvref_t<Type>::value_type>;
      container.length();
      container.data();
    };

template <typename Type>
concept string_view = string<Type> && !requires(Type container) {
                                         container.resize(std::size_t{});
                                       };

template <typename Type>
concept cstring =
    std::is_same_v<std::remove_cvref_t<typename std::decay_t<Type>>, char *> ||
    std::is_same_v<std::remove_cvref_t<typename std::decay_t<Type>>,
                   const char *>;

template <typename Type>
concept pointer = std::is_same_v<Type, void *>;

template <typename T>
constexpr type_id get_integral_type() {
  if constexpr (std::is_same_v<int32_t, T>) {
    return type_id::int32_t;
  }
  else if constexpr (std::is_same_v<uint32_t, T>) {
    return type_id::uint32_t;
  }
  else if constexpr (std::is_same_v<int64_t, T> ||
                     (sizeof(long long) == 8 && std::is_same_v<T, long long>)) {
    return type_id::int64_t;
  }
  else if constexpr (std::is_same_v<uint64_t, T> ||
                     (sizeof(unsigned long long) == 8 &&
                      std::is_same_v<T, unsigned long long>)) {
    return type_id::uint64_t;
  }
  else if constexpr (std::is_same_v<int8_t, T> ||
                     std::is_same_v<signed char, T>) {
    return type_id::int8_t;
  }
  else if constexpr (std::is_same_v<uint8_t, T> ||
                     std::is_same_v<unsigned char, T>) {
    return type_id::uint8_t;
  }
  else if constexpr (std::is_same_v<int16_t, T>) {
    return type_id::int16_t;
  }
  else if constexpr (std::is_same_v<uint16_t, T>) {
    return type_id::uint16_t;
  }
  else if constexpr (std::is_same_v<char, T> || std::is_same_v<char8_t, T>) {
    return type_id::char_8_t;
  }
  else if constexpr (std::is_same_v<bool, T> && sizeof(bool)) {
    static_assert(sizeof(bool) == 1,
                  "sizeof(bool)!=1, which is not supported.");
    return type_id::bool_t;
  }
  else {
    /*
     * Due to different data model,
     * the following types are not allowed on macOS
     * but work on Linux
     * For example,
     * on macOS, `typedef unsigned long long uint64_t;`
     * on Linux, `typedef unsigned long int  uint64_t;`
     *
     * - long
     * - long int
     * - signed long
     * - signed long int
     * - unsigned long
     * - unsigned long int
     *
     * We add this static_assert to give more information about not supported
     * type.
     */
    static_assert(!std::is_same_v<long, T> && !std::is_same_v<unsigned long, T>,
                  "The long types have different width in "
                  "different data model. "
                  "see "
                  "https://en.cppreference.com/w/cpp/language/"
                  "types. "
                  "Please use fixed width integer types. e.g. "
                  "int32_t, int64_t. "
                  "see "
                  "https://en.cppreference.com/w/cpp/types/"
                  "integer.");
    static_assert(!sizeof(T), "not supported type");
    // This branch will always compiled error.
  }
}

template <typename T>
constexpr type_id get_floating_point_type() {
  if constexpr (std::is_same_v<float, T>) {
    if constexpr (!std::numeric_limits<float>::is_iec559 ||
                  sizeof(float) != 4) {
      static_assert(
          !sizeof(T),
          "The float type in this machine is not standard IEEE 754 32bits "
          "float point!");
    }
    return type_id::float32_t;
  }
  else if constexpr (std::is_same_v<double, T>) {
    if constexpr (!std::numeric_limits<double>::is_iec559 ||
                  sizeof(double) != 8) {
      static_assert(
          !sizeof(T),
          "The double type in this machine is not standard IEEE 754 64bits "
          "float point!");
    }
    return type_id::float64_t;
  }
  else if constexpr (std::is_same_v<long double, T>) {
    if constexpr (sizeof(long double) != 16 ||
                  std::numeric_limits<long double>::is_iec559) {
      static_assert(!sizeof(T),
                    "The long double type in this machine is not standard IEEE "
                    "754 128bits "
                    "float point!");
    }
    return type_id::float128_t;
  }
  else {
    static_assert(!sizeof(T), "not supported type");
  }
}

template <typename T>
consteval type_id get_type_id() {
  static_assert(CHAR_BIT == 8);
  if constexpr (std::is_enum_v<T>) {
    return get_integral_type<std::underlying_type_t<T>>();
  }
  else if constexpr (std::is_integral_v<T>) {
    return get_integral_type<T>();
  }
  else if constexpr (std::is_floating_point_v<T>) {
    return get_floating_point_type<T>();
  }
  else if constexpr (string<T> || string_view<T>) {
    return type_id::string_t;
  }
  else if constexpr (cstring<T>)
    return type_id::cstring_t;
  else if constexpr (pointer<T>) {
    return type_id::pointer_t;
  }
  else if constexpr (struct_serialize::is_ser_reflection_v<T>) {
    return type_id::custom_type;
  }
  else {
    static_assert(!sizeof(T), "not supported type");
  }
}

template <size_t Idx>
consteval decltype(auto) get_types() {
  return string_literal<char, 0>{};
}

template <size_t Idx, typename Arg, typename... Args>
constexpr decltype(auto) get_types() {
  constexpr auto id = get_type_id<Arg>();
  constexpr auto ret = string_literal<char, 1>{{static_cast<char>(id)}};
  return ret + get_types<Idx + 1, Args...>();
}

template <size_t Idx>
constexpr std::size_t calculate_needed_size(size_type *cstring_lens) {
  return 0;
}

template <size_t Idx, typename T, typename... Args>
constexpr std::size_t calculate_needed_size(size_type *cstring_lens,
                                            const T &item,
                                            const Args &...items) {
  using type = std::remove_cvref_t<decltype(item)>;
  std::size_t total = 0;
  if constexpr (string<T> || string_view<T>) {
    total += (item.size() + 1 + sizeof(size_type));
    return total + calculate_needed_size<Idx>(cstring_lens, items...);
  }
  else if constexpr (cstring<T>) {
    size_t len = strlen(item) + 1;
    cstring_lens[Idx] = len;
    total += (len + sizeof(size_type));
    return total + calculate_needed_size<Idx + 1>(cstring_lens, items...);
  }
  else if constexpr (std::is_fundamental_v<type> || std::is_enum_v<type> ||
                     pointer<type>) {
    total += sizeof(type);
    return total + calculate_needed_size<Idx>(cstring_lens, items...);
  }
  else if constexpr (struct_serialize::is_ser_reflection_v<T>) {
    // id size struct
    total += sizeof(struct_serialize::get_struct_id<T>());
    total += sizeof(size_type);
    total += sizeof(type);
    return total + calculate_needed_size<Idx>(cstring_lens, items...);
  }
  else {
    static_assert(!sizeof(type), "the type is not supported yet");
  }
}

consteval int get_cstring_cnt(const auto &l) {
  int num = 0;
  for (auto x : l) {
    if (x == static_cast<char>(type_id::cstring_t)) {
      num++;
    }
  }
  return num;
}

template <size_t Idx>
constexpr char *storage_args(size_type *cstring_lens, char *out) {
  return out;
}

template <size_t Idx, typename Arg, typename... Args>
constexpr char *storage_args(size_type *cstring_lens, char *out, const Arg &arg,
                             const Args &...args) {
  if constexpr (cstring<Arg>) {
    size_type len = cstring_lens[Idx];
    memcpy(out, &len, sizeof(size_type));
    memcpy(out + sizeof(size_type), arg, cstring_lens[Idx]);
    return storage_args<Idx + 1>(
        cstring_lens, out + sizeof(size_type) + cstring_lens[Idx], args...);
  }
  else if constexpr (string<Arg> || string_view<Arg>) {
    size_type len = arg.size() + 1;
    memcpy(out, &len, sizeof(size_type));
    memcpy(out + sizeof(size_type), arg.data(), len - 1);
    out[len - 1 + sizeof(size_type)] = 0;
    return storage_args<Idx>(cstring_lens, out + sizeof(size_type) + len,
                             args...);
  }
  else if constexpr (std::is_fundamental_v<Arg> || std::is_enum_v<Arg> ||
                     pointer<Arg>) {
    std::memcpy(out, &arg, sizeof(Arg));
    return storage_args<Idx>(cstring_lens, out + sizeof(Arg), args...);
  }
  else if constexpr (struct_serialize::is_ser_reflection_v<Arg>) {
    // id len struct
    uint32_t id = struct_serialize::get_struct_id<Arg>();
    size_type len = sizeof(arg);
    memcpy(out, &id, sizeof(uint32_t));
    memcpy(out + sizeof(uint32_t), &len, sizeof(size_type));
    memcpy(out + sizeof(size_type) + sizeof(uint32_t), &arg, sizeof(Arg));
    return storage_args<Idx>(cstring_lens,
                             out + sizeof(id) + sizeof(size_type) + sizeof(Arg),
                             args...);
  }
}

// https://github.com/MengRao/str
template <size_t SIZE>
class Str {
 public:
  static const int Size = SIZE;
  char s[SIZE];

  Str() {}
  Str(const char *p) { *this = *(const Str<SIZE> *)p; }

  char &operator[](int i) { return s[i]; }
  char operator[](int i) const { return s[i]; }

  template <typename T>
  void fromi(T num) {
    if constexpr (Size & 1) {
      s[Size - 1] = '0' + (num % 10);
      num /= 10;
    }
    switch (Size & -2) {
      case 18:
        *(uint16_t *)(s + 16) = *(uint16_t *)(digit_pairs + ((num % 100) << 1));
        num /= 100;
      case 16:
        *(uint16_t *)(s + 14) = *(uint16_t *)(digit_pairs + ((num % 100) << 1));
        num /= 100;
      case 14:
        *(uint16_t *)(s + 12) = *(uint16_t *)(digit_pairs + ((num % 100) << 1));
        num /= 100;
      case 12:
        *(uint16_t *)(s + 10) = *(uint16_t *)(digit_pairs + ((num % 100) << 1));
        num /= 100;
      case 10:
        *(uint16_t *)(s + 8) = *(uint16_t *)(digit_pairs + ((num % 100) << 1));
        num /= 100;
      case 8:
        *(uint16_t *)(s + 6) = *(uint16_t *)(digit_pairs + ((num % 100) << 1));
        num /= 100;
      case 6:
        *(uint16_t *)(s + 4) = *(uint16_t *)(digit_pairs + ((num % 100) << 1));
        num /= 100;
      case 4:
        *(uint16_t *)(s + 2) = *(uint16_t *)(digit_pairs + ((num % 100) << 1));
        num /= 100;
      case 2:
        *(uint16_t *)(s + 0) = *(uint16_t *)(digit_pairs + ((num % 100) << 1));
        num /= 100;
    }
  }

  static constexpr const char *digit_pairs =
      "00010203040506070809"
      "10111213141516171819"
      "20212223242526272829"
      "30313233343536373839"
      "40414243444546474849"
      "50515253545556575859"
      "60616263646566676869"
      "70717273747576777879"
      "80818283848586878889"
      "90919293949596979899";
};
}  // namespace cpp_frame::fast_fmt_log::details