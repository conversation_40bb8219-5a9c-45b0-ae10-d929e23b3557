#pragma once
#include <stdio.h>
#include <sys/types.h>
namespace cpp_frame::fast_fmt_log {
class file_appender {
 public:
  explicit file_appender(const char *filename);
  ~file_appender();
  void append(const char *logline, const size_t len);
  void flush();
  off_t written_bytes() const;

 private:
  size_t write(const char *logline, size_t len);
  FILE *fp_;
  char buffer_[64 * 1024];
  off_t written_bytes_;
};
}  // namespace cpp_frame::fast_fmt_log