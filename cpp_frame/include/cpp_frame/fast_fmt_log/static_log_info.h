#pragma once
#include <cpp_frame/fast_fmt_log/level.h>
#include <fmt/core.h>
#include <stdint.h>
#include <string.h>

#include <vector>
namespace cpp_frame::fast_fmt_log {

enum class type_id {
  // fundamental integral type
  int32_t = 1,
  uint32_t,
  int64_t,
  uint64_t,
  int8_t,
  uint8_t,
  int16_t,
  uint16_t,
  int128_t,  // TODO: support int128/uint128
  uint128_t,
  bool_t,
  char_8_t,
  char_16_t,
  char_32_t,
  w_char_t,   // Note: this type is not portable!Enable it with marco
              // STRUCT_PACK_ENABLE_UNPORTABLE_TYPE
              // fundamental float type
  float16_t,  // TODO: wait for C++23 standard float type
  float32_t,
  float64_t,
  float128_t,
  pointer_t,
  // template type
  string_t,
  cstring_t,

  //custom_type
  custom_type
};

struct static_log_info {
  static_log_info(const uint32_t line_num, log_level level,
                  fmt::string_view fmt_string)
      : line_num(line_num), level(level), fmt_string(fmt_string) {}
  const uint32_t line_num;
  const log_level level;
  std::vector<char> type_array;
  fmt::string_view fmt_string;
};
}  // namespace cpp_frame::fast_fmt_log
