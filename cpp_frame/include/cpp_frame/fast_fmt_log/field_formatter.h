#pragma once

#include <cpp_frame/struct_serialize/struct_ser.h>
#include <stdint.h>

#include <string>
namespace cpp_frame::fast_fmt_log {

class formatter {
 public:
  virtual std::string to_string(uint32_t id, void *f) = 0;
};

template <typename T>
class field_formatter : public formatter {
 public:
  std::string to_string(uint32_t id, void *f) {
    T *field = static_cast<T *>(f);
    return struct_serialize::to_json(*field);
  }
};
}  // namespace cpp_frame::fast_fmt_log