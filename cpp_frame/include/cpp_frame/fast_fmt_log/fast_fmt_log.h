#pragma once
#include <assert.h>
#include <cpp_frame/fast_fmt_log/field_formatter.h>
#include <cpp_frame/fast_fmt_log/file_appender.h>
#include <cpp_frame/fast_fmt_log/level.h>
#include <cpp_frame/fast_fmt_log/log_handler.h>
#include <cpp_frame/fast_fmt_log/staging_buffer.h>
#include <cpp_frame/fast_fmt_log/static_log_info.h>
#include <cpp_frame/fast_fmt_log/utils.h>
#include <cpp_frame/struct_serialize/ser_reflection.h>
#include <cpp_frame/struct_serialize/struct_ser.h>
#include <cpp_frame/utils/tscns.h>
#include <stdint.h>
#include <string.h>

#include <algorithm>
#include <cstdint>
#include <iostream>
#include <mutex>
#include <unordered_map>
#include <utility>
#include <vector>

namespace cpp_frame::fast_fmt_log {
class fast_fmt_log {
 public:
  static fast_fmt_log &get_logger() {
    static fast_fmt_log logger;
    return logger;
  }
  ~fast_fmt_log() {}

  template <typename... Args>
  inline void log(int &log_id, const int linenum, const log_level level,
                  fmt::string_view format, const Args &...args) {
    if (static_cast<int>(level) < static_cast<int>(cur_level_)) {
      return;
    }
    constexpr auto types = details::get_types<0, Args...>();
    if (log_id == UNASSIGNED_LOGID) {
      register_log(types, linenum, level, format, log_id);
    }
    int64_t timestamp = tscns_.rdtsc();
    constexpr auto cstring_cnt = details::get_cstring_cnt(types);
    size_type cstring_lens[cstring_cnt];
    auto alloc_size = details::calculate_needed_size<0>(cstring_lens, args...) +
                      sizeof(int64_t);
    auto header = alloc(alloc_size);
    if (!header) {
      std::cout << "header is null" << std::endl;
      return;
    }
    header->logId = log_id;
    char *write_pos = (char *)(header + 1);
    *(int64_t *)write_pos = timestamp;
    write_pos += sizeof(int64_t);
    details::storage_args<0>(cstring_lens, write_pos, args...);
    header->push(alloc_size);
  }

  void set_log_file(const char *filename) {
    log_handler_.set_log_file(filename);
  }
  void set_log_level(log_level logLevel) { cur_level_ = logLevel; }
  void set_log_level(const char *loglevel);
  log_level get_log_level() { return cur_level_; }
  void poll() { poll_inner(); }
  void flush();
  template <typename T>
  void register_field_formatter() {
    log_handler_.register_field_formatter<T>();
  }

  template <typename T, typename Formatter>
  void register_custom_field_formatter(Formatter *formatter) {
    log_handler_.register_custom_field_formatter<T, Formatter>(formatter);
  }

 private:
  fast_fmt_log() : log_handler_(tscns_) { tscns_.init(); }

 private:
  /////////////////////////////////////////////////////////////////
  void ensure_staging_buffer_allocated();
  staging_buffer::queue_header *alloc(size_t size);

  template <std::size_t N>
  void register_log(const details::string_literal<char, N> &l,
                    const int linenum, const log_level level,
                    fmt::string_view format, int &log_id) {
    std::lock_guard<std::mutex> lock(register_mutex_);
    if (log_id != UNASSIGNED_LOGID)
      return;
    log_id = static_cast<int32_t>(log_infos_.size()) +
             static_cast<int32_t>(log_handler_.get_log_infos_cnt());
    static_log_info info(linenum, level, format);
    for (auto t : l) info.type_array.push_back(t);
    log_infos_.emplace_back(info);
  }
  void adjust_heap(size_t i);
  void poll_inner();

 private:
  class staging_buffer_destroyer {
   public:
    explicit staging_buffer_destroyer() {}
    void staging_buffer_created() {}
    virtual ~staging_buffer_destroyer() {
      if (fast_fmt_log::staging_buffer_ != nullptr) {
        staging_buffer_->set_delete_flag();
        fast_fmt_log::staging_buffer_ = nullptr;
      }
    }
    friend class fast_fmt_log;
  };

 private:
  struct heap_node {
    heap_node(staging_buffer *buffer) : tb(buffer) {}

    staging_buffer *tb;
    const staging_buffer::queue_header *header{nullptr};
  };
  std::vector<heap_node> bg_thread_buffers_;

 private:
  log_level cur_level_{log_level::TRACE};
  std::vector<static_log_info> log_infos_;
  static thread_local staging_buffer *staging_buffer_;
  static thread_local staging_buffer_destroyer sbc_;
  std::vector<staging_buffer *> thread_buffers_;
  std::mutex register_mutex_;
  std::mutex buffer_mutex_;
  TSCNS tscns_;
  log_handler log_handler_;
};

}  // namespace cpp_frame::fast_fmt_log
#ifdef SYNC_LOG
#define FAST_LOG(level, format, ...)

#else
#define FAST_LOG(level, format, ...)                              \
  do {                                                            \
    static int log_id = UNASSIGNED_LOGID;                         \
    cpp_frame::fast_fmt_log::fast_fmt_log::get_logger().log(      \
        log_id, __LINE__, cpp_frame::fast_fmt_log::level, format, \
        ##__VA_ARGS__);                                           \
  } while (0)
#endif

#define POLL()                                                  \
  do {                                                          \
    cpp_frame::fast_fmt_log::fast_fmt_log::get_logger().poll(); \
  } while (0)

#define SET_LOG_FILE(FILE)                                                  \
  do {                                                                      \
    cpp_frame::fast_fmt_log::fast_fmt_log::get_logger().set_log_file(FILE); \
  } while (0)

#define SET_LOG_LEVEL(level)                                                  \
  do {                                                                        \
    cpp_frame::fast_fmt_log::fast_fmt_log::get_logger().set_log_level(level); \
  } while (0)

#define REGISTER_FIELD_FORMMATTER(FIELD)              \
  cpp_frame::fast_fmt_log::fast_fmt_log::get_logger() \
      .register_field_formatter<FIELD>();

#define REGISTER_CUSTOM_FIELD_FORMMATTER(FIELD, FORMATTER, formatter) \
  cpp_frame::fast_fmt_log::fast_fmt_log::get_logger()                 \
      .register_custom_field_formatter<FIELD, FORMATTER>(formatter);


