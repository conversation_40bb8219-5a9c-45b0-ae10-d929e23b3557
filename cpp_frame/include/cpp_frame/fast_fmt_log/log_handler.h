#pragma once
#include <cpp_frame/fast_fmt_log/field_formatter.h>
#include <cpp_frame/fast_fmt_log/file_appender.h>
#include <cpp_frame/fast_fmt_log/staging_buffer.h>
#include <cpp_frame/fast_fmt_log/static_log_info.h>
#include <cpp_frame/fast_fmt_log/utils.h>
#include <cpp_frame/utils/tscns.h>
#include <fmt/args.h>
#include <fmt/format.h>

#include <vector>

namespace cpp_frame::fast_fmt_log {

using memory_buffer = fmt::basic_memory_buffer<char, 10000>;

class log_handler {
 public:
  log_handler(TSCNS &tscns);
  ~log_handler();

  void set_log_file(const char *filename);
  void flush();
  
  size_t get_log_infos_cnt();
  void add_log_info(static_log_info info);
  void handle_log(const char *name, const staging_buffer::queue_header *header);

  template <typename T>
  void register_field_formatter() {
    formatter_maps_.insert(std::make_pair(struct_serialize::get_struct_id<T>(),
                                          new field_formatter<T>()));
    std::cout<<"register:"<<struct_serialize::get_struct_id<T>()<<std::endl;
  }

  template <typename T, typename Formatter>
  void register_custom_field_formatter(Formatter *formatter) {
    formatter_maps_.insert(
        std::make_pair(struct_serialize::get_struct_id<T>(), formatter));
  }

  std::string format_filed_to_json(uint32_t id, void *f);

 private:
  template <size_t I, typename T>
  inline void set_arg(const T &arg) {
    args[I] = fmt::detail::make_arg<fmt::format_context>(arg);
  }

  template <size_t I, typename T>
  inline void set_arg_val(const T &arg) {
    fmt::detail::value<fmt::format_context> &value_ =
        *(fmt::detail::value<fmt::format_context> *)&args[I];
    value_ = fmt::detail::arg_mapper<fmt::format_context>().map(arg);
  }

  template <typename T>
  void convert2fmt(char **arg_data,
                   fmt::dynamic_format_arg_store<fmt::format_context> &store,
                   const T &t) {
    store.push_back(t);
    *arg_data += sizeof(T);
  }
  void vformat_to(memory_buffer &out, fmt::string_view fmt,
                  fmt::format_args args);
  void decode_arg(char *arg_data, std::vector<char> &type_array,
                  fmt::string_view fmt_string);
  void reset_date();

 private:
  file_appender *file_{nullptr};
  std::vector<static_log_info> bg_log_infos_;
  TSCNS &tscns_;
  time_t last_second_{0};
  fmt::basic_memory_buffer<char, 10000> output_buf_;
  int64_t midnightNs;
  details::Str<3> weekdayName;
  details::Str<3> monthName;
  details::Str<4> year;
  char dash1 = '-';
  details::Str<2> month;
  char dash2 = '-';
  details::Str<2> day;
  char space = ' ';
  details::Str<2> hour;
  char colon1 = ':';
  details::Str<2> minute;
  char colon2 = ':';
  details::Str<2> second;
  char dot1 = '.';
  details::Str<9> nanosecond;
  details::Str<5> logLevel;
  std::vector<fmt::basic_format_arg<fmt::format_context>> args;
  std::unordered_map<uint32_t, formatter *> formatter_maps_;
};
}  // namespace cpp_frame::fast_fmt_log