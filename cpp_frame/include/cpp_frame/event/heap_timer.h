#include <stdint.h>

#include <functional>
#include <queue>
#include <unordered_map>
#include <unordered_set>
namespace cpp_frame::event {
using TASK_CB = std::function<void()>;
#define INVALD_TIME -1
struct callback_unit {
  uint64_t interval_mil_sec;
  uint64_t timeout_mil_sec;
  bool is_repeate;
  uint32_t task_id;
  TASK_CB func;
  bool is_run{true};
  callback_unit() : interval_mil_sec(0), timeout_mil_sec(0) {}
  callback_unit(uint64_t interval_mil_sec, uint64_t timeout_mil_sec,
                const TASK_CB &&func, bool is_repeate, int task_id)
      : interval_mil_sec(interval_mil_sec),
        timeout_mil_sec(timeout_mil_sec),
        is_repeate(is_repeate),
        task_id(task_id),
        func(func) {}
  bool operator()(callback_unit *i, callback_unit *j) {
    return i->timeout_mil_sec > j->timeout_mil_sec;
  }
};

class heap_timer {
 public:
  heap_timer() : task_id_(0), last_timestamp_(INVALD_TIME) {}

  uint64_t register_task(uint32_t milsec, const TASK_CB &&callback,
                         bool is_repeate) {
    if (last_timestamp_ == INVALD_TIME)
      return -1;
    task_id_++;
    callback_unit *unit =
        new callback_unit(milsec, last_timestamp_ + milsec, std::move(callback),
                          is_repeate, task_id_);
    callback_heap_.push(unit);
    register_tasks_[task_id_] = unit;
    return task_id_;
  }

  void unregister_task(int task_id) {
    auto itor = register_tasks_.find(task_id);
    if (itor == register_tasks_.end()) {
      return;
    }
    itor->second->is_run = false;
  }

  void update(long cur_time) {
    last_timestamp_ = cur_time;
    process_callback(cur_time);
  }

  uint64_t get_last_timestamp() { return last_timestamp_; }

 private:
  void process_callback(uint64_t cur_time) {
    while (!callback_heap_.empty()) {
      callback_unit *cb_unit = callback_heap_.top();
      if (!cb_unit->is_run) {
        callback_heap_.pop();
        delete cb_unit;
        continue;
      }
      if (cb_unit->timeout_mil_sec <= cur_time) {
        cb_unit->func();
        if (cb_unit->is_repeate) {
          cb_unit->timeout_mil_sec =
              last_timestamp_ + cb_unit->interval_mil_sec;
          callback_heap_.pop();
          callback_heap_.push(cb_unit);
        }
        else {
          callback_heap_.pop();
        }
      }
      else {
        break;
      }
    }
  }

 private:
  using CALLBACK_MIN_HEAP =
      std::priority_queue<callback_unit *, std::vector<callback_unit *>,
                          callback_unit>;
  CALLBACK_MIN_HEAP callback_heap_;
  std::unordered_map<uint32_t, callback_unit *> register_tasks_;
  uint32_t task_id_;
  int64_t last_timestamp_;
};
}  // namespace cpp_frame::event
