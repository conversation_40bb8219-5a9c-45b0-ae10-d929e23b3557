#pragma once
#include <cpp_frame/utils/date.h>

#include <boost/asio/deadline_timer.hpp>
#include <boost/asio/io_context.hpp>

namespace cpp_frame::event {
class asio_timer {
 public:
  enum timer_type { TIMER_ONCE = 0, TIMER_CIRCLE };
  enum timer_status { WAITING = 0, TIME_OUT, USER_CANCLE };
  asio_timer(boost::asio::io_context &ioc) : timer_(ioc) {}
  asio_timer(boost::asio::any_io_executor &exec) : timer_(exec) {}
  ~asio_timer() { stop(); }

  template <typename Func>
  void init(uint32_t milsec, Func f, timer_type type = TIMER_ONCE) {
    dwinterval_ = milsec;
    callback_ = f;
    type_ = type;
  }
  bool start() {
    status_ = WAITING;
    timer_.expires_from_now(boost::posix_time::milliseconds(dwinterval_));
    timer_.async_wait([&](boost::system::error_code ec) {
      if (!ec) {
        callback_();
      }
      if (type_ == TIMER_CIRCLE) {
        start();
      }
      else {
        status_ = TIME_OUT;
      }
    });
    return true;
  }
  void stop() {
    status_ = USER_CANCLE;
    boost::system::error_code ec;
    timer_.cancel(ec);
  }
  int status() { return status_; }
  void interval(uint32_t milsec) { dwinterval_ = milsec; }
  uint32_t interval() const { return dwinterval_; }

 private:
  boost::asio::deadline_timer timer_;
  uint32_t dwinterval_;  // unit : s
  timer_type type_;
  timer_status status_;
  std::function<void()> callback_;
};

// 带时间戳的定时器
class asio_timer_with_timestamp {
 public:
  enum timer_type { TIMER_ONCE = 0, TIMER_CIRCLE };
  enum timer_status { WAITING = 0, TIME_OUT, USER_CANCLE };
  asio_timer_with_timestamp(boost::asio::io_context &ioc) : timer_(ioc) {}
  asio_timer_with_timestamp(boost::asio::any_io_executor &exec)
      : timer_(exec) {}
  ~asio_timer_with_timestamp() { stop(); }

  template <typename Func>
  void init(uint32_t milsec, Func f, timer_type type = TIMER_ONCE) {
    dwinterval_ = milsec;
    callback_ = f;
    type_ = type;
  }
  bool start() {
    status_ = WAITING;
    timer_.expires_from_now(boost::posix_time::milliseconds(dwinterval_));
    timer_.async_wait([&](boost::system::error_code ec) {
      if (!ec) {
        uint64_t ns = cpp_frame::date::get_current_nano_sec();
        callback_(ns);
      }
      if (type_ == TIMER_CIRCLE) {
        start();
      }
      else {
        status_ = TIME_OUT;
      }
    });
    return true;
  }
  void stop() {
    status_ = USER_CANCLE;
    boost::system::error_code ec;
    timer_.cancel(ec);
  }
  int status() { return status_; }
  void interval(uint32_t milsec) { dwinterval_ = milsec; }
  uint32_t interval() const { return dwinterval_; }

 private:
  boost::asio::deadline_timer timer_;
  uint32_t dwinterval_;  // unit : s
  timer_type type_;
  timer_status status_;
  std::function<void(uint64_t)> callback_;
};
}  // namespace cpp_frame::event
