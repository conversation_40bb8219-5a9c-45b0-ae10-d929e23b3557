#include <cpp_frame/msg_queue/spsc_queue.h>
#include <cpp_frame/utils/posix_thread.h>
#include <unistd.h>

#include <atomic>
#include <functional>
#include <mutex>
#include <vector>
namespace cpp_frame::event {
class event_thread {
 public:
  using function_type = std::function<void()>;

 public:
  event_thread() = default;
  ~event_thread() {
    for (size_t i = 0; i < start_thread_funcs_.size(); ++i) {
      delete start_thread_funcs_[i];
    }
    for (size_t i = 0; i < stop_thread_funcs_.size(); ++i) {
      delete stop_thread_funcs_[i];
    }
    for (size_t i = 0; i < task_funcs_.size(); ++i) {
      delete task_funcs_[i];
    }
    for (size_t i = 0; i < bg_task_funcs_.size(); ++i) {
      delete bg_task_funcs_[i];
    }
  }
  void set_name(const char *name) { thread_.set_name(name); }
  void start(int milsec = 0) {
    thread_.start([this, milsec]() {
      run(milsec);
    });
  }

  void stop() { is_running_ = false; }
  void join() { thread_.join(); }
  const char *get_name() { return thread_.get_name(); }
  unsigned short get_thread_id() { return thread_.threadid(); }

  void register_start_in_thread_func(const function_type &f) {
    start_thread_funcs_.push_back(new function_type(f));
  }
  void register_stop_in_thread_func(const function_type &f) {
    stop_thread_funcs_.push_back(new function_type(f));
  }
  void register_task_function(const function_type &f) {
    std::lock_guard<std::mutex> lock(register_mutex_);
    task_funcs_.push_back(new function_type(f));
  }

  template <typename FUNC>
  void post(FUNC &&func) {
    event_funcs_.blockPush([&](function_type *f) {
      (*f) = func;
    });
  }

  bool get_thread_status() { return thread_.thread_status(); }

  void run(int milsec) {
    is_running_ = true;
    for (size_t i = 0; i < start_thread_funcs_.size(); ++i) {
      (*(start_thread_funcs_[i]))();
    }
    while (is_running_) {
      if (task_funcs_.size()) [[unlikely]] {
        std::unique_lock<std::mutex> lock(register_mutex_);
        for (auto task : task_funcs_) {
          bg_task_funcs_.push_back(task);
        }
        task_funcs_.clear();
      }
      for (const auto &func : bg_task_funcs_) {
        (*func)();
      }
      while (function_type *func = event_funcs_.front()) {
        (*func)();
        event_funcs_.pop();
      }
      if (milsec != 0) [[unlikely]]
        usleep(milsec * 1000);
    }
    for (size_t i = 0; i < stop_thread_funcs_.size(); ++i) {
      (*(stop_thread_funcs_[i]))();
    }
  }

 private:
  std::vector<function_type *> start_thread_funcs_;
  std::vector<function_type *> stop_thread_funcs_;
  std::vector<function_type *> task_funcs_;
  std::vector<function_type *> bg_task_funcs_;
  std::mutex register_mutex_;
  std::atomic_bool is_running_;
  cpp_frame::posix_thread thread_;
  cpp_frame::msg_queue::SPSCQueue<function_type, 1024 * 4> event_funcs_;
};
}  // namespace cpp_frame::event
