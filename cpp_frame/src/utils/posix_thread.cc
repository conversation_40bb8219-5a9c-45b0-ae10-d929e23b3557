#include <cpp_frame/utils/posix_thread.h>
#include <sched.h>
#include <stdio.h>
#include <sys/prctl.h>
#include <sys/syscall.h>
#include <unistd.h>

#include <stdexcept>
#include <string>

namespace cpp_frame {
void *system_posix_thread_function(void *arg) {
  posix_thread::auto_func_base_ptr func = {
      static_cast<posix_thread::func_base *>(arg)};
  const char *name = func.ptr->name();
  if (name != NULL && *name != 0) {
    prctl(PR_SET_NAME, func.ptr->name());
  }
  func.ptr->run();
  return 0;
}

static std::string get_process_name() {
  char sz[512] = {0};
  int r = readlink("/proc/self/exe", sz, sizeof(sz) - 1);
  if (r < 0) {
    fprintf(stderr, "get_process_name failed\n");
    return "";
  }

  const char *pos = strrchr(sz, '/');
  if (pos == NULL) {
    return sz;
  }
  else {
    return pos + 1;
  }
  return "";
}
#define INVALID_THREAD_ID 0XFFFF
#define INVALID_THREAD_SYSID -1
thread_local uint16_t posix_thread::thread_id_ = 0XFFFF;
thread_local int posix_thread::thread_sysid_ = -1;
posix_thread::~posix_thread() {
  if (is_running_)
    ::pthread_detach(thread_);
}
void posix_thread::set_name(const char *name) {
  std::string process_name = get_process_name();

  memset(thread_name_, 0, sizeof(thread_name_));
  int len = process_name.size();
  memcpy(thread_name_, process_name.c_str(), len);
  if (name != NULL) {
    thread_name_[len++] = '.';
    memcpy(thread_name_ + len, name, strlen(name));
  }
}
const char *posix_thread::get_name() { return thread_name_; }
void posix_thread::join() {
  if (is_running_) {
    ::pthread_join(thread_, 0);
    is_running_ = false;
  }
}
void posix_thread::exit() { ::pthread_exit(NULL); }
void posix_thread::start_thread(func_base *arg) {
  is_running_ = true;
  int error = ::pthread_create(&thread_, 0, system_posix_thread_function, arg);
  if (error != 0) {
    delete arg;
    static char sz[128] = {0};
    snprintf(sz, sizeof(sz) - 1, "pthread_create failed: %d", error);
    throw std::runtime_error(sz);
  }
}
void posix_thread::bind_to(int cpu_no) {
  int cpu_count = sysconf(_SC_NPROCESSORS_CONF);
  if (cpu_no < 0 || cpu_no >= cpu_count) {
    // throw std::runtime_error("posix_thread::bind_to, bad cpu_no");
    fprintf(stderr, "posix_thread::bind_to bad cpu_no[%d], cpu_count[%d]\n",
            cpu_no, cpu_count);
    return;
  }
  cpu_set_t mask;
  CPU_ZERO(&mask);
  CPU_SET(cpu_no, &mask);
  if (pthread_setaffinity_np(pthread_self(), sizeof(mask), &mask) < 0) {
    throw std::runtime_error("set thread affinity failed");
    return;
  }
}
int posix_thread::cpu_num() { return sysconf(_SC_NPROCESSORS_CONF); }
uint16_t posix_thread::threadid() {
  if (thread_id_ == INVALID_THREAD_ID) {
    int tid = syscall(SYS_gettid);
    thread_id_ = tid > 0XFFFF ? tid & 0XFFFF : tid;
  }
  return thread_id_;
}
int posix_thread::thread_sysid() {
  if (thread_sysid_ == INVALID_THREAD_SYSID) {
    thread_sysid_ = syscall(SYS_gettid);
  }
  return thread_sysid_;
}
int posix_thread::create_tls_key(tls_key_t *key, void (*desctructor)(void *)) {
  return ::pthread_key_create(key, desctructor);
}
int posix_thread::set_tls_value(tls_key_t key, const void *value) {
  return ::pthread_setspecific(key, value);
}
void *posix_thread::get_tls_value(tls_key_t key) {
  return pthread_getspecific(key);
}
int posix_thread::delete_tls_key(tls_key_t key) {
  return ::pthread_key_delete(key);
}
}  // namespace cpp_frame