#include <cpp_frame/utils/so_loader.h>
#include <string.h>

namespace cpp_frame {
class so_loader::so_func {
 public:
  typedef void *(*create_func_ptr)();
  typedef void (*destroy_func_ptr)(void *);

  create_func_ptr create_func;
  destroy_func_ptr destroy_func;
  std::string so_name;
  so_handle_type handle;

 public:
  so_func() : create_func(NULL), destroy_func(NULL), handle(NULL) {}
  so_func(so_handle_type h, const char *obj_name, create_func_ptr f_create,
          destroy_func_ptr f_destroy)
      : create_func(f_create),
        destroy_func(f_destroy),
        so_name(obj_name),
        handle(h) {}
};

so_loader::so_loader() {}

so_loader::~so_loader() {
  so_funcs_.clear();
  for (auto &so_func : so_vector_) {
    delete so_func;
  }
  so_vector_.clear();

  if (callback_) {
    delete callback_;
  }
}

void so_loader::load(const char *dir, const char *filter) {
  std::regex cur_filter(filter);
  for (auto const &file : std::filesystem::directory_iterator(dir)) {
    if (!file.is_directory() &&
        std::regex_match(file.path().filename().string(), cur_filter)) {
      std::cout << "file_name:" << file.path().filename().string()
                << " file_full_name:" << file.path().string() << std::endl;
      load_file(file.path().string().c_str());
    }
  }
}

int so_loader::load_file(const char *file_name) {
  so_handle_type h = dlopen(file_name, RTLD_NOW);
  if (h == NULL) {
    printf("%s\n", dlerror());
    return -1;
  }
  so_loader::so_func::create_func_ptr create_func =
      (so_loader::so_func::create_func_ptr)(get_so_func(h, "create"));
  so_loader::so_func::destroy_func_ptr destroy_func =
      (so_loader::so_func::destroy_func_ptr)(get_so_func(h, "destroy"));
  if (create_func == NULL || destroy_func == NULL) {
    if (create_func == NULL) {
      printf("%s\n", "create_func is null");
    }
    return -1;
  }

  so_func *s_f = new so_func(h, file_name, create_func, destroy_func);
  so_funcs_[s_f->so_name] = s_f;
  so_vector_.push_back(s_f);
  if (callback_)
    (*callback_)(s_f->so_name.c_str());
  return 0;
}
void *so_loader::create_obj(const char *so_name) {
  SO_FUNC_MAP::iterator itor = so_funcs_.find(so_name);
  if (itor == so_funcs_.end()) {
    return nullptr;
  }
  return ((itor->second)->create_func)();
}

void so_loader::destroy_obj(const char *so_name, void *obj) {
  SO_FUNC_MAP::iterator itor = so_funcs_.find(so_name);
  if (itor == so_funcs_.end()) {
    return;
  }
  ((itor->second)->destroy_func)(obj);
}

void *so_loader::get_so_func(const char *so_name, const char *func_name) {
  SO_FUNC_MAP::iterator itor = so_funcs_.find(so_name);
  if (itor == so_funcs_.end()) {
    return NULL;
  }
  return get_so_func(itor->second->handle, func_name);
}

void *so_loader::get_so_func(so_handle_type handle, const char *func_name) {
  if (nullptr == handle) {
    printf("handle is null\n");
    return nullptr;
  }
  return dlsym(handle, func_name);
}
}  // namespace cpp_frame