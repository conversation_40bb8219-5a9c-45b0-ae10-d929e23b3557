#include <cpp_frame/fast_fmt_log/fast_fmt_log.h>
#include <cpp_frame/http_client/http_client_settings.h>
#include <cpp_frame/utils/email_helper.h>

#include <boost/beast/core/detail/base64.hpp>
#include <iomanip>
#include <sstream>

namespace cpp_frame::utils {

email_helper::email_helper(const email_config& config,
                           const boost::asio::io_context::executor_type& exec)
    : config_(config), exec_(exec) {
  // 创建HTTP客户端，连接到SMTP服务器
  cpp_frame::http::http_client_settings settings(1, 3, "email_client");
  http_client_ = std::make_unique<cpp_frame::http::http_client>(
      settings, exec_, config.smtp_server, config.smtp_port,
      boost::asio::ssl::context::tlsv12_client);
}

email_helper::~email_helper() {}

std::string email_helper::base64_encode(const std::string& input) {
  std::string result;
  result.resize(boost::beast::detail::base64::encoded_size(input.size()));
  result.resize(boost::beast::detail::base64::encode(&result[0], input.data(),
                                                     input.size()));
  return result;
}

void email_helper::send_email(const email_message& msg,
                              email_callback callback) {
  // 构建SMTP命令序列
  std::string email_content = build_email_content(msg);

  // 设置HTTP头
  cpp_frame::http::http_header_type headers;
  headers.push_back({"Content-Type", "application/x-www-form-urlencoded"});

  // 发送HTTP请求
  http_client_->post(
      [callback](cpp_frame::http::http_request_result result) {
        bool success = result.error_code.value() == 0;
        std::string error_msg = success ? "" : result.error_code.message();

        FAST_LOG(INFO, "email_helper::send_email result:{} error_msg:{}",
                 success ? "success" : "failed", error_msg);

        if (callback) {
          callback(success, error_msg);
        }
      },
      "/", email_content, headers);
}

std::string email_helper::build_email_content(const email_message& msg) {
  // 获取当前时间
  auto now = std::chrono::system_clock::now();
  auto now_time_t = std::chrono::system_clock::to_time_t(now);
  std::tm now_tm = *std::localtime(&now_time_t);

  std::stringstream date_ss;
  date_ss << std::put_time(&now_tm, "%a, %d %b %Y %H:%M:%S %z");
  std::string date_str = date_ss.str();

  // 构建邮件头
  std::stringstream ss;
  ss << "MIME-Version: 1.0\r\n";
  ss << "Date: " << date_str << "\r\n";
  ss << "From: " << config_.from_email << "\r\n";

  // 添加收件人
  ss << "To: ";
  for (size_t i = 0; i < config_.to_emails.size(); ++i) {
    ss << config_.to_emails[i];
    if (i < config_.to_emails.size() - 1) {
      ss << ", ";
    }
  }
  ss << "\r\n";

  ss << "Subject: " << msg.subject << "\r\n";
  ss << "Content-Type: text/plain; charset=UTF-8\r\n";
  ss << "\r\n";  // 邮件头和正文之间的空行
  ss << msg.body;

  // 构建SMTP命令序列
  std::stringstream smtp_commands;
  smtp_commands << "EHLO " << config_.smtp_server << "\r\n";
  smtp_commands << "AUTH LOGIN\r\n";
  smtp_commands << base64_encode(config_.username) << "\r\n";
  smtp_commands << base64_encode(config_.password) << "\r\n";
  smtp_commands << "MAIL FROM:<" << config_.from_email << ">\r\n";

  // 添加收件人
  for (const auto& to_email : config_.to_emails) {
    smtp_commands << "RCPT TO:<" << to_email << ">\r\n";
  }

  smtp_commands << "DATA\r\n";
  smtp_commands << ss.str() << "\r\n.\r\n";  // 邮件内容以.结束
  smtp_commands << "QUIT\r\n";

  return smtp_commands.str();
}

}  // namespace cpp_frame::utils
