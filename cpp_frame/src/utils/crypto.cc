#include <cpp_frame/utils/crypto.h>
static constexpr unsigned char __hex_chars[16]   = {'0', '1', '2', '3', '4', '5', '6', '7', '8', '9', 'a', 'b', 'c', 'd', 'e', 'f'};
static constexpr unsigned char __blend_table[32] = {0, 128, 0, 128, 0, 128, 0, 128, 0, 128, 0, 128, 0, 128, 0, 128,
                                                    0, 128, 0, 128, 0, 128, 0, 128, 0, 128, 0, 128, 0, 128, 0, 128};
static constexpr unsigned char __dup_index[16]   = {0, 0, 1, 1, 2, 2, 3, 3, 4, 4, 5, 5, 6, 6, 7, 7};

namespace cpp_frame {

hex::hex()
{
    table_     = _mm_loadu_si128((__m128i const *)&__hex_chars[0]);
    mask_      = _mm_loadu_si128((__m128i const *)&__blend_table[0]);
    dup_index_ = _mm_loadu_si128((__m128i const *)&__dup_index[0]);
}
hex &hex::clear()
{
    r_.clear();
    return *this;
}

hex &hex::encode(const unsigned char *data, size_t size)
{
    __m128i _s      = _mm_set1_epi8(15);
    size_t  r_index = r_.size();
    r_.resize(r_.size() + size * 2);

    size_t i = 0;
    for (; size >= 8; i += 8, r_index += 16, size -= 8) {
        __m128i _r, _dh, _dl;                                         // result
        __m128i _data = _mm_loadl_epi64((__m128i_u const *)&data[i]); // read 8 bytes

        _dh = _mm_srli_epi16(_data, 4);      // shift right 4 bits
        _dh = _mm_and_si128(_dh, _s);        // remove higher bits
        _dh = _mm_shuffle_epi8(table_, _dh); // search in table

        _dl = _mm_and_si128(_data, _s);      // remove higher bits
        _dl = _mm_shuffle_epi8(table_, _dl); // search in table

        _dh = _mm_shuffle_epi8(_dh, dup_index_); // duplicate first
        _dl = _mm_shuffle_epi8(_dl, dup_index_); // duplicate second

        _r = _mm_blendv_epi8(_dh, _dl, mask_); // blend

        _mm_storeu_si128((__m128i *)&r_[r_index], _r); // store in r_
    }
    while (size > 0) {
        r_[r_index++] = __hex_chars[data[i] >> 4];
        r_[r_index++] = __hex_chars[data[i++] & 15];
        size--;
    }

    return *this;
}
const std::string &hex::final() const
{
    return r_;
}

///////////////////////////////////////////////////////////////////////////
signer::signer(const std::string &key) : hmac_(::HMAC_CTX_new())
{
    ::HMAC_Init_ex(hmac_, &key[0], key.size(), ::EVP_sha256(), nullptr);
}
signer::~signer()
{
    ::HMAC_CTX_free(hmac_);
}
void signer::update(const std::string_view &s)
{
    ::HMAC_Update(hmac_, (unsigned char *)&s[0], s.size());
}
const std::string &signer::final()
{
    unsigned char hash[EVP_MAX_MD_SIZE];
    unsigned int  size;
    ::HMAC_Final(hmac_, &hash[0], &size);
    return hex_.clear().encode(&hash[0], size).final();
}
} // namespace cpp_frame