#include <cpp_frame/fast_fmt_log/log_handler.h>
#include <cpp_frame/struct_serialize/struct_ser.h>

#include <cstdint>

namespace cpp_frame::fast_fmt_log {

log_handler::log_handler(TSCNS &tscns) : tscns_(tscns) {
  args.reserve(4096);
  args.resize(8);
  reset_date();
  set_arg<0>(fmt::string_view(year.s, 29));
  set_arg<1>(fmt::string_view(logLevel.s, 5));
  set_arg<2>(fmt::string_view());
}

log_handler::~log_handler() {
  if (file_) {
    file_->flush();
    delete file_;
  }
}
void log_handler::flush() {
  if (file_) {
    file_->flush();
  }
}
void log_handler::set_log_file(const char *filename) {
  if (file_ != nullptr)
    return;
  file_ = new file_appender(filename);
}
size_t log_handler::get_log_infos_cnt() { return bg_log_infos_.size(); }
void log_handler::add_log_info(static_log_info info) {
  bg_log_infos_.push_back(info);
}

void log_handler::vformat_to(memory_buffer &out, fmt::string_view fmt,
                             fmt::format_args args) {
  fmt::detail::vformat_to(out, fmt, args);
}

std::string log_handler::format_filed_to_json(uint32_t id, void *f) {
  auto itr = formatter_maps_.find(id);
  if (itr != formatter_maps_.end()) {
    return itr->second->to_string(id, f);
  }
  return "not found";
}

void log_handler::decode_arg(char *arg_data, std::vector<char> &type_array,
                             fmt::string_view fmt_string) {
  fmt::dynamic_format_arg_store<fmt::format_context> fmt_store;
  for (const auto type : type_array) {
    switch (type) {
      case static_cast<char>(type_id::int32_t):
        convert2fmt(&arg_data, fmt_store, *(int32_t *)(arg_data));
        break;
      case static_cast<char>(type_id::uint32_t):
        convert2fmt(&arg_data, fmt_store, *(uint32_t *)(arg_data));
        break;
      case static_cast<char>(type_id::int64_t):
        convert2fmt(&arg_data, fmt_store, *(int64_t *)(arg_data));
        break;
      case static_cast<char>(type_id::uint64_t):
        convert2fmt(&arg_data, fmt_store, *(uint64_t *)(arg_data));
        break;
      case static_cast<char>(type_id::int8_t):
        convert2fmt(&arg_data, fmt_store, *(int8_t *)(arg_data));
        break;
      case static_cast<char>(type_id::uint8_t):
        convert2fmt(&arg_data, fmt_store, *(uint8_t *)(arg_data));
        break;
      case static_cast<char>(type_id::int16_t):
        convert2fmt(&arg_data, fmt_store, *(int16_t *)(arg_data));
        break;
      case static_cast<char>(type_id::uint16_t):
        convert2fmt(&arg_data, fmt_store, *(uint16_t *)(arg_data));
        break;
      case static_cast<char>(type_id::bool_t):
        convert2fmt(&arg_data, fmt_store, *(bool *)(arg_data));
        break;
      case static_cast<char>(type_id::char_8_t):
        convert2fmt(&arg_data, fmt_store, *(char *)(arg_data));
        break;
      case static_cast<char>(type_id::float32_t):
        convert2fmt(&arg_data, fmt_store, *(float *)(arg_data));
        break;
      case static_cast<char>(type_id::float64_t): {
        convert2fmt(&arg_data, fmt_store, *(double *)(arg_data));
        break;
      }
      case static_cast<char>(type_id::float128_t):
        convert2fmt(&arg_data, fmt_store, *(long double *)(arg_data));
        break;
      case static_cast<char>(type_id::cstring_t):
      case static_cast<char>(type_id::string_t): {
        size_type data_size = *(size_type *)(arg_data);
        arg_data += sizeof(size_type);
        fmt_store.push_back((const char *)arg_data);
        arg_data += data_size;
        break;
      }
      case static_cast<char>(type_id::pointer_t): {
        convert2fmt(&arg_data, fmt_store, *(void **)(arg_data));
        break;
      }
      // id data_size data|
      case static_cast<char>(type_id::custom_type): {
        uint32_t id = *(uint32_t *)(arg_data);
        arg_data += sizeof(uint32_t);
        size_type data_size = *(size_type *)(arg_data);
        arg_data += sizeof(size_type);
        auto str = format_filed_to_json(id, (void *)(arg_data));
        fmt_store.push_back(str);
        arg_data += data_size;
        break;
      }
      default:
        break;
    }
  }
  vformat_to(output_buf_, fmt_string, fmt_store);
}

void log_handler::reset_date() {
  time_t rawtime = tscns_.rdns() / 1000000000;
  struct tm *timeinfo = localtime(&rawtime);
  timeinfo->tm_sec = timeinfo->tm_min = timeinfo->tm_hour = 0;
  midnightNs = mktime(timeinfo) * 1000000000;
  year.fromi(1900 + timeinfo->tm_year);
  month.fromi(1 + timeinfo->tm_mon);
  day.fromi(timeinfo->tm_mday);
  const char *weekdays[7] = {"Sun", "Mon", "Tue", "Wed", "Thu", "Fri", "Sat"};
  weekdayName = weekdays[timeinfo->tm_wday];
  const char *monthNames[12] = {"Jan", "Feb", "Mar", "Apr", "May", "Jun",
                                "Jul", "Aug", "Sep", "Oct", "Nov", "Dec"};
  monthName = monthNames[timeinfo->tm_mon];
}

void log_handler::handle_log(const char *name,
                             const staging_buffer::queue_header *header) {
  set_arg_val<2>(fmt::string_view(name));
  static_log_info &info = bg_log_infos_[header->logId];
  header++;
  uint64_t tsc = *(uint64_t *)(header);
  header++;
  char *arg_datas = (char *)header;
  int64_t ts = tscns_.tsc2ns(tsc);
  // the date could go back when polling different threads
  uint64_t t = (ts > midnightNs) ? (ts - midnightNs) : 0;
  nanosecond.fromi(t % 1000000000);
  t /= 1000000000;
  second.fromi(t % 60);
  t /= 60;
  minute.fromi(t % 60);
  t /= 60;
  uint32_t h = t;  // hour
  if (h > 23) {
    h %= 24;
    reset_date();
  }
  hour.fromi(h);
  static const char *log_level_names[] = {"TRACE", "DEBUG", "INFO ",
                                          "WARN ", "ERROR", "FATAL"};
  logLevel = log_level_names[info.level];
  vformat_to(output_buf_, "{} {}[{:<6}]  ",
             fmt::basic_format_args(args.data(), 3));
  decode_arg(arg_datas, info.type_array, info.fmt_string);
  output_buf_.push_back('\n');
  if (file_) {
    file_->append(output_buf_.data(), output_buf_.size());
  }
  else {
    fwrite(output_buf_.data(), 1, output_buf_.size(), stdout);
  }
  output_buf_.clear();
}

}  // namespace cpp_frame::fast_fmt_log