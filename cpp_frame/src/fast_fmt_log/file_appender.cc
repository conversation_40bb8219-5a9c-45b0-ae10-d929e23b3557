#include <cpp_frame/fast_fmt_log/file_appender.h>

#include <iostream>
namespace cpp_frame::fast_fmt_log {

file_appender::file_appender(const char *filename)
    : fp_(::fopen(filename, "ae")), written_bytes_(0) {
  std::cout << filename << std::endl;
  ::setbuffer(fp_, buffer_, sizeof(buffer_));
}
file_appender::~file_appender() { 
  flush();
  ::fclose(fp_); }

void file_appender::append(const char *logline, const size_t len) {
  size_t n = write(logline, len);
  size_t remain = len - n;
  while (remain > 0) {
    size_t x = write(logline + n, remain);
    if (x == 0) {
      int err = ferror(fp_);
      if (err) {
        fprintf(stderr, "file_appender::append() failed %d\n", err);
      }
      break;
    }
    n += x;
    remain = len - n;  // remain -= x
  }
  written_bytes_ += len;
}

void file_appender::flush() { ::fflush(fp_); }
off_t file_appender::written_bytes() const { return written_bytes_; }

size_t file_appender::write(const char *logline, size_t len) {
  return ::fwrite_unlocked(logline, 1, len, fp_);
}
}  // namespace cpp_frame::fast_fmt_log
