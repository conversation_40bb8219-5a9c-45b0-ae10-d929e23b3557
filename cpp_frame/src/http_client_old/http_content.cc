#include <cpp_frame/http_client/http_content.h>

namespace cpp_frame::http {

http_content::http_content(const http_executor_type &exec,
                           const std::string &host, const std::string &port)
    : exec_(exec),
      timer_(exec),
      connection_(std::make_shared<http_connection>(exec, host, port)),
      host_(host),
      port_(port) {}

std::pair<std::string, std::uint16_t> http_content::get_host_and_port() {
  return std::make_pair(host_, atoi(port_.c_str()));
}

void http_content::start(
    std::shared_ptr<const http_request> request,
    std::function<void(http_result_data &&, boost::system::error_code)>
        callback) {
  current_request_ = request;
  completed_request_callback_ = std::move(callback);
  is_valid_ = false;
  timer_.expires_from_now(
      boost::posix_time::millisec(request->get_timeout_msec()));
  timer_.async_wait([ptr = this->shared_from_this()](auto &&ec) {
    if (!ec) {
      ptr->complete_request(boost::asio::error::timed_out);
    }
  });
  connection_->write(
      request->get_http_method(), request->get_target(), request->get_body(),
      request->get_http_headers(),
      [ptr = this->shared_from_this()](
          const boost::beast::http::response<boost::beast::http::string_body>
              &rsp,
          boost::system::error_code ec) {
        ptr->on_http_complete(rsp, ec);
      });
}

void http_content::on_http_complete(
    const boost::beast::http::response<boost::beast::http::string_body> &rsp,
    boost::system::error_code ec) {
  result_.result_data = std::move(rsp);
  complete_request(ec);
}

void http_content::complete_request(const boost::system::error_code &ec) {
  if (completed_request_callback_ != nullptr) {
    timer_.cancel();
    result_.request = current_request_;
    completed_request_callback_(std::move(result_), ec);
  }
}
void http_content::finish() {
  result_ = {};
  completed_request_callback_ = nullptr;
  is_valid_ = true;
}

void http_content::start_async(
    std::shared_ptr<const http_request> request,
    std::function<void(http_result_data &&, boost::system::error_code)>
        callback) {
  // start(std::move(request), std::move(callback));
  async<&http_content::start>(std::move(request), std::move(callback));
}

void http_content::cancel() {
  complete_request(make_error_code(boost::asio::error::operation_aborted));
  connection_->close();
}
void http_content::cancel_async() { async<&http_content::cancel>(); }

void http_content::async_close() { async<&http_content::close>(); }
void http_content::close() { connection_->close(); }
bool http_content::is_valid() { return is_valid_; }

}  // namespace cpp_frame::http
