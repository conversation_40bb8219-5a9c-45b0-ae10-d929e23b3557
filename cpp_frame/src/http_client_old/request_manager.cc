#include <cpp_frame/fast_fmt_log/fast_fmt_log.h>
#include <cpp_frame/http_client/completion_handler_invoker.h>
#include <cpp_frame/http_client/request_manager.h>
#include <cpp_frame/utils/date.h>
#include <fmt/core.h>
#include <fmt/ranges.h>

#include <boost/asio/post.hpp>
#include <iostream>
namespace cpp_frame::http {
request_manager::request_manager(const http_client_settings &settings,
                                 const http_executor_type &exec,
                                 const std::string &host,
                                 const std::string &port)
    : settings_(settings),
      exec_(exec),
      connection_pool_(exec, host, port, settings.max_parallel_requests) {}

void request_manager::execute_request(request_data request) {
  request.request_id = request_id_++;
  requests_.insert(std::make_pair(request.request_id, std::move(request)));
  execute_waiting_requests();
}

void request_manager::execute_waiting_requests() {
  auto itr = requests_.begin();
  while (itr != requests_.end()) {
    if (itr->second.cur_request_state == request_state::waiting ||
        itr->second.cur_request_state == request_state::waiting_retry) {
      break;
    }
    itr++;
  }
  if (itr == requests_.end()) {
    return;
  }
  auto handle = connection_pool_.get_connection();
  if (handle == nullptr) {
    return;
  }
  std::cout << "handle 0" << handle.use_count() << std::endl;
  auto &request = itr->second;
  request.exec_time = cpp_frame::date::get_current_nano_sec();
  in_progress_request_cnt_++;
  request.cur_request_state = request_state::in_progress;
  FAST_LOG(DEBUG, "http req: request_id:{} target:{}\n body:{}",
           request.request_id, request.cur_http_request->get_target(),
           request.cur_http_request->get_body().c_str());
  handle->start(
      request.cur_http_request,
      [ptr = this->shared_from_this(), h = std::move(handle),
       id = request.request_id](auto &&http_result_data, auto &&ec) mutable {
          std::cout << "handle 1" << h.use_count() << std::endl;
        ptr->on_request_completed(
            id, std::forward<decltype(http_result_data)>(http_result_data),
            std::move(h), ec);
        FAST_LOG(WARN, "http handle: request_id:{} handle:{}", id,
                 h.use_count());
      });
}

void request_manager::on_request_completed(uint64_t request_id,
                                           http_result_data &&result_data,
                                           http_content_ptr &&handle,
                                           boost::system::error_code ec) {
  connection_pool_.release_connection(handle, static_cast<bool>(ec));
  if (ec) {
    handle->finish();
    auto itr = requests_.find(request_id);
    if (itr == requests_.end()) {
      return;
    }
    itr->second.cur_request_state = request_state::waiting_retry;
    FAST_LOG(WARN, "http retry: request_id:{} handle:{}", request_id,
             handle.use_count());
    in_progress_request_cnt_--;
    boost::asio::post(exec_, [this]() {
      execute_waiting_requests();
    });
  }
  else {
    http_request_result result(result_data.result_data, ec);
    auto itr = requests_.find(request_id);
    if (itr == requests_.end()) {
      return;
    }
    in_progress_request_cnt_--;
    uint64_t end_time = cpp_frame::date::get_current_nano_sec();
    FAST_LOG(DEBUG,
             "http rsp: http_status:{} request_id:{} net_lact:{}us rsp_body:{} "
             "handle:{}",
             result.response.result_int(), request_id,
             int((end_time - itr->second.exec_time) / 1000),
             result.response.body(), handle.use_count());
    completion_handler_invoker::invoke_handler(itr->second, std::move(result));
    requests_.erase(itr);
    handle->finish();
    execute_waiting_requests();
  }
}
size_t request_manager::get_free_conn_cnt() {
  return connection_pool_.get_free_conn_cnt();
}

}  // namespace cpp_frame::http
