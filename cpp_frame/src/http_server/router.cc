#include <cpp_frame/http_server/router.h>
#include <cpp_frame/utils/string_helper.h>

namespace cpp_frame::http {

route::route(const std::string &path, route_cb &&cb)
    : path_(path), cb_(std::move(cb)) {
  if (path.empty() || path[0] != '/') {
    throw std::runtime_error("Route path [" + path + "] must begin with '/'.");
  }

  for (auto &&p : string_helper::split(path, '/')) {
    segments_.emplace_back(p);
  }
}

bool route::match(request &req) const {
  // http://127.0.0.1:26210/user/user_login?user_name=ahao&pwd=111
  auto target_split = string_helper::split(
      std::string_view{req.target().data(), req.target().size()}, '?');
  //[user, user_login]
  auto request_paths = string_helper::split(target_split[0], '/');

  if (segments_.size() != request_paths.size()) {
    return false;
  }
  // user_name=ahao&pwd=111
  std::string attrs =
      (target_split.size() > 1 ? std::string(target_split[1]) : "");

  // /:dirname/:filename
  //[:dirname :filename]
  for (std::size_t i = 0; i < segments_.size(); ++i) {
    auto &segment = segments_[i];
    auto &request_segment = request_paths[i];

    if (segment[0] == ':') {
      attrs += (attrs.empty() ? "" : "&") +
               std::string(&segment[1], segment.size() - 1) + "=" +
               std::string(request_segment);
    }
    else if (segment != request_segment) {
      return false;
    }
  }

  if (!attrs.empty()) {
    req.get_attributes() = attributes(attrs);
  }

  return true;
}

}  // namespace cpp_frame::http