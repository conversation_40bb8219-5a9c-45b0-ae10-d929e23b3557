#include <http_server/http_acceptor.h>
#include <cpp_frame/http_server/http_server.h>
#include <http_server/http_session.h>

#include <functional>

namespace cpp_frame::http {

http_server::http_server(boost::asio::io_context &ioc) : ioc_(ioc) {
  init_acceptor();
}

http_server::~http_server() {}

int http_server::register_addr(std::string_view ip, unsigned short port) {
  auto const address = boost::asio::ip::make_address(ip);
  boost::asio::ip::tcp::endpoint endpoint(address, port);
  return acceptor_->run(endpoint);
}

void http_server::init_acceptor() {
  acceptor_ = new http_acceptor(ioc_);
  acceptor_->set_open_callback(
      std::bind(&http_server::on_socket_created, this, std::placeholders::_1,
                std::placeholders::_2, std::placeholders::_3));
}

void http_server::on_socket_created(boost::asio::ip::tcp::socket *socket,
                                    boost::asio::ip::tcp::endpoint &endpoint,
                                    beast::error_code ec) {
  if (ec) {
    std::cerr << "on_socket_created: " << ec.message() << "\n";
    return;
  }
  std::make_shared<http_session>(ioc_, std::move(*socket), router_)->run();
}

http_server &http_server::get(const std::string &path, route_cb &&cb) {
  router_.add_route(beast::http::verb::get, route(path, std::move(cb)));
  return *this;
}

http_server &http_server::put(const std::string &path, route_cb &&cb) {
  router_.add_route(beast::http::verb::put, route(path, std::move(cb)));
  return *this;
}

http_server &http_server::post(const std::string &path, route_cb &&cb) {
  router_.add_route(beast::http::verb::post, route(path, std::move(cb)));
  return *this;
}

http_server &http_server::del(const std::string &path, route_cb &&cb) {
  router_.add_route(beast::http::verb::delete_, route(path, std::move(cb)));
  return *this;
}

}  // namespace cpp_frame::http