
#include <cpp_frame/http_server/router.h>

#include <boost/asio/strand.hpp>
#include <boost/beast/core.hpp>
#include <boost/beast/http.hpp>
#include <iostream>

namespace cpp_frame::http {

class http_session : public std::enable_shared_from_this<http_session> {
 public:
  http_session(boost::asio::io_context &ioc,
               boost::asio::ip::tcp::socket &&socket, const router &router)
      : stream_(std::move(socket)), strand_(ioc), router_(router) {}

  void run() {
    // boost::asio::dispatch(stream_.get_executor(),
    // beast::bind_front_handler(&http_session::do_read, shared_from_this()));
    do_read();
  }

 private:
  void do_read() {
    std::cout << "http_session: do read" << std::endl;
    request_parser_ = std::make_unique<
        boost::beast::http::request_parser<boost::beast::http::string_body>>();
    boost::beast::http::async_read(
        stream_, buffer_, *request_parser_,
        boost::asio::bind_executor(
            strand_,
            [me = this->shared_from_this()](auto ec, auto bytes_transferred) {
              me->on_read(ec, bytes_transferred);
            }));
  }

  void on_read(boost::system::error_code ec,
               std::size_t /* bytes_transferred */) {
    std::cout << "http_session: on read ec:" << ec.message() << std::endl;
    if (ec == beast::http::error::end_of_stream) {
      return do_close();
    }

    if (ec) {
      std::cout << "fail" << std::endl;
      return;
      // return fail(ec, "read");
    }

    auto response = handle_request();
    if (response) {
      if (!response->is_postponed())
        do_write(response);
      else {
        response->on_done([me = this->shared_from_this(), response] {
          me->do_write(response);
        });
      }
    }
  }

  void do_write(const std::shared_ptr<response> &cur_res) {
    std::cout << "http_session: do write  <<<<<<" << cur_res->body()
              << std::endl;
    cur_res->prepare_payload();
    beast::http::async_write(
        stream_, *cur_res,
        boost::asio::bind_executor(
            strand_, [me = this->shared_from_this(), cur_res](
                         auto ec, auto bytes_transferred) {
              me->on_write(cur_res->keep_alive(), ec, bytes_transferred);
            }));
  }

  void on_write(bool keep_alive, boost::system::error_code ec,
                std::size_t len) {
    std::cout << "on write" << std::endl;
    if (ec) {
      std::cout << "ec" << std::endl;
    }

    if (!keep_alive) {
      return do_close();
    }
    do_read();
  }

  void do_close() {
    boost::system::error_code ec;
    stream_.socket().shutdown(boost::asio::ip::tcp::socket::shutdown_send, ec);
  }

 private:
  std::shared_ptr<response> bad_request(const request &req,
                                        const char *message) {
    auto res =
        std::make_shared<response>(http::status::bad_request, req.version());
    res->set(http::field::server, "v0.1");
    res->set(text_plain);
    res->keep_alive(req.keep_alive());
    res->body() = std::string(message);
    return res;
  }

  std::shared_ptr<response> not_found(const request &req) {
    auto res =
        std::make_shared<response>(http::status::not_found, req.version());
    res->set(http::field::server, "v0.1");
    res->set(text_plain);
    res->keep_alive(req.keep_alive());
    res->body() = "The resource [" + std::string(req.method_string()) + "] '" +
                  std::string(req.target()) + "' was not found.";
    return res;
  }

  std::shared_ptr<response> server_error(const request &req,
                                         const char *message) {
    auto res = std::make_shared<response>(http::status::internal_server_error,
                                          req.version());
    res->set(http::field::server, "v0.1");
    res->set(text_plain);
    res->keep_alive(req.keep_alive());
    res->body() = std::string("An error occurred: '") + message + "'";
    return res;
  }
  std::shared_ptr<response> handle_request() {
    // Make sure we can handle the method
    request_ = request_parser_->release();
    request_.remote(stream_.socket().remote_endpoint());
    std::cout << "method: " << request_.method_string()
              << ", target: " << request_.target() << std::endl;
    auto found_method = router_.find(request_.method());
    if (found_method == router_.end()) {
      return bad_request(request_, "Not supported HTTP-method");
    }
    for (auto &cur_route : found_method->second) {
      if (cur_route.match(request_)) {
        try {
          auto res = std::make_shared<response>(boost::beast::http::status::ok,
                                                request_.version());
          res->set(boost::beast::http::field::server, "v0.1");
          res->keep_alive(request_.keep_alive());
          cur_route.execute(request_, *res);  // Call the route user handler
          return res;
        } catch (const std::exception &ex) {
          return server_error(request_, ex.what());
        }
      }
    }
    return not_found(request_);
  }

 private:
  boost::beast::tcp_stream stream_;
  boost::asio::io_context::strand strand_;
  boost::beast::flat_buffer buffer_;
  request request_;
  const router &router_;

  std::unique_ptr<beast::http::request_parser<beast::http::string_body>>
      request_parser_;
};
}  // namespace cpp_frame::http