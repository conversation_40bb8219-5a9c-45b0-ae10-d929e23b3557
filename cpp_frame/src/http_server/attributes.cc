#include <cpp_frame/http_server/attributes.h>
#include <cpp_frame/utils/string_helper.h>

#include <regex>

namespace cpp_frame::http {

attribute::attribute(std::string str) : _value(std::move(str)) {}

std::string attribute::as_string(const char *default_value) const {
  return as<std::string>(default_value);
}

int attribute::as_integer(int default_value) const {
  return as<int>(default_value);
}

double attribute::as_double(double default_value) const {
  return as<double>(default_value);
}

bool attribute::as_boolean(bool default_value) const {
  return as<bool>(default_value);
}

bool attribute::operator==(const char *other) const { return _value == other; }
bool attribute::operator==(const std::string &other) const {
  return _value == other;
}

static inline unsigned char hexdigit_to_num(unsigned char c) {
  return (c < 'A' ? c - '0' : toupper(c) - 'A' + 10);
}

static std::string unescape(const std::string &s) {
  static std::regex escaped("%([0-9A-Fa-f]{2})");
  // Support urlencoded '+' --> ' '
  std::string t = std::regex_replace(s, std::regex("\\+"), " ");
  std::string unescaped;
  auto cur = std::sregex_token_iterator(t.begin(), t.end(), escaped);
  auto end = std::sregex_token_iterator();
  auto vbegin = t.cbegin();

  for (; cur != end; ++cur) {
    auto it = cur->first;
    // Append unescaped chars
    unescaped.append(vbegin, it);
    // Skip percent
    ++it;
    auto c = hexdigit_to_num(*it++) << 4;
    c |= hexdigit_to_num(*it++);
    unescaped += c;
    vbegin = cur->second;
  }

  unescaped.append(vbegin, t.cend());
  return unescaped;
}

attributes::attributes(const std::string &str, char sep) {
  for (auto &&a : string_helper::split(str, sep)) {
    auto kv = string_helper::split(a, '=');
    if (kv.size() == 2) {
      insert(std::string(kv[0]), std::string(kv[1]));
    }
  }
}

void attributes::insert(std::string key, std::string value) {
  attributes_.emplace(std::move(key), unescape(value));
}

const attribute &attributes::operator[](const std::string &key) const {
  auto found_key = attributes_.find(key);
  if (found_key != attributes_.end()) {
    return found_key->second;
  }
  return EMPTY;
}
}  // namespace cpp_frame::http