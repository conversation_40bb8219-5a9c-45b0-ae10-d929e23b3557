#include <cpp_frame/websocket/ws_server.h>
#include <websocket/ws_acceptor.h>
#include <websocket/ws_server_session.h>

namespace cpp_frame::ws {

ws_server::ws_server(boost::asio::io_context &ioc) : ioc_(ioc) {
  init_acceptor();
}
ws_server::~ws_server() {}
int ws_server::register_addr(std::string_view ip, unsigned short port) {
  auto const address = boost::asio::ip::make_address(ip);
  boost::asio::ip::tcp::endpoint endpoint(address, port);
  return acceptor_->run(endpoint);
}

void ws_server::init_acceptor() {
  acceptor_ = new ws_acceptor(ioc_);
  acceptor_->set_open_callback(
      std::bind(&ws_server::on_socket_created, this, std::placeholders::_1,
                std::placeholders::_2, std::placeholders::_3));
}

void ws_server::on_socket_created(boost::asio::ip::tcp::socket *socket,
                                  boost::asio::ip::tcp::endpoint &endpoint,
                                  boost::beast::error_code ec) {
  if (ec) {
    std::cerr << "on_socket_created: " << ec.message() << "\n";
    return;
  }
  ws_server_session *session =
      new ws_server_session(std::move(*socket), endpoint);
  session->set_open_callback(std::bind(
      &ws_server::on_open, this, std::placeholders::_1, std::placeholders::_2));
  session->set_close_callback(std::bind(&ws_server::on_close, this,
                                        std::placeholders::_1,
                                        std::placeholders::_2));
  session->set_read_callback(
      std::bind(&ws_server::on_read, this, std::placeholders::_1,
                std::placeholders::_2, std::placeholders::_3));
  map_session_[session->session_id()] = session;
  session->run();
}

void ws_server::on_open(uint32_t sessionid,
                        boost::asio::ip::tcp::endpoint &endpoint) {
  open_callback_(sessionid);
}

void ws_server::on_close(uint32_t sessionid,
                         boost::asio::ip::tcp::endpoint &endpoint) {
  SesstionMap::iterator itr = map_session_.find(sessionid);
  if (itr == map_session_.end()) {
    return;
  }
  close_callback_(sessionid);
  delete itr->second;
  map_session_.erase(itr);
}

void ws_server::on_read(uint32_t sessionid, std::string_view msg,
                        bool is_text) {
  read_callback_(sessionid, msg, is_text);
}

int ws_server::send(uint32_t sessionid, std::string &msg) {
  SesstionMap::iterator itr = map_session_.find(sessionid);
  if (itr == map_session_.end()) {
    return -1;
    ;
  }
  itr->second->send(std::move(msg));
  return 0;
}
int ws_server::send(uint32_t sessionid, std::string &&msg) {
  SesstionMap::iterator itr = map_session_.find(sessionid);
  if (itr == map_session_.end()) {
    return -1;
    ;
  }
  itr->second->send(std::move(msg));
  return 0;
}
}  // namespace cpp_frame::ws
