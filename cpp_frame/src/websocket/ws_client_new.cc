#include <cpp_frame/fast_fmt_log/fast_fmt_log.h>
#include <cpp_frame/fast_fmt_log/level.h>
#include <cpp_frame/websocket/ws_client_new.h>

#include <boost/asio/error.hpp>

namespace cpp_frame::ws {

ws_client_new::ws_client_new(boost::asio::any_io_executor &exec)
    : exec_(new_strand(to_io_executor(exec))),
      ctx_(boost::asio::ssl::context::tlsv12_client),
      resolver_(exec),
      connect_timer_(exec) {}

ws_client_new::~ws_client_new() {
  if (ws_stream_)
    async_close();
}

void ws_client_new::async_connect() {
  std::cout << "async_connect" << std::endl;

  boost::asio::post([ptr = shared_from_this()] {
    std::cout << "post force_close" << std::endl;
    ptr->force_close();
  });

  boost::asio::post([ptr = shared_from_this()] {
    std::cout << "post reset" << std::endl;
  });
  connect_timer_.init(1 * 1000, [ptr = shared_from_this()]() {
    std::cout << "post async_connect_inner" << std::endl;
    ptr->reset();
    ptr->write_msgs_.clear();
    ptr->async_connect_inner();
  });
  connect_timer_.start();
  // boost::asio::post([ptr = shared_from_this()] {
  //   std::cout << "post async_connect_inner" << std::endl;
  //   ptr->write_msgs_.clear();
  //   ptr->async_connect_inner();
  // });
}

void ws_client_new::force_close() {
  std::cout << "force_close" << std::endl;
  if (ws_stream_) {
    std::cout << "do force close" << std::endl;
    boost::beast::get_lowest_layer(*ws_stream_).cancel();
    // Forcefully close the connection
    // boost::beast::get_lowest_layer(*ws_stream_)
    //     .socket()
    //     .shutdown(boost::asio::ip::tcp::socket::shutdown_both);
    // boost::beast::get_lowest_layer(*ws_stream_).socket().close();
  }
}

void ws_client_new::async_close() {
  if (ws_stream_ && ws_stream_->is_open()) {
    boost::asio::post([ptr = shared_from_this()] {
      std::cout << "do close" << std::endl;
      FAST_LOG(INFO, "ws_client_new::{}, do close", __FUNCTION__);
      ptr->ws_stream_->async_close(
          boost::beast::websocket::normal,
          std::bind(&ws_client_new::on_close, ptr, std::placeholders::_1));
    });
  }
}

void ws_client_new::send(std::string msg) {
  boost::asio::post([ptr = shared_from_this(), msg] {
    bool write_in_progress = !ptr->write_msgs_.empty();
    ptr->write_msgs_.push_back(msg);
    if (!write_in_progress) {
      ptr->do_write();
    }
  });
}

/////////////////////////////////////////////////////////////////////////////////////

void ws_client_new::async_connect_inner() {
  std::cout << "async_resolve" << std::endl;
  resolver_.async_resolve(host_, port_,
                          boost::beast::bind_front_handler(
                              &ws_client_new::on_resolve, shared_from_this()));
}
void ws_client_new::on_resolve(
    boost::beast::error_code ec,
    boost::asio::ip::tcp::resolver::results_type results) {
  std::cout << "on_resolve " << ec << " ec_msg:" << ec.message() << std::endl;
  if (ec) {
    FAST_LOG(WARN, "ws_client_new::{}, connect ec:[{}] ec_msg:[{}]",
             __FUNCTION__, ec.value(), ec.message());
    (connect_cb_)(ec);
    return;
  }
  // boost::beast::get_lowest_layer(*ws_stream_)
  //     .expires_after(std::chrono::seconds(30));
  boost::beast::get_lowest_layer(*ws_stream_)
      .async_connect(
          results, boost::beast::bind_front_handler(&ws_client_new::on_connect,
                                                    shared_from_this()));
}
void ws_client_new::do_write() {
  std::cout << "do write:"<<write_msgs_.size() << std::endl;
  ws_stream_->async_write(
      boost::asio::buffer(write_msgs_.front()),
      [ptr = shared_from_this()](boost::beast::error_code ec,
                                 std::size_t /*length*/) {
        if (!ec) {
          ptr->send_cb_(ec);
          if (ptr->write_msgs_.size()) {
            FAST_LOG(INFO, "ws_client_new::{}, write async_write:[{}]",
                     __FUNCTION__, ptr->write_msgs_.front());
              ptr->write_msgs_.pop_front();
          }
          if (!ptr->write_msgs_.empty()) {
            ptr->do_write();
          }
        }
        else {
          ptr->send_cb_(ec);
          FAST_LOG(WARN,
                   "ws_client_new::{}, on_message_sent error:[{}] ec_msg:[{}]",
                   __FUNCTION__, ec.value(), ec.message());
          ptr->write_msgs_.clear();
        }
      });
}
// void ws_client_new::close()
// {
//     boost::system::error_code ec;
//     ws_stream_->close(boost::beast::websocket::normal, ec);
//     ws_stream_->next_layer().shutdown(ec);
//     ws_stream_->next_layer().next_layer().close();
// }

void ws_client_new::on_connect(
    boost::beast::error_code ec,
    boost::asio::ip::tcp::resolver::results_type::endpoint_type ep) {
  std::cout << "on_connect" << ec << " ec_msg:" << ec.message() << std::endl;
  FAST_LOG(INFO, "ws_client_new::{}, ssl ec:[{}] ec_msg:[{}]", __FUNCTION__,
           ec.value(), ec.message());
  boost::ignore_unused(ep);
  if (ec) {
    FAST_LOG(WARN, "ws_client_new::{}, on_connect ec:[{}] ec_msg:[{}]",
             __FUNCTION__, ec.value(), ec.message());
    (connect_cb_)(ec);
    return;
  }
  ws_stream_->next_layer().set_verify_mode(boost::asio::ssl::verify_none);
  boost::beast::get_lowest_layer(*ws_stream_)
      .expires_after(std::chrono::seconds(30));
  if (!::SSL_set_tlsext_host_name(ws_stream_->next_layer().native_handle(),
                                  host_.c_str())) {
    ec = boost::system::error_code{static_cast<int>(::ERR_get_error()),
                                   boost::asio::error::get_ssl_category()};
    FAST_LOG(WARN, "ws_client_new::{}, ssl ec:[{}] ec_msg:[{}]", __FUNCTION__,
             ec.value(), ec.message());
    (connect_cb_)(ec);
    return;
  }
  ws_stream_->next_layer().async_handshake(
      boost::asio::ssl::stream_base::client,
      std::bind(&ws_client_new::on_ssl_handshake, shared_from_this(),
                std::placeholders::_1));
}
void ws_client_new::on_ssl_handshake(const boost::system::error_code &ec) {
  std::cout << "on_ssl_handshake " << ec << " ec_msg:" << ec.message()
            << std::endl;
  FAST_LOG(INFO, "ws_client_new::{}, on_ssl_handshake ec:[{}] ec_msg:[{}]",
           __FUNCTION__, ec.value(), ec.message());
  if (ec) {
    FAST_LOG(WARN, "ws_client_new::{}, on_ssl_handshake ec:[{}] ec_msg:[{}]",
             __FUNCTION__, ec.value(), ec.message());
    (connect_cb_)(ec);
    return;
  }
  ws_stream_->set_option(boost::beast::websocket::stream_base::decorator(
      [](boost::beast::websocket::request_type &req) {
        req.set(boost::beast::http::field::user_agent, "0.1");
      }));
  std::string cur_host = host_;
  cur_host += ':' + port_;
  ws_stream_->async_handshake(
      cur_host, endpoint_,
      std::bind(&ws_client_new::on_ws_handshake, shared_from_this(),
                std::placeholders::_1));
}
void ws_client_new::on_ws_handshake(const boost::system::error_code &ec) {
  std::cout << "on_ws_handshake " << ec << " ec_msg:" << ec.message()
            << std::endl;
  if (ec) {
    FAST_LOG(
        WARN,
        "ws_client_new::{}, on_ssl_handshake on_ws_handshake:[{}] ec_msg:[{}]",
        __FUNCTION__, ec.value(), ec.message());
    (connect_cb_)(ec);
    return;
  }
  boost::beast::get_lowest_layer(*ws_stream_).expires_never();
  ws_stream_->control_callback(
      std::bind(&ws_client_new::on_control_frame, shared_from_this(),
                std::placeholders::_1, std::placeholders::_2));
  (connect_cb_)(ec);
}
void ws_client_new::on_control_frame(boost::beast::websocket::frame_type frame,
                                     boost::string_view sv) {
  boost::ignore_unused(sv);
  if (frame == boost::beast::websocket::frame_type::ping) {
    ws_stream_->async_pong("", [](boost::system::error_code const &ec) {
      boost::ignore_unused(ec);
    });
  }
}

// void ws_client_new::on_message_sent(const boost::system::error_code &ec,
// size_t n) {
//   boost::ignore_unused(n);
//   if (ec) {
//     FAST_LOG(WARN,
//              "ws_client_new::{}, on_message_sent on_ws_handshake:[{}]
//              ec_msg:[{}]",
//              __FUNCTION__, ec.value(), ec.message());
//     return;
//   }
// }
void ws_client_new::on_close(const boost::system::error_code &ec) {
  std::cout << "on_close ec_msg:" << ec.message() << std::endl;

  (close_cb_)(ec);
  FAST_LOG(WARN, "ws_client_new::{}, ws_closed:[{}] ec_msg:[{}]", __FUNCTION__,
           ec.value(), ec.message());
}

void ws_client_new::reset() {
  std::cout << "do reset" << std::endl;
  ws_stream_.emplace(exec_, ctx_);
}

void ws_client_new::async_read() {
  buffer_.clear();
  ws_stream_->async_read(
      buffer_,
      [ptr = shared_from_this()](boost::system::error_code ec, std::size_t n) {
        if (!ec) {
          std::string_view buf(
              static_cast<const char *>(ptr->buffer_.data().data()),
              ptr->buffer_.size());
          FAST_LOG(DEBUG, "ws_client_new::{}, ws:{}", __FUNCTION__, buf);
          (ptr->read_cb_)(buf, ec);
        }
        else {
          if (ec == boost::asio::error::eof && n > 0) {
            std::string_view buf(
                static_cast<const char *>(ptr->buffer_.data().data()),
                ptr->buffer_.size());
            FAST_LOG(DEBUG, "ws_client_new::{}, ws:{}", __FUNCTION__, buf);
            (ptr->read_cb_)(buf, ec);
          }
          else {
            std::string_view buf;
            (ptr->read_cb_)(buf, ec);
            FAST_LOG(
                WARN,
                "ws_client_new::{}, async_read read error:[{}] ec_msg: [ {} ] ",
                __FUNCTION__, ec.value(), ec.message());
          }
        }
      });
}
}  // namespace cpp_frame::ws