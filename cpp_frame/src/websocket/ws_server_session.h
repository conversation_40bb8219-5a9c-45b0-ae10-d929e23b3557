#include <algorithm>
#include <boost/asio/dispatch.hpp>
#include <boost/asio/strand.hpp>
#include <boost/beast/core.hpp>
#include <boost/beast/websocket.hpp>
#include <cstdlib>
#include <functional>
#include <iostream>
#include <memory>
#include <queue>
#include <string>
#include <thread>
#include <vector>

namespace cpp_frame::ws {

class ws_server_session {
 public:
  using SESSION_CALLBACK = std::function<void(
      uint32_t sessionid, boost::asio::ip::tcp::endpoint &endpoint)>;
  using READ_CALLBACK =
      std::function<void(uint32_t sessionid, std::string_view, bool is_text)>;
  explicit ws_server_session(boost::asio::ip::tcp::socket &&socket,
                             boost::asio::ip::tcp::endpoint &endpoint)
      : ws_(std::move(socket)), endpoint_(endpoint) {
    static uint32_t sm_session_id = 0;
    session_id_ = (time(NULL) << 16) | (++sm_session_id);
  }
  uint32_t session_id() { return session_id_; }
  void set_open_callback(SESSION_CALLBACK cb) { open_callback_ = cb; }
  void set_close_callback(SESSION_CALLBACK cb) { close_callback_ = cb; }
  void set_read_callback(READ_CALLBACK cb) { read_callback_ = cb; }
  void run() {
    boost::beast::net::dispatch(
        ws_.get_executor(),
        boost::beast::bind_front_handler(&ws_server_session::on_run, this));
  }

  void send(std::string &&msg) {
    boost::beast::net::post(ws_.get_executor(),
                            [this, str = std::move(msg)]() mutable {
                              do_write(std::move(str));
                            });
  }

 private:
  void on_run() {
    ws_.set_option(boost::beast::websocket::stream_base::timeout::suggested(
        boost::beast::role_type::server));
    ws_.set_option(boost::beast::websocket::stream_base::decorator(
        [](boost::beast::websocket::response_type &res) {
          res.set(boost::beast::http::field::server,
                  std::string(BOOST_BEAST_VERSION_STRING) +
                      " boost::beast::websocket-server-async");
        }));
    ws_.async_accept(
        boost::beast::bind_front_handler(&ws_server_session::on_accept, this));
  }

  void on_accept(boost::beast::error_code ec) {
    if (ec) {
      return fail(ec, "accept");
    }
    else {
      open_callback_(session_id_, endpoint_);
    }
    do_read();
  }

  void do_read() {
    buffer_.clear();
    ws_.async_read(buffer_, boost::beast::bind_front_handler(
                                &ws_server_session::on_read, this));
  }

  void on_read(boost::beast::error_code ec, std::size_t bytes_transferred) {
    if (ec == boost::beast::websocket::error::closed ||
        ec == boost::system::errc::connection_reset) {
      close_callback_(session_id_, endpoint_);
      return;
    }
    if (ec) {
      close_callback_(session_id_, endpoint_);
      return fail(ec, "read");
    }
    std::string_view buf(static_cast<const char *>(buffer_.data().data()),
                         buffer_.size());
    read_callback_(session_id_, buf, ws_.got_text());
    do_read();
  }

  void do_write(std::string &&str) {
    queue_.push_back(std::move(str));

    if (queue_.size() > 1) {
      return;
    }
    ws_.async_write(
        boost::beast::net::buffer(queue_.front()),
        boost::beast::bind_front_handler(&ws_server_session::on_write, this));
  }

  void on_write(boost::beast::error_code ec, std::size_t) {
    if (ec) {
      return fail(ec, "write");
    }
    queue_.pop_front();
    if (!queue_.empty()) {
      ws_.async_write(
          boost::beast::net::buffer(queue_.front()),
          boost::beast::bind_front_handler(&ws_server_session::on_write, this));
    }
  }
  void fail(boost::system::error_code ec, const char *what) {
    std::cerr << what << ": " << ec.message() << " " << ec << "\n";
  }

 private:
  boost::beast::websocket::stream<boost::beast::tcp_stream> ws_;
  boost::asio::ip::tcp::endpoint endpoint_;
  boost::beast::flat_buffer buffer_;
  SESSION_CALLBACK open_callback_;
  SESSION_CALLBACK close_callback_;
  READ_CALLBACK read_callback_;
  uint32_t session_id_;
  std::deque<std::string> queue_;
};
}  // namespace cpp_frame::ws