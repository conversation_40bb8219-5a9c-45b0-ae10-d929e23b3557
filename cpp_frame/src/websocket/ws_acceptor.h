#include <algorithm>
#include <boost/asio/dispatch.hpp>
#include <boost/asio/strand.hpp>
#include <boost/beast/core.hpp>
#include <cstdlib>
#include <functional>
#include <iostream>
#include <memory>
#include <string>
#include <thread>
#include <vector>

namespace cpp_frame::ws {

class ws_acceptor {
 public:
  using ACCEPTOR_CALLBACK = std::function<void(boost::asio::ip::tcp::socket *,
                                               boost::asio::ip::tcp::endpoint &,
                                               boost::beast::error_code)>;
  ws_acceptor(boost::asio::io_context &ioc) : ioc_(ioc), acceptor_(ioc) {}

  void set_open_callback(ACCEPTOR_CALLBACK cb) { callback_ = cb; }

  int run(boost::asio::ip::tcp::endpoint endpoint) {
    endpoint_ = endpoint;
    boost::beast::error_code ec;
    acceptor_.open(endpoint.protocol(), ec);
    if (ec) {
      callback_(nullptr, endpoint, ec);
      return -1;
    }
    acceptor_.set_option(boost::asio::socket_base::reuse_address(true), ec);
    if (ec) {
      callback_(nullptr, endpoint, ec);
      return -1;
    }
    acceptor_.bind(endpoint, ec);
    if (ec) {
      callback_(nullptr, endpoint, ec);
      return -1;
    }
    acceptor_.listen(boost::asio::socket_base::max_listen_connections, ec);
    if (ec) {
      callback_(nullptr, endpoint, ec);
      return -1;
    }
    do_accept();
    return 0;
  }

 private:
  void do_accept() {
    acceptor_.async_accept(
        boost::asio::make_strand(ioc_),
        boost::beast::bind_front_handler(&ws_acceptor::on_accept, this));
  }

  void on_accept(boost::beast::error_code ec,
                 boost::asio::ip::tcp::socket socket) {
    if (ec) {
      callback_(nullptr, endpoint_, ec);
    }
    else {
      callback_(&socket, endpoint_, ec);
    }
    do_accept();
  }

 private:
  boost::asio::io_context &ioc_;
  boost::asio::ip::tcp::endpoint endpoint_;
  boost::asio::ip::tcp::acceptor acceptor_;
  ACCEPTOR_CALLBACK callback_;
};
}  // namespace cpp_frame::ws
