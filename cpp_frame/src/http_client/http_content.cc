#include <cpp_frame/http_client/http_content.h>

#include <cstdint>
#include <memory>

namespace cpp_frame::http {

http_content::http_content(const http_executor_type &exec,
                           const std::string &host, const std::string &port,
                           boost::asio::ssl::context_base::method ssl_method,
                           uint64_t id)
    : exec_(exec),
      timer_(exec),
      connection_(
          std::make_unique<http_connection>(exec, host, port, ssl_method)),
      host_(host),
      port_(port),
      id_(id) {}
http_content::~http_content() {
  std::cout << "end http_content id" << id_ << std::endl;
}

std::pair<std::string, std::uint16_t> http_content::get_host_and_port() {
  return std::make_pair(host_, atoi(port_.c_str()));
}

void http_content::start(
    const http_request &request,
    std::function<void(const http_result_data &, boost::system::error_code)>
        callback) {
  completed_request_callback_ = callback;
  // timer_.expires_from_now(
  //     boost::posix_time::millisec(request.get_timeout_msec()));
  // timer_.async_wait([this](auto &&ec) {
  //   if (!ec) {
  //     complete_request(boost::asio::error::timed_out);
  //   }
  // });
  connection_->write(
      request.get_http_method(), request.get_target(), request.get_body(),
      request.get_http_headers(),
      [this](const boost::beast::http::response<boost::beast::http::string_body>
                 &rsp,
             boost::system::error_code ec) {
        on_http_complete(rsp, ec);
      });
}

void http_content::on_http_complete(
    const boost::beast::http::response<boost::beast::http::string_body> &rsp,
    boost::system::error_code ec) {
  result_.result_data = rsp;
  complete_request(ec);
}

void http_content::complete_request(const boost::system::error_code &ec) {
  if (completed_request_callback_ != nullptr) {
    timer_.cancel();
    completed_request_callback_(result_, ec);
  }
}
void http_content::finish() {
  result_ = {};
  completed_request_callback_ = nullptr;
}

void http_content::cancel() {
  complete_request(make_error_code(boost::asio::error::operation_aborted));
  connection_->close();
}

void http_content::close() { connection_->close(); }
uint64_t http_content::get_id() { return id_; }

}  // namespace cpp_frame::http
