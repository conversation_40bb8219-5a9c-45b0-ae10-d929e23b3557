#include <cpp_frame/fast_fmt_log/fast_fmt_log.h>
#include <cpp_frame/http_client/request_manager.h>
#include <cpp_frame/utils/date.h>
#include <fmt/core.h>
#include <fmt/ranges.h>

#include <boost/asio/post.hpp>
#include <cstdint>
#include <iostream>
namespace cpp_frame::http {
request_manager::request_manager(
    const http_client_settings &settings, const http_executor_type &exec,
    const std::string &host, const std::string &port,
    boost::asio::ssl::context_base::method ssl_method)
    : settings_(settings),
      exec_(exec),
      connection_pool_(exec, host, port, ssl_method,
                       settings.max_parallel_requests) {}

void request_manager::execute_request(request_data request) {
  request.request_id = request_id_++;
  requests_.insert({request.request_id, request});
  execute_waiting_requests();
}

void request_manager::execute_waiting_requests() {
  auto itr = requests_.begin();
  while (itr != requests_.end()) {
    if (itr->second.cur_request_state == request_state::waiting ||
        itr->second.cur_request_state == request_state::waiting_retry) {
      break;
    }
    itr++;
  }
  if (itr == requests_.end()) {
    return;
  }
  auto handle = connection_pool_.get_connection();
  if (handle == nullptr) {
    return;
  }
  auto &request = itr->second;
  request.exec_time = cpp_frame::date::get_current_nano_sec();
  in_progress_request_cnt_++;
  request.cur_request_state = request_state::in_progress;
  FAST_LOG(
      INFO,
      "http req: client_name:{} request_id:{} client_id:{} target:{}\n body:{}",
      settings_.client_name, request.request_id, handle->get_id(),
      request.cur_http_request.get_target(),
      request.cur_http_request.get_body().c_str());
  handle->start(request.cur_http_request,
                [this, handle, id = request.request_id](
                    const http_result_data &res, auto &&ec) mutable {
                  on_request_completed(id, res, handle, ec);
                });
}

void request_manager::on_request_completed(uint64_t request_id,
                                           const http_result_data &result_data,
                                           http_content *handle,
                                           boost::system::error_code ec) {
  uint64_t id = handle->get_id();

  if (ec) {
    auto itr = requests_.find(request_id);
    if (itr == requests_.end()) {
      return;
    }
    itr->second.cur_request_state = request_state::waiting_retry;
    FAST_LOG(INFO, "need_retry client_name:{} request_id:{} client_id:{}",
             settings_.client_name, request_id, id);
    boost::asio::post(exec_, [this, handle, ec]() {
      connection_pool_.release_connection(handle, static_cast<bool>(ec));
    });
    boost::asio::post(exec_, [this]() {
      execute_waiting_requests();
    });
  }
  else {
    http_request_result result(result_data.result_data, ec);
    auto itr = requests_.find(request_id);
    if (itr == requests_.end()) {
      return;
    }
    uint64_t end_time = cpp_frame::date::get_current_nano_sec();
    FAST_LOG(
        INFO,
        "http rsp: client_name:{} http_status:{} request_id:{} client_id:{} "
        "net_lact:{}us rsp_body:{} ",
        settings_.client_name, result.response.result_int(), request_id,
        handle->get_id(), int((end_time - itr->second.exec_time) / 1000),
        result.response.body());
    itr->second.handler(result);
    requests_.erase(itr);
    handle->finish();
    boost::asio::post(exec_, [this, handle, ec]() {
      connection_pool_.release_connection(handle, static_cast<bool>(ec));
    });
    boost::asio::post(exec_, [this]() {
      execute_waiting_requests();
    });
  }
}
size_t request_manager::get_free_conn_cnt() {
  return connection_pool_.get_free_conn_cnt();
}

}  // namespace cpp_frame::http
