#include <cpp_frame/http_client/http_connection_pool.h>

#include <cstddef>
#include <memory>

#include "cpp_frame/http_client/http_content.h"
namespace cpp_frame::http {
http_connection_pool::~http_connection_pool() {
  std::cout << "desc http_connection_pool" << std::endl;
  // while (!valid_pool_.empty()) {
  //   http_content *client = valid_pool_.front();
  //   if (client) {
  //     delete client;
  //   }
  //   valid_pool_.pop_front();
  // }
  // while (!running_pool_.empty()) {
  //   http_content *client = running_pool_.front();
  //   if (client) {
  //     delete client;
  //   }
  //   running_pool_.pop_front();
  // }
}
http_content* http_connection_pool::get_connection() {
  FAST_LOG(DEBUG, "valid_pool_size:{} running_pool_size:{}", valid_pool_.size(),
           running_pool_.size());
  size_t all_connect = valid_pool_.size() + running_pool_.size();
  if (all_connect >= max_connect_cnt_ && valid_pool_.empty()) {
    return nullptr;
  }
  http_content* client{nullptr};
  if (valid_pool_.empty()) {
    client_id_++;
    auto uniq_client = std::make_unique<http_content>(exec_, host_, port_,
                                                      ssl_method_, client_id_);
    client = uniq_client.get();
    running_pool_.emplace_back(std::move(uniq_client));
    FAST_LOG(DEBUG, "create_client id:{} ssl_method:{}", client->get_id(),
             (int)ssl_method_);
  }
  else {
    auto& valid_client = valid_pool_.front();
    client = valid_client.get();
    running_pool_.emplace_back(std::move(valid_client));
    valid_pool_.pop_front();
    FAST_LOG(DEBUG, "get_client id:{}", client->get_id());
  }
  return client;
}
void http_connection_pool::release_connection(http_content* handle,
                                              bool clean_up) {
  try {
    if (clean_up) {
      size_t b_runing_size = running_pool_.size();
      size_t b_valid_size = valid_pool_.size();
      auto itr = running_pool_.begin();
      for (; itr != running_pool_.end();) {
        if ((*itr)->get_id() == handle->get_id()) {
          itr = running_pool_.erase(itr);
        }
        else {
          itr++;
        }
      }
      FAST_LOG(DEBUG,
               "remove_client_id:{} b_running_size:{} a_running_size:{} "
               "b_valid_size:{} a_valid_size:{}",
               handle->get_id(), b_runing_size, running_pool_.size(),
               b_valid_size, valid_pool_.size());
    }
    else {
      size_t b_runing_size = running_pool_.size();
      size_t b_valid_size = valid_pool_.size();
      auto id = handle->get_id();
      auto itr = running_pool_.begin();
      for (; itr != running_pool_.end();) {
        if (itr->get()->get_id() == handle->get_id()) {
          valid_pool_.emplace_back(std::move(*itr));
          itr = running_pool_.erase(itr);
        }
        else {
          itr++;
        }
      }
      FAST_LOG(DEBUG,
               "release_client_id:{} b_running_size:{} a_running_size:{} "
               "b_valid_size:{} a_valid_size:{}",
               id, b_runing_size, running_pool_.size(), b_valid_size,
               valid_pool_.size());
    }
  } catch (...) {
    std::cout << "release_connection error" << std::endl;
  }
}
size_t http_connection_pool::get_free_conn_cnt() { return 0; }

}  // namespace cpp_frame::http