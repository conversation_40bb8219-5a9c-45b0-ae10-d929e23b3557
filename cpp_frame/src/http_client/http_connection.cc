#include <cpp_frame/fast_fmt_log/fast_fmt_log.h>
#include <cpp_frame/fast_fmt_log/level.h>
#include <cpp_frame/http_client/http_connection.h>

#include <boost/asio/post.hpp>
#include <iostream>
namespace cpp_frame::http {

http_connection::http_connection(const http_executor_type &exec,
                                 const std::string &host,
                                 const std::string &port,
                                 boost::asio::ssl::context_base::method ssl_method)

    : excutor_(exec), ctx_(ssl_method), resolver_(exec), host_(host), port_(port) {}
http_connection::~http_connection() {
  std::cout << "end http_connection" << std::endl;
  FAST_LOG(WARN, "http_connection::{}, desc", __FUNCTION__);
}

void http_connection::close() {
  if (is_open_) {
    is_open_ = false;
    stream_->async_shutdown([&](boost::beast::error_code ec) {
      if (ec == boost::asio::error::eof) {
        // Rationale:
        // http://stackoverflow.com/questions/25587403/boost-asio-ssl-async-shutdown-always-finishes-with-an-error
        ec = {};
        FAST_LOG(WARN, "http_connection::{}, shutdown read eof", __FUNCTION__);
      }
      if (ec) {
        FAST_LOG(WARN, "http_connection::{}, close_error ec:[{}] ec_msg:[{}]",
                 __FUNCTION__, ec.value(), ec.message());
      }
      else {
        FAST_LOG(WARN, "http_connection::{}, close ec:[{}] ec_msg:[{}]",
                 __FUNCTION__, ec.value(), ec.message());
      }
    });
  }
}
void http_connection::connect() {
  ctx_.set_verify_mode(boost::asio::ssl::verify_none);
  stream_.emplace(excutor_, ctx_);
  if (!::SSL_set_tlsext_host_name(stream_->native_handle(), host_.c_str())) {
    boost::beast::error_code ec{static_cast<int>(::ERR_get_error()),
                                boost::asio::error::get_ssl_category()};
    std::cout << "ssl connect error" << std::endl;
    FAST_LOG(WARN, "http_connection::{}, connect ec:[{}] ec_msg:[{}]",
             __FUNCTION__, ec.value(), ec.message());
    read_cb_(response_, ec);
    return;
  }
  resolver_.async_resolve(
      host_, port_,
      boost::beast::bind_front_handler(&http_connection::on_resolve, this));
}

void http_connection::write(
    boost::beast::http::verb method, const std::string &target,
    const std::string &body,
    const std::vector<std::pair<std::string, std::string>> &header,
    READ_CB &&cb) {
  read_cb_ = std::move(cb);
  req_.version(11);
  req_.method(method);
  req_.set(boost::beast::http::field::connection, "keep-alive");
  req_.target(target);
  req_.set(boost::beast::http::field::host, host_);
  req_.set(boost::beast::http::field::user_agent, BOOST_BEAST_VERSION_STRING);
  req_.body() = body;
  req_.prepare_payload();
  if (header.size()) {
    for (auto &h : header) {
      req_.set(h.first, h.second);
    }
  }
  if (is_open_) {
    write_http();
  }
  else {
    connect();
  }
}

void http_connection::read() {
  boost::beast::http::async_read(
      *stream_, buffer_, response_,
      boost::beast::bind_front_handler(&http_connection::on_read, this));
}

void http_connection::write_http() {
  boost::beast::http::async_write(
      *stream_, req_,
      boost::beast::bind_front_handler(&http_connection::on_write, this));
}

void http_connection::on_resolve(
    boost::beast::error_code ec,
    boost::asio::ip::tcp::resolver::results_type results) {
  if (ec) {
    std::cout << "http resolve error" << std::endl;
    FAST_LOG(WARN, "http_connection::{}, on_resolve ec:[{}] ec_msg:[{}]",
             __FUNCTION__, ec.value(), ec.message());
    read_cb_(response_, ec);
    return;
  }
  // boost::beast::get_lowest_layer(stream_).expires_after(
  //     std::chrono::seconds(5));
  boost::beast::get_lowest_layer(*stream_).async_connect(
      results,
      boost::beast::bind_front_handler(&http_connection::on_connect, this));
}

void http_connection::on_connect(boost::system::error_code ec,
                                 const boost::asio::ip::tcp::endpoint &ep) {
  if (ec) {
    std::cout << "http connect error" << std::endl;
    FAST_LOG(WARN, "http_connection::{}, on_connect ec:[{}] ec_msg:[{}]",
             __FUNCTION__, ec.value(), ec.message());
    read_cb_(response_, ec);
    return;
  }
  stream_->async_handshake(
      boost::asio::ssl::stream_base::client,
      boost::beast::bind_front_handler(&http_connection::on_handshake, this));
}

void http_connection::on_handshake(boost::system::error_code ec) {
  if (ec) {
    std::cout << "http handshake error" << std::endl;
    FAST_LOG(WARN, "http_connection::{}, on_handshake ec:[{}] ec_msg:[{}]",
             __FUNCTION__, ec.value(), ec.message());
    read_cb_(response_, ec);
    return;
  }
  is_open_ = true;
  write_http();
}

void http_connection::on_write(boost::system::error_code ec, size_t) {
  if (ec) {
    std::cout << "write error" << std::endl;
    FAST_LOG(WARN, "http_connection::{}, on_write ec:[{}] ec_msg:[{}]",
             __FUNCTION__, ec.value(), ec.message());
    read_cb_(response_, ec);
    return;
  }
  response_.clear();
  response_.body().clear();
  buffer_.clear();
  read();
}

void http_connection::on_read(boost::system::error_code ec, size_t size) {
  if (ec) {
    std::cout << "read error" << std::endl;
  }
  if (!ec || (ec == boost::asio::error::eof && size > 0)) {
    read_cb_(response_, ec);
    if (ec == boost::asio::error::eof) {
      FAST_LOG(
          WARN,
          "http_connection::{}, on_read_eof msg_size:{} ec:[{}] ec_msg:[{}]",
          __FUNCTION__, size, ec.value(), ec.message());
    }
  }
  else {
    FAST_LOG(WARN, "http_connection::{}, on_read ec:[{}] ec_msg:[{}]",
             __FUNCTION__, ec.value(), ec.message());
    read_cb_(response_, ec);
  }
}
}  // namespace cpp_frame::http
