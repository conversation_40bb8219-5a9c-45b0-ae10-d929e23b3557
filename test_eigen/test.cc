#include <Eigen/Dense>
#include <cmath>
#include <iomanip>
#include <iostream>
#include <limits>

using DataFrame = Eigen::MatrixXd;
using namespace Eigen;
bool isNaN(double value) { return std::isnan(value); }
DataFrame ts_Mean(const DataFrame &s1, int n) {
  if (n <= 0)
    return s1;

  const int rows = s1.rows();
  const int cols = s1.cols();

  // 使用延迟求值创建结果矩阵
  DataFrame result =
      DataFrame::Constant(rows, cols, std::numeric_limits<double>::quiet_NaN());

  // 预分配内存以避免重新分配
  Eigen::Array<double, Eigen::Dynamic, 1> cumsum(rows + 1);
  Eigen::Array<int, Eigen::Dynamic, 1> count(rows + 1);

  // 对每一列计算滚动平均值
  for (int j = 0; j < cols; ++j) {
    // 初始化累积和和计数数组
    cumsum.setZero();
    count.setZero();

    // 计算累积和和有效值计数 - 使用向量化操作
    for (int i = 0; i < rows; ++i) {
      cumsum(i + 1) = cumsum(i);
      count(i + 1) = count(i);

      if (!isNaN(s1(i, j))) {
        cumsum(i + 1) += s1(i, j);
        count(i + 1)++;
      }
    }

    // 计算滚动平均值 - 使用向量化操作
    for (int i = 0; i < rows; ++i) {
      int start = std::max(0, i - n + 1);
      int window_count = count(i + 1) - count(start);

      if (window_count > 0) {
        double window_sum = cumsum(i + 1) - cumsum(start);
        result(i, j) = window_sum / window_count;
      }
    }
  }

  return result;
}

DataFrame ts_Corr(const DataFrame &s1, const DataFrame &s2, int n) {
  if (n <= 1) {
    // 相关系数至少需要2个样本
    return DataFrame::Constant(s1.rows(), s1.cols(),
                               std::numeric_limits<double>::quiet_NaN());
  }

  const int rows = s1.rows();
  const int cols = s1.cols();

  // 使用延迟求值创建结果矩阵
  DataFrame result =
      DataFrame::Constant(rows, cols, std::numeric_limits<double>::quiet_NaN());

  // 计算滚动均值
  DataFrame means1 = ts_Mean(s1, n);
  DataFrame means2 = ts_Mean(s2, n);

  for (int j = 0; j < cols; ++j) {
    for (int i = 0; i < rows; ++i) {
      if (isNaN(means1(i, j)) || isNaN(means2(i, j)))
        continue;

      int start = std::max(0, i - n + 1);

      // 使用Eigen向量化操作
      Eigen::VectorXd x_values(n);
      Eigen::VectorXd y_values(n);
      int valid_count = 0;

      // 收集窗口内的有效值
      for (int k = start; k <= i; ++k) {
        if (!isNaN(s1(k, j)) && !isNaN(s2(k, j))) {
          x_values(valid_count) = s1(k, j) - means1(i, j);
          y_values(valid_count) = s2(k, j) - means2(i, j);
          valid_count++;
        }
      }

      if (valid_count > 1) {
        // 使用Eigen计算点积和范数
        double sum_xy =
            x_values.head(valid_count).dot(y_values.head(valid_count));
        double sum_x2 = x_values.head(valid_count).squaredNorm();
        double sum_y2 = y_values.head(valid_count).squaredNorm();

        // if (sum_x2 > 1e-10 && sum_y2 > 1e-10) {
          // 计算相关系数
          result(i, j) = sum_xy / std::sqrt(sum_x2 * sum_y2);
        // }
      }
    }
  }

  return result;
}
void test_comprehensive() {
  std::cout << std::fixed << std::setprecision(8);

  // 测试用例1：完全线性相关
  DataFrame s1(11, 1), s2(11, 1);
  s1 << 0.16665, 0.16665, 0.16665, 0.16665, 0.16665, 0.16665, 0.16665, 0.16665, 0.16665, 0.16666,0.16677 ;
  s2 << 0, 0, 0, 0, 0, 0, 0, 0, 0, 31,991;
  DataFrame result1 = ts_Corr(s1, s2, 10);
  std::cout << "测试用例1结果:\n" << result1 << std::endl;
}

int main() {
  std::cout << "\n=== 综合测试 ===" << std::endl;
  test_comprehensive();

  return 0;
}