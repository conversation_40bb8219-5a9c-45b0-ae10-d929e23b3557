import pandas as pd
import numpy as np

def ts_Corr(s1, s2, n):
    n = int(n)
    if n <= 1:
        n = 1
    tem1 = s1.copy()
    tem2 = s2.copy()
    tem1[tem2.isna()] = np.nan
    tem2[tem1.isna()] = np.nan
    tem1_m = tem1.rolling(n, axis=0, min_periods=1).mean()
    tem2_m = tem2.rolling(n, axis=0, min_periods=1).mean()
    tem_prod_m = (tem1 * tem2).rolling(n, axis=0, min_periods=1).mean()
    tem1_std = tem1.rolling(n, axis=0, min_periods=1).std(ddof=0)
    tem2_std = tem2.rolling(n, axis=0, min_periods=1).std(ddof=0)
    res = (tem_prod_m - tem1_m * tem2_m) / (tem1_std * tem2_std)
    return res.replace([-np.inf, np.inf], np.nan)

# 测试1
s1 = pd.DataFrame([1.0, 2.0, 3.0, 4.0, 5.0])
s2 = pd.DataFrame([2.0, 4.0, 6.0, 8.0, 10.0])
print("Python测试1:", ts_Corr(s1, s2, 3).values.flatten())

# 测试2  
s3 = pd.DataFrame([1.0, 2.0, 3.0])
s4 = pd.DataFrame([1.0, 2.0, 3.0])
print("Python测试2:", ts_Corr(s3, s4, 2).values.flatten())

# 测试3
s5 = pd.DataFrame([1.0, 2.0, 3.0, 4.0])
s6 = pd.DataFrame([4.0, 3.0, 2.0, 1.0])
print("Python测试3:", ts_Corr(s5, s6, 3).values.flatten())