#include <cmath>
#include <cstddef>
#include <iostream>
#include <queue>

class volatility_calculator {
 public:
  volatility_calculator() {}
  void init(int sampling_length) { sampling_length_ = sampling_length; }
  void add_sample(double value) {
    if (!is_sampling_buffer_full()) {
      sampling_buffer_.push_back(value);
      return;
    }
    else {
      sampling_buffer_.pop_front();
      sampling_buffer_.push_back(value);
    }
  }
  double calculate() {
    last_vol_ = indicator_calculation();
    return last_vol_;
  }
  double current_value() { return last_vol_; }
  bool is_sampling_buffer_full() {
    return sampling_buffer_.size() >= sampling_length_;
  }

 private:
  double indicator_calculation() {
    if (sampling_buffer_.size() == 1)
      return 0;
    double diff_sum = 0;
    for (size_t i = 0; i < sampling_buffer_.size() - 1; i++) {
      diff_sum += std::pow(sampling_buffer_[i + 1] - sampling_buffer_[i], 2);
    }
    double mean = diff_sum / (sampling_buffer_.size());
    return std::sqrt(mean);
  }

 private:
  size_t sampling_length_;
  std::deque<double> sampling_buffer_;
  double last_vol_{-1};
};