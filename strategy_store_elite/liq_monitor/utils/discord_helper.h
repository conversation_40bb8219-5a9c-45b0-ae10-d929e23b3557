#pragma once
#include "fmt/core.h"
#include <dpp/dpp.h>

#include <unordered_map>
#include <vector>

namespace fast_trader_elite {
struct diccord_msg {
  std::string title;
  std::string description;
  std::vector<std::pair<std::string, std::string>> content;
};

class discord_helper {
public:
  discord_helper(std::string discord_token)
      : discord_token_(discord_token),
        bot_(discord_token, dpp::i_default_intents | dpp::i_message_content) {}
  void start() { bot_.start(dpp::st_wait); }

  void send(int64_t channel_id, diccord_msg &msg) {
    send(channel_id, msg.title, msg.description, msg.content);
  }

private:
  void send(int64_t channel_id, std::string title, std::string description,
            std::vector<std::pair<std::string, std::string>> &content) {
    // dpp::embed embed = dpp::embed()
    //                        .set_color(dpp::colors::sti_blue)
    //                        .set_title(title)
    //                        .set_description(description);
    // for (auto &con : content) {
    //   embed.add_field(con.first, con.second, true);
    // }
    // embed.set_timestamp(time(0));
    // bot_.message_create(dpp::message(channel_id, embed));
    for (auto &con : content) {
      size_t msg_size = con.second.size();
      int n = msg_size / 1000;
      for (int i = 0; i < n; i++) {
        dpp::embed embed = dpp::embed()
                               .set_color(dpp::colors::sti_blue)
                               .set_title(title)
                               .set_description(description);
        std::string cur_msg = con.second.substr(i * n, 1000);
        fmt::print("msg1:{}\n", cur_msg);
        embed.add_field(con.first, cur_msg, true);
        embed.set_timestamp(time(0));
        bot_.message_create(dpp::message(channel_id, embed));
      }
      dpp::embed embed = dpp::embed()
                             .set_color(dpp::colors::sti_blue)
                             .set_title(title)
                             .set_description(description);
      std::string cur_msg = con.second.substr(n * 1000, msg_size - n * 1000);

      fmt::print("msg2:{}\n", cur_msg);
      embed.add_field(con.first, cur_msg, true);
      embed.set_timestamp(time(0));
      bot_.message_create(dpp::message(channel_id, embed));
    }
  }

private:
  std::string discord_token_;
  dpp::cluster bot_;
};
} // namespace fast_trader_elite