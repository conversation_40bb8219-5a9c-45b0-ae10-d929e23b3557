#pragma once
#include "../data/data_manager.h"
#include "../data/period_range.h"

#include "../utils/volatility_calculator.h"
#include "fast_trader_elite/data_model/field.h"
#include "i_strategy_ctx.h"
#include <cstdint>
#include <deque>
#include <vector>
namespace fast_trader_elite::strategy {

class rule {
public:
  rule(data *data, fast_trader_elite::strategy::logger &logger,
       i_strategy_ctx *ctx);
  bool on_start(i_strategy_ctx *ctx);
  bool on_stop(i_strategy_ctx *ctx);
  void on_depth_data(i_strategy_ctx *ctx, depth_market_data_field *field);
  void on_tick_data(i_strategy_ctx *ctx, tick_market_data_field *field);
  void on_transaction_data(i_strategy_ctx *ctx, transaction_field *field);
  void on_liquidation_data(i_strategy_ctx *ctx, liquidation_field *field);
  void on_order(i_strategy_ctx *ctx, order_field *field);
  void on_trade(i_strategy_ctx *ctx, trade_field *field);
  void calculate_liq(uint64_t ts);
  void calculate_mean_vol();
  void calculate_trade();
  void calculate_pin(uint64_t recv_time);

private:
  void calculate_pin_by_ts(int ts_type, uint64_t recv_time);

private:
  data *data_{nullptr};
  fast_trader_elite::strategy::logger &logger_;
  std::deque<liquidation_field> ins_liq_;
  std::deque<transaction_field> ins_trans_;
  volatility_calculator vol_calculator_;
  uint64_t feed_vol_ts_{0};
  double volatility_{0};
  std::vector<double> vols_;
  double oi_usdt_;

  period_range_monitor range_monitor_;
};
} // namespace fast_trader_elite::strategy