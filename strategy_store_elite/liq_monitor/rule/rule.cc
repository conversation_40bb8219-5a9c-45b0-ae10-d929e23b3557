#include "rule.h"
#include "../../frame/math_helper.h"
#include "../../frame/order_helper.h"
#include "../../frame/strategy_logger.h"
#include "fast_trader_elite/data_model/field.h"
#include "fast_trader_elite/data_model/type.h"
#include "fmt/core.h"
#include <cstdint>

namespace fast_trader_elite::strategy {
rule::rule(data *data, fast_trader_elite::strategy::logger &logger,
           i_strategy_ctx *ctx)
    : data_(data), logger_(logger) {
  vol_calculator_.init(150);
  range_monitor_.init(data_->instrument, 3, 10, 50, 85);
  data_->bar_pin_counts.resize(4);
  data_->bar_pin_counts = {0, 0, 0, 0};
  data_->pin_range_bps.resize(4);
  data_->stoploss_counts.resize(4);
}
bool rule::on_start(i_strategy_ctx *ctx) { return true; }
bool rule::on_stop(i_strategy_ctx *ctx) { return true; }
void rule::on_depth_data(i_strategy_ctx *ctx, depth_market_data_field *md) {
  // 使用静态常量避免频繁创建临时变量
  static const int64_t sampling_interval = 1e8;

  uint64_t recv_time = md->local_timestamp;
  double mp = (md->ask_price[0] + md->bid_price[0]) / 2;
  data_->mp = mp;

  // 只有当时间间隔足够大时才添加样本，减少不必要的计算
  if (recv_time > feed_vol_ts_ + sampling_interval) {
    // 使用当前中间价格作为样本
    vol_calculator_.add_sample(mp);
    feed_vol_ts_ = recv_time;

    // 如果缓冲区已满，立即计算波动率
    if (vol_calculator_.is_sampling_buffer_full()) {
      // 直接计算 BPS 值，避免额外的乘法运算
      data_->cur_vol = vol_calculator_.calculate() * 10000 / mp;
      vols_.push_back(data_->cur_vol);
    }
  }
}
void rule::on_tick_data(i_strategy_ctx *ctx, tick_market_data_field *field) {
  uint64_t recv_time = field->local_timestamp;
  data_->oi_usdt = field->open_interest_value;
  data_->turnover_24h = field->turnover;
}

void rule::on_transaction_data(i_strategy_ctx *ctx, transaction_field *field) {
  ins_trans_.push_back(*field);
  range_monitor_.feed_mp(field->price, field->local_timestamp);
}
void rule::on_liquidation_data(i_strategy_ctx *ctx, liquidation_field *field) {
  ins_liq_.push_back(*field);
}

void rule::on_order(i_strategy_ctx *ctx, order_field *f) {}

void rule::on_trade(i_strategy_ctx *ctx, trade_field *f) {}
void rule::calculate_liq(uint64_t ts) {
  while (ins_liq_.size() &&
         ts - ins_liq_.front().local_timestamp > 1e9 * 60 * data_->liq_win) {
    ins_liq_.pop_front();
  }
  double long_liq = 0;
  double short_liq = 0;
  for (auto &liq : ins_liq_) {
    if (liq.direction == direction_type::BUY) {
      long_liq += liq.price * liq.volume;
    } else {
      short_liq += liq.price * liq.volume;
    }
  }
  data_->long_liq = long_liq;
  data_->short_liq = short_liq;
}

void rule::calculate_trade() {
  uint64_t buy_cnt = 0, sell_cnt = 0, buy_trade_volume = 0,
           sell_trade_volume = 0;
  for (auto &trade : ins_trans_) {
    if (trade.is_maker) {
      sell_cnt += 1;
      sell_trade_volume += trade.volume;
    } else {
      buy_cnt += 1;
      buy_trade_volume += trade.volume;
    }
  }
  data_->buy_trade_speed = buy_cnt * 1.0 / data_->trade_win;
  data_->sell_trade_speed = sell_cnt * 1.0 / data_->trade_win;
  data_->buy_trade_volume = buy_trade_volume;
  data_->sell_trade_volume = sell_trade_volume;
  ins_trans_.clear();
}

void rule::calculate_mean_vol() {
  double sum = 0;
  for (auto vol : vols_) {
    sum += vol;
  }
  if (vols_.size()) {
    data_->mean_vol = sum * 1.0 / vols_.size();
  } else {
    data_->mean_vol = 0;
  }
  vols_.clear();
}

void rule::calculate_pin(uint64_t recv_time) {
  calculate_pin_by_ts(0, recv_time);
  calculate_pin_by_ts(1, recv_time);
  calculate_pin_by_ts(2, recv_time);
  calculate_pin_by_ts(3, recv_time);
}

void rule::calculate_pin_by_ts(int ts_type, uint64_t recv_time) {
  auto &boom_res = range_monitor_.get_boom_result();
  auto cmp = [](const boom_result &a, const boom_result &b) -> bool {
    return a.boom_start_ts < b.boom_start_ts;
  };
  uint64_t period = 0;
  if (ts_type == 0) {
    period = 10 * 60 * 1e9;
  } else if (ts_type == 1) {
    period = 60 * 60 * 1e9;
  } else if (ts_type == 2) {
    period = 240 * 60 * 1e9;
  } else if (ts_type == 3) {
    period = 48 * 60 * 60 * 1e9;
  }
  boom_result cur_bar;
  cur_bar.boom_start_ts = recv_time - period;
  auto first_itr =
      std::lower_bound(boom_res.begin(), boom_res.end(), cur_bar, cmp);
  if (first_itr == boom_res.end()) {
    fmt::print("can not find\n");
    return;
  }
  size_t pos = first_itr - boom_res.begin();
  double sum_max_range_bps = 0;
  int pin_counts = 0;
  for (size_t i = pos; i < boom_res.size(); i++) {
    sum_max_range_bps += boom_res[i].max_range_bps;
    pin_counts++;
  }
  double avg_bps = (sum_max_range_bps / pin_counts);
  // double avg_bps = 55;
  double real_sum_max_range_bps = 0;
  double real_pin_range_bps = 0;
  int real_pin_counts = 0;
  int real_stop_counts = 0;
  for (size_t i = pos; i < boom_res.size(); i++) {
    auto &boom = boom_res[i];
    if (boom.max_range_bps > avg_bps) {
      if (boom.is_stop_loss) {
        real_stop_counts++;
      } else {
        real_sum_max_range_bps += boom.max_range_bps;
        real_pin_range_bps += boom.regress_bps;
        real_pin_counts++;
      }
    }
  }
  data_->bar_pin_counts[ts_type] = real_pin_counts;
  data_->stoploss_counts[ts_type] = real_stop_counts;
  if (real_pin_counts != 0) {
    data_->pin_range_bps[ts_type] = {real_sum_max_range_bps / real_pin_counts,
                                     real_pin_range_bps / real_pin_counts};
  } else {
    data_->pin_range_bps[ts_type] = {0, 0};
  }
}

} // namespace fast_trader_elite::strategy