#include "../../frame/strategy_logger.h"
#include "../data/data_manager.h"
#include "../rule/rule.h"
#include "../utils/discord_helper.h"
#include "i_strategy.h"
#include <cstdint>
namespace fast_trader_elite::strategy {

class reporter {
public:
  reporter(i_strategy_ctx *ctx, fast_trader_elite::strategy::logger &logger,
           data_manager *data, std::vector<rule *> &rules);
  void init_timer();

private:
  void report_all();
  void report_rank();
  void report_vol_rank();
  void report_trade_speed();
  void report_trade_volume();
  void report_pin();

private:
  i_strategy_ctx *ctx_;
  fast_trader_elite::strategy::logger &logger_;
  data_manager *data_{nullptr};
  std::vector<rule *> &rules_;
  int liq_timer_id_;
  int liq_rank_timer_id_;
  int trade_timer_id_;
  int pin_bar_id_;
  double all_long_liq_;
  double all_short_liq_;
  fast_trader_elite::discord_helper *liq_discord_;
  int64_t all_liq_channel_;
  int64_t rank_liq_channel_;
  int64_t vol_channel_;
  int64_t trade_channel_;
  int64_t pin_channel_;
  uint64_t last_report_all_liq_ts_;
};
} // namespace fast_trader_elite::strategy