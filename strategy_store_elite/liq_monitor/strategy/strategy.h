#include "../../frame/strategy_logger.h"
#include "../data/data_manager.h"
#include "../rule/rule.h"
#include "fast_trader_elite/data_model/field.h"
#include "i_strategy.h"

#include "reporter.h"
#include <nlohmann_json/json.hpp>
#include <vector>

namespace fast_trader_elite::strategy {

class strategy : public i_strategy {
public:
  strategy();
  virtual ~strategy();
  virtual bool on_start(i_strategy_ctx *ctx, const std::string& strategy_name) override;
  virtual bool on_stop(i_strategy_ctx *ctx) override;
  virtual void on_depth_data(i_strategy_ctx *ctx,
                             depth_market_data_field *field) override;
  virtual void on_tick_data(i_strategy_ctx *ctx,
                            tick_market_data_field *field) override;
  virtual void on_transaction_data(i_strategy_ctx *ctx,
                                   transaction_field *field) override;
  virtual void on_liquidation_data(i_strategy_ctx *ctx,
                                   liquidation_field *field) override;
  virtual void on_order(i_strategy_ctx *ctx, order_field *field) override;
  virtual void on_trade(i_strategy_ctx *ctx, trade_field *field) override;

private:
  void parse_config(i_strategy_ctx *ctx, const std::string& config_path);
  void subscribe(i_strategy_ctx *ctx);

private:
  int async_timer_id_;
  fast_trader_elite::strategy::logger logger_;
  data_manager *data_{nullptr};
  std::vector<rule *> rules_;
  nlohmann::json config_json_;
  reporter *reporter_;
};
} // namespace fast_trader_elite::strategy