#include "strategy.h"
#include "fast_trader_elite/data_model/field.h"
#include <algorithm>
#include <fstream>
namespace fast_trader_elite::strategy {

strategy::strategy() {
  rules_.resize(1000);
  std::fill(rules_.begin(), rules_.end(), nullptr);
}
strategy::~strategy() {
  logger_.poll();
  logger_.flush();
  std::cout << "strategy desc" << std::endl;
}
bool strategy::on_start(i_strategy_ctx *ctx, const std::string &strategy_name) {
  // 从 ctx 中获取策略配置
  strategy_instance_config config = ctx->get_strategy_config(strategy_name);

  // 使用配置中的日志路径和日志级别
  std::string log_path = config.log_file.empty()
                             ? "../log/" + strategy_name + ".log"
                             : config.log_file;
  SET_STRA_LOG_FILE(logger_, log_path.c_str());

  // 设置日志级别
  if (config.log_level == "DEBUG") {
    SET_STRA_LOG_LEVEL(logger_, LOG_DEBUG);
  } else if (config.log_level == "INFO") {
    SET_STRA_LOG_LEVEL(logger_, LOG_INFO);
  } else if (config.log_level == "WARN") {
    SET_STRA_LOG_LEVEL(logger_, LOG_WARN);
  } else if (config.log_level == "ERROR") {
    SET_STRA_LOG_LEVEL(logger_, LOG_ERROR);
  } else {
    SET_STRA_LOG_LEVEL(logger_, LOG_DEBUG); // 默认级别
  }

  STRA_LOG(logger_, STRA_INFO,
           "Strategy starting, name: {}, log_file: {}, log_level: {}",
           strategy_name, log_path, config.log_level);

  data_ = new data_manager(logger_);
  parse_config(ctx, config.strategy_config_path);

  for (auto data : data_->datas) {
    if (data) {
      rules_[data->instrument_idx] = new rule(data, logger_, ctx);
    }
  }
  subscribe(ctx);
  reporter_ = new reporter(ctx, logger_, data_, rules_);
  reporter_->init_timer();
  async_timer_id_ = ctx->register_async_timer(
      100, [this]() { STRA_POLL(logger_); }, true);
  return true;
}
bool strategy::on_stop(i_strategy_ctx *ctx) {
  std::cout << "strategy on stop" << std::endl;
  STRA_LOG(logger_, STRA_DEBUG, "{} ", __FUNCTION__);
  for (auto rule : rules_) {
    if (rule) {
      rule->on_stop(ctx);
    }
  }
  return true;
}
void strategy::parse_config(i_strategy_ctx *ctx,
                            const std::string &config_path) {
  STRA_LOG(logger_, STRA_INFO, "Loading config from: {}", config_path);
  data_->parse_config(ctx);
}

void strategy::subscribe(i_strategy_ctx *ctx) {
  auto &datas = data_->datas;
  md_sub_code_field f;
  f.md_id = 0;
  for (auto data : datas) {
    if (data) {
      if (data->instrument.find("PERP") == data->instrument.npos) {
        STRA_LOG(logger_, STRA_DEBUG, "sub:{}", data->instrument);
        std::string liq_sub_code = "liquidation." + data->instrument;
        std::string order_book_sub_code = "orderbook.50." + data->instrument;
        std::string ticker_sub_code = "tickers." + data->instrument;
        std::string trade_sub_code = "publicTrade." + data->instrument;
        f.sub_code.push_back(liq_sub_code);
        f.sub_code.push_back(order_book_sub_code);
        f.sub_code.push_back(ticker_sub_code);
        f.sub_code.push_back(trade_sub_code);
      }
    }
  }
  ctx->subscribe(&f);
}

void strategy::on_depth_data(i_strategy_ctx *ctx,
                             depth_market_data_field *field) {
  auto rule = rules_[field->instrument_idx];
  if (rule) {
    // std::cout<<cpp_frame::struct_serialize::to_json(*field)<<std::endl;
    rule->on_depth_data(ctx, field);
  }
}
void strategy::on_tick_data(i_strategy_ctx *ctx,
                            tick_market_data_field *field) {
  auto rule = rules_[field->instrument_idx];
  if (rule) {
    rule->on_tick_data(ctx, field);
  }
}
void strategy::on_transaction_data(i_strategy_ctx *ctx,
                                   transaction_field *field) {
  auto rule = rules_[field->instrument_idx];
  if (rule) {
    rule->on_transaction_data(ctx, field);
  }
}
void strategy::on_order(i_strategy_ctx *ctx, order_field *field) {

  auto rule = rules_[field->instrument_idx];
  if (rule) {
    rule->on_order(ctx, field);
  }
}
void strategy::on_trade(i_strategy_ctx *ctx, trade_field *field) {
  auto rule = rules_[field->instrument_idx];
  if (rule) {
    rule->on_trade(ctx, field);
  }
}
void strategy::on_liquidation_data(i_strategy_ctx *ctx,
                                   liquidation_field *field) {
  STRA_LOG(logger_, STRA_INFO, "liquidation_field:{}",
           cpp_frame::struct_serialize::to_json(*field));
  auto rule = rules_[field->instrument_idx];
  if (rule) {
    rule->on_liquidation_data(ctx, field);
  }
}
} // namespace fast_trader_elite::strategy
