#include "reporter.h"
#include "../../frame/date.h"
#include <cstdint>
#include <fmt/chrono.h>
#include <fmt/core.h>
#include <fmt/printf.h>
namespace fast_trader_elite::strategy {
reporter::reporter(i_strategy_ctx *ctx,
                   fast_trader_elite::strategy::logger &logger,
                   data_manager *data, std::vector<rule *> &rules)
    : ctx_(ctx), logger_(logger), data_(data), rules_(rules) {
  liq_discord_ = new discord_helper("MTExNjM1MTEzMTYxMDc3OTY2OA.GIqDWz."
                                    "U5y6Vr4dcHOu_mxqQzmgTj1C4ToTqMF3ucLXiM");
  all_liq_channel_ = 1270376816905027636;
  rank_liq_channel_ = 1270556677917118545;
  vol_channel_ = 1273645670053711902;
  trade_channel_ = 1275617166020051065;
  pin_channel_ = 1282262720535728200;
}

void reporter::init_timer() {
  liq_timer_id_ = ctx_->register_timer(
      60 * 1000, [this]() { report_all(); }, true);
  liq_rank_timer_id_ = ctx_->register_timer(
      data_->liq_win * 60 * 1000,
      [this]() {
        uint64_t t1 = date::get_current_nano_sec();

        report_rank();
        report_vol_rank();
        uint64_t t2 = date::get_current_nano_sec();
        fmt::print("lact:{:.2f}\n", (t2 - t1) / 1000000.0);
      },
      true);
  trade_timer_id_ = ctx_->register_timer(
      data_->trade_win * 60 * 1000, [this]() { report_trade_speed(); }, true);
  pin_bar_id_ = ctx_->register_timer(
      10 * 60 * 1000, [this]() { report_pin(); }, true);
}

void reporter::report_all() {
  uint64_t cur_ts = date::get_current_nano_sec();
  for (auto rule : rules_) {
    if (rule != nullptr) {
      rule->calculate_liq(cur_ts);
    }
  }
  double long_liq = 0;
  double short_liq = 0;
  for (auto ins_data : data_->datas) {
    if (ins_data != nullptr) {
      long_liq += ins_data->long_liq;
      short_liq += ins_data->short_liq;
    }
  }
  bool need_report = false;
  if (long_liq > 1.5 * all_long_liq_) {
    need_report = true;
    all_long_liq_ = long_liq;
  }
  if (short_liq > 1.5 * all_short_liq_) {
    need_report = true;
    all_short_liq_ = short_liq;
  }
  if (cur_ts > last_report_all_liq_ts_ + data_->liq_win * 60 * 1e9) {
    need_report = true;
    all_short_liq_ = short_liq;
    all_long_liq_ = long_liq;
  }
  if (!need_report) {
    return;
  }
  last_report_all_liq_ts_ = date::get_current_nano_sec();
  std::string content =
      fmt::format("多头爆仓：{:.4f}万 \n 空头爆仓：{:.4f}万\n",
                  all_long_liq_ / 10000, all_short_liq_ / 10000);
  diccord_msg msg;
  msg.title = fmt::format("{}分钟 爆仓数量\n", data_->liq_win);
  auto cur_time =
      fmt::format("{:%Y-%m-%d %H:%M:%S}\n", fmt::localtime(std::time(0)));
  msg.description += cur_time;
  msg.content.push_back({"", content});
  liq_discord_->send(all_liq_channel_, msg);
}

void reporter::report_rank() {
  uint64_t cur_ts = date::get_current_nano_sec();
  for (auto rule : rules_) {
    if (rule != nullptr) {
      rule->calculate_liq(cur_ts);
    }
  }
  std::vector<std::pair<std::string, double>> long_tmp;
  std::vector<std::pair<std::string, double>> short_tmp;

  for (auto data : data_->datas) {
    if (data) {
      // fmt::print("ins:{} long_liq:{:.2f} short_liq:{:.2f}\n",
      // data->instrument,
      //            data->long_liq, data->short_liq);
      if (data->long_liq != 0) {
        long_tmp.push_back({data->instrument, data->long_liq});
      }
      if (data->short_liq != 0) {
        short_tmp.push_back({data->instrument, data->short_liq});
      }
    }
  }

  std::sort(
      long_tmp.begin(), long_tmp.end(),
      [=](std::pair<std::string, double> &a,
          std::pair<std::string, double> &b) { return a.second > b.second; });

  std::sort(
      short_tmp.begin(), short_tmp.end(),
      [=](std::pair<std::string, double> &a,
          std::pair<std::string, double> &b) { return a.second > b.second; });

  std::string content;
  for (auto i = 0; i < std::min(size_t(15), long_tmp.size()); i++) {
    content += fmt::format("{} 多头爆仓量:{:.4f}万 \n", long_tmp[i].first,
                           long_tmp[i].second / 10000);
  }
  diccord_msg msg;
  msg.title = fmt::format("bybit {}分钟多头爆仓rank\n", data_->liq_win);
  auto cur_time =
      fmt::format("{:%Y-%m-%d %H:%M:%S}\n", fmt::localtime(std::time(0)));
  msg.description += cur_time;
  msg.content.push_back({"", content});
  liq_discord_->send(rank_liq_channel_, msg);

  std::string short_content;
  for (auto i = 0; i < std::min(size_t(15), short_tmp.size()); i++) {
    short_content +=
        fmt::format("{} 空头爆仓量:{:.4f}万 \n", short_tmp[i].first,
                    short_tmp[i].second / 10000);
  }
  diccord_msg short_msg;
  short_msg.title = fmt::format("bybit 空头爆仓rank\n");
  short_msg.description += cur_time;
  short_msg.content.push_back({"", short_content});
  liq_discord_->send(rank_liq_channel_, short_msg);
}

void reporter::report_vol_rank() {
  for (auto rule : rules_) {
    if (rule != nullptr) {
      rule->calculate_mean_vol();
    }
  }
  std::vector<std::pair<std::string, std::vector<double>>> tmp;

  for (auto data : data_->datas) {
    if (data) {
      if (data->mean_vol != 0) {
        tmp.push_back({data->instrument,
                       {data->mean_vol, data->oi_usdt, data->turnover_24h}});
      }
    }
  }

  std::sort(tmp.begin(), tmp.end(),
            [=](std::pair<std::string, std::vector<double>> &a,
                std::pair<std::string, std::vector<double>> &b) {
              return a.second[0] > b.second[0];
            });
  std::string content;
  for (auto i = 0; i < std::min(size_t(15), tmp.size()); i++) {
    content +=
        fmt::format("{} 波动率:{:.2f}bps   oi:{:.2f}M   turnover:{:.2f}\n",
                    tmp[i].first, tmp[i].second[0],
                    tmp[i].second[1] / 1000000.0, tmp[i].second[2] / 1000000.0);
  }
  diccord_msg msg;
  msg.title = fmt::format("bybit {}分钟平均波动率rank\n", data_->liq_win);
  auto cur_time =
      fmt::format("{:%Y-%m-%d %H:%M:%S}\n", fmt::localtime(std::time(0)));
  msg.description += cur_time;
  msg.content.push_back({"", content});
  liq_discord_->send(vol_channel_, msg);
}

void reporter::report_trade_speed() {
  for (auto rule : rules_) {
    if (rule != nullptr) {
      rule->calculate_trade();
    }
  }
  double buy_speed = 0, sell_speed = 0;
  for (auto data : data_->datas) {
    if (data) {
      if (data->buy_trade_speed != 0) {
        buy_speed += data->buy_trade_speed;
      }
      if (data->sell_trade_speed != 0) {
        sell_speed += data->sell_trade_speed;
      }
    }
  }
  buy_speed = buy_speed / data_->ins_cnt;
  sell_speed = sell_speed / data_->ins_cnt;
  std::string content;
  content +=
      fmt::format("买:{:.2f}笔/min 卖:{:.2f}笔/min\n", buy_speed, sell_speed);
  diccord_msg msg;
  msg.title = fmt::format("bybit {}分钟平均成交速度\n", data_->trade_win);
  auto cur_time =
      fmt::format("{:%Y-%m-%d %H:%M:%S}\n", fmt::localtime(std::time(0)));
  msg.description += cur_time;
  msg.content.push_back({"", content});
  liq_discord_->send(trade_channel_, msg);

  // std::vector<data *> tmp;
  // for (auto data : data_->datas) {
  //   if (data) {
  //     double trade_all =
  //         (data->buy_trade_volume + data->sell_trade_volume) * data->mp;
  //     double trade_ratio = data->buy_trade_volume /
  //                          (data->buy_trade_volume + data->sell_trade_volume);
  //     if (trade_all > 5.0 * 1e6 / 24 / 6 && data->turnover_24h > 5 * 1e6 &&
  //         (trade_ratio > 0.6 || trade_ratio < 0.4)) {
  //       tmp.push_back(data);
  //     }
  //   }
  // }
  // std::sort(tmp.begin(), tmp.end(), [=](data *a, data *b) {
  //   double a_trade_ratio =
  //       a->buy_trade_volume / (a->buy_trade_volume + a->sell_trade_volume);
  //   double b_trade_ratio =
  //       b->buy_trade_volume / (b->buy_trade_volume + b->sell_trade_volume);
  //   return a_trade_ratio > b_trade_ratio;
  // });
  // std::string trade_ratio_content;
  // for (auto i = 0; i < std::min(size_t(15), tmp.size()); i++) {
  //   auto data = tmp[i];
  //   double trade_ratio = data->buy_trade_volume /
  //                        (data->buy_trade_volume + data->sell_trade_volume);
  //   if (trade_ratio > 0.5) {
  //     trade_ratio_content +=
  //         fmt::format("{} ratio:{:.2f}\n", data->instrument, trade_ratio);
  //     fmt::print("{} {:.2f} {:.2f}\n", data->instrument, data->buy_trade_volume,
  //                data->sell_trade_volume);
  //   }
  // }

  // diccord_msg buy_trade_ratio_msg;
  // buy_trade_ratio_msg.title = fmt::format("成交买主动占比排序\n");
  // buy_trade_ratio_msg.description += cur_time;
  // buy_trade_ratio_msg.content.push_back({"", trade_ratio_content});
  // liq_discord_->send(trade_channel_, buy_trade_ratio_msg);

  // trade_ratio_content.clear();
  // for (auto i = tmp.size() - 1; i > std::max(tmp.size() - 15, size_t(0)); i--) {
  //   auto data = tmp[i];
  //   double trade_ratio = data->buy_trade_volume /
  //                        (data->buy_trade_volume + data->sell_trade_volume);
  //   double trade_all =
  //       (data->buy_trade_volume + data->sell_trade_volume) * data->mp;
  //   if (trade_ratio < 0.5) {
  //     trade_ratio_content +=
  //         fmt::format("{} ratio:{:.2f}\n", data->instrument, trade_ratio);
  //     fmt::print("{} {:.2f} {:.2f}\n", data->instrument, data->buy_trade_volume,
  //                data->sell_trade_volume);
  //   }
  // }
  // diccord_msg sell_trade_ratio_msg;
  // sell_trade_ratio_msg.title = fmt::format("成交卖主动占比排序\n");
  // sell_trade_ratio_msg.description += cur_time;
  // sell_trade_ratio_msg.content.push_back({"", trade_ratio_content});
  // liq_discord_->send(trade_channel_, sell_trade_ratio_msg);
}

void reporter::report_pin() {
  uint64_t cur_ts = date::get_current_nano_sec();
  for (auto rule : rules_) {
    if (rule != nullptr) {
      rule->calculate_pin(cur_ts);
    }
  }
  std::vector<data *> tmp;
  for (auto data : data_->datas) {
    if (data) {
      if (data->bar_pin_counts[3] > 0) {
        tmp.push_back(data);
      }
    }
  }
  std::sort(tmp.begin(), tmp.end(), [=](data *a, data *b) {
    double x = a->bar_pin_counts[0] + 0.75 * a->bar_pin_counts[1] +
               0.5 * a->bar_pin_counts[2] + 0.25 * a->bar_pin_counts[3];
    double y = b->bar_pin_counts[0] + 0.75 * b->bar_pin_counts[1] +
               0.5 * b->bar_pin_counts[2] + 0.25 * b->bar_pin_counts[3];
    return x > y;
  });
  std::string content;

  for (auto i = 0; i < std::min(size_t(30), tmp.size()); i++) {
    auto &range = tmp[i]->pin_range_bps;
    auto &big_bar = tmp[i]->stoploss_counts;
    content += fmt::format(
        "{}:{} {} {} {} | ({:.0f},{:.0f}),({:.0f},{:.0f}),({:.0f},{:.0f}) | {} {} {} {}\n",
        tmp[i]->instrument, tmp[i]->bar_pin_counts[0],
        tmp[i]->bar_pin_counts[1], tmp[i]->bar_pin_counts[2],
        tmp[i]->bar_pin_counts[3], range[0].first, range[0].second,
        range[1].first, range[1].second, range[2].first, range[2].second,
        big_bar[0], big_bar[1], big_bar[2], big_bar[3]);
  }
  diccord_msg msg;
  msg.title = fmt::format("针个数排序\n");
  auto cur_time =
      fmt::format("{:%Y-%m-%d %H:%M:%S}\n", fmt::localtime(std::time(0)));
  msg.description += cur_time;
  msg.content.push_back({"", content});
  liq_discord_->send(pin_channel_, msg);
}

} // namespace fast_trader_elite::strategy