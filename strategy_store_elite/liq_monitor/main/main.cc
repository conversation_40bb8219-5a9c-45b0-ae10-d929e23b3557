#include "../strategy/strategy.h"

extern "C" {
fast_trader_elite::strategy::i_strategy *create() {
  fprintf(stdout, "my_strategy_so::%s\n", __FUNCTION__);
  fast_trader_elite::strategy::strategy *strategy =
      new fast_trader_elite::strategy::strategy();
  return strategy;
}

void destroy(void *p) {
  fprintf(stdout, "my_strategy_so::%s, p[%p]\n", __FUNCTION__, p);
  delete (fast_trader_elite::strategy::strategy *)p;
}
};
