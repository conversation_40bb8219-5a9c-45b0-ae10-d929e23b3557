#pragma once
#include "fast_trader_elite/data_model/field.h"
#include "fmt/core.h"
#include <algorithm>
#include <cstdint>
#include <deque>
#include <string>
#include <sys/time.h>
#include <time.h>
#define BPS 10000.0
namespace fast_trader_elite {

struct boom_result {
  uint64_t boom_start_ts;
  double max_range_bps{0};
  double regress_bps{0};
  bool is_stop_loss{false};
};

class period_range_monitor {
public:
  void init(std::string ins, int boom_period, int regress_period,
            double boom_bps, double stop_loss_bps) {
    ins_ = ins;
    boom_period_ = boom_period * 1e9;
    regress_period_ = regress_period * 1e9;
    boom_bps_ = boom_bps;
    stop_loss_bps_ = stop_loss_bps;
  }
  void feed_trade(trade_field *f) {
    while (trades_.size() > 0 &&
           trades_.front().local_timestamp + regress_period_ * 1e9 <
               f->local_timestamp) {
      trades_.pop_front();
    }
    trades_.push_back(*f);
  }
  std::deque<boom_result> &get_boom_result() { return boom_queue_; }

private:
  void process_trade() {
    // 从0开始找到第一个大于boom_period_的位置 在这个区间找到最大值和最小值
    // 判断极值 - 初始值是否 > boom_bps_
    // 如果大于就开始计算回归值：看剩下的队列中 止损 和止盈的位置
    if (trades_.empty()) {
      return;
    }
    auto first_trade = trades_.front();
    uint64_t begin_ts = first_trade.local_timestamp;
    uint64_t boom_end_ts = begin_ts + boom_period_;
    double min_p = first_trade.last_price;
    double max_p = first_trade.last_price;
    double max_idx = 0;
    double min_idx = 0;
    for (int i = 0; i < trades_.size(); i++) {
      auto &tr = trades_[i];
      if (tr.local_timestamp < boom_end_ts) {
        if (tr.last_price > max_p) {
          max_p = tr.last_price;
          max_idx = i;
        }
        if (tr.last_price < min_p) {
          min_p = tr.last_price;
          min_idx = i;
        }
      } else {
        break;
      }
    }
    double max_range_bps = (max_p - min_p) * BPS / first_trade.last_price;
    if (max_range_bps < boom_bps_) {
      return;
    }
    // 先往下走 在拉回来 
    if (max_ts > min_ts) {

    }
    for (int i = 0; i < trades_.size(); i++) {
      auto &tr = trades_[i];
      if (tr.local_timestamp > boom_end_ts) {

      } else {
        break;
      }
    }
  }

private:
  std::string ins_;
  uint64_t boom_period_;
  uint64_t regress_period_;
  double boom_bps_;
  double stop_loss_bps_;
  std::deque<trade_field> trades_;
  std::deque<boom_result> boom_queue_;
};
} // namespace fast_trader_elite