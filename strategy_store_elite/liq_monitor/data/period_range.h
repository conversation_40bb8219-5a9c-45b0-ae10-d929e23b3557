#pragma once
#include "fmt/core.h"
#include <algorithm>
#include <cstdint>
#include <deque>
#include <string>
#include <sys/time.h>
#include <time.h>
// 维护一个5s的时间窗口 在5s的窗口中用第1s去计算最高最低 
//计算第1s的波动 大于 60bps 并且后续的5s中找极值 结果push到结果队列中保存
inline static uint64_t get_current_nano_sec() {
  timespec ts;
  ::clock_gettime(CLOCK_REALTIME, &ts);
  return ts.tv_sec * 1000000000 + ts.tv_nsec;
}
struct bar {
  uint64_t start_time;
  uint64_t end_time;

  double high{0};
  double low{0};
  double open{0};
  double close{0};

  int tick_cnt{0};

  std::string to_string() {
    return fmt::format("ts:{} H:{:.6f} L:{:.6f} O:{:.6f} C{:.6f}", start_time,
                       high, low, open, close);
  }
};

struct boom_result {
  uint64_t boom_start_ts;
  double max_range_bps{0};
  double regress_bps{0};
  bool is_stop_loss{false};
};

#define BPS 10000.0
class period_range_monitor {
public:
  void init(std::string ins, int boom_period, int regress_period,
            double boom_bps, double stop_loss_bps) {
    ins_ = ins;
    boom_period_ = boom_period * 1e9;
    regress_period_ = regress_period * 1e9;
    boom_bps_ = boom_bps;
    stop_loss_bps_ = stop_loss_bps;
    auto cur_ts = get_current_nano_sec();
    current_bar_.start_time = cur_ts - cur_ts % boom_period_ + boom_period_;
    current_bar_.end_time = current_bar_.start_time + boom_period_;
  }
  void feed_mp(double price, uint64_t recv_time) {
    if (current_bar_.start_time <= recv_time &&
        current_bar_.end_time >= recv_time) {
      if (current_bar_.tick_cnt == 0) {
        current_bar_.open = price;
        current_bar_.close = price;
        current_bar_.high = price;
        current_bar_.low = price;
      }
      current_bar_.tick_cnt++;
      current_bar_.high = std::max(current_bar_.high, price);
      current_bar_.low = std::min(current_bar_.low, price);
      current_bar_.close = price;
    }
    if (current_bar_.end_time <= recv_time) {
      bar_queue_.push_back(current_bar_);
      process_bar(recv_time);
      current_bar_.start_time = current_bar_.end_time;
      // 休盘
      while (current_bar_.start_time + boom_period_ < recv_time) {
        current_bar_.start_time += boom_period_;
      }
      current_bar_.end_time = current_bar_.start_time + boom_period_;
      if (current_bar_.start_time <= recv_time) {
        current_bar_.tick_cnt = 1;
        current_bar_.open = price;
        current_bar_.close = price;
        current_bar_.high = price;
        current_bar_.low = price;
      } else {
        current_bar_.tick_cnt = 0;
        current_bar_.open = 0;
        current_bar_.close = 0;
        current_bar_.high = 0;
        current_bar_.low = 0;
      }
    }
  }

  std::deque<boom_result> &get_boom_result() { return boom_queue_; }

private:
  void process_bar(uint64_t recv_time) {
    while (!bar_queue_.empty() &&
           recv_time > bar_queue_.front().start_time + regress_period_) {
      auto &first_bar = bar_queue_.front();
      double max_range =
          (first_bar.high - first_bar.low) * BPS / first_bar.open;
      if (max_range > boom_bps_) {
        double regress_high = first_bar.close;
        double regress_low = first_bar.close;
        fmt::print("ins:{} first_bar:{} bar_queue_size:{}\n", ins_,
                   first_bar.to_string(), bar_queue_.size());
        for (int i = 1; i < bar_queue_.size(); i++) {
          if (bar_queue_[i].high > regress_high) {
            regress_high = bar_queue_[i].high;
          }
          if (bar_queue_[i].low < regress_low) {
            regress_low = bar_queue_[i].low;
          }
          fmt::print("ins:{} merge idx:{} bar:{}\n", ins_, i,
                     bar_queue_[i].to_string());
        }
        fmt::print("ins:{} merge regress_low:{} regress_high:{}\n", ins_,
                   regress_low, regress_high);
        boom_result res;
        res.boom_start_ts = first_bar.start_time;
        res.max_range_bps = max_range;
        if (first_bar.open > first_bar.close) {
          double stop_loss_price =
              first_bar.open - (stop_loss_bps_ + 80) * first_bar.open / BPS;
          if (regress_low < stop_loss_price) {
            res.is_stop_loss = true;
          }
          res.regress_bps =
              (regress_high - first_bar.low) * BPS / first_bar.open;
        } else {
          double stop_loss_price =
              first_bar.open + (stop_loss_bps_ + 80) * first_bar.open / BPS;
          if (regress_high > stop_loss_price) {
            res.is_stop_loss = true;
          }
          res.regress_bps =
              (first_bar.high - regress_low) * BPS / first_bar.open;
        }
        boom_queue_.push_back(res);
      }
      bar_queue_.pop_front();
    }
    while (!boom_queue_.empty() &&
           recv_time - boom_queue_.front().boom_start_ts > 49 * 60 * 60 * 1e9) {
      boom_queue_.pop_front();
    }
  }

private:
  std::string ins_;
  uint64_t boom_period_;
  uint64_t regress_period_;
  double boom_bps_;
  double stop_loss_bps_;

  bar current_bar_;
  std::deque<bar> bar_queue_;
  std::deque<boom_result> boom_queue_;
};