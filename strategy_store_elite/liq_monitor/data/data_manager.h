#pragma once
#include "../../frame/strategy_logger.h"
#include "cpp_frame/struct_serialize/ser_reflection.h"
#include "fast_trader_elite/data_model/type.h"
#include "i_strategy_ctx.h"
#include "nlohmann_json/json.hpp"
#include <cstdint>
#include <fstream>
#include <vector>

#define BPS 10000
namespace fast_trader_elite::strategy {

class data {
public:
  data(fast_trader_elite::strategy::logger &logger) : logger_(logger) {}
  fast_trader_elite::exchange_type exchange_id{exchange_type::BYBIT};
  std::string instrument;
  uint16_t instrument_idx;
  double long_liq{0};
  double short_liq{0};
  uint16_t liq_win;
  uint16_t trade_win;
  double mean_vol{0};
  double cur_vol{0};
  double oi_usdt{0};
  double turnover_24h{0};
  double buy_trade_speed{0};
  double sell_trade_speed{0};
  double buy_trade_volume{0};
  double sell_trade_volume{0};
  double mp{0};
  std::vector<int> bar_pin_counts;
  std::vector<int> stoploss_counts;
  std::vector<std::pair<double, double>> pin_range_bps;
  fast_trader_elite::strategy::logger &logger_;
};

DEFINE_SER_DATA(data, instrument, instrument_idx, long_liq, short_liq)

class data_manager {
public:
  data_manager(fast_trader_elite::strategy::logger &logger) : logger_(logger) {
    datas.resize(1000);
    std::fill(datas.begin(), datas.end(), nullptr);
  }
  ~data_manager() {
    for (auto &data : datas) {
      if (data) {
        delete data;
      }
    }
  }
  void init_json(nlohmann::json json) { config_json_ = json; }
  void parse_config(i_strategy_ctx *ctx) {
    auto all_ins = ctx->get_all_instruments(exchange_type::BYBIT);
    for (auto ins : all_ins) {
      auto cur_data = new data(logger_);
      cur_data->instrument = ins->instrument_name;
      cur_data->instrument_idx = ins->instrument_idx;
      cur_data->liq_win = liq_win;
      cur_data->trade_win = trade_win;
      datas[ins->instrument_idx] = cur_data;
      STRA_LOG(logger_, STRA_INFO, "config:{}",
               cpp_frame::struct_serialize::to_json(*cur_data));
    }
    ins_cnt = all_ins.size();
  }

public:
  uint16_t liq_win{10};
  uint16_t trade_win{10};
  uint16_t ins_cnt{0};
  std::vector<data *> datas;
  fast_trader_elite::strategy::logger &logger_;

private:
  nlohmann::json config_json_;
};
} // namespace fast_trader_elite::strategy