CMAKE_MINIMUM_REQUIRED(VERSION 3.7)
PROJECT(liq_monitor)
set(CMAKE_CXX_FLAGS "${CMAKE_CXX_FLAGS} -std=c++20 -O3 -g -Wall -msse4 -fPIC")
set(CMAKE_EXPORT_COMPILE_COMMANDS ON) 
add_definitions(-DFMT_HEADER_ONLY)

file(GLOB_RECURSE srcs CONFIGURE_DEPENDS strategy/*.h strategy/*.cc data/*.h rule/*.h rule/*.cc main/*.h main/*.cc)


add_library(liq_monitor SHARED ${srcs})
target_include_directories(liq_monitor PUBLIC
    ../../cpp_frame/include
    ../../fast_trader_elite/data_model/include
    ../../fast_trader_elite/api
    ../../cpp_frame/thirdparty/fmt/include
    ../../cpp_frame/thirdparty/)
target_link_libraries(liq_monitor pthread dl stdc++fs rt fmt dpp)