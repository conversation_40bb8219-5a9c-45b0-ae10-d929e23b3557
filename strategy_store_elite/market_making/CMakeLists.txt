CMAKE_MINIMUM_REQUIRED(VERSION 3.7)
PROJECT(market_making)
set(CMAKE_CXX_FLAGS "${CMAKE_CXX_FLAGS} -std=c++20 -O3 -g -Wall -msse4 -fPIC")
set(CMAKE_EXPORT_COMPILE_COMMANDS ON)
add_definitions(-DFMT_HEADER_ONLY)

file(GLOB_RECURSE srcs CONFIGURE_DEPENDS strategy/*.h strategy/*.cc data/*.h rule/*.h rule/*.cc main/*.h main/*.cc)

add_library(market_making SHARED ${srcs})
target_include_directories(market_making PUBLIC
    ../../cpp_frame/include
    ../../fast_trader_elite/data_model/include
    ../../fast_trader_elite/api
    ../../fast_trader_elite/cpp_backtest/include  # 添加回测头文件路径
    ../../cpp_frame/thirdparty/fmt/include
    ../../cpp_frame/thirdparty/)
target_compile_definitions(market_making PRIVATE BACKTEST_MODE)
target_link_libraries(market_making pthread dl stdc++fs rt fmt dpp)
