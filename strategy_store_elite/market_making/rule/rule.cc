#include "rule.h"

#include <algorithm>
#include <cmath>
#include <cstdint>

#include "../../frame/math_helper.h"
#include "../../frame/order_helper.h"
#include "../../frame/strategy_logger.h"
#include "cpp_frame/struct_serialize/struct_ser.h"
#include "fast_trader_elite/data_model/field.h"
#include "fast_trader_elite/data_model/type.h"
#include <fmt/core.h>

namespace fast_trader_elite::strategy {
rule::rule(data *data, fast_trader_elite::strategy::logger &logger,
           i_strategy_ctx *ctx)
    : rule_impl(data, logger, ctx), data_(data), logger_(logger) {}
bool rule::on_start(i_strategy_ctx *ctx) {
  order_helper::cancel_order_all(ctx_, data_->exchange_id,
                                 data_->instrument_idx,
                                 data_->trading_account_id);
  return true;
}
bool rule::on_stop(i_strategy_ctx *ctx) {
  cancel_and_stopall();
  return true;
}
void rule::on_depth_data(i_strategy_ctx *ctx, depth_market_data_field *md) {
  // STRA_LOG(logger_, STRA_DEBUG, "recv_md md:{}",
  //          cpp_frame::struct_serialize::to_json(*md));
  uint64_t recv_time = md->local_timestamp;
  last_md_update_ts_ = recv_time;
  mp_ = (best_ask_ + best_bid_) / 2;
  if (init_trans_flag == false || init_kline_flag == false ||
      trans_init_ == false) {
    return;
  }
  feed_md(md);
  if (volatility_ < 0) {
    return;
  }
  check_order_cancel_status(recv_time, entry_orders_, "entry");
  check_order_cancel_status(recv_time, add_orders_, "add");
  check_order_cancel_status(recv_time, exit_orders_, "exit");
  check_stop_loss(recv_time);
  auto net_pos = pnl_.get_net_pos(data_->portfolio_id, data_->instrument_idx);
  bool in_cool_down =
      (stoploss_trade_ts_ + data_->stop_loss_cooldown * 1e9 > recv_time);
  if (math_helper::eq(net_pos, 0)) {
    if (!in_cool_down) {
      check_cancel_entry_orders(recv_time);
    }
  } else {
    check_cancel_add_orders(recv_time);
    check_cancel_exit_orders(recv_time);
    // 应对一些特殊情况，比如初始化有持仓 或者 由于意外有仓没平掉
    if (stoploss_orders_.size() == 0) {
      if (exit_orders_.size() == 0) {
        STRA_LOG(logger_, STRA_DEBUG, "exit_orders_size is 0");
        do_exit(recv_time);
      }
      if (add_orders_.size() == 0) {
        STRA_LOG(logger_, STRA_DEBUG, "add_orders_size is 0");
        do_add(recv_time);
      }
    }
  }
}

void rule::on_transaction_data(i_strategy_ctx *ctx, transaction_field *field) {
  // STRA_LOG(logger_, STRA_DEBUG, "recv_trans transaction_field:{}",
  //          cpp_frame::struct_serialize::to_json(*field));
  if (init_trans_flag && init_kline_flag) {
    feed_trans(field);
  }
}

void rule::on_order(i_strategy_ctx *ctx, order_field *f) {
  STRA_LOG(logger_, STRA_DEBUG, "recv_order order_field:{}",
           cpp_frame::struct_serialize::to_json(*f));

  if (f->order_status != order_status_type::ERROR_CANCEL &&
      f->order_status != order_status_type::ERROR_INSERT) {
    recancel_cnt_ = 0;
  }
  if (f->order_status == order_status_type::ERROR_CANCEL) {
    STRA_LOG(logger_, STRA_DEBUG, "cancel_error order_id:{}", f->order_id);
  }
  if (f->order_status == order_status_type::CANCELLED) {
    remove_order_if_exists(f->order_id, "canceled");
  } else if (f->order_status == order_status_type::PARTIAL_FILLED_NOT_ACTIVE) {
    remove_order_if_exists(f->order_id, "part_trade_and_canceled");
  } else if (f->order_status == order_status_type::ERROR_INSERT) {
    STRA_LOG(logger_, STRA_DEBUG, "insert_error order_id:{}", f->order_id);
    remove_order_if_exists(f->order_id, "insert_error");
  }
}
void rule::on_trade(i_strategy_ctx *ctx, trade_field *f) {
  uint64_t recv_time = f->local_timestamp;
  STRA_LOG(logger_, STRA_DEBUG, "recv_trade trade:{}",
           cpp_frame::struct_serialize::to_json(*f));
  if (f->is_manural) {
    STRA_LOG(logger_, STRA_DEBUG, "recv_manural_trade trade_id:{}",
             f->trade_id);
  }
  feed_trade(f, recv_time);
  if (f->order_status == order_status_type::PARTIAL_FILLED_ACTIVE) {
    // 处理部分成交的订单
    // auto itr_entry = entry_orders_.find(f->order_id);
    // if (itr_entry != entry_orders_.end()) {
    //   cancel_entry_orders(recv_time);
    //   do_exit(recv_time);
    //   do_add(recv_time);
    //   entry_orders_.erase(itr_entry);
    // }
    auto itr_exit = exit_orders_.find(f->order_id);
    if (itr_exit != exit_orders_.end()) {
      exit_order_trade_ts_ = recv_time;
      STRA_LOG(logger_, STRA_DEBUG,
               "exit_order_part_trade trade_id:{} order_id:{}", f->trade_id,
               f->order_id);
    }
  } else if (f->order_status == order_status_type::FILLED) {
    // todo: check cancel cnt == 0，如果不是0
    // 证明在撤单过程中被成交了，可能需要直接cancel close all，有可能导致反手
    process_filled_order(f->order_id, recv_time, f, entry_orders_,
                         "entry_order_trade",
                         [this](const order &order, uint64_t time) {
                           cancel_entry_orders(time);
                           do_exit(time);
                           do_add(time);
                         });
    process_filled_order(
        f->order_id, recv_time, f, exit_orders_, "exit_order_trade",
        [this](const order &o, uint64_t time) {
          exit_order_trade_ts_ = time;
          double net_pos =
              pnl_.get_net_pos(data_->portfolio_id, data_->instrument_idx);
          if (o.cancel_cnt != 0) {
            STRA_LOG(logger_, STRA_DEBUG, "exit_cancel_order_trade order_id:{}",
                     o.id);
            cancel_exit_orders(time);
            do_exit(time);
            if (o.direction == direction_type::BUY && net_pos > 0) {
              cancel_and_stopall();
            }
            if (o.direction == direction_type::SELL && net_pos < 0) {
              cancel_and_stopall();
            }
          }
          if (math_helper::eq(net_pos, 0)) {
            cancel_add_orders(time);
            add_cnt_ = 0;
          } else {
            cancel_add_orders(time);
            do_add(time);
          }
        });

    process_filled_order(f->order_id, recv_time, f, add_orders_,
                         "add_order_trade",
                         [this](const order &order, uint64_t time) {
                           add_order_trade_ts_ = time;
                           // 撤止盈重挂
                           cancel_exit_orders(time);
                           do_exit(time);
                           add_cnt_++;
                         });
    process_filled_order(f->order_id, recv_time, f, stoploss_orders_,
                         "stop_loss_order_trade",
                         [this](const order &order, uint64_t time) {
                           stoploss_trade_ts_ = time;
                           add_cnt_ = 0;
                         });
  }
}

void rule::on_rtn_position(i_strategy_ctx *ctx, position_field *pos) {
  STRA_LOG(logger_, STRA_DEBUG, "init_pos:{}",
           cpp_frame::struct_serialize::to_json(*pos));
  if (math_helper::eq(pos->net_position)) {
    return;
  }
  trade_field f;
  f.instrument_idx = pos->instrument_idx;
  f.volume = std::abs(pos->net_position);
  f.last_price = pos->avg_price;
  f.direction =
      pos->net_position < 0 ? direction_type::SELL : direction_type::BUY;
  pnl_.add_trade(&f, data_->portfolio_id);
}
void rule::on_rtn_wallet_balance(i_strategy_ctx *ctx,
                                 wallet_balance_field *field) {
  STRA_LOG(logger_, STRA_DEBUG, "{}",
           cpp_frame::struct_serialize::to_json(*field));
  // init_wallet_balance_ = field->wallet_balance;
  init_wallet_balance_ = data_->init_usdt;
  quote_base_usdt_ = init_wallet_balance_ * data_->leverage *
                     data_->entry_qty_pct * data_->balance_pct;
  STRA_LOG(logger_, STRA_DEBUG, "quote_base_usdt:{}", quote_base_usdt_);
}
void rule::on_http_kline_data(i_strategy_ctx *ctx,
                              kline_market_data_field *field, bool is_last) {
  if (!is_last) {
    feed_kline(field);
  } else {
    init_kline_flag = true;
    STRA_LOG(logger_, STRA_DEBUG, "init_kline_end");
  }
}
void rule::on_http_transaction_data(i_strategy_ctx *ctx,
                                    transaction_field *field, bool is_last) {
  if (!is_last) {
    feed_http_trans(field);
  } else {
    init_trans_flag = true;
    STRA_LOG(logger_, STRA_DEBUG, "init_trans_end");
  }
}

void rule::do_entry(uint64_t recv_time) {
  entry_order_insert_ts_ = recv_time;
  entry_mp_ = mp_;
  calc_initial_bid_ask(recv_time);
  bool can_long = true;
  bool can_short = true;
  double ema_diff = (ema_price_ - mp_) * BPS / mp_;
  // ema偏离过大赌反转而不是顺势
  if (ema_diff > 60) {
    can_short = false;
  }
  if (ema_diff < -60) {
    can_long = false;
  }
  int64_t bid_id = 0, ask_id = 0;
  double base_quote =
      math_helper::round_down(quote_base_usdt_ / mp_, data_->volume_step_size);
  if (can_long) {
    bid_id = order_helper::insert_limit_order(
        ctx_, data_->exchange_id, data_->instrument_idx,
        data_->trading_account_id, data_->portfolio_id, calc_bid_price_,
        base_quote, fast_trader_elite::direction_type::BUY,
        fast_trader_elite::offset_type::NON);
    order bid_o(bid_id, calc_bid_price_, base_quote, 0, mp_,
                fast_trader_elite::direction_type::BUY, recv_time);
    entry_orders_[bid_id] = bid_o;
  }
  if (can_short) {
    ask_id = order_helper::insert_limit_order(
        ctx_, data_->exchange_id, data_->instrument_idx,
        data_->trading_account_id, data_->portfolio_id, calc_ask_price_,
        base_quote, fast_trader_elite::direction_type::SELL,
        fast_trader_elite::offset_type::NON);
    order sell_o(ask_id, calc_ask_price_, base_quote, 0, mp_,
                 fast_trader_elite::direction_type::SELL, recv_time);
    entry_orders_[ask_id] = sell_o;
  }
  STRA_LOG(logger_, STRA_DEBUG,
           "fp:{:.6f} bid_id:{} "
           "bid:{:.6f}@{:.6f} b_sp:{:.2f} ask_id:{} ask:{:.6f}@{:.6f} "
           "a_sp:{:.2f}",
           fair_price_, bid_id, calc_bid_price_, base_quote,
           (mp_ - calc_bid_price_) * BPS / mp_, ask_id, calc_ask_price_,
           base_quote, (calc_ask_price_ - mp_) * BPS / mp_);
}

void rule::do_add(uint64_t recv_time) {
  double max_pos = init_wallet_balance_ * data_->leverage * 0.92 / mp_;
  double curr_used_pos =
      pnl_.get_net_pos(data_->portfolio_id, data_->instrument_idx);
  double avg_p = pnl_.get_avg_price();
  double pos_price = avg_p;
  double pos_margin = calc_pos_margin_cost(curr_used_pos, pos_price);
  double add_cnt_plus = (1 + add_cnt_ * add_cnt_) * 0.004;
  double level_plus = 0.005;
  if (math_helper::lt(curr_used_pos, 0)) {
    double ask_p =
        std::max(best_ask_, calc_short_reentry_price(pos_margin, pos_price));
    ask_p = ask_p * (1 + add_cnt_plus);
    for (int i = 0; i < data_->n_entry_orders; i++) {
      double ask_v = calc_reentry_qty(curr_used_pos, pos_price);
      if (ask_p * ask_v < 6 && i > 0) {
        break;
      }
      double new_pos_size = std::abs(curr_used_pos) + ask_v;
      if (new_pos_size >= max_pos && i > 0) {
        break;
      }
      if ((ask_p - mp_) / mp_ > 0.1 && i > 0) {
        break;
      }
      double ask_id = order_helper::insert_limit_order(
          ctx_, data_->exchange_id, data_->instrument_idx,
          data_->trading_account_id, data_->portfolio_id, ask_p, ask_v,
          fast_trader_elite::direction_type::SELL,
          fast_trader_elite::offset_type::NON);
      order ask_o(ask_id, ask_p, ask_v, 0, mp_,
                  fast_trader_elite::direction_type::SELL, recv_time);
      add_orders_[ask_id] = ask_o;
      STRA_LOG(logger_, STRA_DEBUG,
               "add_ask_order level:{} ask_id:{} ask:{:.6f}@{:.6f} diff:{:.2f} "
               "add_cnt_plus:{} add_cnt:{}",
               i, ask_id, ask_p, ask_v, (ask_p - avg_p) * BPS / avg_p,
               add_cnt_plus, add_cnt_);
      // 计算补单以后的均价 继续往上挪 跟py有出入需要确认
      pos_price =
          (pos_price * std::abs(curr_used_pos) + ask_p * ask_v) / new_pos_size;
      curr_used_pos = new_pos_size; // 占用上升
      pos_margin = calc_pos_margin_cost(curr_used_pos, pos_price);
      ask_p =
          std::max(best_ask_, calc_short_reentry_price(pos_margin, pos_price));
      ask_p = ask_p * (1 + add_cnt_plus) * (1 + (i + 1) * level_plus);
    }
  } else {
    double bid_p =
        std::min(best_bid_, calc_long_reentry_price(pos_margin, pos_price));
    bid_p = bid_p * (1 - add_cnt_plus);
    for (int i = 0; i < data_->n_entry_orders; i++) {
      double bid_v = calc_reentry_qty(curr_used_pos, pos_price);
      if (bid_p * bid_v < 6 && i > 0) {
        break;
      }
      double new_pos_size = curr_used_pos + bid_v;
      if (new_pos_size >= max_pos && i > 0) {
        break;
      }

      if ((mp_ - bid_p) / mp_ > 0.1 && i > 0) {
        break;
      }
      double bid_id = order_helper::insert_limit_order(
          ctx_, data_->exchange_id, data_->instrument_idx,
          data_->trading_account_id, data_->portfolio_id, bid_p, bid_v,
          fast_trader_elite::direction_type::BUY,
          fast_trader_elite::offset_type::NON);
      order bid_o(bid_id, bid_p, bid_v, 0, mp_,
                  fast_trader_elite::direction_type::BUY, recv_time);
      add_orders_[bid_id] = bid_o;
      STRA_LOG(logger_, STRA_DEBUG,
               "add_bid_order level:{} bid_id:{} bid:{:.6f}@{:.6f} diff:{:.2f} "
               "add_cnt_plus:{} add_cnt:{}",
               i, bid_id, bid_p, bid_v, (avg_p - bid_p) * BPS / avg_p,
               add_cnt_plus, add_cnt_);
      // 计算补单以后的均价 继续往上挪 跟py有出入需要确认
      pos_price = (pos_price * curr_used_pos + bid_p * bid_v) / new_pos_size;
      curr_used_pos = new_pos_size; // 占用上升

      pos_margin = calc_pos_margin_cost(curr_used_pos, pos_price);
      bid_p =
          std::min(best_bid_, calc_long_reentry_price(pos_margin, pos_price));
      bid_p = bid_p * (1 - add_cnt_plus) * (1 - (i + 1) * level_plus);
    }
  }
}

void rule::do_exit(uint64_t recv_time) {
  exit_order_trade_ts_ = 0;
  double min_close_qty = std::max(calc_min_close_qty(), 6 / mp_);
  double stop_loss_qty = 0;
  double net_pos = pnl_.get_net_pos(data_->portfolio_id, data_->instrument_idx);
  STRA_LOG(logger_, STRA_DEBUG, "do_exit net_pos:{}", net_pos);
  if (math_helper::eq(net_pos, 0)) {
    return;
  }
  double pos_price = pnl_.get_avg_price();
  double abs_pos = std::abs(net_pos) - stop_loss_qty;
  int n_orders = static_cast<int>(std::round(
      std::min(data_->n_close_orders * 1.0, abs_pos / min_close_qty)));

  if (n_orders <= 0) {
    n_orders = 1;
  }

  if (math_helper::gt(net_pos, 0)) { // 多头仓位
    std::vector<double> filtered_prices;
    double price_diff = (data_->max_markup - data_->min_markup) /
                        (n_orders - 1 > 0 ? n_orders - 1 : 1);
    bool filtered_flag = false;
    for (int i = 0; i < n_orders; i++) {
      double price = (1 + data_->min_markup + i * price_diff) * pos_price;
      price = math_helper::round_up(price, data_->price_tick_size);
      if (price >= best_trade_ask_) {
        filtered_prices.push_back(price);
      } else {
        if (!filtered_flag) {
          filtered_prices.push_back(best_trade_ask_);
        }
        filtered_flag = true;
      }
    }
    STRA_LOG(
        logger_, STRA_DEBUG,
        "n_orders:{} filtered_prices:{} best_trade_ask:{:.6f} abs_pos:{:.6f} "
        "min_close_qty:{:.6f}",
        n_orders, fmt::format("{}", filtered_prices), best_trade_ask_, abs_pos,
        min_close_qty);

    if (filtered_prices.empty()) {
      double price =
          std::max(best_trade_ask_,
                   math_helper::round_up(pos_price * (1 + data_->min_markup),
                                         data_->price_tick_size));
      create_exit_order(recv_time, price, abs_pos,
                        fast_trader_elite::direction_type::SELL,
                        "exit_long filtered_prices empty ", pos_price, 0);
      return;
    }

    if (filtered_prices.size() == 1) {
      create_exit_order(recv_time, filtered_prices[0], abs_pos,
                        fast_trader_elite::direction_type::SELL,
                        "exit_long filtered_prices one ", pos_price, 0);
      return;
    }
    double price_diff_ratio =
        std::abs(filtered_prices[1] - filtered_prices[0]) / filtered_prices[0];
    if (price_diff_ratio > 0.005) { // 使用与 Python 代码相同的阈值
      double price = std::max(
          best_ask_, math_helper::round_up(pos_price * (1 + data_->min_markup),
                                           data_->price_tick_size));
      create_exit_order(
          recv_time, price, abs_pos, fast_trader_elite::direction_type::SELL,
          "exit_long single order (price diff too large) ", pos_price, 0);
      return;
    }
    int n_filtered_orders = filtered_prices.size();
    std::vector<double> quantities(n_filtered_orders,
                                   abs_pos / n_filtered_orders);
    adjust_quantities(quantities, abs_pos, false);
    for (int i = 0; i < n_filtered_orders; i++) {
      double ask_p = filtered_prices[i];
      double ask_v = quantities[i];
      if (math_helper::le(ask_v, 0)) {
        continue;
      }
      create_exit_order(recv_time, ask_p, ask_v,
                        fast_trader_elite::direction_type::SELL, "exit_long ",
                        pos_price, i);
    }
  } else { // 空头仓位
    std::vector<double> filtered_prices;
    double price_diff = (data_->max_markup - data_->min_markup) /
                        (n_orders - 1 > 0 ? n_orders - 1 : 1);
    for (int i = 0; i < n_orders; i++) {
      double price = (1 - (data_->min_markup + i * price_diff)) * pos_price;
      price = math_helper::round_down(price, data_->price_tick_size);
      bool filtered_flag = false;
      if (price <= best_trade_bid_) {
        filtered_prices.push_back(price);
      } else {
        if (!filtered_flag) {
          filtered_prices.push_back(best_trade_bid_);
        }
        filtered_flag = true;
      }
    }
    STRA_LOG(
        logger_, STRA_DEBUG,
        "n_orders:{} filtered_prices:{} best_trade_bid:{:.6f} abs_pos:{:.6f} "
        "min_close_qty:{:.6f}",
        n_orders, fmt::format("{}", filtered_prices), best_trade_bid_, abs_pos,
        min_close_qty);
    if (filtered_prices.empty()) {
      double price =
          std::min(best_trade_bid_,
                   math_helper::round_down(pos_price * (1 - data_->min_markup),
                                           data_->price_tick_size));
      create_exit_order(recv_time, price, abs_pos,
                        fast_trader_elite::direction_type::BUY,
                        "exit_short filtered_prices empty ", pos_price, 0);
      return;
    }

    if (filtered_prices.size() == 1) {
      create_exit_order(recv_time, filtered_prices[0], abs_pos,
                        fast_trader_elite::direction_type::BUY,
                        "exit_short filtered_prices one ", pos_price, 0);
      return;
    }

    double price_diff_ratio =
        std::abs(filtered_prices[0] - filtered_prices[1]) / filtered_prices[1];
    if (price_diff_ratio > 0.005) { // 使用与 Python 代码相同的阈值
      double price =
          std::min(best_bid_,
                   math_helper::round_down(pos_price * (1 - data_->min_markup),
                                           data_->price_tick_size));
      create_exit_order(
          recv_time, price, abs_pos, fast_trader_elite::direction_type::BUY,
          "exit_short single order (price diff too large) ", pos_price, 0);
      return;
    }
    int n_filtered_orders = filtered_prices.size();
    std::vector<double> quantities(n_filtered_orders,
                                   abs_pos / n_filtered_orders);
    adjust_quantities(quantities, abs_pos, true);
    for (int i = 0; i < n_filtered_orders; i++) {
      double bid_p = filtered_prices[i];
      double bid_v = quantities[i];
      if (math_helper::le(bid_v, 0)) {
        continue;
      }
      create_exit_order(recv_time, bid_p, bid_v,
                        fast_trader_elite::direction_type::BUY, "exit_short ",
                        pos_price, i);
    }
  }
}
void rule::create_exit_order(uint64_t recv_time, double price, double qty,
                             fast_trader_elite::direction_type direction,
                             const std::string &log_prefix,
                             double pos_avg_price, int level) {
  int64_t order_id = order_helper::insert_limit_order(
      ctx_, data_->exchange_id, data_->instrument_idx,
      data_->trading_account_id, data_->portfolio_id, price, qty, direction,
      fast_trader_elite::offset_type::NON);

  order order_obj(order_id, price, qty, 0, mp_, direction, recv_time);
  exit_orders_[order_id] = order_obj;

  if (direction == fast_trader_elite::direction_type::SELL) {
    STRA_LOG(logger_, STRA_DEBUG,
             "{}level:{} ask_id:{} ask:{:.6f}@{:.6f} diff:{:.2f} bid_p1:{:.6f} "
             "ask_p1:{:.6f}",
             log_prefix, level, order_id, price, qty,
             (price - pos_avg_price) * BPS / pos_avg_price, best_bid_,
             best_ask_);
  } else {
    STRA_LOG(logger_, STRA_DEBUG,
             "{}level:{} bid_id:{} bid:{:.6f}@{:.6f} diff:{:.2f} bid_p1:{:.6f} "
             "ask_p1:{:.6f}",
             log_prefix, level, order_id, price, qty,
             (pos_avg_price - price) * BPS / pos_avg_price, best_bid_,
             best_ask_);
  }
}

void rule::adjust_quantities(std::vector<double> &quantities, double abs_pos,
                             bool reverse_order) {
  // 向上取整到数量步长并计算总量
  double sum_qty = 0;
  for (double &qty : quantities) {
    qty = math_helper::round_up(qty, data_->volume_step_size);
    sum_qty += qty;
  }

  // 如果总数量超过仓位大小，调整数量
  if (sum_qty > abs_pos) {
    // 计算需要减少的总量
    double excess =
        math_helper::round_up(sum_qty - abs_pos, data_->volume_step_size);
    // 最小步长
    double step = data_->volume_step_size;

    if (reverse_order) {
      // 从后往前逐个减少
      for (int i = quantities.size() - 1; i >= 0 && excess > 0; i--) {
        if (quantities[i] > step) {
          double reduce = std::min(excess, quantities[i] - step);
          reduce = math_helper::round_down(reduce, step);
          if (reduce > 0) {
            quantities[i] -= reduce;
            excess -= reduce;
          }
        }
      }
    } else {
      // 从前往后逐个减少
      for (size_t i = 0; i < quantities.size() && excess > 0; i++) {
        if (quantities[i] > step) {
          double reduce = std::min(excess, quantities[i] - step);
          reduce = math_helper::round_down(reduce, step);
          if (reduce > 0) {
            quantities[i] -= reduce;
            excess -= reduce;
          }
        }
      }
    }
  }
}

void rule::do_stop_loss(uint64_t recv_time) {
  double net_pos = pnl_.get_net_pos(data_->portfolio_id, data_->instrument_idx);
  if (stoploss_orders_.size()) {
    return;
  }
  if (math_helper::gt(net_pos, 0)) {
    int64_t ask_id = order_helper::insert_market_order(
        ctx_, data_->exchange_id, data_->instrument_idx,
        data_->trading_account_id, data_->portfolio_id, std::abs(net_pos),
        fast_trader_elite::direction_type::SELL,
        fast_trader_elite::offset_type::NON);
    order ask_o(ask_id, mp_, std::abs(net_pos), 0, mp_,
                fast_trader_elite::direction_type::SELL, recv_time);
    stoploss_orders_[ask_id] = ask_o;
    STRA_LOG(logger_, STRA_DEBUG, "stop_loss ask_id:{} ask:{:.6f}@{:.6f}",
             ask_id, mp_, std::abs(net_pos));
  } else {
    int64_t bid_id = order_helper::insert_market_order(
        ctx_, data_->exchange_id, data_->instrument_idx,
        data_->trading_account_id, data_->portfolio_id, std::abs(net_pos),
        fast_trader_elite::direction_type::BUY,
        fast_trader_elite::offset_type::NON);
    order bid_o(bid_id, mp_, std::abs(net_pos), 0, mp_,
                fast_trader_elite::direction_type::BUY, recv_time);
    stoploss_orders_[bid_id] = bid_o;
    STRA_LOG(logger_, STRA_DEBUG, "stop_loss bid_id:{} bid:{:.6f}@{:.6f}",
             bid_id, mp_, std::abs(net_pos));
  }
}

void rule::check_cancel_entry_orders(uint64_t recv_time) {
  double diff_p = std::abs(mp_ - entry_mp_) * BPS / mp_;
  bool ts_flag =
      recv_time > entry_order_insert_ts_ + data_->refresh_window * 1e9;
  bool price_flag = diff_p > 5;
  if (ts_flag && price_flag) {
    cancel_entry_orders(recv_time);
    do_entry(recv_time);
  }
}

void rule::cancel_entry_orders(uint64_t recv_time) {
  std::vector<int64_t> need_cancel_ids;
  for (auto &o : entry_orders_) {
    if (o.second.cancel_cnt == 0) {
      need_cancel_ids.push_back(o.first);
    }
  }
  if (need_cancel_ids.size()) {
    cancel_list(recv_time, need_cancel_ids, entry_orders_);
    std::string need_cancel_id_str = fmt::format("{}", need_cancel_ids);
    STRA_LOG(logger_, STRA_DEBUG, "cancel_entry_orders cancel_ids:{}",
             need_cancel_id_str);
  }
}
void rule::cancel_exit_orders(uint64_t recv_time) {
  std::vector<int64_t> need_cancel_ids;
  for (auto &o : exit_orders_) {
    if (o.second.cancel_cnt == 0) {
      need_cancel_ids.push_back(o.first);
    }
  }
  if (need_cancel_ids.size()) {
    cancel_list(recv_time, need_cancel_ids, exit_orders_);
    std::string need_cancel_id_str = fmt::format("{}", need_cancel_ids);
    STRA_LOG(logger_, STRA_DEBUG, "cancel_exit_orders cancel_ids:{}",
             need_cancel_id_str);
  }
}
void rule::cancel_add_orders(uint64_t recv_time) {
  std::vector<int64_t> need_cancel_ids;
  for (auto &o : add_orders_) {
    if (o.second.cancel_cnt == 0) {
      need_cancel_ids.push_back(o.first);
    }
  }
  if (need_cancel_ids.size()) {
    cancel_list(recv_time, need_cancel_ids, add_orders_);
    std::string need_cancel_id_str = fmt::format("{}", need_cancel_ids);
    STRA_LOG(logger_, STRA_DEBUG, "cancel_add_orders cancel_ids:{}",
             need_cancel_id_str);
  }
}

void rule::check_cancel_add_orders(uint64_t recv_time) {
  if (add_order_trade_ts_ != 0 && recv_time > add_order_trade_ts_ + 1e9) {
    add_order_trade_ts_ = 0;
    std::vector<int64_t> need_cancel_ids;
    for (auto &o : add_orders_) {
      need_cancel_ids.push_back(o.first);
    }
    if (need_cancel_ids.size()) {
      cancel_list(recv_time, need_cancel_ids, add_orders_);
      std::string need_cancel_id_str = fmt::format("{}", need_cancel_ids);
      STRA_LOG(logger_, STRA_DEBUG, "cancel_add_orders cancel_ids:{}",
               need_cancel_id_str);
    }
    // 两个方案：直接补不等撤单回报，等撤单回报以后再补
    do_add(recv_time);
  }
}

void rule::check_cancel_exit_orders(uint64_t recv_time) {
  if (exit_order_trade_ts_ != 0 && recv_time > exit_order_trade_ts_ + 3 * 1e9) {
    exit_order_trade_ts_ = 0;
    std::vector<int64_t> need_cancel_ids;
    for (auto &o : exit_orders_) {
      if (o.second.cancel_cnt == 0) {
        need_cancel_ids.push_back(o.first);
      }
    }
    if (need_cancel_ids.size()) {
      cancel_list(recv_time, need_cancel_ids, exit_orders_);
      std::string need_cancel_id_str = fmt::format("{}", need_cancel_ids);
      STRA_LOG(logger_, STRA_DEBUG,
               "check_cancel_exit_orders cancel_exit_orders cancel_ids:{}",
               need_cancel_id_str);
      do_exit(recv_time);
    }
  }
}

void rule::check_stop_loss(uint64_t recv_time) {
  double net_pos = pnl_.get_net_pos(data_->portfolio_id, data_->instrument_idx);
  if (math_helper::eq(net_pos, 0) || stoploss_orders_.size() != 0) {
    return;
  }
  double price = pnl_.get_avg_price();
  double profit_bps =
      net_pos > 0 ? (mp_ - price) * BPS / mp_ : (price - mp_) * BPS / mp_;
  double profit_pct =
      profit_bps * std::abs(net_pos) * mp_ / BPS / init_wallet_balance_;
  if (profit_pct < -data_->stop_loss_danger) {
    STRA_LOG(
        logger_, STRA_DEBUG,
        "net_pos:{:.6f} profit_bps:{:.2f} avg_price:{:.6f} profit_pct:{:.6f}",
        net_pos, profit_bps, price, profit_pct);
    // order_helper::cancel_order_all(ctx_, exchange_type::BYBIT,
    //                                data_->instrument_idx,
    //                                data_->trading_account_id);
    cancel_entry_orders(recv_time);
    cancel_exit_orders(recv_time);
    cancel_add_orders(recv_time);
    do_stop_loss(recv_time);
  }
}
void rule::remove_order_if_exists(int64_t order_id,
                                  const std::string &status_desc) {
  // 检查并删除 entry_orders_ 中的订单
  auto itr_entry = entry_orders_.find(order_id);
  if (itr_entry != entry_orders_.end()) {
    STRA_LOG(logger_, STRA_DEBUG, "entry_order_{} order_id:{}", status_desc,
             order_id);
    entry_orders_.erase(itr_entry);
  }
  auto itr_exit = exit_orders_.find(order_id);
  if (itr_exit != exit_orders_.end()) {
    STRA_LOG(logger_, STRA_DEBUG, "exit_order_{} order_id:{}", status_desc,
             order_id);
    exit_orders_.erase(itr_exit);
  }
  auto itr_add = add_orders_.find(order_id);
  if (itr_add != add_orders_.end()) {
    STRA_LOG(logger_, STRA_DEBUG, "add_order_{} order_id:{}", status_desc,
             order_id);
    add_orders_.erase(itr_add);
  }
  auto itr_stoploss = stoploss_orders_.find(order_id);
  if (itr_stoploss != stoploss_orders_.end()) {
    STRA_LOG(logger_, STRA_DEBUG, "stop_loss_order_{} order_id:{}", status_desc,
             order_id);
    stoploss_orders_.erase(itr_stoploss);
  }
}

void rule::check_order_cancel_status(
    uint64_t recv_time, std::unordered_map<int64_t, order> &order_map,
    const std::string &order_type, std::function<void(uint64_t)> post_action) {
  std::vector<int64_t> need_cancel_ids;
  for (auto it = order_map.begin(); it != order_map.end();) {
    if (it->second.cancel_cnt != 0 && it->second.cancel_cnt < 3 &&
        it->second.close_time < recv_time) {
      // 尝试过取消但次数少于3次且超时，需要重新取消
      need_cancel_ids.push_back(it->first);
      it++;
    } else if (it->second.cancel_cnt == 3 &&
               (it->second.close_time < recv_time)) {
      // 尝试取消次数达到3次且超时，认为取消成功
      recancel_cnt_++;
      STRA_LOG(logger_, STRA_WARN, "{}_order_recancel_timeout order_id:{}",
               order_type, it->first);
      recancel_failed_orders_[it->first] = it->second;
      order_map.erase(it++);
    } else {
      it++;
    }
  }

  if (!need_cancel_ids.empty()) {
    cancel_list(recv_time, need_cancel_ids, order_map);
    std::string need_cancel_id_str = fmt::format("{}", need_cancel_ids);
    STRA_LOG(logger_, STRA_DEBUG, "cancel_{}_orders cancel_ids:{}", order_type,
             need_cancel_id_str);

    if (post_action) {
      post_action(recv_time);
    }
  }
}

bool rule::process_filled_order(
    int64_t order_id, uint64_t recv_time, const trade_field *f,
    std::unordered_map<int64_t, order> &order_map,
    const std::string &order_type,
    std::function<void(const order &, uint64_t)> post_action) {
  auto itr = order_map.find(order_id);
  if (itr != order_map.end()) {
    if (itr->second.cancel_cnt != 0) {
      STRA_LOG(logger_, STRA_DEBUG,
               "cancel_order_trade order_type:{} "
               "order_id:{}",
               order_type, order_id);
    }
    STRA_LOG(logger_, STRA_DEBUG,
             "{} trade_id:{} order_id:{} ts:{} dir:{} {}@{}", order_type,
             f->trade_id, f->order_id,
             (recv_time - itr->second.insert_ts) / 1e6,
             static_cast<int>(itr->second.direction), f->last_price, f->volume);
    order o = itr->second;
    order_map.erase(itr);
    if (post_action) {
      post_action(o, recv_time);
    }
    return true;
  }
  return false;
}

} // namespace fast_trader_elite::strategy