#pragma once
#include <cstdint>
#include <unordered_map>

#include "../data/data_manager.h"
#include "fast_trader_elite/data_model/field.h"
#include "i_strategy_ctx.h"
#include "rule_impl.h"
#include <fmt/ranges.h>
namespace fast_trader_elite::strategy {

class rule : public rule_impl {
public:
  rule(data *data, fast_trader_elite::strategy::logger &logger,
       i_strategy_ctx *ctx);
  bool on_start(i_strategy_ctx *ctx);
  bool on_stop(i_strategy_ctx *ctx);
  void on_kline_data(i_strategy_ctx *ctx, kline_market_data_field *field) {}
  void on_depth_data(i_strategy_ctx *ctx, depth_market_data_field *field);
  void on_tick_data(i_strategy_ctx *ctx, tick_market_data_field *field) {}
  void on_transaction_data(i_strategy_ctx *ctx, transaction_field *field);
  void on_liquidation_data(i_strategy_ctx *ctx, liquidation_field *field) {}

  void on_http_depth_data(i_strategy_ctx *ctx, depth_market_data_field *field,
                          bool is_last) {}
  void on_http_tick_data(i_strategy_ctx *ctx, tick_market_data_field *field,
                         bool is_last) {}
  void on_http_liquidation_data(i_strategy_ctx *ctx, liquidation_field *field,
                                bool is_last) {}
  void on_http_kline_data(i_strategy_ctx *ctx, kline_market_data_field *field,
                          bool is_last);
  void on_http_transaction_data(i_strategy_ctx *ctx, transaction_field *field,
                                bool is_last);
  void on_order(i_strategy_ctx *ctx, order_field *field);
  void on_trade(i_strategy_ctx *ctx, trade_field *field);
  void on_rtn_position(i_strategy_ctx *ctx, position_field *position);
  void on_rtn_wallet_balance(i_strategy_ctx *ctx,
                             wallet_balance_field *wallet_balance);

private:
  void do_entry(uint64_t recv_time);
  void do_exit(uint64_t recv_time);
  void do_add(uint64_t recv_time);
  void do_stop_loss(uint64_t recv_time);

  // 平仓
  void adjust_quantities(std::vector<double> &quantities, double abs_pos,
                         bool reverse_order = false);
  void create_exit_order(uint64_t recv_time, double price, double qty,
                         fast_trader_elite::direction_type direction,
                         const std::string &log_prefix, double pos_avg_price,
                         int level = -1);

  void cancel_entry_orders(uint64_t recv_time);
  void cancel_exit_orders(uint64_t recv_time);
  void cancel_add_orders(uint64_t recv_time);

  void check_cancel_entry_orders(uint64_t recv_time);
  void check_cancel_add_orders(uint64_t recv_time);
  void check_cancel_exit_orders(uint64_t recv_time);

  void remove_order_if_exists(int64_t order_id, const std::string &status_desc);
  
  void check_order_cancel_status(
      uint64_t recv_time, std::unordered_map<int64_t, order> &order_map,
      const std::string &order_type,
      std::function<void(uint64_t)> post_action = nullptr);
  bool process_filled_order(
      int64_t order_id, uint64_t recv_time, const trade_field *f,
      std::unordered_map<int64_t, order> &order_map,
      const std::string &order_type,
      std::function<void(const order &, uint64_t)> post_action = nullptr);

  void check_stop_loss(uint64_t recv_time);

private:
  data *data_{nullptr};
  fast_trader_elite::strategy::logger &logger_;
  bool init_kline_flag{false};
  bool init_trans_flag{false};
  bool init_postion_flag{false};

  double entry_mp_{0};
  uint64_t entry_order_insert_ts_{0};
  uint64_t exit_order_trade_ts_{0};
  uint64_t add_order_trade_ts_{0};
  uint64_t stoploss_trade_ts_{0};

  std::unordered_map<int64_t, order> entry_orders_;
  std::unordered_map<int64_t, order> exit_orders_;
  std::unordered_map<int64_t, order> add_orders_;
  std::unordered_map<int64_t, order> stoploss_orders_;

  std::unordered_map<int64_t, order> recancel_failed_orders_;
  int add_cnt_{0};
};
} // namespace fast_trader_elite::strategy