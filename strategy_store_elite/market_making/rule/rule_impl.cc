#include "rule_impl.h"

#include <cstdint>

#include "../../frame/math_helper.h"
#include "../../frame/order_helper.h"
#include "cpp_frame/struct_serialize/struct_ser.h"
#include "cpp_frame/utils/date.h"

namespace fast_trader_elite::strategy {
rule_impl::rule_impl(data *data, fast_trader_elite::strategy::logger &logger,
                     i_strategy_ctx *ctx)
    : ctx_(ctx), data_(data), factor_calculator_(&share_data_),
      logger_(logger) {
  last_md_update_ts_ = cpp_frame::date::get_current_nano_sec();
  bar_.init_bar(data_->kline_sec, 30,
                [this](const fast_trader_elite::strategy::bar &bar) {
                  calculate_atr(bar);
                  calculate_ewma_vol(bar);
                });
  vol_calculator_.init(180);
  share_data_.init(0, 0);
  tema_price_calculator_.init(data_->tick_ema_span, false, 0);
  ema_price_calculator_.init_with_period(data_->tick_ema_span);
  ewma_vol_calculator_.init_with_period(30);
  pnl_timer_id_ = ctx_->register_timer_with_timestamp(
      5 * 1000,
      [this](int64_t recv_time) {
        calculate_speed();
        auto net_pos =
            pnl_.get_net_pos(data_->portfolio_id, data_->instrument_idx);
        STRA_LOG(logger_, STRA_DEBUG,
                 "pnl_timer ins:{} net_pos:{:.4f} value:{:.2f} pnl:{:.4f} "
                 "sum_traded:{:.4f} mp:{:.6f} tema_p:{:6f} ema_p:{:.6f} "
                 "emp_diff:{:.2f} temap_diff:{:.2f} "
                 "vol:{:.2f} atr_bps:{:.2f} trade_speed:{:.2f}",
                 data_->instrument, net_pos, net_pos * mp_,
                 pnl_.get_pnl(mp_, net_pos), data_->trade_sum, mp_, tema_price_,
                 ema_price_, (ema_price_ - mp_) * BPS / mp_,
                 (tema_price_ - mp_) * BPS / mp_, volatility_ * BPS / mp_,
                 natr_bps_, trade_speed_);
        // uint64_t diff_ts =
        //     cpp_frame::date::get_current_nano_sec() - last_md_update_ts_;
        // if (diff_ts > 60 * 1e9) {
        //   std::cout << "force stop md not update" << std::endl;
        //   force_stop_ = true;
        //   cancel_and_stopall();
        //   STRA_LOG(
        //       logger_, STRA_DEBUG,
        //       "rule_maker::{} force stop md not update last_md_update_ts:{}",
        //       __FUNCTION__, last_md_update_ts_);
        // }
        if (recancel_cnt_ > 10) {
          std::cout << "force stop order not update" << std::endl;
          force_stop_ = true;
          cancel_and_stopall();
          STRA_LOG(logger_, STRA_DEBUG,
                   "rule_maker::{} force stop order not update", __FUNCTION__);
        }
      },

      true);
}
rule_impl::~rule_impl() {}
void rule_impl::feed_kline(kline_market_data_field *kline) {
  bar f;
  f.start_time = kline->start_time * 1000000;
  f.high = kline->high;
  f.open = kline->open;
  f.close = kline->close;
  f.low = kline->low;
  bar_.feed_bar(f, kline->is_close);
  // if (!kline->is_close) {
  //   int s = bar_.get_bar_record().size();
  //   bar last_bar = bar_.get_bar_record()[s - 1];
  // STRA_LOG(logger_, STRA_DEBUG,
  //          "not_close:{} last_bar_close{} start:{} end{}",
  //          cpp_frame::struct_serialize::to_json(*kline), last_bar.close,
  //          last_bar.start_time, last_bar.end_time);
  // }
}
void rule_impl::feed_md(depth_market_data_field *md) {
  // share_data_.feed_md(md);
  best_bid_ = md->bid_price[0];
  best_ask_ = md->ask_price[0];
  calculate_volatility(md->local_timestamp);
  // calculate_factor_by_md(md->local_timestamp);
}
void rule_impl::feed_trans(transaction_field *trans) {
  // share_data_.feed_trans(trans);
  tema_price_ =
      tema_price_calculator_.update(trans->price, trans->exchange_timestamp);
  ema_price_ = ema_price_calculator_.update(trans->price);
  bar_.feed(trans->price, trans->local_timestamp);
  trans_.push_back(*trans);
  if (trans_.size() > 500) {
    trans_init_ = true;
    trans_.pop_front();
  }
  if (trans->is_maker) {
    best_trade_ask_ = trans->price;
  } else {
    best_trade_bid_ = trans->price;
  }

  // EWMA volatility is now calculated in the bar callback

  double buy_v = 0, sell_v = 0;
  for (auto &t : trans_) {
    if (t.is_maker) {
      sell_v += t.volume;
    } else {
      buy_v += t.volume;
    }
  }
  trade_imb_ = (buy_v - sell_v) / (buy_v + sell_v);
  // calculate_factor_by_trans(trans->local_timestamp);
}

void rule_impl::feed_http_trans(transaction_field *trans) {
  tema_price_ =
      tema_price_calculator_.update(trans->price, trans->exchange_timestamp);
  ema_price_ = ema_price_calculator_.update(trans->price);
  trans_.push_back(*trans);
  if (trans_.size() > 500) {
    trans_.pop_front();
  }
  // if (trans->exchange_timestamp * 1e6 > feed_vol_ts_ + 1e8) {
  //   feed_vol_ts_ = trans->exchange_timestamp * 1e6;
  //   vol_calculator_.add_sample(trans->price);
  //   STRA_LOG(logger_, STRA_DEBUG, "feed_vol_ts_:{} trans:{}", feed_vol_ts_,
  //            cpp_frame::struct_serialize::to_json(*trans));
  //   if (vol_calculator_.is_sampling_buffer_full()) {
  //     volatility_ = vol_calculator_.calculate();
  //   }
  // }
  double buy_v = 0, sell_v = 0;
  for (auto &t : trans_) {
    if (t.is_maker) {
      sell_v += t.volume;
    } else {
      buy_v += t.volume;
    }
  }
  trade_imb_ = (buy_v - sell_v) / (buy_v + sell_v);
}

void rule_impl::feed_trade(const trade_field *f, long recv_time) {
  pnl_.add_trade(const_cast<trade_field *>(f), data_->portfolio_id);
  data_->trade_sum += f->last_price * f->volume;
  last_trade_ts_ = recv_time;
}

void rule_impl::calculate_atr(const fast_trader_elite::strategy::bar &bar) {
  auto &record = bar_.get_bar_record();
  if (record.size() < 21) {
    return;
  }
  auto atr = ta_.ATR(record, 20);
  double atr_value = atr[atr.size() - 1];
  natr_bps_ = atr_value * BPS / record[record.size() - 1].close;

  STRA_LOG(logger_, STRA_DEBUG, "calculate_atr atr:{:.6f} natr_bps:{:.2f}",
           atr_value, natr_bps_);
}

void rule_impl::calculate_ewma_vol(
    const fast_trader_elite::strategy::bar &bar) {
  auto &record = bar_.get_bar_record();
  if (record.size() < 2) {
    return;
  }
  double current_close = bar.close;
  double previous_close = record[record.size() - 2].close;
  double log_return = std::log(current_close / previous_close);
  double squared_diff = log_return * log_return;
  double ema_variance = ewma_vol_calculator_.update(squared_diff);
  double sqrt_days = std::sqrt(time_to_days(data_->kline_sec));
  mid_period_ewma_vol_ = std::sqrt(ema_variance) * sqrt_days;
  STRA_LOG(logger_, STRA_DEBUG,
           "calculate_ewma_vol ewma_vol:{:.6f} sqrt_days:{:.2f} ",
           mid_period_ewma_vol_ * BPS, sqrt_days);
}

void rule_impl::calculate_volatility(long recv_time) {
  // 使用静态常量避免频繁创建临时变量
  static const int64_t sampling_interval = 1e8;

  // 只有当时间间隔足够大时才添加样本，减少不必要的计算
  if (recv_time > feed_vol_ts_ + sampling_interval) {
    // 使用当前中间价格作为样本
    bar_.feed(mp_, recv_time);
    vol_calculator_.add_sample(mp_);
    feed_vol_ts_ = recv_time;

    // 如果缓冲区已满，立即计算波动率
    // 将条件判断移到这里，减少不必要的函数调用
    if (vol_calculator_.is_sampling_buffer_full()) {
      volatility_ = vol_calculator_.calculate();
    }
  }
}

double rule_impl::calculate_trade_price_std(size_t window_size) {
  if (trans_.size() < window_size || window_size == 0) {
    return 0.0;
  }
  size_t start_idx = trans_.size() - window_size;
  double sum = 0.0;
  for (size_t i = start_idx; i < trans_.size(); ++i) {
    sum += trans_[i].price;
  }
  double mean = sum / window_size;
  double variance = 0.0;
  for (size_t i = start_idx; i < trans_.size(); ++i) {
    double diff = trans_[i].price - mean;
    variance += diff * diff;
  }
  variance /= window_size;
  return std::sqrt(variance);
}

void rule_impl::cancel_helper(std::vector<int64_t> &cancel_rids) {
  if (cancel_rids.size() > 10) {
    int n = cancel_rids.size() / 10;
    for (int i = 0; i < n; i++) {
      std::vector<int64_t> c = std::vector<int64_t>(
          cancel_rids.begin() + i * 10, cancel_rids.begin() + (i + 1) * 10);
      order_helper::cancel_order_list(
          ctx_, fast_trader_elite::exchange_type::BINANCE,
          data_->instrument_idx, data_->trading_account_id, c);
    }
    int q = cancel_rids.size() % 10;
    if (q) {
      std::vector<int64_t> c2 =
          std::vector<int64_t>(cancel_rids.end() - q, cancel_rids.end());
      order_helper::cancel_order_list(
          ctx_, fast_trader_elite::exchange_type::BINANCE,
          data_->instrument_idx, data_->trading_account_id, c2);
    }
  } else {
    order_helper::cancel_order_list(
        ctx_, fast_trader_elite::exchange_type::BINANCE, data_->instrument_idx,
        data_->trading_account_id, cancel_rids);
  }
}
void rule_impl::cancel(long rcv_time, int64_t order_id,
                       std::unordered_map<int64_t, order> &check_map) {
  auto itr = check_map.find(order_id);
  if (itr != check_map.end()) {
    order_helper::cancel_order(ctx_, fast_trader_elite::exchange_type::BINANCE,
                               data_->instrument_idx, data_->trading_account_id,
                               order_id);
    itr->second.cancel_cnt++;
    itr->second.close_time = rcv_time + data_->delay_cancel_time * 1e9;
    STRA_LOG(logger_, STRA_DEBUG, "rule_impl::{} ins:{} order_id:{} ",
             __FUNCTION__, data_->instrument, order_id);
  }
}

void rule_impl::cancel_list(long rcv_time, std::vector<int64_t> &cancel_rids,
                            std::unordered_map<int64_t, order> &check_map) {
  if (cancel_rids.size() == 0) {
    return;
  }
  std::vector<int64_t> real_cancel_ids;
  for (auto order_id : cancel_rids) {
    auto itr = check_map.find(order_id);
    if (itr != check_map.end()) {
      real_cancel_ids.push_back(order_id);
      itr->second.cancel_cnt++;
      itr->second.close_time = rcv_time + data_->delay_cancel_time * 1e9;
    }
  }
  cancel_helper(real_cancel_ids);
}

void rule_impl::cancel_and_stopall() {
  STRA_LOG(logger_, STRA_INFO, "stopall ins:{}", data_->instrument);
  order_helper::cancel_order_all(ctx_, data_->exchange_id,
                                 data_->instrument_idx,
                                 data_->trading_account_id);
  order_helper::close_order_all(ctx_, data_->exchange_id, data_->instrument_idx,
                                data_->trading_account_id, data_->portfolio_id,
                                fast_trader_elite::direction_type::SELL);
  order_helper::close_order_all(ctx_, data_->exchange_id, data_->instrument_idx,
                                data_->trading_account_id, data_->portfolio_id,
                                fast_trader_elite::direction_type::BUY);
}

void rule_impl::calculate_speed() {
  if (trans_.size() < 2) {
    return;
  }
  auto &trade_front = trans_.front();
  auto &trade_end = trans_.back();
  trade_speed_ =
      trans_.size() * 1.0 /
      ((trade_end.exchange_timestamp - trade_front.exchange_timestamp) * 1.0 /
       1000);
}

void rule_impl::calc_initial_bid_ask(int64_t recv_time) {
  double short_vol = calculate_trade_price_std(100);
  double long_vol = calculate_trade_price_std(400);
  double std_ratio = std::max(std::log(1.71828 + short_vol / long_vol), 0.999);

  double atr_fix = 1.0;
  if (std_ratio > 1.0) {
    atr_fix = last_atr_fix_ * 0.6 + std_ratio * 0.4;
  } else {
    atr_fix = last_atr_fix_ * 0.9 + std_ratio * 0.1;
  }
  last_atr_fix_ = atr_fix;

  double std_dev =
      short_vol * data_->tick_ema_std_width * std::max(1.0, atr_fix);

  double ema_spread = data_->tick_ema_spread;
  double no_trade_punish = no_trade_punishment(recv_time);
  double custom_spread_ratio = 1;

  // 如果有中期波动率数据，使用加权平均计算ema_spread
  if (natr_bps_ > 0.0 && mid_period_ewma_vol_ > 0.0) {
    double natr = natr_bps_ / BPS; // Convert from BPS to ratio
    // ema_spread = data_->tick_ema_spread + 0.1 * natr +
    //              0.005 * mid_period_ewma_vol_ +
    //              data_->gamma * volatility_ / mp_;
    ema_spread = data_->tick_ema_spread + data_->natr_ratio * natr +
                 data_->ewma_vol_ratio * mid_period_ewma_vol_ +
                 data_->gamma * volatility_ / mp_;
  } else if (volatility_ > 0) {
    ema_spread = data_->tick_ema_spread + data_->gamma * volatility_ / mp_;
  }

  ema_spread *= no_trade_punish;
  ema_spread *= custom_spread_ratio;

  fair_price_ = ema_price_;
  fair_price_ = mp_ - data_->tick_ema_shift_multi * (fair_price_ - mp_);

  if (data_->do_long) {
    calc_bid_price_ = std::min(
        best_bid_,
        math_helper::round_down(fair_price_ * (1 - ema_spread * atr_fix),
                                data_->price_tick_size));

    if (data_->tick_ema_first_imbalance != -1.0) {
      if (mp_ < ema_price_) {
        if (trade_imb_ < -data_->tick_ema_first_imbalance &&
            trade_imb_ > -0.3333) {
          calc_bid_price_ =
              std::min(calc_bid_price_,
                       math_helper::round_down(
                           fair_price_ - std_dev * data_->tick_ema_num_std,
                           data_->price_tick_size));
        } else if (trade_imb_ > -0.5 && trade_imb_ <= -0.3333) {
          calc_bid_price_ = std::min(
              calc_bid_price_,
              math_helper::round_down(
                  fair_price_ - std_dev * (data_->tick_ema_num_std +
                                           data_->tick_ema_std_spacing),
                  data_->price_tick_size));
        } else if (trade_imb_ <= -0.5) {
          calc_bid_price_ = std::min(
              calc_bid_price_,
              math_helper::round_down(
                  fair_price_ - std_dev * (data_->tick_ema_num_std +
                                           2.5 * data_->tick_ema_std_spacing),
                  data_->price_tick_size));
        }
      }
    }
  }

  if (data_->do_shrt) {
    calc_ask_price_ =
        std::max(best_ask_,
                 math_helper::round_up(fair_price_ * (1 + ema_spread * atr_fix),
                                       data_->price_tick_size));

    if (data_->tick_ema_first_imbalance != -1.0) {
      if (mp_ > ema_price_) {
        if (trade_imb_ > data_->tick_ema_first_imbalance &&
            trade_imb_ < 0.3333) {
          calc_ask_price_ =
              std::max(calc_ask_price_,
                       math_helper::round_up(
                           fair_price_ + data_->tick_ema_num_std * std_dev,
                           data_->price_tick_size));
        } else if (trade_imb_ >= 0.3333 && trade_imb_ < 0.5) {
          calc_ask_price_ = std::max(
              calc_ask_price_,
              math_helper::round_up(fair_price_ +
                                        std_dev * (data_->tick_ema_num_std +
                                                   data_->tick_ema_std_spacing),
                                    data_->price_tick_size));
        } else if (trade_imb_ >= 0.5) {
          calc_ask_price_ = std::max(
              calc_ask_price_,
              math_helper::round_up(
                  fair_price_ + std_dev * (data_->tick_ema_num_std +
                                           2.5 * data_->tick_ema_std_spacing),
                  data_->price_tick_size));
        }
      }
    }
  }

  STRA_LOG(logger_, STRA_DEBUG,
           "calc_initial_bid_ask mid_p:{:.6f} fp:{:.6f} ema_spread:{:.2f} "
           "ema_spread_raw:{:.2f} "
           "natr_bps:{:.2f} ewma_vol:{:.2f} std_ratio:{:.4f} "
           "std_dev:{:.4f} atr_fix:{:.4f} tema_p:{:.2f} ema_p:{:.2f} "
           "imb:{:.3f} bid_p:{:.6f} "
           "ask_p:{:.6f} no_trade_punish:{:.2f} vol:{:.2f}",
           mp_, fair_price_, ema_spread * BPS, data_->tick_ema_spread * BPS,
           natr_bps_, mid_period_ewma_vol_ * BPS, std_ratio,
           std_dev * BPS / mp_, atr_fix, (tema_price_ - mp_) * BPS / mp_,
           (ema_price_ - mp_) * BPS / mp_, trade_imb_, calc_bid_price_,
           calc_ask_price_, no_trade_punish, volatility_ * BPS / mp_);
}

double rule_impl::no_trade_punishment(int64_t current_time) {
  if (last_trade_ts_ == 0) {
    return 1;
  }
  int64_t time_diff_ns = current_time - last_trade_ts_;
  double time_diff_sec = time_diff_ns / 1e9; // 纳秒转换为秒
  double res =
      std::max(0.8, 3600.0 / (3600.0 + std::max(0.0, time_diff_sec - 200.0)));
  return res;
}

double rule_impl::time_to_days(int seconds) {
  // 计算一天中有多少个这样的时间周期
  // 例如，如果 seconds = 60（即 1 分钟），那么一天中有 24 * 60 = 1440
  // 个这样的周期
  const int seconds_per_day = 24 * 60 * 60; // 一天的秒数
  return static_cast<double>(seconds_per_day) / static_cast<double>(seconds);
}

double rule_impl::calc_pos_margin_cost(double pos, double pos_price) {
  double pos_margin = std::abs(pos) * pos_price / data_->leverage;
  return pos_margin;
}

double rule_impl::calc_long_reentry_price(double pos_margin, double pos_price) {
  double modified_grid_spacing =
      data_->grid_spacing *
      (1 + pos_margin / init_wallet_balance_ * data_->grid_coefficient);
  STRA_LOG(logger_, STRA_DEBUG,
           "pos_margin:{:.6f} "
           "pos_price:{:.6f} modified_grid_spacing:{:.6f} "
           "init_wallet_balance_:{:.6f} grid_coefficient:{:.6f} "
           "data_->grid_spacing:{:.6f} ",
           pos_margin, pos_price, modified_grid_spacing, init_wallet_balance_,
           data_->grid_coefficient, data_->grid_spacing);
  return math_helper::round_down(pos_price * (1 - modified_grid_spacing),
                                 data_->price_tick_size);
  // return math_helper::round_down(
  //     pos_price * (1 - modified_grid_spacing),
  //     math_helper::round_up(pos_price * data_->grid_spacing / 4,
  //                           data_->price_tick_size));
}
double rule_impl::calc_short_reentry_price(double pos_margin,
                                           double pos_price) {
  double modified_grid_spacing =
      data_->grid_spacing *
      (1 + pos_margin / init_wallet_balance_ * data_->grid_coefficient);
  STRA_LOG(logger_, STRA_DEBUG,
           "pos_margin:{:.6f} "
           "pos_price:{:.6f} modified_grid_spacing:{:.6f} "
           "init_wallet_balance_:{:.6f} grid_coefficient:{:.6f} "
           "data_->grid_spacing:{:.6f} ",
           pos_margin, pos_price, modified_grid_spacing, init_wallet_balance_,
           data_->grid_coefficient, data_->grid_spacing);
  return math_helper::round_up(pos_price * (1 + modified_grid_spacing),
                               data_->price_tick_size);
  // return math_helper::round_up(
  //     pos_price * (1 + modified_grid_spacing),
  //     math_helper::round_up(pos_price * data_->grid_spacing / 4,
  //                           data_->price_tick_size));
}
double rule_impl::calc_reentry_qty(double curr_used_pos, double pos_price) {
  double max_pos = init_wallet_balance_ * data_->leverage * 0.92 / mp_;
  double qty_available =
      std::max(0.0, math_helper::round_down(max_pos - std::abs(curr_used_pos),
                                            data_->volume_step_size));
  double qty =
      std::min(qty_available, math_helper::round_down(std::abs(curr_used_pos) *
                                                          data_->ddown_factor,
                                                      data_->volume_step_size));
  return qty;
}
double rule_impl::calc_min_close_qty() {
  return math_helper::round_down(quote_base_usdt_ *
                                     data_->min_close_qty_multiplier / mp_,
                                 data_->volume_step_size);
}
void rule_impl::calculate_factor_by_md(uint64_t recv_time) {
  if (!share_data_.md_ready()) {
    return;
  }
}
void rule_impl::calculate_factor_by_trans(uint64_t recv_time) {
  if (!share_data_.trans_ready()) {
    return;
  }
}
} // namespace fast_trader_elite::strategy