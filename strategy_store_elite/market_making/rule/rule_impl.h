#pragma once
#include "../../frame/bar_feeder.h"
#include "../../frame/pnl_calculator.h"
#include "../../frame/strategy_logger.h"
#include "../../frame/ta_helper.h"
#include "../data/data_manager.h"
#include "../utils/ema_calculator.h"
#include "../utils/order.h"
#include "../utils/volatility_calculator.h"
#include "../utils/share_data.h"
#include "../utils/factor_calculator.h"
#include "fast_trader_elite/data_model/field.h"
#include "i_strategy_ctx.h"
#include <cstdint>
#include <deque>
#include <sys/types.h>

namespace fast_trader_elite::strategy {
class rule_impl {
public:
  rule_impl(data *data, fast_trader_elite::strategy::logger &logger,
            i_strategy_ctx *ctx);
  virtual ~rule_impl();
  void feed_kline(kline_market_data_field *kline);
  void feed_md(depth_market_data_field *md);
  void feed_trans(transaction_field *trans);
  void feed_http_trans(transaction_field *trans);
  void feed_trade(const trade_field *f, long recv_time);

protected:
  void cancel_helper(std::vector<int64_t> &cancel_rids);
  void cancel(long rcv_time, int64_t order_id,
              std::unordered_map<int64_t, order> &check_map);
  void cancel_list(long rcv_time, std::vector<int64_t> &cancel_rids,
                   std::unordered_map<int64_t, order> &check_map);
  void cancel_and_stopall();
  void calc_initial_bid_ask(int64_t recv_time);
  double calc_long_reentry_price(double pos_margin, double pos_price);
  double calc_short_reentry_price(double pos_margin, double pos_price);
  double calc_reentry_qty(double curr_used_pos, double pos_price);
  double calc_pos_margin_cost(double pos, double pos_price);
    double calc_min_close_qty();
private:
  void calculate_atr(const fast_trader_elite::strategy::bar &bar);
  void calculate_ewma_vol(const fast_trader_elite::strategy::bar &bar);
  void calculate_volatility(long recv_time);
  double calculate_trade_price_std(size_t window_size);

  void calculate_speed();
  double time_to_days(int seconds);
  double no_trade_punishment(int64_t current_time);

  void calculate_factor_by_md(uint64_t recv_time);
  void calculate_factor_by_trans(uint64_t recv_time);

protected:
  // 计算
  double mp_;
  double fair_price_;
  double natr_bps_{-1};
  double volatility_{-1};
  double ema_price_{0};
  double tema_price_{0};

  double trade_imb_{0};
  double last_atr_fix_{1.0};
  double best_bid_{0.0};
  double best_ask_{0.0};
  double best_trade_bid_{0.0};
  double best_trade_ask_{0.0};
  double calc_bid_price_{-1.0};
  double calc_ask_price_{-1.0};
  int64_t last_trade_ts_{0};
  double mid_period_ewma_vol_{0.0}; // 用于计算atr_fix
  double init_wallet_balance_{0};
  double quote_base_usdt_{0};

  // 风控 行情不更新或者单子撤不掉及时关机
  uint64_t last_md_update_ts_;
  int recancel_cnt_{0};
  bool force_stop_{false};

  bool trans_init_{false};
  // 辅助
  i_strategy_ctx *ctx_;
  pnl_calculator pnl_;
  int pnl_timer_id_;

private:
  std::deque<transaction_field> trans_;
  data *data_;
  fast_trader_elite::strategy::bar_feeder bar_;
  fast_trader_elite::strategy::ta_helper ta_;
  volatility_calculator vol_calculator_;
  ema_calculator tema_price_calculator_;
  simple_ema_calculator ema_price_calculator_;
  simple_ema_calculator ewma_vol_calculator_;
  uint64_t feed_vol_ts_{0};
  double trade_speed_;

  //factor
  share_data share_data_;
  factor_calculator factor_calculator_;


protected:
  fast_trader_elite::strategy::logger &logger_;
};
} // namespace fast_trader_elite::strategy