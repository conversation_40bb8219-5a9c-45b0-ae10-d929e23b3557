#pragma once
#include "../../frame/strategy_logger.h"
#include "cpp_frame/struct_serialize/ser_reflection.h"
#include "fast_trader_elite/data_model/type.h"
#include "i_strategy_ctx.h"
#include "nlohmann_json/json.hpp"
#include <cstdint>
#include <fstream>
#include <vector>

#define BPS 10000
namespace fast_trader_elite::strategy {

class data {
public:
  data(fast_trader_elite::strategy::logger &logger) : logger_(logger) {}
  fast_trader_elite::exchange_type exchange_id{exchange_type::BYBIT};
  std::string instrument;
  uint16_t instrument_idx;
  double price_tick_size;
  double volume_step_size;

  // Global config
  int log_level{1};
  bool cross_mode{false};

  // Symbol config
  int trading_account_id{0};
  double init_usdt{0.0};
  int logging_level{1};
  double balance_pct{0.0};
  double ddown_factor{0.0};
  double entry_qty_pct{0.0};
  double min_close_qty_multiplier{0.0};
  int leverage{5};
  int n_entry_orders{0};
  int n_close_orders{0};
  double grid_spacing{0.0};
  double grid_coefficient{0.0};
  double min_markup{0.0};
  double max_markup{0.0};
  double gamma{0.0};
  double natr_ratio{0.0};
  double ewma_vol_ratio{0.0};

  // Indicator settings
  bool do_long{false};
  bool do_shrt{false};
  bool funding_fee_collect_mode{false};
  int kline_sec{180};

  // Tick EMA settings
  int tick_ema_span{0};
  double tick_ema_spread{0.0};
  double tick_ema_shift_multi{0.0};
  double tick_ema_first_imbalance{0.0};
  double tick_ema_num_std{0.0};
  double tick_ema_std_spacing{0.0};
  double tick_ema_std_width{0.0};

  // Stop loss settings
  bool market_stop_loss{false};
  double stop_loss_liq_diff{0.0};
  double stop_loss_pos_price_diff{0.0};
  double stop_loss_pos_reduction{0.0};
  double stop_loss_cooldown{0.0};
  std::string key;
  double stop_loss_danger{0.0};
  int volbars_in_day{0};
  double n_reverse_qty{0.0};

  double refresh_window{1.5};

  int delay_cancel_time{2};
  int16_t portfolio_id{0};
  double trade_sum{0};

  fast_trader_elite::strategy::logger &logger_;
};

DEFINE_SER_DATA(data, instrument, instrument_idx, log_level, cross_mode,
                trading_account_id, logging_level, balance_pct, ddown_factor,
                entry_qty_pct, min_close_qty_multiplier, leverage,
                n_entry_orders, n_close_orders, grid_spacing, grid_coefficient,
                min_markup, max_markup, gamma, kline_sec, do_long, do_shrt,
                funding_fee_collect_mode, tick_ema_span, tick_ema_spread,
                tick_ema_shift_multi, tick_ema_first_imbalance,
                tick_ema_num_std, tick_ema_std_spacing, tick_ema_std_width,
                market_stop_loss, stop_loss_liq_diff, stop_loss_pos_price_diff,
                stop_loss_pos_reduction, stop_loss_cooldown, key,
                stop_loss_danger, volbars_in_day, n_reverse_qty, refresh_window,
                init_usdt, ewma_vol_ratio, natr_ratio)

class data_manager {
public:
  data_manager(fast_trader_elite::strategy::logger &logger) : logger_(logger) {
    datas.resize(1500);
    std::fill(datas.begin(), datas.end(), nullptr);
  }
  ~data_manager() {
    for (auto &data : datas) {
      if (data) {
        delete data;
      }
    }
  }
  void init_json(nlohmann::json json) { config_json_ = json; }
  void parse_config(i_strategy_ctx *ctx) {
    // std::cout << "parse_config:" << config_json_ << std::endl;
    // Parse global config
    log_level = config_json_["log_level"].get<int>();
    cross_mode = config_json_["cross_mode"].get<bool>();

    // Parse symbol configs
    auto symbol_configs = config_json_["symbol_configs"];
    for (auto &symbol_config : symbol_configs) {
      std::string symbol = symbol_config["symbol"].get<std::string>();
      auto ins =
          ctx->get_instrument_filed(exchange_type::BYBIT, symbol.c_str());
      if (ins == nullptr) {
        STRA_LOG(logger_, STRA_ERROR, "not find symbol {}", symbol);
        continue;
      }
      auto cur_data = new data(logger_);
      datas[ins->instrument_idx] = cur_data;

      // Set basic instrument info
      cur_data->instrument_idx = ins->instrument_idx;
      cur_data->instrument = symbol;
      cur_data->price_tick_size = ins->tick_size;
      cur_data->volume_step_size = ins->step_size;

      // Set global config
      cur_data->log_level = log_level;
      cur_data->cross_mode = cross_mode;

      // Set symbol config
      cur_data->trading_account_id =
          symbol_config["trading_account_id"].get<int>();
      cur_data->init_usdt = symbol_config["init_usdt"].get<double>();
      cur_data->logging_level = symbol_config["logging_level"].get<int>();
      cur_data->balance_pct = symbol_config["balance_pct"].get<double>();
      cur_data->ddown_factor = symbol_config["ddown_factor"].get<double>();
      cur_data->entry_qty_pct = symbol_config["entry_qty_pct"].get<double>();
      cur_data->min_close_qty_multiplier =
          symbol_config["min_close_qty_multiplier"].get<double>();
      cur_data->leverage = symbol_config["leverage"].get<int>();
      cur_data->n_entry_orders = symbol_config["n_entry_orders"].get<int>();
      cur_data->n_close_orders = symbol_config["n_close_orders"].get<int>();
      cur_data->grid_spacing = symbol_config["grid_spacing"].get<double>();
      cur_data->grid_coefficient = symbol_config["grid_coefficient"].get<int>();
      cur_data->min_markup = symbol_config["min_markup"].get<double>();
      cur_data->max_markup = symbol_config["max_markup"].get<double>();
      cur_data->gamma = symbol_config["gamma"].get<double>();
      cur_data->ewma_vol_ratio = symbol_config["ewma_vol_ratio"].get<double>();
      cur_data->natr_ratio = symbol_config["natr_ratio"].get<double>();
      cur_data->refresh_window = symbol_config["refresh_window"].get<double>();

      // Set indicator settings
      auto indicator_settings = symbol_config["indicator_settings"];
      cur_data->kline_sec = indicator_settings["kline_sec"].get<int>();
      cur_data->do_long = indicator_settings["do_long"].get<bool>();
      cur_data->do_shrt = indicator_settings["do_shrt"].get<bool>();
      cur_data->funding_fee_collect_mode =
          indicator_settings["funding_fee_collect_mode"].get<bool>();

      // Set tick EMA settings
      auto tick_ema = indicator_settings["tick_ema"];
      cur_data->tick_ema_span = tick_ema["span"].get<int>();
      cur_data->tick_ema_spread = tick_ema["spread"].get<double>();
      cur_data->tick_ema_shift_multi =
          tick_ema["ema_shift_multi"].get<double>();
      cur_data->tick_ema_first_imbalance =
          tick_ema["first_imbalance"].get<double>();
      cur_data->tick_ema_num_std = tick_ema["num_std"].get<double>();
      cur_data->tick_ema_std_spacing = tick_ema["std_spacing"].get<double>();
      cur_data->tick_ema_std_width = tick_ema["std_width"].get<double>();

      // Set stop loss settings
      cur_data->market_stop_loss =
          symbol_config["market_stop_loss"].get<bool>();
      cur_data->stop_loss_liq_diff =
          symbol_config["stop_loss_liq_diff"].get<double>();
      cur_data->stop_loss_pos_price_diff =
          symbol_config["stop_loss_pos_price_diff"].get<double>();
      cur_data->stop_loss_pos_reduction =
          symbol_config["stop_loss_pos_reduction"].get<double>();
      cur_data->stop_loss_cooldown =
          symbol_config["stop_loss_cooldown"].get<double>();
      cur_data->key = symbol_config["key"].get<std::string>();
      cur_data->stop_loss_danger =
          symbol_config["stop_loss_danger"].get<double>();
      cur_data->volbars_in_day = symbol_config["volbars_in_day"].get<int>();
      cur_data->n_reverse_qty = symbol_config["n_reverse_qty"].get<double>();
      // std::cout << "config:" << cpp_frame::struct_serialize::to_json(*cur_data)
      //           << std::endl;

      STRA_LOG(logger_, STRA_INFO, "config:{}",
               cpp_frame::struct_serialize::to_json(*cur_data));
    }
  }

public:
  std::vector<data *> datas;
  fast_trader_elite::strategy::logger &logger_;

private:
  nlohmann::json config_json_;
  int log_level{1};
  bool cross_mode{false};
};
} // namespace fast_trader_elite::strategy
