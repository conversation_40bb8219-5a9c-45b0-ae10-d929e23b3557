#include <nlohmann_json/json.hpp>
#include <vector>

#include "../../frame/strategy_logger.h"
#include "../data/data_manager.h"
#include "../rule/rule.h"
#include "fast_trader_elite/data_model/field.h"
#include "i_strategy.h"

namespace fast_trader_elite::strategy {

class strategy : public i_strategy {
 public:
  strategy();
  virtual ~strategy();
  virtual bool on_start(i_strategy_ctx *ctx, const std::string& strategy_name) override;
  virtual bool on_stop(i_strategy_ctx *ctx) override;
  virtual void on_kline_data(i_strategy_ctx *ctx,
                             kline_market_data_field *field) override;
  virtual void on_depth_data(i_strategy_ctx *ctx,
                             depth_market_data_field *field) override;
  virtual void on_tick_data(i_strategy_ctx *ctx,
                            tick_market_data_field *field) override;
  virtual void on_transaction_data(i_strategy_ctx *ctx,
                                   transaction_field *field) override;
  virtual void on_liquidation_data(i_strategy_ctx *ctx,
                                   liquidation_field *field) override;

  virtual void on_http_depth_data(i_strategy_ctx *ctx,
                                  depth_market_data_field *field,
                                  bool is_last) override;
  virtual void on_http_tick_data(i_strategy_ctx *ctx,
                                 tick_market_data_field *field,
                                 bool is_last) override;
  virtual void on_http_liquidation_data(i_strategy_ctx *ctx,
                                        liquidation_field *field,
                                        bool is_last) override;
  virtual void on_http_kline_data(i_strategy_ctx *ctx,
                                  kline_market_data_field *field,
                                  bool is_last) override;
  virtual void on_http_transaction_data(i_strategy_ctx *ctx,
                                        transaction_field *field,
                                        bool is_last) override;

  virtual void on_order(i_strategy_ctx *ctx, order_field *field) override;
  virtual void on_trade(i_strategy_ctx *ctx, trade_field *field) override;
  virtual void on_rtn_position(i_strategy_ctx *ctx,
                               position_field *position) override;
  virtual void on_rtn_wallet_balance(
      i_strategy_ctx *ctx, wallet_balance_field *wallet_balance) override;
  virtual void on_http_rsp(i_strategy_ctx *ctx, http_rsp_field *field) override;

 private:
  void parse_config(const std::string& config_path);
  void perpare_strategy();
  void subscribe();

 private:
  int async_timer_id_;
  fast_trader_elite::strategy::logger logger_;
  data_manager *data_{nullptr};
  i_strategy_ctx *ctx_;
  std::vector<rule *> rules_;
  nlohmann::json config_json_;
};
}  // namespace fast_trader_elite::strategy