#include "strategy.h"
#include "fast_trader_elite/data_model/field.h"
#include "fast_trader_elite/data_model/type.h"
#include <algorithm>
#include <fstream>
namespace fast_trader_elite::strategy {

strategy::strategy() {
  rules_.resize(1000);
  std::fill(rules_.begin(), rules_.end(), nullptr);
}
strategy::~strategy() {
  logger_.poll();
  logger_.flush();
  std::cout << "strategy desc" << std::endl;
}
bool strategy::on_start(i_strategy_ctx *ctx, const std::string &strategy_name) {
  std::cout << "on start, strategy_name: " << strategy_name << std::endl;
  ctx_ = ctx;
  logger_.set_context(ctx);
  strategy_instance_config config = ctx->get_strategy_config(strategy_name);
  // 设置日志级别
  if (config.log_level == "DEBUG") {
    SET_STRA_LOG_LEVEL(logger_, LOG_DEBUG);
  } else if (config.log_level == "INFO") {
    SET_STRA_LOG_LEVEL(logger_, LOG_INFO);
  } else if (config.log_level == "WARN") {
    SET_STRA_LOG_LEVEL(logger_, LOG_WARN);
  } else if (config.log_level == "ERROR") {
    SET_STRA_LOG_LEVEL(logger_, LOG_ERROR);
  } else {
    SET_STRA_LOG_LEVEL(logger_, LOG_DEBUG); // 默认级别
  }

  // STRA_LOG(logger_, STRA_INFO,
  //          "Strategy starting, name: {}, log_file: {}, log_level: {}",
  //          strategy_name, path, config.log_level);

  data_ = new data_manager(logger_);

  // 使用配置中的策略配置路径
  if (config.strategy_config_path.empty()) {
    STRA_LOG(
        logger_, STRA_ERROR,
        "No strategy_config_path provided in configuration for strategy: {}",
        strategy_name);
    return false;
  }

  // 解析策略配置
  parse_config(config.strategy_config_path);
  // only one symbol
  for (auto data : data_->datas) {
    if (data) {
      rules_[data->instrument_idx] = new rule(data, logger_, ctx);
      std::string path = "../log/" + data->instrument + "_market_maker.log";
      SET_STRA_LOG_FILE(logger_, path.c_str());
    }
  }
  perpare_strategy();
  subscribe();
  async_timer_id_ = ctx->register_async_timer(
      100, [this]() { STRA_POLL(logger_); }, true);
  return true;
}
bool strategy::on_stop(i_strategy_ctx *ctx) {
  std::cout << "strategy on stop" << std::endl;
  STRA_LOG(logger_, STRA_DEBUG, "{} ", __FUNCTION__);
  for (auto rule : rules_) {
    if (rule) {
      rule->on_stop(ctx);
    }
  }

  return true;
}

void strategy::parse_config(const std::string &config_path) {
  STRA_LOG(logger_, STRA_INFO, "Loading config from: {}", config_path);
  std::ifstream ifs(config_path);
  if (!ifs.good()) {
    STRA_LOG(logger_, STRA_ERROR, "Failed to open config file: {}",
             config_path);
    return;
  }
  ifs >> config_json_;
  ifs.close();
  data_->init_json(config_json_);
  data_->parse_config(ctx_);
}

void strategy::perpare_strategy() {
  auto &datas = data_->datas;
  for (auto data : datas) {
    if (data) {
      md_kline_req_field f;
      f.instrument_idx = data->instrument_idx;
      f.exchange_id = exchange_type::BYBIT;
      if (data->kline_sec == 60) {
        f.kline_period = kline_period_type::MINUTE1;
      } else if (data->kline_sec == 180) {
        f.kline_period = kline_period_type::MINUTE3;
      } else if (data->kline_sec == 300) {
        f.kline_period = kline_period_type::MINUTE5;
      } else if (data->kline_sec == 900) {
        f.kline_period = kline_period_type::MINUTE15;
      } else if (data->kline_sec == 1800) {
        f.kline_period = kline_period_type::MINUTE30;
      } else if (data->kline_sec == 3600) {
        f.kline_period = kline_period_type::HOUR1;
      } else {
        f.kline_period = kline_period_type::MINUTE3;
      }
      f.md_id = 0;
      f.kline_cnt = 200;
      ctx_->get_kline(&f, 0);
      transaction_req_field trans_f;
      trans_f.instrument_idx = data->instrument_idx;
      trans_f.exchange_id = exchange_type::BYBIT;
      trans_f.md_id = 0;
      trans_f.trans_cnt = 1000;
      ctx_->get_recent_transaction(&trans_f, 1);
      leverage_req_field leverage_req;
      leverage_req.leverage = data->leverage;
      leverage_req.instrument_idx = data->instrument_idx;
      leverage_req.exchange_id = exchange_type::BYBIT;
      leverage_req.trading_account_id = 0;
      ctx_->set_leverage(&leverage_req, 2);
      position_req_field position_req;
      position_req.instrument_idx = data->instrument_idx;
      position_req.exchange_id = exchange_type::BYBIT;
      position_req.trading_account_id = 0;
      ctx_->get_postion(&position_req, 3);
      wallet_balance_req_field wallet_balance_req;
      wallet_balance_req.exchange_id = exchange_type::BYBIT;
      wallet_balance_req.trading_account_id = 0;
      wallet_balance_req.instrument_idx = 0;
      strcpy(wallet_balance_req.instrument_name, "USDT");
      ctx_->get_wallet_banlance(&wallet_balance_req, 4);
    }
  }
}

void strategy::subscribe() {
  auto &datas = data_->datas;
  md_sub_code_field f;
  f.md_id = 0;
  for (auto data : datas) {
    if (data) {
      STRA_LOG(logger_, STRA_DEBUG, "sub:{}", data->instrument);
      std::string order_book_sub_code = "orderbook.50." + data->instrument;
      std::string trade_sub_code = "publicTrade." + data->instrument;
      f.sub_code.push_back(order_book_sub_code);
      f.sub_code.push_back(trade_sub_code);
      // std::string liq_sub_code = "liquidation." + data->instrument;
      // std::string ticker_sub_code = "tickers." + data->instrument;
      // f.sub_code.push_back(liq_sub_code);
      // f.sub_code.push_back(ticker_sub_code);
    }
  }
  ctx_->subscribe(&f);
}
void strategy::on_kline_data(i_strategy_ctx *ctx,
                             kline_market_data_field *field) {
  auto rule = rules_[field->instrument_idx];
  if (rule) {
    rule->on_kline_data(ctx, field);
  }
}
void strategy::on_depth_data(i_strategy_ctx *ctx,
                             depth_market_data_field *field) {
  auto rule = rules_[field->instrument_idx];
  if (rule) {
    rule->on_depth_data(ctx, field);
  }
}
void strategy::on_tick_data(i_strategy_ctx *ctx,
                            tick_market_data_field *field) {
  auto rule = rules_[field->instrument_idx];
  if (rule) {
    rule->on_tick_data(ctx, field);
  }
}
void strategy::on_transaction_data(i_strategy_ctx *ctx,
                                   transaction_field *field) {
  auto rule = rules_[field->instrument_idx];
  if (rule) {
    rule->on_transaction_data(ctx, field);
  }
}
void strategy::on_liquidation_data(i_strategy_ctx *ctx,
                                   liquidation_field *field) {
  STRA_LOG(logger_, STRA_INFO, "liquidation_field:{}",
           cpp_frame::struct_serialize::to_json(*field));
  auto rule = rules_[field->instrument_idx];
  if (rule) {
    rule->on_liquidation_data(ctx, field);
  }
}
void strategy::on_http_kline_data(i_strategy_ctx *ctx,
                                  kline_market_data_field *field,
                                  bool is_last) {
  auto rule = rules_[field->instrument_idx];
  if (rule) {
    rule->on_http_kline_data(ctx, field, is_last);
  }
}
void strategy::on_http_depth_data(i_strategy_ctx *ctx,
                                  depth_market_data_field *field,
                                  bool is_last) {
  auto rule = rules_[field->instrument_idx];
  if (rule) {
    rule->on_http_depth_data(ctx, field, is_last);
  }
}
void strategy::on_http_tick_data(i_strategy_ctx *ctx,
                                 tick_market_data_field *field, bool is_last) {
  auto rule = rules_[field->instrument_idx];
  if (rule) {
    rule->on_http_tick_data(ctx, field, is_last);
  }
}
void strategy::on_http_transaction_data(i_strategy_ctx *ctx,
                                        transaction_field *field,
                                        bool is_last) {
  auto rule = rules_[field->instrument_idx];
  if (rule) {
    rule->on_http_transaction_data(ctx, field, is_last);
  }
}
void strategy::on_http_liquidation_data(i_strategy_ctx *ctx,
                                        liquidation_field *field,
                                        bool is_last) {
  STRA_LOG(logger_, STRA_INFO, "liquidation_field:{}",
           cpp_frame::struct_serialize::to_json(*field));
  auto rule = rules_[field->instrument_idx];
  if (rule) {
    rule->on_http_liquidation_data(ctx, field, is_last);
  }
}
void strategy::on_order(i_strategy_ctx *ctx, order_field *field) {

  auto rule = rules_[field->instrument_idx];
  if (rule) {
    rule->on_order(ctx, field);
  }
}
void strategy::on_trade(i_strategy_ctx *ctx, trade_field *field) {
  auto rule = rules_[field->instrument_idx];
  if (rule) {
    rule->on_trade(ctx, field);
  }
}

void strategy::on_rtn_position(i_strategy_ctx *ctx, position_field *field) {
  auto rule = rules_[field->instrument_idx];
  if (rule) {
    rule->on_rtn_position(ctx, field);
  }
}
void strategy::on_rtn_wallet_balance(i_strategy_ctx *ctx,
                                     wallet_balance_field *field) {
  STRA_LOG(logger_, STRA_INFO, "on_rtn_wallet_balance:{}",
           cpp_frame::struct_serialize::to_json(*field));
  for (auto &rule : rules_) {
    if (rule) {
      rule->on_rtn_wallet_balance(ctx, field);
    }
  }
}
void strategy::on_http_rsp(i_strategy_ctx *ctx, http_rsp_field *field) {
  STRA_LOG(logger_, STRA_INFO, "http_rsp_field:{}",
           cpp_frame::struct_serialize::to_json(*field));
}

} // namespace fast_trader_elite::strategy
