#pragma once

#include <fast_trader_elite/data_model/type.h>

#include <cstdint>
struct order {
  order() {}
  order(int64_t order_id, double price, double volume, uint64_t close_time,
        double mid_p, fast_trader_elite::direction_type direction, uint64_t insert_ts)
      : id(order_id), price(price), volume(volume), close_time(close_time),
        direction(direction),insert_ts(insert_ts) {}
  int64_t id;
  double price;
  double volume;
  uint64_t close_time;
  fast_trader_elite::direction_type direction;
  uint64_t insert_ts;
  int cancel_cnt{0}; // 撤销的次数
  bool operator<(const order &b) const { return close_time > b.close_time; }
};