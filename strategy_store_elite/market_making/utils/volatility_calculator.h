#pragma once

#include <cmath>
#include <cstddef>
#include <deque>
#include <algorithm>

/**
 * 优化版本的波动率计算器
 *
 * 主要优化点：
 * 1. 增量计算差值平方和，避免每次重新计算
 * 2. 使用预计算的差值和，减少循环开销
 * 3. 避免使用 std::pow，直接使用乘法计算平方
 * 4. 使用 const 和 inline 函数提高性能
 */
class volatility_calculator {
public:
    /**
     * 构造函数
     */
    volatility_calculator() : sampling_length_(0), diff_sum_(0.0), last_vol_(-1.0) {}

    /**
     * 初始化采样长度
     *
     * @param sampling_length 采样长度
     */
    inline void init(int sampling_length) {
        sampling_length_ = sampling_length;
        sampling_buffer_.clear();
        diff_sum_ = 0.0;
    }

    /**
     * 添加样本，使用增量计算方式
     *
     * @param value 新的样本值
     */
    inline void add_sample(double value) {
        if (sampling_buffer_.size() < sampling_length_) {
            // 缓冲区未满，直接添加
            if (!sampling_buffer_.empty()) {
                // 计算与前一个值的差值平方
                double diff = value - sampling_buffer_.back();
                diff_sum_ += diff * diff; // 直接使用乘法代替 std::pow
            }
            sampling_buffer_.push_back(value);
            return;
        } else {
            // 缓冲区已满，移除最旧的样本并添加新样本
            if (sampling_buffer_.size() >= 2) {
                // 移除最旧的差值
                double old_diff = sampling_buffer_[1] - sampling_buffer_[0];
                diff_sum_ -= old_diff * old_diff;
            }

            // 添加新的差值
            double new_diff = value - sampling_buffer_.back();
            diff_sum_ += new_diff * new_diff;

            // 更新缓冲区
            sampling_buffer_.pop_front();
            sampling_buffer_.push_back(value);
        }
    }

    /**
     * 计算波动率，使用预计算的差值和
     *
     * @return 计算的波动率
     */
    inline double calculate() {
        if (sampling_buffer_.size() <= 1) {
            last_vol_ = 0;
            return last_vol_;
        }

        // 使用预计算的差值和计算波动率
        double mean = diff_sum_ / sampling_buffer_.size();
        last_vol_ = std::sqrt(mean);
        return last_vol_;
    }

    /**
     * 返回最后计算的波动率
     *
     * @return 最后计算的波动率
     */
    inline double current_value() const {
        return last_vol_;
    }

    /**
     * 检查缓冲区是否已满
     *
     * @return 缓冲区是否已满
     */
    inline bool is_sampling_buffer_full() const {
        return sampling_buffer_.size() >= sampling_length_;
    }

private:
    size_t sampling_length_;           // 采样长度
    std::deque<double> sampling_buffer_; // 采样缓冲区
    double diff_sum_;                  // 差值平方和
    double last_vol_;                  // 最后计算的波动率
};