#pragma once
#include <cmath>
#include <vector>

class ema_calculator {
public:
  ema_calculator() = default;
  void init(double half_life_ms, bool decay_on_read, double init_val) {
    value_ = init_val;
    timestamp_ = 0;
    half_life_ = half_life_ms;
    lambda_ = (std::log(2.0) / half_life_);
    decay_on_read_ = decay_on_read;
  }
  void decay(double current_timestamp) {
    double time_difference = current_timestamp - timestamp_;
    double decay_factor = std::exp(-lambda_ * time_difference);
    value_ *= decay_factor;
    timestamp_ = current_timestamp;
  }

  double update(double new_value, double new_timestamp) {
    if (timestamp_ == 0.0) {
      value_ = new_value;
    } else {
      double time_difference = new_timestamp - timestamp_;
      double decay_factor = std::exp(-lambda_ * time_difference);
      value_ = (1.0 - decay_factor) * new_value + decay_factor * value_;
    }
    timestamp_ = new_timestamp;
    return value_;
  }

  double get_value(double current_timestamp) {
    if (decay_on_read_) {
      decay(current_timestamp);
    }
    return value_;
  }

private:
  double value_;
  double timestamp_;
  double half_life_;
  double lambda_;
  bool decay_on_read_;
};

// A simple EMA calculator that doesn't depend on time
class simple_ema_calculator {
public:
  simple_ema_calculator() = default;

  // Initialize with a smoothing factor alpha (0 < alpha <= 1)
  // alpha = 2 / (N + 1) where N is the period
  void init(double alpha = 0.2, double init_val = 0.0) {
    alpha_ = alpha;
    value_ = init_val;
    initialized_ = false;
  }

  // Initialize with a period N
  // alpha = 2 / (N + 1)
  void init_with_period(int period, double init_val = 0.0) {
    alpha_ = 2.0 / (period + 1);
    value_ = init_val;
    initialized_ = false;
  }

  // Initialize with lambda parameter for EWMA
  // alpha = 1 - lambda
  void init_with_lambda(double lambda, double init_val = 0.0) {
    alpha_ = 1.0 - lambda;
    value_ = init_val;
    initialized_ = false;
  }

  // Update the EMA with a new value
  double update(double new_value) {
    if (!initialized_) {
      value_ = new_value;
      initialized_ = true;
    } else {
      value_ = alpha_ * new_value + (1.0 - alpha_) * value_;
    }
    return value_;
  }

  // Get the current EMA value
  double get_value() const {
    return value_;
  }

  // Calculate EMA for a series of values
  static std::vector<double> calculate(const std::vector<double>& values, double alpha) {
    std::vector<double> result;
    result.reserve(values.size());

    if (values.empty()) {
      return result;
    }

    double ema = values[0];
    result.push_back(ema);

    for (size_t i = 1; i < values.size(); ++i) {
      ema = alpha * values[i] + (1.0 - alpha) * ema;
      result.push_back(ema);
    }

    return result;
  }



private:
  double alpha_ = 0.2;  // Smoothing factor
  double value_ = 0.0;  // Current EMA value
  bool initialized_ = false;
};
