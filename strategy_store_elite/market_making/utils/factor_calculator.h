#pragma once
#include "fast_trader_elite/data_model/field.h"

#include <cmath>

#include "share_data.h"

class factor_calculator {
 public:
  factor_calculator(share_data* data) : data_(data) {}

  double big_vpin(int n = 100) {
    double sum_v = 0;
    int i = 0;
    data_->for_each_trans(
        n + 1, [&sum_v, &i, &n](const fast_trader_elite::transaction_field& f) {
          if (i < n) {
            if (f.is_maker) {
              sum_v -= f.volume;
            }
            else {
              sum_v += f.volume;
            }
          }
          i++;
        });
    return sum_v;
  }

  double tpap_diff(int n = 100) {
    double last_price = data_->get_last_trans().price;
    double sum_p = 0;
    data_->for_each_trans(n, [&sum_p](const fast_trader_elite::transaction_field& f) {
      sum_p += f.price;
    });
    return (sum_p / n) - last_price;
  }

  double price_impact() {
    auto md = data_->get_last_md();
    double ask = 0, bid = 0, ask_v = 0, bid_v = 0;
    for (int i = 0; i < 5; i++) {
      ask += md.ask_price[i] * md.ask_volume[i];
      bid += md.bid_price[i] * md.bid_volume[i];
      ask_v += md.ask_volume[i];
      bid_v += md.bid_volume[i];
    }
    ask = ask / ask_v;
    bid = bid / bid_v;
    double ask_p1 = md.ask_price[0];
    double bid_p1 = md.bid_price[0];
    return -(ask_p1 - ask) / ask_p1 - (bid_p1 - bid) / bid_p1;
  }
  
  double oir() {
    auto& md = data_->get_last_md();
    return (md.bid_volume[0] - md.ask_volume[0]) /
           (md.ask_volume[0] + md.bid_volume[0]);
  }
  double oir_diff(int n) {
    auto& md = data_->get_last_md();
    auto& prev_md = data_->get_rnth_md_itr(2);
    auto& prev_n_md = data_->get_rnth_md_itr(n + 1);
    double av = 0, bv = 0, last_av = 0, last_bv = 0, last_n_av = 0,
           last_n_bv = 0;
    for (int i = 0; i < 5; i++) {
      av += md.ask_volume[i];
      bv += md.bid_volume[i];
      last_av += prev_md.ask_volume[i];
      last_bv += prev_md.bid_volume[i];
      last_n_av += prev_n_md.ask_volume[i];
      last_n_bv += prev_n_md.bid_volume[i];
    }
    double oir =
        2 * ((std::log(bv + 1) / (std::log(bv + 1) + std::log(av + 1))) - 0.5);
    double prev_oir = 2 * ((std::log(last_bv + 1) /
                            (std::log(last_bv + 1) + std::log(last_av + 1))) -
                           0.5);
    double prev_n_oir =
        2 * ((std::log(last_n_bv + 1) /
              (std::log(last_n_bv + 1) + std::log(last_n_av + 1))) -
             0.5);
    return (oir - prev_n_oir) - (oir - prev_oir);
  }

 private:
  share_data* data_;
};
