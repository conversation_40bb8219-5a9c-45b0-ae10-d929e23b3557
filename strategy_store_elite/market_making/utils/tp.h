#pragma once
#include <cmath>

#include "../data/data_manager.h"
#include "fast_trader_elite/data_model/type.h"
namespace fast_trader_elite::strategy {

enum tp_action_type { NO_ACTION, TP_BUY, TP_SELL };

class tp {
public:
  void init(double stop_profit_atr_n, double trailing_profit_atr_n,
            data *data) {
    stop_profit_atr_n_ = stop_profit_atr_n;
    trailing_profit_atr_n_ = trailing_profit_atr_n;
    data_ = data;
  }
  void trigger(double cur_price, double avg_price,
               fast_trader_elite::direction_type dir, double net_pos) {
    cur_avg_price_ = avg_price;
    dir_ = dir;
    tp_hh_ = cur_price;
    tp_ll_ = cur_price;
    net_pos_ = net_pos;
    tp_status_ = false;
    tp_action_flag_ = false;
    log_flag_ = false;
  }
  void update_atr_bps(double atr_bps) { atr_bps_ = atr_bps; }
  tp_action_type update(double cur_price) {
    if (tp_action_flag_) {
      return NO_ACTION;
    }
    double base_quote_v = data_->quote_base_usdt / cur_price;
    double ratio = 1;
    double net_ratio = std::abs(net_pos_) / base_quote_v;
    if (net_ratio > 1.5 && net_ratio < 3) {
      ratio = 0.8;
    } else if (net_ratio > 3) {
      ratio = 0.6;
    }
    double tp_ratio = 1;
    double TP_first_long =
        cur_avg_price_ +
        ratio * (stop_profit_atr_n_ * atr_bps_) * cur_avg_price_ / 10000.0;
    double TP_first_short =
        cur_avg_price_ -
        ratio * (stop_profit_atr_n_ * atr_bps_) * cur_avg_price_ / 10000.0;
    double cur_bps = 0, bps_ratio = 0;
    // 回撤允许范围跟随盈利变大
    if (dir_ == fast_trader_elite::direction_type::BUY &&
        cur_price > TP_first_long) {
      cur_bps = (cur_price - cur_avg_price_) * 10000.0 / cur_avg_price_;
      bps_ratio = cur_bps / (stop_profit_atr_n_ * atr_bps_);
      if (bps_ratio > 2 && bps_ratio < 4) {
        tp_ratio = 1.25;
      } else if (bps_ratio > 4 && bps_ratio < 7.5) {
        tp_ratio = 1.5;
      } else if (bps_ratio > 7.5 && bps_ratio < 12) {
        tp_ratio = 2.5;
      } else if (bps_ratio > 12) {
        tp_ratio = 3;
      }
    }
    if (dir_ == fast_trader_elite::direction_type::SELL &&
        cur_price < TP_first_short) {
      cur_bps = (cur_avg_price_ - cur_price) * 10000.0 / cur_price;
      bps_ratio = cur_bps / (stop_profit_atr_n_ * atr_bps_);
      if (bps_ratio > 2 && bps_ratio < 4) {
        tp_ratio = 1.25;
      } else if (bps_ratio > 4 && bps_ratio < 7.5) {
        tp_ratio = 1.5;
      } else if (bps_ratio > 7.5 && bps_ratio < 12) {
        tp_ratio = 2.5;
      } else if (bps_ratio > 12) {
        tp_ratio = 3;
      }
    }
    tp_action_type action = NO_ACTION;
    double TP_trailing_long = tp_hh_ - tp_ratio *
                                           (trailing_profit_atr_n_ * atr_bps_) *
                                           cur_avg_price_ / 10000.0;
    double TP_trailing_short =
        tp_ll_ + tp_ratio * (trailing_profit_atr_n_ * atr_bps_) *
                     cur_avg_price_ / 10000.0;
    if (dir_ == fast_trader_elite::direction_type::BUY) {
      if (cur_price > TP_first_long) {
        if (!log_flag_) {
          log_flag_ = true;
          STRA_LOG(
              data_->logger_, STRA_DEBUG,
              "rule_maker::{} start_tp long price:{:.6f} "
              "ratio:{:.3f} net_ratio:{:.3f} net_pos:{:.4f} atr_bps:{:.2f}",
              __FUNCTION__, cur_price, ratio, net_ratio, net_pos_, atr_bps_);
        }
        tp_status_ = true;
      }
      // 触发追踪止盈, 已有开仓后最大价格, 现价大于开仓后最大价格 ->
      // 开仓后最大价格更新为现价
      if (tp_status_ == true && cur_price > tp_hh_) {
        tp_hh_ = cur_price;
      }
      // 触发追踪止盈, 已有开仓后最大价格, 现价小于 (开仓后最大价格减 - 回撤USD)
      // -> 开空平仓止盈
      else if (tp_status_ == true && cur_price < TP_trailing_long) {
        STRA_LOG(data_->logger_, STRA_DEBUG,
                 "rule_maker::{} end_tp long price:{:.6f} high_price:{:.6f} "
                 "TP_trailing_long:{:.6f} tp_ratio:{:.3f} cur_bps:{:.6f} "
                 "bps_ratio:{:.6f} atr_bps:{:.2f}",
                 __FUNCTION__, cur_price, tp_hh_, TP_trailing_long, tp_ratio,
                 cur_bps, bps_ratio, atr_bps_);
        tp_status_ = false;
        tp_hh_ = 0;
        action = TP_SELL;
        tp_action_flag_ = true;
      }
    }
    if (dir_ == fast_trader_elite::direction_type::SELL) {
      if (cur_price < TP_first_short) {
        if (!log_flag_) {
          log_flag_ = true;
          STRA_LOG(
              data_->logger_, STRA_DEBUG,
              "rule_maker::{} start_tp short price:{:.6f} "
              "ratio:{:.3f} net_ratio:{:.3f} net_pos:{:.4f} atr_bps:{:.2f}",
              __FUNCTION__, cur_price, ratio, net_ratio, net_pos_, atr_bps_);
        }
        tp_status_ = true;
      }
      // 触发追踪止盈, 已有开仓后最小价格, 现价小于开仓后最小价格 ->
      // 开仓后最小价格更新为现价
      if (tp_status_ == true && cur_price < tp_ll_) {
        tp_ll_ = cur_price;
      }
      // 触发追踪止盈, 已有开仓后最大价格, 现价小于 (开仓后最大价格减 - 回撤USD)
      // -> 开空平仓止盈
      else if (tp_status_ == true && cur_price > TP_trailing_short) {
        STRA_LOG(data_->logger_, STRA_DEBUG,
                 "rule_maker::{} end_tp short price:{:.6f} low_price:{:.6f} "
                 "TP_trailing_short:{:.6f} tp_ratio:{:.3f} cur_bps:{:.6f} "
                 "bps_ratio:{:.6f} atr_bps:{:.2f}",
                 __FUNCTION__, cur_price, tp_ll_, TP_trailing_short, tp_ratio,
                 cur_bps, bps_ratio, atr_bps_);
        tp_status_ = false;
        tp_ll_ = 0;
        action = TP_BUY;
        tp_action_flag_ = true;
      }
    }

    return action;
  }

private:
  double stop_profit_atr_n_;
  double trailing_profit_atr_n_;
  data *data_;
  double cur_avg_price_;
  double net_pos_;
  fast_trader_elite::direction_type dir_;
  double tp_hh_;
  double tp_ll_;
  bool tp_status_{false};
  bool tp_action_flag_{false};
  bool log_flag_{false};
  double atr_bps_;
};

class tp_normal {
public:
  void init(double stop_profit_bps, double trailing_profit_bps, data *data) {
    take_profit_bps_ = stop_profit_bps;
    trailing_profit_bps_ = trailing_profit_bps;
    data_ = data;
  }
  void trigger(double cur_price, double avg_price,
               fast_trader_elite::direction_type dir, double net_pos) {
    cur_avg_price_ = avg_price;
    dir_ = dir;
    tp_hh_ = cur_price;
    tp_ll_ = cur_price;
    net_pos_ = net_pos;
    tp_status_ = false;
    tp_action_flag_ = false;
    log_flag_ = false;
  }
  tp_action_type update(double cur_price) {
    if (tp_action_flag_) {
      return NO_ACTION;
    }
    double base_quote_v = data_->quote_base_usdt / cur_price;
    double ratio = 1;
    double net_ratio = std::abs(net_pos_) / base_quote_v;
    if (net_ratio > 1.5 && net_ratio < 3) {
      ratio = 0.8;
    } else if (net_ratio > 3) {
      ratio = 0.6;
    }
    double tp_ratio = 1;
    double TP_first_long =
        cur_avg_price_ + ratio * take_profit_bps_ * cur_avg_price_ / 10000.0;
    double TP_first_short =
        cur_avg_price_ - ratio * take_profit_bps_ * cur_avg_price_ / 10000.0;
    double cur_bps = 0, bps_ratio = 0;
    // 回撤允许范围跟随盈利变大
    if (dir_ == fast_trader_elite::direction_type::BUY &&
        cur_price > TP_first_long) {
      cur_bps = (cur_price - cur_avg_price_) * 10000.0 / cur_avg_price_;
      bps_ratio = cur_bps / take_profit_bps_;
      if (bps_ratio > 2 && bps_ratio < 3) {
        tp_ratio = 1.5;
      } else if (bps_ratio > 3 && bps_ratio < 4) {
        tp_ratio = 2;
      } else if (bps_ratio > 4 && bps_ratio < 5) {
        tp_ratio = 2.5;
      } else if (bps_ratio > 5) {
        tp_ratio = 3;
      }
    }
    if (dir_ == fast_trader_elite::direction_type::SELL &&
        cur_price < TP_first_short) {
      cur_bps = (cur_avg_price_ - cur_price) * 10000.0 / cur_price;
      bps_ratio = cur_bps / take_profit_bps_;
      if (bps_ratio > 2 && bps_ratio < 3) {
        tp_ratio = 1.5;
      } else if (bps_ratio > 3 && bps_ratio < 4) {
        tp_ratio = 2;
      } else if (bps_ratio > 4 && bps_ratio < 5) {
        tp_ratio = 2.5;
      } else if (bps_ratio > 5) {
        tp_ratio = 3;
      }
    }
    tp_action_type action = NO_ACTION;
    double TP_trailing_long =
        tp_hh_ - tp_ratio * trailing_profit_bps_ * cur_avg_price_ / 10000.0;
    double TP_trailing_short =
        tp_ll_ + tp_ratio * trailing_profit_bps_ * cur_avg_price_ / 10000.0;
    if (dir_ == fast_trader_elite::direction_type::BUY) {
      if (cur_price > TP_first_long) {
        if (!log_flag_) {
          log_flag_ = true;
          STRA_LOG(data_->logger_, STRA_DEBUG,
                   "rule_maker::{} start_tp long price:{:.6f} "
                   "ratio:{:.3f} net_ratio:{:.3f} net_pos:{:.4f}",
                   __FUNCTION__, cur_price, ratio, net_ratio, net_pos_);
        }
        tp_status_ = true;
      }
      // 触发追踪止盈, 已有开仓后最大价格, 现价大于开仓后最大价格 ->
      // 开仓后最大价格更新为现价
      if (tp_status_ == true && cur_price > tp_hh_) {
        tp_hh_ = cur_price;
      }
      // 触发追踪止盈, 已有开仓后最大价格, 现价小于 (开仓后最大价格减 - 回撤USD)
      // -> 开空平仓止盈
      else if (tp_status_ == true && cur_price < TP_trailing_long) {
        STRA_LOG(data_->logger_, STRA_DEBUG,
                 "rule_maker::{} end_tp long price:{:.6f} high_price:{:.6f} "
                 "TP_trailing_long:{:.6f} tp_ratio:{:.3f} cur_bps:{:.6f} "
                 "bps_ratio:{:.6f}",
                 __FUNCTION__, cur_price, tp_hh_, TP_trailing_long, tp_ratio,
                 cur_bps, bps_ratio);
        tp_status_ = false;
        tp_hh_ = 0;
        action = TP_SELL;
        tp_action_flag_ = true;
      }
    }
    if (dir_ == fast_trader_elite::direction_type::SELL) {
      if (cur_price < TP_first_short) {
        if (!log_flag_) {
          log_flag_ = true;
          STRA_LOG(data_->logger_, STRA_DEBUG,
                   "rule_maker::{} start_tp short price:{:.6f} "
                   "ratio:{:.3f} net_ratio:{:.3f} net_pos:{:.4f}",
                   __FUNCTION__, cur_price, ratio, net_ratio, net_pos_);
        }
        tp_status_ = true;
      }
      // 触发追踪止盈, 已有开仓后最小价格, 现价小于开仓后最小价格 ->
      // 开仓后最小价格更新为现价
      if (tp_status_ == true && cur_price < tp_ll_) {
        tp_ll_ = cur_price;
      }
      // 触发追踪止盈, 已有开仓后最大价格, 现价小于 (开仓后最大价格减 - 回撤USD)
      // -> 开空平仓止盈
      else if (tp_status_ == true && cur_price > TP_trailing_short) {
        STRA_LOG(data_->logger_, STRA_DEBUG,
                 "rule_maker::{} end_tp short price:{:.6f} low_price:{:.6f} "
                 "TP_trailing_short:{:.6f} tp_ratio:{:.3f} cur_bps:{:.6f} "
                 "bps_ratio:{:.6f}",
                 __FUNCTION__, cur_price, tp_ll_, TP_trailing_short, tp_ratio,
                 cur_bps, bps_ratio);
        tp_status_ = false;
        tp_ll_ = 0;
        action = TP_BUY;
        tp_action_flag_ = true;
      }
    }

    return action;
  }

private:
  double take_profit_bps_;
  double trailing_profit_bps_;
  data *data_;
  double cur_avg_price_;
  double net_pos_;
  fast_trader_elite::direction_type dir_;
  double tp_hh_;
  double tp_ll_;
  bool tp_status_{false};
  bool tp_action_flag_{false};
  bool log_flag_{false};
};

} // namespace fast_trader_elite::strategy