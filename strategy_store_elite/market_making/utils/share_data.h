#pragma once
#include "fast_trader_elite/data_model/field.h"
#include <fmt/core.h>
#include <math.h>

#include <algorithm>
#include <cmath>
#include <cstddef>
#include <deque>

class share_data {
 public:
  void init(size_t md_num, size_t trade_num) {
    md_num_ = md_num;
    trade_num_ = trade_num;
  }

  bool md_ready() { return mds_.size() == md_num_; }
  bool trans_ready() { return trades_.size() == trade_num_; }
  size_t get_md_size() { return mds_.size(); }
  size_t get_trans_size() { return trades_.size(); }

  void feed_md(const fast_trader_elite::depth_market_data_field* f) {
    while (mds_.size() > 0 && mds_.size() >= md_num_) {
      mds_.pop_front();
    }
    mds_.push_back(*f);
  }

  void feed_trans(const fast_trader_elite::transaction_field* f) {
    while (trades_.size() > 0 && trades_.size() >= trade_num_) {
      trades_.pop_front();
    }
    trades_.push_back(*f);
  }

  template <typename Func>
  void for_each_trans(size_t n, Func&& f) {
    if (n > trades_.size()) {
      return;
    }
    auto itr = trades_.end() - n;
    std::for_each(itr, trades_.end(), f);
  }

  template <typename Func>
  void for_each_mds(size_t n, Func&& f) {
    if (n > mds_.size()) {
      return;
    }
    auto itr = mds_.end() - n;
    std::for_each(itr, mds_.end(), f);
  }

  template <typename Func>
  void rolling_md_apply(size_t need_size, int roll_window_size, Func func) {
    if (need_size > mds_.size()) {
      return;
    }
    int begin_pos = mds_.size() - need_size;
    for (size_t i = begin_pos + roll_window_size - 1; i < mds_.size(); ++i) {
      func(mds_.begin() + i - roll_window_size + 1, mds_.begin() + i + 1);
    }
    return;
  }

  const fast_trader_elite::depth_market_data_field& get_nth_md_itr(size_t n) {
    auto itr = mds_.begin();
    return *(itr + n);
  }

  const fast_trader_elite::transaction_field& get_nth_trans_itr(size_t n) {
    auto itr = trades_.begin();
    return *(itr + n);
  }

  const fast_trader_elite::depth_market_data_field& get_rnth_md_itr(size_t n) {
    auto itr = mds_.end();
    return *(itr - n);
  }

  const fast_trader_elite::transaction_field& get_rnth_trans_itr(size_t n) {
    auto itr = trades_.end();
    return *(itr - n);
  }

  const fast_trader_elite::depth_market_data_field& get_last_md() {
    return mds_.back();
  }

  const fast_trader_elite::transaction_field& get_last_trans() {
    return trades_.back();
  }

 private:
  std::deque<fast_trader_elite::depth_market_data_field> mds_;
  std::deque<fast_trader_elite::transaction_field> trades_;
  std::vector<double> rolling_md_results_;
  size_t md_num_;
  size_t trade_num_;
};