#pragma once
#include <fast_trader_elite/data_model/type.h>
#include "math_helper.h"

#include <cstdint>

#include "portfolio_position_manager.h"

class pnl_calculator {
 public:
  void add_trade(fast_trader_elite::trade_field* f, uint16_t portfolio_id) {
    auto pos = portfolio_pos_.add_trade(f, portfolio_id);
    cost_ += f->last_price * f->volume *
             (f->direction == fast_trader_elite::direction_type::BUY ? 1 : -1);
    double net_pos = pos->net_position;
    if ((fast_trader_elite::strategy::math_helper::le(net_pos, 0) &&
         f->direction == fast_trader_elite::direction_type::SELL) ||
        (fast_trader_elite::strategy::math_helper::ge(net_pos, 0) &&
         f->direction == fast_trader_elite::direction_type::BUY)) {
      if ((fast_trader_elite::strategy::math_helper::gt(net_pos, 0) &&
           fast_trader_elite::strategy::math_helper::lt(net_pos - f->volume, 0)) ||
          (fast_trader_elite::strategy::math_helper::lt(net_pos, 0) &&
           (fast_trader_elite::strategy::math_helper::gt(net_pos + f->volume, 0)))) {
        avg_price_ = f->last_price;
      }
      else if (fast_trader_elite::strategy::math_helper::eq(net_pos, 0)) {
        avg_price_ = 0;
      }
      else {
        avg_price_ = ((std::abs(net_pos) - f->volume) * avg_price_ +
                      f->last_price * f->volume) /
                     std::abs(net_pos);
      }
    }
    calculate_hold_avg_price(f, pos);
  }
  void calculate_hold_avg_price(fast_trader_elite::trade_field* f,
                                fast_trader_elite::portfolio_position_field* pos) {
    double net_pos = pos->net_position;
    // 之前是空仓 某个成交导致变成多仓 多仓的部分
    if (fast_trader_elite::strategy::math_helper::gt(net_pos, 0) &&
        fast_trader_elite::strategy::math_helper::lt(net_pos - f->volume, 0)) {
      hold_cost_ = net_pos * f->last_price;
      hold_avg_price_ = f->last_price;
    }
    // 之前是多仓 某个成交导致变成空仓 多仓的部分
    else if (fast_trader_elite::strategy::math_helper::lt(net_pos, 0) &&
             (fast_trader_elite::strategy::math_helper::gt(net_pos + f->volume, 0))) {
      hold_cost_ = net_pos * f->last_price;
      hold_avg_price_ = f->last_price;
    }
    else if (fast_trader_elite::strategy::math_helper::eq(net_pos, 0)) {
      hold_cost_ = 0;
      hold_avg_price_ = 0;
    }
    else {
      hold_cost_ += f->last_price * f->volume *
                    (f->direction == fast_trader_elite::direction_type::BUY ? 1 : -1);
      hold_avg_price_ = hold_cost_ / net_pos;
    }
  }

  double get_net_pos(uint16_t portfolio_id, uint32_t instrument_idx) {
    return portfolio_pos_.get_net_pos(portfolio_id, instrument_idx);
  }
  double get_pnl(double cur_price, double net_pos) {
    return cur_price * net_pos - cost_;
  }

  double get_avg_price() { return avg_price_; }

 private:
  double cost_{0};
  double avg_price_{0};
  double hold_avg_price_{0};
  double hold_cost_{0};
  fast_trader_elite::portfolio_position_manager portfolio_pos_;
};