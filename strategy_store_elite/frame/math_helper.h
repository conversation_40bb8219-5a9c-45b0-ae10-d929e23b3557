#pragma once

#include <math.h>

#include <algorithm>
#include <numeric>
namespace fast_trader_elite {
namespace strategy {
namespace math_helper {
inline double round_up(double x, double min_increment) {
  double remainder = std::fmod(x, min_increment);
  if (remainder == 0)
    return x;
  else
    return x + min_increment - remainder;
}

inline double round_down(double x, double min_increment) {
  double remainder = std::fmod(x, min_increment);
  if (remainder == 0)
    return x;
  else
    return std::floor(x / min_increment) * min_increment;
}

inline double round_nearest(double x, double min_increment) {
  double up = round_up(x, min_increment);
  double down = round_down(x, min_increment);
  return (std::fabs(x - down) > std::fabs(up - x)) ? up : down;
}

constexpr double EPSINON = 1e-8;

inline bool eq(double a, double b = 0.0) { return (fabs(a - b) < EPSINON); }
inline bool gt(double a, double b = 0.0) { return a - b > EPSINON; }
inline bool lt(double a, double b = 0.0) { return b - a > EPSINON; }
inline bool ge(double a, double b = 0.0) { return gt(a, b) || eq(a, b); }
inline bool le(double a, double b = 0.0) { return lt(a, b) || eq(a, b); }

// 计算方差
template <typename ForwardIt>
inline double calculate_avg(ForwardIt first, ForwardIt last) {
  auto size = std::distance(first, last);
  if (size <= 1) {  // 样本数量小于或等于1时，直接返回0
    return 0;
  }
  double avg = std::accumulate(first, last, 0.0) / size;  // 计算均值
  return avg;
}
// 计算方差
template <typename ForwardIt>
inline double calculate_var(ForwardIt first, ForwardIt last) {
  using ValueType = typename std::iterator_traits<ForwardIt>::value_type;
  auto size = std::distance(first, last);
  if (size <= 1) {  // 样本数量小于或等于1时，直接返回0
    return 0;
  }

  double avg = std::accumulate(first, last, 0.0) / size;  // 计算均值
  double variance(0);
  std::for_each(first, last, [avg, &variance](const ValueType &num) {
    variance += (num - avg) * (num - avg);
  });
  return variance / size;
}

// 计算标准差
template <typename ForwardIt>
inline double calculate_std(ForwardIt first, ForwardIt last) {
  return std::sqrt(calculate_var(first, last));
}
}  // namespace math_helper
}  // namespace strategy
}  // namespace fast_trader