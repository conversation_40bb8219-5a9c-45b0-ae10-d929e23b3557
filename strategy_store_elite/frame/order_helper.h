#pragma once

#include "fast_trader_elite/data_model/field.h"
#include "fast_trader_elite/data_model/type.h"
#include "i_strategy_ctx.h"
#include <cstddef>
#include <cstdint>
#include <vector>

namespace fast_trader_elite {
namespace strategy {

class order_helper {
public:
  static int64_t insert_market_order(i_strategy_ctx *caller, exchange_type ex,
                                     uint16_t instrument_idx,
                                     uint16_t trading_account_id,
                                     uint16_t portfolio_id, double volume,
                                     direction_type direction,
                                     offset_type offset, double tp_price = -1,
                                     double sl_price = -1) {
    order_input_field order_f;
    order_f.exchange_id = ex;
    order_f.instrument_idx = instrument_idx;
    order_f.trading_account_id = trading_account_id;
    order_f.volume = volume;
    order_f.direction = direction;
    order_f.offset = offset;
    order_f.pricetype = price_type::ANY;
    order_f.time_condition = time_condition_type::GTC;
    order_f.volume_condition = volume_condition_type::ALL;
    order_f.portfolio_id = portfolio_id;
    order_f.take_profit_price = tp_price;
    order_f.stop_loss_price = sl_price;
    return caller->insert_order(&order_f);
  }

  static int64_t close_order_all(i_strategy_ctx *caller, exchange_type ex,
                                 uint16_t instrument_idx,
                                 uint16_t trading_account_id,
                                 uint16_t portfolio_id,
                                 direction_type direction) {
    order_input_field order_f;
    order_f.exchange_id = ex;
    order_f.instrument_idx = instrument_idx;
    order_f.trading_account_id = trading_account_id;
    order_f.volume = 0;
    order_f.direction = direction;
    order_f.portfolio_id = portfolio_id;
    order_f.offset = offset_type::NON;
    return caller->close_order_all(&order_f);
  }

  static int64_t insert_limit_order(
      i_strategy_ctx *caller, exchange_type ex, uint16_t instrument_idx,
      uint16_t trading_account_id, uint16_t portfolio_id, double price,
      double volume, direction_type direction, offset_type offset,
      bool reduce_only = false, double tp_price = -1, double sl_price = -1) {
    order_input_field order_f;
    order_f.exchange_id = ex;
    order_f.instrument_idx = instrument_idx;
    order_f.trading_account_id = trading_account_id;
    order_f.price = price;
    order_f.volume = volume;
    order_f.direction = direction;
    order_f.offset = offset;
    order_f.pricetype = price_type::LIMIT;
    order_f.time_condition = time_condition_type::GTC;
    order_f.volume_condition = volume_condition_type::ANY;
    if (reduce_only) {
      order_f.volume_condition = volume_condition_type::MIN;
    }
    order_f.portfolio_id = portfolio_id;
    order_f.take_profit_price = tp_price;
    order_f.stop_loss_price = sl_price;
    return caller->insert_order(&order_f);
  }
  static int64_t
  insert_maker_only_order(i_strategy_ctx *caller, exchange_type ex,
                          uint16_t instrument_idx, uint16_t trading_account_id,
                          uint16_t portfolio_id, double price, double volume,
                          direction_type direction, offset_type offset,
                          double tp_price = -1, double sl_price = -1) {
    order_input_field order_f;
    order_f.exchange_id = ex;
    order_f.instrument_idx = instrument_idx;
    order_f.trading_account_id = trading_account_id;
    order_f.price = price;
    order_f.volume = volume;
    order_f.direction = direction;
    order_f.offset = offset;
    order_f.pricetype = price_type::LIMIT;
    order_f.time_condition = time_condition_type::GTX;
    order_f.volume_condition = volume_condition_type::ANY;
    order_f.portfolio_id = portfolio_id;
    order_f.take_profit_price = tp_price;
    order_f.stop_loss_price = sl_price;
    return caller->insert_order(&order_f);
  }

  static int64_t cancel_order(i_strategy_ctx *caller, exchange_type ex,
                              uint16_t instrument_idx,
                              uint16_t trading_account_id, int64_t order_id) {
    order_action_field order_f;
    order_f.exchange_id = ex;
    order_f.order_id = order_id;
    order_f.instrument_idx = instrument_idx;
    order_f.trading_account_id = trading_account_id;
    return caller->cancel_order(&order_f);
  }
  static int64_t cancel_order_list(i_strategy_ctx *caller, exchange_type ex,
                                   uint16_t instrument_idx,
                                   uint16_t trading_account_id,
                                   std::vector<int64_t> &ids) {
    if (ids.size() == 0) {
      return -1;
    }
    order_action_list_field order_f;
    order_f.exchange_id = ex;
    order_f.count = ids.size();
    order_f.instrument_idx = instrument_idx;
    order_f.trading_account_id = trading_account_id;
    for (size_t i = 0; i < ids.size(); i++) {
      order_f.order_ids[i] = ids[i];
    }
    return caller->cancel_order_list(&order_f);
  }

  static int64_t cancel_order_list(i_strategy_ctx *caller, exchange_type ex,
                                   std::vector<uint16_t> instrument_idx,
                                   uint16_t trading_account_id,
                                   std::vector<int64_t> &ids) {
    if (ids.size() == 0) {
      return -1;
    }
    order_action_list_field order_f;
    order_f.exchange_id = ex;
    order_f.count = ids.size();
    order_f.instrument_count = instrument_idx.size();
    order_f.trading_account_id = trading_account_id;
    for (size_t i = 0; i < ids.size(); i++) {
      order_f.order_ids[i] = ids[i];
      order_f.instrument_idxs[i] = instrument_idx[i];
    }
    return caller->cancel_order_list(&order_f);
  }

  static int64_t cancel_order_all(i_strategy_ctx *caller, exchange_type ex,
                                  uint16_t instrument_idx,
                                  uint16_t trading_account_id) {
    order_action_field order_f;
    order_f.exchange_id = ex;
    order_f.instrument_idx = instrument_idx;
    order_f.trading_account_id = trading_account_id;
    return caller->cancel_order_all(&order_f);
  }
};
} // namespace strategy
} // namespace fast_trader_elite