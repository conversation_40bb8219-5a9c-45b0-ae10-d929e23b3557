#pragma once
#include "cpp_frame/async_logger/async_log.h"

#include "fmt/format.h"
#include <stdint.h>

#include <array>
#include <cstddef>
#include <cstdint>
#include <cstdio>
#include <cstring>
#include <ctime>
#include <fstream>
#include <functional>
#include <mutex>
#include <string_view>
#include <vector>

#include "cpp_frame/async_logger/file_helper.h"
#include "cpp_frame/async_logger/fmt_async.h"
#include "cpp_frame/async_logger/log_buffer.h"
#include "cpp_frame/async_logger/log_helper.h"
#include "cpp_frame/async_logger/nanots.h"
#include "fmt/args.h" // 添加这个头文件，用于 fmt::dynamic_format_arg_store
#include "fmt/core.h"
#include "i_strategy_ctx.h"

// 检查是否定义了 BACKTEST_MODE 宏
#ifdef BACKTEST_MODE
#define IN_BACKTEST_ENV 1
#else
#define IN_BACKTEST_ENV 0
#endif

namespace fast_trader_elite::strategy {
struct log_info {
  constexpr log_info(const int level, const int line,
                     const std::string_view fmt, const std::string_view file,
                     const std::string_view func, bool is_kv)
      : level(level), line(line), fmt(fmt), file(file), func(func),
        is_kv(is_kv), pos(file.rfind('/')) {}
  const int level;
  const int line;
  const std::string_view fmt;
  const std::string_view file;
  const std::string_view func;
  const bool is_kv;
  const size_t pos;
};

#if IN_BACKTEST_ENV
// 回测环境下的简单同步日志实现
class logger {
public:
  logger() : cur_level_(0), ctx_(nullptr) {}

  ~logger() {
    if (file_.is_open()) {
      file_.close();
    }
  }

  void set_log_file(const char *filename) {
    if (file_.is_open()) {
      file_.close();
    }
    file_.open(filename, std::ios::app);
  }

  void set_log_level(int8_t logLevel) { cur_level_ = logLevel; }

  int8_t get_log_level() { return cur_level_; }

  // 设置策略上下文
  void set_context(i_strategy_ctx *ctx) { ctx_ = ctx; }

  template <typename Context, typename... Args>
  void log(const log_info *info, Args &&...args) {
    if (info->level < cur_level_) {
      return;
    }

    // 获取时间戳
    uint64_t timestamp;
    static int64_t last_valid_timestamp = 0;

    if (ctx_) {
      // 使用类成员变量ctx_获取当前时间戳，并更新最后一次有效的时间戳
      int64_t current_ts = ctx_->get_current_timestamp();
      if (current_ts > 0) {
        last_valid_timestamp = current_ts;
        timestamp = current_ts;
      } else {
        // 如果获取到的时间戳无效，使用最后一次有效的时间戳或系统时间
        timestamp =
            last_valid_timestamp > 0
                ? last_valid_timestamp
                : cpp_frame::async_logger::detail::get_current_nano_sec();
      }
    } else {
      // 如果上下文未设置或已经被销毁，使用最后一次有效的时间戳或系统时间
      timestamp = last_valid_timestamp > 0
                      ? last_valid_timestamp
                      : cpp_frame::async_logger::detail::get_current_nano_sec();
    }

    // 格式化时间
    char time_buf[64];
    format_timestamp(timestamp, time_buf, sizeof(time_buf));

    // 获取日志级别名称
    static const char *level_names[] = {"TRACE", "DEBUG", "INFO ",
                                        "WARN ", "ERROR", "FATAL"};

    // 使用 fmt 库直接格式化整个日志消息
    try {
      std::string log_message = fmt::format(
          "[{}] [{}] [{}:{}] [{}] {}", level_names[info->level], time_buf,
          info->file.substr(info->pos + 1), info->line, info->func,
          fmt::format(fmt::runtime(info->fmt), std::forward<Args>(args)...));

      // 输出到文件或控制台
      if (file_.is_open()) {
        file_ << log_message << std::endl;
        file_.flush();
      } else {
        fmt::print("{}\n", log_message);
        fflush(stdout);
      }
    } catch (const std::exception &e) {
      // 如果格式化失败，输出错误信息
      std::string error_message = fmt::format(
          "[{}] [{}] [{}:{}] [{}] Error formatting log: {}",
          level_names[info->level], time_buf, info->file.substr(info->pos + 1),
          info->line, info->func, e.what());

      if (file_.is_open()) {
        file_ << error_message << std::endl;
        file_.flush();
      } else {
        fmt::print("{}\n", error_message);
        fflush(stdout);
      }
    }
  }

  void poll() {
    // 空实现，同步日志不需要轮询
  }

  void flush() {
    if (file_.is_open()) {
      file_.flush();
    }
  }

private:
  void format_timestamp(int64_t timestamp, char *buf, size_t size) {
    // 将纳秒时间戳转换为可读时间
    time_t seconds = timestamp / 1000000000;
    auto tm = std::localtime(&seconds);
    std::strftime(buf, size, "%Y-%m-%d %H:%M:%S", tm);
  }

  int8_t cur_level_;
  std::ofstream file_;
  i_strategy_ctx *ctx_; // 策略上下文指针
};

#else
// 非回测环境下的原有异步日志实现
class logger {
public:
  logger() : ctx_(nullptr) {
    header_args_.reserve(4096);
    header_args_.resize(6);
    set_arg<0>(fmt::string_view());
    set_arg<1>(fmt::string_view());
    set_arg<2>(fmt::string_view());
    set_arg<3>(fmt::string_view());
    set_arg<4>(1);
    set_arg<5>(fmt::string_view());
    log_buffer_ = new cpp_frame::async_logger::log_buffer();
  }

  ~logger() {
    if (write_buffer_.size()) {
      flush_log_file();
    }
    file_helper_.close();
  }
  template <typename Context, typename... Args>
  inline void log(const log_info *info, Args &&...args) {
    if (info->level < cur_level_) {
      return;
    }

    // 在非回测环境下，直接使用系统时间
    uint64_t timestamp =
        cpp_frame::async_logger::detail::get_current_nano_sec();
    constexpr size_t num_cstring = fmt::detail::count<
        cpp_frame::async_logger::detail::is_cstring<Context, Args>()...>();
    size_t cstring_sizes[std::max(num_cstring, (size_t)1)];
    size_t s = cpp_frame::async_logger::alloc_size_with_cstring_size<
                   fmt::format_context>(cstring_sizes, args...) +
               sizeof(addtion_info);
    auto header = alloc(s);
    if (!header) {
      // todo:set callback
      fprintf(stderr, "queue_full...\n");
      return;
    }
    char *write_pos = (char *)(header + 1);
    addtion_info add_info{timestamp, info};
    memcpy(write_pos, &add_info, sizeof(addtion_info));
    write_pos += sizeof(addtion_info);
    cpp_frame::async_logger::store_with_cstring_size(
        (void *)write_pos, info->fmt, cstring_sizes,
        std::forward<Args>(args)...);

    header->push(s);
  }
  void set_log_file(const char *filename) { file_helper_.open(filename); }
  void set_log_level(int8_t logLevel) { cur_level_ = logLevel; }
  int8_t get_log_level() { return cur_level_; }
  void poll() { poll_inner(); }
  void flush() { file_helper_.flush(); }

  // 设置策略上下文
  void set_context(i_strategy_ctx *ctx) { ctx_ = ctx; }

private:
  struct addtion_info {
    addtion_info(uint64_t ts, const log_info *info) : ts(ts), info(info) {}
    uint64_t ts;
    const log_info *info;
  };

  cpp_frame::async_logger::log_buffer::queue_header *alloc(size_t size) {

    return log_buffer_->alloc(size);
  }
  template <size_t I, typename T> void set_arg(const T &arg) {
    header_args_[I] = fmt::detail::make_arg<fmt::format_context>(arg);
  }
  template <size_t I, typename T> void set_arg_val(const T &arg) {
    fmt::detail::value<fmt::format_context> &value_ =
        *(fmt::detail::value<fmt::format_context> *)&header_args_[I];
    value_ = fmt::detail::arg_mapper<fmt::format_context>().map(arg);
  }
  void flush_log_file() {
    if (file_helper_.is_open()) {
      file_helper_.write(write_buffer_.data(), write_buffer_.size());
      file_helper_.flush();
    }
    write_buffer_.clear();
  }

  void handle_log(fmt::string_view thread_name, const char *data) {
    addtion_info *info = (addtion_info *)(data);
    data += sizeof(addtion_info);
    auto entry =
        (cpp_frame::async_logger::async_entry<fmt::format_context> *)(data);
    static const std::array<fmt::string_view, 6> log_level_names{
        "TRACE", "DEBUG", "INFO ", "WARN ", "ERROR", "FATAL"};
    set_arg_val<0>(thread_name);
    set_arg_val<1>(log_level_names[info->info->level]);
    set_arg_val<2>(ts_.convert(info->ts));
    set_arg_val<3>(info->info->file.substr(info->info->pos + 1));
    set_arg_val<4>(info->info->line);
    set_arg_val<5>(info->info->func);
    if (!info->info->is_kv) {
      static fmt::string_view header_pattern{"[{}] [{}] [{}] [{}:{}] [{}] "};
      fmt::detail::vformat_to(write_buffer_, header_pattern,
                              fmt::basic_format_args(header_args_.data(), 6));
    } else {
      static fmt::string_view kv_header_pattern{
          "ts={} level={} pid={} file={}:{} "};
      fmt::detail::vformat_to(write_buffer_, kv_header_pattern,
                              fmt::basic_format_args(header_args_.data(), 5));
    }
    try {
      format_to(*entry, fmt::appender(write_buffer_));
    } catch (...) {
      fmt::format_to(fmt::appender(write_buffer_), "format error");
    }
    write_buffer_.push_back('\n');
    if (file_helper_.is_open()) {
      if (write_buffer_.size() >= flush_buf_size_) {
        flush_log_file();
      }
    } else {
      fwrite(write_buffer_.data(), 1, write_buffer_.size(), stdout);
    }
  }

  void poll_inner() {
    // 在非回测环境下，直接使用系统时间
    uint64_t ts = cpp_frame::async_logger::detail::get_current_nano_sec();

    while (true) {
      auto header = log_buffer_->front();
      if (!header)
        break;

      addtion_info *info = (addtion_info *)((const char *)(header + 1));

      if (info->ts >= ts) {
        break;
      }

      const char *data = (const char *)(header + 1);
      handle_log(log_buffer_->get_name(), data);
      log_buffer_->pop();
    }
    flush_log_file();
  }

private:
  int8_t cur_level_{0};
  cpp_frame::async_logger::file_helper file_helper_;
  cpp_frame::async_logger::nanots ts_;

  cpp_frame::async_logger::log_buffer *log_buffer_{nullptr};
  using buffer = fmt::basic_memory_buffer<char, 10000>;
  buffer write_buffer_;
  uint32_t flush_buf_size_{8 * 1024};
  std::vector<fmt::basic_format_arg<fmt::format_context>> header_args_;
  i_strategy_ctx *ctx_{nullptr}; // 策略上下文指针
};
#endif // IN_BACKTEST_ENV

} // namespace fast_trader_elite::strategy

enum {
  STRA_TRACE = 0,
  STRA_DEBUG = 1,
  STRA_INFO = 2,
  STRA_WARN = 3,
  STRA_ERROR = 4,
  STRA_FATAL = 5,
  NUM_STRA_LOG_LEVELS
};

#define SET_STRA_LOG_FILE(logger, file)                                        \
  do {                                                                         \
    logger.set_log_file(file);                                                 \
  } while (0)

#define SET_STRA_LOG_LEVEL(logger, level)                                      \
  do {                                                                         \
    logger.set_log_level(level);                                               \
  } while (0)

#define STRA_LOG(logger, level, format, ...)                                   \
  do {                                                                         \
    static constexpr fast_trader_elite::strategy::log_info                     \
        async_logger_log_info{level,    __LINE__,     format,                  \
                              __FILE__, __FUNCTION__, false};                  \
    logger.log<fmt::format_context>(&async_logger_log_info, ##__VA_ARGS__);    \
  } while (0)

#define STRA_POLL(logger)                                                      \
  do {                                                                         \
    logger.poll();                                                             \
  } while (0)
