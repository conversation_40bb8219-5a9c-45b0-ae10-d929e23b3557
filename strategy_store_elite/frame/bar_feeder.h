#pragma once
#include <cpp_frame/utils/date.h>

#include <cstddef>
#include <cstdint>
#include <functional>
#include <vector>

#include "fast_trader_elite/data_model/field.h"

namespace fast_trader_elite {
namespace strategy {

struct bar {
  double high = 0;
  double low = 0;
  double open = 0;
  double close = 0;
  double volume = 0;
  int64_t start_time = 0;
  int64_t end_time = 0;
  int tick_cnt = 0;
};

class bar_record {
public:
  void push_back(const bar &r) {
    rs.push_back(r);
    open.push_back(r.open);
    close.push_back(r.close);
    high.push_back(r.high);
    low.push_back(r.low);
  }

  void erase(std::vector<bar>::iterator it) {
    int pos = it - rs.begin();
    rs.erase(it);
    open.erase(open.begin() + pos);
    close.erase(close.begin() + pos);
    high.erase(high.begin() + pos);
    low.erase(low.begin() + pos);
  }
  size_t size() { return rs.size(); }

  bar &operator[](size_t i) {
    if (i > rs.size()) {
      return rs[0];
    }
    return rs[i];
  }
  std::vector<bar>::iterator begin() { return rs.begin(); }
  std::vector<bar>::iterator end() { return rs.end(); }

  std::vector<double> &get_close() { return close; }
  std::vector<double> &get_open() { return open; }
  std::vector<double> &get_high() { return high; }
  std::vector<double> &get_low() { return low; }

private:
  std::vector<bar> rs;
  std::vector<double> high;
  std::vector<double> low;
  std::vector<double> open;
  std::vector<double> close;
};

class bar_feeder {
public:
  using CB_FUNC = std::function<void(const bar &bar)>;
  // period 秒
  void init_bar(int64_t period, int max_count, const CB_FUNC &func) {
    period_ = period * 1e9;
    cb_func_ = func;
    max_count_ = max_count;
    auto cur_ts = cpp_frame::date::get_current_nano_sec();
    current_bar_.start_time = cur_ts - cur_ts % period_ + period_;
    current_bar_.end_time = current_bar_.start_time + period_;
  }

  // recv ns
  void feed(double price, int64_t recv_time) {
    if (current_bar_.start_time <= recv_time &&
        current_bar_.end_time >= recv_time) {
      if (current_bar_.tick_cnt == 0) {
        current_bar_.open = price;
        current_bar_.close = price;
        current_bar_.high = price;
        current_bar_.low = price;
      }
      current_bar_.tick_cnt++;
      current_bar_.high = std::max(current_bar_.high, price);
      current_bar_.low = std::min(current_bar_.low, price);
      current_bar_.close = price;
    }
    if (current_bar_.end_time <= recv_time) {
      bar_record_.push_back(current_bar_);
      cb_func_(current_bar_);
      if (bar_record_.size() > max_count_) {
        bar_record_.erase(bar_record_.begin());
      }
      current_bar_.start_time = current_bar_.end_time;
      // 休盘
      while (current_bar_.start_time + period_ < recv_time) {
        current_bar_.start_time += period_;
      }
      current_bar_.end_time = current_bar_.start_time + period_;
      if (current_bar_.start_time <= recv_time) {
        current_bar_.tick_cnt = 1;
        current_bar_.open = price;
        current_bar_.close = price;
        current_bar_.high = price;
        current_bar_.low = price;
      } else {
        current_bar_.tick_cnt = 0;
        current_bar_.open = 0;
        current_bar_.close = 0;
        current_bar_.high = 0;
        current_bar_.low = 0;
      }
    }
  }
  void feed_bar(bar &f, bool is_close) {
    if (is_close) {
      bar_record_.push_back(f);
      cb_func_(f);
      if (bar_record_.size() > max_count_) {
        bar_record_.erase(bar_record_.begin());
      }
    }
    uint64_t cur_ts = f.start_time;
    current_bar_ = f;
    current_bar_.start_time = cur_ts - cur_ts % period_ + period_;
    current_bar_.end_time = current_bar_.start_time + period_;
  }

  bar_record &get_bar_record() { return bar_record_; }

private:
  bar_record bar_record_;
  bar current_bar_;
  int64_t period_;
  size_t max_count_;
  CB_FUNC cb_func_;
};

} // namespace strategy
} // namespace fast_trader_elite