#pragma once

#include "../strategy_logger.h"
#include "i_executor.h"
#include "i_strategy.h"
#include "position_executor.h"
#include "rate_limiter.h"
#include <cstdint>
#include <memory>
#include <unordered_map>
#include <vector>

namespace fast_trader_elite::strategy {

/**
 * 执行器管理器，管理多个品种的执行器
 */
class executor_manager {
public:
  executor_manager(fast_trader_elite::strategy::logger &logger);
  ~executor_manager();

  /**
   * 添加执行器
   * @param instrument_idx 品种索引
   * @param executor 执行器
   */
  void add_executor(uint16_t instrument_idx, std::unique_ptr<i_executor> executor);

  /**
   * 处理行情数据
   * @param md 行情数据
   */
  void process_market_data(const depth_market_data_field *md);

  /**
   * 处理订单回报
   * @param field 订单回报
   */
  void process_order(const order_field *field);

  /**
   * 处理成交回报
   * @param field 成交回报
   */
  void process_trade(const trade_field *field);

  /**
   * 处理持仓回报
   * @param field 持仓回报
   */
  void process_position(const position_field *field);

  /**
   * 处理HTTP响应
   * @param field HTTP响应
   */
  void process_http_rsp(const http_rsp_field *field);

  /**
   * 获取执行器
   * @param instrument_idx 品种索引
   * @return 执行器指针，如果不存在则返回nullptr
   */
  i_executor *get_executor(uint16_t instrument_idx);

  /**
   * 获取所有执行器
   * @return 执行器指针列表
   */
  std::vector<i_executor *> get_all_executors();

  /**
   * 获取所有已完成的执行器
   * @return 已完成的执行器指针列表
   */
  std::vector<i_executor *> get_completed_executors();

  /**
   * 获取所有未完成的执行器
   * @return 未完成的执行器指针列表
   */
  std::vector<i_executor *> get_incomplete_executors();

  /**
   * 检查是否所有执行器都已完成
   * @return 是否所有执行器都已完成
   */
  bool is_all_completed() const;

  /**
   * 检查是否所有执行器都已执行完毕但未验证持仓
   * @return 是否所有执行器都已执行完毕但未验证持仓
   */
  bool is_all_executed() const;

  /**
   * 获取完成百分比
   * @return 完成百分比
   */
  double get_completion_percentage() const;

  /**
   * 重置所有执行器
   */
  void reset();

  /**
   * 获取执行器数量
   * @return 执行器数量
   */
  size_t get_executor_count() const;

  /**
   * 获取已完成的执行器数量
   * @return 已完成的执行器数量
   */
  size_t get_completed_executor_count() const;

  /**
   * 获取共享的流量控制器
   * @return 流量控制器的共享指针
   */
  std::shared_ptr<rate_limiter> get_rate_limiter() const;

private:
  std::unordered_map<uint16_t, std::unique_ptr<i_executor>>
      executors_; // 执行器映射
  fast_trader_elite::strategy::logger &logger_; // 日志器
  std::shared_ptr<rate_limiter> rate_limiter_; // 共享的流量控制器
  uint32_t next_executor_id_{1}; // 下一个可用的执行器ID，从1开始分配
};

} // namespace fast_trader_elite::strategy
