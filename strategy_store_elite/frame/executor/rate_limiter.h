#pragma once

#include <cstdint>
#include <queue>

namespace fast_trader_elite::strategy {

class rate_limiter {
public:
  rate_limiter(int max_requests_per_window, uint64_t window_size_ns);
  ~rate_limiter() = default;

  bool try_acquire(uint64_t current_time);
  int get_available_tokens(uint64_t current_time);
  void reset();

private:
  void cleanup_expired_timestamps(uint64_t current_time);

public:
  int max_requests_per_window;             // 窗口内最大请求数
  uint64_t window_size_ns;                 // 窗口大小（纳秒）
  std::queue<uint64_t> request_timestamps; // 请求时间戳队列
};

} // namespace fast_trader_elite::strategy
