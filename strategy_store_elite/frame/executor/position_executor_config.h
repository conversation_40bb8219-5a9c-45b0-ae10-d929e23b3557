#pragma once

#include "fast_trader_elite/data_model/type.h"
#include <cstdint>
#include <string>

namespace fast_trader_elite::strategy {
class position_executor_config {
public:
  position_executor_config() = default;

  bool init(uint16_t instrument_idx, std::string instrument_id,
            double price_tick_size, double volume_step_size,
            double initial_position, double target_volume,
            exchange_type exchange_id, uint16_t trading_account_id,
            uint16_t portfolio_id, uint64_t max_self_order_time = 10 * 1e9) {
    this->instrument_idx = instrument_idx;
    this->instrument_id = instrument_id;
    this->price_tick_size = price_tick_size;
    this->volume_step_size = volume_step_size;
    this->initial_position = initial_position;
    this->target_volume = target_volume;
    this->exchange_id = exchange_id;
    this->trading_account_id = trading_account_id;
    this->portfolio_id = portfolio_id;
    this->max_self_order_time = max_self_order_time;
    return true;
  }

  uint16_t instrument_idx{0};
  std::string instrument_id;
  double price_tick_size{0.0};
  double volume_step_size{0.0};
  double initial_position{0.0};
  double target_volume{0.0};
  exchange_type exchange_id{exchange_type::UNKNOWN};
  uint16_t trading_account_id{0};
  uint16_t portfolio_id{0};
  uint64_t max_self_order_time{0};

  // 订单超时时间（纳秒）
  static constexpr uint64_t ORDER_TIMEOUT_NS = 1 * 1e9; // 1秒
  // 撤单超时时间（纳秒）
  static constexpr uint64_t CANCEL_TIMEOUT_NS = 2 * 1e9; // 2秒
  // 最大撤单尝试次数
  static constexpr int MAX_CANCEL_ATTEMPTS = 3;
};

} // namespace fast_trader_elite::strategy
