#pragma once

#include "../strategy_logger.h"
#include "fast_trader_elite/data_model/field.h"
#include "fast_trader_elite/data_model/type.h"
#include "i_executor.h"
#include "i_strategy.h"
#include "position_executor_config.h"
#include "rate_limiter.h"
#include <cstdint>
#include <string>

namespace fast_trader_elite::strategy {

/**
 * 仓位执行器，实现 i_executor 接口
 */
class position_executor : public i_executor {
public:
  position_executor(i_strategy_ctx *ctx,
                    fast_trader_elite::strategy::logger &logger);
  ~position_executor() override = default;

  /**
   * 初始化执行器
   * @param config 执行器配置
   * @return 是否初始化成功
   */
  bool init() override;

  /**
   * 使用配置初始化执行器
   * @param config 执行器配置
   * @return 是否初始化成功
   */
  bool init(const position_executor_config &config);

  /**
   * 设置流量控制器
   * @param limiter 流量控制器的共享指针
   */
  void set_rate_limiter(std::shared_ptr<rate_limiter> limiter);

  // 实现 i_executor 接口
  void process_market_data(const depth_market_data_field *md) override;
  void process_order(const order_field *field) override;
  void process_trade(const trade_field *field) override;
  void reset() override;
  std::string get_status_description() const override;

  // 获取属性的接口实现
  uint16_t get_instrument_idx() const override {
    return config_.instrument_idx;
  }
  std::string get_instrument_id() const override {
    return config_.instrument_id;
  }
  double get_target_volume() const override { return config_.target_volume; }
  double get_traded_volume() const override { return traded_volume_; }
  executor_status get_status() const override { return status_; }
  int get_retry_count() const override { return retry_count_; }

  // 设置和获取执行器ID
  void set_executor_id(uint32_t id) { executor_id_ = id; }
  uint32_t get_executor_id() const { return executor_id_; }

  // 获取当前实际持仓
  double get_current_position() const { return current_position_; }

  // 处理持仓回报
  void process_position(const position_field *field) override;

  // 处理HTTP响应
  void process_http_rsp(const http_rsp_field *field) override;



  // 查询当前持仓
  void query_position();

  // 检查持仓查询是否正在进行
  bool is_position_check_in_progress() const { return position_check_in_progress_; }

private:
  void create_order(const depth_market_data_field *md);
  void check_cancel_order(uint64_t current_time);
  bool send_cancel_order(uint64_t current_time);
  bool try_acquire_token(uint64_t current_time);

  // 检查持仓是否匹配，如果不匹配则重新初始化执行器
  bool check_position(double current_position);

  // 判断是否为reduce-only订单
  bool is_reduce_only_order(double initial_pos, double target_pos) const;

  // 判断订单量是否满足最小步长要求
  bool is_volume_valid(double volume) const;

  direction_type direction_{direction_type::BUY};
  double traded_volume_{0.0};
  double abs_target_pos_{0.0};
  int64_t active_order_id_{0};
  uint64_t order_create_time_{0};
  int retry_count_{0};
  int consecutive_insert_errors_{0}; // 连续的INSERT_ERROR错误计数
  executor_status status_{executor_status::IDLE};

  fast_trader_elite::strategy::logger &logger_;
  i_strategy_ctx *ctx_;
  int cancel_count_{0};
  uint64_t cancel_request_time_{0};
  bool is_cancelling_{false};
  position_executor_config config_;
  std::shared_ptr<rate_limiter> rate_limiter_;
  double mp_{0};

  double order_price_;

  // 记录开始发单时的买一卖一价格和成交均价
  double initial_bid_price_{0.0};  // 开始发单时的买一价格
  double initial_ask_price_{0.0};  // 开始发单时的卖一价格
  double total_trade_amount_{0.0}; // 成交总金额
  double avg_trade_price_{0.0};    // 成交均价
  uint64_t initial_ts_{0};       // 最初报价时间

  // 持仓查询相关
  double current_position_{0.0}; // 当前实际持仓
  int position_check_retry_count_{0}; // 持仓检查重试次数
  uint64_t position_check_request_id_{0}; // 持仓查询请求ID
  bool position_check_in_progress_{false}; // 持仓查询是否正在进行
  uint32_t executor_id_{0}; // 执行器唯一ID

  static constexpr uint64_t ORDER_TIMEOUT_NS =
      position_executor_config::ORDER_TIMEOUT_NS;
  static constexpr uint64_t CANCEL_TIMEOUT_NS =
      position_executor_config::CANCEL_TIMEOUT_NS;
  static constexpr int MAX_CANCEL_ATTEMPTS =
      position_executor_config::MAX_CANCEL_ATTEMPTS;
};

} // namespace fast_trader_elite::strategy
