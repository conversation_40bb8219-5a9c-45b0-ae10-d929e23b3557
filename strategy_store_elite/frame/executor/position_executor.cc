#include "position_executor.h"
#include "../math_helper.h"
#include "../order_helper.h"
#include "cpp_frame/struct_serialize/struct_ser.h"
#include "fast_trader_elite/data_model/type.h"
#include "position_executor_config.h"
#include <cstdint>
#include <cstdlib> // for std::rand
namespace fast_trader_elite::strategy {
#define ORDER_USDT_MIN 5.3
#define MAX_ORDER_ERROR_CNT 5

position_executor::position_executor(
    i_strategy_ctx *ctx, fast_trader_elite::strategy::logger &logger)
    : logger_(logger), ctx_(ctx) {
  // 初始化持仓查询相关变量
  position_check_retry_count_ = 0;
  current_position_ = 0.0;
  consecutive_insert_errors_ = 0;

  // 初始化执行器ID为一个随机值，确保唯一性
  executor_id_ = static_cast<uint32_t>(std::rand());
}

bool position_executor::init() { return true; }

void position_executor::set_rate_limiter(
    std::shared_ptr<rate_limiter> limiter) {
  rate_limiter_ = limiter;
  if (!config_.instrument_id.empty()) {
    STRA_LOG(logger_, STRA_INFO,
             "rate_limiter_set instrument:{} status:success",
             config_.instrument_id);
  }
}

bool position_executor::init(const position_executor_config &config) {
  config_ = config;

  double position_change = config_.target_volume - config_.initial_position;
  abs_target_pos_ = std::abs(position_change);
  direction_ =
      (position_change >= 0) ? direction_type::BUY : direction_type::SELL;
  STRA_LOG(logger_, STRA_INFO,
           "executor_initialized instrument:{} initial_position:{} "
           "target_volume:{} position_change:{} direction:{}",
           config_.instrument_id, config_.initial_position,
           config_.target_volume, position_change,
           direction_ == direction_type::BUY ? "buy" : "sell");

  traded_volume_ = 0.0;
  active_order_id_ = 0;
  order_create_time_ = 0;
  retry_count_ = 0;
  status_ = executor_status::IDLE;

  cancel_count_ = 0;
  cancel_request_time_ = 0;
  is_cancelling_ = false;

  // 初始化价格记录相关变量
  initial_bid_price_ = 0.0;
  initial_ask_price_ = 0.0;
  total_trade_amount_ = 0.0;
  avg_trade_price_ = 0.0;

  return true;
}

void position_executor::process_market_data(const depth_market_data_field *md) {
  mp_ = (md->ask_price[0] + md->bid_price[0]) / 2;
  uint64_t recv_time = md->local_timestamp;
  if (status_ == executor_status::EXECUTED ||
      status_ == executor_status::COMPLETED ||
      status_ == executor_status::FAILED) {
    return;
  }
  // 存在订单 检查撤单超时/ 到达撤单时间撤单
  if (active_order_id_ > 0) {
    if (is_cancelling_) {
      check_cancel_order(recv_time);
    } else {
      bool ts_flag = recv_time > order_create_time_ + ORDER_TIMEOUT_NS;
      double cur_price = direction_ == direction_type::BUY ? md->bid_price[0]
                                                           : md->ask_price[0];
      bool price_flag = !math_helper::eq(cur_price, order_price_);
      if (ts_flag && price_flag) {
        send_cancel_order(recv_time);
        cancel_count_ = 0;
      }
    }
  } else {
    create_order(md);
  }
}

void position_executor::process_order(const order_field *field) {
  if (field->order_status == order_status_type::CANCELLED ||
      field->order_status == order_status_type::PARTIAL_FILLED_NOT_ACTIVE) {
    STRA_LOG(logger_, STRA_INFO, "order_cancel instrument:{} order_id:{}",
             config_.instrument_id, field->order_id);
    active_order_id_ = 0;
    is_cancelling_ = false;
    cancel_count_ = 0;
    retry_count_++;
  } else if (field->order_status == order_status_type::ERROR_CANCEL) {
    STRA_LOG(logger_, STRA_WARN, "order_cancel_error instrument:{} order_id:{}",
             config_.instrument_id, field->order_id);
    retry_count_++;
    if (field->error_id == 110001 &&
        field->exchange_id == exchange_type::BYBIT) {
      STRA_LOG(logger_, STRA_WARN,
               "order_cancel_error_110001 instrument:{} order_id:{}",
               config_.instrument_id, field->order_id);
      query_position();
    }

    if (is_cancelling_ && cancel_count_ < MAX_CANCEL_ATTEMPTS) {
      STRA_LOG(logger_, STRA_INFO, "cancel_retry_later attempts:{}/{}",
               cancel_count_, MAX_CANCEL_ATTEMPTS);
    } else {
      STRA_LOG(logger_, STRA_WARN, "cancel_max_attempts_reached");
      active_order_id_ = 0;
      is_cancelling_ = false;
      cancel_count_ = 0;
    }
  } else if (field->order_status == order_status_type::ERROR_INSERT) {
    STRA_LOG(logger_, STRA_ERROR,
             "insert_order_error instrument:{} order_id:{}",
             config_.instrument_id, field->order_id);
    active_order_id_ = 0;
    retry_count_++;
    consecutive_insert_errors_++;
    if (field->error_id == 110017 &&
        field->exchange_id == exchange_type::BYBIT) {
      STRA_LOG(logger_, STRA_ERROR,
               "insert_order_error_1100117 instrument:{} order_id:{}",
               config_.instrument_id, field->order_id);
    }

    if (consecutive_insert_errors_ >= MAX_ORDER_ERROR_CNT) {
      status_ = executor_status::FAILED;
      STRA_LOG(
          logger_, STRA_ERROR,
          "consecutive_insert_errors instrument:{} error_count:{} max_errors:5",
          config_.instrument_id, consecutive_insert_errors_);
    }
  }
}

void position_executor::process_trade(const trade_field *field) {
  STRA_LOG(
      logger_, STRA_INFO,
      "recv_trade instrument:{} order_id:{} direction:{} trade:{:.6f}@{:.6f}",
      config_.instrument_id, field->order_id,
      direction_ == direction_type::BUY ? "buy" : "sell", field->last_price,
      field->volume);

  // 更新成交量和成交金额
  traded_volume_ += field->volume;
  total_trade_amount_ += field->last_price * field->volume;

  // 计算成交均价
  if (traded_volume_ > 0) {
    avg_trade_price_ = total_trade_amount_ / traded_volume_;
  }

  if (field->order_status == order_status_type::FILLED) {
    STRA_LOG(logger_, STRA_INFO, "order_filled instrument:{} order_id:{} ",
             config_.instrument_id, field->order_id);
    active_order_id_ = 0;
    is_cancelling_ = false;
    cancel_count_ = 0;
  }
  double remaining_volume = abs_target_pos_ - traded_volume_;
  if (math_helper::eq(remaining_volume, 0)) {
    status_ = executor_status::EXECUTED; // 已执行完毕但未验证持仓
    double position_change = config_.target_volume - config_.initial_position;
    STRA_LOG(
        logger_, STRA_INFO,
        "target_executed instrument:{} initial_position:{} target_volume:{} "
        "position_change:{} traded_volume:{} remaining:{} "
        "initial_bid_price:{:.6f} initial_ask_price:{:.6f} "
        "avg_trade_price:{:.6f}",
        config_.instrument_id, config_.initial_position, config_.target_volume,
        position_change, traded_volume_, remaining_volume, initial_bid_price_,
        initial_ask_price_, avg_trade_price_);

    // 自动查询持仓进行验证
    query_position();
  }
}

bool position_executor::send_cancel_order(uint64_t current_time) {
  if (!try_acquire_token(current_time)) {
    STRA_LOG(logger_, STRA_WARN,
             "rate_limit_exceeded instrument:{} order_id:{}",
             config_.instrument_id, active_order_id_);
    return false;
  }

  order_helper::cancel_order(ctx_, config_.exchange_id, config_.instrument_idx,
                             config_.trading_account_id, active_order_id_);

  is_cancelling_ = true;
  cancel_count_++;
  cancel_request_time_ = current_time;
  status_ = executor_status::CANCELLING;

  STRA_LOG(logger_, STRA_INFO,
           "cancel_order instrument:{} order_id:{} cancel_cnt:{}",
           config_.instrument_id, active_order_id_, cancel_count_);

  return true;
}

void position_executor::check_cancel_order(uint64_t current_time) {
  if (current_time - cancel_request_time_ > CANCEL_TIMEOUT_NS) {
    if (cancel_count_ < MAX_CANCEL_ATTEMPTS) {
      send_cancel_order(current_time);
    } else {
      STRA_LOG(logger_, STRA_WARN,
               "cancel_retry_timeout instrument:{} order_id:{} "
               "attempts:{} max_attempts:{}",
               config_.instrument_id, active_order_id_, cancel_count_,
               MAX_CANCEL_ATTEMPTS);
      active_order_id_ = 0;
      is_cancelling_ = false;
      cancel_count_ = 0;
      status_ = executor_status::EXECUTING;
    }
  }
}

void position_executor::create_order(const depth_market_data_field *md) {
  if (status_ == executor_status::COMPLETED ||
      status_ == executor_status::FAILED || active_order_id_ > 0 ||
      position_check_in_progress_) {
    return;
  }
  uint64_t current_time = md->local_timestamp;
  // 记录开始发单时的买一卖一价格（只在第一次发单时记录）
  if (traded_volume_ == 0.0 && retry_count_ == 0) {
    initial_bid_price_ = md->bid_price[0];
    initial_ask_price_ = md->ask_price[0];
    initial_ts_ = md->local_timestamp;
    STRA_LOG(logger_, STRA_INFO,
             "initial_prices_recorded instrument:{} initial_bid_price:{:.6f} "
             "initial_ask_price:{:.6f}",
             config_.instrument_id, initial_bid_price_, initial_ask_price_);
  }

  double order_volume = abs_target_pos_ - traded_volume_;
  double price;
  double spread = md->ask_price[0] - md->bid_price[0];
  if (current_time > initial_ts_ + config_.max_self_order_time) {
    price = (direction_ == direction_type::BUY) ? md->ask_price[0]
                                                : md->bid_price[0];
  } else {
    if (math_helper::gt(spread, config_.price_tick_size)) {
      price = (md->bid_price[0] + md->ask_price[0]) / 2;
    } else {
      price = (direction_ == direction_type::SELL) ? md->ask_price[0]
                                                   : md->bid_price[0];
    }
  }
  bool is_reduce_only =
      is_reduce_only_order(config_.initial_position, config_.target_volume);
  double order_usdt = price * order_volume;
  if (order_usdt < ORDER_USDT_MIN) {
    // 检查是否是reduce-only订单
    // 检查订单量是否满足最小步长要求
    bool volume_valid = is_volume_valid(order_volume);

    if (is_reduce_only && volume_valid) {
      STRA_LOG(
          logger_, STRA_INFO,
          "reduce_only_order_amount_less_than_5 amount:{} instrument:{} "
          "price:{:.6f} "
          "order_volume:{:.6f} initial_position:{:.6f} target_volume:{:.6f}",
          order_usdt, config_.instrument_id, price, order_volume,
          config_.initial_position, config_.target_volume);
      // 对于reduce-only订单，且订单量满足最小步长要求，即使金额小于最小限制，也继续执行
    } else {
      STRA_LOG(logger_, STRA_INFO,
               "order_amount_less_than_5 amount:{} instrument:{} price:{:.6f} "
               "order_volume:{:.6f} is_reduce_only:{} volume_valid:{}",
               order_usdt, config_.instrument_id, price, order_volume,
               is_reduce_only ? "true" : "false",
               volume_valid ? "true" : "false");
      status_ = executor_status::EXECUTED;

      // 自动查询持仓进行验证
      query_position();
      return;
    }
  }

  if (!try_acquire_token(md->local_timestamp)) {
    STRA_LOG(logger_, STRA_WARN, "insert_order_rate_limit instrument:{}",
             config_.instrument_id);
    return;
  }

  int64_t order_id = order_helper::insert_limit_order(
      ctx_, config_.exchange_id, config_.instrument_idx,
      config_.trading_account_id, config_.portfolio_id,
      math_helper::round_nearest(price, config_.price_tick_size),
      math_helper::round_nearest(order_volume, config_.volume_step_size),
      direction_, offset_type::NON, is_reduce_only);

  if (order_id > 0) {
    active_order_id_ = order_id;
    order_create_time_ = md->local_timestamp;
    status_ = executor_status::EXECUTING;
    order_price_ = price;
    STRA_LOG(logger_, STRA_INFO,
             "order_created instrument:{} order_id:{} direction:{} "
             "order:{:.6f}@{:.6f} reduce_only:{}",
             config_.instrument_id, order_id,
             direction_ == direction_type::BUY ? "buy" : "sell", price,
             order_volume, is_reduce_only ? "true" : "false");
  } else {
    STRA_LOG(logger_, STRA_ERROR, "check trading_account_id");
  }
}

bool position_executor::try_acquire_token(uint64_t current_time) {
  if (!rate_limiter_) {
    return true;
  }

  bool result = rate_limiter_->try_acquire(current_time);
  int available_tokens = rate_limiter_->get_available_tokens(current_time);
  STRA_LOG(
      logger_, STRA_DEBUG,
      "rate_limit_check instrument:{} available_tokens:{} result:{} status:{}",
      config_.instrument_id, available_tokens, result ? "allowed" : "blocked",
      result ? "success" : "rate_limited");
  return result;
}

void position_executor::reset() {
  double position_change = config_.target_volume - config_.initial_position;
  abs_target_pos_ = std::abs(position_change);
  direction_ =
      (position_change >= 0) ? direction_type::BUY : direction_type::SELL;

  traded_volume_ = 0.0;
  active_order_id_ = 0;
  order_create_time_ = 0;
  retry_count_ = 0;
  consecutive_insert_errors_ = 0;
  status_ = executor_status::IDLE;

  is_cancelling_ = false;
  cancel_count_ = 0;
  cancel_request_time_ = 0;

  // 重置价格记录相关变量
  initial_bid_price_ = 0.0;
  initial_ask_price_ = 0.0;
  total_trade_amount_ = 0.0;
  avg_trade_price_ = 0.0;

  // 重置持仓查询相关变量
  position_check_retry_count_ = 0;
  position_check_request_id_ = 0;
  position_check_in_progress_ = false;
  // 不重置executor_id_，保持唯一性

  // 检查rate_limiter_是否已设置
  if (rate_limiter_) {
    rate_limiter_->reset();
  } else {
    STRA_LOG(logger_, STRA_WARN,
             "rate_limiter_not_set_for_reset instrument:{} status:warning",
             config_.instrument_id);
  }
}

std::string position_executor::get_status_description() const {
  switch (status_) {
  case executor_status::IDLE:
    return "IDLE";
  case executor_status::EXECUTING:
    return "EXECUTING";
  case executor_status::CANCELLING:
    return "CANCELLING";
  case executor_status::EXECUTED:
    return "EXECUTED";
  case executor_status::COMPLETED:
    return "COMPLETED";
  case executor_status::FAILED:
    return "FAILED";
  default:
    return "UNKNOWN";
  }
}

bool position_executor::check_position(double current_position) {
  // 更新当前持仓
  this->current_position_ = current_position;

  // 只有当执行器状态为EXECUTED时才进行检查
  if (status_ != executor_status::EXECUTED) {
    STRA_LOG(logger_, STRA_INFO,
             "position_check_skipped instrument:{} status:{} "
             "current_position:{:.6f}",
             config_.instrument_id, (int)status_, current_position);
    return false;
  }
  double diff_v = std::abs(config_.target_volume - current_position);
  double order_usdt = mp_ * diff_v;

  // 检查是否是reduce-only订单
  bool is_reduce_only =
      is_reduce_only_order(current_position, config_.target_volume);
  // 检查订单量是否满足最小步长要求
  bool volume_valid = is_volume_valid(diff_v);

  bool order_usdt_small = order_usdt < ORDER_USDT_MIN;
  if (order_usdt_small && (!is_reduce_only || !volume_valid)) {
    status_ = executor_status::COMPLETED;
    STRA_LOG(logger_, STRA_INFO,
             "position_check_success instrument:{} target_position:{:.6f} "
             "current_position:{:.6f}",
             config_.instrument_id, config_.target_volume, current_position);
    return true;
  } else {
    // 持仓不匹配或者是reduce-only订单，需要重新初始化执行器继续下单
    STRA_LOG(logger_, STRA_WARN,
             "position_mismatch_detected instrument:{} target_position:{:.6f} "
             "current_position:{:.6f}",
             config_.instrument_id, config_.target_volume, current_position);

    // 创建新的执行器配置，使用当前实际持仓作为初始持仓
    position_executor_config new_config = config_;
    new_config.initial_position = current_position;

    // 重置执行器
    reset();

    // 使用新配置重新初始化执行器
    init(new_config);

    STRA_LOG(logger_, STRA_INFO,
             "executor_reinitialized instrument:{} initial_position:{:.6f} "
             "target_volume:{:.6f} position_change:{:.6f}",
             config_.instrument_id, current_position, config_.target_volume,
             config_.target_volume - current_position);

    return false;
  }
}

void position_executor::query_position() {
  // 如果已经在查询持仓，则跳过
  if (position_check_in_progress_) {
    STRA_LOG(logger_, STRA_INFO,
             "position_query_already_in_progress instrument:{}",
             config_.instrument_id);
    return;
  }

  // 创建持仓查询请求
  position_req_field position_req;
  position_req.instrument_idx =
      config_.instrument_idx; // 只查询当前执行器的持仓
  position_req.exchange_id = config_.exchange_id;
  position_req.trading_account_id = config_.trading_account_id;

  // 发送持仓查询请求
  position_check_request_id_++; // 递增请求ID
  position_check_in_progress_ = true;

  // 将执行器ID和请求ID组合成一个唯一的请求ID
  // 使用位运算：高32位存储执行器ID，低32位存储请求ID
  uint64_t combined_request_id = (static_cast<uint64_t>(executor_id_) << 32) | position_check_request_id_;

  STRA_LOG(logger_, STRA_INFO, "querying_position instrument:{} executor_id:{} request_id:{} combined_id:{}",
           config_.instrument_id, executor_id_, position_check_request_id_, combined_request_id);

  ctx_->get_postion(&position_req, combined_request_id);
}

void position_executor::process_position(const position_field *field) {
  // 只处理与当前执行器相关的持仓回报
  if (field->instrument_idx != config_.instrument_idx) {
    return;
  }

  STRA_LOG(logger_, STRA_INFO,
           "received_position instrument:{} position:{:.6f}",
           config_.instrument_id, field->net_position);

  // 检查持仓是否匹配
  check_position(field->net_position);
}

void position_executor::process_http_rsp(const http_rsp_field *field) {
  // 只处理持仓查询的响应
  if (field->req_type != req_type_type::REQ_POS) {
    return;
  }

  // 从请求ID中提取执行器ID和原始请求ID
  uint32_t request_executor_id = static_cast<uint32_t>(field->request_id >> 32);
  uint32_t original_request_id = static_cast<uint32_t>(field->request_id & 0xFFFFFFFF);

  // 检查是否是当前执行器发出的请求
  if (position_check_in_progress_ && request_executor_id == executor_id_) {
    position_check_in_progress_ = false;

    if (field->error_id == 0) {
      STRA_LOG(logger_, STRA_INFO,
               "position_query_completed instrument:{} executor_id:{} request_id:{}",
               config_.instrument_id, executor_id_, original_request_id);
    } else {
      STRA_LOG(logger_, STRA_ERROR,
               "position_query_failed instrument:{} executor_id:{} error_id:{} error_msg:{}",
               config_.instrument_id, executor_id_, field->error_id, field->error_msg);

      // 如果查询失败，增加重试次数
      position_check_retry_count_++;

      // 如果重试次数未超过最大值，则重新查询
      if (position_check_retry_count_ < 3) {
        STRA_LOG(logger_, STRA_INFO,
                 "retrying_position_query instrument:{} executor_id:{} retry_count:{}",
                 config_.instrument_id, executor_id_, position_check_retry_count_);
        query_position();
      } else {
        STRA_LOG(
            logger_, STRA_ERROR,
            "position_query_max_retries_reached instrument:{} executor_id:{} max_retries:{}",
            config_.instrument_id, executor_id_, 3);
      }
    }
  } else if (request_executor_id != executor_id_) {
    // 不是当前执行器的请求，忽略
    // STRA_LOG(logger_, STRA_DEBUG,
    //          "position_query_response_ignored instrument:{} my_executor_id:{} request_executor_id:{} request_id:{}",
    //          config_.instrument_id, executor_id_, request_executor_id, original_request_id);
  } else {
    STRA_LOG(logger_, STRA_INFO,
             "position_query_response instrument:{} executor_id:{} request_id:{}",
             config_.instrument_id, executor_id_, original_request_id);
  }
}

bool position_executor::is_reduce_only_order(double initial_pos,
                                             double target_pos) const {
  // 只有当仓位向0方向移动，且不穿过0点时，才视为reduce-only
  // 例如：从1到0.5是reduce-only，从1到-0.5不是reduce-only
  // 从-1到-0.5是reduce-only，从-1到0.5不是reduce-only
  return (initial_pos > 0 && target_pos >= 0 && target_pos < initial_pos) ||
         (initial_pos < 0 && target_pos <= 0 && target_pos > initial_pos);
}

bool position_executor::is_volume_valid(double volume) const {
  // 使用math_helper检查订单量是否大于等于最小步长
  return math_helper::ge(volume, config_.volume_step_size);
}

} // namespace fast_trader_elite::strategy
