#include "rate_limiter.h"

namespace fast_trader_elite::strategy {

rate_limiter::rate_limiter(int max_requests_per_window, uint64_t window_size_ns)
    : max_requests_per_window(max_requests_per_window),
      window_size_ns(window_size_ns) {}

bool rate_limiter::try_acquire(uint64_t current_time) {
  cleanup_expired_timestamps(current_time);
  if (request_timestamps.size() >= max_requests_per_window) {
    return false;
  }
  request_timestamps.push(current_time);
  return true;
}

int rate_limiter::get_available_tokens(uint64_t current_time) {
  while (!request_timestamps.empty() &&
         (current_time - request_timestamps.front() > window_size_ns)) {
    request_timestamps.pop();
  }
  return max_requests_per_window - static_cast<int>(request_timestamps.size());
}

void rate_limiter::reset() {
  while (!request_timestamps.empty()) {
    request_timestamps.pop();
  }
}

void rate_limiter::cleanup_expired_timestamps(uint64_t current_time) {
  while (!request_timestamps.empty() &&
         (current_time - request_timestamps.front() > window_size_ns)) {
    request_timestamps.pop();
  }
}

} // namespace fast_trader_elite::strategy
