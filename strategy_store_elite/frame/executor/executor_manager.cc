#include "executor_manager.h"
#include "position_executor_config.h"

namespace fast_trader_elite::strategy {

executor_manager::executor_manager(fast_trader_elite::strategy::logger &logger)
    : logger_(logger) {
  // 创建共享的流量控制器，限制为1秒10笔报撤单
  rate_limiter_ = std::make_shared<rate_limiter>(10, 1000000000); // 10笔/1秒
  STRA_LOG(logger_, STRA_INFO, "rate_limiter_initialized max_requests:10 window:1s status:success");
}

executor_manager::~executor_manager() {
  // 清理资源
  executors_.clear();
}

// 添加执行器
void executor_manager::add_executor(uint16_t instrument_idx,
                                    std::unique_ptr<i_executor> executor) {
  // 检查是否已存在该品种的执行器
  if (executors_.find(instrument_idx) != executors_.end()) {
    STRA_LOG(logger_, STRA_WARN,
             "executor_already_exists instrument:{} status:duplicate",
             executor->get_instrument_id());
    return;
  }

  // 获取instrument_id用于日志
  std::string instrument_id = executor->get_instrument_id();

  // 分配唯一的执行器ID
  position_executor* pos_executor = dynamic_cast<position_executor*>(executor.get());
  if (pos_executor) {
    pos_executor->set_executor_id(next_executor_id_);
    STRA_LOG(logger_, STRA_INFO,
             "executor_id_assigned instrument:{} executor_id:{}",
             instrument_id, next_executor_id_);
    next_executor_id_++;
  }

  // 添加执行器
  executors_[instrument_idx] = std::move(executor);

  STRA_LOG(logger_, STRA_INFO, "executor_added instrument:{} status:success",
           instrument_id);
}

void executor_manager::process_market_data(const depth_market_data_field *md) {
  // 查找对应品种的执行器
  auto it = executors_.find(md->instrument_idx);
  if (it == executors_.end()) {
    return;
  }

  i_executor *executor = it->second.get();

  // 如果执行器已完成或失败，不处理行情数据
  if (executor->get_status() == executor_status::COMPLETED ||
      executor->get_status() == executor_status::FAILED) {
    return;
  }

  // 直接处理行情数据，流量控制由执行器内部处理
  executor->process_market_data(md);
}

void executor_manager::process_order(const order_field *field) {
  // 查找对应品种的执行器
  auto it = executors_.find(field->instrument_idx);
  if (it == executors_.end()) {
    return; // 没有找到对应的执行器，忽略
  }

  // 处理订单回报
  it->second->process_order(field);

  // 如果所有执行器都已完成，记录日志
  if (is_all_completed()) {
    STRA_LOG(logger_, STRA_INFO, "all_executors_completed status:completed");
  }
}

void executor_manager::process_trade(const trade_field *field) {
  // 查找对应品种的执行器
  auto it = executors_.find(field->instrument_idx);
  if (it == executors_.end()) {
    return; // 没有找到对应的执行器，忽略
  }

  // 处理成交回报
  it->second->process_trade(field);

  // 如果所有执行器都已完成，记录日志
  if (is_all_completed()) {
    STRA_LOG(logger_, STRA_INFO, "all_executors_completed status:completed");
  }
}

void executor_manager::process_position(const position_field *field) {
  // 查找对应品种的执行器
  auto it = executors_.find(field->instrument_idx);
  if (it == executors_.end()) {
    return; // 没有找到对应的执行器，忽略
  }

  // 处理持仓回报
  it->second->process_position(field);
}

void executor_manager::process_http_rsp(const http_rsp_field *field) {
  // 如果是持仓查询响应，转发给所有执行器
  if (field->req_type == req_type_type::REQ_POS) {
    for (auto &pair : executors_) {
      pair.second->process_http_rsp(field);
    }
  }
}

i_executor *executor_manager::get_executor(uint16_t instrument_idx) {
  auto it = executors_.find(instrument_idx);
  if (it == executors_.end()) {
    return nullptr;
  }

  return it->second.get();
}

std::vector<i_executor *> executor_manager::get_all_executors() {
  std::vector<i_executor *> result;
  for (auto &pair : executors_) {
    result.push_back(pair.second.get());
  }

  return result;
}

std::vector<i_executor *> executor_manager::get_completed_executors() {
  std::vector<i_executor *> result;
  for (auto &pair : executors_) {
    if (pair.second->get_status() == executor_status::COMPLETED) {
      result.push_back(pair.second.get());
    }
  }

  return result;
}

std::vector<i_executor *> executor_manager::get_incomplete_executors() {
  std::vector<i_executor *> result;
  for (auto &pair : executors_) {
    if (pair.second->get_status() != executor_status::COMPLETED) {
      result.push_back(pair.second.get());
    }
  }

  return result;
}

bool executor_manager::is_all_completed() const {
  for (const auto &pair : executors_) {
    if (pair.second->get_status() != executor_status::COMPLETED) {
      return false;
    }
  }

  return !executors_.empty();
}

bool executor_manager::is_all_executed() const {
  for (const auto &pair : executors_) {
    if (pair.second->get_status() != executor_status::EXECUTED &&
        pair.second->get_status() != executor_status::COMPLETED) {
      return false;
    }
  }

  return !executors_.empty();
}

double executor_manager::get_completion_percentage() const {
  if (executors_.empty()) {
    return 0.0;
  }

  int completed_count = 0;
  for (const auto &pair : executors_) {
    if (pair.second->get_status() == executor_status::COMPLETED ||
        pair.second->get_status() == executor_status::EXECUTED) {
      completed_count++;
    }
  }

  return static_cast<double>(completed_count) / executors_.size() * 100.0;
}

void executor_manager::reset() {
  for (auto &pair : executors_) {
    pair.second->reset();
  }
  STRA_LOG(logger_, STRA_INFO, "executors_reset count:{} status:reset",
           executors_.size());
}

size_t executor_manager::get_executor_count() const {
  return executors_.size();
}

size_t executor_manager::get_completed_executor_count() const {
  size_t completed_count = 0;
  for (const auto &pair : executors_) {
    if (pair.second->get_status() == executor_status::COMPLETED ||
        pair.second->get_status() == executor_status::EXECUTED) {
      completed_count++;
    }
  }

  return completed_count;
}

std::shared_ptr<rate_limiter> executor_manager::get_rate_limiter() const {
  return rate_limiter_;
}

} // namespace fast_trader_elite::strategy
