#pragma once

#include "fast_trader_elite/data_model/field.h"
#include "fast_trader_elite/data_model/type.h"
#include <cstdint>
#include <string>

namespace fast_trader_elite::strategy {

/**
 * 执行器状态
 */
enum class executor_status {
  IDLE,                // 空闲状态，未开始执行
  EXECUTING,           // 正在执行中
  CANCELLING,          // 正在撤单中
  EXECUTED,            // 已执行完毕但未验证持仓
  COMPLETED,           // 已完成目标且持仓已验证
  FAILED               // 执行失败
};

/**
 * 执行器接口类
 */
class i_executor {
public:
  virtual ~i_executor() = default;

  // 初始化执行器
  virtual bool init() = 0;

  // 处理行情数据
  virtual void process_market_data(const depth_market_data_field *md) = 0;

  // 处理订单回报
  virtual void process_order(const order_field *field) = 0;

  // 处理成交回报
  virtual void process_trade(const trade_field *field) = 0;

  // 处理持仓回报
  virtual void process_position(const position_field *field) = 0;

  // 处理HTTP响应
  virtual void process_http_rsp(const http_rsp_field *field) = 0;

  // 重置执行器状态
  virtual void reset() = 0;

  // 获取执行器状态描述
  virtual std::string get_status_description() const = 0;

  // 获取品种索引
  virtual uint16_t get_instrument_idx() const = 0;

  // 获取品种ID
  virtual std::string get_instrument_id() const = 0;

  // 获取目标数量
  virtual double get_target_volume() const = 0;

  // 获取已成交数量
  virtual double get_traded_volume() const = 0;

  // 获取执行器状态
  virtual executor_status get_status() const = 0;

  // 获取重试次数
  virtual int get_retry_count() const = 0;


};

} // namespace fast_trader_elite::strategy
