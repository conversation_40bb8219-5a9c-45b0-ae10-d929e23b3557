#pragma once
#include <cpp_frame/storage/double_vector_storage.h>
#include <fast_trader_elite/data_model/field.h>
namespace fast_trader_elite {
class portfolio_position_manager {
 public:
  portfolio_position_manager() {}
  ~portfolio_position_manager() {}

  portfolio_position_field *add_trade(trade_field *trade, uint16_t portfolio_id) {
    portfolio_position_field *pos =
        pos_.find(portfolio_id, trade->instrument_idx);
    if (pos == nullptr) {
      pos = new portfolio_position_field();
      pos->instrument_idx = trade->instrument_idx;
      pos->ins_type = trade->ins_type;
      strcpy(pos->instrument_id, trade->instrument_id);
      strcpy(pos->instrument_name, trade->instrument_name);
      pos->long_position = 0;
      pos->short_position = 0;
      pos->net_position = 0;
      pos_.insert(portfolio_id, trade->instrument_idx, pos);
    }
    if (trade->offset_flag == offset_type::NON) {
      pos->net_position += trade->direction == direction_type::BUY
                               ? trade->volume
                               : -trade->volume;
    }
    else if (trade->offset_flag == offset_type::OPEN) {
    }
    else if (trade->offset_flag == offset_type::CLOSE) {
    }
    else {
    }
    return pos;
  }

  portfolio_position_field *get_portfolio_position(uint16_t portfolio_id,
                                                   uint32_t ins_id) {
    return pos_.find(static_cast<int>(portfolio_id), ins_id);
  }

  double get_long_pos(uint16_t portfolio_id, uint32_t ins_id) {
    portfolio_position_field *pos =
        pos_.find(static_cast<int>(portfolio_id), ins_id);
    if (pos == nullptr) {
      return 0;
    }
    return pos->long_position;
  }

  double get_short_pos(uint16_t portfolio_id, uint32_t ins_id) {
    portfolio_position_field *pos =
        pos_.find(static_cast<int>(portfolio_id), ins_id);
    if (pos == nullptr) {
      return 0;
    }
    return pos->short_position;
  }

  double get_net_pos(uint16_t portfolio_id, uint32_t ins_id) {
    portfolio_position_field *pos =
        pos_.find(static_cast<int>(portfolio_id), ins_id);
    if (pos == nullptr) {
      return 0;
    }
    return pos->net_position;
  }
  template <class Func>
  void foreach (Func &&f) {
    pos_.foreach (f);
  }

 private:
  cpp_frame::storage::double_vector_storage<portfolio_position_field> pos_;
};
}  // namespace fast_trader
