#pragma once
#include <stdint.h>
#include <sys/time.h>
#include <time.h>

#include <chrono>

namespace fast_trader_elite {
class date {
 public:
  inline static uint64_t get_current_mil_sec() {
    struct timeval tv;
    gettimeofday(&tv, nullptr);
    return tv.tv_sec * 1000 + tv.tv_usec / 1000;
  }
  inline static uint64_t get_current_macro_sec() {
    struct timeval tv;
    gettimeofday(&tv, nullptr);
    return tv.tv_sec * 1000000 + tv.tv_usec;
  }
  inline static uint64_t get_current_nano_sec() {
    timespec ts;
    ::clock_gettime(CLOCK_REALTIME, &ts);
    return ts.tv_sec * 1000000000 + ts.tv_nsec;
  }
  static char *get_timestamp(char *timestamp, int len) {
    time_t t;
    time(&t);
    struct tm *ptm = gmtime(&t);
    strftime(timestamp, len, "%FT%T.123Z", ptm);
    return timestamp;
  }
};
}  // namespace cpp_frame