# This file is auto-generated from environment.yml, do not modify.
# See that file for comments about the need/usage of each dependency.

pip
versioneer[toml]
cython~=3.0.5
meson[ninja]==1.2.1
meson-python==0.13.1
pytest>=7.3.2
pytest-cov
pytest-xdist>=3.4.0
pytest-qt>=4.4.0
pytest-localserver
PyQt5>=5.15.9
coverage
python-dateutil
numpy<3
beautifulsoup4>=4.12.3
bottleneck>=1.3.6
fastparquet>=2024.2.0
fsspec>=2023.12.2
html5lib>=1.1
hypothesis>=6.84.0
gcsfs>=2023.12.2
ipython
pickleshare
jinja2>=3.1.3
lxml>=4.9.2
matplotlib>=3.8.3
numba>=0.59.0
numexpr>=2.9.0
openpyxl>=3.1.2
odfpy>=1.4.1
psycopg2-binary>=2.9.6
pyarrow>=10.0.1
pyi<PERSON>berg>=0.7.1
pymysql>=1.1.0
pyreadstat>=1.2.6
tables>=3.8.0
python-calamine>=0.1.7
pytz>=2023.4
pyxlsb>=1.0.10
s3fs>=2023.12.2
scipy>=1.12.0
SQLAlchemy>=2.0.0
tabulate>=0.9.0
xarray>=2024.1.1
xlrd>=2.0.1
xlsxwriter>=3.2.0
zstandard>=0.22.0
dask
seaborn
moto
flask
asv>=0.6.1
flake8==7.1.0
mypy==1.13.0
tokenize-rt
pre-commit>=4.2.0
gitpython
natsort
numpydoc
pydata-sphinx-theme==0.16
pytest-cython
sphinx
sphinx-design
sphinx-copybutton
types-python-dateutil
types-PyMySQL
types-pytz
types-PyYAML
types-setuptools
nbconvert>=7.11.0
nbsphinx
pandoc
ipywidgets
nbformat
notebook>=7.0.6
ipykernel
markdown
feedparser
pyyaml
requests
pygments
jupyterlite-core
jupyterlite-pyodide-kernel
adbc-driver-postgresql>=0.10.0
adbc-driver-sqlite>=0.8.0
typing_extensions; python_version<"3.11"
tzdata>=2022.7
