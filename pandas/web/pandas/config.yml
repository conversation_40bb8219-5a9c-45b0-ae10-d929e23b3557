main:
  templates_path: _templates
  base_template: "layout.html"
  production_url: "https://pandas.pydata.org/"
  ignore:
  - _templates/layout.html
  - config.yml
  github_repo_url: pandas-dev/pandas
  context_preprocessors:
  - pandas_web.Preprocessors.current_year
  - pandas_web.Preprocessors.navbar_add_info
  - pandas_web.Preprocessors.blog_add_posts
  - pandas_web.Preprocessors.maintainers_add_info
  - pandas_web.Preprocessors.home_add_releases
  - pandas_web.Preprocessors.roadmap_pdeps
  markdown_extensions:
  - toc
  - tables
  - fenced_code
  - meta
  - footnotes
  - codehilite
static:
  logo: static/img/pandas_white.svg
  css:
  - static/css/pandas.css
  - static/css/codehilite.css
navbar:
  - name: "About us"
    target:
    - name: "About pandas"
      target: about/
    - name: "Project roadmap"
      target: about/roadmap.html
    - name: "Governance"
      target: about/governance.html
    - name: "Team"
      target: about/team.html
    - name: "Sponsors"
      target: about/sponsors.html
    - name: "Citing and logo"
      target: about/citing.html
  - name: "Getting started"
    target: getting_started.html
  - name: "Documentation"
    target: docs/
  - name: "Community"
    target:
    - name: "Blog"
      target: community/blog/
    - name: "Ask a question (StackOverflow)"
      target: https://stackoverflow.com/questions/tagged/pandas
    - name: "Code of conduct"
      target: community/coc.html
    - name: "Ecosystem"
      target: community/ecosystem.html
    - name: "Benchmarks"
      target: community/benchmarks.html
  - name: "Contribute"
    target: contribute.html
blog:
  num_posts: 50
  posts_path: community/blog
  author: "pandas team"
  feed_name: "pandas blog"
  feed:
  - https://wesmckinney.com/feeds/pandas.atom.xml
  - https://tomaugspurger.github.io/feed
  - https://jorisvandenbossche.github.io/feeds/pandas.atom.xml
  - https://datapythonista.me/blog/feeds/pandas.atom.xml
  - https://numfocus.org/tag/pandas/feed/
  - https://phofl.github.io/feeds/pandas.atom.xml
maintainers:
  active:
  - jorisvandenbossche
  - TomAugspurger
  - jreback
  - WillAyd
  - mroeschke
  - jbrockmendel
  - datapythonista
  - simonjayhawkins
  - topper-123
  - alimcmaster1
  - bashtage
  - Dr-Irv
  - rhshadrach
  - phofl
  - attack68
  - fangchenli
  - lithomas1
  - lukemanley
  - noatamir
  inactive:
  - lodagro
  - jseabold
  - jtratner
  - shoyer
  - chris-b1
  - sinhrks
  - cpcloud
  - toobaz
  - jschendel
  - charlesdong1991
  - dsaxton
  - wesm
  - gfyoung
  - mzeitlin11
  - twoertwein
  - MarcoGorelli
workgroups:
  coc:
    name: Code of Conduct
    contact: <EMAIL>
    responsibilities: "Make sure pandas is the welcoming and inclusive community we want it to be. Keeping the CoC updated, and addressing violation reports."
    members:
    - Tom Augspurger
    - Bijay Regmi
    - Wuraola Oyewusi
    - Мария Чакчурина
  finance:
    name: Finance
    contact: <EMAIL>
    responsibilities: "Manage the funding. Coordinate the request of grants. Approve the project expenses."
    members:
    - Matthew Roeschke
    - Jeff Reback
    - Joris Van den Bossche
    - Patrick Hoefler
  infrastructure:
    name: Infrastructure
    contact: <EMAIL>
    responsibilities: "Keep the pandas infrastructure up and working. In particular the servers for the website, benchmarks, CI and others needed."
    members:
    - William Ayd
    - Thomas Li
  communications:
    name: Communications
    contact: <EMAIL>
    responsibilities: "Share relevant information with the broader community, mainly via our social networks, as well as being the main point of contact between NumFOCUS and the core team."
    members:
    - datapythonista
sponsors:
  active:
  - name: "NumFOCUS"
    url: https://numfocus.org/
    logo: static/img/partners/numfocus.svg
    kind: numfocus
  - name: "Nvidia"
    url: https://www.nvidia.com
    logo: static/img/partners/nvidia.svg
    kind: partner
    description: "Matthew Roeschke"
  - name: "Tidelift"
    url: https://tidelift.com
    logo: static/img/partners/tidelift.svg
    kind: regular
    description: "<i>pandas</i> is part of the <a href=\"https://tidelift.com/subscription/pkg/pypi-pandas?utm_source=pypi-pandas&utm_medium=referral&utm_campaign=readme\">Tidelift subscription</a>. You can support pandas by becoming a Tidelift subscriber."
  - name: "Bodo"
    url: https://www.bodo.ai/
    logo: static/img/partners/bodo.svg
    kind: regular
    description: "Bodo's parallel computing platform uses pandas API, and Bodo financially supports pandas development to help improve pandas, in particular the pandas API"
  inkind:  # not included in active so they don't appear in the home page
  - name: "OVH"
    url: https://us.ovhcloud.com/
    description: "Website and documentation hosting."
  - name: "Indeed"
    url: https://opensource.indeedeng.io/
    description: "<i>pandas</i> logo design"
  past:
  - name: "Paris-Saclay Center for Data Science"
    url: https://www.datascience-paris-saclay.fr/
    kind: partner
  - name: "Anaconda"
    url: https://www.anaconda.com/
    kind: partner
  - name: "RStudio"
    url: https://www.rstudio.com/
    kind: partner
  - name: "Ursa Labs"
    url: https://ursalabs.org/
    kind: partner
  - name: "Gousto"
    url: https://www.gousto.co.uk/
    kind: partner
  - name: "d-fine GmbH"
    url: https://www.d-fine.com/en/
    kind: partner
  - name: "Two Sigma"
    url: https://www.twosigma.com/
    kind: partner
  - name: "Voltron Data"
    url: https://voltrondata.com/
    kind: partner
  - name: "Intel"
    url: https://www.intel.com/
    kind: partner
  - name: "Chan Zuckerberg Initiative"
    url: https://chanzuckerberg.com/
    kind: regular
  - name: "Coiled"
    url: https://www.coiled.io
    kind: partner
roadmap:
  pdeps_path: pdeps
