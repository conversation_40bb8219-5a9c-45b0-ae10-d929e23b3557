body {
    padding-top: 5em;
    color: #444;
}
h1 {
    font-size: 2.4rem;
    font-weight: 700;
    color: #130654;
}
h2 {
    font-size: 1.8rem;
    font-weight: 700;
    color: #130654;
}
h3 {
    font-size: 1.3rem;
    font-weight: 600;
    color: black;
}
h3 a {
    color: black;
    text-decoration: underline dotted !important;
}
a {
    color: #130654;
}
code {
    white-space: pre;
}
ol ol, ol ul, ul ol, ul ul {
    margin-bottom: 1rem;
}
.blue {
    color: #150458;
}
.pink {
    color: #e70488;
}
.follow-us-button {
    font-size: 2.4rem !important;
    color: #0d6efd !important;
}
.follow-us-button:hover {
    color: #0b5ed7 !important;
}
.fab {
    font-size: 1.2rem;
    color: #666;
}
.fab:hover {
    color: #130654;
}
a.navbar-brand img {
    height: 3rem;
}
a:link:not(.btn):not(.dropdown-item):not(.nav-link) {
text-decoration: none;
}
a:visited:not(.btn):not(.dropdown-item):not(.nav-link) {
text-decoration: none;
}
a:hover:not(.btn):not(.dropdown-item):not(.nav-link) {
text-decoration: underline;
}
a:active:not(.btn):not(.dropdown-item):not(.nav-link) {
text-decoration: underline;
}
div.card {
    margin: 0 0 .2em .2em !important;
}
@media (min-width: 576px) {
    .card-group.maintainers div.card {
        min-width: 10rem;
        max-width: 10rem;
    }
}
div.card .card-title {
    font-weight: 500;
    color: #130654;
}
.book {
    padding: 0 20%;
}
.bg-dark {
    background-color: #130654 !important;
}
.navbar-dark .navbar-nav .nav-link {
    color: rgba(255, 255, 255, .9);
}
.navbar-dark .navbar-nav .nav-link:hover {
    color: white;
}
table.logo td {
    text-align: center;
}
table.logo img {
    height: 4rem;
}
blockquote {
    background: #f9f9f9;
    border-left: 5px solid #ccc;
    padding-left:15px;
    color: #787878;
    font-size: 18px;
 }
