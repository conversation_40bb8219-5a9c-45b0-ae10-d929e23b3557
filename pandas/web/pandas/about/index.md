# About pandas

## History of development

In 2008, _pandas_ development began at [AQR Capital Management](https://www.aqr.com).
By the end of 2009 it had been [open sourced](https://en.wikipedia.org/wiki/Open_source),
and is actively supported today by a community of like-minded individuals around the world who
contribute their valuable time and energy to help make open source _pandas_
possible. Thank you to [all of our contributors](team.html).

Since 2015, _pandas_ is a [NumFOCUS sponsored project](https://numfocus.org/sponsored-projects).
This will help ensure the success of development of _pandas_ as a world-class open-source project.

### Timeline

- **2008**:  Development of _pandas_ started
- **2009**: _pandas_ becomes open source
- **2012**: First edition of _Python for Data Analysis_ is published
- **2015**: _pandas_ becomes a [NumFOCUS sponsored project](https://numfocus.org/sponsored-projects)
- **2018**: First in-person core developer sprint

## Library Highlights

- A fast and efficient **DataFrame** object for data manipulation with
  integrated indexing;

- Tools for **reading and writing data** between in-memory data structures and
  different formats: CSV and text files, Microsoft Excel, SQL databases, and
  the fast HDF5 format;

- Intelligent **data alignment** and integrated handling of **missing data**:
  gain automatic label-based alignment in computations and easily manipulate
  messy data into an orderly form;

- Flexible **reshaping** and pivoting of data sets;

- Intelligent label-based **slicing**, **fancy indexing**, and **subsetting**
  of large data sets;

- Columns can be inserted and deleted from data structures for **size
  mutability**;

- Aggregating or transforming data with a powerful **group by** engine
  allowing split-apply-combine operations on data sets;

- High performance **merging and joining** of data sets;

- **Hierarchical axis indexing** provides an intuitive way of working with
  high-dimensional data in a lower-dimensional data structure;

- **Time series**-functionality: date range generation and frequency
  conversion, moving window statistics, date shifting and lagging.
  Even create domain-specific time offsets and join time
  series without losing data;

- Highly **optimized for performance**, with critical code paths written in
  [Cython](https://cython.org) or C.

- Python with *pandas* is in use in a wide variety of **academic and
  commercial** domains, including Finance, Neuroscience, Economics,
  Statistics, Advertising, Web Analytics, and more.

## Mission

_pandas_ aims to be the fundamental high-level building block for doing practical,
real world data analysis in Python.
Additionally, it has the broader goal of becoming the most powerful and flexible
open source data analysis / manipulation tool available in any language.

## Vision

A world where data analytics and manipulation software is:

- Accessible to everyone
- Free for users to use and modify
- Flexible
- Powerful
- Easy to use
- Fast

## Values

Is in the core of _pandas_ to be respectful and welcoming with everybody,
users, contributors and the broader community. Regardless of level of experience,
gender, gender identity and expression, sexual orientation, disability,
personal appearance, body size, race, ethnicity, age, religion, or nationality.
