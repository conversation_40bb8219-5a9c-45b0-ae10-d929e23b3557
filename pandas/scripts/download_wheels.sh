#!/bin/bash
#
# Download all wheels for a pandas version.
#
# This script is mostly useful during the release process, when wheels
# generated by the MacPython repo need to be downloaded locally to then
# be uploaded to the PyPI.
#
# There is no API to access the wheel files, so the script downloads the
# website, extracts the file urls from the html, and then downloads it
# one by one to the dist/ directory where they would be generated.

VERSION=$1
BASE_DIR=$(dirname -- $0)
mkdir -p $BASE_DIR/../dist
DIST_DIR="$(realpath $BASE_DIR/../dist)"

if [ -z "$VERSION" ]; then
    printf "Usage:\n\t%s <version>\n\nWhere <version> is for example 1.5.3"  "$0"
    exit 1
fi

curl "https://anaconda.org/multibuild-wheels-staging/pandas/files?version=${VERSION}" | \
    grep "href=\"/multibuild-wheels-staging/pandas/${VERSION}" | \
    sed -r 's/.*<a href="([^"]+\.(whl|tar.gz))">.*/\1/g' | \
    awk '{print "https://anaconda.org" $0 }' | \
    xargs wget -P "$DIST_DIR"

printf '\nWheels downloaded to %s\nYou can upload them to PyPI using:\n\n' "$DIST_DIR"
printf "\ttwine upload %s/pandas-%s*.{whl,tar.gz} --skip-existing" "$DIST_DIR" "$VERSION"
