# Test: random
name: pandas-dev
channels:
  - conda-forge
dependencies:
  - python=3.8

  # build dependencies
  - versioneer[toml]
  - cython>=0.29.32

  # test dependencies
  - pytest>=7.3.2
  - pytest-cov
  - pytest-xdist>=3.4.0
  - psutil
  - boto3

  # required dependencies
  - python-dateutil
  - numpy
  - pytz

  # optional dependencies
  - beautifulsoup4>=5.9.3
  - bottleneck>=1.3.2
  - fastparquet>=0.6.3
  - fsspec>=2021.07.0
  - html5lib>=1.1
  - hypothesis>=6.34.2
  - gcsfs>=2021.07.0
  - jinja2>=3.0.0
  - lxml>=4.6.3
  - matplotlib>=3.6.1
  - numba>=0.53.1
  - numexpr>=2.7.3
  - openpyxl>=3.0.7
  - odfpy>=1.4.1
  - psycopg2>=2.8.6
  - pyarrow<11, >=7.0.0
  - pymysql>=1.1.0
  - pyreadstat>=1.1.2
  - pytables>=3.6.1
  - python-calamine>=0.1.7
  - pyxlsb>=1.0.8
  - s3fs>=2021.08.0
  - scipy>=1.7.1
  - sqlalchemy>=1.4.16
  - tabulate>=0.8.9
  - xarray>=0.21.0
  - xlrd>=2.0.1
  - xlsxwriter>=1.4.3
  - zstandard>=0.15.2
