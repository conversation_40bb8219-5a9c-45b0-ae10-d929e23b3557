{
    "typeCheckingMode": "off",
    "reportArgumentType": true,
    "reportAssignmentType": true,
    "reportAttributeAccessIssue": true,
    "reportCallIssue": true,
    "reportGeneralTypeIssues": true,
    "reportIndexIssue": true,
    "reportOperatorIssue": true,
    "reportRedeclaration": true,
    "reportReturnType": true,
    "useLibraryCodeForTypes": false,
    "analyzeUnannotatedFunctions": false,
    "disableBytesTypePromotions": true,
    "include":
      [
        "pandas",
        "typings"
      ],
    "exclude":
      [
        "pandas/tests",

        "pandas/io/clipboard",
        "pandas/util/version",

        "pandas/_testing/__init__.py",
        "pandas/_testing/_io.py",
        "pandas/compat/pickle_compat.py",
        "pandas/core/_numba/extensions.py",
        "pandas/core/_numba/kernels/sum_.py",
        "pandas/core/_numba/kernels/var_.py",
        "pandas/core/algorithms.py",
        "pandas/core/apply.py",
        "pandas/core/array_algos/take.py",
        "pandas/core/arrays/_mixins.py",
        "pandas/core/arrays/arrow/array.py",
        "pandas/core/arrays/base.py",
        "pandas/core/arrays/boolean.py",
        "pandas/core/arrays/categorical.py",
        "pandas/core/arrays/datetimelike.py",
        "pandas/core/arrays/datetimes.py",
        "pandas/core/arrays/interval.py",
        "pandas/core/arrays/masked.py",
        "pandas/core/arrays/period.py",
        "pandas/core/arrays/sparse/accessor.py",
        "pandas/core/arrays/sparse/array.py",
        "pandas/core/arrays/string_.py",
        "pandas/core/arrays/string_arrow.py",
        "pandas/core/arrays/timedeltas.py",
        "pandas/core/computation/align.py",
        "pandas/core/construction.py",
        "pandas/core/dtypes/cast.py",
        "pandas/core/dtypes/common.py",
        "pandas/core/dtypes/concat.py",
        "pandas/core/dtypes/dtypes.py",
        "pandas/core/frame.py",
        "pandas/core/generic.py",
        "pandas/core/groupby/generic.py",
        "pandas/core/groupby/groupby.py",
        "pandas/core/groupby/grouper.py",
        "pandas/core/groupby/ops.py",
        "pandas/core/indexers/utils.py",
        "pandas/core/indexes/base.py",
        "pandas/core/indexes/category.py",
        "pandas/core/indexes/datetimelike.py",
        "pandas/core/indexes/datetimes.py",
        "pandas/core/indexes/extension.py",
        "pandas/core/indexes/interval.py",
        "pandas/core/indexes/multi.py",
        "pandas/core/indexes/period.py",
        "pandas/core/indexing.py",
        "pandas/core/internals/api.py",
        "pandas/core/internals/blocks.py",
        "pandas/core/internals/construction.py",
        "pandas/core/internals/managers.py",
        "pandas/core/missing.py",
        "pandas/core/nanops.py",
        "pandas/core/ops/array_ops.py",
        "pandas/core/resample.py",
        "pandas/core/reshape/concat.py",
        "pandas/core/reshape/merge.py",
        "pandas/core/reshape/pivot.py",
        "pandas/core/reshape/tile.py",
        "pandas/core/series.py",
        "pandas/core/sorting.py",
        "pandas/core/strings/accessor.py",
        "pandas/core/tools/datetimes.py",
        "pandas/core/tools/numeric.py",
        "pandas/core/util/hashing.py",
        "pandas/core/window/ewm.py",
        "pandas/core/window/rolling.py",
        "pandas/io/common.py",
        "pandas/io/excel/_base.py",
        "pandas/io/excel/_odfreader.py",
        "pandas/io/formats/excel.py",
        "pandas/io/formats/format.py",
        "pandas/io/formats/info.py",
        "pandas/io/formats/printing.py",
        "pandas/io/formats/style.py",
        "pandas/io/formats/style_render.py",
        "pandas/io/json/_json.py",
        "pandas/io/json/_normalize.py",
        "pandas/io/parsers/arrow_parser_wrapper.py",
        "pandas/io/parsers/base_parser.py",
        "pandas/io/parsers/c_parser_wrapper.py",
        "pandas/io/pytables.py",
        "pandas/io/sql.py",
        "pandas/io/stata.py",
        "pandas/plotting/_matplotlib/boxplot.py",
        "pandas/plotting/_matplotlib/core.py",
        "pandas/plotting/_matplotlib/misc.py",
        "pandas/plotting/_matplotlib/timeseries.py",
        "pandas/plotting/_matplotlib/tools.py",
        "pandas/tseries/frequencies.py",
        "pandas/tseries/holiday.py",
      ],
}
