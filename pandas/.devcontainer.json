// For format details, see https://aka.ms/vscode-remote/devcontainer.json or the definition README at
// https://github.com/microsoft/vscode-dev-containers/tree/master/containers/python-3-miniconda
{
	"name": "pandas",
	"context": ".",
	"dockerFile": "Dockerfile",

	// Use 'settings' to set *default* container specific settings.json values on container create.
	// You can edit these settings after create using File > Preferences > Settings > Remote.
	"settings": {
		"python.pythonPath": "/usr/local/bin/python",
		"python.formatting.provider": "black",
		"python.linting.enabled": true,
		"python.linting.flake8Enabled": true,
		"python.linting.pylintEnabled": false,
		"python.linting.mypyEnabled": true,
		"python.testing.pytestEnabled": true,
		"python.testing.pytestArgs": [
			"pandas"
		]
	},

	// Add the IDs of extensions you want installed when the container is created in the array below.
	"extensions": [
		"ms-python.python",
		"ms-vscode.cpptools"
	]
}
