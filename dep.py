import pandas as pd
import re
from collections import defaultdict, deque, Counter
import graphviz # Import the graphviz library (still needed to define the graph structure)

# Factor data (retained as before)
factors_data = {
    "fname": [
        "p1_corrs0", "p1_corrs1", "p1_corrs2", "p1_corrs3", "p1_corrs4", "p1_corrs5", "p1_corrs6",
        "p1_corrs7", "p1_corrs8", "p1_corrs9", "p1_corrs10", "p1_corrs11", "p1_corrs12",
        "p1_corrs13", "p1_corrs14", "p1_corrs15", "p2_et0", "p2_et1", "p2_et2", "p2_et3",
        "p2_et4", "p2_et5", "p2_et6", "p2_et7", "p2_et8", "p2_et9", "p2_et10", "p2_et11",
        "p2_et12", "p2_et13", "p2_et14", "p2_et15", "p2_et16", "p2_et17", "p2_et18",
        "p2_et19", "p3_mf0", "p3_mf1", "p3_mf2", "p3_mf3", "p3_mf4", "p3_mf5", "p3_mf6",
        "p3_mf7", "p3_mf8", "p3_mf9", "p3_mf10", "p3_mf11", "p3_mf12", "p4_ms0", "p4_ms1",
        "p4_ms2", "p4_ms3", "p4_ms4", "p4_ms5", "p4_ms6", "p5_to0", "p5_to1", "p5_to2",
        "p5_to3", "p5_to4", "p5_to5", "p5_to6", "p5_to7", "p6_tn0", "p6_tn1", "p6_tn2",
        "p6_tn3", "p6_tn4", "p6_tn5", "p6_tn6", "p6_tn7", "p6_tn8", "p6_tn9", "p6_tn10",
        "p6_tn11", "p6_tn12", "p6_tn13", "kama", "adosc", "dcperiod", "dcphase", "cci",
        "cmo", "dx", "di", "dm", "ultosc", "liangle", "lislope"
    ],
    "forms": [
        "ts_Corr(Close,Volume,60)",
        "ts_Corr(Close/ts_Delay(Close,1)-1,Volume,60)",
        "ts_Corr(ts_Delay(Close,1),Volume,60)",
        "ts_Corr(Close,ts_Delay(Volume,1),60)",
        "ts_Corr(ts_Delay(Close/ts_Delay(Close,1)-1,1),Close/ts_Delay(Close,1)-1,60)",
        "ts_Corr(ts_Delay(Close/ts_Delay(Close,1)-1,1),Close,60)",
        "ts_Corr(Volume,Volume-ts_Delay(Volume,1),60)",
        "ts_Corr(Volume-ts_Delay(Volume,1), Volume-ts_Delay(Volume,1) - ts_Delay(Volume-ts_Delay(Volume,1),1),60)",
        "ts_Corr(VWAP,Volume,60)",
        "ts_Corr(VWAP/ts_Delay(VWAP,1)-1,Volume,60)",
        "ts_Corr(ts_Delay(VWAP,1),Volume,60)",
        "ts_Corr(VWAP,ts_Delay(Volume,1),60)",
        "ts_Corr(ts_Delay(VWAP/ts_Delay(VWAP,1)-1,1),VWAP/ts_Delay(VWAP,1)-1,60)",
        "ts_Corr(ts_Delay(VWAP/ts_Delay(VWAP,1)-1,1),VWAP,60)",
        "ts_Corr(Volume,Volume-ts_Delay(Volume,1),60)",
        "ts_Corr(Volume-ts_Delay(Volume,1), Volume-ts_Delay(Volume,1) - ts_Delay(Volume-ts_Delay(Volume,1),1),60)",
        "Tot_Mean(IfThen(IfThen(Volume-ts_Delay(Volume,1)-Tot_Mean(Volume-ts_Delay(Volume,1))-Tot_Stdev(Volume-ts_Delay(Volume,1)),1,0),Close/ts_Delay(Close,1)-1,getNan(Close/ts_Delay(Close,1)-1)))",
        "Tot_Mean(IfThen(ts_Sum(IfThen(Tot_Rank(Volume-ts_Delay(Volume,1))-0.9,1,0),10),Close/ts_Delay(Close,1)-1,getNan(Close/ts_Delay(Close,1)-1)))",
        "Tot_Stdev(IfThen(ts_Sum(IfThen(Tot_Rank(Close/ts_Delay(Close,1)-1)-0.9,1,0),10),Close/ts_Delay(Close,1)-1,getNan(Close/ts_Delay(Close,1)-1)))",
        "Tot_Mean(IfThen(IfThen(Close/ts_Delay(Close,1)-1-Tot_Mean(Close/ts_Delay(Close,1)-1)-Tot_Stdev(Close/ts_Delay(Close,1)-1),1,0),Close/ts_Delay(Close,1)-1,getNan(Close/ts_Delay(Close,1)-1)))",
        "Tot_Mean(IfThen(ts_Sum(IfThen(Tot_Rank(Close/ts_Delay(Close,1)-1)-0.9,1,0),10),Close/ts_Delay(Close,1)-1,getNan(Close/ts_Delay(Close,1)-1)))",
        "ts_Stdev(Close/ts_Delay(Close,1)-1,60)-pn_Mean(ts_Stdev(Close/ts_Delay(Close,1)-1,60))",
        "Abs(ts_Stdev(Close/ts_Delay(Close,1)-1,60)-pn_Mean(ts_Stdev(Close/ts_Delay(Close,1)-1,60)))",
        "ts_Mean(Close/ts_Delay(Close,1)-1,60)-pn_Mean(ts_Mean(Close/ts_Delay(Close,1)-1,60))",
        "Abs(ts_Mean(Close/ts_Delay(Close,1)-1,60)-pn_Mean(ts_Mean(Close/ts_Delay(Close,1)-1,60)))",
        "ts_Mean((Close/ts_Delay(Close,1)-1)*IfThen(Volume-ts_Mean(Volume,30)-1.210*ts_Stdev(Volume,30),1,0),30)",
        "ts_Mean(IfThen((Close/ts_Delay(Close,1)-1),(Close/ts_Delay(Close,1)-1),0)*IfThen(Volume-ts_Mean(Volume,30)-0.10*ts_Stdev(Volume,30),1,0),30)",
        "Tot_Sum(IfThen(ts_Delay(ts_Min(Low,30),1)-Low,1,0))",
        "Tot_Stdev(Abs(Close/ts_Delay(Close,1)-1-pn_Mean(Close/ts_Delay(Close,1)-1)))",
        "Tot_Stdev(pn_Rank(Close/ts_Delay(Close,1)-1))",
        "Tot_Stdev(pn_Rank(Volume))",
        "Tot_Mean(Abs(Close/ts_Delay(Close,1)-1-pn_Mean(Close/ts_Delay(Close,1)-1))/(Abs(pn_Mean(Close/ts_Delay(Close,1)-1))+Abs(Close/ts_Delay(Close,1)-1)+0.1))",
        "Tot_Mean(IfThen(-(Close/ts_Delay(Close,1)-1)+(Tot_Mean(Close/ts_Delay(Close,1)-1)-Tot_Stdev(Close/ts_Delay(Close,1)-1)),Close/ts_Delay(Close,1)-1,getNan(Close)))",
        "Tot_Mean(IfThen(Tot_Rank(Close/ts_Delay(Close,1)-1)-0.93,Close/ts_Delay(Close,1)-1,getNan(Close)))",
        "Tot_Mean(IfThen(0.07-Tot_Rank(Close/ts_Delay(Close,1)-1),Close/ts_Delay(Close,1)-1,getNan(Close)))",
        "Tot_Sum(IfThen(Equal(Close/ts_Delay(Close,1)-1,0),1,getNan(Close)))",
        "Tot_Mean(Abs(Close/Open-1)/Sqrt(Volume))",
        "Tot_Stdev(Abs(Close/Open-1)/Sqrt(Volume))",
        "Tot_Mean(IfThen(Tot_Rank(Abs(Close/Open-1)/Sqrt(Volume))-0.8,Close,getNan(Close)))",
        "Tot_Mean(IfThen(Tot_Rank(Abs(Close/Open-1)+(Volume))-0.8,Close,getNan(Close)))",
        "Tot_Mean(IfThen(Tot_Rank(Abs(Close/Open-1)/Log(Volume))-0.8,Close,getNan(Close)))",
        "Tot_Sum(IfThen(Equal(Abs(Close-Open),Abs(High-Low)),Amount,getNan(Close)))",
        "Tot_Sum(IfThen(Tot_Rank(Abs(Close/Open-1)/Sqrt(Volume))-0.8,Volume,getNan(Close)))",
        "Tot_Mean(ts_Stdev(Close/ts_Delay(Close,1)-1,10))",
        "Tot_Mean(ts_Stdev(ts_Stdev(Close/ts_Delay(Close,1)-1,10),10))",
        "ts_Corr(ts_Stdev(ts_Stdev(Close/ts_Delay(Close,1)-1,10),10),Volume*Close,60)",
        "Tot_Sum(IfThen(ts_Stdev(ts_Stdev(Close/ts_Delay(Close,1)-1,10),10)-Tot_Mean(ts_Stdev(ts_Stdev(Close/ts_Delay(Close,1)-1,10),10)),Volume*Close,getNan(Close)))",
        "Tot_Sum(IfThen(Tot_Rank(Volume)-0.8,(Close-Open)/Open,getNan(Close)))",
        "(Tot_Sum(IfThen(0.2-Tot_Rank(Volume),(Close-Open)/Open,getNan(Close))))",
        "Tot_Mean(Close/ts_Delay(Close,1)-1-Log(Close/ts_Delay(Close,1)))",
        "Tot_Mean(Close/ts_Delay(Close,1)-1-Log(Close/ts_Delay(Close,1))-(Log(Close/ts_Delay(Close,1)))**2/2)",
        "Tot_ArgMax(Close)",
        "Tot_ArgMin(Close)",
        "Tot_Sum(((Close-ts_Delay(Close,1))/Close)**2)",
        "Tot_Sum(((Close-ts_Delay(Close,1))/Close)**3)",
        "Tot_Sum(IfThen(Close/ts_Delay(Close,1)-1,Volume,getNan(Close)))",
        "Tot_Sum(IfThen(Close-ts_Delay(Close,1),Amount,-Amount))",
        "Tot_ArgMax(Volume)",
        "ts_Corr(Amount,ts_Delay(Amount,1),60)",
        "Tot_Sum(Amount)/(Tot_Sum(Abs(High-Low)/Close+Abs(Open-ts_Delay(Close,1)))*(1+Tot_Stdev(Close)/Tot_Mean(Close)))",
        "Tot_Mean(Abs(Close/ts_Delay(Close,1)-1)/Amount)",
        "Tot_Sum((High-Low)/Close)/Tot_Sum(Amount)",
        "Tot_Sum((2*(High-Low)-Abs(Open-Close))/Close)/Tot_Sum(Amount)",
        "Tot_Sum(Abs(Close/ts_Delay(Close,1)-1)/(Close*Volume))",
        "Tot_Sum(IfThen(Close-(ts_Mean(Close,30)+ts_Stdev(Close,30)),-1,IfThen((ts_Mean(Close,30)-ts_Stdev(Close,30))-Close,1,0)))",
        "Tot_Sum(High-Open)-Tot_Sum(Open-Low)",
        "ts_Regression(High,Low,60,'D')",
        "Tot_Mean(((High+Low)-ts_Delay(High+Low,1))*(High-Low)/2/Amount)",
        "Tot_Sum(IfThen(Close-ts_Delay(Close,1),1,0))",
        "Tot_Mean(High-ts_Delay(Close,1))/Tot_Sum(ts_Delay(Close,1)-Low)",
        "Tot_Mean((ts_Max(High,30)-Close)/(ts_Max(High,30)-ts_Min(Low,30)))",
        "Tot_Mean((Close-ts_Min(Low,30))/(ts_Max(High,30)-ts_Min(Low,30)))",
        "Tot_Mean((High-Low)/Close)",
        "Tot_Mean(IfThen(Tot_Rank((Close-ts_Delay(Close,1))/Close)-0.910,(Close-ts_Delay(Close,1))/Close,getNan(Close)))",
        "Tot_Mean(IfThen(Tot_Rank(0.01-(Close-ts_Delay(Close,1))/Close),(Close-ts_Delay(Close,1))/Close,getNan(Close)))",
        "ts_Corr(Close-ts_Delay(Close,1),pn_Mean(Close-ts_Delay(Close,1)),60)",
        "Tot_Sum(IfThen(Tot_Rank(High-Low)-0.10,Volume,0))-Tot_Sum(IfThen(Tot_Rank(High-Low)-0.10,0,Volume))",
        "Tot_Sum(IfThen(Close-ts_Delay(Close,1),Volume,-Volume))",
        "get_KAMA(Close,60)",
        "get_ADOSC(High, Low, Close,Volume,20,60)",
        "get_HT_DCPERIOD(Close)",
        "get_HT_DCPHASE(Close)",
        "get_CCI(High, Low, Close,60)",
        "get_CMO(Close,60)",
        "get_DX(High, Low, Close,60)",
        "get_MINUS_DI(High, Low, Close,60)",
        "get_MINUS_DM(High, Low, Close,60)",
        "get_ULTOSC(High, Low, Close,10,20,60)",
        "get_LINEARREG_ANGLE(Close,60)",
        "get_LINEARREG_SLOPE(Close,60)"
    ]
}

df_factors = pd.DataFrame(factors_data)

# Define raw data fields
RAW_DATA_FIELDS = {'Close', 'Volume', 'VWAP', 'Open', 'High', 'Low', 'Amount'}

# Map factor names to their expressions
factor_name_to_form = {row['fname']: row['forms'] for idx, row in df_factors.iterrows()}
all_defined_factor_names = set(df_factors['fname'].tolist())

# Map get_XXX function calls to their corresponding factor names
get_func_to_factor_map = {
    'get_KAMA': 'kama', 'get_ADOSC': 'adosc', 'get_HT_DCPERIOD': 'dcperiod',
    'get_HT_DCPHASE': 'dcphase', 'get_CCI': 'cci', 'get_CMO': 'cmo',
    'get_DX': 'dx', 'get_MINUS_DI': 'di', 'get_MINUS_DM': 'dm',
    'get_ULTOSC': 'ultosc', 'get_LINEARREG_ANGLE': 'liangle', 'get_LINEARREG_SLOPE': 'lislope'
}

# Store dependencies (factor -> direct dependencies)
dependencies = defaultdict(set)
# Store reverse dependencies (dependency -> factors that use it)
reverse_dependencies = defaultdict(set)

# Keep track of all nodes encountered (raw data, defined factors, virtual factors)
all_graph_nodes = set(RAW_DATA_FIELDS) | all_defined_factor_names

# Map for virtual factors: {expression_string: virtual_factor_name}
virtual_factor_map = {}
virtual_factor_counter = 0

def get_virtual_factor_name(expr_str):
    global virtual_factor_counter
    normalized_expr = expr_str.strip()
    if normalized_expr not in virtual_factor_map:
        new_name = f"VIRTUAL_EXPR_{virtual_factor_counter}"
        virtual_factor_map[normalized_expr] = new_name
        all_graph_nodes.add(new_name)
        virtual_factor_counter += 1
    return virtual_factor_map[normalized_expr]

def clean_and_split_args(args_str):
    """
    Splits arguments string by commas, respecting parentheses and ensuring clean arguments.
    Handles nested structures.
    """
    args = []
    balance = 0
    current_arg_chars = []
    
    for char in args_str:
        if char == '(':
            balance += 1
        elif char == ')':
            balance -= 1
        
        if char == ',' and balance == 0:
            arg = "".join(current_arg_chars).strip()
            if arg:
                args.append(arg)
            current_arg_chars = []
        else:
            current_arg_chars.append(char)
    
    last_arg = "".join(current_arg_chars).strip()
    if last_arg:
        args.append(last_arg)
        
    return args

# Define operator precedence (higher number = higher precedence)
OPERATORS = {
    '**': 4,
    '*': 3, '/': 3,
    '+': 2, '-': 2,
    '==': 1, '!=': 1, '>': 1, '<': 1, '>=': 1, '<=': 1
}

def find_lowest_precedence_operator(expr_str):
    """
    Finds the lowest precedence operator outside of any parentheses.
    Returns (operator, index) or None if not found.
    This version correctly prioritizes the rightmost lowest precedence operator for left-associativity.
    """
    balance = 0
    lowest_prec_found = float('inf')
    best_op_idx = -1
    best_op = None

    i = 0
    while i < len(expr_str):
        char = expr_str[i]
        
        if char == '(':
            balance += 1
        elif char == ')':
            balance -= 1
        elif balance == 0:
            # Check for multi-character operators first (e.g., '**', '==', '!=', '>=', '<=')
            # Iterate through operators by length, longest first
            for op_len in sorted(set(len(op) for op in OPERATORS), reverse=True):
                if i + op_len <= len(expr_str):
                    current_op = expr_str[i:i+op_len]
                    if current_op in OPERATORS:
                        prec = OPERATORS[current_op]
                        
                        # Use <= to ensure we get the rightmost operator for lowest precedence
                        # This correctly handles left-associativity for operators of the same precedence
                        if prec <= lowest_prec_found:
                            lowest_prec_found = prec
                            best_op_idx = i
                            best_op = current_op
                        i += op_len - 1 # Adjust index for the operator length
                        break # Found an operator, move to the next char after it
            
        i += 1 # Move to the next character

    if best_op_idx != -1:
        return (best_op, best_op_idx)
    return None


def parse_expression(expr_str, current_node_being_parsed):
    expr_str = expr_str.strip()
    if not expr_str:
        return set()

    # 1. Base Cases: Raw Data, Defined Factors, Numbers
    if expr_str in RAW_DATA_FIELDS:
        return {expr_str}
    
    if expr_str in all_defined_factor_names:
        return {expr_str}
        
    if re.fullmatch(r'[-+]?\d+\.?\d*(?:e[-+]?\d+)?', expr_str):
        return set()

    # Remove outermost parentheses if truly outermost
    if expr_str.startswith('(') and expr_str.endswith(')'):
        balance = 0
        is_outermost = True
        for i in range(1, len(expr_str) - 1):
            if expr_str[i] == '(':
                balance += 1
            elif expr_str[i] == ')':
                balance -= 1
            if balance < 0: # Mismatched or not outermost, e.g., (A)(B)
                is_outermost = False
                break
        if is_outermost and balance == 0:
            return parse_expression(expr_str[1:-1].strip(), current_node_being_parsed)


    # 2. Function Calls (e.g., FuncName(arg1, arg2))
    func_match = re.match(r'([a-zA-Z_][a-zA-Z0-9_]*)\((.*)\)$', expr_str)
    if func_match:
        func_name = func_match.group(1)
        args_str = func_match.group(2)
        
        if func_name in get_func_to_factor_map:
            actual_factor_name = get_func_to_factor_map[func_name]
            dependencies[current_node_being_parsed].add(actual_factor_name)
            reverse_dependencies[actual_factor_name].add(current_node_being_parsed)
            
            arg_list = clean_and_split_args(args_str)
            for arg_expr in arg_list:
                parse_expression(arg_expr, actual_factor_name)
            return {actual_factor_name}

        # Generic function (ts_Corr, Abs, IfThen, ts_Delay, etc.)
        arg_list = clean_and_split_args(args_str)
        current_expr_direct_deps = set()
        for arg_expr in arg_list:
            deps_from_arg = parse_expression(arg_expr, current_node_being_parsed)
            current_expr_direct_deps.update(deps_from_arg)
            
            for dep_item in deps_from_arg:
                if dep_item != current_node_being_parsed and not re.fullmatch(r'[-+]?\d+\.?\d*(?:e[-+]?\d+)?', dep_item):
                    dependencies[current_node_being_parsed].add(dep_item)
                    reverse_dependencies[dep_item].add(current_node_being_parsed)
        return current_expr_direct_deps

    # 3. Complex Infix Expressions (e.g., A + B, A / B - 1)
    op_info = find_lowest_precedence_operator(expr_str)

    if op_info:
        op, op_idx = op_info
        left_expr = expr_str[:op_idx].strip()
        right_expr = expr_str[op_idx + len(op):].strip()

        # Create a virtual factor for the entire infix expression
        virtual_name = get_virtual_factor_name(expr_str)
        dependencies[current_node_being_parsed].add(virtual_name)
        reverse_dependencies[virtual_name].add(current_node_being_parsed)

        # The virtual factor itself depends on its left and right operands
        parse_expression(left_expr, virtual_name)
        parse_expression(right_expr, virtual_name)
        
        return {virtual_name} # The direct dependency is the virtual factor itself
    
    # 4. Fallback for unhandled identifiers (e.g., string literals, or single identifier expressions
    # that are not raw data or defined factors, often due to complex parsing)
    if re.match(r'^[a-zA-Z_]', expr_str) and not re.fullmatch(r'[-+]?\d+\.?\d*(?:e[-+]?\d+)?', expr_str):
        # If it looks like an identifier and hasn't been parsed as anything else,
        # treat it as a virtual factor representing itself.
        virtual_name = get_virtual_factor_name(expr_str)
        dependencies[current_node_being_parsed].add(virtual_name)
        reverse_dependencies[virtual_name].add(current_node_being_parsed)
        return {virtual_name}
    elif expr_str.startswith("'") and expr_str.endswith("'"): # string literals like 'D'
        return set() # string literals are not dependencies
        
    return set() # Default for unhandled or non-dependency expressions


#  Main Parsing Loop 
print(" Step 1: Parsing Factor Expressions and Building Dependencies ")
for current_factor_name, expression in factor_name_to_form.items():
    print(f"Parsing factor: {current_factor_name} -> '{expression}'")
    parse_expression(expression, current_factor_name)

#  Verification of Virtual Factor Dependencies 
print("\n Verifying Virtual Factor Internal Dependencies ")
# Iterate a few times to ensure all levels of virtual factors are processed.
for _ in range(5):
    for virtual_expr_str, virtual_name in list(virtual_factor_map.items()):
        # Only re-parse if it's a complex expression and its dependencies haven't been found yet
        # And ensure it's not just a numerical string (already handled)
        if virtual_name in all_graph_nodes and not re.fullmatch(r'[-+]?\d+\.?\d*(?:e[-+]?\d+)?', virtual_expr_str):
            initial_deps_count = len(dependencies[virtual_name])
            parse_expression(virtual_expr_str, virtual_name)
            if len(dependencies[virtual_name]) > initial_deps_count: # If new deps were added
                print(f"  Re-parsed and added dependencies for virtual factor '{virtual_name}': '{virtual_expr_str}'")


print("\n Direct Dependencies (Node -> Its Direct Dependencies) ")
for node, deps in sorted(dependencies.items()):
    if deps:
        print(f"'{node}': {', '.join(sorted(deps))}")

print("\n Reverse Dependencies (Dependent Node -> Nodes that depend on it) ")
for dep, factors in sorted(reverse_dependencies.items()):
    if factors:
        print(f"'{dep}': {', '.join(sorted(factors))}")


### Step 2: Find All Reachable Dependencies (Direct and Indirect)

print("\n All Dependencies (Direct and Indirect) ")

def find_all_reachable_nodes(start_node, adj_list):
    q = deque([start_node])
    visited = {start_node}
    
    while q:
        node = q.popleft()
        for neighbor in adj_list.get(node, set()):
            if neighbor not in visited:
                visited.add(neighbor)
                q.append(neighbor)
    
    if start_node in all_defined_factor_names or start_node.startswith("VIRTUAL_EXPR_"):
        visited.discard(start_node)
    
    return sorted(list(visited))

for node_name in sorted(list(all_defined_factor_names) + list(virtual_factor_map.values())):
    all_deps = find_all_reachable_nodes(node_name, dependencies)
    if all_deps:
        print(f"'{node_name}' dependencies: {', '.join(all_deps)}")
    else:
        print(f"'{node_name}' has no external dependencies (might be a direct raw data or self-contained).")


### Step 3: Identify "Base Factors" (Only Depend on Raw Data)

print("\n Base Factors (Only Depend on Raw Data) ")
base_factors = []
for node_name in sorted(list(all_defined_factor_names) + list(virtual_factor_map.values())):
    direct_deps = dependencies.get(node_name, set())
    if all(dep in RAW_DATA_FIELDS for dep in direct_deps):
        base_factors.append(node_name)
print(f"Nodes that only depend on raw data: {', '.join(sorted(base_factors))}")


### Step 4: Determine Factor Calculation Order (Topological Sort)

print("\n Factor Calculation Order (Topological Sort) ")

in_degree = defaultdict(int)
for node in all_graph_nodes:
    in_degree[node] = 0

for node, deps in dependencies.items():
    if node not in RAW_DATA_FIELDS:
        for dep in deps:
            in_degree[node] += 1

q = deque()
for node in all_graph_nodes:
    if in_degree[node] == 0:
        q.append(node)

topological_order = []
factor_levels = defaultdict(int)

while q:
    node = q.popleft()
    topological_order.append(node)

    current_level = 0
    if node in dependencies:
        for dep_node in dependencies.get(node, set()):
            if dep_node in factor_levels:
                current_level = max(current_level, factor_levels[dep_node] + 1)
    factor_levels[node] = current_level

    for dependent_node in reverse_dependencies[node]:
        if dependent_node in all_graph_nodes:
            in_degree[dependent_node] -= 1
            if in_degree[dependent_node] == 0:
                q.append(dependent_node)

calculated_nodes_in_order = [f for f in topological_order if f not in RAW_DATA_FIELDS]

if len(calculated_nodes_in_order) == (len(all_defined_factor_names) + len(virtual_factor_map)):
    print("Topological sort successful. Factor computation order by level:")
    levels_output = defaultdict(list)
    for node in calculated_nodes_in_order:
        levels_output[factor_levels[node]].append(node)
    
    for level in sorted(levels_output.keys()):
        print(f"  Level {level}: {', '.join(sorted(levels_output[level]))}")
else:
    print("Warning: Topological sort failed. This might indicate cyclic dependencies or unhandled nodes.")
    print("Sorted nodes:", calculated_nodes_in_order)
    print("Remaining un-sorted nodes (potential cycles):", all_graph_nodes - set(topological_order))



### Step 5: Generate the Dependency Graph (`.dot` file)

print("\n Generating Dependency Graph (.dot file) ")

dot = graphviz.Digraph(comment='Factor Dependency Graph', graph_attr={'rankdir': 'LR', 'splines': 'true'})

# Add nodes
for node in all_graph_nodes:
    if node in RAW_DATA_FIELDS:
        dot.node(node, node, shape='box', style='filled', fillcolor='lightblue') # Raw data as blue boxes
    elif node.startswith("VIRTUAL_EXPR_"):
        # For virtual factors, use their original expression as the label for clarity
        original_expr = next((expr for expr, v_name in virtual_factor_map.items() if v_name == node), node)
        # Limit label length for better readability in graph
        display_label = original_expr
        if len(display_label) > 50:
            display_label = display_label[:47] + "..."
        dot.node(node, display_label, shape='ellipse', style='filled', fillcolor='lightyellow', fontsize='10')
    else:
        dot.node(node, node, shape='oval', style='filled', fillcolor='lightgreen') # Defined factors as green ovals

# Add edges (dependencies)
for dependent_node, deps in dependencies.items():
    for dependency_node in deps:
        if dependent_node != dependency_node:
            dot.edge(dependency_node, dependent_node)

output_filepath = "factor_dependency_graph.dot"
try:
    with open(output_filepath, "w") as f:
        f.write(dot.source) # Directly write the DOT source string to the file
    print(f"Graph definition saved as '{output_filepath}'")
    print("You can upload this .dot file to an online Graphviz viewer (e.g., https://dreampuf.github.io/GraphvizOnline/) to visualize it.")
except Exception as e:
    print(f"Error writing .dot file: {e}")
    print("Please ensure you have write permissions in the current directory.")