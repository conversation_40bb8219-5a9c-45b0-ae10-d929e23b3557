// Factor Dependency Graph
digraph {
	graph [rankdir=LR splines=true]
	VIRTUAL_EXPR_14 [label="Volume-ts_Mean(Volume,30)" fillcolor=lightyellow fontsize=10 shape=ellipse style=filled]
	VIRTUAL_EXPR_83 [label="(High-Low)/Close" fillcolor=lightyellow fontsize=10 shape=ellipse style=filled]
	p2_et16 [label=p2_et16 fillcolor=lightgreen shape=oval style=filled]
	p5_to0 [label=p5_to0 fillcolor=lightgreen shape=oval style=filled]
	cci [label=cci fillcolor=lightgreen shape=oval style=filled]
	p4_ms5 [label=p4_ms5 fillcolor=lightgreen shape=oval style=filled]
	VIRTUAL_EXPR_22 [label="Close/ts_Delay(Close,1)-1-pn_Mean(Close/ts_Dela..." fillcolor=lightyellow fontsize=10 shape=ellipse style=filled]
	VIRTUAL_EXPR_64 [label="Open-Close" fillcolor=lightyellow fontsize=10 shape=ellipse style=filled]
	p2_et2 [label=p2_et2 fillcolor=lightgreen shape=oval style=filled]
	p2_et12 [label=p2_et12 fillcolor=lightgreen shape=oval style=filled]
	VIRTUAL_EXPR_6 [label="Volume-ts_Delay(Volume,1)-Tot_Mean(Volume-ts_De..." fillcolor=lightyellow fontsize=10 shape=ellipse style=filled]
	p1_corrs0 [label=p1_corrs0 fillcolor=lightgreen shape=oval style=filled]
	VIRTUAL_EXPR_18 [label="0.10*ts_Stdev(Volume,30)" fillcolor=lightyellow fontsize=10 shape=ellipse style=filled]
	Volume [label=Volume fillcolor=lightblue shape=box style=filled]
	kama [label=kama fillcolor=lightgreen shape=oval style=filled]
	VIRTUAL_EXPR_20 [label="Close/ts_Delay(Close,1)-1-pn_Mean(Close/ts_Dela..." fillcolor=lightyellow fontsize=10 shape=ellipse style=filled]
	VIRTUAL_EXPR_40 [label="High-Low" fillcolor=lightyellow fontsize=10 shape=ellipse style=filled]
	VIRTUAL_EXPR_44 [label="0.2-Tot_Rank(Volume)" fillcolor=lightyellow fontsize=10 shape=ellipse style=filled]
	VIRTUAL_EXPR_11 [label="Close/ts_Delay(Close,1)-1-Tot_Mean(Close/ts_Del..." fillcolor=lightyellow fontsize=10 shape=ellipse style=filled]
	VIRTUAL_EXPR_79 [label="(ts_Max(High,30)-Close)/(ts_Max(High,30)-ts_Min..." fillcolor=lightyellow fontsize=10 shape=ellipse style=filled]
	VIRTUAL_EXPR_61 [label="(2*(High-Low)-Abs(Open-Close))/Close)/Tot_Sum(A..." fillcolor=lightyellow fontsize=10 shape=ellipse style=filled]
	VIRTUAL_EXPR_38 [label="Close/Open-1)/Log(Volume" fillcolor=lightyellow fontsize=10 shape=ellipse style=filled]
	p6_tn12 [label=p6_tn12 fillcolor=lightgreen shape=oval style=filled]
	p2_et10 [label=p2_et10 fillcolor=lightgreen shape=oval style=filled]
	VIRTUAL_EXPR_1 [label="Close/ts_Delay(Close,1)" fillcolor=lightyellow fontsize=10 shape=ellipse style=filled]
	p5_to6 [label=p5_to6 fillcolor=lightgreen shape=oval style=filled]
	VIRTUAL_EXPR_72 [label="((High+Low)-ts_Delay(High+Low,1))*(High-Low)/2/..." fillcolor=lightyellow fontsize=10 shape=ellipse style=filled]
	VIRTUAL_EXPR_23 [label="Close/ts_Delay(Close,1)-1-pn_Mean(Close/ts_Dela..." fillcolor=lightyellow fontsize=10 shape=ellipse style=filled]
	VIRTUAL_EXPR_37 [label="Tot_Rank(Abs(Close/Open-1)/Log(Volume))-0.8" fillcolor=lightyellow fontsize=10 shape=ellipse style=filled]
	VIRTUAL_EXPR_55 [label="Amount)/(Tot_Sum(Abs(High-Low)/Close+Abs(Open-t..." fillcolor=lightyellow fontsize=10 shape=ellipse style=filled]
	VIRTUAL_EXPR_33 [label="Close/Open" fillcolor=lightyellow fontsize=10 shape=ellipse style=filled]
	p6_tn5 [label=p6_tn5 fillcolor=lightgreen shape=oval style=filled]
	VIRTUAL_EXPR_26 [label="-(Close/ts_Delay(Close,1)-1)" fillcolor=lightyellow fontsize=10 shape=ellipse style=filled]
	p3_mf1 [label=p3_mf1 fillcolor=lightgreen shape=oval style=filled]
	p6_tn3 [label=p6_tn3 fillcolor=lightgreen shape=oval style=filled]
	p1_corrs8 [label=p1_corrs8 fillcolor=lightgreen shape=oval style=filled]
	VIRTUAL_EXPR_69 [label="High-Open)-Tot_Sum(Open-Low" fillcolor=lightyellow fontsize=10 shape=ellipse style=filled]
	p1_corrs11 [label=p1_corrs11 fillcolor=lightgreen shape=oval style=filled]
	VIRTUAL_EXPR_84 [label="Tot_Rank((Close-ts_Delay(Close,1))/Close)-0.910" fillcolor=lightyellow fontsize=10 shape=ellipse style=filled]
	p6_tn8 [label=p6_tn8 fillcolor=lightgreen shape=oval style=filled]
	VIRTUAL_EXPR_36 [label="Close/Open-1)+(Volume" fillcolor=lightyellow fontsize=10 shape=ellipse style=filled]
	VIRTUAL_EXPR_80 [label="ts_Max(High,30)-Close" fillcolor=lightyellow fontsize=10 shape=ellipse style=filled]
	p6_tn10 [label=p6_tn10 fillcolor=lightgreen shape=oval style=filled]
	dcperiod [label=dcperiod fillcolor=lightgreen shape=oval style=filled]
	VIRTUAL_EXPR_65 [label="Close/ts_Delay(Close,1)-1)/(Close*Volume" fillcolor=lightyellow fontsize=10 shape=ellipse style=filled]
	p4_ms2 [label=p4_ms2 fillcolor=lightgreen shape=oval style=filled]
	VIRTUAL_EXPR_42 [label="Tot_Rank(Volume)-0.8" fillcolor=lightyellow fontsize=10 shape=ellipse style=filled]
	VIRTUAL_EXPR_4 [label="VWAP/ts_Delay(VWAP,1)-1" fillcolor=lightyellow fontsize=10 shape=ellipse style=filled]
	p2_et8 [label=p2_et8 fillcolor=lightgreen shape=oval style=filled]
	p3_mf12 [label=p3_mf12 fillcolor=lightgreen shape=oval style=filled]
	VIRTUAL_EXPR_8 [label="Tot_Rank(Volume-ts_Delay(Volume,1))-0.9" fillcolor=lightyellow fontsize=10 shape=ellipse style=filled]
	High [label=High fillcolor=lightblue shape=box style=filled]
	p6_tn1 [label=p6_tn1 fillcolor=lightgreen shape=oval style=filled]
	p1_corrs15 [label=p1_corrs15 fillcolor=lightgreen shape=oval style=filled]
	VIRTUAL_EXPR_67 [label="Close-(ts_Mean(Close,30)+ts_Stdev(Close,30))" fillcolor=lightyellow fontsize=10 shape=ellipse style=filled]
	VIRTUAL_EXPR_70 [label="High-Open)-Tot_Sum(Open" fillcolor=lightyellow fontsize=10 shape=ellipse style=filled]
	VIRTUAL_EXPR_30 [label="Tot_Rank(Close/ts_Delay(Close,1)-1)-0.93" fillcolor=lightyellow fontsize=10 shape=ellipse style=filled]
	p4_ms6 [label=p4_ms6 fillcolor=lightgreen shape=oval style=filled]
	p6_tn7 [label=p6_tn7 fillcolor=lightgreen shape=oval style=filled]
	VIRTUAL_EXPR_48 [label="(Log(Close/ts_Delay(Close,1)))**2" fillcolor=lightyellow fontsize=10 shape=ellipse style=filled]
	p3_mf7 [label=p3_mf7 fillcolor=lightgreen shape=oval style=filled]
	cmo [label=cmo fillcolor=lightgreen shape=oval style=filled]
	VIRTUAL_EXPR_49 [label="((Close-ts_Delay(Close,1))/Close)**2" fillcolor=lightyellow fontsize=10 shape=ellipse style=filled]
	p6_tn0 [label=p6_tn0 fillcolor=lightgreen shape=oval style=filled]
	liangle [label=liangle fillcolor=lightgreen shape=oval style=filled]
	p5_to3 [label=p5_to3 fillcolor=lightgreen shape=oval style=filled]
	VIRTUAL_EXPR_53 [label="-Amount" fillcolor=lightyellow fontsize=10 shape=ellipse style=filled]
	VIRTUAL_EXPR_5 [label="VWAP/ts_Delay(VWAP,1)" fillcolor=lightyellow fontsize=10 shape=ellipse style=filled]
	VIRTUAL_EXPR_21 [label="Close/ts_Delay(Close,1)-1-pn_Mean(Close/ts_Dela..." fillcolor=lightyellow fontsize=10 shape=ellipse style=filled]
	p1_corrs12 [label=p1_corrs12 fillcolor=lightgreen shape=oval style=filled]
	VIRTUAL_EXPR_56 [label="1+Tot_Stdev(Close)/Tot_Mean(Close)" fillcolor=lightyellow fontsize=10 shape=ellipse style=filled]
	p2_et7 [label=p2_et7 fillcolor=lightgreen shape=oval style=filled]
	p2_et3 [label=p2_et3 fillcolor=lightgreen shape=oval style=filled]
	VIRTUAL_EXPR_31 [label="0.07-Tot_Rank(Close/ts_Delay(Close,1)-1)" fillcolor=lightyellow fontsize=10 shape=ellipse style=filled]
	p3_mf3 [label=p3_mf3 fillcolor=lightgreen shape=oval style=filled]
	VIRTUAL_EXPR_78 [label="High-ts_Delay(Close,1))/Tot_Sum(ts_Delay(Close,1)" fillcolor=lightyellow fontsize=10 shape=ellipse style=filled]
	p4_ms4 [label=p4_ms4 fillcolor=lightgreen shape=oval style=filled]
	p1_corrs10 [label=p1_corrs10 fillcolor=lightgreen shape=oval style=filled]
	p2_et13 [label=p2_et13 fillcolor=lightgreen shape=oval style=filled]
	p1_corrs1 [label=p1_corrs1 fillcolor=lightgreen shape=oval style=filled]
	adosc [label=adosc fillcolor=lightgreen shape=oval style=filled]
	VIRTUAL_EXPR_9 [label="Tot_Rank(Close/ts_Delay(Close,1)-1)-0.9" fillcolor=lightyellow fontsize=10 shape=ellipse style=filled]
	VIRTUAL_EXPR_59 [label="(High-Low)/Close)/Tot_Sum(Amount" fillcolor=lightyellow fontsize=10 shape=ellipse style=filled]
	p6_tn11 [label=p6_tn11 fillcolor=lightgreen shape=oval style=filled]
	VIRTUAL_EXPR_63 [label="2*(High-Low)" fillcolor=lightyellow fontsize=10 shape=ellipse style=filled]
	VIRTUAL_EXPR_17 [label="0)*IfThen(Volume-ts_Mean(Volume,30)" fillcolor=lightyellow fontsize=10 shape=ellipse style=filled]
	p3_mf8 [label=p3_mf8 fillcolor=lightgreen shape=oval style=filled]
	VIRTUAL_EXPR_66 [label="1)/(Close*Volume" fillcolor=lightyellow fontsize=10 shape=ellipse style=filled]
	VIRTUAL_EXPR_43 [label="(Close-Open)/Open" fillcolor=lightyellow fontsize=10 shape=ellipse style=filled]
	p4_ms0 [label=p4_ms0 fillcolor=lightgreen shape=oval style=filled]
	VIRTUAL_EXPR_45 [label="Close/ts_Delay(Close,1)-1-Log(Close/ts_Delay(Cl..." fillcolor=lightyellow fontsize=10 shape=ellipse style=filled]
	p1_corrs3 [label=p1_corrs3 fillcolor=lightgreen shape=oval style=filled]
	p2_et18 [label=p2_et18 fillcolor=lightgreen shape=oval style=filled]
	p2_et6 [label=p2_et6 fillcolor=lightgreen shape=oval style=filled]
	p2_et19 [label=p2_et19 fillcolor=lightgreen shape=oval style=filled]
	p1_corrs6 [label=p1_corrs6 fillcolor=lightgreen shape=oval style=filled]
	p3_mf6 [label=p3_mf6 fillcolor=lightgreen shape=oval style=filled]
	VIRTUAL_EXPR_34 [label="Tot_Rank(Abs(Close/Open-1)/Sqrt(Volume))-0.8" fillcolor=lightyellow fontsize=10 shape=ellipse style=filled]
	VIRTUAL_EXPR_81 [label="(Close-ts_Min(Low,30))/(ts_Max(High,30)-ts_Min(..." fillcolor=lightyellow fontsize=10 shape=ellipse style=filled]
	p1_corrs2 [label=p1_corrs2 fillcolor=lightgreen shape=oval style=filled]
	p5_to7 [label=p5_to7 fillcolor=lightgreen shape=oval style=filled]
	VIRTUAL_EXPR_32 [label="Close/Open-1)/Sqrt(Volume" fillcolor=lightyellow fontsize=10 shape=ellipse style=filled]
	p3_mf9 [label=p3_mf9 fillcolor=lightgreen shape=oval style=filled]
	VIRTUAL_EXPR_13 [label="Volume-ts_Mean(Volume,30)-1.210*ts_Stdev(Volume..." fillcolor=lightyellow fontsize=10 shape=ellipse style=filled]
	VIRTUAL_EXPR_58 [label="Abs(Close/ts_Delay(Close,1)-1)/Amount" fillcolor=lightyellow fontsize=10 shape=ellipse style=filled]
	VIRTUAL_EXPR_60 [label="Close)/Tot_Sum(Amount" fillcolor=lightyellow fontsize=10 shape=ellipse style=filled]
	p3_mf11 [label=p3_mf11 fillcolor=lightgreen shape=oval style=filled]
	VIRTUAL_EXPR_41 [label="Volume*Close" fillcolor=lightyellow fontsize=10 shape=ellipse style=filled]
	p2_et4 [label=p2_et4 fillcolor=lightgreen shape=oval style=filled]
	Open [label=Open fillcolor=lightblue shape=box style=filled]
	p3_mf2 [label=p3_mf2 fillcolor=lightgreen shape=oval style=filled]
	VIRTUAL_EXPR_16 [label="0)*IfThen(Volume-ts_Mean(Volume,30)-0.10*ts_Std..." fillcolor=lightyellow fontsize=10 shape=ellipse style=filled]
	VIRTUAL_EXPR_27 [label="Close/ts_Delay(Close,1)-1)-Tot_Stdev(Close/ts_D..." fillcolor=lightyellow fontsize=10 shape=ellipse style=filled]
	p6_tn9 [label=p6_tn9 fillcolor=lightgreen shape=oval style=filled]
	VIRTUAL_EXPR_86 [label="Tot_Rank(High-Low)-0.10" fillcolor=lightyellow fontsize=10 shape=ellipse style=filled]
	VIRTUAL_EXPR_7 [label="Volume-ts_Delay(Volume,1)-Tot_Mean(Volume-ts_De..." fillcolor=lightyellow fontsize=10 shape=ellipse style=filled]
	VIRTUAL_EXPR_52 [label="((Close-ts_Delay(Close,1))/Close)**3" fillcolor=lightyellow fontsize=10 shape=ellipse style=filled]
	p6_tn2 [label=p6_tn2 fillcolor=lightgreen shape=oval style=filled]
	p1_corrs14 [label=p1_corrs14 fillcolor=lightgreen shape=oval style=filled]
	p1_corrs5 [label=p1_corrs5 fillcolor=lightgreen shape=oval style=filled]
	ultosc [label=ultosc fillcolor=lightgreen shape=oval style=filled]
	VIRTUAL_EXPR_25 [label="-(Close/ts_Delay(Close,1)-1)+(Tot_Mean(Close/ts..." fillcolor=lightyellow fontsize=10 shape=ellipse style=filled]
	VIRTUAL_EXPR_71 [label="Open)-Tot_Sum(Open" fillcolor=lightyellow fontsize=10 shape=ellipse style=filled]
	VIRTUAL_EXPR_0 [label="Close/ts_Delay(Close,1)-1" fillcolor=lightyellow fontsize=10 shape=ellipse style=filled]
	VIRTUAL_EXPR_47 [label="(Log(Close/ts_Delay(Close,1)))**2/2" fillcolor=lightyellow fontsize=10 shape=ellipse style=filled]
	dm [label=dm fillcolor=lightgreen shape=oval style=filled]
	VIRTUAL_EXPR_75 [label="(High+Low)-ts_Delay(High+Low,1)" fillcolor=lightyellow fontsize=10 shape=ellipse style=filled]
	p4_ms1 [label=p4_ms1 fillcolor=lightgreen shape=oval style=filled]
	p6_tn13 [label=p6_tn13 fillcolor=lightgreen shape=oval style=filled]
	p2_et11 [label=p2_et11 fillcolor=lightgreen shape=oval style=filled]
	VIRTUAL_EXPR_35 [label="Tot_Rank(Abs(Close/Open-1)+(Volume))-0.8" fillcolor=lightyellow fontsize=10 shape=ellipse style=filled]
	VIRTUAL_EXPR_24 [label="Close/ts_Delay(Close,1)-1))/(Abs(pn_Mean(Close/..." fillcolor=lightyellow fontsize=10 shape=ellipse style=filled]
	dcphase [label=dcphase fillcolor=lightgreen shape=oval style=filled]
	p3_mf5 [label=p3_mf5 fillcolor=lightgreen shape=oval style=filled]
	VIRTUAL_EXPR_54 [label="Amount)/(Tot_Sum(Abs(High-Low)/Close+Abs(Open-t..." fillcolor=lightyellow fontsize=10 shape=ellipse style=filled]
	VIRTUAL_EXPR_12 [label="(Close/ts_Delay(Close,1)-1)*IfThen(Volume-ts_Me..." fillcolor=lightyellow fontsize=10 shape=ellipse style=filled]
	VIRTUAL_EXPR_50 [label="(Close-ts_Delay(Close,1))/Close" fillcolor=lightyellow fontsize=10 shape=ellipse style=filled]
	VIRTUAL_EXPR_85 [label="0.01-(Close-ts_Delay(Close,1))/Close" fillcolor=lightyellow fontsize=10 shape=ellipse style=filled]
	p5_to5 [label=p5_to5 fillcolor=lightgreen shape=oval style=filled]
	VIRTUAL_EXPR_3 [label="Volume-ts_Delay(Volume,1) - ts_Delay(Volume-ts_..." fillcolor=lightyellow fontsize=10 shape=ellipse style=filled]
	VIRTUAL_EXPR_73 [label="((High+Low)-ts_Delay(High+Low,1))*(High-Low)/2" fillcolor=lightyellow fontsize=10 shape=ellipse style=filled]
	VIRTUAL_EXPR_15 [label="1.210*ts_Stdev(Volume,30)" fillcolor=lightyellow fontsize=10 shape=ellipse style=filled]
	VIRTUAL_EXPR_10 [label="Close/ts_Delay(Close,1)-1-Tot_Mean(Close/ts_Del..." fillcolor=lightyellow fontsize=10 shape=ellipse style=filled]
	VIRTUAL_EXPR_62 [label="2*(High-Low)-Abs(Open-Close)" fillcolor=lightyellow fontsize=10 shape=ellipse style=filled]
	VWAP [label=VWAP fillcolor=lightblue shape=box style=filled]
	p6_tn6 [label=p6_tn6 fillcolor=lightgreen shape=oval style=filled]
	VIRTUAL_EXPR_39 [label="Close-Open" fillcolor=lightyellow fontsize=10 shape=ellipse style=filled]
	p3_mf0 [label=p3_mf0 fillcolor=lightgreen shape=oval style=filled]
	p1_corrs13 [label=p1_corrs13 fillcolor=lightgreen shape=oval style=filled]
	VIRTUAL_EXPR_82 [label="Close-ts_Min(Low,30)" fillcolor=lightyellow fontsize=10 shape=ellipse style=filled]
	p2_et0 [label=p2_et0 fillcolor=lightgreen shape=oval style=filled]
	VIRTUAL_EXPR_29 [label="1)-Tot_Stdev(Close/ts_Delay(Close,1)" fillcolor=lightyellow fontsize=10 shape=ellipse style=filled]
	p2_et1 [label=p2_et1 fillcolor=lightgreen shape=oval style=filled]
	Amount [label=Amount fillcolor=lightblue shape=box style=filled]
	p5_to2 [label=p5_to2 fillcolor=lightgreen shape=oval style=filled]
	p5_to1 [label=p5_to1 fillcolor=lightgreen shape=oval style=filled]
	VIRTUAL_EXPR_88 [label="-Volume" fillcolor=lightyellow fontsize=10 shape=ellipse style=filled]
	p2_et14 [label=p2_et14 fillcolor=lightgreen shape=oval style=filled]
	VIRTUAL_EXPR_19 [label="ts_Delay(ts_Min(Low,30),1)-Low" fillcolor=lightyellow fontsize=10 shape=ellipse style=filled]
	VIRTUAL_EXPR_57 [label="Close)/Tot_Mean(Close" fillcolor=lightyellow fontsize=10 shape=ellipse style=filled]
	VIRTUAL_EXPR_2 [label="Volume-ts_Delay(Volume,1)" fillcolor=lightyellow fontsize=10 shape=ellipse style=filled]
	VIRTUAL_EXPR_74 [label="((High+Low)-ts_Delay(High+Low,1))*(High-Low)" fillcolor=lightyellow fontsize=10 shape=ellipse style=filled]
	VIRTUAL_EXPR_77 [label="High-ts_Delay(Close,1))/Tot_Sum(ts_Delay(Close,..." fillcolor=lightyellow fontsize=10 shape=ellipse style=filled]
	p2_et9 [label=p2_et9 fillcolor=lightgreen shape=oval style=filled]
	VIRTUAL_EXPR_46 [label="Close/ts_Delay(Close,1)-1-Log(Close/ts_Delay(Cl..." fillcolor=lightyellow fontsize=10 shape=ellipse style=filled]
	p4_ms3 [label=p4_ms3 fillcolor=lightgreen shape=oval style=filled]
	VIRTUAL_EXPR_28 [label="Close/ts_Delay(Close,1)-1)-Tot_Stdev(Close/ts_D..." fillcolor=lightyellow fontsize=10 shape=ellipse style=filled]
	p3_mf4 [label=p3_mf4 fillcolor=lightgreen shape=oval style=filled]
	p1_corrs4 [label=p1_corrs4 fillcolor=lightgreen shape=oval style=filled]
	p1_corrs9 [label=p1_corrs9 fillcolor=lightgreen shape=oval style=filled]
	lislope [label=lislope fillcolor=lightgreen shape=oval style=filled]
	Low [label=Low fillcolor=lightblue shape=box style=filled]
	p1_corrs7 [label=p1_corrs7 fillcolor=lightgreen shape=oval style=filled]
	di [label=di fillcolor=lightgreen shape=oval style=filled]
	p2_et15 [label=p2_et15 fillcolor=lightgreen shape=oval style=filled]
	VIRTUAL_EXPR_76 [label="High+Low" fillcolor=lightyellow fontsize=10 shape=ellipse style=filled]
	dx [label=dx fillcolor=lightgreen shape=oval style=filled]
	p3_mf10 [label=p3_mf10 fillcolor=lightgreen shape=oval style=filled]
	VIRTUAL_EXPR_68 [label="(ts_Mean(Close,30)-ts_Stdev(Close,30))-Close" fillcolor=lightyellow fontsize=10 shape=ellipse style=filled]
	p6_tn4 [label=p6_tn4 fillcolor=lightgreen shape=oval style=filled]
	p2_et17 [label=p2_et17 fillcolor=lightgreen shape=oval style=filled]
	VIRTUAL_EXPR_87 [label="0))-Tot_Sum(IfThen(Tot_Rank(High-Low)-0.10" fillcolor=lightyellow fontsize=10 shape=ellipse style=filled]
	VIRTUAL_EXPR_51 [label="Close-ts_Delay(Close,1)" fillcolor=lightyellow fontsize=10 shape=ellipse style=filled]
	p2_et5 [label=p2_et5 fillcolor=lightgreen shape=oval style=filled]
	p5_to4 [label=p5_to4 fillcolor=lightgreen shape=oval style=filled]
	Close [label=Close fillcolor=lightblue shape=box style=filled]
	Volume -> p1_corrs0
	Close -> p1_corrs0
	Volume -> p1_corrs1
	VIRTUAL_EXPR_0 -> p1_corrs1
	VIRTUAL_EXPR_1 -> VIRTUAL_EXPR_0
	Close -> VIRTUAL_EXPR_1
	Volume -> p1_corrs2
	Close -> p1_corrs2
	Volume -> p1_corrs3
	Close -> p1_corrs3
	VIRTUAL_EXPR_0 -> p1_corrs4
	Close -> p1_corrs5
	VIRTUAL_EXPR_0 -> p1_corrs5
	VIRTUAL_EXPR_2 -> p1_corrs6
	Volume -> p1_corrs6
	Volume -> VIRTUAL_EXPR_2
	VIRTUAL_EXPR_2 -> p1_corrs7
	VIRTUAL_EXPR_3 -> p1_corrs7
	VIRTUAL_EXPR_2 -> VIRTUAL_EXPR_3
	VWAP -> p1_corrs8
	Volume -> p1_corrs8
	VIRTUAL_EXPR_4 -> p1_corrs9
	Volume -> p1_corrs9
	VIRTUAL_EXPR_5 -> VIRTUAL_EXPR_4
	VWAP -> VIRTUAL_EXPR_5
	VWAP -> p1_corrs10
	Volume -> p1_corrs10
	VWAP -> p1_corrs11
	Volume -> p1_corrs11
	VIRTUAL_EXPR_4 -> p1_corrs12
	VWAP -> p1_corrs13
	VIRTUAL_EXPR_4 -> p1_corrs13
	VIRTUAL_EXPR_2 -> p1_corrs14
	Volume -> p1_corrs14
	VIRTUAL_EXPR_2 -> p1_corrs15
	VIRTUAL_EXPR_3 -> p1_corrs15
	VIRTUAL_EXPR_6 -> p2_et0
	VIRTUAL_EXPR_0 -> p2_et0
	VIRTUAL_EXPR_2 -> VIRTUAL_EXPR_6
	VIRTUAL_EXPR_7 -> VIRTUAL_EXPR_6
	VIRTUAL_EXPR_2 -> VIRTUAL_EXPR_7
	VIRTUAL_EXPR_0 -> p2_et1
	VIRTUAL_EXPR_8 -> p2_et1
	VIRTUAL_EXPR_2 -> VIRTUAL_EXPR_8
	VIRTUAL_EXPR_9 -> p2_et2
	VIRTUAL_EXPR_0 -> p2_et2
	VIRTUAL_EXPR_0 -> VIRTUAL_EXPR_9
	VIRTUAL_EXPR_10 -> p2_et3
	VIRTUAL_EXPR_0 -> p2_et3
	VIRTUAL_EXPR_11 -> VIRTUAL_EXPR_10
	VIRTUAL_EXPR_0 -> VIRTUAL_EXPR_10
	VIRTUAL_EXPR_0 -> VIRTUAL_EXPR_11
	VIRTUAL_EXPR_9 -> p2_et4
	VIRTUAL_EXPR_0 -> p2_et4
	VIRTUAL_EXPR_0 -> p2_et5
	VIRTUAL_EXPR_0 -> p2_et6
	VIRTUAL_EXPR_0 -> p2_et7
	VIRTUAL_EXPR_0 -> p2_et8
	VIRTUAL_EXPR_12 -> p2_et9
	VIRTUAL_EXPR_13 -> VIRTUAL_EXPR_12
	VIRTUAL_EXPR_0 -> VIRTUAL_EXPR_12
	VIRTUAL_EXPR_14 -> VIRTUAL_EXPR_13
	VIRTUAL_EXPR_15 -> VIRTUAL_EXPR_13
	Volume -> VIRTUAL_EXPR_14
	Volume -> VIRTUAL_EXPR_15
	VIRTUAL_EXPR_16 -> p2_et10
	VIRTUAL_EXPR_0 -> p2_et10
	VIRTUAL_EXPR_18 -> VIRTUAL_EXPR_16
	VIRTUAL_EXPR_17 -> VIRTUAL_EXPR_16
	Volume -> VIRTUAL_EXPR_17
	Volume -> VIRTUAL_EXPR_18
	VIRTUAL_EXPR_19 -> p2_et11
	Low -> VIRTUAL_EXPR_19
	VIRTUAL_EXPR_20 -> p2_et12
	VIRTUAL_EXPR_0 -> VIRTUAL_EXPR_20
	VIRTUAL_EXPR_0 -> p2_et13
	Volume -> p2_et14
	VIRTUAL_EXPR_21 -> p2_et15
	VIRTUAL_EXPR_22 -> VIRTUAL_EXPR_21
	VIRTUAL_EXPR_23 -> VIRTUAL_EXPR_22
	VIRTUAL_EXPR_0 -> VIRTUAL_EXPR_22
	VIRTUAL_EXPR_24 -> VIRTUAL_EXPR_23
	VIRTUAL_EXPR_0 -> VIRTUAL_EXPR_23
	VIRTUAL_EXPR_1 -> VIRTUAL_EXPR_24
	VIRTUAL_EXPR_25 -> p2_et16
	Close -> p2_et16
	VIRTUAL_EXPR_0 -> p2_et16
	VIRTUAL_EXPR_26 -> VIRTUAL_EXPR_25
	VIRTUAL_EXPR_27 -> VIRTUAL_EXPR_25
	VIRTUAL_EXPR_0 -> VIRTUAL_EXPR_26
	VIRTUAL_EXPR_28 -> VIRTUAL_EXPR_27
	VIRTUAL_EXPR_29 -> VIRTUAL_EXPR_28
	VIRTUAL_EXPR_1 -> VIRTUAL_EXPR_28
	Close -> VIRTUAL_EXPR_29
	Close -> p2_et17
	VIRTUAL_EXPR_30 -> p2_et17
	VIRTUAL_EXPR_0 -> p2_et17
	VIRTUAL_EXPR_0 -> VIRTUAL_EXPR_30
	VIRTUAL_EXPR_31 -> p2_et18
	Close -> p2_et18
	VIRTUAL_EXPR_0 -> p2_et18
	VIRTUAL_EXPR_0 -> VIRTUAL_EXPR_31
	Close -> p2_et19
	VIRTUAL_EXPR_0 -> p2_et19
	VIRTUAL_EXPR_32 -> p3_mf0
	VIRTUAL_EXPR_33 -> VIRTUAL_EXPR_32
	VIRTUAL_EXPR_32 -> p3_mf1
	VIRTUAL_EXPR_34 -> p3_mf2
	Close -> p3_mf2
	VIRTUAL_EXPR_32 -> VIRTUAL_EXPR_34
	VIRTUAL_EXPR_35 -> p3_mf3
	Close -> p3_mf3
	VIRTUAL_EXPR_36 -> VIRTUAL_EXPR_35
	VIRTUAL_EXPR_33 -> VIRTUAL_EXPR_36
	Close -> p3_mf4
	VIRTUAL_EXPR_37 -> p3_mf4
	VIRTUAL_EXPR_38 -> VIRTUAL_EXPR_37
	VIRTUAL_EXPR_33 -> VIRTUAL_EXPR_38
	Close -> p3_mf5
	VIRTUAL_EXPR_39 -> p3_mf5
	Amount -> p3_mf5
	VIRTUAL_EXPR_40 -> p3_mf5
	VIRTUAL_EXPR_34 -> p3_mf6
	Volume -> p3_mf6
	Close -> p3_mf6
	VIRTUAL_EXPR_0 -> p3_mf7
	VIRTUAL_EXPR_0 -> p3_mf8
	VIRTUAL_EXPR_41 -> p3_mf9
	VIRTUAL_EXPR_0 -> p3_mf9
	VIRTUAL_EXPR_41 -> p3_mf10
	Close -> p3_mf10
	VIRTUAL_EXPR_0 -> p3_mf10
	Close -> p3_mf11
	VIRTUAL_EXPR_42 -> p3_mf11
	VIRTUAL_EXPR_43 -> p3_mf11
	Volume -> VIRTUAL_EXPR_42
	VIRTUAL_EXPR_39 -> VIRTUAL_EXPR_43
	Close -> p3_mf12
	VIRTUAL_EXPR_44 -> p3_mf12
	VIRTUAL_EXPR_43 -> p3_mf12
	Volume -> VIRTUAL_EXPR_44
	VIRTUAL_EXPR_45 -> p4_ms0
	VIRTUAL_EXPR_1 -> VIRTUAL_EXPR_45
	VIRTUAL_EXPR_0 -> VIRTUAL_EXPR_45
	VIRTUAL_EXPR_46 -> p4_ms1
	VIRTUAL_EXPR_45 -> VIRTUAL_EXPR_46
	VIRTUAL_EXPR_47 -> VIRTUAL_EXPR_46
	VIRTUAL_EXPR_48 -> VIRTUAL_EXPR_47
	VIRTUAL_EXPR_1 -> VIRTUAL_EXPR_48
	Close -> p4_ms2
	Close -> p4_ms3
	VIRTUAL_EXPR_49 -> p4_ms4
	VIRTUAL_EXPR_50 -> VIRTUAL_EXPR_49
	VIRTUAL_EXPR_51 -> VIRTUAL_EXPR_50
	Close -> VIRTUAL_EXPR_51
	VIRTUAL_EXPR_52 -> p4_ms5
	VIRTUAL_EXPR_50 -> VIRTUAL_EXPR_52
	Volume -> p4_ms6
	Close -> p4_ms6
	VIRTUAL_EXPR_0 -> p4_ms6
	VIRTUAL_EXPR_51 -> p5_to0
	VIRTUAL_EXPR_53 -> p5_to0
	Amount -> p5_to0
	Volume -> p5_to1
	Amount -> p5_to2
	VIRTUAL_EXPR_54 -> p5_to3
	VIRTUAL_EXPR_55 -> VIRTUAL_EXPR_54
	VIRTUAL_EXPR_56 -> VIRTUAL_EXPR_54
	VIRTUAL_EXPR_57 -> VIRTUAL_EXPR_56
	VIRTUAL_EXPR_58 -> p5_to4
	VIRTUAL_EXPR_0 -> VIRTUAL_EXPR_58
	VIRTUAL_EXPR_59 -> p5_to5
	VIRTUAL_EXPR_60 -> VIRTUAL_EXPR_59
	VIRTUAL_EXPR_40 -> VIRTUAL_EXPR_59
	VIRTUAL_EXPR_61 -> p5_to6
	VIRTUAL_EXPR_60 -> VIRTUAL_EXPR_61
	VIRTUAL_EXPR_62 -> VIRTUAL_EXPR_61
	VIRTUAL_EXPR_64 -> VIRTUAL_EXPR_62
	VIRTUAL_EXPR_63 -> VIRTUAL_EXPR_62
	VIRTUAL_EXPR_40 -> VIRTUAL_EXPR_63
	VIRTUAL_EXPR_65 -> p5_to7
	VIRTUAL_EXPR_66 -> VIRTUAL_EXPR_65
	VIRTUAL_EXPR_1 -> VIRTUAL_EXPR_65
	VIRTUAL_EXPR_68 -> p6_tn0
	VIRTUAL_EXPR_67 -> p6_tn0
	Close -> VIRTUAL_EXPR_67
	Close -> VIRTUAL_EXPR_68
	VIRTUAL_EXPR_69 -> p6_tn1
	VIRTUAL_EXPR_70 -> VIRTUAL_EXPR_69
	VIRTUAL_EXPR_71 -> VIRTUAL_EXPR_70
	Low -> p6_tn2
	High -> p6_tn2
	VIRTUAL_EXPR_72 -> p6_tn3
	VIRTUAL_EXPR_73 -> VIRTUAL_EXPR_72
	VIRTUAL_EXPR_74 -> VIRTUAL_EXPR_73
	VIRTUAL_EXPR_75 -> VIRTUAL_EXPR_74
	VIRTUAL_EXPR_40 -> VIRTUAL_EXPR_74
	VIRTUAL_EXPR_76 -> VIRTUAL_EXPR_75
	VIRTUAL_EXPR_51 -> p6_tn4
	VIRTUAL_EXPR_77 -> p6_tn5
	VIRTUAL_EXPR_78 -> VIRTUAL_EXPR_77
	Close -> VIRTUAL_EXPR_78
	VIRTUAL_EXPR_79 -> p6_tn6
	VIRTUAL_EXPR_80 -> VIRTUAL_EXPR_79
	High -> VIRTUAL_EXPR_79
	High -> VIRTUAL_EXPR_80
	VIRTUAL_EXPR_81 -> p6_tn7
	VIRTUAL_EXPR_82 -> VIRTUAL_EXPR_81
	High -> VIRTUAL_EXPR_81
	Low -> VIRTUAL_EXPR_82
	VIRTUAL_EXPR_83 -> p6_tn8
	VIRTUAL_EXPR_40 -> VIRTUAL_EXPR_83
	VIRTUAL_EXPR_84 -> p6_tn9
	VIRTUAL_EXPR_50 -> p6_tn9
	Close -> p6_tn9
	VIRTUAL_EXPR_50 -> VIRTUAL_EXPR_84
	VIRTUAL_EXPR_85 -> p6_tn10
	VIRTUAL_EXPR_50 -> p6_tn10
	Close -> p6_tn10
	VIRTUAL_EXPR_50 -> VIRTUAL_EXPR_85
	VIRTUAL_EXPR_51 -> p6_tn11
	VIRTUAL_EXPR_86 -> p6_tn12
	VIRTUAL_EXPR_87 -> p6_tn12
	Volume -> p6_tn12
	VIRTUAL_EXPR_40 -> VIRTUAL_EXPR_86
	VIRTUAL_EXPR_51 -> p6_tn13
	VIRTUAL_EXPR_88 -> p6_tn13
	Volume -> p6_tn13
}
