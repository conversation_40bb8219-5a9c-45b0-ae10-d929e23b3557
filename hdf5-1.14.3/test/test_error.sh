#! /bin/sh
#
# Copyright by The HDF Group.
# All rights reserved.
#
# This file is part of HDF5.  The full HDF5 copyright notice, including
# terms governing use, modification, and redistribution, is contained in
# the COPYING file, which can be found at the root of the source code
# distribution tree, or in https://www.hdfgroup.org/licenses.
# If you do not have access to either file, you may request a copy from
# <EMAIL>.
#
# Tests for test_error and err_compat

srcdir=.

# Check if backward compatibility options are enabled
DEPRECATED_SYMBOLS="yes"

CMP='cmp -s'
DIFF='diff -c'

# Skip plugin module to test missing filter
# Also reset the VOL connector to only use the native connector, because of the
#       error stack checking.   QAK - 2019/03/09
ENVCMD="env HDF5_PLUGIN_PRELOAD=:: HDF5_VOL_CONNECTOR=native"

nerrors=0
verbose=yes

test -d ./testfiles || mkdir ./testfiles

# Print a line-line message left justified in a field of 70 characters
# beginning with the word "Testing".
#
TESTING() {
    SPACES="                                                               "
    echo "Testing $* $SPACES" | cut -c1-70 | tr -d '\012'
}

# Run a test and print PASS or *FAIL*.  If a test fails then increment
# the `nerrors' global variable and (if $verbose is set) display the
# difference between the actual output and the expected output. The
# expected output is given as the first argument to this function and
# the actual output file is calculated by replacing the `.ddl' with
# `.out'.  The actual output is not removed if $HDF5_NOCLEANUP has a
# non-zero value.
#
TEST() {
   TEST_ERR=$1                  # The test name
   TEST_ERR_BIN=`pwd`/$TEST_ERR # The path of the test binary

   expect1="$srcdir/testfiles/$1_1"
   expect2="$srcdir/testfiles/$1_2"
   actual="./`basename $1`.out"
   actual_err="./`basename $1`.err"
   actual_ext="./`basename $1`.ext"
   shift

   # Run test.
   TESTING $TEST_ERR
   (
      # Skip the plugin for testing missing filter.
      $ENVCMD $RUNSERIAL $TEST_ERR_BIN
   ) >$actual 2>$actual_err

   # Check for core dump
   if [ $? != 0 ]; then
       nerrors=`expr $nerrors + 1`
   fi

   # Extract file name, line number, version and thread IDs because they may be different
   sed -e 's/thread [0-9]*/thread (IDs)/' -e 's/: .*\.c /: (file name) /' \
	-e 's/line [0-9]*/line (number)/' \
        -e 's/v[1-9]*\.[0-9]*\./version (number)\./' \
	-e 's/[1-9]*\.[0-9]*\.[0-9]*[^)]*/version (number)/' \
        -e 's/H5Eget_auto[1-2]*/H5Eget_auto(1 or 2)/' \
        -e 's/H5Eset_auto[1-2]*/H5Eset_auto(1 or 2)/' \
	$actual_err > $actual_ext
   cat $actual_ext >> $actual

   if $CMP $expect1 $actual; then
      echo " PASSED"
   elif $CMP $expect2 $actual; then
      echo " PASSED"
   else
      echo "*FAILED*"
      echo "    Expected result differs from actual result"
      nerrors="`expr $nerrors + 1`"
      test yes = "$verbose" && $DIFF $expect1 $actual |sed 's/^/    /'
   fi

   # Clean up output file
   if test -z "$HDF5_NOCLEANUP"; then
      rm -f $actual $actual_err $actual_ext
   fi
}

# Print a "SKIP" message
SKIP() {
    TESTING $@
    echo  " -SKIP-"
}

##############################################################################
##############################################################################
###			  T H E   T E S T S                                ###
##############################################################################
##############################################################################

# test for err_compat
if test $DEPRECATED_SYMBOLS != "yes"; then
SKIP err_compat
else
TEST err_compat
fi

# test for error_test.  Skip the plugin for testing missing filter.
TEST error_test

if test $nerrors -eq 0 ; then
   echo "All Error API tests passed."
fi

exit $nerrors
