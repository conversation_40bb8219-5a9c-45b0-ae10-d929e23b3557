# libfilter_plugin4_groups.la - a libtool library file
# Generated by libtool (GNU libtool) 2.4.6
#
# Please DO NOT delete this file!
# It is necessary for linking the library.

# The name that we can dlopen(3).
dlname='libfilter_plugin4_groups.so'

# Names of this library.
library_names='libfilter_plugin4_groups.so libfilter_plugin4_groups.so libfilter_plugin4_groups.so'

# The name of the static archive.
old_library=''

# Linker flags that cannot go in dependency_libs.
inherited_linker_flags=''

# Libraries that this one depends upon.
dependency_libs=' /usr/local/lib/libhdf5.la -lsz -lz -ldl -lm'

# Names of additional weak libraries provided by this library
weak_library_names=''

# Version information for libfilter_plugin4_groups.
current=0
age=0
revision=0

# Is this an already installed library?
installed=yes

# Should we warn about portability when linking against -modules?
shouldnotlink=yes

# Files to dlopen/dlpreopen
dlopen=''
dlpreopen=''

# Directory that this library needs to be installed in:
libdir='/nowhere'
