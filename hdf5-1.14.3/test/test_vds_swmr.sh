#! /bin/bash
#
# Copyright by The HDF Group.
# All rights reserved.
#
# This file is part of HDF5.  The full HDF5 copyright notice, including
# terms governing use, modification, and redistribution, is contained in
# the COPYING file, which can be found at the root of the source code
# distribution tree, or in https://www.hdfgroup.org/licenses.
# If you do not have access to either file, you may request a copy from
# <EMAIL>.
#
# Tests for the swmr feature using virtual datasets.

srcdir=.
utils_testdir=/home/<USER>/git/hdf5-1.14.3/utils/test
testdir=/home/<USER>/git/hdf5-1.14.3/test

###############################################################################
## test parameters
###############################################################################

Nwriters=6              # number of writers (1 per source dataset)
Nreaders=5              # number of readers to launch
nerrors=0

###############################################################################
## definitions for message file to coordinate test runs
###############################################################################
WRITER_MESSAGE=SWMR_WRITER_MESSAGE      # The message file created by writer that the open is complete
                                        # This should be the same as the define in "test/swmr_common.h"
MESSAGE_TIMEOUT=300                     # Message timeout length in secs
                                        # This should be the same as the define in "test/h5test.h"

###############################################################################
## short hands and function definitions
###############################################################################
DPRINT=:                # Set to "echo Debug:" for debugging printing,
                        # else ":" for noop.
IFDEBUG=:               # Set to null to turn on debugging, else ":" for noop.

# Print a line-line message left justified in a field of 70 characters
# beginning with the word "Testing".
#
TESTING() {
   SPACES="                                                               "
   echo "Testing $* $SPACES" | cut -c1-70 | tr -d '\012'
}

# To wait for the writer message file or till the maximum # of seconds is reached
# $1 is the message file to wait for
# This performs similar function as the routine h5_wait_message() in test/h5test.c
WAIT_MESSAGE() {
    message=$1                                  # Get the name of the message file to wait for
    t0=`date +%s`                               # Get current time in seconds
    difft=0                                     # Initialize the time difference
    mexist=0                                    # Indicate whether the message file is found
    while [ $difft -lt $MESSAGE_TIMEOUT ] ;     # Loop till message times out
    do
        t1=`date +%s`                           # Get current time in seconds
        difft=`expr $t1 - $t0`                  # Calculate the time difference
        if [ -e $message ]; then                # If message file is found:
            mexist=1                            #       indicate the message file is found
            rm $message                         #       remove the message file
            break                               #       get out of the while loop
        fi
    done;
    if test $mexist -eq 0; then
        # Issue warning that the writer message file is not found, continue with launching the reader(s)
        echo warning: $WRITER_MESSAGE is not found after waiting $MESSAGE_TIMEOUT seconds
    else
        echo $WRITER_MESSAGE is found
    fi
}

###############################################################################
## Main
###############################################################################

# Check to see if the VFD specified by the HDF5_DRIVER environment variable
# supports SWMR.
$utils_testdir/swmr_check_compat_vfd
rc=$?
if [ $rc -ne 0 ] ; then
    echo
    echo "The VFD specified by the HDF5_DRIVER environment variable"
    echo "does not support SWMR."
    echo
    echo "SWMR acceptance tests skipped"
    echo
    exit 0
fi

# Parse options (none accepted at this time)
while [ $# -gt 0 ]; do
    case "$1" in
    *)  # unknown option
        echo "$0: Unknown option ($1)"
        exit 1
        ;;
    esac
done

# HDF5 has several tests that create and delete signal files to communicate
# between processes, and it seems that even though the names of the files are
# different, occasionally the wrong file is deleted, interrupting the flow of
# the test.  Running each of these tests in its own directory should eliminate
# the problem.
mkdir vds_swmr_test
for FILE in vds_swmr*; do
    case "$FILE" in
        *.o) continue ;;    ## don't copy the .o files
    esac
    if test -f "$FILE" ; then
        cp $FILE vds_swmr_test
    fi
done

# With the --disable-shared option, swmr program files are built in the test
# directory, otherwise they are in test/.libs with a corresponding wrapper
# script in the test directory.  The programs or wrapper scripts in test should
# always be copied, swmr files in .libs should be copied only if they exists.
if [ -f .libs/vds_swmr_writer ]; then
    mkdir vds_swmr_test/.libs
    for FILE in .libs/vds_swmr*; do
        case "$FILE" in
            *.o) continue ;;    ## don't copy the .o files
        esac
        if test -f "$FILE" ; then
            cp $FILE vds_swmr_test/.libs
        fi
    done
fi

cd vds_swmr_test


echo
echo "###############################################################################"
echo "## Basic VDS SWMR test - writing to a tiled plane"
echo "###############################################################################"

# Launch the file generator
echo launch the generator
$testdir/vds_swmr_gen
if test $? -ne 0; then
    echo generator had error
    nerrors=`expr $nerrors + 1`
fi

# Check for error and exit if one occurred
$DPRINT nerrors=$nerrors
if test $nerrors -ne 0 ; then
    echo "VDS SWMR tests failed with $nerrors errors."
    exit 1
fi

# Launch the writers
echo "launch the $Nwriters SWMR VDS writers (1 per source)"
pid_writers=""
n=0
while [ $n -lt $Nwriters ]; do
    $testdir/vds_swmr_writer $n &
    pid_writers="$pid_writers $!"
    n=`expr $n + 1`
done
$DPRINT pid_writers=$pid_writers
$IFDEBUG ps

# Sleep to ensure that the writers have started
sleep 3

# Launch the readers
echo launch $Nreaders SWMR readers
pid_readers=""
n=0
while [ $n -lt $Nreaders ]; do
    $testdir/vds_swmr_reader &
    pid_readers="$pid_readers $!"
    n=`expr $n + 1`
done
$DPRINT pid_readers=$pid_readers
$IFDEBUG ps

# Collect exit code of the writers
for xpid in $pid_writers; do
    $DPRINT checked writer $xpid
    wait $xpid
    if test $? -ne 0; then
        echo writer had error
        nerrors=`expr $nerrors + 1`
    fi
done

# Collect exit code of the readers
# (they usually finish after the writers)
for xpid in $pid_readers; do
    $DPRINT checked reader $xpid
    wait $xpid
    if test $? -ne 0; then
        echo reader had error
        nerrors=`expr $nerrors + 1`
    fi
done

# Check for error and exit if one occurred
$DPRINT nerrors=$nerrors
if test $nerrors -ne 0 ; then
    echo "VDS SWMR tests failed with $nerrors errors."
    exit 1
fi

###############################################################################
## Report and exit
###############################################################################
cd ..
$DPRINT nerrors=$nerrors
if test $nerrors -eq 0 ; then
    echo "VDS SWMR tests passed."
    if test -z "$HDF5_NOCLEANUP"; then
        # delete the test directory
        rm -rf vds_swmr_test
    fi
    exit 0
else
    echo "VDS SWMR tests failed with $nerrors errors."
    exit 1
fi

