/* * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * *
 * Copyright by The HDF Group.                                               *
 * All rights reserved.                                                      *
 *                                                                           *
 * This file is part of HDF5.  The full HDF5 copyright notice, including     *
 * terms governing use, modification, and redistribution, is contained in    *
 * the COPYING file, which can be found at the root of the source code       *
 * distribution tree, or in https://www.hdfgroup.org/licenses.               *
 * If you do not have access to either file, you may request a copy from     *
 * <EMAIL>.                                                        *
 * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * */

#ifndef H5_API_TEST_H
#define H5_API_TEST_H

#include <hdf5.h>
#include <H5private.h>

#include "h5test.h"

#include "H5_api_test_config.h"
#include "H5_api_test_util.h"

/* Define H5VL_VERSION if not already defined */
#ifndef H5VL_VERSION
#define H5VL_VERSION 0
#endif

/* Define macro to wait forever depending on version */
#if H5VL_VERSION >= 2
#define H5_API_TEST_WAIT_FOREVER H5ES_WAIT_FOREVER
#else
#define H5_API_TEST_WAIT_FOREVER UINT64_MAX
#endif

/******************************************************************************/

/* The name of the file that all of the tests will operate on */
#define TEST_FILE_NAME "H5_api_test.h5"
extern char H5_api_test_filename[];

extern const char *test_path_prefix;

/*
 * Environment variable specifying a prefix string to add to
 * filenames generated by the API tests
 */
#define HDF5_API_TEST_PATH_PREFIX "HDF5_API_TEST_PATH_PREFIX"

/* The names of a set of container groups which hold objects
 * created by each of the different types of tests.
 */
#define GROUP_TEST_GROUP_NAME         "group_tests"
#define ATTRIBUTE_TEST_GROUP_NAME     "attribute_tests"
#define DATASET_TEST_GROUP_NAME       "dataset_tests"
#define DATATYPE_TEST_GROUP_NAME      "datatype_tests"
#define LINK_TEST_GROUP_NAME          "link_tests"
#define OBJECT_TEST_GROUP_NAME        "object_tests"
#define MISCELLANEOUS_TEST_GROUP_NAME "miscellaneous_tests"

#define ARRAY_LENGTH(array) sizeof(array) / sizeof(array[0])

#define UNUSED(o) (void)(o);

#define H5_API_TEST_FILENAME_MAX_LENGTH 1024

/* The maximum size of a dimension in an HDF5 dataspace as allowed
 * for this testing suite so as not to try to create too large
 * of a dataspace/datatype. */
#define MAX_DIM_SIZE 16

#endif
