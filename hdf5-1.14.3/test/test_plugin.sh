#!/usr/bin/env bash
#
# Copyright by The HDF Group.
# All rights reserved.
#
# This file is part of HDF5.  The full HDF5 copyright notice, including
# terms governing use, modification, and redistribution, is contained in
# the COPYING file, which can be found at the root of the source code
# distribution tree, or in https://www.hdfgroup.org/licenses.
# If you do not have access to either file, you may request a copy from
# <EMAIL>.
#
# This shell script is for testing filter, VFD, and VOL plugins.
#
srcdir=.
TOP_BUILDDIR=..

EXIT_SUCCESS=0
EXIT_FAILURE=1

CP="cp -p"    # Use -p to preserve mode,ownership, timestamps
RM="rm -rf"

nerrors=0
verbose=yes
exit_code=$EXIT_SUCCESS

# Test binary names
FILTER_TEST_NAME=filter_plugin
FILTER_TEST_BIN=`pwd`/$FILTER_TEST_NAME

VFD_TEST_NAME=vfd_plugin
VFD_TEST_BIN=`pwd`/$VFD_TEST_NAME

VOL_TEST_NAME=vol_plugin
VOL_TEST_BIN=`pwd`/$VOL_TEST_NAME

# Paths to actual plugins ("libraries" in test directory are just stubs)
FROM_DIR=`pwd`/.libs
case $(uname) in
    CYGWIN* )
        NULL_VFD_PLUGIN="$FROM_DIR/cygnull_vfd_plugin*"
        NULL_VOL_PLUGIN="$FROM_DIR/cygnull_vol_connector*"
        PLUGINS_FOR_DIR1="$FROM_DIR/cygfilter_plugin1* $FROM_DIR/cygfilter_plugin3*"
        PLUGINS_FOR_DIR2="$FROM_DIR/cygfilter_plugin2* $FROM_DIR/cygfilter_plugin4*"
        ;;
    *)
        NULL_VFD_PLUGIN="$FROM_DIR/libnull_vfd_plugin*"
        NULL_VOL_PLUGIN="$FROM_DIR/libnull_vol_connector*"
        PLUGINS_FOR_DIR1="$FROM_DIR/libfilter_plugin1* $FROM_DIR/libfilter_plugin3*"
        PLUGINS_FOR_DIR2="$FROM_DIR/libfilter_plugin2* $FROM_DIR/libfilter_plugin4*"
        ;;
esac

# Directories where we'll copy plugins
TEMP_PLUGIN_DIR=temp_plugins
TEMP_FILTER_DIR1=temp_filter_plugin_dir1
TEMP_FILTER_DIR2=temp_filter_plugin_dir2

# Function to print a line-line message left justified in a field of
# 70 characters beginning with the word "Testing".
#
TESTING() {
    SPACES="                                                               "
    echo "Testing $* $SPACES" | cut -c1-70 | tr -d '\012'
}

#############
# Main Body #
#############

# Create plugin directories
test -d $TEMP_PLUGIN_DIR || mkdir -p $TEMP_PLUGIN_DIR
if [ $? != 0 ]; then
    echo "Failed to create plugin test directory ($TEMP_PLUGIN_DIR)"
    exit $EXIT_FAILURE
fi
test -d $TEMP_FILTER_DIR1 || mkdir -p $TEMP_FILTER_DIR1
if [ $? != 0 ]; then
    echo "Failed to create filter plugin test directory ($TEMP_FILTER_DIR1)"
    exit $EXIT_FAILURE
fi
test -d $TEMP_FILTER_DIR2 || mkdir -p $TEMP_FILTER_DIR2
if [ $? != 0 ]; then
    echo "Failed to create filter plugin test directory ($TEMP_FILTER_DIR2)"
    exit $EXIT_FAILURE
fi

# Copy plugins for the tests
$CP $NULL_VFD_PLUGIN $TEMP_PLUGIN_DIR
if [ $? != 0 ]; then
    echo "Failed to copy NULL VFD plugin ($NULL_VFD_PLUGIN) to test directory."
    exit $EXIT_FAILURE
fi
$CP $NULL_VOL_PLUGIN $TEMP_PLUGIN_DIR
if [ $? != 0 ]; then
    echo "Failed to copy NULL VOL plugin ($NULL_VOL_PLUGIN) to test directory."
    exit $EXIT_FAILURE
fi
$CP $PLUGINS_FOR_DIR1 $TEMP_FILTER_DIR1
if [ $? != 0 ]; then
    echo "Failed to copy filter plugins ($PLUGINS_FOR_DIR1) to test directory."
    exit $EXIT_FAILURE
fi
$CP $PLUGINS_FOR_DIR2 $TEMP_FILTER_DIR2
if [ $? != 0 ]; then
    echo "Failed to copy filter plugins ($PLUGINS_FOR_DIR2) to test directory."
    exit $EXIT_FAILURE
fi

# Set plugin path
ENVCMD="env HDF5_PLUGIN_PATH=${TEMP_PLUGIN_DIR}:${TEMP_FILTER_DIR1}:${TEMP_FILTER_DIR2}:${HDF5_PLUGIN_PATH}"

# Run the tests
$ENVCMD $FILTER_TEST_BIN
if [ $? != 0 ]; then
    nerrors=`expr $nerrors + 1`
fi
$ENVCMD $VFD_TEST_BIN
if [ $? != 0 ]; then
    nerrors=`expr $nerrors + 1`
fi
$ENVCMD $VOL_TEST_BIN
if [ $? != 0 ]; then
    nerrors=`expr $nerrors + 1`
fi

# Print results
if test $nerrors -ne 0 ; then
    echo "$nerrors errors encountered"
    exit_code=$EXIT_FAILURE
else
    echo "All plugin tests passed."
    exit_code=$EXIT_SUCCESS
fi

# Clean up temporary files/directories and leave
$RM $TEMP_PLUGIN_DIR $TEMP_FILTER_DIR1 $TEMP_FILTER_DIR2

exit $exit_code
