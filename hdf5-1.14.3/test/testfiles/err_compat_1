Testing error API H5Eset/get_auto                                     
Testing error API based on data I/O                                   
All error API tests passed.
   This program tests the Error API compatible with HDF5 version (number).  There are supposed to be some error messages
********* Print error stack in HDF5 default way *********
HDF5-DIAG: Error detected in HDF5 (version (number)) thread (IDs):
  #000: (file name) line (number) in main(): fake error message 1
    major: Error API
    minor: Bad value

********* Print error stack in customized way *********
    error #000: (file name) in main(): line (number)
        major: Error API
        minor: Bad value
HDF5-DIAG: Error detected in HDF5 (version (number)) thread (IDs):
  #000: (file name) line (number) in H5Dcreate2(): unable to synchronously create dataset
    major: Dataset
    minor: Unable to create file
  #001: (file name) line (number) in H5D__create_api_common(): can't set object access arguments
    major: Dataset
    minor: Can't set value
  #002: (file name) line (number) in H5VL_setup_acc_args(): invalid location identifier
    major: Invalid arguments to routine
    minor: Inappropriate type
  #003: (file name) line (number) in H5VL_vol_object(): invalid identifier type to function
    major: Invalid arguments to routine
    minor: Inappropriate type

********* Print error stack in customized way *********
    error #000: (file name) in H5VL_vol_object(): line (number)
        major: Invalid arguments to routine
        minor: Inappropriate type
    error #001: (file name) in H5VL_setup_acc_args(): line (number)
        major: Invalid arguments to routine
        minor: Inappropriate type
    error #002: (file name) in H5D__create_api_common(): line (number)
        major: Dataset
        minor: Can't set value
    error #003: (file name) in H5Dcreate2(): line (number)
        major: Dataset
        minor: Unable to create file

********* Print error stack in customized way *********
    error #000: (file name) in H5VL_vol_object(): line (number)
        major: Invalid arguments to routine
        minor: Inappropriate type
    error #001: (file name) in H5VL_setup_acc_args(): line (number)
        major: Invalid arguments to routine
        minor: Inappropriate type
    error #002: (file name) in H5D__create_api_common(): line (number)
        major: Dataset
        minor: Can't set value
    error #003: (file name) in H5Dcreate2(): line (number)
        major: Dataset
        minor: Unable to create file
    error #004: (file name) in H5Eget_auto(1 or 2)(): line (number)
        major: Error API
        minor: Can't get value

********* Print error stack in customized way *********
    error #000: (file name) in H5VL_vol_object(): line (number)
        major: Invalid arguments to routine
        minor: Inappropriate type
    error #001: (file name) in H5VL_setup_acc_args(): line (number)
        major: Invalid arguments to routine
        minor: Inappropriate type
    error #002: (file name) in H5D__create_api_common(): line (number)
        major: Dataset
        minor: Can't set value
    error #003: (file name) in H5Dcreate2(): line (number)
        major: Dataset
        minor: Unable to create file
HDF5-DIAG: Error detected in HDF5 (version (number)) thread (IDs):
  #000: (file name) line (number) in H5Dcreate2(): unable to synchronously create dataset
    major: Dataset
    minor: Unable to create file
  #001: (file name) line (number) in H5D__create_api_common(): can't set object access arguments
    major: Dataset
    minor: Can't set value
  #002: (file name) line (number) in H5VL_setup_acc_args(): invalid location identifier
    major: Invalid arguments to routine
    minor: Inappropriate type
  #003: (file name) line (number) in H5VL_vol_object(): invalid identifier type to function
    major: Invalid arguments to routine
    minor: Inappropriate type
HDF5-DIAG: Error detected in HDF5 (version (number)) thread (IDs):
  #000: (file name) line (number) in main(): fake error message 2
    major: Error API
    minor: Unrecognized message
  #001: (file name) line (number) in test_h5epush1(): expected H5Dwrite error
    major: Error API
    minor: Write failed
  #002: (file name) line (number) in H5Dwrite(): can't synchronously write data
    major: Dataset
    minor: Write failed
  #003: (file name) line (number) in H5D__write_api_common(): dset_id is not a dataset ID
    major: Invalid arguments to routine
    minor: Inappropriate type
