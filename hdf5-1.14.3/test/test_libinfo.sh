#! /bin/sh
#
# Copyright by The HDF Group.
# All rights reserved.
#
# This file is part of HDF5.  The full HDF5 copyright notice, including
# terms governing use, modification, and redistribution, is contained in
# the COPYING file, which can be found at the root of the source code
# distribution tree, or in https://www.hdfgroup.org/licenses.
# If you do not have access to either file, you may request a copy from
# <EMAIL>.


#
# Tests for the embedded library information feature.
# Part 1:
# Verify the HDF5 library does contains an exact copy of the content of the
# libhdf5.settings file.
# Part 2:
# If executable is linked with the static hdf5 library (how to determine?),
# verify an executable indeed contains an exact copy of the content of the
# libhdf5.settings file.
#

srcdir=.

# Determine the configure options of the hdf5 library and executables.

Shared_Lib=yes
Static_Lib=yes
Static_exec=no


# Print a line-line message left justified in a field of 70 characters.
#
LINEMSG() {
   SPACES="                                                               "
   echo "Check file $* $SPACES" | cut -c1-70 | tr -d '\012'
}


# Print a "SKIP" message
SKIP() {
    LINEMSG $*
    echo  " -SKIP-"
}

# Function definitions
CHECK_LIBINFO(){
    LINEMSG $1
    # Some systems, like Mac, the strings command inspects library files. Older
    # versions of strings may not know newer library format, resulting in
    # command errors. Make it read the file as stdin to avoid the problem.
    if strings < $1 | grep "SUMMARY OF THE HDF5 CONFIGURATION" > /dev/null; then
	echo " PASSED"
    else
	echo " FAILED"
	nerrors=`expr $nerrors + 1`
    fi
}


# MAIN Body
nerrors=0
H5_HAVE_EMBEDDED_LIBINFO=`grep '#define H5_HAVE_EMBEDDED_LIBINFO ' ../src/H5pubconf.h`

# Skip the rest if embedded-libinfo is not enabled.
if [ -z "$H5_HAVE_EMBEDDED_LIBINFO" ]; then
    echo "embedded-libinfo is not enabled. Test skipped."
    exit 0
fi

# The location of HDF library file(s) depends on whether shared lib is
# built too.
if [ -n $Shared_Lib ]; then
   h5libdir=../src/.libs
   shlib=$(grep dlname ../src/libhdf5.la | sed -e "s/dlname='//" -e "s/'//")
else
   h5libdir=../src
fi

h5libsettings=../src/libhdf5.settings

# Part 1:
# Verify the HDF5 library does contains an exact copy of the content of the
# libhdf5.settings file.
# Check dynamic library file if built.
if [ x-$Shared_Lib = x-yes ]; then
    CHECK_LIBINFO ${h5libdir}/${shlib}
else
    SKIP shlib
fi

# Though rare, libhdf5.a may not have been built.
if [ x-$Static_Lib = x-yes ]; then
    CHECK_LIBINFO ${h5libdir}/libhdf5.a
else
    SKIP ${h5libdir}/libhdf5.a
fi

# Check if executables has the lib information only if shared lib is not
# built or static-exec is used.  (Don't care static-exec since it affects
# tools binary only.)
if [ x-$Shared_Lib != x-yes ]; then
    CHECK_LIBINFO testhdf5
else
    SKIP testhdf5
fi


if [ $nerrors -gt 0 ]; then
    echo "***$nerrors errors encountered***"
    exit 1
else
    echo "No error encountered"
    exit 0
fi
