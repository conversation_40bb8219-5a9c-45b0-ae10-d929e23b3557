/* * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * *
 * Copyright by The HDF Group.                                               *
 * All rights reserved.                                                      *
 *                                                                           *
 * This file is part of HDF5.  The full HDF5 copyright notice, including     *
 * terms governing use, modification, and redistribution, is contained in    *
 * the COPYING file, which can be found at the root of the source code       *
 * distribution tree, or in https://www.hdfgroup.org/licenses.               *
 * If you do not have access to either file, you may request a copy from     *
 * <EMAIL>.                                                        *
 * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * */

/* Purpose:     A simple virtual object layer (VOL) connector with almost no
 *              functionality that is used for testing basic plugin handling
 *              (registration, etc.).
 */

#ifndef NULL_VOL_CONNECTOR_H
#define NULL_VOL_CONNECTOR_H

#define NULL_VOL_CONNECTOR_VALUE ((H5VL_class_value_t)160)
#define NULL_VOL_CONNECTOR_NAME  "null_vol_connector"

#endif /* NULL_VOL_CONNECTOR_H */
