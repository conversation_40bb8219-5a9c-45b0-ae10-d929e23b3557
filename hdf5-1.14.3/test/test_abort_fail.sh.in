#! /bin/sh
#
# Copyright by The HDF Group.
# All rights reserved.
#
# This file is part of HDF5.  The full HDF5 copyright notice, including
# terms governing use, modification, and redistribution, is contained in
# the COPYING file, which can be found at the root of the source code
# distribution tree, or in https://www.hdfgroup.org/licenses.
# If you do not have access to either file, you may request a copy from
# <EMAIL>.
#
# Test to verify that the assertion/abort failure is fixed when the application
# does not close the file. (See HDFFV-10160)
#
# Test to verify that the infinite loop closing library/abort failure is fixed
# when the application creates and removes dense attributes (See HDFFV-10659)

srcdir=@srcdir@

nerrors=0

##############################################################################
##############################################################################
###			  T H E   T E S T S                                            ###
##############################################################################
##############################################################################
#
#
echo "Testing file not closed assertion/abort failure"
TEST_NAME=filenotclosed     # The test name
TEST_BIN=`pwd`/$TEST_NAME   # The path of the test binary
#
# Run the test
$RUNSERIAL $TEST_BIN >/dev/null 2>&1
exitcode=$?
if [ $exitcode -eq 0 ]; then
    echo "Test PASSED"
else
    echo "Test FAILED"
 	nerrors="`expr $nerrors + 1`"
fi
#
#
echo "Testing infinite loop closing library/abort failure"
TEST_NAME=del_many_dense_attrs  # The test name
TEST_BIN=`pwd`/$TEST_NAME 	    # The path of the test binary
# Run the test
$RUNSERIAL $TEST_BIN >/dev/null 2>&1
exitcode=$?
if [ $exitcode -eq 0 ]; then
    echo "Test PASSED"
else
    echo "Test FAILED"
 	nerrors="`expr $nerrors + 1`"
fi
#
#
if test $nerrors -eq 0 ; then
    echo "All tests for abort failure passed."
    exit 0
else
    echo "Tests for abort failure failed with $nerrors errors."
    exit 1
fi
