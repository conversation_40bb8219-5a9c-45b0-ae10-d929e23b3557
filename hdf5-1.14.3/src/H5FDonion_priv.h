/* * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * *
 * Copyright by The HDF Group.                                               *
 * All rights reserved.                                                      *
 *                                                                           *
 * This file is part of HDF5.  The full HDF5 copyright notice, including     *
 * terms governing use, modification, and redistribution, is contained in    *
 * the COPYING file, which can be found at the root of the source code       *
 * distribution tree, or in https://www.hdfgroup.org/licenses.               *
 * If you do not have access to either file, you may request a copy from     *
 * <EMAIL>.                                                        *
 * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * */

/*
 * Onion Virtual File Driver (VFD) Internals.
 *
 * Purpose:    The private header file for the Onion VFD.
 *             Contains definitions and declarations used internallay and by
 *             tests.
 */

#ifndef H5FDonion_priv_H
#define H5FDonion_priv_H

#include "H5FDonion_header.h"
#include "H5FDonion_history.h"
#include "H5FDonion_index.h"

#endif /* H5FDonion_priv_H */
