# libhdf5_hl_cpp.la - a libtool library file
# Generated by libtool (GNU libtool) 2.4.6
#
# Please DO NOT delete this file!
# It is necessary for linking the library.

# The name that we can dlopen(3).
dlname='libhdf5_hl_cpp.so.310'

# Names of this library.
library_names='libhdf5_hl_cpp.so.310.0.3 libhdf5_hl_cpp.so.310 libhdf5_hl_cpp.so'

# The name of the static archive.
old_library='libhdf5_hl_cpp.a'

# Linker flags that cannot go in dependency_libs.
inherited_linker_flags=''

# Libraries that this one depends upon.
dependency_libs=' /usr/local/lib/libhdf5_hl.la /usr/local/lib/libhdf5_cpp.la /usr/local/lib/libhdf5.la -lsz -lz -ldl'

# Names of additional weak libraries provided by this library
weak_library_names=''

# Version information for libhdf5_hl_cpp.
current=310
age=0
revision=3

# Is this an already installed library?
installed=yes

# Should we warn about portability when linking against -modules?
shouldnotlink=no

# Files to dlopen/dlpreopen
dlopen=''
dlpreopen=''

# Directory that this library needs to be installed in:
libdir='/usr/local/lib'
