[INFO] [2025-05-28 15:23:49.146] [bybit_md_proxy.cc:48] [init] [bybit_md_proxy_5] Initializing bybit_md_proxy with log file: ../log/md//bybit_md_proxy_5.log
[INFO] [2025-05-28 15:23:49.147] [bybit_md_proxy.cc:66] [init] [bybit_md_proxy_5] bybit_md_proxy init
[INFO] [2025-05-28 15:23:49.262] [bybit_md_proxy.cc:108] [do_ws_connect] [bybit_md_proxy_5] bybit_md_proxy do_ws_connect
[INFO] [2025-05-28 15:23:49.262] [bybit_md_proxy.cc:97] [connect] [bybit_md_proxy_5] bybit_md_proxy connect
[INFO] [2025-05-28 15:23:49.421] [bybit_md_proxy.cc:171] [subscribe] [bybit_md_proxy_5] bybit_md_proxy subscribe: received 52 codes, new codes: 52, total codes: 52 force:false
[INFO] [2025-05-28 15:23:50.397] [bybit_md_proxy.cc:303] [on_connect] [bybit_md_proxy_5] bybit_md_proxy reconnected, subscribing all codes
[INFO] [2025-05-28 15:23:50.397] [bybit_md_proxy.cc:171] [subscribe] [bybit_md_proxy_5] bybit_md_proxy subscribe: received 52 codes, new codes: 0, total codes: 52 force:false
[INFO] [2025-05-28 15:23:50.397] [bybit_md_proxy.cc:178] [subscribe] [bybit_md_proxy_5] bybit_md_proxy: full subscription with 52 codes
[INFO] [2025-05-28 15:23:50.398] [bybit_md_proxy.cc:206] [do_sub] [bybit_md_proxy_5] bybit_md_proxy do_sub: {"args":["kline.1.TOKENUSDT","kline.1.GMTUSDT","kline.1.1000BTTUSDT","kline.1.OGUSDT","kline.1.1000LUNCUSDT","kline.1.SCAUSDT","kline.1.HBARUSDT","kline.1.WAVESUSDT","kline.1.BEAMUSDT","kline.1.ALUUSDT","kline.1.ATAUSDT","kline.1.KAIAUSDT","kline.1.CROUSDT","kline.1.LUMIAUSDT","kline.1.MEMEUSDT","kline.1.POPCATUSDT","kline.1.MAVUSDT","kline.1.IDEXUSDT","kline.1.SIGNUSDT","kline.1.SERAPHUSDT","kline.1.CETUSUSDT","kline.1.SCRTUSDT","kline.1.SAFEUSDT","kline.1.RLCUSDT","kline.1.SENDUSDT","kline.1.JSTUSDT","kline.1.AKTUSDT","kline.1.ELXUSDT","kline.1.BADGERUSDT","kline.1.UMAUSDT","kline.1.BUSDT","kline.1.APTUSDT","kline.1.FLOCKUSDT","kline.1.SNTUSDT","kline.1.BRUSDT","kline.1.ORBSUSDT","kline.1.QUICKUSDT","kline.1.ETHBTCUSDT","kline.1.B3USDT","kline.1.API3USDT","kline.1.BLURUSDT","kline.1.OPUSDT","kline.1.CYBERUSDT","kline.1.AERGOUSDT","kline.1.ONGUSDT","kline.1.ORDERUSDT","kline.1.PRIMEUSDT","kline.1.TAIKOUSDT","kline.1.BIOUSDT","kline.1.IOTXUSDT","kline.1.COREUSDT","kline.1.FIDAUSDT"],"op":"subscribe","req_id":"0"}
[INFO] [2025-05-28 15:23:50.398] [bybit_md_proxy.cc:310] [on_sent] [bybit_md_proxy_5] bybit_md_proxy send msg ec: 0 ec_msg: Success
