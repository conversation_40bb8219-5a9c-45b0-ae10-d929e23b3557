[INFO] [2025-05-28 15:23:49.144] [bybit_md_proxy.cc:48] [init] [bybit_md_proxy_1] Initializing bybit_md_proxy with log file: ../log/md//bybit_md_proxy_1.log
[INFO] [2025-05-28 15:23:49.144] [bybit_md_proxy.cc:66] [init] [bybit_md_proxy_1] bybit_md_proxy init
[INFO] [2025-05-28 15:23:49.262] [bybit_md_proxy.cc:108] [do_ws_connect] [bybit_md_proxy_1] bybit_md_proxy do_ws_connect
[INFO] [2025-05-28 15:23:49.262] [bybit_md_proxy.cc:97] [connect] [bybit_md_proxy_1] bybit_md_proxy connect
[INFO] [2025-05-28 15:23:49.420] [bybit_md_proxy.cc:171] [subscribe] [bybit_md_proxy_1] bybit_md_proxy subscribe: received 53 codes, new codes: 53, total codes: 53 force:false
[INFO] [2025-05-28 15:23:50.431] [bybit_md_proxy.cc:303] [on_connect] [bybit_md_proxy_1] bybit_md_proxy reconnected, subscribing all codes
[INFO] [2025-05-28 15:23:50.431] [bybit_md_proxy.cc:171] [subscribe] [bybit_md_proxy_1] bybit_md_proxy subscribe: received 53 codes, new codes: 0, total codes: 53 force:false
[INFO] [2025-05-28 15:23:50.431] [bybit_md_proxy.cc:178] [subscribe] [bybit_md_proxy_1] bybit_md_proxy: full subscription with 53 codes
[INFO] [2025-05-28 15:23:50.432] [bybit_md_proxy.cc:206] [do_sub] [bybit_md_proxy_1] bybit_md_proxy do_sub: {"args":["kline.1.APEUSDT","kline.1.FTNUSDT","kline.1.ANKRUSDT","kline.1.PARTIUSDT","kline.1.BANANAS31USDT","kline.1.POLYXUSDT","kline.1.ONEUSDT","kline.1.FORMUSDT","kline.1.PHBUSDT","kline.1.DOODUSDT","kline.1.DBRUSDT","kline.1.MYRIAUSDT","kline.1.PEOPLEUSDT","kline.1.IOSTUSDT","kline.1.VANRYUSDT","kline.1.PENGUUSDT","kline.1.MEUSDT","kline.1.FORTHUSDT","kline.1.TONUSDT","kline.1.XRPUSDT","kline.1.CELOUSDT","kline.1.XVSUSDT","kline.1.10000COQUSDT","kline.1.SONICUSDT","kline.1.OGNUSDT","kline.1.1000TURBOUSDT","kline.1.PIXELUSDT","kline.1.HIPPOUSDT","kline.1.ZRXUSDT","kline.1.MDTUSDT","kline.1.BROCCOLIUSDT","kline.1.REQUSDT","kline.1.JUSDT","kline.1.CHILLGUYUSDT","kline.1.MICHIUSDT","kline.1.SANDUSDT","kline.1.ROSEUSDT","kline.1.DARKUSDT","kline.1.KAITOUSDT","kline.1.RUNEUSDT","kline.1.SPXUSDT","kline.1.HIGHUSDT","kline.1.ZETAUSDT","kline.1.THETAUSDT","kline.1.TUSDT","kline.1.VRUSDT","kline.1.AGLDUSDT","kline.1.XMRUSDT","kline.1.OBOLUSDT","kline.1.POLUSDT","kline.1.ZKJUSDT","kline.1.ZRCUSDT","kline.1.ALICEUSDT"],"op":"subscribe","req_id":"0"}
[INFO] [2025-05-28 15:23:50.433] [bybit_md_proxy.cc:310] [on_sent] [bybit_md_proxy_1] bybit_md_proxy send msg ec: 0 ec_msg: Success
