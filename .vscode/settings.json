{
  "files.associations": {
    "filesystem": "cpp",
    "iostream": "cpp",
    "string_view": "cpp",
    "array": "cpp",
    "string": "cpp",
    "ranges": "cpp",
    "*.ipp": "cpp",
    "random": "cpp",
    "bitset": "cpp",
    "any": "cpp",
    "atomic": "cpp",
    "strstream": "cpp",
    "bit": "cpp",
    "*.tcc": "cpp",
    "cctype": "cpp",
    "cfenv": "cpp",
    "charconv": "cpp",
    "chrono": "cpp",
    "cinttypes": "cpp",
    "clocale": "cpp",
    "cmath": "cpp",
    "codecvt": "cpp",
    "compare": "cpp",
    "complex": "cpp",
    "concepts": "cpp",
    "condition_variable": "cpp",
    "coroutine": "cpp",
    "csignal": "cpp",
    "cstdarg": "cpp",
    "cstddef": "cpp",
    "cstdint": "cpp",
    "cstdio": "cpp",
    "cstdlib": "cpp",
    "cstring": "cpp",
    "ctime": "cpp",
    "cwchar": "cpp",
    "cwctype": "cpp",
    "deque": "cpp",
    "forward_list": "cpp",
    "list": "cpp",
    "map": "cpp",
    "set": "cpp",
    "unordered_map": "cpp",
    "unordered_set": "cpp",
    "vector": "cpp",
    "exception": "cpp",
    "algorithm": "cpp",
    "functional": "cpp",
    "iterator": "cpp",
    "memory": "cpp",
    "memory_resource": "cpp",
    "numeric": "cpp",
    "optional": "cpp",
    "ratio": "cpp",
    "regex": "cpp",
    "source_location": "cpp",
    "system_error": "cpp",
    "tuple": "cpp",
    "type_traits": "cpp",
    "utility": "cpp",
    "fstream": "cpp",
    "future": "cpp",
    "initializer_list": "cpp",
    "iomanip": "cpp",
    "iosfwd": "cpp",
    "istream": "cpp",
    "limits": "cpp",
    "mutex": "cpp",
    "new": "cpp",
    "numbers": "cpp",
    "ostream": "cpp",
    "semaphore": "cpp",
    "shared_mutex": "cpp",
    "sstream": "cpp",
    "stdexcept": "cpp",
    "stop_token": "cpp",
    "streambuf": "cpp",
    "thread": "cpp",
    "typeindex": "cpp",
    "typeinfo": "cpp",
    "valarray": "cpp",
    "variant": "cpp"
  },
  // "clangd.arguments": [
  //   "--all-scopes-completion", // 全局补全(补全建议会给出在当前作用域不可见的索引,插入后自动补充作用域标识符),例如在main()中直接写cout,即使没有`#include <iostream>`,也会给出`std::cout`的建议,配合"--header-insertion=iwyu",还可自动插入缺失的头文件
  //   "--background-index", // 后台分析并保存索引文件
  //   "--clang-tidy", // 启用 Clang-Tidy 以提供「静态检查」，下面设置 clang tidy 规则
  //   "--clang-tidy-checks=performance-*, bugprone-*, misc-*, google-*, modernize-*, readability-*, portability-*",
  //   // "--compile-commands-dir=${workspaceFolder}/.vscode", // 编译数据库(例如 compile_commands.json 文件)的目录位置
  //   "--completion-parse=auto", // 当 clangd 准备就绪时，用它来分析建议
  //   "--completion-style=detailed", // 建议风格：打包(重载函数只会给出一个建议);还可以设置为 detailed
  //   "--query-driver=/opt/rh/gcc-toolset-11/root/usr/bin/gcc", // MacOS 上需要设定 clang 编译器的路径，homebrew 安装的clang 是 /usr/local/opt/llvm/bin/clang++
  //   // 启用配置文件(YAML格式)项目配置文件是在项目文件夹里的“.clangd”,用户配置文件是“clangd/config.yaml”,该文件来自:Windows: %USERPROFILE%\AppData\Local || MacOS: ~/Library/Preferences/ || Others: $XDG_CONFIG_HOME, usually ~/.config
  //   "--enable-config",
  //   // "--fallback-style=Webkit", // 默认格式化风格: 在没找到 .clang-format 文件时采用,可用的有 LLVM, Google, Chromium, Mozilla, Webkit, Microsoft, GNU
  //   "--function-arg-placeholders=true", // 补全函数时，将会给参数提供占位符，键入后按 Tab 可以切换到下一占位符，乃至函数末
  //   "--header-insertion-decorators", // 输入建议中，已包含头文件的项与还未包含头文件的项会以圆点加以区分
  //   "--header-insertion=iwyu", // 插入建议时自动引入头文件 iwyu
  //   "--include-cleaner-stdlib", // 为标准库头文件启用清理功能(不成熟!!!)
  //   "--log=verbose", // 让 Clangd 生成更详细的日志
  //   "--pch-storage=memory", // pch 优化的位置(Memory 或 Disk,前者会增加内存开销，但会提升性能)
  //   "--pretty", // 输出的 JSON 文件更美观
  //   "--ranking-model=decision_forest", // 建议的排序方案：hueristics (启发式), decision_forest (决策树)
  //   "-j=2" // 同时开启的任务数量
  // ],
  "clangd.arguments": [
    "--clang-tidy",
    "--all-scopes-completion", 
    "--completion-style=detailed", 
    "--header-insertion=iwyu",
    "--pch-storage=disk", 
    "--log=verbose",
    "--j=2",
    "--background-index=false",
  ],
  // Clangd 找不到编译数据库(例如 compile_flags.json 文件)时采用的设置,缺陷是不能直接索引同一项目的不同文件,只能分析系统头文件、当前文件和include的文件
  "clangd.fallbackFlags": [
    // "-pedantic",
    // "-Wall",
    // "-Wextra",
    // "-Wcast-align",
    // "-Wdouble-promotion",
    // "-Wformat=2",
    // "-Wimplicit-fallthrough",
    // "-Wmisleading-indentation",
    // "-Wnon-virtual-dtor",
    // "-Wnull-dereference",
    // "-Wold-style-cast",
    // "-Woverloaded-virtual",
    // "-Wpedantic",
    // "-Wshadow",
    // "-Wunused",
    // "-pthread",
    // "-fuse-ld=lld",
    // "-fsanitize=address",
    // "-fsanitize=undefined",
    // "-stdlib=libc++",
    // "-std=c++20",
    "-I/home/<USER>/cpp_frame/include",
    "-I/home/<USER>/git/fast_trader_elite/data_model/include",
    "-I/home/<USER>/git/fast_trader_elite/api",
    "-I/home/<USER>/git/fast_trader_elite/common/include",
    "-I/home/<USER>/git/fast_trader_elite/md_proxy/include",
    "-I/home/<USER>/git/fast_trader_elite/i_strategy/include",
    "-I/home/<USER>/git/fast_trader_elite/strategy/include",
    "-I/home/<USER>/git/fast_trader_elite/td_proxy/include",
    "-I/home/<USER>/strategy_store/common/include",
    "-I/home/<USER>/git/fast_trader_elite/strategy_runner/include",
    "-I/home/<USER>/cpp_frame/thirdparty",
    "-I/home/<USER>/git/fast_trader_elite/crypto_api",
    "-I/home/<USER>/strategy_store_elite/frame",
    "-I/home/<USER>/git/fast_trader_elite/backtest/include",
    "-I/usr/local/include/eigen3",
    "-I/home/<USER>/git/cross_selction_strategy/frame"
  ],
  "cmake.sourceDirectory": "${workspaceFolder}/git/fast_trader_elite",
  "C_Cpp.errorSquiggles": "disabled"
}