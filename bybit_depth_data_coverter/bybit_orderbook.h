#pragma once

#include <cstddef>
#include <cstdint>
#include <cmath>
#include <functional>
#include <iostream>
#include <map>
#include <utility>
#include <vector>

class orderbook {
public:
  void init(double price_tick, int level = 50) {
    price_tick_ = price_tick;
    level_ = level;
    top_level_ask_.resize(level);
    top_level_bid_.resize(level);

    // 计算价格因子 - 这是将价格转换为整数所需的乘数
    // 例如，如果价格精度是0.00001，则价格因子为100000
    // 如果价格精度是0.00000123，则价格因子为813008.13...
    price_factor_ = 1.0 / price_tick_;

    // 预计算一个小的偏移量，用于处理浮点数精度问题
    // 这个偏移量是价格因子的一小部分，足够处理浮点数精度问题
    // 对于任意精度的价格，我们使用价格因子的百万分之一作为偏移量
    epsilon_ = price_factor_ * 1e-10;
  }

  int64_t convert_price(double price) {
    // 将浮点数价格转换为整数表示，适用于任意精度
    // 例如：
    // 1. 如果 price_tick_ = 0.00001，price_factor_ = 100000，price = 0.17869 会被转换为 17869
    // 2. 如果 price_tick_ = 0.00000123，price_factor_ = 813008.13，price = 0.00000246 会被转换为 2

    // 检查价格是否为0或接近0
    // if (price < price_tick_ / 2) {
    //   return 0;
    // }

    // 使用浮点数乘法和小偏移量，确保精确转换
    // 添加epsilon_偏移量来处理浮点数精度问题，避免舍入误差
    return static_cast<int64_t>(price * price_factor_ + epsilon_);
  }

  // 将整数价格转换回浮点数价格
  double convert_back_price(int64_t tick_price) {
    // 将整数表示的价格转换回浮点数价格，适用于任意精度
    // 例如：
    // 1. 如果 price_factor_ = 100000，tick_price = 17869 会被转换为 0.17869
    // 2. 如果 price_factor_ = 813008.13，tick_price = 2 会被转换为 0.00000246

    // 直接使用浮点数除法，简单且精确
    return static_cast<double>(tick_price) / price_factor_;
  }
  void clear() {
    asks_.clear();
    bids_.clear();
  }

  void update_ask(double ask_p, double ask_v) {
    int64_t p = convert_price(ask_p);
    if (ask_v == 0) {
      asks_.erase(p);
    } else {
      asks_[p] = ask_v;
    }
  }

  void update_bid(double bid_p, double bid_v) {
    int64_t p = convert_price(bid_p);
    if (bid_v == 0) {
      bids_.erase(p);
    } else {
      bids_[p] = bid_v;
    }
  }

  void erase_level() {
    if (asks_.size() > 5 * level_) {
      size_t i = 0;
      for (auto it = asks_.begin(); it != asks_.end();) {
        i++;
        if (i > level_) {
          it = asks_.erase(it);
        } else {
          it++;
        }
      }
    }
    if (bids_.size() > 5 * level_) {
      size_t i = 0;
      for (auto it = bids_.begin(); it != bids_.end();) {
        i++;
        if (i > level_) {
          it = bids_.erase(it);
        } else {
          it++;
        }
      }
    }
  }

  std::vector<std::pair<double, double>> &get_asks() {
    size_t i = 0;
    for (auto it = asks_.begin(); it != asks_.end(); it++) {
      if (i < level_) {
        // 使用 convert_back_price 将整数价格转换回浮点数价格
        top_level_ask_[i].first = convert_back_price(it->first);
        top_level_ask_[i].second = it->second;
        i++;
      } else {
        break;
      }
    }
    return top_level_ask_;
  }

  std::vector<std::pair<double, double>> &get_bids() {
    size_t i = 0;
    for (auto it = bids_.begin(); it != bids_.end(); it++) {
      if (i < level_) {
        // 使用 convert_back_price 将整数价格转换回浮点数价格
        top_level_bid_[i].first = convert_back_price(it->first);
        top_level_bid_[i].second = it->second;
        i++;
      } else {
        break;
      }
    }
    return top_level_bid_;
  }

private:
  double price_tick_;    // 价格最小变动单位
  size_t level_;         // 深度级别
  double price_factor_;  // 价格转换因子 (1.0 / price_tick_)
  double epsilon_;       // 小偏移量，用于处理浮点数精度问题
  std::map<int64_t, double, std::less<int>> asks_;
  std::map<int64_t, double, std::greater<int>> bids_;
  std::vector<std::pair<double, double>> top_level_ask_;
  std::vector<std::pair<double, double>> top_level_bid_;
};