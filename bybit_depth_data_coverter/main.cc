#include "bybit_orderbook.h"
#include "file_helper.h"
#include "nlohmann_json/json.hpp"
#include <cstdint>
#include <fstream>
#include <iostream>
#include <simdjson.h>
#include <string>

int main() {
  std::string config_path = "./config.json";
  nlohmann::json config_json;
  std::ifstream ifs(config_path);
  ifs >> config_json;
  double price_tick = config_json["price_tick"].get<double>();
  std::string ins = config_json["instrument"];
  std::string date = config_json["date"];
  std::string data_path = config_json["data_path"];
  orderbook orderbook;
  orderbook.init(price_tick, 10);
  using namespace simdjson;

  std::ifstream file(data_path);
  if (!file.is_open()) {
    std::cerr << "无法打开文件: " << data_path << std::endl;
    return 1;
  }
  file_helper in_file;
  in_file.open(ins + "_" + date + ".csv", true);
  std::string header = "ts,ap1,bp1,av1,bv1,ap2,bp2,av2,bv2,ap3,bp3,av3,bv3,ap4,bp4,av4,bv4,ap5,bp5,av5,bv5\n";
  in_file.write(header.c_str(), header.size());
  ondemand::parser parser_;
  std::string line;
  std::string firstLine;
  std::getline(file, firstLine);
  std::cout << firstLine << std::endl;
  file.clear();
  file.seekg(0, std::ios::beg);
  while (std::getline(file, line)) {
    padded_string padd_str = padded_string(line);
    ondemand::document doc;
    auto ret = parser_.iterate(padd_str).get(doc);
    std::string_view topic;
    ret = doc["topic"].get(topic);
    if (topic[0] == 'o') {
      std::string_view type;
      auto ret = doc["type"].get(type);
      if (ret) [[unlikely]] {
        std::cout << "error" << std::endl;
      }
      std::string_view topic;
      ret = doc["topic"].get(topic);
      auto data = doc["data"];
      // auto ins = data["s"].get_string().value();
      if (type[0] == 's') {
        orderbook.clear();
        for (auto bid_pv : data["b"]) {
          auto itr = bid_pv.get_array().begin();
          double price = (*itr).get_double_in_string();
          ++itr;
          double volume = (*itr).get_double_in_string();
          orderbook.update_bid(price, volume);
        }
        for (auto ask_pv : data["a"]) {
          auto itr = ask_pv.get_array().begin();
          double price = (*itr).get_double_in_string();
          ++itr;
          double volume = (*itr).get_double_in_string();
          orderbook.update_ask(price, volume);
        }
        uint64_t ts = doc["ts"].get_uint64();
        const auto &bids = orderbook.get_bids();
        const auto &asks = orderbook.get_asks();
        std::string content = std::to_string(ts) + ",";
        for (int i = 0; i < 5; i++) {
          std::string bp1 = std::to_string(bids[i].first);
          std::string ap1 = std::to_string(asks[i].first);
          std::string bv1 = std::to_string(bids[i].second);
          std::string av1 = std::to_string(asks[i].second);
          content += ap1 + "," + bp1 + "," +
                              av1 + "," + bv1 + ",";
        }
        content[content.size()-1] = '\n';
        in_file.write(content.c_str(), content.size());
      } else if (type[0] == 'd') {
        for (auto bid_pv : data["b"]) {
          auto itr = bid_pv.get_array().begin();
          double price = (*itr).get_double_in_string();
          ++itr;
          double volume = (*itr).get_double_in_string();
          orderbook.update_bid(price, volume);
        }
        for (auto ask_pv : data["a"]) {
          auto itr = ask_pv.get_array().begin();
          double price = (*itr).get_double_in_string();
          ++itr;
          double volume = (*itr).get_double_in_string();
          orderbook.update_ask(price, volume);
        }
        uint64_t ts = doc["ts"].get_uint64();
        const auto &bids = orderbook.get_bids();
        const auto &asks = orderbook.get_asks();
        std::string content = std::to_string(ts) + ",";
        for (int i = 0; i < 5; i++) {
          std::string bp1 = std::to_string(bids[i].first);
          std::string ap1 = std::to_string(asks[i].first);
          std::string bv1 = std::to_string(bids[i].second);
          std::string av1 = std::to_string(asks[i].second);
          content += ap1 + "," + bp1 + "," +
                              av1 + "," + bv1 + ",";
        }
        content[content.size()-1] = '\n';
        in_file.write(content.c_str(), content.size());
      }
    }
  }
  file.close();
  in_file.close();
  return 0;
}