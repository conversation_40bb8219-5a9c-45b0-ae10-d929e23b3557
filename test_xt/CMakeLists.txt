cmake_minimum_required(VERSION 3.10) # 确保CMake版本符合要求

project(XtensorRollSum LANGUAGES CXX)

# 查找 xtensor 包
# 如果你使用 Conda/Mamba，CMake 通常能自动找到
# 如果你手动安装到 /usr/local，它也能找到
# 如果你安装在非标准路径，可能需要设置 CMAKE_PREFIX_PATH
find_package(xtensor CONFIG REQUIRED)

# 添加你的源文件
add_executable(roll_sum_example test.cc)

# 链接 xtensor 库
# xtensor::xtensor 是一个 INTERFACE 库，它包含了头文件路径等信息
target_link_libraries(roll_sum_example PRIVATE xtensor::xtensor)

# C++14 或更高版本是 xtensor 的要求
# target_compile_features(roll_sum_example PRIVATE cxx_14)