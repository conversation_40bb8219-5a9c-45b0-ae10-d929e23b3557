# CMAKE generated file: DO NOT EDIT!
# Generated by "Unix Makefiles" Generator, CMake Version 3.29

# Delete rule output on recipe failure.
.DELETE_ON_ERROR:

#=============================================================================
# Special targets provided by cmake.

# Disable implicit rules so canonical targets will work.
.SUFFIXES:

# Disable VCS-based implicit rules.
% : %,v

# Disable VCS-based implicit rules.
% : RCS/%

# Disable VCS-based implicit rules.
% : RCS/%,v

# Disable VCS-based implicit rules.
% : SCCS/s.%

# Disable VCS-based implicit rules.
% : s.%

.SUFFIXES: .hpux_make_needs_suffix_list

# Command-line flag to silence nested $(MAKE).
$(VERBOSE)MAKESILENT = -s

#Suppress display of executed commands.
$(VERBOSE).SILENT:

# A target that is always out of date.
cmake_force:
.PHONY : cmake_force

#=============================================================================
# Set environment variables for the build.

# The shell in which to execute make rules.
SHELL = /bin/sh

# The CMake executable.
CMAKE_COMMAND = /usr/local/bin/cmake

# The command to remove a file.
RM = /usr/local/bin/cmake -E rm -f

# Escaping for special characters.
EQUALS = =

# The top-level source directory on which CMake was run.
CMAKE_SOURCE_DIR = /home/<USER>/git/test_xt

# The top-level build directory on which CMake was run.
CMAKE_BINARY_DIR = /home/<USER>/git/test_xt/build

# Include any dependencies generated for this target.
include CMakeFiles/roll_sum_example.dir/depend.make
# Include any dependencies generated by the compiler for this target.
include CMakeFiles/roll_sum_example.dir/compiler_depend.make

# Include the progress variables for this target.
include CMakeFiles/roll_sum_example.dir/progress.make

# Include the compile flags for this target's objects.
include CMakeFiles/roll_sum_example.dir/flags.make

CMakeFiles/roll_sum_example.dir/test.cc.o: CMakeFiles/roll_sum_example.dir/flags.make
CMakeFiles/roll_sum_example.dir/test.cc.o: /home/<USER>/git/test_xt/test.cc
CMakeFiles/roll_sum_example.dir/test.cc.o: CMakeFiles/roll_sum_example.dir/compiler_depend.ts
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green --progress-dir=/home/<USER>/git/test_xt/build/CMakeFiles --progress-num=$(CMAKE_PROGRESS_1) "Building CXX object CMakeFiles/roll_sum_example.dir/test.cc.o"
	/opt/rh/gcc-toolset-11/root/usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -MD -MT CMakeFiles/roll_sum_example.dir/test.cc.o -MF CMakeFiles/roll_sum_example.dir/test.cc.o.d -o CMakeFiles/roll_sum_example.dir/test.cc.o -c /home/<USER>/git/test_xt/test.cc

CMakeFiles/roll_sum_example.dir/test.cc.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Preprocessing CXX source to CMakeFiles/roll_sum_example.dir/test.cc.i"
	/opt/rh/gcc-toolset-11/root/usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -E /home/<USER>/git/test_xt/test.cc > CMakeFiles/roll_sum_example.dir/test.cc.i

CMakeFiles/roll_sum_example.dir/test.cc.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Compiling CXX source to assembly CMakeFiles/roll_sum_example.dir/test.cc.s"
	/opt/rh/gcc-toolset-11/root/usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -S /home/<USER>/git/test_xt/test.cc -o CMakeFiles/roll_sum_example.dir/test.cc.s

# Object files for target roll_sum_example
roll_sum_example_OBJECTS = \
"CMakeFiles/roll_sum_example.dir/test.cc.o"

# External object files for target roll_sum_example
roll_sum_example_EXTERNAL_OBJECTS =

roll_sum_example: CMakeFiles/roll_sum_example.dir/test.cc.o
roll_sum_example: CMakeFiles/roll_sum_example.dir/build.make
roll_sum_example: CMakeFiles/roll_sum_example.dir/link.txt
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green --bold --progress-dir=/home/<USER>/git/test_xt/build/CMakeFiles --progress-num=$(CMAKE_PROGRESS_2) "Linking CXX executable roll_sum_example"
	$(CMAKE_COMMAND) -E cmake_link_script CMakeFiles/roll_sum_example.dir/link.txt --verbose=$(VERBOSE)

# Rule to build all files generated by this target.
CMakeFiles/roll_sum_example.dir/build: roll_sum_example
.PHONY : CMakeFiles/roll_sum_example.dir/build

CMakeFiles/roll_sum_example.dir/clean:
	$(CMAKE_COMMAND) -P CMakeFiles/roll_sum_example.dir/cmake_clean.cmake
.PHONY : CMakeFiles/roll_sum_example.dir/clean

CMakeFiles/roll_sum_example.dir/depend:
	cd /home/<USER>/git/test_xt/build && $(CMAKE_COMMAND) -E cmake_depends "Unix Makefiles" /home/<USER>/git/test_xt /home/<USER>/git/test_xt /home/<USER>/git/test_xt/build /home/<USER>/git/test_xt/build /home/<USER>/git/test_xt/build/CMakeFiles/roll_sum_example.dir/DependInfo.cmake "--color=$(COLOR)"
.PHONY : CMakeFiles/roll_sum_example.dir/depend

