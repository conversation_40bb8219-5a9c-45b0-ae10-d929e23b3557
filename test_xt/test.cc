#include <iostream>
#include <xtensor/xarray.hpp>
#include <xtensor/xio.hpp>
#include <xtensor/xrandom.hpp>
#include <xtensor/xview.hpp>
#include <xtensor/xbuilder.hpp>

// 函数模板，用于沿着指定轴进行滚动求和 (增量计算版本)
template <typename T, std::size_t N, typename S = xt::xarray<T, N>>
xt::xarray<T, N> roll_sum_incremental(const S& arr, std::size_t window_size, std::size_t axis) {
    if (axis >= N) {
        throw std::out_of_range("Axis out of bounds for the given array dimension.");
    }
    if (window_size == 0) {
        throw std::invalid_argument("Window size must be greater than 0.");
    }
    if (arr.shape()[axis] < window_size) {
        throw std::invalid_argument("Window size cannot be greater than the dimension size along the specified axis.");
    }

    // 计算结果数组的形状
    xt::xarray<T, N> result_shape = arr.shape();
    result_shape[axis] = arr.shape()[axis] - window_size + 1;
    xt::xarray<T, N> result = xt::xarray<T, N>::from_shape(result_shape);

    // 确定非滚动轴的维度大小
    std::vector<std::size_t> non_rolling_dims_shape;
    std::vector<std::size_t> non_rolling_dims_indices;
    for (std::size_t i = 0; i < N; ++i) {
        if (i != axis) {
            non_rolling_dims_shape.push_back(arr.shape()[i]);
            non_rolling_dims_indices.push_back(i);
        }
    }

    // 迭代非滚动轴的组合（如果存在）
    xt::xarray<T> current_sum; // 用于存储当前窗口在非滚动轴上的和

    // 创建一个包含所有轴的切片，用于索引到原始数组的特定“行”或“列”
    std::vector<xt::xslice> outer_loop_slices(N);
    for (std::size_t i = 0; i < N; ++i) {
        outer_loop_slices[i] = xt::all(); // 默认全范围
    }

    // `xt::iterate_coords` 用于迭代所有非滚动轴的组合
    // 对于 2D 数组，如果 axis=0 (行滚动)，那么就只迭代列
    // 如果 axis=1 (列滚动)，那么就只迭代行
    // 对于 2D 数组，这个循环可以简化为单个循环，但在 N 维通用函数中，`iterate_coords` 更灵活
    xt::detail::flat_iterable<std::vector<std::size_t>> it(non_rolling_dims_shape);

    for (auto it_coords = it.begin(); it_coords != it.end(); ++it_coords) {
        const auto& coords = *it_coords; // 当前非滚动轴的坐标

        // 设置非滚动轴的切片，固定这些维度
        for (std::size_t dim_idx = 0; dim_idx < non_rolling_dims_indices.size(); ++dim_idx) {
            outer_loop_slices[non_rolling_dims_indices[dim_idx]] = coords[dim_idx];
        }

        // 获取当前固定非滚动轴后的“一维”数据视图
        // 例如，如果 arr 是 (R, C)，axis=0，那么这里拿到的是 arr(xt::all(), col_idx) 的视图
        // 如果 axis=1，那么拿到的是 arr(row_idx, xt::all()) 的视图
        auto current_line_view = xt::view(arr, outer_loop_slices);

        // 初始化第一个窗口的和
        // 取当前行/列的第一个窗口
        outer_loop_slices[axis] = xt::range(0, window_size);
        auto initial_window = xt::view(current_line_view, outer_loop_slices);
        current_sum = xt::sum(initial_window); // 求和，结果会是标量，或者根据上下文而定

        // 设置结果数组中非滚动轴的坐标
        std::vector<std::size_t> result_coords_prefix = coords;
        
        // 存储第一个窗口的结果
        // 对于 2D 数组，结果是 result(outer_coord_idx, 0) 或 result(0, outer_coord_idx)
        // 找到结果数组中对应的索引
        std::vector<std::size_t> result_current_index(N);
        std::size_t k = 0; // 跟踪 result_coords_prefix 的索引
        for(std::size_t dim = 0; dim < N; ++dim) {
            if (dim == axis) {
                result_current_index[dim] = 0; // 第一个滚动位置
            } else {
                result_current_index[dim] = result_coords_prefix[k++];
            }
        }
        xt::nested_view(result, result_current_index) = current_sum;


        // 增量计算后续窗口
        for (std::size_t i = 1; i <= arr.shape()[axis] - window_size; ++i) {
            // 减去离开窗口的元素
            outer_loop_slices[axis] = xt::range(i - 1, i); // 单个元素切片
            auto element_to_subtract = xt::view(current_line_view, outer_loop_slices);
            current_sum -= element_to_subtract;

            // 加上进入窗口的元素
            outer_loop_slices[axis] = xt::range(i + window_size - 1, i + window_size); // 单个元素切片
            auto element_to_add = xt::view(current_line_view, outer_loop_slices);
            current_sum += element_to_add;

            // 存储当前窗口的结果
            // 找到结果数组中对应的索引
            k = 0;
            for(std::size_t dim = 0; dim < N; ++dim) {
                if (dim == axis) {
                    result_current_index[dim] = i; // 当前滚动位置
                } else {
                    result_current_index[dim] = result_coords_prefix[k++];
                }
            }
            xt::nested_view(result, result_current_index) = current_sum;
        }
    }

    return result;
}


int main() {
    xt::xarray<double, 2> data = {
        {1, 2, 3, 4, 5},
        {6, 7, 8, 9, 10},
        {11, 12, 13, 14, 15},
        {16, 17, 18, 19, 20}
    };
    std::cout << "Original data:\n" << data << std::endl;

    std::size_t window_size = 3;

    // 沿轴 0 (行) 滚动求和 (增量计算)
    xt::xarray<double, 2> roll_sum_axis0_inc = roll_sum_incremental(data, window_size, 0);
    std::cout << "\nRoll sum (incremental) along axis 0 (window=" << window_size << "):\n" << roll_sum_axis0_inc << std::endl;
    // 预期结果：
    // [[18, 21, 24, 27, 30],
    //  [33, 36, 39, 42, 45]]


    // 沿轴 1 (列) 滚动求和 (增量计算)
    xt::xarray<double, 2> roll_sum_axis1_inc = roll_sum_incremental(data, window_size, 1);
    std::cout << "\nRoll sum (incremental) along axis 1 (window=" << window_size << "):\n" << roll_sum_axis1_inc << std::endl;
    // 预期结果：
    // [[ 6,  9, 12],
    //  [21, 24, 27],
    //  [36, 39, 42],
    //  [51, 54, 57]]

    // 示例随机数据
    xt::xarray<double, 2> big_data = xt::random::rand<double>({1000, 500}, 0.0, 10.0);
    std::cout << "\nComputing roll sum on larger random data (axis 0, window 10) with incremental method..." << std::endl;
    xt::xarray<double, 2> big_roll_sum_inc = roll_sum_incremental(big_data, 10, 0);
    std::cout << "Shape of big_roll_sum_inc: " << big_roll_sum_inc.shape() << std::endl;

    return 0;
}