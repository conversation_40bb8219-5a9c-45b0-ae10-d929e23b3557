#include <iostream>
#include <Eigen/Dense>
#include <chrono>

using namespace Eigen;
using namespace std::chrono;

void test_MatrixXd() {
    const int size = 500;
    const int n_runs = 1000;
    srand(42);

    MatrixXd mat = MatrixXd::Random(size, size);
    volatile MatrixXd tmp = mat.array().abs();  // 预热

    double checksum = 0.0;
    auto start = high_resolution_clock::now();

    for (int i = 0; i < n_runs; ++i) {
        MatrixXd abs_result = mat.array().abs();
        checksum += abs_result(0, 0);  // 防优化
    }

    auto end = high_resolution_clock::now();
    double avg_time_us = duration_cast<microseconds>(end - start).count() / n_runs;

    std::cout << "[Eigen::MatrixXd] Size " << size << "x" << size << ", " << n_runs << " runs\n"
              << "  Avg time: " << avg_time_us << " μs\n"
              << "  Checksum: " << checksum << "\n";
}
ArrayXXd Abs(const ArrayXXd& a) {
    return a.abs();
}
void test_ArrayXXd() {
    const int size = 500;
    const int n_runs = 1000;
    srand(42);

    ArrayXXd arr = ArrayXXd::Random(size, size);
    volatile ArrayXXd tmp = arr.abs();  // 预热

    double checksum = 0.0;
    auto start = high_resolution_clock::now();

    for (int i = 0; i < n_runs; ++i) {
        ArrayXXd abs_result = arr .abs();
        checksum += abs_result(0, 0);  // 防优化
    }

    auto end = high_resolution_clock::now();
    double avg_time_us = duration_cast<microseconds>(end - start).count() / n_runs;

    std::cout << "[Eigen::ArrayXXd] Size " << size << "x" << size << ", " << n_runs << " runs\n"
              << "  Avg time: " << avg_time_us << " μs\n"
              << "  Checksum: " << checksum << "\n";
}
void test_ArrayXXd_func() {
    const int size = 500;
    const int n_runs = 1000;
    srand(42);

    ArrayXXd arr = ArrayXXd::Random(size, size);
    volatile ArrayXXd tmp = arr.abs();  // 预热
    ArrayXXd arr = ArrayXXd::Random(size, size);

    double checksum = 0.0;
    auto start = high_resolution_clock::now();

    for (int i = 0; i < n_runs; ++i) {
        ArrayXXd abs_result = Abs(arr);
        checksum += abs_result(0, 0);  // 防优化
    }

    auto end = high_resolution_clock::now();
    double avg_time_us = duration_cast<microseconds>(end - start).count() / n_runs;

    std::cout << "[Eigen::ArrayXXd] Size " << size << "x" << size << ", " << n_runs << " runs\n"
              << "  Avg time: " << avg_time_us << " μs\n"
              << "  Checksum: " << checksum << "\n";
}
int main() {
    test_MatrixXd();
    test_ArrayXXd();
    test_ArrayXXd_func();
    return 0;
}