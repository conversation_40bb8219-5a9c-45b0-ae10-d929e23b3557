import numpy as np
import time

# 生成 5000x5000 的随机浮点矩阵（单精度）
size = 500
np.random.seed(42)
float_mat = np.random.randn(size, size).astype(np.float64)

# 预热
_ = np.abs(float_mat)

# 运行 1000 次，记录总时间（防优化：累加校验和）
n_runs = 1000
checksum = 0.0
start_us = time.perf_counter_ns() // 1000

for _ in range(n_runs):
    abs_result = np.abs(float_mat)
    checksum += abs_result[0, 0]  # 防优化

total_time_us = (time.perf_counter_ns() // 1000) - start_us
avg_time_us = total_time_us / n_runs

print(f"[NumPy] Size {size}x{size}, {n_runs} runs")
print(f"  Avg time: {avg_time_us:.3f} μs")
print(f"  Checksum: {checksum:.2f} (validation)")

import pandas as pd
import numpy as np
import time

# 生成 5000x5000 的 DataFrame（单精度）
size = 500
np.random.seed(42)
df = pd.DataFrame(np.random.randn(size, size).astype(np.float64))

# 预热
_ = df.abs()

# 运行 1000 次（防优化：累加校验和）
n_runs = 1000
checksum = 0.0
start_us = time.perf_counter_ns() // 1000

for _ in range(n_runs):
    abs_result = df.abs()
    checksum += abs_result.iloc[0, 0]  # 防优化

total_time_us = (time.perf_counter_ns() // 1000) - start_us
avg_time_us = total_time_us / n_runs

print(f"[Pandas] Size {size}x{size}, {n_runs} runs")
print(f"  Avg time: {avg_time_us:.3f} μs")
print(f"  Checksum: {checksum:.2f} (validation)")