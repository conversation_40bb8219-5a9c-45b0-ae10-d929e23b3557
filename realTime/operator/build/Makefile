# CMAKE generated file: DO NOT EDIT!
# Generated by "Unix Makefiles" Generator, CMake Version 3.26

# Default target executed when no arguments are given to make.
default_target: all
.PHONY : default_target

# Allow only one "make -f Makefile2" at a time, but pass parallelism.
.NOTPARALLEL:

#=============================================================================
# Special targets provided by cmake.

# Disable implicit rules so canonical targets will work.
.SUFFIXES:

# Disable VCS-based implicit rules.
% : %,v

# Disable VCS-based implicit rules.
% : RCS/%

# Disable VCS-based implicit rules.
% : RCS/%,v

# Disable VCS-based implicit rules.
% : SCCS/s.%

# Disable VCS-based implicit rules.
% : s.%

.SUFFIXES: .hpux_make_needs_suffix_list

# Command-line flag to silence nested $(MAKE).
$(VERBOSE)MAKESILENT = -s

#Suppress display of executed commands.
$(VERBOSE).SILENT:

# A target that is always out of date.
cmake_force:
.PHONY : cmake_force

#=============================================================================
# Set environment variables for the build.

# The shell in which to execute make rules.
SHELL = /bin/sh

# The CMake executable.
CMAKE_COMMAND = /usr/bin/cmake

# The command to remove a file.
RM = /usr/bin/cmake -E rm -f

# Escaping for special characters.
EQUALS = =

# The top-level source directory on which CMake was run.
CMAKE_SOURCE_DIR = /home/<USER>/git/realTime/operator

# The top-level build directory on which CMake was run.
CMAKE_BINARY_DIR = /home/<USER>/git/realTime/operator/build

#=============================================================================
# Targets provided globally by CMake.

# Special rule for the target edit_cache
edit_cache:
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --cyan "Running CMake cache editor..."
	/usr/bin/ccmake -S$(CMAKE_SOURCE_DIR) -B$(CMAKE_BINARY_DIR)
.PHONY : edit_cache

# Special rule for the target edit_cache
edit_cache/fast: edit_cache
.PHONY : edit_cache/fast

# Special rule for the target rebuild_cache
rebuild_cache:
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --cyan "Running CMake to regenerate build system..."
	/usr/bin/cmake --regenerate-during-build -S$(CMAKE_SOURCE_DIR) -B$(CMAKE_BINARY_DIR)
.PHONY : rebuild_cache

# Special rule for the target rebuild_cache
rebuild_cache/fast: rebuild_cache
.PHONY : rebuild_cache/fast

# The main all target
all: cmake_check_build_system
	$(CMAKE_COMMAND) -E cmake_progress_start /home/<USER>/git/realTime/operator/build/CMakeFiles /home/<USER>/git/realTime/operator/build//CMakeFiles/progress.marks
	$(MAKE) $(MAKESILENT) -f CMakeFiles/Makefile2 all
	$(CMAKE_COMMAND) -E cmake_progress_start /home/<USER>/git/realTime/operator/build/CMakeFiles 0
.PHONY : all

# The main clean target
clean:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/Makefile2 clean
.PHONY : clean

# The main clean target
clean/fast: clean
.PHONY : clean/fast

# Prepare targets for installation.
preinstall: all
	$(MAKE) $(MAKESILENT) -f CMakeFiles/Makefile2 preinstall
.PHONY : preinstall

# Prepare targets for installation.
preinstall/fast:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/Makefile2 preinstall
.PHONY : preinstall/fast

# clear depends
depend:
	$(CMAKE_COMMAND) -S$(CMAKE_SOURCE_DIR) -B$(CMAKE_BINARY_DIR) --check-build-system CMakeFiles/Makefile.cmake 1
.PHONY : depend

#=============================================================================
# Target rules for targets named test_operators_csv

# Build rule for target.
test_operators_csv: cmake_check_build_system
	$(MAKE) $(MAKESILENT) -f CMakeFiles/Makefile2 test_operators_csv
.PHONY : test_operators_csv

# fast build rule for target.
test_operators_csv/fast:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/test_operators_csv.dir/build.make CMakeFiles/test_operators_csv.dir/build
.PHONY : test_operators_csv/fast

feature_operator.o: feature_operator.cc.o
.PHONY : feature_operator.o

# target to build an object file
feature_operator.cc.o:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/test_operators_csv.dir/build.make CMakeFiles/test_operators_csv.dir/feature_operator.cc.o
.PHONY : feature_operator.cc.o

feature_operator.i: feature_operator.cc.i
.PHONY : feature_operator.i

# target to preprocess a source file
feature_operator.cc.i:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/test_operators_csv.dir/build.make CMakeFiles/test_operators_csv.dir/feature_operator.cc.i
.PHONY : feature_operator.cc.i

feature_operator.s: feature_operator.cc.s
.PHONY : feature_operator.s

# target to generate assembly for a file
feature_operator.cc.s:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/test_operators_csv.dir/build.make CMakeFiles/test_operators_csv.dir/feature_operator.cc.s
.PHONY : feature_operator.cc.s

test_operators_csv.o: test_operators_csv.cpp.o
.PHONY : test_operators_csv.o

# target to build an object file
test_operators_csv.cpp.o:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/test_operators_csv.dir/build.make CMakeFiles/test_operators_csv.dir/test_operators_csv.cpp.o
.PHONY : test_operators_csv.cpp.o

test_operators_csv.i: test_operators_csv.cpp.i
.PHONY : test_operators_csv.i

# target to preprocess a source file
test_operators_csv.cpp.i:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/test_operators_csv.dir/build.make CMakeFiles/test_operators_csv.dir/test_operators_csv.cpp.i
.PHONY : test_operators_csv.cpp.i

test_operators_csv.s: test_operators_csv.cpp.s
.PHONY : test_operators_csv.s

# target to generate assembly for a file
test_operators_csv.cpp.s:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/test_operators_csv.dir/build.make CMakeFiles/test_operators_csv.dir/test_operators_csv.cpp.s
.PHONY : test_operators_csv.cpp.s

# Help Target
help:
	@echo "The following are some of the valid targets for this Makefile:"
	@echo "... all (the default if no target is provided)"
	@echo "... clean"
	@echo "... depend"
	@echo "... edit_cache"
	@echo "... rebuild_cache"
	@echo "... test_operators_csv"
	@echo "... feature_operator.o"
	@echo "... feature_operator.i"
	@echo "... feature_operator.s"
	@echo "... test_operators_csv.o"
	@echo "... test_operators_csv.i"
	@echo "... test_operators_csv.s"
.PHONY : help



#=============================================================================
# Special targets to cleanup operation of make.

# Special rule to run CMake to check the build system integrity.
# No rule that depends on this can have commands that come from listfiles
# because they might be regenerated.
cmake_check_build_system:
	$(CMAKE_COMMAND) -S$(CMAKE_SOURCE_DIR) -B$(CMAKE_BINARY_DIR) --check-build-system CMakeFiles/Makefile.cmake 0
.PHONY : cmake_check_build_system

