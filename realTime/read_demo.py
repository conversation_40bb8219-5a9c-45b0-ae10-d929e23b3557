import ctypes
import mmap
import numpy as np
import pandas as pd
from datetime import datetime
import os
import argparse
import time

# 常量定义
KLINE_SHM_MAGIC = 0x4B4C494E  # "KLIN" in ASCII
KLINE_SHM_VERSION = 1
MAX_CONTRACTS = 1000
MAX_KLINES_PER_CONTRACT = 5000

# 使用ctypes定义与C++相同的数据结构
class KlineData(ctypes.Structure):
    _fields_ = [
        ("timestamp", ctypes.c_uint64),
        ("open", ctypes.c_double),
        ("high", ctypes.c_double),
        ("low", ctypes.c_double),
        ("close", ctypes.c_double),
        ("volume", ctypes.c_double)
    ]

class ContractMetadata(ctypes.Structure):
    _fields_ = [
        ("instrument_name", ctypes.c_char * 64),
        ("kline_count", ctypes.c_uint32),  # 原子类型，但在Python中仍然表示为uint32
        ("head_index", ctypes.c_uint32),   # 原子类型，但在Python中仍然表示为uint32
        ("last_timestamp", ctypes.c_uint64),
        ("reserved", ctypes.c_uint32 * 2)  # 保留字段减少为2个
    ]

class KlineShmHeader(ctypes.Structure):
    _fields_ = [
        ("magic", ctypes.c_uint32),
        ("version", ctypes.c_uint32),
        ("max_contracts", ctypes.c_uint32),
        ("max_klines_per_contract", ctypes.c_uint32),
        ("contract_count", ctypes.c_uint32),
        ("last_update_time", ctypes.c_uint64),
        ("reserved", ctypes.c_uint32 * 8)
    ]

class KlineReader:
    def __init__(self, shm_path):
        """
        初始化K线读取器

        参数:
            shm_path: 共享内存文件路径
        """
        self.shm_path = shm_path
        self.shm = None
        self.header = None
        self.contracts = []
        self.open_shm()

    def open_shm(self):
        """打开共享内存文件并读取元数据"""
        try:
            fd = os.open(self.shm_path, os.O_RDONLY)
            self.shm = mmap.mmap(fd, 0, mmap.MAP_SHARED, mmap.PROT_READ)
            os.close(fd)

            # 读取头部信息
            header_size = ctypes.sizeof(KlineShmHeader)
            header_data = self.shm[:header_size]
            self.header = KlineShmHeader.from_buffer_copy(header_data)

            # 验证魔数和版本
            if self.header.magic != KLINE_SHM_MAGIC:
                raise ValueError(f"无效的共享内存格式，魔数不匹配: {self.header.magic}")

            if self.header.version != KLINE_SHM_VERSION:
                print(f"警告: 共享内存版本不匹配，期望 {KLINE_SHM_VERSION}，实际 {self.header.version}")

            # 读取合约信息
            contract_size = ctypes.sizeof(ContractMetadata)
            for i in range(self.header.contract_count):
                offset = header_size + i * contract_size
                contract_data = self.shm[offset:offset + contract_size]
                contract = ContractMetadata.from_buffer_copy(contract_data)
                self.contracts.append(contract)

            print(f"成功打开共享内存，包含 {len(self.contracts)} 个合约 最近更新时间:{self.header.last_update_time}")

        except Exception as e:
            print(f"打开共享内存失败: {e}")
            if hasattr(self, 'shm') and self.shm:
                self.shm.close()
                self.shm = None

    def get_contract_names(self):
        """获取所有合约名称"""
        if not self.contracts:
            return []

        names = []
        for contract in self.contracts:
            name = contract.instrument_name.decode('utf-8').rstrip('\0')
            names.append(name)
        return names

    def read_kline_data(self, instrument=None, fields=None):
        """
        读取K线数据 - 使用NumPy内存映射的极速版本

        参数:
            instrument: 合约名称，如果为None则读取所有合约
            fields: 需要读取的字段列表，默认为['close', 'high', 'low', 'open', 'volume']

        返回:
            一个字典，键为字段名，值为DataFrame
        """
        if not self.header or not self.shm:
            return {}

        if fields is None:
            fields = ['close', 'high', 'low', 'open', 'volume']

        # 创建结果字典
        result = {field: {} for field in fields}

        # 字段到结构体中索引的映射
        field_indices = {
            'timestamp': 0,
            'open': 1,
            'high': 2,
            'low': 3,
            'close': 4,
            'volume': 5
        }

        # 确定要读取的合约列表
        contract_indices = []
        if instrument is None:
            # 读取所有合约
            for i in range(len(self.contracts)):
                contract_indices.append(i)
        else:
            # 读取指定合约
            for i, contract in enumerate(self.contracts):
                name = contract.instrument_name.decode('utf-8').rstrip('\0')
                if name == instrument:
                    contract_indices.append(i)
                    break

        # 计算基础偏移量
        header_size = ctypes.sizeof(KlineShmHeader)
        contracts_size = ctypes.sizeof(ContractMetadata) * MAX_CONTRACTS
        kline_size = ctypes.sizeof(KlineData)

        # 读取每个合约的数据
        for idx in contract_indices:
            contract = self.contracts[idx]
            name = contract.instrument_name.decode('utf-8').rstrip('\0')

            kline_count = min(contract.kline_count, MAX_KLINES_PER_CONTRACT)
            if kline_count == 0:
                continue

            # 计算数据区域的起始位置
            data_start = header_size + contracts_size + (idx * MAX_KLINES_PER_CONTRACT * kline_size)

            # 创建一个直接映射到共享内存的numpy数组
            # 每个K线数据包含6个double值：timestamp, open, high, low, close, volume
            # 注意：这里假设所有字段都是double类型，实际上timestamp是uint64，但在内存布局上大小相同
            kline_array = np.frombuffer(
                self.shm,
                dtype=np.float64,
                count=MAX_KLINES_PER_CONTRACT * 6,  # 每个K线6个字段
                offset=data_start
            ).reshape(MAX_KLINES_PER_CONTRACT, 6)

            # 提取需要的数据
            indices = [(contract.head_index + i) % MAX_KLINES_PER_CONTRACT for i in range(kline_count)]

            # 提取时间戳并创建datetime索引
            # 注意：这里需要将float64转换回uint64，以正确解释时间戳
            timestamps = kline_array[indices, field_indices['timestamp']].view(np.uint64)
            unique_mask = pd.Series(timestamps).duplicated(keep='last')  # 标记重复值（保留最后一个）
            timestamps = timestamps[~unique_mask]  # 去重
            
            # 检查是否有重复
            # 检查重复时间戳
            # unique_ts, counts = np.unique(timestamps, return_counts=True)
            # duplicate_ts = unique_ts[counts > 1]

            # if len(duplicate_ts) > 0:
            #     print(f"\n合约 {name} 发现 {len(duplicate_ts)} 个重复时间戳，共 {len(timestamps)} 条数据")
                
            #     # 打印前5个重复时间戳的详细信息
            #     for ts in duplicate_ts[:5]:
            #         # 找到该时间戳对应的所有行索引
            #         dup_indices = np.where(timestamps == ts)[0]
                    
            #         print(f"\n重复时间戳: {datetime.fromtimestamp(ts/1000)} ({ts})")
            #         print("出现次数:", len(dup_indices))
            #         print("对应的K线数据:")
                    
            #         # 打印每条重复数据
            #         for i in dup_indices:
            #             data_idx = indices[i]  # 获取原始数据索引
            #             print(f"  第{i}条: ", end="")
            #             print(f"O:{kline_array[data_idx,1]:.2f} ", end="")
            #             print(f"H:{kline_array[data_idx,2]:.2f} ", end="")
            #             print(f"L:{kline_array[data_idx,3]:.2f} ", end="")
            #             print(f"C:{kline_array[data_idx,4]:.2f} ", end="")
            #             print(f"V:{kline_array[data_idx,5]:.2f}")
            # datetime_index = [datetime.fromtimestamp(ts / 1000) for ts in timestamps]

            # 提取每个字段的数据
            for field in fields:
                if field in field_indices:
                    data = kline_array[indices, field_indices[field]][~unique_mask]  # 同步过滤数据
                    result[field][name] = pd.Series(data, index=timestamps)

        # 将每个字段的数据转换为DataFrame
        for field in fields:
            if result[field]:
                result[field] = pd.DataFrame(result[field])

        return result

    def close(self):
        """关闭共享内存"""
        if self.shm:
            self.shm.close()
            self.shm = None
            self.header = None
            self.contracts = []

def main():
    path = "/home/<USER>/git/fast_trader_elite_pkg/main/kline_data.shm"
    # 创建K线读取器
    reader = KlineReader(path)
    data = reader.read_kline_data()
    # print(data['close'].head())
    reader.close()



if __name__ == "__main__":
    main()
