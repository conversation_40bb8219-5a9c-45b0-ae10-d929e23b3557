import pandas as pd
import numpy as np
import time
import os


def get_minFactor(Open, High, Low, Close, Volume, Amount, VWAP,form):
    # from bn_mf_11.Feature.code.feature_operator_funcs import ts_Corr, ts_Delay, Abs, pn_<PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON>, <PERSON>,ts_<PERSON>, ts_Stdev, ts_Sum, get<PERSON><PERSON>, Sqrt, ts_<PERSON>, ts_<PERSON>, pn_Rank, Equal,Max ,Min, Log, ts_Regression
    # from bn_mf_11.Feature.code.feature_operator_funcs import Tot_Mean, Tot_Sum, Tot_Stdev, Tot_Delta, Tot_Divide, Tot_ChgRate, Tot_Rank, Tot_Min, Tot_Max, Tot_ArgMax, Tot_ArgMin , GetSingleBar
    from feature_operator_funcs import ts_Corr, ts_Delay, Abs, pn_Mean, IfThen, Mthan, And,ts_Mean, ts_Stdev, ts_<PERSON>m, get<PERSON><PERSON>, <PERSON>q<PERSON>, ts_<PERSON>, ts_<PERSON>, pn_<PERSON>, <PERSON>,<PERSON> ,<PERSON>, Log, ts_Regression
    from feature_operator_funcs import Tot_Mean, Tot_Sum, Tot_Stdev, Tot_Delta, Tot_Divide, Tot_ChgRate, Tot_Rank, Tot_Min, Tot_Max, Tot_ArgMax, Tot_ArgMin , GetSingleBar
    tmp = eval(form)
    return tmp


def get_minFactorForms2_long():
    Forms = pd.DataFrame(columns = ['type','forms'])
    forms = pd.DataFrame(columns = ['type','forms'])
    p1_corrs = [
                'ts_Corr(Close,Volume,120)',
                'ts_Corr(Close/ts_Delay(Close,1)-1,Volume,120)',
                'ts_Corr(ts_Delay(Close,1),Volume,120)',
                'ts_Corr(Close,ts_Delay(Volume,1),120)',
                'ts_Corr(ts_Delay(Close/ts_Delay(Close,1)-1,1),Close/ts_Delay(Close,1)-1,120)',
                'ts_Corr(ts_Delay(Close/ts_Delay(Close,1)-1,1),Close,120)',
                'ts_Corr(Volume,Volume-ts_Delay(Volume,1),120)',
                'ts_Corr(Volume-ts_Delay(Volume,1), Volume-ts_Delay(Volume,1) - ts_Delay(Volume-ts_Delay(Volume,1),1),120)',
                'ts_Corr(VWAP,Volume,120)',
                'ts_Corr(VWAP/ts_Delay(VWAP,1)-1,Volume,120)',
                'ts_Corr(ts_Delay(VWAP,1),Volume,120)',
                'ts_Corr(VWAP,ts_Delay(Volume,1),120)',
                'ts_Corr(ts_Delay(VWAP/ts_Delay(VWAP,1)-1,1),VWAP/ts_Delay(VWAP,1)-1,120)',
                'ts_Corr(ts_Delay(VWAP/ts_Delay(VWAP,1)-1,1),VWAP,120)',
                'ts_Corr(Volume,Volume-ts_Delay(Volume,1),120)',
                'ts_Corr(Volume-ts_Delay(Volume,1), Volume-ts_Delay(Volume,1) - ts_Delay(Volume-ts_Delay(Volume,1),1),120)',
                ]
    names = 'p1_corrs'
    forms['forms'] = p1_corrs
    forms['type'] = names
    z = []
    for v in range(len(p1_corrs)):
        z.append(names + str(int(v)))
    forms['fname'] = z
    Forms = pd.concat([Forms,forms],axis = 0)

    forms = pd.DataFrame(columns = ['type','forms'])
    p2_et = [
            'Tot_Mean(IfThen(IfThen(Volume-ts_Delay(Volume,1)-Tot_Mean(Volume-ts_Delay(Volume,1))-Tot_Stdev(Volume-ts_Delay(Volume,1)),1,0),Close/ts_Delay(Close,1)-1,getNan(Close/ts_Delay(Close,1)-1)))',
            'Tot_Mean(IfThen(ts_Sum(IfThen(Tot_Rank(Volume-ts_Delay(Volume,1))-0.9,1,0),20),Close/ts_Delay(Close,1)-1,getNan(Close/ts_Delay(Close,1)-1)))',
            'Tot_Stdev(IfThen(ts_Sum(IfThen(Tot_Rank(Close/ts_Delay(Close,1)-1)-0.9,1,0),20),Close/ts_Delay(Close,1)-1,getNan(Close/ts_Delay(Close,1)-1)))',
            'Tot_Mean(IfThen(IfThen(Close/ts_Delay(Close,1)-1-Tot_Mean(Close/ts_Delay(Close,1)-1)-Tot_Stdev(Close/ts_Delay(Close,1)-1),1,0),Close/ts_Delay(Close,1)-1,getNan(Close/ts_Delay(Close,1)-1)))',
            'Tot_Mean(IfThen(ts_Sum(IfThen(Tot_Rank(Close/ts_Delay(Close,1)-1)-0.9,1,0),20),Close/ts_Delay(Close,1)-1,getNan(Close/ts_Delay(Close,1)-1)))',
            'ts_Stdev(Close/ts_Delay(Close,1)-1,120)-pn_Mean(ts_Stdev(Close/ts_Delay(Close,1)-1,120))',
            'Abs(ts_Stdev(Close/ts_Delay(Close,1)-1,120)-pn_Mean(ts_Stdev(Close/ts_Delay(Close,1)-1,120)))',
            'ts_Mean(Close/ts_Delay(Close,1)-1,120)-pn_Mean(ts_Mean(Close/ts_Delay(Close,1)-1,120))',
            'Abs(ts_Mean(Close/ts_Delay(Close,1)-1,120)-pn_Mean(ts_Mean(Close/ts_Delay(Close,1)-1,120)))',
            'ts_Mean((Close/ts_Delay(Close,1)-1)*IfThen(Volume-ts_Mean(Volume,60)-1.25*ts_Stdev(Volume,60),1,0),60)',
            'ts_Mean(IfThen((Close/ts_Delay(Close,1)-1),(Close/ts_Delay(Close,1)-1),0)*IfThen(Volume-ts_Mean(Volume,60)-0.5*ts_Stdev(Volume,60),1,0),60)',
            'Tot_Sum(IfThen(ts_Delay(ts_Min(Low,60),1)-Low,1,0))',
            'Tot_Stdev(Abs(Close/ts_Delay(Close,1)-1-pn_Mean(Close/ts_Delay(Close,1)-1)))',
            'Tot_Stdev(pn_Rank(Close/ts_Delay(Close,1)-1))',
            'Tot_Stdev(pn_Rank(Volume))',
            'Tot_Mean(Abs(Close/ts_Delay(Close,1)-1-pn_Mean(Close/ts_Delay(Close,1)-1))/(Abs(pn_Mean(Close/ts_Delay(Close,1)-1))+Abs(Close/ts_Delay(Close,1)-1)+0.1))',
            'Tot_Mean(IfThen(-(Close/ts_Delay(Close,1)-1)+(Tot_Mean(Close/ts_Delay(Close,1)-1)-Tot_Stdev(Close/ts_Delay(Close,1)-1)),Close/ts_Delay(Close,1)-1,getNan(Close)))',
            'Tot_Mean(IfThen(Tot_Rank(Close/ts_Delay(Close,1)-1)-0.93,Close/ts_Delay(Close,1)-1,getNan(Close)))',
            'Tot_Mean(IfThen(0.07-Tot_Rank(Close/ts_Delay(Close,1)-1),Close/ts_Delay(Close,1)-1,getNan(Close)))',
            'Tot_Sum(IfThen(Equal(Close/ts_Delay(Close,1)-1,0),1,getNan(Close)))',
        ]
    names = 'p2_et'
    forms['forms'] = p2_et
    forms['type'] = names
    z = []
    for v in range(len(p2_et)):
        z.append(names + str(int(v)))
    forms['fname'] = z
    Forms = pd.concat([Forms,forms],axis = 0)

    forms = pd.DataFrame(columns = ['type','forms'])
    p3_mf = [
                'Tot_Mean(Abs(Close/Open-1)/Sqrt(Volume))',
                'Tot_Stdev(Abs(Close/Open-1)/Sqrt(Volume))',
                'Tot_Mean(IfThen(Tot_Rank(Abs(Close/Open-1)/Sqrt(Volume))-0.8,Close,getNan(Close)))',
                'Tot_Mean(IfThen(Tot_Rank(Abs(Close/Open-1)+(Volume))-0.8,Close,getNan(Close)))',
                'Tot_Mean(IfThen(Tot_Rank(Abs(Close/Open-1)/Log(Volume))-0.8,Close,getNan(Close)))',
                'Tot_Sum(IfThen(Equal(Abs(Close-Open),Abs(High-Low)),Amount,getNan(Close)))',
                'Tot_Sum(IfThen(Tot_Rank(Abs(Close/Open-1)/Sqrt(Volume))-0.8,Volume,getNan(Close)))',
                'Tot_Mean(ts_Stdev(Close/ts_Delay(Close,1)-1,20))',
                'Tot_Mean(ts_Stdev(ts_Stdev(Close/ts_Delay(Close,1)-1,20),20))',
                'ts_Corr(ts_Stdev(ts_Stdev(Close/ts_Delay(Close,1)-1,20),20),Volume*Close,120)',
                'Tot_Sum(IfThen(ts_Stdev(ts_Stdev(Close/ts_Delay(Close,1)-1,20),20)-Tot_Mean(ts_Stdev(ts_Stdev(Close/ts_Delay(Close,1)-1,20),20)),Volume*Close,getNan(Close)))',
                'Tot_Sum(IfThen(Tot_Rank(Volume)-0.8,(Close-Open)/Open,getNan(Close)))',
                '(Tot_Sum(IfThen(0.2-Tot_Rank(Volume),(Close-Open)/Open,getNan(Close))))',
            ]
    names = 'p3_mf'
    forms['forms'] = p3_mf
    forms['type'] = names
    z = []
    for v in range(len(p3_mf)):
        z.append(names + str(int(v)))
    forms['fname'] = z
    Forms = pd.concat([Forms,forms],axis = 0)

    forms = pd.DataFrame(columns = ['type','forms'])
    p4_ms = [
            'Tot_Mean(Close/ts_Delay(Close,1)-1-Log(Close/ts_Delay(Close,1)))',
            'Tot_Mean(Close/ts_Delay(Close,1)-1-Log(Close/ts_Delay(Close,1))-(Log(Close/ts_Delay(Close,1)))**2/2)',
            'Tot_ArgMax(Close)',
            'Tot_ArgMin(Close)',
            'Tot_Sum(((Close-ts_Delay(Close,1))/Close)**2)',
            'Tot_Sum(((Close-ts_Delay(Close,1))/Close)**3)',
            'Tot_Sum(IfThen(Close/ts_Delay(Close,1)-1,Volume,getNan(Close)))',
            ]
    names = 'p4_ms'
    forms['forms'] = p4_ms
    forms['type'] = names
    z = []
    for v in range(len(p4_ms)):
        z.append(names + str(int(v)))
    forms['fname'] = z
    Forms = pd.concat([Forms,forms],axis = 0)

    forms = pd.DataFrame(columns = ['type','forms'])
    p5_to = [
            'Tot_Sum(IfThen(Close-ts_Delay(Close,1),Amount,-Amount))',
            'Tot_ArgMax(Volume)',
            'ts_Corr(Amount,ts_Delay(Amount,1),120)',
            'Tot_Sum(Amount)/(Tot_Sum(Abs(High-Low)/Close+Abs(Open-ts_Delay(Close,1)))*(1+Tot_Stdev(Close)/Tot_Mean(Close)))',
            'Tot_Mean(Abs(Close/ts_Delay(Close,1)-1)/Amount)',
            'Tot_Sum((High-Low)/Close)/Tot_Sum(Amount)',
            'Tot_Sum((2*(High-Low)-Abs(Open-Close))/Close)/Tot_Sum(Amount)',
            'Tot_Sum(Abs(Close/ts_Delay(Close,1)-1)/(Close*Volume))',
            ]
    names = 'p5_to'
    forms['forms'] = p5_to
    forms['type'] = names
    z = []
    for v in range(len(p5_to)):
        z.append(names + str(int(v)))
    forms['fname'] = z
    Forms = pd.concat([Forms,forms],axis = 0)

    forms = pd.DataFrame(columns = ['type','forms'])
    p6_tn = [
            'Tot_Sum(IfThen(Close-(ts_Mean(Close,60)+ts_Stdev(Close,60)),-1,IfThen((ts_Mean(Close,60)-ts_Stdev(Close,60))-Close,1,0)))',
            'Tot_Sum(High-Open)-Tot_Sum(Open-Low)',
            "ts_Regression(High,Low,20,'D')",
            'Tot_Mean(((High+Low)-ts_Delay(High+Low,1))*(High-Low)/2/Amount)',
            'Tot_Sum(IfThen(Close-ts_Delay(Close,1),1,0))',
            'Tot_Mean(High-ts_Delay(Close,1))/Tot_Sum(ts_Delay(Close,1)-Low)',
            'Tot_Mean((ts_Max(High,60)-Close)/(ts_Max(High,60)-ts_Min(Low,60)))',
            'Tot_Mean((Close-ts_Min(Low,60))/(ts_Max(High,60)-ts_Min(Low,60)))',
            'Tot_Mean((High-Low)/Close)',
            'Tot_Mean(IfThen(Tot_Rank((Close-ts_Delay(Close,1))/Close)-0.95,(Close-ts_Delay(Close,1))/Close,getNan(Close)))',
            'Tot_Mean(IfThen(Tot_Rank(0.05-(Close-ts_Delay(Close,1))/Close),(Close-ts_Delay(Close,1))/Close,getNan(Close)))',
            'ts_Corr(Close-ts_Delay(Close,1),pn_Mean(Close-ts_Delay(Close,1)),120)',
            'Tot_Sum(IfThen(Tot_Rank(High-Low)-0.5,Volume,0))-Tot_Sum(IfThen(Tot_Rank(High-Low)-0.5,0,Volume))',
            'Tot_Sum(IfThen(Close-ts_Delay(Close,1),Volume,-Volume))',
            ]
    names = 'p6_tn'
    forms['forms'] = p6_tn
    forms['type'] = names
    z = []
    for v in range(len(p6_tn)):
        z.append(names + str(int(v)))
    forms['fname'] = z
    Forms = pd.concat([Forms,forms],axis = 0)
    return Forms



def get_minFactorForms3():
    Forms = pd.DataFrame(columns = ['type','forms'])
    forms = pd.DataFrame(columns = ['type','forms'])
    p1_corrs = [
                'ts_Corr(Close,Volume,60)',
                'ts_Corr(Close/ts_Delay(Close,1)-1,Volume,60)',
                'ts_Corr(ts_Delay(Close,1),Volume,60)',
                'ts_Corr(Close,ts_Delay(Volume,1),60)',
                'ts_Corr(ts_Delay(Close/ts_Delay(Close,1)-1,1),Close/ts_Delay(Close,1)-1,60)',
                'ts_Corr(ts_Delay(Close/ts_Delay(Close,1)-1,1),Close,60)',
                'ts_Corr(Volume,Volume-ts_Delay(Volume,1),60)',
                'ts_Corr(Volume-ts_Delay(Volume,1), Volume-ts_Delay(Volume,1) - ts_Delay(Volume-ts_Delay(Volume,1),1),60)',
                'ts_Corr(VWAP,Volume,60)',
                'ts_Corr(VWAP/ts_Delay(VWAP,1)-1,Volume,60)',
                'ts_Corr(ts_Delay(VWAP,1),Volume,60)',
                'ts_Corr(VWAP,ts_Delay(Volume,1),60)',
                'ts_Corr(ts_Delay(VWAP/ts_Delay(VWAP,1)-1,1),VWAP/ts_Delay(VWAP,1)-1,60)',
                'ts_Corr(ts_Delay(VWAP/ts_Delay(VWAP,1)-1,1),VWAP,60)',
                'ts_Corr(Volume,Volume-ts_Delay(Volume,1),60)',
                'ts_Corr(Volume-ts_Delay(Volume,1), Volume-ts_Delay(Volume,1) - ts_Delay(Volume-ts_Delay(Volume,1),1),60)',
                ]
    names = 'p1_corrs'
    forms['forms'] = p1_corrs
    forms['type'] = names
    z = []
    for v in range(len(p1_corrs)):
        z.append(names + str(int(v)))
    forms['fname'] = z
    Forms = pd.concat([Forms,forms],axis = 0)

    forms = pd.DataFrame(columns = ['type','forms'])
    p2_et = [
            'Tot_Mean(IfThen(IfThen(Volume-ts_Delay(Volume,1)-Tot_Mean(Volume-ts_Delay(Volume,1))-Tot_Stdev(Volume-ts_Delay(Volume,1)),1,0),Close/ts_Delay(Close,1)-1,getNan(Close/ts_Delay(Close,1)-1)))',
            'Tot_Mean(IfThen(ts_Sum(IfThen(Tot_Rank(Volume-ts_Delay(Volume,1))-0.9,1,0),10),Close/ts_Delay(Close,1)-1,getNan(Close/ts_Delay(Close,1)-1)))',
            'Tot_Stdev(IfThen(ts_Sum(IfThen(Tot_Rank(Close/ts_Delay(Close,1)-1)-0.9,1,0),10),Close/ts_Delay(Close,1)-1,getNan(Close/ts_Delay(Close,1)-1)))',
            'Tot_Mean(IfThen(IfThen(Close/ts_Delay(Close,1)-1-Tot_Mean(Close/ts_Delay(Close,1)-1)-Tot_Stdev(Close/ts_Delay(Close,1)-1),1,0),Close/ts_Delay(Close,1)-1,getNan(Close/ts_Delay(Close,1)-1)))',
            'Tot_Mean(IfThen(ts_Sum(IfThen(Tot_Rank(Close/ts_Delay(Close,1)-1)-0.9,1,0),10),Close/ts_Delay(Close,1)-1,getNan(Close/ts_Delay(Close,1)-1)))',
            'ts_Stdev(Close/ts_Delay(Close,1)-1,60)-pn_Mean(ts_Stdev(Close/ts_Delay(Close,1)-1,60))',
            'Abs(ts_Stdev(Close/ts_Delay(Close,1)-1,60)-pn_Mean(ts_Stdev(Close/ts_Delay(Close,1)-1,60)))',
            'ts_Mean(Close/ts_Delay(Close,1)-1,60)-pn_Mean(ts_Mean(Close/ts_Delay(Close,1)-1,60))',
            'Abs(ts_Mean(Close/ts_Delay(Close,1)-1,60)-pn_Mean(ts_Mean(Close/ts_Delay(Close,1)-1,60)))',
            'ts_Mean((Close/ts_Delay(Close,1)-1)*IfThen(Volume-ts_Mean(Volume,30)-1.210*ts_Stdev(Volume,30),1,0),30)',
            'ts_Mean(IfThen((Close/ts_Delay(Close,1)-1),(Close/ts_Delay(Close,1)-1),0)*IfThen(Volume-ts_Mean(Volume,30)-0.10*ts_Stdev(Volume,30),1,0),30)',
            'Tot_Sum(IfThen(ts_Delay(ts_Min(Low,30),1)-Low,1,0))',
            'Tot_Stdev(Abs(Close/ts_Delay(Close,1)-1-pn_Mean(Close/ts_Delay(Close,1)-1)))',
            'Tot_Stdev(pn_Rank(Close/ts_Delay(Close,1)-1))',
            'Tot_Stdev(pn_Rank(Volume))',
            'Tot_Mean(Abs(Close/ts_Delay(Close,1)-1-pn_Mean(Close/ts_Delay(Close,1)-1))/(Abs(pn_Mean(Close/ts_Delay(Close,1)-1))+Abs(Close/ts_Delay(Close,1)-1)+0.1))',
            'Tot_Mean(IfThen(-(Close/ts_Delay(Close,1)-1)+(Tot_Mean(Close/ts_Delay(Close,1)-1)-Tot_Stdev(Close/ts_Delay(Close,1)-1)),Close/ts_Delay(Close,1)-1,getNan(Close)))',
            'Tot_Mean(IfThen(Tot_Rank(Close/ts_Delay(Close,1)-1)-0.93,Close/ts_Delay(Close,1)-1,getNan(Close)))',
            'Tot_Mean(IfThen(0.07-Tot_Rank(Close/ts_Delay(Close,1)-1),Close/ts_Delay(Close,1)-1,getNan(Close)))',
            'Tot_Sum(IfThen(Equal(Close/ts_Delay(Close,1)-1,0),1,getNan(Close)))',
        ]
    names = 'p2_et'
    forms['forms'] = p2_et
    forms['type'] = names
    z = []
    for v in range(len(p2_et)):
        z.append(names + str(int(v)))
    forms['fname'] = z
    Forms = pd.concat([Forms,forms],axis = 0)

    forms = pd.DataFrame(columns = ['type','forms'])
    p3_mf = [
                'Tot_Mean(Abs(Close/Open-1)/Sqrt(Volume))',
                'Tot_Stdev(Abs(Close/Open-1)/Sqrt(Volume))',
                'Tot_Mean(IfThen(Tot_Rank(Abs(Close/Open-1)/Sqrt(Volume))-0.8,Close,getNan(Close)))',
                'Tot_Mean(IfThen(Tot_Rank(Abs(Close/Open-1)+(Volume))-0.8,Close,getNan(Close)))',
                'Tot_Mean(IfThen(Tot_Rank(Abs(Close/Open-1)/Log(Volume))-0.8,Close,getNan(Close)))',
                'Tot_Sum(IfThen(Equal(Abs(Close-Open),Abs(High-Low)),Amount,getNan(Close)))',
                'Tot_Sum(IfThen(Tot_Rank(Abs(Close/Open-1)/Sqrt(Volume))-0.8,Volume,getNan(Close)))',
                'Tot_Mean(ts_Stdev(Close/ts_Delay(Close,1)-1,10))',
                'Tot_Mean(ts_Stdev(ts_Stdev(Close/ts_Delay(Close,1)-1,10),10))',
                'ts_Corr(ts_Stdev(ts_Stdev(Close/ts_Delay(Close,1)-1,10),10),Volume*Close,60)',
                'Tot_Sum(IfThen(ts_Stdev(ts_Stdev(Close/ts_Delay(Close,1)-1,10),10)-Tot_Mean(ts_Stdev(ts_Stdev(Close/ts_Delay(Close,1)-1,10),10)),Volume*Close,getNan(Close)))',
                'Tot_Sum(IfThen(Tot_Rank(Volume)-0.8,(Close-Open)/Open,getNan(Close)))',
                '(Tot_Sum(IfThen(0.2-Tot_Rank(Volume),(Close-Open)/Open,getNan(Close))))',
            ]
    names = 'p3_mf'
    forms['forms'] = p3_mf
    forms['type'] = names
    z = []
    for v in range(len(p3_mf)):
        z.append(names + str(int(v)))
    forms['fname'] = z
    Forms = pd.concat([Forms,forms],axis = 0)

    forms = pd.DataFrame(columns = ['type','forms'])
    p4_ms = [
            'Tot_Mean(Close/ts_Delay(Close,1)-1-Log(Close/ts_Delay(Close,1)))',
            'Tot_Mean(Close/ts_Delay(Close,1)-1-Log(Close/ts_Delay(Close,1))-(Log(Close/ts_Delay(Close,1)))**2/2)',
            'Tot_ArgMax(Close)',
            'Tot_ArgMin(Close)',
            'Tot_Sum(((Close-ts_Delay(Close,1))/Close)**2)',
            'Tot_Sum(((Close-ts_Delay(Close,1))/Close)**3)',
            'Tot_Sum(IfThen(Close/ts_Delay(Close,1)-1,Volume,getNan(Close)))',
            ]
    names = 'p4_ms'
    forms['forms'] = p4_ms
    forms['type'] = names
    z = []
    for v in range(len(p4_ms)):
        z.append(names + str(int(v)))
    forms['fname'] = z
    Forms = pd.concat([Forms,forms],axis = 0)

    forms = pd.DataFrame(columns = ['type','forms'])
    p5_to = [
            'Tot_Sum(IfThen(Close-ts_Delay(Close,1),Amount,-Amount))',
            'Tot_ArgMax(Volume)',
            'ts_Corr(Amount,ts_Delay(Amount,1),60)',
            'Tot_Sum(Amount)/(Tot_Sum(Abs(High-Low)/Close+Abs(Open-ts_Delay(Close,1)))*(1+Tot_Stdev(Close)/Tot_Mean(Close)))',
            'Tot_Mean(Abs(Close/ts_Delay(Close,1)-1)/Amount)',
            'Tot_Sum((High-Low)/Close)/Tot_Sum(Amount)',
            'Tot_Sum((2*(High-Low)-Abs(Open-Close))/Close)/Tot_Sum(Amount)',
            'Tot_Sum(Abs(Close/ts_Delay(Close,1)-1)/(Close*Volume))',
            ]
    names = 'p5_to'
    forms['forms'] = p5_to
    forms['type'] = names
    z = []
    for v in range(len(p5_to)):
        z.append(names + str(int(v)))
    forms['fname'] = z
    Forms = pd.concat([Forms,forms],axis = 0)

    forms = pd.DataFrame(columns = ['type','forms'])
    p6_tn = [
            'Tot_Sum(IfThen(Close-(ts_Mean(Close,30)+ts_Stdev(Close,30)),-1,IfThen((ts_Mean(Close,30)-ts_Stdev(Close,30))-Close,1,0)))',
            'Tot_Sum(High-Open)-Tot_Sum(Open-Low)',
            "ts_Regression(High,Low,60,'D')",
            'Tot_Mean(((High+Low)-ts_Delay(High+Low,1))*(High-Low)/2/Amount)',
            'Tot_Sum(IfThen(Close-ts_Delay(Close,1),1,0))',
            'Tot_Mean(High-ts_Delay(Close,1))/Tot_Sum(ts_Delay(Close,1)-Low)',
            'Tot_Mean((ts_Max(High,30)-Close)/(ts_Max(High,30)-ts_Min(Low,30)))',
            'Tot_Mean((Close-ts_Min(Low,30))/(ts_Max(High,30)-ts_Min(Low,30)))',
            'Tot_Mean((High-Low)/Close)',
            'Tot_Mean(IfThen(Tot_Rank((Close-ts_Delay(Close,1))/Close)-0.910,(Close-ts_Delay(Close,1))/Close,getNan(Close)))',
            'Tot_Mean(IfThen(Tot_Rank(0.010-(Close-ts_Delay(Close,1))/Close),(Close-ts_Delay(Close,1))/Close,getNan(Close)))',
            'ts_Corr(Close-ts_Delay(Close,1),pn_Mean(Close-ts_Delay(Close,1)),60)',
            'Tot_Sum(IfThen(Tot_Rank(High-Low)-0.10,Volume,0))-Tot_Sum(IfThen(Tot_Rank(High-Low)-0.10,0,Volume))',
            'Tot_Sum(IfThen(Close-ts_Delay(Close,1),Volume,-Volume))',
            ]
    names = 'p6_tn'
    forms['forms'] = p6_tn
    forms['type'] = names
    z = []
    for v in range(len(p6_tn)):
        z.append(names + str(int(v)))
    forms['fname'] = z
    Forms = pd.concat([Forms,forms],axis = 0)
    return Forms


def get_rawData( dataPath_future, dataPath_future_base, start_date , end_date, codes, type_ ) :
    base = 'USDT'
    frq = '1m'
    raw_data = {}
    date = []
    for v in range(len(codes)):
        print(v,codes[v])
        if type_ == 'F':
            fileName_ = codes[v] + '_' + base + '_USDT-' + frq + '-futures.feather'
        elif type_ == 'S':
            fileName_ = codes[v] + '_' + base + '-' + frq + '.feather'
        tmp = pd.read_feather(dataPath_future+fileName_)
        tmp = tmp[tmp['date'] >= start_date]
        raw_data[codes[v]] = tmp.copy()
        date_ = list(tmp['date'])
        if len(date) < len(date_):
            date = date_.copy()
    date = np.sort(date)

    Open = pd.DataFrame(np.nan, index =date, columns = codes )
    High = pd.DataFrame(np.nan, index =date, columns = codes )
    Low = pd.DataFrame(np.nan, index =date, columns = codes )
    Close = pd.DataFrame(np.nan, index =date, columns = codes )
    Volume = pd.DataFrame(np.nan, index =date, columns = codes )
    for v in range(len(codes)):
        # if v > 30 :    ## 后面需要注释掉这一行以获取更多的数据
        #     continue
        # fileName_ = codes[v] + '_' + base + '-' + frq + '.feather'
        # tmp = pd.read_feather(path+fileName_)
        print(v,codes[v], 'get in.')
        tmp = raw_data[codes[v]].copy()
        tmp.index = tmp['date']
        # print(codes[v],len(tmp))
        Open[codes[v]] = tmp['open']
        High[codes[v]] = tmp['high']
        Low[codes[v]] = tmp['low']
        Close[codes[v]] = tmp['close']
        Volume[codes[v]] = tmp['volume']
        raw_data.pop(codes[v])

    Open = Open[start_date:end_date]
    High = High[start_date:end_date]
    Low = Low[start_date:end_date]
    Close = Close[start_date:end_date]
    Volume = Volume[start_date:end_date]

    VWAP = (Open + High+Low+Close) * 4
    Amount = VWAP * Volume
    df = {}
    df['open'],df['high'],df['low'],df['close'],df['volume'], df['amount'], df['vwap']= Open ,High,Low,Close,Volume, Amount, VWAP
    return df



def get_saveFeature1(df_all,need):
    features1 = {}
    Forms = get_minFactorForms3()  # 获取特征序列公式

    # 筛选需要计算的因子
    filtered_forms = []
    filtered_fms = []
    for v in range(len(Forms)):
        fm = Forms['fname'].values[v]
        if fm in need:
            expr = Forms['forms'].values[v]
            filtered_forms.append(expr)
            filtered_fms.append(fm)

    # 定义并行计算的函数
    def calculate_factor(expr, fm):
        # start_time = time.time()
        tmp = get_minFactor(df_all['open'], df_all['high'], df_all['low'], df_all['close'],
                           df_all['volume'], df_all['amount'], df_all['vwap'], expr)
        # print(f"{fm} toc: {time.time() - start_time:.2f} s, {expr}")
        return fm, tmp

    # 使用joblib进行并行计算
    from joblib import Parallel, delayed
    import multiprocessing

    # 获取CPU核心数，留一个核心给系统使用
    n_jobs = max(1, multiprocessing.cpu_count() - 1)

    # 并行计算所有因子
    results = Parallel(n_jobs=n_jobs)(
        delayed(calculate_factor)(expr, fm) for expr, fm in zip(filtered_forms, filtered_fms)
    )

    # 整理结果
    for fm, tmp in results:
        features1[fm] = tmp

    return features1

def get_saveFeature1_cut(df_all,need,frq):
    features1 = {}
    Forms = get_minFactorForms3()  # 获取特征序列公式

    # 筛选需要计算的因子
    filtered_forms = []
    filtered_fms = []
    for v in range(len(Forms)):
        fm = Forms['fname'].values[v]
        if fm in need:
            expr = Forms['forms'].values[v]
            filtered_forms.append(expr)
            filtered_fms.append(fm)

    # 定义并行计算的函数
    def calculate_factor(expr, fm):
        # start_time = time.time()
        tmp = get_minFactor(df_all['open'], df_all['high'], df_all['low'], df_all['close'],
                           df_all['volume'], df_all['amount'], df_all['vwap'], expr)
        tmp = tmp[::frq]  # 采样
        # print(f"{fm} toc: {time.time() - start_time:.2f} s, {expr}")
        return fm, tmp

    # 使用joblib进行并行计算
    from joblib import Parallel, delayed
    import multiprocessing

    # 获取CPU核心数，留一个核心给系统使用
    n_jobs = max(1, multiprocessing.cpu_count() - 1)

    # 并行计算所有因子
    results = Parallel(n_jobs=n_jobs)(
        delayed(calculate_factor)(expr, fm) for expr, fm in zip(filtered_forms, filtered_fms)
    )

    # 整理结果
    for fm, tmp in results:
        features1[fm] = tmp

    return features1


def get_KAMA(df_all):
    # 函数名：KAMA
    # 名称： 考夫曼的自适应移动平均线
    # 简介：短期均线贴近价格走势，灵敏度高，但会有很多噪声，产生虚假信号；长期均线在判断趋势上一般比较准确 ，但是长期均线有着严重滞后的问题。我们想得到这样的均线，当价格沿一个方向快速移动时，短期的移动 平均线是最合适的；当价格在横盘的过程中，长期移动平均线是合适的。
    # NOTE: The KAMA function has an unstable period.
    # df["KAMA"] = talib.KAMA(close_p, timeperiod=30)
    # open = df_all['open']
    from  talib import KAMA
    close = df_all['close']
    tmp2 = pd.DataFrame(np.nan, index = close.index, columns = close.columns)
    for v in tmp2.columns:
        try:
            _tmp = KAMA(close[v],timeperiod=60)
            tmp2[v] = _tmp
        except:
            c = 1
    return tmp2

def get_ADOSC(df_all):
    # 函数名：ADOSC
    # 名称：Chaikin A/D Oscillator Chaikin震荡指标
    # 简介：将资金流动情况与价格行为相对比，检测市场中资金流入和流出的情况
    # 计算公式：fastperiod A/D - slowperiod A/D
    # 研判：
    # 1、交易信号是背离：看涨背离做多，看跌背离做空
    # 2、股价与90天移动平均结合，与其他指标结合
    # 3、由正变负卖出，由负变正买进
    # real = ADOSC(high, low, close, volume, fastperiod=3, slowperiod=10)
    # df["ADOSC"] = talib.ADOSC(high, low, close, volume, fastperiod=3, slowperiod=10)
    from  talib import ADOSC
    close = df_all['close']
    high =  df_all['high']
    low = df_all['low']
    volume = df_all['volume']
    tmp2 = pd.DataFrame(np.nan, index = close.index, columns = close.columns)
    for v in tmp2.columns:
        try:
            _tmp = ADOSC(high[v],low[v],close[v], volume[v],  fastperiod=30, slowperiod=60)
            tmp2[v] = _tmp
        except:
            c = 1
    return tmp2

def get_HT_DCPERIOD(df_all):
    # 名称： 希尔伯特变换-主导周期
    # 简介：将价格作为信息信号，计算价格处在的周期的位置，作为择时的依据。
    # NOTE: The HT_DCPERIOD function has an unstable period.
    # real = HT_DCPERIOD(close)
    # df["HT_DCPERIOD"] = talib.HT_DCPERIOD(close)
    from  talib import HT_DCPERIOD
    close = df_all['close']
    tmp2 = pd.DataFrame(np.nan, index = close.index, columns = close.columns)
    for v in tmp2.columns:
        try:
            _tmp = HT_DCPERIOD(close[v])
            tmp2[v] = _tmp
        except:
            c = 1
    return tmp2

def get_HT_DCPHASE(df_all):
    # 名称： 希尔伯特变换-主导循环阶段
    # NOTE: The HT_DCPHASE function has an unstable period.
    # real = HT_DCPHASE(close)
    # df["HT_DCPHASE"] = talib.HT_DCPHASE(close)
    from  talib import HT_DCPHASE
    close = df_all['close']
    tmp2 = pd.DataFrame(np.nan, index = close.index, columns = close.columns)
    for v in tmp2.columns:
        try:
            _tmp = HT_DCPHASE(close[v])
            tmp2[v] = _tmp
        except:
            c = 1
    return tmp2

def get_CCI(df_all):
    # CCI - Commodity Channel Index
    # 函数名：CCI
    # 名称：顺势指标
    # 简介：CCI指标专门测量股价是否已超出常态分布范围
    # real = CCI(high, low, close, timeperiod=14)
    # df["CCI"] = talib.CCI(high, low, close, timeperiod=14)
    from  talib import CCI
    close = df_all['close']
    high =  df_all['high']
    low = df_all['low']
    tmp2 = pd.DataFrame(np.nan, index = close.index, columns = close.columns)
    for v in tmp2.columns:
        try:
            _tmp = CCI(high[v], low[v], close[v], timeperiod=60)
            tmp2[v] = _tmp
        except:
            c = 1
    return tmp2

def get_CMO(df_all):
    # 名称：钱德动量摆动指标
    # 简介：与其他动量指标摆动指标如相对强弱指标（RSI）和随机指标（KDJ）不同，钱德动量指标在计算公式的分子中采用上涨日和下跌日的数据。 计算公式：CMO=（Su－Sd）*100/（Su+Sd）
    # 其中：Su是今日收盘价与昨日收盘价（上涨日）差值加总。若当日下跌，则增加值为0；Sd是今日收盘价与做日收盘价（下跌日）差值的绝对值加总。若当日上涨，则增加值为0；
    # 指标应用
    # 本指标类似RSI指标。
    # 当本指标下穿-50水平时是买入信号，上穿+50水平是卖出信号。
    # 钱德动量摆动指标的取值介于-100和100之间。
    # 本指标也能给出良好的背离信号。
    # 当股票价格创出新低而本指标未能创出新低时，出现牛市背离；
    # 当股票价格创出新高而本指标未能创出新高时，当出现熊市背离时。
    # 我们可以用移动均值对该指标进行平滑。
    # NOTE: The CMO function has an unstable period.
    # real = CMO(close, timeperiod=14)
    # df["CMO"] = talib.CMO(close, timeperiod=14)
    from  talib import CMO
    close = df_all['close']
    tmp2 = pd.DataFrame(np.nan, index = close.index, columns = close.columns)
    for v in tmp2.columns:
        try:
            _tmp = CMO(close[v], timeperiod=60)
            tmp2[v] = _tmp
        except:
            c = 1
    return tmp2

def get_DX(df_all):
    # DX - Directional Movement Index DMI指标又叫动向指标或趋向指标
    # 函数名：DX
    # 名称：动向指标或趋向指标
    # 简介：通过分析股票价格在涨跌过程中买卖双方力量均衡点的变化情况，即多空双方的力量的变化受价格波动的影响而发生由均衡到失衡的循环过程，从而提供对趋势判断依据的一种技术指标。
    # 分析和应用：百度百科 维基百科 同花顺学院
    # NOTE: The DX function has an unstable period.
    # real = DX(high, low, close, timeperiod=14)
    # df["DX"] = talib.DX(high, low, close, timeperiod=14)
    from  talib import DX
    close = df_all['close']
    high =  df_all['high']
    low = df_all['low']
    tmp2 = pd.DataFrame(np.nan, index = close.index, columns = close.columns)
    for v in tmp2.columns:
        try:
            _tmp = DX(high[v], low[v], close[v], timeperiod=60)
            tmp2[v] = _tmp
        except:
            c = 1
    return tmp2

def get_MINUS_DI(df_all):
    # MINUS_DI - Minus Directional Indicator
    # 函数名：DMI 中的DI指标 负方向指标
    # 名称：下升动向值
    # 简介：通过分析股票价格在涨跌过程中买卖双方力量均衡点的变化情况，即多空双方的力量的变化受价格波动的影响而发生由均衡到失衡的循环过程，从而提供对趋势判断依据的一种技术指标。
    # real = MINUS_DI(high, low, close, timeperiod=14)
    # df["MINUS_DI"] = talib.MINUS_DI(high, low, close, timeperiod=14)
    from  talib import MINUS_DI
    close = df_all['close']
    high =  df_all['high']
    low = df_all['low']
    tmp2 = pd.DataFrame(np.nan, index = close.index, columns = close.columns)
    for v in tmp2.columns:
        try:
            _tmp = MINUS_DI(high[v], low[v], close[v], timeperiod=60)
            tmp2[v] = _tmp
        except:
            c = 1
    return tmp2

def get_MINUS_DM(df_all):
    # MINUS_DM - Minus Directional Movement
    # 函数名：MINUS_DM
    # 名称： 上升动向值 DMI中的DM代表正趋向变动值即上升动向值
    # 简介：通过分析股票价格在涨跌过程中买卖双方力量均衡点的变化情况，即多空双方的力量的变化受价格波动的影响而发生由均衡到失衡的循环过程，从而提供对趋势判断依据的一种技术指标。
    # 分析和应用：百度百科 维基百科 同花顺学院
    # NOTE: The MINUS_DM function has an unstable period.
    # real = MINUS_DM(high, low, timeperiod=14)
    # df["MINUS_DM"] = talib.MINUS_DM(high, low, timeperiod=14)
    from  talib import MINUS_DM
    close = df_all['close']
    high =  df_all['high']
    low = df_all['low']
    tmp2 = pd.DataFrame(np.nan, index = close.index, columns = close.columns)
    for v in tmp2.columns:
        try:
            _tmp = MINUS_DM(high[v], low[v], timeperiod=60)
            tmp2[v] = _tmp
        except:
            c = 1
    return tmp2

def get_ULTOSC(df_all):
    # ULTOSC - Ultimate Oscillator 终极波动指标
    # 函数名：ULTOSC
    # 名称：终极波动指标
    # 简介：UOS是一种多方位功能的指标，除了趋势确认及超买超卖方面的作用之外，它的“突破”讯号不仅可以提供最适当的交易时机之外，更可以进一步加强指标的可靠度。
    # 分析和应用：百度百科 同花顺学院
    # real = ULTOSC(high, low, close, timeperiod1=7, timeperiod2=14, timeperiod3=28)
    # df["ULTOSC"] = talib.ULTOSC(high, low, close, timeperiod1=7, timeperiod2=14, timeperiod3=28)
    from  talib import ULTOSC
    close = df_all['close']
    high =  df_all['high']
    low = df_all['low']
    tmp2 = pd.DataFrame(np.nan, index = close.index, columns = close.columns)
    for v in tmp2.columns:
        try:
            _tmp = ULTOSC(high[v], low[v], close[v], timeperiod1=20, timeperiod2=40, timeperiod3=60)
            tmp2[v] = _tmp
        except:
            c = 1
    return tmp2

def get_LINEARREG_ANGLE(df_all):
    # LINEARREG_ANGLE - Linear Regression Angle
    # 函数名：LINEARREG_ANGLE
    # 名称：线性回归的角度
    # 简介：来确定价格的角度变化. 参考
    # real = LINEARREG_ANGLE(close, timeperiod=14)
    # df["LINEARREG_ANGLE"] = talib.LINEARREG_ANGLE(close, timeperiod=14)
    from  talib import LINEARREG_ANGLE
    close = df_all['close']
    tmp2 = pd.DataFrame(np.nan, index = close.index, columns = close.columns)
    for v in tmp2.columns:
        try:
            _tmp = LINEARREG_ANGLE(close[v], timeperiod=60)
            tmp2[v] = _tmp
        except:
            c = 1
    return tmp2

def get_LINEARREG_SLOPE(df_all):
    # LINEARREG_SLOPE - Linear Regression Slope
    # 函数名：LINEARREG_SLOPE
    # 名称：线性回归斜率指标
    # real = LINEARREG_SLOPE(close, timeperiod=14)
    # df["LINEARREG_SLOPE"] = talib.LINEARREG_SLOPE(close, timeperiod=14)
    from talib import LINEARREG_SLOPE
    close = df_all['close']
    high =  df_all['high']
    low = df_all['low']
    volume = df_all['volume']
    tmp2 = pd.DataFrame(np.nan, index = close.index, columns = close.columns)
    for v in tmp2.columns:
        try:
            _tmp = LINEARREG_SLOPE(close[v], timeperiod=60)
            tmp2[v] = _tmp
        except:
            c = 1
    return tmp2


def get_talib_factor(df_all, need):
    features2 = {}

    # 定义所有可能的talib因子及其对应的计算函数
    talib_factors = {
        'kama': get_KAMA,
        'adosc': get_ADOSC,
        'dcperiod': get_HT_DCPERIOD,
        'dcphase': get_HT_DCPHASE,
        'cci': get_CCI,
        'cmo': get_CMO,
        'dx': get_DX,
        'di': get_MINUS_DI,
        'dm': get_MINUS_DM,
        'ultosc': get_ULTOSC,
        'liangle': get_LINEARREG_ANGLE,
        'lislope': get_LINEARREG_SLOPE
    }

    # 筛选需要计算的因子
    factors_to_calculate = {factor: func for factor, func in talib_factors.items() if factor in need}

    if not factors_to_calculate:
        return features2

    # 定义并行计算的函数
    def calculate_talib_factor(factor_name, factor_func):
        # start_time = time.time()
        result = factor_func(df_all)
        # print(f"{factor_name} toc: {time.time() - start_time:.2f} s")
        return factor_name, result

    # 使用joblib进行并行计算
    from joblib import Parallel, delayed
    import multiprocessing

    # 获取CPU核心数，留一个核心给系统使用
    n_jobs = max(1, multiprocessing.cpu_count() - 1)

    # 并行计算所有因子
    results = Parallel(n_jobs=n_jobs)(
        delayed(calculate_talib_factor)(factor, func)
        for factor, func in factors_to_calculate.items()
    )

    # 整理结果
    for factor_name, result in results:
        features2[factor_name] = result

    return features2


def get_fs_mix(dataPath_future, dataPath_stock):
    base = 'USDT'
    frq = '1m'
    # 获取S货币名称序列
    fileNames = os.listdir(dataPath_stock)
    codes_S = []
    for v in fileNames:
        t = list( v.split('_'))
        if t == ['futures']:
            continue
        base_ = t[1].split('-')[0]
        freq_ = t[1].split('-')[1].split('.')[0]
        if base_ == base and freq_ == frq:
            codes_S.append(t[0])
    print(len(codes_S))

    # 获取F货币名称序列
    fileNames = os.listdir(dataPath_future)
    codes_F = []
    for v in fileNames:
        t = list( v.split('_'))
        base_ = t[1].split('-')[0]
        freq_ = t[2].split('-')[1].split('.')[0]
        if base_ == base and freq_ == frq:
            codes_F.append(t[0])
    print(len(codes_F))

    #  交集
    union_set = set(codes_S) & set(codes_F)
    union_list = list(union_set)
    print(len(union_list))
    return union_list




def get_data_feature_fast(df_all_future, need):
    # 计算feature：使用并行处理
    # 原始的feature

    # 得到一共有哪些feature
    features_names1 = get_minFactorForms3()
    features_names1 = list(features_names1['fname'])
    features_names2 = [  'kama','adosc','dcperiod','dcphase','cci',  'cmo','dx','di','dm','ultosc','liangle','lislope']


    # 计算并导出
    F_feature = {}

    # 自定义因子
    if not set(need).isdisjoint(set(features_names1)):
        # 填充数据以避免在计算中出现问题
        df_copy = df_all_future.copy()
        features1 = get_saveFeature1(df_copy, need)
        for v in features1.keys():
            F_feature[v] = features1[v]

    # 调用talib的feature
    if not set(need).isdisjoint(set(features_names2)):
        # 填充数据以避免在计算中出现问题
        df_copy = df_all_future.copy()
        for v in df_copy.keys():
            df_copy[v] = df_copy[v].fillna(0)
        features2 = get_talib_factor(df_copy, need)

        for v in features2.keys():
            F_feature[v] = features2[v]

    return F_feature


def get_data_feature_fast_sample(df_all_future, need, frq):
    # 计算feature：使用并行处理
    # 原始的feature

    # 得到一共有哪些feature
    features_names1 = get_minFactorForms3()
    features_names1 = list(features_names1['fname'])
    features_names2 = [  'kama','adosc','dcperiod','dcphase','cci',  'cmo','dx','di','dm','ultosc','liangle','lislope']


    # 计算并导出
    F_feature = {}

    # 自定义因子
    if not set(need).isdisjoint(set(features_names1)):
        # 填充数据以避免在计算中出现问题
        df_copy = df_all_future.copy()
        features1 = get_saveFeature1_cut(df_copy, need, frq)
        for v in features1.keys():
            F_feature[v] = features1[v]

    # 调用talib的feature
    if not set(need).isdisjoint(set(features_names2)):
        # 填充数据以避免在计算中出现问题
        df_copy = df_all_future.copy()
        for v in df_copy.keys():
            df_copy[v] = df_copy[v].fillna(0)
        features2 = get_talib_factor(df_copy, need)

        for v in features2.keys():
            F_feature[v] = features2[v][::frq]

    return F_feature


def get_data_feature_fast_fromExpr(df_all_future, expr):
    # 计算feature：使用并行处理
    # 原始的feature

    # 得到一共有哪些feature
    features_names1 = get_minFactorForms3()
    features_names1 = list(features_names1['fname'])
    features_names2 = [  'kama','adosc','dcperiod','dcphase','cci',  'cmo','dx','di','dm','ultosc','liangle','lislope']
    features_names = features_names1 + features_names2

    # 判断需要哪些featrue
    need = []
    for v in features_names:
        for vv in expr:
            if v in vv:
               need.append(v)
               break
    print('feature need: len',len(need))
    # print(need)

    # 计算并导出
    F_feature = {}

    # 计算自定义因子和talib因子
    # 自定义因子
    if not set(need).isdisjoint(set(features_names1)):
        # 填充数据以避免在计算中出现问题
        df_copy = df_all_future.copy()
        features1 = get_saveFeature1(df_copy, need)
        for v in features1.keys():
            F_feature[v] = features1[v]

    # talib因子
    if not set(need).isdisjoint(set(features_names2)):
        # 填充数据以避免在计算中出现问题
        df_copy = df_all_future.copy()
        for v in df_copy.keys():
            df_copy[v] = df_copy[v].fillna(0)
        features2 = get_talib_factor(df_copy, need)
        for v in features2.keys():
            F_feature[v] = features2[v]

    return F_feature


if __name__=="__main__":
    get_data_feature_fast()


