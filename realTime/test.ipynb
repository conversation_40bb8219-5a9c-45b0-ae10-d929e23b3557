{"cells": [{"cell_type": "code", "execution_count": 1, "id": "1348c1da", "metadata": {}, "outputs": [], "source": ["import pandas as pd"]}, {"cell_type": "code", "execution_count": 2, "id": "cd040ad7", "metadata": {}, "outputs": [], "source": ["home_dir = \"/home/<USER>/git/\"\n", "opt_config =  {'factorNum': 100, 'lb': 3 }"]}, {"cell_type": "code", "execution_count": 3, "id": "26a7ddc3", "metadata": {}, "outputs": [], "source": ["expr = pd.read_csv(home_dir + \"/realTime/Expr/result_1m10m2h_0508.csv\")\n", "expr = list(expr.sort_values(by = 'results_is',ascending=False).head(opt_config['factorNum'])['expr'])\n", "expr.append(\"pn_GroupNeutral(ts_Delta(df['p6_tn6'],9),<PERSON>(df['p5_to2'],ts_<PERSON>(df['p2_et7'],11)))\")"]}, {"cell_type": "code", "execution_count": 4, "id": "1e7eee32", "metadata": {}, "outputs": [], "source": ["def get_minFactorForms3():\n", "    Forms = pd.DataFrame(columns = ['type','forms'])\n", "    forms = pd.DataFrame(columns = ['type','forms'])\n", "    p1_corrs = [\n", "                'ts_<PERSON>rr(Close,Volume,60)',\n", "                'ts_<PERSON><PERSON>(Close/ts_Delay(Close,1)-1,Volume,60)',\n", "                'ts_<PERSON><PERSON>(ts_<PERSON>ay(Close,1),Volume,60)',\n", "                'ts_<PERSON><PERSON>(Close,ts_<PERSON>ay(Volume,1),60)',\n", "                'ts_<PERSON><PERSON>(ts_Delay(Close/ts_Delay(Close,1)-1,1),Close/ts_Delay(Close,1)-1,60)',\n", "                'ts_<PERSON><PERSON>(ts_Delay(Close/ts_Delay(Close,1)-1,1),Close,60)',\n", "                'ts_<PERSON><PERSON>(Volume,Volume-ts_Delay(Volume,1),60)',\n", "                'ts_<PERSON><PERSON>(Volume-ts_Delay(Volume,1), Volume-ts_Delay(Volume,1) - ts_Delay(Volume-ts_Delay(Volume,1),1),60)',\n", "                'ts_<PERSON>rr(VWAP,Volume,60)',\n", "                'ts_Corr(VWAP/ts_Delay(VWAP,1)-1,Volume,60)',\n", "                'ts_<PERSON>rr(ts_Delay(VWAP,1),Volume,60)',\n", "                'ts_<PERSON><PERSON>(VWAP,ts_Delay(Volume,1),60)',\n", "                'ts_<PERSON>rr(ts_Delay(VWAP/ts_Delay(VWAP,1)-1,1),VWAP/ts_Delay(VWAP,1)-1,60)',\n", "                'ts_<PERSON>rr(ts_Delay(VWAP/ts_Delay(VWAP,1)-1,1),VWA<PERSON>,60)',\n", "                'ts_<PERSON><PERSON>(Volume,Volume-ts_Delay(Volume,1),60)',\n", "                'ts_<PERSON><PERSON>(Volume-ts_Delay(Volume,1), Volume-ts_Delay(Volume,1) - ts_Delay(Volume-ts_Delay(Volume,1),1),60)',\n", "                ]\n", "    names = 'p1_corrs'\n", "    forms['forms'] = p1_corrs\n", "    forms['type'] = names\n", "    z = []\n", "    for v in range(len(p1_corrs)):\n", "        z.append(names + str(int(v)))\n", "    forms['fname'] = z\n", "    Forms = pd.concat([Forms,forms],axis = 0)\n", "    \n", "    forms = pd.DataFrame(columns = ['type','forms'])\n", "    p2_et = [\n", "            'Tot_Mean(IfThen(IfThen(Volume-ts_Delay(Volume,1)-Tot_<PERSON>(Volume-ts_Delay(Volume,1))-Tot_Stdev(Volume-ts_Delay(Volume,1)),1,0),Close/ts_Delay(Close,1)-1,get<PERSON><PERSON>(Close/ts_Delay(Close,1)-1)))',\n", "            'Tot_<PERSON>(IfThen(ts_Sum(IfThen(Tot_Rank(Volume-ts_Delay(Volume,1))-0.9,1,0),10),Close/ts_Delay(Close,1)-1,get<PERSON><PERSON>(Close/ts_Delay(Close,1)-1)))',\n", "            'Tot_Stdev(IfThen(ts_Sum(IfThen(Tot_Rank(Close/ts_Delay(Close,1)-1)-0.9,1,0),10),Close/ts_Delay(Close,1)-1,get<PERSON><PERSON>(Close/ts_Delay(Close,1)-1)))',\n", "            'Tot_<PERSON>(IfThen(IfThen(Close/ts_Delay(Close,1)-1-<PERSON><PERSON>_<PERSON>(Close/ts_Delay(Close,1)-1)-To<PERSON>_Stdev(Close/ts_Delay(Close,1)-1),1,0),Close/ts_Delay(Close,1)-1,get<PERSON><PERSON>(Close/ts_Delay(Close,1)-1)))',\n", "            'Tot_<PERSON>(IfThen(ts_Sum(IfThen(Tot_Rank(Close/ts_Delay(Close,1)-1)-0.9,1,0),10),Close/ts_Delay(Close,1)-1,get<PERSON><PERSON>(Close/ts_Delay(Close,1)-1)))',\n", "            'ts_Stdev(Close/ts_Delay(Close,1)-1,60)-pn_Mean(ts_Stdev(Close/ts_Delay(Close,1)-1,60))',\n", "            'Abs(ts_Stdev(Close/ts_Delay(Close,1)-1,60)-pn_Mean(ts_Stdev(Close/ts_Delay(Close,1)-1,60)))',\n", "            'ts_Mean(Close/ts_Delay(Close,1)-1,60)-pn_Mean(ts_Mean(Close/ts_Delay(Close,1)-1,60))',\n", "            'Abs(ts_Mean(Close/ts_Delay(Close,1)-1,60)-pn_Mean(ts_Mean(Close/ts_Delay(Close,1)-1,60)))',\n", "            'ts_Mean((Close/ts_Delay(Close,1)-1)*IfThen(Volume-ts_Mean(Volume,30)-1.210*ts_Stdev(Volume,30),1,0),30)',\n", "            'ts_Mean(IfThen((Close/ts_Delay(Close,1)-1),(Close/ts_Delay(Close,1)-1),0)*IfThen(Volume-ts_Mean(Volume,30)-0.10*ts_Stdev(Volume,30),1,0),30)',\n", "            'To<PERSON>_<PERSON><PERSON>(IfThen(ts_<PERSON>ay(ts_<PERSON>(Low,30),1)-Low,1,0))',\n", "            'Tot_Stdev(Abs(Close/ts_Delay(Close,1)-1-pn_Mean(Close/ts_Delay(Close,1)-1)))',\n", "            'Tot_Stdev(pn_Rank(Close/ts_Delay(Close,1)-1))',\n", "            'Tot_Stdev(pn_Rank(Volume))',\n", "            'Tot_Mean(Abs(Close/ts_Delay(Close,1)-1-pn_Mean(Close/ts_Delay(Close,1)-1))/(Abs(pn_Mean(Close/ts_Delay(Close,1)-1))+Abs(Close/ts_Delay(Close,1)-1)+0.1))',\n", "            'Tot_Mean(IfThen(-(Close/ts_Delay(Close,1)-1)+(Tot_Mean(Close/ts_Delay(Close,1)-1)-Tot_Stdev(Close/ts_Delay(Close,1)-1)),Close/ts_Delay(Close,1)-1,get<PERSON><PERSON>(Close)))',\n", "            'Tot_<PERSON>(IfThen(Tot_Rank(Close/ts_Delay(Close,1)-1)-0.93,Close/ts_Delay(Close,1)-1,get<PERSON><PERSON>(Close)))',\n", "            'Tot_<PERSON>(IfThen(0.07-Tot_Rank(Close/ts_Delay(Close,1)-1),Close/ts_Delay(Close,1)-1,get<PERSON><PERSON>(Close)))',\n", "            'To<PERSON>_<PERSON><PERSON>(IfThen(Equal(Close/ts_Delay(Close,1)-1,0),1,get<PERSON><PERSON>(Close)))',\n", "        ]\n", "    names = 'p2_et'\n", "    forms['forms'] = p2_et\n", "    forms['type'] = names  \n", "    z = []\n", "    for v in range(len(p2_et)):\n", "        z.append(names + str(int(v)))\n", "    forms['fname'] = z\n", "    Forms = pd.concat([Forms,forms],axis = 0)\n", "    \n", "    forms = pd.DataFrame(columns = ['type','forms'])\n", "    p3_mf = [\n", "                'Tot_Mean(Abs(Close/Open-1)/Sqrt(Volume))',\n", "                'Tot_Stdev(Abs(Close/Open-1)/Sqrt(Volume))',\n", "                'Tot_Mean(IfThen(Tot_Rank(Abs(Close/Open-1)/Sqrt(Volume))-0.8,Close,get<PERSON><PERSON>(Close)))',\n", "                'Tot_Mean(IfThen(Tot_Rank(Abs(Close/Open-1)+(Volume))-0.8,Close,get<PERSON><PERSON>(Close)))',\n", "                'Tot_Mean(IfThen(Tot_Rank(Abs(Close/Open-1)/Log(Volume))-0.8,Close,get<PERSON><PERSON>(Close)))',\n", "                'To<PERSON>_<PERSON><PERSON>(If<PERSON>hen(Equal(Abs(Close-Open),A<PERSON>(High-Low)),Amount,get<PERSON><PERSON>(Close)))',\n", "                'Tot_Sum(IfThen(Tot_Rank(Abs(Close/Open-1)/Sqrt(Volume))-0.8,Volume,get<PERSON>an(Close)))',\n", "                'Tot_Mean(ts_Stdev(Close/ts_Delay(Close,1)-1,10))',\n", "                'Tot_<PERSON>(ts_Stdev(ts_Stdev(Close/ts_Delay(Close,1)-1,10),10))',\n", "                'ts_<PERSON><PERSON>(ts_Stdev(ts_Stdev(Close/ts_Delay(Close,1)-1,10),10),Volume*Close,60)',\n", "                'To<PERSON>_<PERSON><PERSON>(IfThen(ts_Stdev(ts_Stdev(Close/ts_Delay(Close,1)-1,10),10)-<PERSON><PERSON>_<PERSON>(ts_Stdev(ts_Stdev(Close/ts_Delay(Close,1)-1,10),10)),Volume*Close,get<PERSON><PERSON>(Close)))',\n", "                'Tot_Sum(IfThen(Tot_Rank(Volume)-0.8,(Close-Open)/Open,get<PERSON>an(Close)))',\n", "                '(<PERSON><PERSON>_<PERSON>m(IfThen(0.2-Tot_Rank(Volume),(Close-Open)/Open,get<PERSON>an(Close))))',\n", "            ]\n", "    names = 'p3_mf'\n", "    forms['forms'] = p3_mf\n", "    forms['type'] = names\n", "    z = []\n", "    for v in range(len(p3_mf)):\n", "        z.append(names + str(int(v)))\n", "    forms['fname'] = z\n", "    Forms = pd.concat([Forms,forms],axis = 0)\n", "    \n", "    forms = pd.DataFrame(columns = ['type','forms'])\n", "    p4_ms = [\n", "            'Tot_<PERSON>(Close/ts_Delay(Close,1)-1-Log(Close/ts_Delay(Close,1)))',\n", "            'Tot_Mean(Close/ts_Delay(Close,1)-1-Log(Close/ts_Delay(Close,1))-(Log(Close/ts_Delay(Close,1)))**2/2)',\n", "            'Tot_ArgMax(Close)',\n", "            '<PERSON><PERSON>_<PERSON><PERSON><PERSON><PERSON>(Close)',\n", "            'Tot_Sum(((Close-ts_Delay(Close,1))/Close)**2)',\n", "            'Tot_Sum(((Close-ts_Delay(Close,1))/Close)**3)',\n", "            'To<PERSON>_<PERSON><PERSON>(IfThen(Close/ts_Delay(Close,1)-1,Volume,get<PERSON><PERSON>(Close)))',\n", "            ]\n", "    names = 'p4_ms'\n", "    forms['forms'] = p4_ms\n", "    forms['type'] = names \n", "    z = []\n", "    for v in range(len(p4_ms)):\n", "        z.append(names + str(int(v)))\n", "    forms['fname'] = z\n", "    Forms = pd.concat([Forms,forms],axis = 0)\n", "    \n", "    forms = pd.DataFrame(columns = ['type','forms'])\n", "    p5_to = [\n", "            'To<PERSON>_<PERSON><PERSON>(If<PERSON>hen(Close-ts_<PERSON>ay(Close,1),Amount,-Amount))',\n", "            'Tot_ArgMax(Volume)',\n", "            'ts_<PERSON><PERSON>(Amount,ts_<PERSON><PERSON>(Amount,1),60)',\n", "            'Tot_Sum(Amount)/(Tot_Sum(Abs(High-Low)/Close+Abs(Open-ts_Delay(Close,1)))*(1+Tot_Stdev(Close)/Tot_Mean(Close)))',\n", "            'Tot_Mean(Abs(Close/ts_Delay(Close,1)-1)/Amount)',\n", "            'Tot_Sum((High-Low)/Close)/Tot_Sum(Amount)',\n", "            'Tot_Sum((2*(High-Low)-Abs(Open-Close))/Close)/Tot_Sum(Amount)',\n", "            'Tot_Sum(Abs(Close/ts_Delay(Close,1)-1)/(Close*Volume))',\n", "            ]\n", "    names = 'p5_to'\n", "    forms['forms'] = p5_to\n", "    forms['type'] = names\n", "    z = []\n", "    for v in range(len(p5_to)):\n", "        z.append(names + str(int(v)))\n", "    forms['fname'] = z\n", "    Forms = pd.concat([Forms,forms],axis = 0)\n", "    \n", "    forms = pd.DataFrame(columns = ['type','forms'])\n", "    p6_tn = [\n", "            'Tot_<PERSON>m(IfThen(Close-(ts_Mean(Close,30)+ts_Stdev(Close,30)),-1,IfThen((ts_Mean(Close,30)-ts_Stdev(Close,30))-Close,1,0)))',\n", "            '<PERSON><PERSON>_<PERSON><PERSON>(High-Open)-<PERSON><PERSON>_<PERSON><PERSON>(Open-Low)',\n", "            \"ts_Regression(High,Low,60,'D')\",\n", "            'Tot_Mean(((High+Low)-ts_Delay(High+Low,1))*(High-Low)/2/Amount)',\n", "            'Tot_<PERSON>m(IfThen(Close-ts_Delay(Close,1),1,0))',\n", "            'Tot_<PERSON>(High-ts_Delay(Close,1))/Tot_Sum(ts_Delay(Close,1)-Low)',\n", "            'Tot_Mean((ts_<PERSON>(High,30)-Close)/(ts_<PERSON>(High,30)-ts_<PERSON>(Low,30)))',\n", "            'Tot_Mean((Close-ts_Min(Low,30))/(ts_<PERSON>(High,30)-ts_<PERSON>(Low,30)))',\n", "            'Tot_Mean((High-Low)/Close)',\n", "            'Tot_Mean(IfThen(Tot_Rank((Close-ts_Delay(Close,1))/Close)-0.910,(Close-ts_Delay(Close,1))/Close,get<PERSON><PERSON>(Close)))',\n", "            'Tot_Mean(IfThen(Tot_Rank(0.010-(Close-ts_Delay(Close,1))/Close),(Close-ts_Delay(Close,1))/Close,get<PERSON><PERSON>(Close)))',\n", "            'ts_<PERSON><PERSON>(Close-ts_Delay(Close,1),pn_<PERSON>(Close-ts_Delay(Close,1)),60)',\n", "            'Tot_Sum(IfThen(Tot_Rank(High-Low)-0.10,Volume,0))-Tot_Sum(IfThen(Tot_Rank(High-Low)-0.10,0,Volume))',\n", "            'To<PERSON>_<PERSON><PERSON>(If<PERSON><PERSON>(Close-ts_Delay(Close,1),Volume,-Volume))',\n", "            ]\n", "    names = 'p6_tn'\n", "    forms['forms'] = p6_tn\n", "    forms['type'] = names  \n", "    z = []\n", "    for v in range(len(p6_tn)):\n", "        z.append(names + str(int(v)))\n", "    forms['fname'] = z\n", "    Forms = pd.concat([Forms,forms],axis = 0)\n", "    return Forms"]}, {"cell_type": "code", "execution_count": 5, "id": "b033fbc6", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["feature need: len 71\n"]}], "source": ["features_names1 = get_minFactorForms3()\n", "features_names1 = list(features_names1['fname'])\n", "features_names2 = [  'kama','adosc','dcperiod','dcphase','cci',  'cmo','dx','di','dm','ultosc','liangle','lislope']\n", "features_names = features_names1 + features_names2\n", "\n", "# 判断需要哪些featrue\n", "need = []\n", "for v in features_names:\n", "    for vv in expr:\n", "        if v in vv:\n", "            need.append(v) \n", "            break\n", "print('feature need: len',len(need))"]}, {"cell_type": "code", "execution_count": null, "id": "d4ce84d7", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["p1_corrs0 ts_Corr(Close,Volume,60)\n", "p1_corrs1 ts_Corr(Close/ts_Delay(Close,1)-1,Volume,60)\n", "p1_corrs2 ts_Corr(ts_Delay(Close,1),Volume,60)\n", "p1_corrs3 ts_Corr(Close,ts_Delay(Volume,1),60)\n", "p1_corrs5 ts_Corr(ts_Delay(Close/ts_Delay(Close,1)-1,1),Close,60)\n", "p1_corrs7 ts_Corr(Volume-ts_Delay(Volume,1), Volume-ts_Delay(Volume,1) - ts_Delay(Volume-ts_Delay(Volume,1),1),60)\n", "p1_corrs8 ts_Corr(VWAP,Volume,60)\n", "p2_et0 Tot_Mean(IfThen(IfThen(Volume-ts_Delay(Volume,1)-Tot_Mean(Volume-ts_Delay(Volume,1))-Tot_Stdev(Volume-ts_Delay(Volume,1)),1,0),Close/ts_Delay(Close,1)-1,get<PERSON>an(Close/ts_Delay(Close,1)-1)))\n", "p2_et1 Tot_Mean(IfThen(ts_Sum(IfThen(Tot_Rank(Volume-ts_Delay(Volume,1))-0.9,1,0),10),Close/ts_Delay(Close,1)-1,get<PERSON><PERSON>(Close/ts_Delay(Close,1)-1)))\n", "p2_et2 Tot_Stdev(IfThen(ts_Sum(IfThen(Tot_Rank(Close/ts_Delay(Close,1)-1)-0.9,1,0),10),Close/ts_Delay(Close,1)-1,get<PERSON><PERSON>(Close/ts_Delay(Close,1)-1)))\n", "p2_et4 Tot_Mean(IfThen(ts_Sum(IfThen(Tot_Rank(Close/ts_Delay(Close,1)-1)-0.9,1,0),10),Close/ts_Delay(Close,1)-1,get<PERSON><PERSON>(Close/ts_Delay(Close,1)-1)))\n", "p2_et5 ts_Stdev(Close/ts_Delay(Close,1)-1,60)-pn_Mean(ts_Stdev(Close/ts_Delay(Close,1)-1,60))\n", "p2_et7 ts_Mean(Close/ts_Delay(Close,1)-1,60)-pn_Mean(ts_Mean(Close/ts_Delay(Close,1)-1,60))\n", "p2_et8 Abs(ts_Mean(Close/ts_Delay(Close,1)-1,60)-pn_Mean(ts_Mean(Close/ts_Delay(Close,1)-1,60)))\n", "p2_et9 ts_Mean((Close/ts_Delay(Close,1)-1)*IfThen(Volume-ts_Mean(Volume,30)-1.210*ts_Stdev(Volume,30),1,0),30)\n", "p2_et10 ts_Mean(IfThen((Close/ts_Delay(Close,1)-1),(Close/ts_Delay(Close,1)-1),0)*IfThen(Volume-ts_Mean(Volume,30)-0.10*ts_Stdev(Volume,30),1,0),30)\n", "p2_et11 <PERSON><PERSON>_<PERSON>m(IfThen(ts_Delay(ts_<PERSON>(Low,30),1)-Low,1,0))\n", "p2_et12 Tot_Stdev(Abs(Close/ts_Delay(Close,1)-1-pn_Mean(Close/ts_Delay(Close,1)-1)))\n", "p2_et13 Tot_Stdev(pn_Rank(Close/ts_Delay(Close,1)-1))\n", "p2_et14 Tot_Stdev(pn_Rank(Volume))\n", "p2_et15 <PERSON>t_Mean(Abs(Close/ts_Delay(Close,1)-1-pn_Mean(Close/ts_Delay(Close,1)-1))/(Abs(pn_Mean(Close/ts_Delay(Close,1)-1))+Abs(Close/ts_Delay(Close,1)-1)+0.1))\n", "p2_et16 <PERSON><PERSON>_<PERSON>(IfThen(-(Close/ts_Delay(Close,1)-1)+(Tot_Mean(Close/ts_Delay(Close,1)-1)-Tot_Stdev(Close/ts_Delay(Close,1)-1)),Close/ts_Delay(Close,1)-1,get<PERSON><PERSON>(Close)))\n", "p2_et17 Tot_<PERSON>(IfThen(Tot_Rank(Close/ts_Delay(Close,1)-1)-0.93,Close/ts_Delay(Close,1)-1,get<PERSON><PERSON>(Close)))\n", "p2_et18 <PERSON><PERSON>_<PERSON>(IfThen(0.07-Tot_Rank(Close/ts_Delay(Close,1)-1),Close/ts_Delay(Close,1)-1,get<PERSON><PERSON>(Close)))\n", "p3_mf0 Tot_Mean(Abs(Close/Open-1)/Sqrt(Volume))\n", "p3_mf1 Tot_Stdev(Abs(Close/Open-1)/Sqrt(Volume))\n", "p3_mf3 Tot_Mean(IfThen(Tot_Rank(Abs(Close/Open-1)+(Volume))-0.8,Close,get<PERSON><PERSON>(Close)))\n", "p3_mf4 Tot_Mean(IfThen(Tot_Rank(Abs(Close/Open-1)/Log(Volume))-0.8,Close,get<PERSON><PERSON>(Close)))\n", "p3_mf5 Tot_Sum(If<PERSON><PERSON>(Equal(Abs(Close-Open),A<PERSON>(High-Low)),Amount,get<PERSON><PERSON>(Close)))\n", "p3_mf7 Tot_Mean(ts_Stdev(Close/ts_Delay(Close,1)-1,10))\n", "p3_mf8 Tot_Mean(ts_Stdev(ts_Stdev(Close/ts_Delay(Close,1)-1,10),10))\n", "p3_mf9 ts_Corr(ts_Stdev(ts_Stdev(Close/ts_Delay(Close,1)-1,10),10),Volume*Close,60)\n", "p3_mf11 Tot_Sum(IfThen(Tot_Rank(Volume)-0.8,(Close-Open)/Open,getNan(Close)))\n", "p3_mf12 (Tot_Sum(IfThen(0.2-Tot_Rank(Volume),(Close-Open)/Open,get<PERSON>an(Close))))\n", "p4_ms0 Tot_Mean(Close/ts_Delay(Close,1)-1-Log(Close/ts_Delay(Close,1)))\n", "p4_ms1 Tot_<PERSON>(Close/ts_Delay(Close,1)-1-Log(Close/ts_Delay(Close,1))-(Log(Close/ts_Delay(Close,1)))**2/2)\n", "p4_ms2 Tot_ArgMax(Close)\n", "p4_ms3 Tot_ArgMin(Close)\n", "p4_ms4 Tot_Sum(((Close-ts_Delay(Close,1))/Close)**2)\n", "p4_ms5 Tot_Sum(((Close-ts_Delay(Close,1))/Close)**3)\n", "p4_ms6 Tot_Sum(IfThen(Close/ts_Delay(Close,1)-1,Volume,get<PERSON>an(Close)))\n", "p5_to0 Tot_Sum(IfThen(Close-ts_Delay(Close,1),Amount,-Amount))\n", "p5_to1 Tot_ArgMax(Volume)\n", "p5_to2 ts_<PERSON>rr(Amount,ts_<PERSON><PERSON>(Amount,1),60)\n", "p5_to3 Tot_Sum(Amount)/(Tot_Sum(Abs(High-Low)/Close+Abs(Open-ts_Delay(Close,1)))*(1+Tot_Stdev(Close)/Tot_Mean(Close)))\n", "p5_to4 Tot_Mean(Abs(Close/ts_Delay(Close,1)-1)/Amount)\n", "p5_to5 Tot_Sum((High-Low)/Close)/Tot_Sum(Amount)\n", "p5_to6 Tot_Sum((2*(High-Low)-Abs(Open-Close))/Close)/Tot_Sum(Amount)\n", "p5_to7 Tot_Sum(Abs(Close/ts_Delay(Close,1)-1)/(Close*Volume))\n", "p6_tn0 Tot_Sum(IfThen(Close-(ts_Mean(Close,30)+ts_Stdev(Close,30)),-1,IfThen((ts_Mean(Close,30)-ts_Stdev(Close,30))-Close,1,0)))\n", "p6_tn1 <PERSON><PERSON>_<PERSON>m(High-Open)-<PERSON><PERSON>_<PERSON><PERSON>(Open-Low)\n", "p6_tn2 ts_Regression(High,Low,60,'D')\n", "p6_tn4 Tot_Sum(IfThen(Close-ts_Delay(Close,1),1,0))\n", "p6_tn5 <PERSON><PERSON>_<PERSON>(High-ts_Delay(Close,1))/Tot_Sum(ts_Delay(Close,1)-Low)\n", "p6_tn6 Tot_Mean((ts_<PERSON>(High,30)-Close)/(ts_<PERSON>(High,30)-ts_<PERSON>(Low,30)))\n", "p6_tn7 Tot_Mean((Close-ts_Min(Low,30))/(ts_<PERSON>(High,30)-ts_Min(Low,30)))\n", "p6_tn8 Tot_Mean((High-Low)/Close)\n", "p6_tn9 Tot_Mean(IfThen(Tot_Rank((Close-ts_Delay(Close,1))/Close)-0.910,(Close-ts_Delay(Close,1))/Close,get<PERSON><PERSON>(Close)))\n", "p6_tn10 Tot_Mean(IfThen(Tot_Rank(0.010-(Close-ts_Delay(Close,1))/Close),(Close-ts_Delay(Close,1))/Close,get<PERSON>an(Close)))\n", "p6_tn11 ts_<PERSON>rr(Close-ts_Delay(Close,1),pn_<PERSON>(Close-ts_Delay(Close,1)),60)\n", "p6_tn12 Tot_Sum(IfThen(Tot_Rank(High-Low)-0.10,Volume,0))-Tot_Sum(IfThen(Tot_Rank(High-Low)-0.10,0,Volume))\n", "p6_tn13 <PERSON><PERSON>_<PERSON><PERSON>(IfThen(Close-ts_Delay(Close,1),Volume,-Volume))\n"]}], "source": ["features1 = {}\n", "Forms = get_minFactorForms3()  # 获取特征序列公式\n", "for v in range(len(Forms)):\n", "    # start_time = time.time()\n", "    expr = Forms['forms'].values[v]\n", "    fm = Forms['fname'].values[v]\n", "    # if fm in need:\n", "    print(fm, expr)"]}, {"cell_type": "code", "execution_count": 7, "id": "b9e64cf6", "metadata": {}, "outputs": [], "source": ["def get_saveFeature1(df_all,need):\n", "    features1 = {}\n", "    Forms = get_minFactorForms3()  # 获取特征序列公式\n", "    for v in range(len(Forms)):\n", "        # start_time = time.time()\n", "        expr = Forms['forms'].values[v]\n", "        fm = Forms['fname'].values[v]\n", "        if fm in need:\n", "            # tmp = get_minFactor(df_all['open'], df_all['high'],df_all['low'],df_all['close'], df_all['volume'], df_all['amount'], df_all['vwap'], expr)\n", "        # path_factor = outputfile2 + fm +  '.pkl'\n", "        # pd.to_pickle(tmp, path_factor)\n", "            # print( v , \"toc: {:.2f} s\".format(time.time() -  start_time), expr)\n", "        # print(tmp.shape)\n", "            features1[fm] = tmp\n", "    return features1"]}], "metadata": {"kernelspec": {"display_name": "Python 3", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.10.13"}}, "nbformat": 4, "nbformat_minor": 5}