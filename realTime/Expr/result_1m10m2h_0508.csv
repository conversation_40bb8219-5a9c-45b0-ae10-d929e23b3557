,Unnamed: 0,expr,expr2,result,fac_ic,lsret,sharpe,max<PERSON>orr,<PERSON>ate,EDate,rets_up,rets_dn,pct,vCorr,srs_is,ars_is,ics_is,results_is
212,212,"-1*Min(ts_Delta(Min(df['p4_ms5'],36),50),get_KAMA(Mthan(df['p3_mf5'],df['p3_mf12']),7))","265_-1*Min(ts_Delta(Min(df['p4_ms5'],36),50),get_KAMA(<PERSON><PERSON>(df['p3_mf5'],df['p3_mf12']),7))",15.9545,0.0078,1.3892,5.0114,0.5996539394731388,2024-02-01 22:49:00+00:00,2025-01-30 20:49:00+00:00,0.73,0.651,0.5286,-0.16,3.43,0.84,0.00758,3.71
365,365,"-1*Min(pn_TransNorm(df['p4_ms5']),pn_Stand(ts_Stdev(df['p6_tn11'],36)))","389_-1*Min(pn_TransNorm(df['p4_ms5']),pn_Stand(ts_Stdev(df['p6_tn11'],36)))",15.0623,0.0075,1.3021,4.993,0.5665764483298728,2024-02-01 22:49:00+00:00,2025-01-30 20:49:00+00:00,0.584,0.711,0.451,-0.15,3.49,0.8,0.00745,3.65
253,253,"-1*ts_Regression(df['p4_ms1'],Add(df['p6_tn0'],df['p5_to0']),39,'C')","300_-1*ts_Regression(df['p4_ms1'],Add(df['p6_tn0'],df['p5_to0']),39,'C')",15.0436,0.007,1.3059,4.8972,0.538441381692458,2024-02-01 22:49:00+00:00,2025-01-30 20:49:00+00:00,0.657,0.641,0.5062,-0.16,2.87,0.71,0.00649,3.14
339,339,"-1*get_CCI(inv(df['p1_corrs7']),inv(ts_Entropy(df['p3_mf12'],10)),pn_Rank2(df['p5_to0']),29)","374_-1*get_CCI(inv(df['p1_corrs7']),inv(ts_Entropy(df['p3_mf12'],10)),pn_Rank2(df['p5_to0']),29)",15.0188,0.0078,1.2676,5.8621,0.5941459445826087,2024-02-01 22:49:00+00:00,2025-01-30 20:49:00+00:00,0.487,0.773,0.3865,-0.114,3.68,0.7,0.00611,3.32
446,446,"-1*pn_GroupNeutral(ts_Scale(df['p2_et7'],9),ts_Delay(pn_TransStd(df['p2_et7']),19))","447_-1*pn_GroupNeutral(ts_Scale(df['p2_et7'],9),ts_Delay(pn_TransStd(df['p2_et7']),19))",15.017,0.0074,1.2779,5.6043,0.562138667233962,2024-02-01 22:49:00+00:00,2025-01-30 20:49:00+00:00,0.568,0.703,0.4469,-0.446,4.63,0.95,0.00804,4.34
250,250,"-1*ts_Regression(df['p4_ms1'],ts_Rank(ts_TransNorm(df['p2_et7'],49),24),42,'C')","298_-1*ts_Regression(df['p4_ms1'],ts_Rank(ts_TransNorm(df['p2_et7'],49),24),42,'C')",14.71,0.0075,1.2778,4.6655,0.5051511677311074,2024-02-01 22:49:00+00:00,2025-01-30 20:49:00+00:00,0.551,0.72,0.4335,-0.414,3.33,0.81,0.00764,3.65
226,226,"-1*Minus(ts_TransNorm(df['p6_tn13'],1),ts_Delta(pn_TransStd(df['p5_to0']),24))","282_-1*Minus(ts_TransNorm(df['p6_tn13'],1),ts_Delta(pn_TransStd(df['p5_to0']),24))",14.69,0.0071,1.252,5.4498,0.5840645064384635,2024-02-01 22:49:00+00:00,2025-01-30 20:49:00+00:00,0.746,0.498,0.5997,-0.135,4.57,0.87,0.00638,3.89
229,229,"-1*Minus(pn_CrossFit(ts_Scale(df['dx'],42),df['p2_et7']),Lthan(df['p1_corrs5'],ts_Decay2(df['p6_tn5'],26)))","285_-1*Minus(pn_CrossFit(ts_Scale(df['dx'],42),df['p2_et7']),Lthan(df['p1_corrs5'],ts_Decay2(df['p6_tn5'],26)))",14.5828,0.0074,1.2747,4.3921,0.5471427713842807,2024-02-01 22:49:00+00:00,2025-01-30 20:49:00+00:00,0.856,0.412,0.6751,-0.501,2.99,0.75,0.0071,3.35
251,251,"-1*ts_Regression(df['p4_ms1'],get_CMO(df['p1_corrs5'],10),39,'C')","299_-1*ts_Regression(df['p4_ms1'],get_CMO(df['p1_corrs5'],10),39,'C')",14.4235,0.0059,1.2597,4.5905,0.4899739937053174,2024-02-01 22:49:00+00:00,2025-01-30 20:49:00+00:00,0.577,0.675,0.4609,-0.06,3.28,0.8,0.00602,3.3
387,387,"-1*get_LINEARREG_ANGLE(pn_GroupRank(df['p6_tn10'],Exp(df['p3_mf4'])),12)","405_-1*get_LINEARREG_ANGLE(pn_GroupRank(df['p6_tn10'],Exp(df['p3_mf4'])),12)",14.2804,0.0077,1.2307,4.7629,0.5794883102068371,2024-02-01 22:49:00+00:00,2025-01-30 20:49:00+00:00,0.59,0.633,0.4824,-0.419,4.9,1.08,0.00958,4.9
175,175,"-1*pn_GroupRank(ts_MeanChg(df['p4_ms2'],11),ts_Median(pn_Winsor(df['p1_corrs1'],40),29))","236_-1*pn_GroupRank(ts_MeanChg(df['p4_ms2'],11),ts_Median(pn_Winsor(df['p1_corrs1'],40),29))",14.0434,0.0066,1.1974,5.2245,0.5126993311474226,2024-02-01 22:49:00+00:00,2025-01-30 20:49:00+00:00,0.471,0.719,0.3958,-0.273,3.92,0.75,0.00632,3.51
194,194,"-1*ts_MeanChg(FilterInf(ts_Scale(df['p6_tn6'],33)),10)","248_-1*ts_MeanChg(FilterInf(ts_Scale(df['p6_tn6'],33)),10)",14.0405,0.0069,1.2137,4.6704,0.5702689062884257,2024-02-01 22:49:00+00:00,2025-01-30 20:49:00+00:00,0.473,0.734,0.3919,-0.388,4.43,0.98,0.00763,4.23
56,56,"-1*pn_GroupRank(df['p5_to0'],ts_Max(Reverse(df['p3_mf5']),10))","108_-1*pn_GroupRank(df['p5_to0'],ts_Max(Reverse(df['p3_mf5']),10))",13.831,0.0073,1.1554,5.7311,0.4993992419738541,2024-02-01 22:49:00+00:00,2025-01-30 20:49:00+00:00,0.527,0.621,0.4591,-0.155,4.2,0.84,0.00599,3.65
118,118,"pn_GroupNeutral(ts_Divide(df['dm'],4),MEthan(ts_Max(df['p6_tn5'],21),ts_Corr2(df['p2_et7'],df['di'],39)))","173_pn_GroupNeutral(ts_Divide(df['dm'],4),MEthan(ts_Max(df['p6_tn5'],21),ts_Corr2(df['p2_et7'],df['di'],39)))",13.7903,0.0066,1.1987,4.4152,0.5703163429211722,2024-02-01 22:49:00+00:00,2025-01-30 20:49:00+00:00,0.576,0.624,0.48,-0.139,3.51,0.84,0.00697,3.61
297,297,"-1*pn_GroupRank(ts_TransNorm(ts_Delta(df['p6_tn13'],10),31),ts_TransNorm(ts_Delta(df['p4_ms1'],37),10))","335_-1*pn_GroupRank(ts_TransNorm(ts_Delta(df['p6_tn13'],10),31),ts_TransNorm(ts_Delta(df['p4_ms1'],37),10))",13.7691,0.007,1.1696,5.161,0.5415950307135113,2024-02-01 22:49:00+00:00,2025-01-30 20:49:00+00:00,0.554,0.608,0.4768,-0.097,4.76,0.89,0.00715,4.13
260,260,"-1*Add(Min(Sign(df['p5_to0']),pn_TransStd(df['p4_ms5'])),df['p5_to2'])","304_-1*Add(Min(Sign(df['p5_to0']),pn_TransStd(df['p4_ms5'])),df['p5_to2'])",13.7623,0.0066,1.1798,4.9095,0.5677066254492528,2024-02-01 22:49:00+00:00,2025-01-30 20:49:00+00:00,0.515,0.657,0.4394,-0.2,4.05,0.9,0.00693,3.86
445,445,"-1*pn_GroupNeutral(get_LINEARREG_ANGLE(df['p2_et1'],12),ts_Product(pn_GroupNorm(df['p6_tn1'],df['p3_mf0']),7))","450_-1*pn_GroupNeutral(get_LINEARREG_ANGLE(df['p2_et1'],12),ts_Product(pn_GroupNorm(df['p6_tn1'],df['p3_mf0']),7))",13.7069,0.0061,1.183,4.7199,0.5612362029603507,2024-02-01 22:49:00+00:00,2025-01-30 20:49:00+00:00,0.57,0.606,0.4847,-0.274,4.58,1.04,0.0076,4.35
62,62,"-1*Power(ts_Scale(get_LINEARREG_SLOPE(df['p2_et7'],3),11),47)","115_-1*Power(ts_Scale(get_LINEARREG_SLOPE(df['p2_et7'],3),11),47)",13.6546,0.0059,1.1825,4.6042,0.5479521187905391,2024-02-01 22:49:00+00:00,2025-01-30 20:49:00+00:00,0.562,0.613,0.4783,-0.187,3.32,0.74,0.00591,3.21
11,11,"-1*Add(IfThen(df['p2_et12'],44,19),Multiply(df['p2_et14'],df['p6_tn10']))","37_-1*Add(IfThen(df['p2_et12'],44,19),Multiply(df['p2_et14'],df['p6_tn10']))",13.6006,0.0081,1.1578,4.8522,0.5990941190555508,2024-02-01 22:49:00+00:00,2025-01-30 20:49:00+00:00,0.316,0.835,0.2745,-0.222,3.25,0.83,0.00778,3.68
359,359,"-1*Min(ts_Scale(df['dcphase'],21),ts_Delta(df['p6_tn5'],10))","386_-1*Min(ts_Scale(df['dcphase'],21),ts_Delta(df['p6_tn5'],10))",13.4667,0.0073,1.1649,4.3626,0.5706960201635558,2024-02-01 22:49:00+00:00,2025-01-30 20:49:00+00:00,0.589,0.568,0.5091,-0.217,4.32,0.99,0.0089,4.46
198,198,"-1*pn_GroupNorm(pn_GroupNeutral(ts_Delta(df['p2_et1'],10),ts_Argmin(df['p2_et16'],43)),UnEqual(ts_Entropy(df['p5_to6'],10),pn_Rank(df['p5_to0'])))","250_-1*pn_GroupNorm(pn_GroupNeutral(ts_Delta(df['p2_et1'],10),ts_Argmin(df['p2_et16'],43)),UnEqual(ts_Entropy(df['p5_to6'],10),pn_Rank(df['p5_to0'])))",13.4468,0.0069,1.1516,4.7541,0.5480047561771423,2024-02-01 22:49:00+00:00,2025-01-30 20:49:00+00:00,0.527,0.617,0.4607,-0.148,4.46,1.13,0.00803,4.51
128,128,"-1*get_CCI(ts_Delta(df['p6_tn13'],26),df['p2_et12'],df['p4_ms5'],14)","186_-1*get_CCI(ts_Delta(df['p6_tn13'],26),df['p2_et12'],df['p4_ms5'],14)",13.4312,0.0067,1.1452,4.9277,0.5506110047346545,2024-02-01 22:49:00+00:00,2025-01-30 20:49:00+00:00,0.535,0.603,0.4701,-0.104,3.79,0.79,0.00675,3.6
211,211,"-1*Min(ts_Delta(Min(df['p4_ms5'],0.399),7),get_KAMA(Mthan(df['p3_mf5'],df['p3_mf12']),7))","264_-1*Min(ts_Delta(Min(df['p4_ms5'],0.399),7),get_KAMA(Mthan(df['p3_mf5'],df['p3_mf12']),7))",13.3754,0.0072,1.1621,4.1814,0.5238040593331776,2024-02-01 22:49:00+00:00,2025-01-30 20:49:00+00:00,0.593,0.561,0.5139,-0.174,3.75,0.94,0.00771,3.97
89,89,"Power(get_CCI(df['p6_tn6'],ts_Decay(df['dcperiod'],34),get_CMO(df['p6_tn6'],24),12),23)","142_Power(get_CCI(df['p6_tn6'],ts_Decay(df['dcperiod'],34),get_CMO(df['p6_tn6'],24),12),23)",13.3072,0.0066,1.1463,4.5354,0.5918620343709333,2024-02-01 22:49:00+00:00,2025-01-30 20:49:00+00:00,0.642,0.506,0.5592,-0.176,3.89,0.88,0.00717,3.83
284,284,"-1*Multiply(pn_Winsor(pn_Winsor(df['p5_to0'],31),8),get_MINUS_DI(ts_Skewness(df['p3_mf11'],23),df['p3_mf11'],Sign(df['p6_tn11']),20))","323_-1*Multiply(pn_Winsor(pn_Winsor(df['p5_to0'],31),8),get_MINUS_DI(ts_Skewness(df['p3_mf11'],23),df['p3_mf11'],Sign(df['p6_tn11']),20))",13.3036,0.0065,1.1394,4.7496,0.5865984293204365,2024-02-01 22:49:00+00:00,2025-01-30 20:49:00+00:00,0.608,0.525,0.5366,-0.12,3.27,0.73,0.00594,3.19
369,369,"-1*pn_GroupNorm(df['p6_tn10'],df['cci'])","391_-1*pn_GroupNorm(df['p6_tn10'],df['cci'])",13.2303,0.0066,1.1477,4.2648,0.5784692181544266,2024-02-01 22:49:00+00:00,2025-01-30 20:49:00+00:00,0.613,0.528,0.5372,-0.038,2.34,0.55,0.00579,2.63
44,44,"-1*pn_GroupNeutral(df['p2_et9'],ts_ChgRate(ts_Scale(df['p2_et3'],23),38))","86_-1*pn_GroupNeutral(df['p2_et9'],ts_ChgRate(ts_Scale(df['p2_et3'],23),38))",13.2233,0.0062,1.1341,4.7095,0.5739589845425641,2024-02-01 22:49:00+00:00,2025-01-30 20:49:00+00:00,0.48,0.647,0.4259,-0.26,2.47,0.64,0.00562,2.75
237,237,"-1*pn_GroupNeutral(df['p2_et7'],Max(Not(df['p3_mf8']),df['p2_et7']))","287_-1*pn_GroupNeutral(df['p2_et7'],Max(Not(df['p3_mf8']),df['p2_et7']))",13.2169,0.0059,1.1406,4.5556,0.5156601488320975,2024-02-01 22:49:00+00:00,2025-01-30 20:49:00+00:00,0.745,0.389,0.657,-0.31,3.01,0.68,0.0058,3.01
216,216,"pn_GroupNorm(get_CMO(ts_Delta(df['dm'],10),12),inv(get_LINEARREG_SLOPE(df['p2_et7'],9)))","268_pn_GroupNorm(get_CMO(ts_Delta(df['dm'],10),12),inv(get_LINEARREG_SLOPE(df['p2_et7'],9)))",13.2093,0.0056,1.14,4.5923,0.5912632230581834,2024-02-01 22:49:00+00:00,2025-01-30 20:49:00+00:00,0.662,0.479,0.5802,0.019,3.65,0.77,0.00602,3.38
33,33,"-1*Add(pn_GroupNeutral(ts_Scale(df['p6_tn13'],21),pn_Stand(df['p4_ms2'])),UnEqual(get_LINEARREG_SLOPE(df['p3_mf1'],28),pn_TransNorm(df['p6_tn13'])))","70_-1*Add(pn_GroupNeutral(ts_Scale(df['p6_tn13'],21),pn_Stand(df['p4_ms2'])),UnEqual(get_LINEARREG_SLOPE(df['p3_mf1'],28),pn_TransNorm(df['p6_tn13'])))",13.1112,0.0056,1.1161,5.0035,0.5976411429937107,2024-02-01 22:49:00+00:00,2025-01-30 20:49:00+00:00,0.445,0.664,0.4013,-0.05,3.57,0.68,0.00508,3.06
410,410,"-1*pn_GroupNeutral(df['p4_ms1'],ts_Delay(df['p4_ms1'],7))","421_-1*pn_GroupNeutral(df['p4_ms1'],ts_Delay(df['p4_ms1'],7))",13.1023,0.0065,1.1383,4.1772,0.595213004737174,2024-02-01 22:49:00+00:00,2025-01-30 20:49:00+00:00,0.571,0.56,0.5049,-0.188,3.95,0.96,0.00783,4.08
92,92,"Minus(get_LINEARREG_SLOPE(df['di'],4),Min(Exp(df['p3_mf11']),get_KAMA(df['p4_ms4'],11)))","147_Minus(get_LINEARREG_SLOPE(df['di'],4),Min(Exp(df['p3_mf11']),get_KAMA(df['p4_ms4'],11)))",13.0149,0.0065,1.1247,4.3334,0.578112349472421,2024-02-01 22:49:00+00:00,2025-01-30 20:49:00+00:00,0.563,0.563,0.5,-0.27,3.37,0.83,0.00725,3.61
266,266,"-1*get_CCI(Min(FilterInf(df['p5_to0']),ts_Rank(df['p6_tn12'],17)),ts_Cov(pn_FillMin(df['p5_to6']),pn_GroupNeutral(df['p4_ms3'],df['p5_to3']),50),df['ultosc'],22)","309_-1*get_CCI(Min(FilterInf(df['p5_to0']),ts_Rank(df['p6_tn12'],17)),ts_Cov(pn_FillMin(df['p5_to6']),pn_GroupNeutral(df['p4_ms3'],df['p5_to3']),50),df['ultosc'],22)",12.9933,0.0056,1.1031,5.0496,0.584878916728303,2024-02-01 22:49:00+00:00,2025-01-30 20:49:00+00:00,0.663,0.433,0.6049,-0.059,3.5,0.68,0.00547,3.11
437,437,"-1*pn_GroupNeutral(df['p2_et7'],df['p6_tn0'])","445_-1*pn_GroupNeutral(df['p2_et7'],df['p6_tn0'])",12.918,0.0066,1.1252,4.0094,0.5998025196336724,2024-02-01 22:49:00+00:00,2025-01-30 20:49:00+00:00,0.727,0.391,0.6503,-0.598,2.56,0.65,0.00631,2.93
78,78,"-1*ts_Scale(pn_Stand(df['p6_tn10']),15)","129_-1*ts_Scale(pn_Stand(df['p6_tn10']),15)",12.8774,0.0076,1.1007,4.4656,0.5931843766984487,2024-02-01 22:49:00+00:00,2025-01-30 20:49:00+00:00,0.678,0.416,0.6197,-0.191,2.77,0.62,0.00662,3.02
113,113,"-1*pn_GroupRank(pn_Rank2(df['p5_to0']),ts_Delay(df['p2_et16'],50))","169_-1*pn_GroupRank(pn_Rank2(df['p5_to0']),ts_Delay(df['p2_et16'],50))",12.8751,0.0064,1.1022,4.5918,0.5758984577359219,2024-02-01 22:49:00+00:00,2025-01-30 20:49:00+00:00,0.45,0.646,0.4106,-0.16,2.69,0.59,0.00509,2.65
52,52,"-1*pn_GroupRank(df['p6_tn4'],ts_Mean(df['p4_ms0'],26))","106_-1*pn_GroupRank(df['p6_tn4'],ts_Mean(df['p4_ms0'],26))",12.8121,0.0063,1.088,4.8449,0.5667084410698529,2024-02-01 22:49:00+00:00,2025-01-30 20:49:00+00:00,0.527,0.554,0.4875,-0.174,3.34,0.62,0.00526,2.94
292,292,"pn_GroupRank(ts_Delta(Reverse(df['p6_tn10']),27),ts_Decay2(Abs(df['p6_tn4']),24))","331_pn_GroupRank(ts_Delta(Reverse(df['p6_tn10']),27),ts_Decay2(Abs(df['p6_tn4']),24))",12.7883,0.0076,1.1085,3.9668,0.594646799620328,2024-02-01 22:49:00+00:00,2025-01-30 20:49:00+00:00,0.514,0.595,0.4635,-0.22,3.58,0.88,0.00865,4.02
375,375,"-1*Add(pn_GroupNeutral(FilterInf(df['cci']),get_KAMA(df['p2_et10'],47)),get_CCI(ts_Quantile(df['di'],19,'C'),ts_MeanChg(df['p2_et15'],3),ts_Min(df['adosc'],22),49))","396_-1*Add(pn_GroupNeutral(FilterInf(df['cci']),get_KAMA(df['p2_et10'],47)),get_CCI(ts_Quantile(df['di'],19,'C'),ts_MeanChg(df['p2_et15'],3),ts_Min(df['adosc'],22),49))",12.7142,0.0062,1.0899,4.5184,0.4581586557119605,2024-02-01 22:49:00+00:00,2025-01-30 20:49:00+00:00,0.659,0.424,0.6085,-0.324,3.16,0.66,0.00592,3.06
133,133,"-1*pn_GroupNeutral(pn_Winsor(pn_TransStd(df['p2_et1']),44),Minus(Sign(df['cmo']),get_LINEARREG_ANGLE(df['p2_et10'],2)))","194_-1*pn_GroupNeutral(pn_Winsor(pn_TransStd(df['p2_et1']),44),Minus(Sign(df['cmo']),get_LINEARREG_ANGLE(df['p2_et10'],2)))",12.6576,0.0061,1.0912,4.328,0.4906880468501056,2024-02-01 22:49:00+00:00,2025-01-30 20:49:00+00:00,0.595,0.489,0.5489,-0.119,3.05,0.75,0.00651,3.26
157,157,"-1*get_LINEARREG_ANGLE(ts_Delta(df['p2_et7'],38),3)","225_-1*get_LINEARREG_ANGLE(ts_Delta(df['p2_et7'],38),3)",12.6572,0.0062,1.1067,3.8407,0.5984215646099664,2024-02-01 22:49:00+00:00,2025-01-30 20:49:00+00:00,0.598,0.502,0.5436,-0.251,3.07,0.79,0.00631,3.27
213,213,"-1*get_KAMA(df['p4_ms1'],20)","265_-1*get_KAMA(df['p4_ms1'],20)",12.5116,0.005,1.0911,4.0475,0.5191339125835245,2024-02-01 22:49:00+00:00,2025-01-30 20:49:00+00:00,0.454,0.63,0.4188,-0.016,1.6,0.38,0.00215,1.44
283,283,"get_CCI(df['p6_tn6'],Max(df['p2_et18'],0.514),ts_Divide(df['p6_tn6'],10),40)","320_get_CCI(df['p6_tn6'],Max(df['p2_et18'],0.514),ts_Divide(df['p6_tn6'],10),40)",12.427,0.0068,1.0547,4.6181,0.5650977714992222,2024-02-01 22:49:00+00:00,2025-01-30 20:49:00+00:00,0.49,0.566,0.464,-0.214,4.71,1.02,0.00861,4.57
18,18,"Minus(LEthan(df['p6_tn4'],df['p4_ms5']),ts_Delta(get_LINEARREG_ANGLE(df['ultosc'],11),8))","51_Minus(LEthan(df['p6_tn4'],df['p4_ms5']),ts_Delta(get_LINEARREG_ANGLE(df['ultosc'],11),8))",12.4224,0.0066,1.058,4.5346,0.4844012674149002,2024-02-01 22:49:00+00:00,2025-01-30 20:49:00+00:00,0.449,0.61,0.424,-0.177,5.66,1.13,0.00873,5.05
98,98,"-1*SignedPower(ts_Rank(df['p3_mf11'],27),ts_Rank(Divide(df['p2_et5'],0.339),35))","155_-1*SignedPower(ts_Rank(df['p3_mf11'],27),ts_Rank(Divide(df['p2_et5'],0.339),35))",12.4032,0.0055,1.0588,4.6243,0.4718909415622192,2024-02-01 22:49:00+00:00,2025-01-30 20:49:00+00:00,0.583,0.469,0.5542,-0.019,3.58,0.72,0.00567,3.23
179,179,"-1*FilterInf(get_CCI(Power(df['p6_tn7'],20),ts_ChgRate(df['p2_et19'],6),df['p2_et4'],13))","238_-1*FilterInf(get_CCI(Power(df['p6_tn7'],20),ts_ChgRate(df['p2_et19'],6),df['p2_et4'],13))",12.3789,0.0055,1.0526,4.7353,0.4820381224934777,2024-02-01 22:49:00+00:00,2025-01-30 20:49:00+00:00,0.48,0.566,0.4589,-0.109,3.92,0.75,0.00567,3.38
433,433,"-1*pn_GroupRank(pn_CrossFit(ts_Rank(df['p4_ms5'],2),df['p4_ms5']),Lthan(pn_FillMin(df['p1_corrs4']),ts_CorrChg(df['kama'],df['p1_corrs4'],23)))","442_-1*pn_GroupRank(pn_CrossFit(ts_Rank(df['p4_ms5'],2),df['p4_ms5']),Lthan(pn_FillMin(df['p1_corrs4']),ts_CorrChg(df['kama'],df['p1_corrs4'],23)))",12.3752,0.0057,1.066,4.2907,0.4947888755599124,2024-02-01 22:49:00+00:00,2025-01-30 20:49:00+00:00,0.503,0.556,0.475,-0.202,3.22,0.74,0.00665,3.33
81,81,"-1*get_CCI(pn_Cut(df['p2_et1']),Minus(df['p6_tn6'],ts_MeanChg(df['p5_to0'],13)),Max(df['p6_tn4'],pn_Stand(df['p2_et4'])),9)","134_-1*get_CCI(pn_Cut(df['p2_et1']),Minus(df['p6_tn6'],ts_MeanChg(df['p5_to0'],13)),Max(df['p6_tn4'],pn_Stand(df['p2_et4'])),9)",12.3739,0.0061,1.0465,4.8154,0.4887657621707831,2024-02-01 22:49:00+00:00,2025-01-30 20:49:00+00:00,0.52,0.52,0.5,-0.146,5.24,1.01,0.00757,4.52
49,49,"-1*pn_CrossFit(pn_GroupNeutral(ts_Stdev(df['p1_corrs5'],16),df['p6_tn1']),Power(ts_Rank(df['p2_et9'],41),16))","99_-1*pn_CrossFit(pn_GroupNeutral(ts_Stdev(df['p1_corrs5'],16),df['p6_tn1']),Power(ts_Rank(df['p2_et9'],41),16))",12.3692,0.0055,1.0643,4.3483,0.4906024269632861,2024-02-01 22:49:00+00:00,2025-01-30 20:49:00+00:00,0.655,0.402,0.6197,-0.199,2.22,0.45,0.00388,2.08
428,428,"-1*ts_Regression(df['p2_et1'],Divide(Sign(df['p6_tn8']),inv(df['liangle'])),6,'C')","438_-1*ts_Regression(df['p2_et1'],Divide(Sign(df['p6_tn8']),inv(df['liangle'])),6,'C')",12.3602,0.006,1.0673,4.1556,0.5381531919137068,2024-02-01 22:49:00+00:00,2025-01-30 20:49:00+00:00,0.436,0.624,0.4113,-0.33,2.77,0.69,0.00567,2.92
131,131,"-1*Multiply(df['p5_to4'],pn_TransStd(df['p3_mf11']))","193_-1*Multiply(df['p5_to4'],pn_TransStd(df['p3_mf11']))",12.3423,0.0061,1.0421,4.843,0.5901723047207251,2024-02-01 22:49:00+00:00,2025-01-30 20:49:00+00:00,0.627,0.408,0.6058,-0.166,3.98,0.76,0.0063,3.54
67,67,"-1*ts_Scale(ts_Regression(FilterInf(df['cmo']),get_CMO(df['p4_ms2'],39),7,'D'),12)","122_-1*ts_Scale(ts_Regression(FilterInf(df['cmo']),get_CMO(df['p4_ms2'],39),7,'D'),12)",12.3386,0.0058,1.0633,4.2432,0.5934991548518814,2024-02-01 22:49:00+00:00,2025-01-30 20:49:00+00:00,0.581,0.475,0.5502,-0.228,4.0,0.88,0.00679,3.79
432,432,"-1*pn_GroupRank(pn_CrossFit(pn_GroupRank(df['p2_et10'],df['p2_et3']),df['p2_et4']),ts_Sum(df['p2_et5'],22))","441_-1*pn_GroupRank(pn_CrossFit(pn_GroupRank(df['p2_et10'],df['p2_et3']),df['p2_et4']),ts_Sum(df['p2_et5'],22))",12.3321,0.0065,1.0492,4.5468,0.5778317757084496,2024-02-01 22:49:00+00:00,2025-01-30 20:49:00+00:00,0.504,0.538,0.4837,-0.174,3.46,0.77,0.00614,3.34
317,317,"-1*pn_CrossFit(Minus(ts_Partial_corr(df['p4_ms0'],df['p4_ms4'],df['p1_corrs7'],45),Sign(df['p2_et5'])),Minus(df['p6_tn10'],df['p3_mf7']))","354_-1*pn_CrossFit(Minus(ts_Partial_corr(df['p4_ms0'],df['p4_ms4'],df['p1_corrs7'],45),Sign(df['p2_et5'])),Minus(df['p6_tn10'],df['p3_mf7']))",12.3314,0.0055,1.0796,3.7843,0.4177174318329574,2024-02-01 22:49:00+00:00,2025-01-30 20:49:00+00:00,0.664,0.409,0.6188,-0.08,3.4,0.84,0.00637,3.46
303,303,"-1*pn_GroupNorm(pn_CrossFit(df['p6_tn4'],ts_Regression(df['p4_ms5'],df['p3_mf12'],44,'B')),ts_Scale(df['p1_corrs7'],16))","341_-1*pn_GroupNorm(pn_CrossFit(df['p6_tn4'],ts_Regression(df['p4_ms5'],df['p3_mf12'],44,'B')),ts_Scale(df['p1_corrs7'],16))",12.3037,0.0057,1.0615,4.2156,0.5779628242303145,2024-02-01 22:49:00+00:00,2025-01-30 20:49:00+00:00,0.455,0.6,0.4313,-0.049,1.57,0.35,0.00308,1.58
224,224,"-1*pn_GroupNeutral(pn_CrossFit(ts_Decay(df['cci'],30),df['p6_tn5']),ts_Decay2(ts_Decay2(df['p2_et13'],4),4))","280_-1*pn_GroupNeutral(pn_CrossFit(ts_Decay(df['cci'],30),df['p6_tn5']),ts_Decay2(ts_Decay2(df['p2_et13'],4),4))",12.3001,0.0072,1.0625,3.9474,0.5460299739803554,2024-02-01 22:49:00+00:00,2025-01-30 20:49:00+00:00,0.438,0.617,0.4152,-0.182,2.92,0.67,0.00718,3.25
184,184,"-1*pn_GroupNeutral(ts_Delta(get_CCI(df['p1_corrs9'],df['p5_to0'],df['p2_et19'],43),39),Not(get_HT_DCPHASE(df['p4_ms4'])))","239_-1*pn_GroupNeutral(ts_Delta(get_CCI(df['p1_corrs9'],df['p5_to0'],df['p2_et19'],43),39),Not(get_HT_DCPHASE(df['p4_ms4'])))",12.2621,0.0062,1.0461,4.4705,0.5643438785482041,2024-02-01 22:49:00+00:00,2025-01-30 20:49:00+00:00,0.518,0.521,0.4986,-0.132,3.09,0.64,0.00544,2.92
197,197,"-1*pn_GroupNorm(ts_Delta(df['p2_et1'],38),ts_CovChg(df['p2_et16'],ts_MeanChg(df['p2_et5'],6),40))","252_-1*pn_GroupNorm(ts_Delta(df['p2_et1'],38),ts_CovChg(df['p2_et16'],ts_MeanChg(df['p2_et5'],6),40))",12.2128,0.0059,1.0449,4.4036,0.5765360915765361,2024-02-01 22:49:00+00:00,2025-01-30 20:49:00+00:00,0.445,0.593,0.4287,-0.144,2.17,0.49,0.00379,2.09
223,223,"Minus(ts_Mean(ts_Skewness(df['p3_mf4'],47),8),ts_TransNorm(get_CCI(df['p3_mf9'],df['p5_to2'],df['p6_tn13'],14),45))","278_Minus(ts_Mean(ts_Skewness(df['p3_mf4'],47),8),ts_TransNorm(get_CCI(df['p3_mf9'],df['p5_to2'],df['p6_tn13'],14),45))",12.1479,0.0065,1.0456,4.1036,0.5552984892784498,2024-02-01 22:49:00+00:00,2025-01-30 20:49:00+00:00,0.723,0.324,0.6905,-0.089,3.96,0.87,0.00714,3.84
343,343,"-1*get_CCI(pn_TransStd(get_CMO(df['cmo'],40)),ts_Decay2(df['p5_to0'],47),inv(df['p6_tn6']),5)","376_-1*get_CCI(pn_TransStd(get_CMO(df['cmo'],40)),ts_Decay2(df['p5_to0'],47),inv(df['p6_tn6']),5)",12.1215,0.0056,1.0359,4.4472,0.5826987893403934,2024-02-01 22:49:00+00:00,2025-01-30 20:49:00+00:00,0.386,0.643,0.3751,-0.225,3.47,0.69,0.00545,3.11
381,381,"-1*get_CCI(ts_StdevChg(df['p5_to5'],14),Power(df['p1_corrs2'],36),Minus(df['p2_et10'],df['dm']),14)","401_-1*get_CCI(ts_StdevChg(df['p5_to5'],14),Power(df['p1_corrs2'],36),Minus(df['p2_et10'],df['dm']),14)",12.0949,0.0063,1.0363,4.2575,0.5857680245114544,2024-02-01 22:49:00+00:00,2025-01-30 20:49:00+00:00,0.544,0.485,0.5287,-0.126,4.31,0.96,0.00764,4.16
351,351,"-1*pn_GroupNeutral(pn_TransNorm(ts_Rank(df['ultosc'],15)),ts_Product(pn_TransNorm(df['p4_ms2']),35))","381_-1*pn_GroupNeutral(pn_TransNorm(ts_Rank(df['ultosc'],15)),ts_Product(pn_TransNorm(df['p4_ms2']),35))",12.0939,0.0058,1.039,4.236,0.570229708063356,2024-02-01 22:49:00+00:00,2025-01-30 20:49:00+00:00,0.59,0.442,0.5717,-0.206,3.11,0.65,0.00524,2.9
173,173,"Add(Exp(df['p6_tn6']),pn_GroupRank(ts_Divide(df['p6_tn12'],35),df['p2_et10']))","236_Add(Exp(df['p6_tn6']),pn_GroupRank(ts_Divide(df['p6_tn12'],35),df['p2_et10']))",12.0816,0.0059,1.0335,4.3594,0.573621983046713,2024-02-01 22:49:00+00:00,2025-01-30 20:49:00+00:00,0.473,0.562,0.457,-0.252,4.32,0.88,0.00735,4.01
337,337,"-1*Add(ts_Mean(pn_GroupRank(df['p5_to2'],df['p4_ms0']),39),Min(df['p5_to2'],get_LINEARREG_ANGLE(df['ultosc'],11)))","373_-1*Add(ts_Mean(pn_GroupRank(df['p5_to2'],df['p4_ms0']),39),Min(df['p5_to2'],get_LINEARREG_ANGLE(df['ultosc'],11)))",12.0708,0.0055,1.0333,4.3893,0.5871295402883862,2024-02-01 22:49:00+00:00,2025-01-30 20:49:00+00:00,0.437,0.589,0.4259,-0.266,5.45,1.1,0.00761,4.71
294,294,"-1*pn_GroupRank(ts_TransNorm(ts_Delta(df['p6_tn13'],5),10),ts_Delay(df['p1_corrs8'],41))","332_-1*pn_GroupRank(ts_TransNorm(ts_Delta(df['p6_tn13'],5),10),ts_Delay(df['p1_corrs8'],41))",12.0682,0.006,1.037,4.1982,0.5470404842354463,2024-02-01 22:49:00+00:00,2025-01-30 20:49:00+00:00,0.584,0.446,0.567,-0.132,3.3,0.67,0.0057,3.08
190,190,"ts_MeanChg(get_CCI(ts_Delay(df['p5_to0'],48),ts_Cov2(df['p2_et13'],df['p3_mf4'],43),df['p5_to0'],15),10)","245_ts_MeanChg(get_CCI(ts_Delay(df['p5_to0'],48),ts_Cov2(df['p2_et13'],df['p3_mf4'],43),df['p5_to0'],15),10)",11.9928,0.0056,1.0151,4.6815,0.4992344352142789,2024-02-01 22:49:00+00:00,2025-01-30 20:49:00+00:00,0.493,0.523,0.4852,-0.141,5.03,0.96,0.00713,4.3
241,241,"-1*get_CCI(ts_Regression(df['p6_tn13'],Softsign(df['dcperiod']),7,'C'),Sign(df['p6_tn2']),Minus(get_LINEARREG_SLOPE(df['dcperiod'],45),Add(df['p6_tn10'],0.815)),19)","290_-1*get_CCI(ts_Regression(df['p6_tn13'],Softsign(df['dcperiod']),7,'C'),Sign(df['p6_tn2']),Minus(get_LINEARREG_SLOPE(df['dcperiod'],45),Add(df['p6_tn10'],0.815)),19)",11.9666,0.0056,1.0121,4.7022,0.5713328980680294,2024-02-01 22:49:00+00:00,2025-01-30 20:49:00+00:00,0.4,0.605,0.398,-0.209,3.11,0.62,0.00527,2.87
423,423,"-1*ts_Regression(Softsign(df['ultosc']),df['p6_tn13'],24,'C')","433_-1*ts_Regression(Softsign(df['ultosc']),df['p6_tn13'],24,'C')",11.9437,0.0056,1.0376,3.8631,0.5606376721461609,2024-02-01 22:49:00+00:00,2025-01-30 20:49:00+00:00,0.431,0.6,0.418,-0.239,2.46,0.56,0.00434,2.39
72,72,"-1*Min(ts_TransNorm(ts_Scale(df['dx'],24),6),ts_TransNorm(df['p5_to0'],25))","126_-1*Min(ts_TransNorm(ts_Scale(df['dx'],24),6),ts_TransNorm(df['p5_to0'],25))",11.942,0.0058,1.0249,4.216,0.5153893379239434,2024-02-01 22:49:00+00:00,2025-01-30 20:49:00+00:00,0.433,0.585,0.4253,-0.145,3.22,0.68,0.00589,3.1
314,314,"get_CCI(ts_Quantile(ts_Scale(df['lislope'],38),42,'B'),Softsign(ts_Regression(df['p4_ms2'],df['p6_tn7'],7,'C')),Power(df['p5_to4'],28),42)","352_get_CCI(ts_Quantile(ts_Scale(df['lislope'],38),42,'B'),Softsign(ts_Regression(df['p4_ms2'],df['p6_tn7'],7,'C')),Power(df['p5_to4'],28),42)",11.919,0.0059,1.0224,4.2035,0.5951851979185405,2024-02-01 22:49:00+00:00,2025-01-30 20:49:00+00:00,0.529,0.495,0.5166,-0.245,3.26,0.68,0.00593,3.12
167,167,"Multiply(df['p6_tn10'],pn_CrossFit(ts_Mean(df['p2_et13'],46),df['p4_ms6']))","233_Multiply(df['p6_tn10'],pn_CrossFit(ts_Mean(df['p2_et13'],46),df['p4_ms6']))",11.9171,0.0058,1.0379,3.7526,0.5309066418411631,2024-02-01 22:49:00+00:00,2025-01-30 20:49:00+00:00,0.447,0.592,0.4302,-0.21,3.67,0.89,0.00581,3.5
32,32,"-1*Add(df['p6_tn10'],pn_GroupNeutral(Divide(df['p6_tn13'],0.696),Power(df['p6_tn12'],41)))","68_-1*Add(df['p6_tn10'],pn_GroupNeutral(Divide(df['p6_tn13'],0.696),Power(df['p6_tn12'],41)))",11.914,0.0054,1.0182,4.3791,0.4277856000758673,2024-02-01 22:49:00+00:00,2025-01-30 20:49:00+00:00,0.488,0.524,0.4822,-0.108,3.63,0.74,0.00625,3.38
348,348,"-1*pn_GroupNeutral(pn_TransNorm(ts_Cov(df['p4_ms5'],df['p2_et14'],16)),And(pn_Cut(df['p5_to7']),ts_Rank(df['p2_et10'],24)))","380_-1*pn_GroupNeutral(pn_TransNorm(ts_Cov(df['p4_ms5'],df['p2_et14'],16)),And(pn_Cut(df['p5_to7']),ts_Rank(df['p2_et10'],24)))",11.897,0.0041,1.035,4.0285,0.3941116340404241,2024-02-01 22:49:00+00:00,2025-01-30 20:49:00+00:00,0.54,0.488,0.5253,-0.059,4.17,0.92,0.00468,3.48
83,83,"-1*get_CCI(pn_GroupNorm(df['ultosc'],df['p3_mf9']),ts_Decay2(pn_FillMax(df['p6_tn4']),3),ts_Mean(df['p5_to0'],40),10)","136_-1*get_CCI(pn_GroupNorm(df['ultosc'],df['p3_mf9']),ts_Decay2(pn_FillMax(df['p6_tn4']),3),ts_Mean(df['p5_to0'],40),10)",11.882,0.0059,1.0079,4.5258,0.5447652777410169,2024-02-01 22:49:00+00:00,2025-01-30 20:49:00+00:00,0.622,0.379,0.6214,-0.214,4.71,0.94,0.00734,4.21
75,75,"-1*get_LINEARREG_ANGLE(Min(df['p6_tn10'],Min(df['p4_ms0'],df['p6_tn4'])),15)","129_-1*get_LINEARREG_ANGLE(Min(df['p6_tn10'],Min(df['p4_ms0'],df['p6_tn4'])),15)",11.8807,0.0065,1.0239,3.9455,0.5485082696611909,2024-02-01 22:49:00+00:00,2025-01-30 20:49:00+00:00,0.636,0.381,0.6254,-0.305,3.85,0.94,0.0082,4.1
340,340,"-1*ts_TransNorm(pn_CrossFit(Mthan(df['dcperiod'],df['cci']),pn_Rank(df['p6_tn13'])),5)","375_-1*ts_TransNorm(pn_CrossFit(Mthan(df['dcperiod'],df['cci']),pn_Rank(df['p6_tn13'])),5)",11.8758,0.0051,0.9979,4.9185,0.4953623943605694,2024-02-01 22:49:00+00:00,2025-01-30 20:49:00+00:00,0.485,0.506,0.4894,0.011,2.02,0.36,0.00311,1.75
429,429,"-1*ts_Regression(ts_Cov2(df['ultosc'],pn_Stand(df['p4_ms0']),3),ts_TransNorm(df['p2_et1'],18),46,'C')","439_-1*ts_Regression(ts_Cov2(df['ultosc'],pn_Stand(df['p4_ms0']),3),ts_TransNorm(df['p2_et1'],18),46,'C')",11.8389,0.0049,1.0253,4.0202,0.5031143451331094,2024-02-01 22:49:00+00:00,2025-01-30 20:49:00+00:00,0.339,0.679,0.333,-0.071,4.46,1.07,0.00738,4.3
82,82,"-1*get_CCI(ts_MeanChg(df['p4_ms2'],17),Min(get_CMO(df['p4_ms1'],49),df['p2_et9']),ts_Delta(pn_Cut(df['p2_et1']),17),15)","135_-1*get_CCI(ts_MeanChg(df['p4_ms2'],17),Min(get_CMO(df['p4_ms1'],49),df['p2_et9']),ts_Delta(pn_Cut(df['p2_et1']),17),15)",11.8273,0.0058,1.007,4.3989,0.4494403333088029,2024-02-01 22:49:00+00:00,2025-01-30 20:49:00+00:00,0.537,0.463,0.537,-0.157,2.32,0.47,0.00444,2.25
97,97,"-1*Minus(get_CCI(pn_Rank2(df['p6_tn13']),Sign(df['p5_to7']),pn_Rank2(df['p2_et9']),48),get_MINUS_DI(ts_Scale(df['p2_et8'],31),get_KAMA(df['p6_tn6'],48),df['p2_et9'],48))","152_-1*Minus(get_CCI(pn_Rank2(df['p6_tn13']),Sign(df['p5_to7']),pn_Rank2(df['p2_et9']),48),get_MINUS_DI(ts_Scale(df['p2_et8'],31),get_KAMA(df['p6_tn6'],48),df['p2_et9'],48))",11.8175,0.0064,0.9967,4.5977,0.5950835072156432,2024-02-01 22:49:00+00:00,2025-01-30 20:49:00+00:00,0.449,0.541,0.4535,-0.25,2.68,0.51,0.00503,2.54
238,238,"-1*pn_GroupNeutral(pn_Winsor(ts_Delta(df['p2_et9'],40),3),Log(df['p5_to4']))","287_-1*pn_GroupNeutral(pn_Winsor(ts_Delta(df['p2_et9'],40),3),Log(df['p5_to4']))",11.8073,0.0055,1.0212,3.9612,0.5832839634411816,2024-02-01 22:49:00+00:00,2025-01-30 20:49:00+00:00,0.527,0.488,0.5192,-0.246,3.46,0.8,0.00653,3.46
388,388,"-1*Min(pn_GroupNorm(ts_Decay2(df['p4_ms5'],3),df['lislope']),df['p4_ms3'])","405_-1*Min(pn_GroupNorm(ts_Decay2(df['p4_ms5'],3),df['lislope']),df['p4_ms3'])",11.8003,0.0061,1.0228,3.8055,0.5626565491630633,2024-02-01 22:49:00+00:00,2025-01-30 20:49:00+00:00,0.566,0.45,0.5571,-0.259,2.24,0.52,0.00558,2.51
404,404,"-1*SignedPower(FilterInf(ts_Divide(df['p6_tn8'],43)),df['p2_et9'])","417_-1*SignedPower(FilterInf(ts_Divide(df['p6_tn8'],43)),df['p2_et9'])",11.7691,0.005,1.0139,4.1376,0.3953032490444112,2024-02-01 22:49:00+00:00,2025-01-30 20:49:00+00:00,0.535,0.472,0.5313,-0.092,3.68,0.84,0.00607,3.49
386,386,"-1*pn_CrossFit(IfThen(ts_Product(df['dcperiod'],14),6,19),ts_Delta(df['p6_tn10'],42))","406_-1*pn_CrossFit(IfThen(ts_Product(df['dcperiod'],14),6,19),ts_Delta(df['p6_tn10'],42))",11.7618,0.0066,1.0254,3.5266,0.5930957727609498,2024-02-01 22:49:00+00:00,2025-01-30 20:49:00+00:00,0.436,0.583,0.4279,-0.252,,0.0,,
352,352,"-1*ts_Regression(Divide(Power(df['p2_et15'],31),df['p4_ms0']),ts_Sum(df['p3_mf7'],32),13,'D')","382_-1*ts_Regression(Divide(Power(df['p2_et15'],31),df['p4_ms0']),ts_Sum(df['p3_mf7'],32),13,'D')",11.7295,0.0044,1.0298,3.6325,0.3043279397041123,2024-02-01 22:49:00+00:00,2025-01-30 20:49:00+00:00,0.436,0.587,0.4262,-0.018,3.94,1.01,0.00515,3.61
271,271,"Min(ts_MeanChg(ts_Delta(df['p6_tn13'],1),22),get_CCI(Not(df['p5_to4']),df['p4_ms4'],ts_Delay(df['p3_mf0'],32),44))","314_Min(ts_MeanChg(ts_Delta(df['p6_tn13'],1),22),get_CCI(Not(df['p5_to4']),df['p4_ms4'],ts_Delay(df['p3_mf0'],32),44))",11.6944,0.0053,1.0029,4.1975,0.4524567357267915,2024-02-01 22:49:00+00:00,2025-01-30 20:49:00+00:00,0.582,0.423,0.5791,0.005,1.99,0.43,0.00449,2.1
405,405,"-1*pn_CrossFit(FilterInf(df['p2_et9']),Abs(ts_Product(df['p3_mf1'],21)))","418_-1*pn_CrossFit(FilterInf(df['p2_et9']),Abs(ts_Product(df['p3_mf1'],21)))",11.6621,0.005,1.012,3.8824,0.2099796481565072,2024-02-01 22:49:00+00:00,2025-01-30 20:49:00+00:00,0.553,0.452,0.5502,-0.056,-0.56,-0.15,,
227,227,"-1*Minus(get_CCI(df['p6_tn13'],IfThen(df['cci'],45,39),df['p4_ms5'],26),Exp(get_LINEARREG_SLOPE(df['p4_ms2'],19)))","283_-1*Minus(get_CCI(df['p6_tn13'],IfThen(df['cci'],45,39),df['p4_ms5'],26),Exp(get_LINEARREG_SLOPE(df['p4_ms2'],19)))",11.6431,0.0062,0.9971,4.0877,0.5725849790326762,2024-02-01 22:49:00+00:00,2025-01-30 20:49:00+00:00,0.421,0.57,0.4248,-0.264,2.66,0.58,0.00521,2.65
100,100,"-1*SignedPower(Exp(df['p2_et14']),pn_TransNorm(FilterInf(df['p2_et0'])))","155_-1*SignedPower(Exp(df['p2_et14']),pn_TransNorm(FilterInf(df['p2_et0'])))",11.62,0.0057,0.9877,4.3671,0.5811974999121269,2024-02-01 22:49:00+00:00,2025-01-30 20:49:00+00:00,0.43,0.551,0.4383,-0.187,3.5,0.73,0.00573,3.23
181,181,"-1*pn_GroupNeutral(ts_Rank(df['p3_mf11'],16),ts_Min(df['p2_et15'],6))","237_-1*pn_GroupNeutral(ts_Rank(df['p3_mf11'],16),ts_Min(df['p2_et15'],6))",11.6138,0.0055,0.9907,4.2925,0.5995417098912176,2024-02-01 22:49:00+00:00,2025-01-30 20:49:00+00:00,0.476,0.508,0.4837,-0.166,4.02,0.83,0.00607,3.59
425,425,"-1*ts_Regression(df['p2_et0'],inv(Log(df['p6_tn6'])),46,'C')","434_-1*ts_Regression(df['p2_et0'],inv(Log(df['p6_tn6'])),46,'C')",11.6072,0.0055,1.0041,3.8667,0.510746866995334,2024-02-01 22:49:00+00:00,2025-01-30 20:49:00+00:00,0.438,0.559,0.4393,-0.282,3.13,0.8,0.00631,3.31
275,275,"-1*get_CCI(ts_Mean(df['p5_to1'],25),df['ultosc'],ts_Delay(df['ultosc'],32),45)","318_-1*get_CCI(ts_Mean(df['p5_to1'],25),df['ultosc'],ts_Delay(df['ultosc'],32),45)",11.6049,0.0061,0.9909,4.174,0.5718628285970271,2024-02-01 22:49:00+00:00,2025-01-30 20:49:00+00:00,0.595,0.389,0.6047,-0.194,3.65,0.77,0.00608,3.4
65,65,"-1*ts_Regression(ts_Regression(FilterInf(df['cmo']),get_HT_DCPHASE(df['p2_et2']),10,'D'),get_MINUS_DM(df['p5_to3'],ts_Stdev(df['liangle'],31),21),17,'D')","120_-1*ts_Regression(ts_Regression(FilterInf(df['cmo']),get_HT_DCPHASE(df['p2_et2']),10,'D'),get_MINUS_DM(df['p5_to3'],ts_Stdev(df['liangle'],31),21),17,'D')",11.5844,0.006,0.9959,3.9827,0.5724668551201101,2024-02-01 22:49:00+00:00,2025-01-30 20:49:00+00:00,0.496,0.493,0.5015,-0.151,3.9,0.86,0.00746,3.87
269,269,"-1*Min(ts_Delay(df['p3_mf0'],32),ts_Cov(Abs(df['p4_ms1']),df['ultosc'],43))","313_-1*Min(ts_Delay(df['p3_mf0'],32),ts_Cov(Abs(df['p4_ms1']),df['ultosc'],43))",11.5637,0.0043,1.0126,3.6706,0.5533184995180515,2024-02-01 22:49:00+00:00,2025-01-30 20:49:00+00:00,0.453,0.553,0.4503,-0.033,3.04,0.74,0.00558,3.05
390,390,"-1*Min(pn_GroupNorm(Mthan(df['p2_et1'],df['p5_to7']),ts_Cov2(df['p3_mf11'],df['p3_mf11'],1)),df['p4_ms5'])","403_-1*Min(pn_GroupNorm(Mthan(df['p2_et1'],df['p5_to7']),ts_Cov2(df['p3_mf11'],df['p3_mf11'],1)),df['p4_ms5'])",11.541,0.0059,0.995,3.8908,0.5813737892239828,2024-02-01 22:49:00+00:00,2025-01-30 20:49:00+00:00,0.27,0.718,0.2733,-0.203,2.5,0.56,0.00594,2.72
363,363,"-1*Min(ts_Corr(ts_Product(df['p5_to5'],19),df['p5_to4'],43),df['p4_ms5'])","390_-1*Min(ts_Corr(ts_Product(df['p5_to5'],19),df['p5_to4'],43),df['p4_ms5'])",11.5401,0.0052,0.9838,4.3321,0.4745445566884737,2024-02-01 22:49:00+00:00,2025-01-30 20:49:00+00:00,0.505,0.472,0.5169,-0.122,3.42,0.76,0.00491,3.07
165,165,"Multiply(df['p6_tn10'],pn_CrossFit(ts_Product(df['p2_et10'],7),ts_Product(df['p1_corrs6'],28)))","231_Multiply(df['p6_tn10'],pn_CrossFit(ts_Product(df['p2_et10'],7),ts_Product(df['p1_corrs6'],28)))",11.5352,0.0053,0.9912,4.0722,0.4574714242647666,2024-02-01 22:49:00+00:00,2025-01-30 20:49:00+00:00,0.38,0.613,0.3827,-0.148,2.08,0.54,0.0061,2.59
341,341,"-1*ts_Regression(df['p4_ms5'],pn_CrossFit(Lthan(df['p6_tn10'],df['dx']),ts_Stdev2(df['p3_mf2'],12)),43,'D')","376_-1*ts_Regression(df['p4_ms5'],pn_CrossFit(Lthan(df['p6_tn10'],df['dx']),ts_Stdev2(df['p3_mf2'],12)),43,'D')",11.5026,0.0061,1.0033,3.4989,0.5611312506865034,2024-02-01 22:49:00+00:00,2025-01-30 20:49:00+00:00,0.509,0.488,0.5105,-0.143,3.15,0.82,0.00633,3.34
112,112,"-1*pn_GroupNeutral(ts_Cov2(pn_Rank2(df['p5_to0']),df['p2_et12'],38),ts_ChgRate(df['p1_corrs9'],34))","171_-1*pn_GroupNeutral(ts_Cov2(pn_Rank2(df['p5_to0']),df['p2_et12'],38),ts_ChgRate(df['p1_corrs9'],34))",11.4999,0.0048,0.9941,3.9543,0.4044169267705924,2024-02-01 22:49:00+00:00,2025-01-30 20:49:00+00:00,0.495,0.492,0.5015,-0.054,2.39,0.53,0.00414,2.29
289,289,"pn_GroupRank(df['p2_et11'],get_MINUS_DM(pn_GroupNeutral(df['p2_et4'],df['cmo']),df['p4_ms4'],37))","327_pn_GroupRank(df['p2_et11'],get_MINUS_DM(pn_GroupNeutral(df['p2_et4'],df['cmo']),df['p4_ms4'],37))",11.4828,0.0055,0.9791,4.243,0.4336126768137909,2024-02-01 22:49:00+00:00,2025-01-30 20:49:00+00:00,0.444,0.537,0.4526,-0.236,3.06,0.61,0.00505,2.79
58,58,"-1*pn_GroupRank(SignedPower(df['p5_to0'],ts_Min(df['p1_corrs5'],11)),get_LINEARREG_SLOPE(ts_Cov2(df['p3_mf2'],df['p3_mf10'],0.439),13))","110_-1*pn_GroupRank(SignedPower(df['p5_to0'],ts_Min(df['p1_corrs5'],11)),get_LINEARREG_SLOPE(ts_Cov2(df['p3_mf2'],df['p3_mf10'],0.439),13))",11.4823,0.0051,0.9711,4.5517,0.4725750883719084,2024-02-01 22:49:00+00:00,2025-01-30 20:49:00+00:00,0.346,0.619,0.3585,-0.121,2.74,0.49,0.00437,2.4
320,320,"pn_CrossFit(get_DX(ts_ChgRate(df['dx'],43),ts_Kurtosis(df['dcperiod'],37),df['p2_et10'],20),SignedPower(ts_ChgRate(df['p4_ms2'],34),ts_Min(df['p2_et12'],25)))","357_pn_CrossFit(get_DX(ts_ChgRate(df['dx'],43),ts_Kurtosis(df['dcperiod'],37),df['p2_et10'],20),SignedPower(ts_ChgRate(df['p4_ms2'],34),ts_Min(df['p2_et12'],25)))",11.4688,0.0052,0.9755,4.3581,0.5091676405306582,2024-02-01 22:49:00+00:00,2025-01-30 20:49:00+00:00,0.533,0.444,0.5455,-0.088,2.27,0.45,0.00313,1.95
140,140,"get_CCI(ts_Sum(df['p4_ms2'],9),ts_TransNorm(ts_Skewness(df['p4_ms4'],29),17),Abs(pn_Cut(df['p2_et8'])),25)","202_get_CCI(ts_Sum(df['p4_ms2'],9),ts_TransNorm(ts_Skewness(df['p4_ms4'],29),17),Abs(pn_Cut(df['p2_et8'])),25)",11.4507,0.0053,0.9771,4.2414,0.575099930254533,2024-02-01 22:49:00+00:00,2025-01-30 20:49:00+00:00,0.549,0.43,0.5608,-0.265,2.41,0.47,0.00436,2.26
34,34,"-1*Add(pn_GroupNeutral(ts_Scale(df['p6_tn13'],21),Power(df['cmo'],41)),ts_Min(df['p1_corrs2'],10))","72_-1*Add(pn_GroupNeutral(ts_Scale(df['p6_tn13'],21),Power(df['cmo'],41)),ts_Min(df['p1_corrs2'],10))",11.4126,0.0049,0.9745,4.2643,0.5560969545342194,2024-02-01 22:49:00+00:00,2025-01-30 20:49:00+00:00,0.462,0.506,0.4773,-0.04,3.01,0.6,0.00451,2.66
239,239,"-1*pn_CrossFit(get_MINUS_DI(get_HT_DCPERIOD(df['dcphase']),df['p6_tn1'],ts_StdevChg(df['p2_et9'],34),17),df['p1_corrs5'])","287_-1*pn_CrossFit(get_MINUS_DI(get_HT_DCPERIOD(df['dcphase']),df['p6_tn1'],ts_StdevChg(df['p2_et9'],34),17),df['p1_corrs5'])",11.4078,0.0055,0.9793,4.0157,0.5379374742934115,2024-02-01 22:49:00+00:00,2025-01-30 20:49:00+00:00,0.588,0.384,0.6049,-0.043,2.02,0.43,0.0033,1.87
439,439,"-1*pn_GroupNeutral(ts_Delta(df['p2_et0'],45),ts_StdevChg(get_CMO(df['p2_et17'],6),43))","447_-1*pn_GroupNeutral(ts_Delta(df['p2_et0'],45),ts_StdevChg(get_CMO(df['p2_et17'],6),43))",11.4046,0.0058,0.9706,4.22,0.5116487241072539,2024-02-01 22:49:00+00:00,2025-01-30 20:49:00+00:00,0.367,0.597,0.3807,-0.145,4.36,1.06,0.00709,4.2
8,8,"-1*get_CCI(df['p6_tn0'],ts_Delta(get_CMO(df['p2_et7'],8),26),df['p2_et14'],36)","30_-1*get_CCI(df['p6_tn0'],ts_Delta(get_CMO(df['p2_et7'],8),26),df['p2_et14'],36)",11.385,0.0056,0.9707,4.1886,0.4573178656210649,2024-02-01 22:49:00+00:00,2025-01-30 20:49:00+00:00,0.536,0.428,0.556,-0.345,4.73,0.98,0.00781,4.36
318,318,"pn_CrossFit(get_DX(df['dx'],ts_Cov(df['p6_tn2'],df['p2_et10'],49),Sqrt(df['p6_tn1']),20),SignedPower(ts_ChgRate(df['p4_ms2'],29),ts_Min(df['p2_et12'],8)))","355_pn_CrossFit(get_DX(df['dx'],ts_Cov(df['p6_tn2'],df['p2_et10'],49),Sqrt(df['p6_tn1']),20),SignedPower(ts_ChgRate(df['p4_ms2'],29),ts_Min(df['p2_et12'],8)))",11.3803,0.0058,0.9694,4.1828,0.4830061769121296,2024-02-01 22:49:00+00:00,2025-01-30 20:49:00+00:00,0.574,0.397,0.5911,-0.11,3.23,0.67,0.00513,2.94
235,235,"pn_GroupNeutral(pn_Rank2(pn_CrossFit(df['p2_et7'],df['adosc'])),ts_Mean(pn_Rank2(df['p6_tn4']),40))","284_pn_GroupNeutral(pn_Rank2(pn_CrossFit(df['p2_et7'],df['adosc'])),ts_Mean(pn_Rank2(df['p6_tn4']),40))",11.3702,0.0051,0.9891,3.6795,0.4226032398562546,2024-02-01 22:49:00+00:00,2025-01-30 20:49:00+00:00,0.655,0.335,0.6616,-0.325,2.92,0.66,0.00477,2.75
159,159,"pn_GroupNorm(ts_Rank(df['dm'],36),get_MINUS_DM(df['p1_corrs5'],pn_GroupNeutral(df['p2_et11'],df['p2_et13']),49))","225_pn_GroupNorm(ts_Rank(df['dm'],36),get_MINUS_DM(df['p1_corrs5'],pn_GroupNeutral(df['p2_et11'],df['p2_et13']),49))",11.3552,0.0056,0.9823,3.7641,0.5667720794944408,2024-02-01 22:49:00+00:00,2025-01-30 20:49:00+00:00,0.38,0.604,0.3862,-0.085,3.54,0.79,0.00632,3.43
214,214,"-1*pn_GroupNorm(Divide(ts_Delta(df['p2_et7'],49),0.635),ts_Scale(df['p2_et5'],32))","266_-1*pn_GroupNorm(Divide(ts_Delta(df['p2_et7'],49),0.635),ts_Scale(df['p2_et5'],32))",11.3348,0.0059,0.9874,3.4974,0.583250775810685,2024-02-01 22:49:00+00:00,2025-01-30 20:49:00+00:00,0.509,0.471,0.5194,-0.472,2.65,0.63,0.00622,2.91
326,326,"-1*ts_Rank(get_CCI(df['p2_et16'],df['dm'],ts_Rank(df['cmo'],31),22),11)","362_-1*ts_Rank(get_CCI(df['p2_et16'],df['dm'],ts_Rank(df['cmo'],31),22),11)",11.3137,0.0055,0.9608,4.2899,0.5231077865656403,2024-02-01 22:49:00+00:00,2025-01-30 20:49:00+00:00,0.315,0.639,0.3302,-0.166,3.75,0.76,0.00617,3.43
382,382,"-1*get_CCI(MEthan(ts_Regression(df['p6_tn13'],df['p2_et16'],42,'D'),ts_Regression(df['p3_mf4'],df['p2_et16'],42,'D')),pn_Winsor(df['p2_et4'],50),Power(df['p6_tn13'],3),50)","403_-1*get_CCI(MEthan(ts_Regression(df['p6_tn13'],df['p2_et16'],42,'D'),ts_Regression(df['p3_mf4'],df['p2_et16'],42,'D')),pn_Winsor(df['p2_et4'],50),Power(df['p6_tn13'],3),50)",11.3103,0.0062,0.9666,4.011,0.5931052525930252,2024-02-01 22:49:00+00:00,2025-01-30 20:49:00+00:00,0.429,0.53,0.4473,-0.114,3.27,0.71,0.00588,3.15
1,1,"-1*pn_GroupNeutral(Min(df['p1_corrs5'],ts_TransNorm(df['p2_et9'],1)),get_HT_DCPHASE(ts_ChgRate(df['p2_et7'],29)))","16_-1*pn_GroupNeutral(Min(df['p1_corrs5'],ts_TransNorm(df['p2_et9'],1)),get_HT_DCPHASE(ts_ChgRate(df['p2_et7'],29)))",11.3076,0.0058,0.959,4.2806,0.4425324697813584,2024-02-01 22:49:00+00:00,2025-01-30 20:49:00+00:00,0.421,0.532,0.4418,-0.196,3.08,0.61,0.00562,2.91
329,329,"-1*Min(df['p4_ms1'],get_CMO(ts_Regression(df['p4_ms1'],df['p3_mf4'],13,'C'),2))","365_-1*Min(df['p4_ms1'],get_CMO(ts_Regression(df['p4_ms1'],df['p3_mf4'],13,'C'),2))",11.3069,0.0061,0.9807,3.5874,0.5417535429177269,2024-02-01 22:49:00+00:00,2025-01-30 20:49:00+00:00,0.631,0.343,0.6478,-0.162,2.77,0.68,0.00594,2.96
76,76,"-1*ts_Scale(pn_RankCentered(ts_ChgRate(df['p6_tn4'],12)),48)","130_-1*ts_Scale(pn_RankCentered(ts_ChgRate(df['p6_tn4'],12)),48)",11.2861,0.0059,0.9592,4.1976,0.5964631628233142,2024-02-01 22:49:00+00:00,2025-01-30 20:49:00+00:00,0.556,0.397,0.5834,-0.127,3.45,0.65,0.00588,3.14
117,117,"-1*Min(ts_Decay2(get_CMO(df['p2_et0'],39),38),ts_TransNorm(df['p4_ms1'],13))","174_-1*Min(ts_Decay2(get_CMO(df['p2_et0'],39),38),ts_TransNorm(df['p4_ms1'],13))",11.2841,0.0059,0.9641,4.0372,0.56730413699757,2024-02-01 22:49:00+00:00,2025-01-30 20:49:00+00:00,0.468,0.489,0.489,-0.189,3.11,0.69,0.00664,3.23
38,38,"-1*Add(ts_Skewness(Exp(df['p3_mf3']),11),pn_Stand(df['p4_ms5']))","80_-1*Add(ts_Skewness(Exp(df['p3_mf3']),11),pn_Stand(df['p4_ms5']))",11.2661,0.0062,0.9612,4.0398,0.5145388077344699,2024-02-01 22:49:00+00:00,2025-01-30 20:49:00+00:00,0.429,0.525,0.4497,-0.197,4.3,0.85,0.00666,3.83
391,391,"-1*Min(df['p4_ms5'],ts_Cov(df['p2_et17'],df['p3_mf2'],10))","404_-1*Min(df['p4_ms5'],ts_Cov(df['p2_et17'],df['p3_mf2'],10))",11.2267,0.0057,0.9651,3.8794,0.5109460495227991,2024-02-01 22:49:00+00:00,2025-01-30 20:49:00+00:00,0.529,0.43,0.5516,-0.162,3.23,0.71,0.00662,3.29
168,168,"-1*pn_GroupNorm(ts_Delay(df['p2_et3'],36),inv(df['p4_ms1']))","233_-1*pn_GroupNorm(ts_Delay(df['p2_et3'],36),inv(df['p4_ms1']))",11.1943,0.0042,0.9534,4.3555,0.5010266823635134,2024-02-01 22:49:00+00:00,2025-01-30 20:49:00+00:00,0.487,0.46,0.5143,0.057,3.25,0.68,0.00482,2.9
252,252,"-1*ts_Regression(df['p4_ms1'],get_MINUS_DI(df['p6_tn13'],ts_Decay(df['p6_tn4'],38),df['p5_to2'],35),39,'C')","300_-1*ts_Regression(df['p4_ms1'],get_MINUS_DI(df['p6_tn13'],ts_Decay(df['p6_tn4'],38),df['p5_to2'],35),39,'C')",11.1842,0.0047,0.9723,3.6729,0.5636806036411117,2024-02-01 22:49:00+00:00,2025-01-30 20:49:00+00:00,0.294,0.672,0.3043,-0.109,2.25,0.54,0.00476,2.38
84,84,"get_CCI(get_MINUS_DI(Power(df['p6_tn10'],25),df['p6_tn1'],get_KAMA(df['ultosc'],2),29),Max(df['p2_et4'],Power(df['p6_tn10'],25)),ts_Delay(df['p2_et5'],9),9)","137_get_CCI(get_MINUS_DI(Power(df['p6_tn10'],25),df['p6_tn1'],get_KAMA(df['ultosc'],2),29),Max(df['p2_et4'],Power(df['p6_tn10'],25)),ts_Delay(df['p2_et5'],9),9)",11.1573,0.0053,0.9455,4.315,0.4804149283337045,2024-02-01 22:49:00+00:00,2025-01-30 20:49:00+00:00,0.646,0.301,0.6822,-0.133,3.18,0.59,0.00484,2.77
349,349,"pn_GroupNeutral(ts_Mean(Reverse(df['p4_ms1']),35),pn_RankCentered(ts_Divide(df['p1_corrs4'],17)))","380_pn_GroupNeutral(ts_Mean(Reverse(df['p4_ms1']),35),pn_RankCentered(ts_Divide(df['p1_corrs4'],17)))",11.1552,0.0045,0.9668,3.7938,0.5896419513760013,2024-02-01 22:49:00+00:00,2025-01-30 20:49:00+00:00,0.389,0.579,0.4019,-0.047,1.39,0.32,0.00294,1.45
397,397,"Min(ts_Delta(df['p6_tn6'],22),Sqrt(ts_Entropy(df['p6_tn11'],27)))","412_Min(ts_Delta(df['p6_tn6'],22),Sqrt(ts_Entropy(df['p6_tn11'],27)))",11.1392,0.0059,0.9592,3.7521,0.5328707287925858,2024-02-01 22:49:00+00:00,2025-01-30 20:49:00+00:00,0.573,0.388,0.5963,-0.277,2.43,0.55,0.00558,2.61
124,124,"-1*ts_ChgRate(pn_Rank(df['p2_et7']),42)","180_-1*ts_ChgRate(pn_Rank(df['p2_et7']),42)",11.1328,0.006,0.9536,3.8882,0.5426561426657978,2024-02-01 22:49:00+00:00,2025-01-30 20:49:00+00:00,0.57,0.377,0.6019,-0.518,3.37,0.76,0.00714,3.5
417,417,"-1*pn_GroupRank(pn_GroupNeutral(df['p4_ms1'],df['p2_et13']),pn_FillMin(ts_MeanChg(df['p2_et7'],21)))","428_-1*pn_GroupRank(pn_GroupNeutral(df['p4_ms1'],df['p2_et13']),pn_FillMin(ts_MeanChg(df['p2_et7'],21)))",11.1312,0.0065,0.9694,3.3424,0.5917952481595704,2024-02-01 22:49:00+00:00,2025-01-30 20:49:00+00:00,0.482,0.481,0.5005,-0.169,2.16,0.56,0.00599,2.62
182,182,"-1*Add(pn_GroupNorm(ts_Delay(df['p3_mf6'],36),Or(df['adosc'],df['p3_mf11'])),pn_TransStd(pn_TransStd(df['p3_mf11'])))","238_-1*Add(pn_GroupNorm(ts_Delay(df['p3_mf6'],36),Or(df['adosc'],df['p3_mf11'])),pn_TransStd(pn_TransStd(df['p3_mf11'])))",11.0666,0.005,0.9463,4.061,0.5674329664820619,2024-02-01 22:49:00+00:00,2025-01-30 20:49:00+00:00,0.531,0.409,0.5649,-0.159,2.03,0.5,0.00531,2.36
24,24,"-1*pn_GroupRank(pn_TransStd(ts_Delta(df['p2_et9'],26)),pn_GroupRank(df['p2_et4'],df['p5_to3']))","59_-1*pn_GroupRank(pn_TransStd(ts_Delta(df['p2_et9'],26)),pn_GroupRank(df['p2_et4'],df['p5_to3']))",11.0638,0.0055,0.9468,3.9648,0.4710586013834503,2024-02-01 22:49:00+00:00,2025-01-30 20:49:00+00:00,0.432,0.508,0.4596,-0.194,3.7,0.82,0.00586,3.43
188,188,"-1*Multiply(get_CCI(ts_Entropy(df['p3_mf10'],6),ts_MeanChg(df['dm'],32),df['p6_tn5'],15),IfThen(df['p2_et13'],11,0.674))","243_-1*Multiply(get_CCI(ts_Entropy(df['p3_mf10'],6),ts_MeanChg(df['dm'],32),df['p6_tn5'],15),IfThen(df['p2_et13'],11,0.674))",11.0617,0.0052,0.9371,4.2944,0.4907600231396401,2024-02-01 22:49:00+00:00,2025-01-30 20:49:00+00:00,0.402,0.529,0.4318,-0.105,3.15,0.61,0.00515,2.84
403,403,"-1*pn_CrossFit(ts_Delay(get_HT_DCPERIOD(df['p3_mf3']),11),Min(pn_Stand(df['ultosc']),get_HT_DCPERIOD(df['p5_to4'])))","416_-1*pn_CrossFit(ts_Delay(get_HT_DCPERIOD(df['p3_mf3']),11),Min(pn_Stand(df['ultosc']),get_HT_DCPERIOD(df['p5_to4'])))",11.0474,0.0055,0.936,4.2362,0.5399289482894593,2024-02-01 22:49:00+00:00,2025-01-30 20:49:00+00:00,0.541,0.389,0.5817,-0.198,4.33,0.74,0.00486,3.34
205,205,"-1*Min(MEthan(ts_Rank(df['p3_mf10'],36),pn_FillMax(df['p3_mf9'])),get_LINEARREG_SLOPE(df['p6_tn13'],6))","258_-1*Min(MEthan(ts_Rank(df['p3_mf10'],36),pn_FillMax(df['p3_mf9'])),get_LINEARREG_SLOPE(df['p6_tn13'],6))",11.0438,0.0057,0.9444,3.9442,0.5075578357091783,2024-02-01 22:49:00+00:00,2025-01-30 20:49:00+00:00,0.519,0.419,0.5533,-0.14,2.47,0.53,0.0041,2.31
153,153,"-1*Multiply(pn_GroupRank(ts_Decay2(df['p4_ms5'],50),df['p6_tn7']),ts_Rank(df['p2_et4'],1))","221_-1*Multiply(pn_GroupRank(ts_Decay2(df['p4_ms5'],50),df['p6_tn7']),ts_Rank(df['p2_et4'],1))",11.0432,0.0055,0.9452,3.9442,0.5949512837819463,2024-02-01 22:49:00+00:00,2025-01-30 20:49:00+00:00,0.331,0.608,0.3525,-0.101,3.1,0.72,0.00548,3.03
162,162,"pn_GroupNorm(get_KAMA(ts_Rank(df['dm'],8),25),ts_Rank(ts_Entropy(df['p3_mf8'],25),22))","228_pn_GroupNorm(get_KAMA(ts_Rank(df['dm'],8),25),ts_Rank(ts_Entropy(df['p3_mf8'],25),22))",11.0362,0.0038,0.9538,3.9304,0.4412235753498348,2024-02-01 22:49:00+00:00,2025-01-30 20:49:00+00:00,0.604,0.351,0.6325,-0.041,2.16,0.45,0.00182,1.65
20,20,"-1*Minus(LEthan(LEthan(df['p6_tn13'],df['liangle']),LEthan(df['p6_tn4'],df['p1_corrs9'])),pn_GroupRank(ts_Cov2(df['di'],df['p2_et15'],50),df['cci']))","54_-1*Minus(LEthan(LEthan(df['p6_tn13'],df['liangle']),LEthan(df['p6_tn4'],df['p1_corrs9'])),pn_GroupRank(ts_Cov2(df['di'],df['p2_et15'],50),df['cci']))",10.9705,0.0054,0.9353,4.0366,0.5947707215824042,2024-02-01 22:49:00+00:00,2025-01-30 20:49:00+00:00,0.41,0.518,0.4418,-0.137,2.75,0.59,0.00477,2.61
99,99,"-1*SignedPower(pn_Winsor(inv(df['p5_to2']),2),ts_Delta(df['p6_tn10'],12))","156_-1*SignedPower(pn_Winsor(inv(df['p5_to2']),2),ts_Delta(df['p6_tn10'],12))",10.9612,0.0064,0.9381,3.7818,0.5715490154672416,2024-02-01 22:49:00+00:00,2025-01-30 20:49:00+00:00,0.445,0.486,0.478,-0.175,2.68,0.58,0.00611,2.84
430,430,"pn_GroupRank(ts_Decay(df['p4_ms2'],2),ts_Partial_corr(ts_Median(df['dm'],41),df['p2_et14'],Log(df['p6_tn6']),7))","440_pn_GroupRank(ts_Decay(df['p4_ms2'],2),ts_Partial_corr(ts_Median(df['dm'],41),df['p2_et14'],Log(df['p6_tn6']),7))",10.958,0.0055,0.9427,3.7762,0.5790851576863434,2024-02-01 22:49:00+00:00,2025-01-30 20:49:00+00:00,0.516,0.428,0.5466,-0.239,2.83,0.66,0.0046,2.69
69,69,"-1*pn_GroupNorm(get_LINEARREG_SLOPE(Max(df['p2_et7'],df['p2_et4']),7),ts_Entropy(ts_Entropy(df['p5_to0'],7),46))","124_-1*pn_GroupNorm(get_LINEARREG_SLOPE(Max(df['p2_et7'],df['p2_et4']),7),ts_Entropy(ts_Entropy(df['p5_to0'],7),46))",10.9527,0.0049,0.9489,3.6537,0.5055082083723055,2024-02-01 22:49:00+00:00,2025-01-30 20:49:00+00:00,0.358,0.584,0.38,-0.285,3.76,0.84,0.00543,3.39
0,0,"-1*get_CCI(inv(df['p5_to0']),ts_Corr(Not(df['p6_tn6']),Add(df['p2_et18'],0.542),20),ts_Decay(ts_Scale(df['p6_tn4'],12),49),10)","12_-1*get_CCI(inv(df['p5_to0']),ts_Corr(Not(df['p6_tn6']),Add(df['p2_et18'],0.542),20),ts_Decay(ts_Scale(df['p6_tn4'],12),49),10)",10.9333,0.0057,0.9179,4.4116,0.4091832101252424,2024-02-01 22:49:00+00:00,2025-01-30 20:49:00+00:00,0.505,0.406,0.5543,-0.168,3.91,0.7,0.00625,3.43
104,104,"-1*SignedPower(get_CCI(Minus(df['p2_et8'],0.136),ts_TransNorm(df['p2_et0'],33),get_HT_DCPERIOD(df['p6_tn6']),36),ts_Stdev2(ts_MeanChg(df['p6_tn11'],50),21))","159_-1*SignedPower(get_CCI(Minus(df['p2_et8'],0.136),ts_TransNorm(df['p2_et0'],33),get_HT_DCPERIOD(df['p6_tn6']),36),ts_Stdev2(ts_MeanChg(df['p6_tn11'],50),21))",10.9304,0.0047,0.9282,4.2382,0.1868764132233751,2024-02-01 22:49:00+00:00,2025-01-30 20:49:00+00:00,0.435,0.487,0.4718,-0.034,3.82,0.69,0.00474,3.08
87,87,"-1*Power(get_CCI(Log(df['p1_corrs8']),ts_ChgRate(df['p6_tn7'],26),ts_TransNorm(df['p6_tn7'],6),12),23)","142_-1*Power(get_CCI(Log(df['p1_corrs8']),ts_ChgRate(df['p6_tn7'],26),ts_TransNorm(df['p6_tn7'],6),12),23)",10.9279,0.0046,0.9358,4.0162,0.5111923270856548,2024-02-01 22:49:00+00:00,2025-01-30 20:49:00+00:00,0.426,0.503,0.4586,-0.113,4.8,0.93,0.00687,4.14
137,137,"-1*Multiply(get_DX(pn_Winsor(df['p3_mf11'],44),df['p3_mf11'],ts_Sum(df['p3_mf7'],3),31),pn_Winsor(ts_Delta(df['p2_et0'],42),27))","199_-1*Multiply(get_DX(pn_Winsor(df['p3_mf11'],44),df['p3_mf11'],ts_Sum(df['p3_mf7'],3),31),pn_Winsor(ts_Delta(df['p2_et0'],42),27))",10.9274,0.0053,0.9329,4.0054,0.5276486780776997,2024-02-01 22:49:00+00:00,2025-01-30 20:49:00+00:00,0.302,0.625,0.3258,-0.145,2.58,0.67,0.00483,2.66
270,270,"-1*get_CCI(Min(pn_RankCentered(df['p6_tn13']),Softsign(df['p6_tn2'])),Lthan(Log(df['p1_corrs7']),pn_Rank(df['ultosc'])),Softsign(df['p2_et13']),47)","314_-1*get_CCI(Min(pn_RankCentered(df['p6_tn13']),Softsign(df['p6_tn2'])),Lthan(Log(df['p1_corrs7']),pn_Rank(df['ultosc'])),Softsign(df['p2_et13']),47)",10.9152,0.0053,0.9082,4.7066,0.5492303294494721,2024-02-01 22:49:00+00:00,2025-01-30 20:49:00+00:00,0.402,0.5,0.4457,-0.107,1.89,0.34,0.00326,1.71
296,296,"-1*pn_GroupRank(ts_TransNorm(ts_Delta(df['p6_tn13'],44),28),get_HT_DCPHASE(Minus(df['p1_corrs1'],37)))","334_-1*pn_GroupRank(ts_TransNorm(ts_Delta(df['p6_tn13'],44),28),get_HT_DCPHASE(Minus(df['p1_corrs1'],37)))",10.9151,0.0057,0.9312,3.9611,0.5554000026622757,2024-02-01 22:49:00+00:00,2025-01-30 20:49:00+00:00,0.449,0.475,0.4859,-0.114,2.35,0.46,0.00451,2.26
399,399,"-1*Min(df['p2_et9'],pn_GroupNorm(df['p4_ms5'],df['p4_ms5']))","413_-1*Min(df['p2_et9'],pn_GroupNorm(df['p4_ms5'],df['p4_ms5']))",10.8973,0.0052,0.9278,4.0851,0.5261384677914087,2024-02-01 22:49:00+00:00,2025-01-30 20:49:00+00:00,0.277,0.644,0.3008,-0.164,2.85,0.57,0.00465,2.59
77,77,"-1*ts_Scale(Min(df['p6_tn5'],pn_Stand(df['p6_tn4'])),15)","131_-1*ts_Scale(Min(df['p6_tn5'],pn_Stand(df['p6_tn4'])),15)",10.8745,0.0053,0.9176,4.3062,0.5723338881026651,2024-02-01 22:49:00+00:00,2025-01-30 20:49:00+00:00,0.413,0.498,0.4533,-0.107,3.26,0.62,0.00544,2.95
134,134,"-1*pn_GroupNeutral(pn_Winsor(ts_Delta(df['p4_ms1'],40),44),get_HT_DCPHASE(ts_Decay2(df['p5_to6'],44)))","195_-1*pn_GroupNeutral(pn_Winsor(ts_Delta(df['p4_ms1'],40),44),get_HT_DCPHASE(ts_Decay2(df['p5_to6'],44)))",10.8736,0.005,0.9392,3.6944,0.5837659684415866,2024-02-01 22:49:00+00:00,2025-01-30 20:49:00+00:00,0.481,0.452,0.5155,-0.132,2.72,0.62,0.00564,2.81
25,25,"-1*Add(ts_Max(df['p1_corrs5'],2),pn_GroupNorm(ts_Delta(df['p2_et7'],26),get_CMO(df['p1_corrs8'],19)))","61_-1*Add(ts_Max(df['p1_corrs5'],2),pn_GroupNorm(ts_Delta(df['p2_et7'],26),get_CMO(df['p1_corrs8'],19)))",10.8538,0.0057,0.9463,3.3142,0.5658324042842952,2024-02-01 22:49:00+00:00,2025-01-30 20:49:00+00:00,0.486,0.454,0.517,-0.467,2.81,0.69,0.00638,3.08
86,86,"-1*ts_Scale(get_CCI(inv(df['dm']),pn_CrossFit(df['dcphase'],df['p2_et14']),df['p6_tn7'],36),15)","140_-1*ts_Scale(get_CCI(inv(df['dm']),pn_CrossFit(df['dcphase'],df['p2_et14']),df['p6_tn7'],36),15)",10.8507,0.005,0.934,3.781,0.5268061220962451,2024-02-01 22:49:00+00:00,2025-01-30 20:49:00+00:00,0.449,0.478,0.4844,-0.075,3.67,0.79,0.006,3.41
91,91,"Minus(get_KAMA(ts_Sum(df['p5_to4'],49),10),df['p4_ms5'])","146_Minus(get_KAMA(ts_Sum(df['p5_to4'],49),10),df['p4_ms5'])",10.8482,0.005,0.9315,3.857,0.5851197107176469,2024-02-01 22:49:00+00:00,2025-01-30 20:49:00+00:00,0.61,0.323,0.6538,-0.097,3.2,0.77,0.00447,2.92
90,90,"-1*ts_ChgRate(df['p1_corrs5'],38)","145_-1*ts_ChgRate(df['p1_corrs5'],38)",10.8369,0.0043,0.9282,4.0266,0.4979911154066122,2024-02-01 22:49:00+00:00,2025-01-30 20:49:00+00:00,0.453,0.469,0.4913,-0.024,3.79,0.71,0.00463,3.08
254,254,"-1*ts_Regression(df['p4_ms1'],ts_Argmax(df['p1_corrs7'],28),39,'C')","301_-1*ts_Regression(df['p4_ms1'],ts_Argmax(df['p1_corrs7'],28),39,'C')",10.8333,0.0047,0.9422,3.5278,0.5774852098853266,2024-02-01 22:49:00+00:00,2025-01-30 20:49:00+00:00,0.417,0.518,0.446,-0.114,2.66,0.62,0.00496,2.65
436,436,"Abs(ts_Scale(pn_Stand(df['dm']),11))","445_Abs(ts_Scale(pn_Stand(df['dm']),11))",10.8271,0.0054,0.9117,4.3216,0.5730570881411764,2024-02-01 22:49:00+00:00,2025-01-30 20:49:00+00:00,0.52,0.394,0.5689,-0.044,2.01,0.4,0.00426,2.02
154,154,"-1*Multiply(ts_Corr2(df['p3_mf3'],df['p2_et4'],18),Power(pn_TransNorm(df['p5_to0']),45))","222_-1*Multiply(ts_Corr2(df['p3_mf3'],df['p2_et4'],18),Power(pn_TransNorm(df['p5_to0']),45))",10.7948,0.0054,0.9213,3.9303,0.2642232792371314,2024-02-01 22:49:00+00:00,2025-01-30 20:49:00+00:00,0.425,0.49,0.4645,-0.061,3.98,0.78,0.0059,3.48
139,139,"-1*get_CCI(pn_FillMin(df['p4_ms2']),df['p6_tn2'],df['p6_tn10'],12)","201_-1*get_CCI(pn_FillMin(df['p4_ms2']),df['p6_tn2'],df['p6_tn10'],12)",10.7895,0.0056,0.9243,3.7943,0.5758101098637423,2024-02-01 22:49:00+00:00,2025-01-30 20:49:00+00:00,0.492,0.426,0.5359,-0.113,2.38,0.5,0.00453,2.32
170,170,"-1*pn_GroupRank(pn_Winsor(ts_Divide(df['p6_tn4'],33),41),Not(ts_Mean(df['p5_to3'],11)))","235_-1*pn_GroupRank(pn_Winsor(ts_Divide(df['p6_tn4'],33),41),Not(ts_Mean(df['p5_to3'],11)))",10.7842,0.0054,0.9134,4.1324,0.5977665858456546,2024-02-01 22:49:00+00:00,2025-01-30 20:49:00+00:00,0.498,0.409,0.5491,-0.13,2.98,0.57,0.00498,2.7
26,26,"-1*Add(pn_GroupNeutral(ts_Scale(df['p5_to7'],46),df['p2_et3']),pn_TransStd(ts_Skewness(df['p4_ms5'],40)))","62_-1*Add(pn_GroupNeutral(ts_Scale(df['p5_to7'],46),df['p2_et3']),pn_TransStd(ts_Skewness(df['p4_ms5'],40)))",10.7691,0.0043,0.9251,3.9048,0.5971957716091151,2024-02-01 22:49:00+00:00,2025-01-30 20:49:00+00:00,0.396,0.523,0.4309,-0.025,3.79,0.77,0.00516,3.26
70,70,"-1*FilterInf(Minus(ts_Regression(df['p6_tn11'],df['p2_et13'],24,'B'),0.192))","122_-1*FilterInf(Minus(ts_Regression(df['p6_tn11'],df['p2_et13'],24,'B'),0.192))",10.7688,0.004,0.9341,3.6773,0.3055250863074105,2024-02-01 22:49:00+00:00,2025-01-30 20:49:00+00:00,0.618,0.31,0.6659,-0.03,3.62,0.77,0.00397,2.96
13,13,"-1*Minus(SignedPower(get_CMO(df['dx'],5),get_CMO(df['p3_mf6'],34)),SignedPower(FilterInf(df['p6_tn7']),get_CMO(df['p3_mf6'],36)))","43_-1*Minus(SignedPower(get_CMO(df['dx'],5),get_CMO(df['p3_mf6'],34)),SignedPower(FilterInf(df['p6_tn7']),get_CMO(df['p3_mf6'],36)))",10.7666,0.0059,0.9228,3.7287,0.3829099104830888,2024-02-01 22:49:00+00:00,2025-01-30 20:49:00+00:00,0.637,0.279,0.6954,-0.145,2.87,0.65,0.00559,2.89
39,39,"-1*pn_GroupNorm(Multiply(Add(df['p4_ms5'],df['p2_et17']),FilterInf(df['p6_tn5'])),get_CMO(df['p2_et17'],29))","82_-1*pn_GroupNorm(Multiply(Add(df['p4_ms5'],df['p2_et17']),FilterInf(df['p6_tn5'])),get_CMO(df['p2_et17'],29))",10.7573,0.0053,0.9233,3.7764,0.4801480223160935,2024-02-01 22:49:00+00:00,2025-01-30 20:49:00+00:00,0.48,0.436,0.524,-0.128,1.96,0.6,0.00536,2.48
401,401,"Minus(get_MINUS_DM(df['p2_et16'],df['ultosc'],26),df['ultosc'])","415_Minus(get_MINUS_DM(df['p2_et16'],df['ultosc'],26),df['ultosc'])",10.7539,0.0043,0.9255,3.8551,0.5164565036789067,2024-02-01 22:49:00+00:00,2025-01-30 20:49:00+00:00,0.603,0.324,0.6505,-0.154,3.23,0.67,0.0043,2.77
256,256,"-1*Add(get_LINEARREG_ANGLE(df['p6_tn8'],2),ts_Decay2(get_KAMA(df['p6_tn0'],30),46))","303_-1*Add(get_LINEARREG_ANGLE(df['p6_tn8'],2),ts_Decay2(get_KAMA(df['p6_tn0'],30),46))",10.7393,0.0054,0.9285,3.5566,0.2236639363509772,2024-02-01 22:49:00+00:00,2025-01-30 20:49:00+00:00,0.645,0.277,0.6996,0.02,2.96,0.75,0.00655,3.23
40,40,"-1*pn_GroupNorm(Multiply(Add(df['p4_ms5'],df['p5_to4']),df['p2_et7']),get_MINUS_DM(Min(df['liangle'],0.209),ts_Partial_corr(df['p5_to0'],df['p4_ms3'],df['p6_tn8'],26),26))","82_-1*pn_GroupNorm(Multiply(Add(df['p4_ms5'],df['p5_to4']),df['p2_et7']),get_MINUS_DM(Min(df['liangle'],0.209),ts_Partial_corr(df['p5_to0'],df['p4_ms3'],df['p6_tn8'],26),26))",10.7366,0.0055,0.9315,3.4454,0.3531246105717617,2024-02-01 22:49:00+00:00,2025-01-30 20:49:00+00:00,0.554,0.371,0.5989,-0.146,1.43,0.34,0.00374,1.65
73,73,"-1*pn_GroupNeutral(df['p6_tn10'],ts_StdevChg(ts_Corr2(df['p1_corrs1'],df['p3_mf5'],13),22))","127_-1*pn_GroupNeutral(df['p6_tn10'],ts_StdevChg(ts_Corr2(df['p1_corrs1'],df['p3_mf5'],13),22))",10.7356,0.0064,0.9025,4.1666,0.5290393846274491,2024-02-01 22:49:00+00:00,2025-01-30 20:49:00+00:00,0.513,0.383,0.5725,-0.255,3.08,0.92,0.00743,3.66
169,169,"-1*pn_GroupNorm(get_CMO(df['p6_tn1'],37),df['p6_tn1'])","234_-1*pn_GroupNorm(get_CMO(df['p6_tn1'],37),df['p6_tn1'])",10.7354,0.0056,0.9246,3.6297,0.5414560959539513,2024-02-01 22:49:00+00:00,2025-01-30 20:49:00+00:00,0.507,0.411,0.5523,-0.154,2.68,0.59,0.00619,2.87
185,185,"-1*pn_GroupNeutral(pn_GroupNorm(Lthan(df['p6_tn6'],df['p1_corrs8']),pn_TransNorm(df['p2_et15'])),pn_GroupNorm(ts_Delay(df['p3_mf11'],11),pn_TransNorm(df['p2_et15'])))","240_-1*pn_GroupNeutral(pn_GroupNorm(Lthan(df['p6_tn6'],df['p1_corrs8']),pn_TransNorm(df['p2_et15'])),pn_GroupNorm(ts_Delay(df['p3_mf11'],11),pn_TransNorm(df['p2_et15'])))",10.7281,0.0044,0.9308,3.5958,0.3216409430210467,2024-02-01 22:49:00+00:00,2025-01-30 20:49:00+00:00,0.554,0.371,0.5989,-0.11,2.47,0.56,0.00299,2.12
160,160,"SignedPower(ts_Rank(df['dm'],8),Add(Sqrt(df['p1_corrs5']),df['p1_corrs1']))","226_SignedPower(ts_Rank(df['dm'],8),Add(Sqrt(df['p1_corrs5']),df['p1_corrs1']))",10.727,0.0062,0.9007,4.2369,0.4096333993730617,2024-02-01 22:49:00+00:00,2025-01-30 20:49:00+00:00,0.217,0.685,0.2406,-0.208,3.81,0.79,0.00637,3.53
366,366,"-1*Min(pn_GroupNorm(IfThen(df['p6_tn13'],11,2),df['p4_ms5']),ts_Decay2(df['p4_ms5'],27))","390_-1*Min(pn_GroupNorm(IfThen(df['p6_tn13'],11,2),df['p4_ms5']),ts_Decay2(df['p4_ms5'],27))",10.7109,0.0053,0.9149,3.8971,0.5642202351570351,2024-02-01 22:49:00+00:00,2025-01-30 20:49:00+00:00,0.293,0.616,0.3223,-0.123,1.75,0.37,0.00347,1.74
286,286,"-1*Multiply(pn_GroupRank(df['p2_et0'],df['p6_tn1']),get_MINUS_DI(get_MINUS_DI(df['p6_tn7'],df['p4_ms4'],df['di'],24),pn_TransStd(df['p3_mf5']),pn_TransNorm(df['p4_ms2']),20))","325_-1*Multiply(pn_GroupRank(df['p2_et0'],df['p6_tn1']),get_MINUS_DI(get_MINUS_DI(df['p6_tn7'],df['p4_ms4'],df['di'],24),pn_TransStd(df['p3_mf5']),pn_TransNorm(df['p4_ms2']),20))",10.7076,0.0059,0.8926,4.461,0.4931860911657066,2024-02-01 22:49:00+00:00,2025-01-30 20:49:00+00:00,0.236,0.651,0.2661,-0.091,2.6,0.55,0.00513,2.58
177,177,"-1*pn_GroupRank(Power(pn_Rank(df['p6_tn7']),36),get_CMO(get_LINEARREG_ANGLE(df['p6_tn0'],20),34))","237_-1*pn_GroupRank(Power(pn_Rank(df['p6_tn7']),36),get_CMO(get_LINEARREG_ANGLE(df['p6_tn0'],20),34))",10.7064,0.0057,0.9237,3.5525,0.550009417212984,2024-02-01 22:49:00+00:00,2025-01-30 20:49:00+00:00,0.646,0.271,0.7045,-0.27,2.84,0.62,0.00541,2.8
31,31,"-1*Add(ts_Corr2(df['p6_tn8'],df['p6_tn13'],20),pn_GroupNorm(ts_Scale(df['p2_et0'],19),pn_GroupRank(df['p1_corrs8'],df['p1_corrs3'])))","67_-1*Add(ts_Corr2(df['p6_tn8'],df['p6_tn13'],20),pn_GroupNorm(ts_Scale(df['p2_et0'],19),pn_GroupRank(df['p1_corrs8'],df['p1_corrs3'])))",10.7029,0.0051,0.9055,4.1727,0.5614494839797097,2024-02-01 22:49:00+00:00,2025-01-30 20:49:00+00:00,0.365,0.534,0.406,-0.158,3.3,0.67,0.00525,2.99
268,268,"-1*ts_Scale(ts_Divide(df['ultosc'],9),9)","311_-1*ts_Scale(ts_Divide(df['ultosc'],9),9)",10.7003,0.0047,0.9142,3.9695,0.5022334333188222,2024-02-01 22:49:00+00:00,2025-01-30 20:49:00+00:00,0.488,0.42,0.5374,-0.1,5.39,1.08,0.00764,4.67
158,158,"-1*get_LINEARREG_ANGLE(ts_Mean(ts_Rank(df['p6_tn10'],14),4),3)","225_-1*get_LINEARREG_ANGLE(ts_Mean(ts_Rank(df['p6_tn10'],14),4),3)",10.6998,0.0049,0.9268,3.5562,0.5933955918008034,2024-02-01 22:49:00+00:00,2025-01-30 20:49:00+00:00,0.417,0.503,0.4533,-0.104,2.74,0.63,0.00453,2.61
9,9,"get_CCI(get_MINUS_DI(Multiply(df['p2_et15'],df['p5_to2']),ts_Product(df['p2_et19'],25),Min(df['p2_et15'],24),24),ts_MeanChg(ts_Entropy(df['p3_mf12'],20),28),Log(IfThen(df['p5_to0'],34,46)),25)","33_get_CCI(get_MINUS_DI(Multiply(df['p2_et15'],df['p5_to2']),ts_Product(df['p2_et19'],25),Min(df['p2_et15'],24),24),ts_MeanChg(ts_Entropy(df['p3_mf12'],20),28),Log(IfThen(df['p5_to0'],34,46)),25)",10.6935,0.0053,0.9088,4.0263,0.4964702050308417,2024-02-01 22:49:00+00:00,2025-01-30 20:49:00+00:00,0.498,0.412,0.5473,-0.048,3.51,0.69,0.00567,3.17
218,218,"-1*ts_Rank(get_CCI(df['p2_et5'],df['p6_tn4'],get_KAMA(df['p6_tn4'],3),21),9)","272_-1*ts_Rank(get_CCI(df['p2_et5'],df['p6_tn4'],get_KAMA(df['p6_tn4'],3),21),9)",10.6887,0.0052,0.9055,4.1268,0.5707619085911484,2024-02-01 22:49:00+00:00,2025-01-30 20:49:00+00:00,0.475,0.424,0.5284,-0.079,2.91,0.55,0.00439,2.54
206,206,"-1*Min(pn_TransNorm(Exp(df['p6_tn1'])),get_KAMA(df['p3_mf7'],41))","259_-1*Min(pn_TransNorm(Exp(df['p6_tn1'])),get_KAMA(df['p3_mf7'],41))",10.6883,0.0053,0.9162,3.7803,0.5321877153969794,2024-02-01 22:49:00+00:00,2025-01-30 20:49:00+00:00,0.187,0.722,0.2057,-0.126,1.89,0.45,0.00526,2.24
161,161,"-1*pn_GroupNorm(ts_Mean(df['p6_tn10'],4),ts_Rank(df['p3_mf3'],1))","227_-1*pn_GroupNorm(ts_Mean(df['p6_tn10'],4),ts_Rank(df['p3_mf3'],1))",10.6734,0.0061,0.9239,3.3824,0.5734514791972692,2024-02-01 22:49:00+00:00,2025-01-30 20:49:00+00:00,0.521,0.397,0.5675,-0.454,2.52,0.63,0.00576,2.78
107,107,"-1*pn_GroupRank(ts_Delay(df['p2_et10'],35),pn_TransStd(df['ultosc']))","164_-1*pn_GroupRank(ts_Delay(df['p2_et10'],35),pn_TransStd(df['ultosc']))",10.6663,0.0046,0.9091,4.0312,0.296055713124806,2024-02-01 22:49:00+00:00,2025-01-30 20:49:00+00:00,0.31,0.592,0.3437,0.012,3.9,0.8,0.00613,3.53
288,288,"-1*Power(ts_Divide(df['p6_tn5'],39),31)","326_-1*Power(ts_Divide(df['p6_tn5'],39),31)",10.6646,0.0062,0.9178,3.5348,0.5922970634847267,2024-02-01 22:49:00+00:00,2025-01-30 20:49:00+00:00,0.386,0.526,0.4232,-0.21,3.1,0.68,0.00612,3.11
309,309,"-1*get_CCI(get_LINEARREG_ANGLE(df['p3_mf1'],34),Xor(Sign(df['p3_mf9']),Sign(df['p6_tn11'])),ts_Median(ts_Scale(df['p6_tn10'],10),5),32)","347_-1*get_CCI(get_LINEARREG_ANGLE(df['p3_mf1'],34),Xor(Sign(df['p3_mf9']),Sign(df['p6_tn11'])),ts_Median(ts_Scale(df['p6_tn10'],10),5),32)",10.658,0.006,0.9113,3.7347,0.5304025144688204,2024-02-01 22:49:00+00:00,2025-01-30 20:49:00+00:00,0.581,0.324,0.642,-0.22,3.99,0.81,0.00649,3.64
393,393,"-1*Min(get_KAMA(df['p4_ms5'],35),pn_GroupNorm(ts_TransNorm(df['p4_ms5'],15),df['p6_tn4']))","408_-1*Min(get_KAMA(df['p4_ms5'],35),pn_GroupNorm(ts_TransNorm(df['p4_ms5'],15),df['p6_tn4']))",10.6537,0.0049,0.9168,3.7164,0.5733188879299058,2024-02-01 22:49:00+00:00,2025-01-30 20:49:00+00:00,0.386,0.524,0.4242,-0.089,2.61,0.55,0.00476,2.51
325,325,"-1*ts_Rank(Multiply(pn_GroupRank(df['cmo'],df['lislope']),df['p5_to3']),11)","361_-1*ts_Rank(Multiply(pn_GroupRank(df['cmo'],df['lislope']),df['p5_to3']),11)",10.6493,0.0059,0.9092,3.7816,0.5602026225620977,2024-02-01 22:49:00+00:00,2025-01-30 20:49:00+00:00,0.365,0.538,0.4042,-0.307,3.03,0.62,0.00575,2.93
232,232,"pn_CrossFit(Minus(Not(df['p2_et13']),df['cmo']),Minus(pn_FillMin(df['lislope']),Mthan(df['p6_tn13'],df['p5_to4'])))","287_pn_CrossFit(Minus(Not(df['p2_et13']),df['cmo']),Minus(pn_FillMin(df['lislope']),Mthan(df['p6_tn13'],df['p5_to4'])))",10.637,0.0047,0.9104,3.9004,0.3856686582024331,2024-02-01 22:49:00+00:00,2025-01-30 20:49:00+00:00,0.711,0.201,0.7796,0.26,2.47,0.55,0.00341,2.19
293,293,"-1*pn_GroupRank(ts_Delta(df['p6_tn13'],27),Multiply(pn_GroupNeutral(df['p5_to3'],df['p2_et4']),Lthan(df['p1_corrs1'],df['p6_tn9'])))","332_-1*pn_GroupRank(ts_Delta(df['p6_tn13'],27),Multiply(pn_GroupNeutral(df['p5_to3'],df['p2_et4']),Lthan(df['p1_corrs1'],df['p6_tn9'])))",10.6311,0.0054,0.9047,3.9443,0.5466339006649753,2024-02-01 22:49:00+00:00,2025-01-30 20:49:00+00:00,0.375,0.523,0.4176,-0.096,0.91,0.2,0.00255,1.06
210,210,"-1*Min(ts_Delta(df['p2_et4'],7),Add(get_KAMA(df['p3_mf8'],41),Not(df['p6_tn7'])))","263_-1*Min(ts_Delta(df['p2_et4'],7),Add(get_KAMA(df['p3_mf8'],41),Not(df['p6_tn7'])))",10.6273,0.0054,0.9116,3.7218,0.4739149176915173,2024-02-01 22:49:00+00:00,2025-01-30 20:49:00+00:00,0.444,0.461,0.4906,-0.124,2.45,0.66,0.00556,2.75
192,192,"ts_MeanChg(get_CCI(df['p5_to0'],pn_RankCentered(df['p3_mf8']),MEthan(df['p6_tn10'],df['dx']),10),15)","249_ts_MeanChg(get_CCI(df['p5_to0'],pn_RankCentered(df['p3_mf8']),MEthan(df['p6_tn10'],df['dx']),10),15)",10.6195,0.0065,0.8915,4.1353,0.4536416712299966,2024-02-01 22:49:00+00:00,2025-01-30 20:49:00+00:00,0.554,0.339,0.6204,-0.171,4.02,0.77,0.00655,3.61
200,200,"Add(df['p2_et11'],ts_CovChg(get_LINEARREG_ANGLE(df['cmo'],46),pn_Stand(df['p2_et7']),49))","253_Add(df['p2_et11'],ts_CovChg(get_LINEARREG_ANGLE(df['cmo'],46),pn_Stand(df['p2_et7']),49))",10.6109,0.0043,0.9207,3.5707,0.2224474203956131,2024-02-01 22:49:00+00:00,2025-01-30 20:49:00+00:00,0.468,0.454,0.5076,-0.107,3.32,0.79,0.00511,3.12
183,183,"-1*Add(pn_RankCentered(get_LINEARREG_SLOPE(df['p4_ms3'],11)),ts_Scale(df['p5_to5'],5))","239_-1*Add(pn_RankCentered(get_LINEARREG_SLOPE(df['p4_ms3'],11)),ts_Scale(df['p5_to5'],5))",10.6081,0.0047,0.8944,4.2846,0.3774541009367566,2024-02-01 22:49:00+00:00,2025-01-30 20:49:00+00:00,0.522,0.366,0.5878,-0.158,3.67,0.66,0.00533,3.11
15,15,"-1*Minus(get_MINUS_DM(df['p2_et13'],pn_TransNorm(df['p3_mf8']),49),SignedPower(FilterInf(df['p6_tn7']),get_CMO(df['p6_tn5'],29)))","45_-1*Minus(get_MINUS_DM(df['p2_et13'],pn_TransNorm(df['p3_mf8']),49),SignedPower(FilterInf(df['p6_tn7']),get_CMO(df['p6_tn5'],29)))",10.6066,0.0059,0.9052,3.7859,0.5401935144104903,2024-02-01 22:49:00+00:00,2025-01-30 20:49:00+00:00,0.442,0.456,0.4922,-0.18,2.16,0.43,0.00374,2.01
331,331,"-1*Min(df['p3_mf8'],get_CMO(pn_GroupRank(df['p4_ms1'],df['p2_et4']),35))","367_-1*Min(df['p3_mf8'],get_CMO(pn_GroupRank(df['p4_ms1'],df['p2_et4']),35))",10.5998,0.0048,0.9159,3.6061,0.5834877336656883,2024-02-01 22:49:00+00:00,2025-01-30 20:49:00+00:00,0.385,0.524,0.4235,-0.126,2.21,0.48,0.0045,2.24
141,141,"get_CCI(Mthan(get_HT_DCPERIOD(df['p2_et17']),df['p6_tn5']),df['p4_ms2'],pn_Stand(get_HT_DCPERIOD(df['p2_et17'])),48)","202_get_CCI(Mthan(get_HT_DCPERIOD(df['p2_et17']),df['p6_tn5']),df['p4_ms2'],pn_Stand(get_HT_DCPERIOD(df['p2_et17'])),48)",10.5707,0.0054,0.894,4.0866,0.4844751421695643,2024-02-01 22:49:00+00:00,2025-01-30 20:49:00+00:00,0.537,0.358,0.6,-0.109,1.59,0.33,0.00356,1.65
144,144,"-1*Min(df['p2_et0'],ts_TransNorm(df['p3_mf8'],14))","208_-1*Min(df['p2_et0'],ts_TransNorm(df['p3_mf8'],14))",10.563,0.0045,0.9091,3.7439,0.423305992028282,2024-02-01 22:49:00+00:00,2025-01-30 20:49:00+00:00,0.381,0.522,0.4219,-0.184,3.06,0.73,0.00433,2.8
411,411,"-1*pn_GroupNeutral(df['p4_ms1'],get_LINEARREG_ANGLE(df['p4_ms1'],5))","422_-1*pn_GroupNeutral(df['p4_ms1'],get_LINEARREG_ANGLE(df['p4_ms1'],5))",10.5504,0.0054,0.9194,3.2585,0.4338275437075135,2024-02-01 22:49:00+00:00,2025-01-30 20:49:00+00:00,0.464,0.449,0.5082,-0.146,2.09,0.51,0.0053,2.39
330,330,"-1*Min(df['p4_ms1'],get_CMO(Reverse(df['dm']),2))","366_-1*Min(df['p4_ms1'],get_CMO(Reverse(df['dm']),2))",10.5469,0.0065,0.9113,3.3326,0.5876314581639009,2024-02-01 22:49:00+00:00,2025-01-30 20:49:00+00:00,0.526,0.379,0.5812,-0.105,2.95,0.74,0.00735,3.38
383,383,"-1*get_CCI(Sqrt(ts_Regression(df['p6_tn13'],df['p2_et16'],42,'D')),IfThen(df['p5_to0'],29,32),get_HT_DCPERIOD(pn_FillMax(df['di'])),50)","403_-1*get_CCI(Sqrt(ts_Regression(df['p6_tn13'],df['p2_et16'],42,'D')),IfThen(df['p5_to0'],29,32),get_HT_DCPERIOD(pn_FillMax(df['di'])),50)",10.5429,0.0053,0.8902,4.1221,0.5280325479338712,2024-02-01 22:49:00+00:00,2025-01-30 20:49:00+00:00,0.431,0.453,0.4876,-0.113,2.55,0.51,0.00392,2.27
35,35,"-1*Max(FilterInf(df['p2_et0']),Multiply(ts_Delta(df['p6_tn13'],30),df['p2_et10']))","75_-1*Max(FilterInf(df['p2_et0']),Multiply(ts_Delta(df['p6_tn13'],30),df['p2_et10']))",10.542,0.0057,0.897,3.8632,0.5993401088843385,2024-02-01 22:49:00+00:00,2025-01-30 20:49:00+00:00,0.532,0.358,0.5978,-0.172,2.42,0.59,0.00517,2.58
225,225,"Minus(Sign(df['p1_corrs4']),ts_TransNorm(df['p6_tn13'],9))","281_Minus(Sign(df['p1_corrs4']),ts_TransNorm(df['p6_tn13'],9))",10.5384,0.0053,0.8988,3.8552,0.5390936398383827,2024-02-01 22:49:00+00:00,2025-01-30 20:49:00+00:00,0.403,0.497,0.4478,-0.113,3.61,0.72,0.00572,3.25
234,234,"-1*pn_GroupNeutral(pn_Winsor(pn_Rank2(df['p2_et7']),40),pn_Winsor(df['p2_et7'],25))","283_-1*pn_GroupNeutral(pn_Winsor(pn_Rank2(df['p2_et7']),40),pn_Winsor(df['p2_et7'],25))",10.5319,0.0045,0.8826,4.4376,0.5643101315236354,2024-02-01 22:49:00+00:00,2025-01-30 20:49:00+00:00,0.43,0.446,0.4909,-0.137,1.67,0.27,0.00215,1.32
17,17,"ts_Rank(SignedPower(FilterInf(df['dm']),get_CMO(df['p6_tn5'],36)),12)","49_ts_Rank(SignedPower(FilterInf(df['dm']),get_CMO(df['p6_tn5'],36)),12)",10.5269,0.0056,0.8911,4.0108,0.4791604352160715,2024-02-01 22:49:00+00:00,2025-01-30 20:49:00+00:00,0.341,0.552,0.3819,-0.11,2.77,0.53,0.00472,2.53
240,240,"-1*pn_GroupNeutral(ts_Sum(pn_Stand(df['ultosc']),0.141),ts_Scale(df['p2_et16'],2))","288_-1*pn_GroupNeutral(ts_Sum(pn_Stand(df['ultosc']),0.141),ts_Scale(df['p2_et16'],2))",10.5157,0.0047,0.9105,3.5294,0.5745877224525838,2024-02-01 22:49:00+00:00,2025-01-30 20:49:00+00:00,0.62,0.284,0.6858,-0.391,2.46,0.59,0.00381,2.32
265,265,"get_CCI(IfThen(df['p3_mf1'],36,18),df['dm'],ts_Scale(df['di'],8),45)","310_get_CCI(IfThen(df['p3_mf1'],36,18),df['dm'],ts_Scale(df['di'],8),45)",10.4965,0.006,0.8948,3.7397,0.5864554808250966,2024-02-01 22:49:00+00:00,2025-01-30 20:49:00+00:00,0.261,0.635,0.2913,-0.235,3.11,0.66,0.00629,3.12
308,308,"-1*get_CCI(df['p4_ms5'],pn_FillMax(ts_Median(df['p4_ms0'],34)),Abs(Power(df['p2_et4'],41)),23)","346_-1*get_CCI(df['p4_ms5'],pn_FillMax(ts_Median(df['p4_ms0'],34)),Abs(Power(df['p2_et4'],41)),23)",10.495,0.0053,0.9162,3.2038,0.4933831939589722,2024-02-01 22:49:00+00:00,2025-01-30 20:49:00+00:00,0.433,0.477,0.4758,-0.091,1.37,0.36,0.00329,1.56
110,110,"get_MINUS_DM(get_MINUS_DM(df['liangle'],df['p5_to7'],49),pn_CrossFit(ts_Median(df['p2_et17'],49),get_MINUS_DM(df['p2_et17'],df['p5_to7'],12)),37)","167_get_MINUS_DM(get_MINUS_DM(df['liangle'],df['p5_to7'],49),pn_CrossFit(ts_Median(df['p2_et17'],49),get_MINUS_DM(df['p2_et17'],df['p5_to7'],12)),37)",10.482,0.004,0.8894,4.1685,0.5192122602799556,2024-02-01 22:49:00+00:00,2025-01-30 20:49:00+00:00,0.639,0.252,0.7172,0.015,4.13,1.02,0.00533,3.72
313,313,"-1*get_CCI(ts_Product(df['p2_et4'],13),ts_TransNorm(df['p6_tn1'],42),ts_Decay(df['p3_mf4'],7),42)","351_-1*get_CCI(ts_Product(df['p2_et4'],13),ts_TransNorm(df['p6_tn1'],42),ts_Decay(df['p3_mf4'],7),42)",10.4819,0.0059,0.8975,3.6333,0.5997981435455687,2024-02-01 22:49:00+00:00,2025-01-30 20:49:00+00:00,0.353,0.538,0.3962,-0.196,1.68,0.35,0.00415,1.83
68,68,"-1*pn_GroupNorm(get_LINEARREG_ANGLE(df['p2_et4'],3),ts_Entropy(df['p6_tn12'],46))","123_-1*pn_GroupNorm(get_LINEARREG_ANGLE(df['p2_et4'],3),ts_Entropy(df['p6_tn12'],46))",10.4817,0.0048,0.9079,3.4858,0.5148759208955407,2024-02-01 22:49:00+00:00,2025-01-30 20:49:00+00:00,0.367,0.534,0.4073,-0.016,3.05,0.67,0.00511,2.88
230,230,"-1*pn_GroupNorm(pn_TransStd(ts_Max(df['p6_tn1'],2)),pn_TransStd(ts_Stdev(df['p4_ms6'],17)))","286_-1*pn_GroupNorm(pn_TransStd(ts_Max(df['p6_tn1'],2)),pn_TransStd(ts_Stdev(df['p4_ms6'],17)))",10.4782,0.0046,0.9015,3.6932,0.5350831375724197,2024-02-01 22:49:00+00:00,2025-01-30 20:49:00+00:00,0.552,0.343,0.6168,-0.253,3.76,0.79,0.00496,3.23
132,132,"-1*Multiply(ts_TransNorm(ts_Divide(df['p4_ms4'],17),11),df['p6_tn13'])","195_-1*Multiply(ts_TransNorm(ts_Divide(df['p4_ms4'],17),11),df['p6_tn13'])",10.4702,0.0041,0.9015,3.7532,0.2410067399798161,2024-02-01 22:49:00+00:00,2025-01-30 20:49:00+00:00,0.391,0.504,0.4369,-0.041,2.7,0.54,0.00373,2.32
129,129,"-1*get_CCI(pn_RankCentered(df['cmo']),ts_Decay(df['p3_mf12'],50),ts_Delta(df['p6_tn13'],12),40)","188_-1*get_CCI(pn_RankCentered(df['cmo']),ts_Decay(df['p3_mf12'],50),ts_Delta(df['p6_tn13'],12),40)",10.4629,0.0053,0.8901,3.8839,0.5492805227724645,2024-02-01 22:49:00+00:00,2025-01-30 20:49:00+00:00,0.483,0.401,0.5464,-0.125,2.93,0.63,0.00549,2.86
36,36,"-1*Max(ts_TransNorm(pn_TransStd(df['p5_to6']),11),df['p2_et7'])","77_-1*Max(ts_TransNorm(pn_TransStd(df['p5_to6']),11),df['p2_et7'])",10.4603,0.0048,0.8653,4.706,0.2530742005292603,2024-02-01 22:49:00+00:00,2025-01-30 20:49:00+00:00,0.562,0.297,0.6542,-0.256,3.23,0.57,0.00445,2.68
193,193,"ts_MeanChg(pn_TransNorm(pn_TransNorm(df['p5_to0'])),10)","250_ts_MeanChg(pn_TransNorm(pn_TransNorm(df['p5_to0'])),10)",10.4483,0.0054,0.8881,3.8923,0.517066726483515,2024-02-01 22:49:00+00:00,2025-01-30 20:49:00+00:00,0.395,0.495,0.4438,-0.237,4.0,0.8,0.00584,3.5
47,47,"-1*Multiply(df['p6_tn1'],Multiply(ts_Cov(df['p6_tn8'],df['p4_ms1'],36),0.739))","96_-1*Multiply(df['p6_tn1'],Multiply(ts_Cov(df['p6_tn8'],df['p4_ms1'],36),0.739))",10.4357,0.0042,0.8966,3.7771,0.2407581688561696,2024-02-01 22:49:00+00:00,2025-01-30 20:49:00+00:00,0.333,0.557,0.3742,-0.07,2.22,0.46,0.00356,2.03
414,414,"-1*Min(Reverse(ts_Delta(df['p2_et11'],25)),df['p4_ms5'])","425_-1*Min(Reverse(ts_Delta(df['p2_et11'],25)),df['p4_ms5'])",10.4292,0.0057,0.8915,3.6857,0.5244748214160605,2024-02-01 22:49:00+00:00,2025-01-30 20:49:00+00:00,0.573,0.312,0.6475,-0.241,3.25,0.73,0.00595,3.19
262,262,"Add(LEthan(df['p5_to0'],SignedPower(df['p3_mf6'],0.905)),get_MINUS_DI(ts_Decay(df['p3_mf0'],44),df['p4_ms6'],df['p1_corrs9'],35))","307_Add(LEthan(df['p5_to0'],SignedPower(df['p3_mf6'],0.905)),get_MINUS_DI(ts_Decay(df['p3_mf0'],44),df['p4_ms6'],df['p1_corrs9'],35))",10.4285,0.0045,0.8687,4.5472,0.4202775663065596,2024-02-01 22:49:00+00:00,2025-01-30 20:49:00+00:00,0.343,0.527,0.3943,-0.093,2.96,0.53,0.00408,2.47
435,435,"-1*pn_RankCentered(ts_Delta(df['p2_et0'],10))","444_-1*pn_RankCentered(ts_Delta(df['p2_et0'],10))",10.4255,0.005,0.8897,3.8305,0.5316837032535592,2024-02-01 22:49:00+00:00,2025-01-30 20:49:00+00:00,0.385,0.498,0.436,-0.151,3.17,0.83,0.00554,3.2
186,186,"ts_MeanChg(get_CCI(df['p4_ms0'],UnEqual(df['p6_tn5'],df['p1_corrs1']),pn_TransNorm(df['ultosc']),15),10)","241_ts_MeanChg(get_CCI(df['p4_ms0'],UnEqual(df['p6_tn5'],df['p1_corrs1']),pn_TransNorm(df['ultosc']),15),10)",10.4227,0.0053,0.8731,4.2834,0.5608325730496646,2024-02-01 22:49:00+00:00,2025-01-30 20:49:00+00:00,0.488,0.387,0.5577,-0.206,4.3,0.77,0.00621,3.64
231,231,"-1*ts_Cov2(df['p2_et3'],ts_Scale(df['ultosc'],42),40)","288_-1*ts_Cov2(df['p2_et3'],ts_Scale(df['ultosc'],42),40)",10.4158,0.0045,0.9019,3.5122,0.4483587154033022,2024-02-01 22:49:00+00:00,2025-01-30 20:49:00+00:00,0.426,0.469,0.476,-0.01,2.75,0.63,0.00498,2.7
277,277,"-1*get_CCI(get_MINUS_DM(df['p3_mf6'],df['p2_et9'],49),df['p2_et10'],get_CMO(get_CMO(df['p5_to0'],16),2),30)","319_-1*get_CCI(get_MINUS_DM(df['p3_mf6'],df['p2_et9'],49),df['p2_et10'],get_CMO(get_CMO(df['p5_to0'],16),2),30)",10.3884,0.005,0.8877,3.7834,0.5823837613290116,2024-02-01 22:49:00+00:00,2025-01-30 20:49:00+00:00,0.499,0.383,0.5658,-0.034,2.97,0.6,0.00489,2.72
150,150,"pn_GroupNeutral(get_LINEARREG_SLOPE(df['p2_et8'],18),Add(df['dx'],get_KAMA(df['p5_to1'],2)))","215_pn_GroupNeutral(get_LINEARREG_SLOPE(df['p2_et8'],18),Add(df['dx'],get_KAMA(df['p5_to1'],2)))",10.3793,0.0036,0.9093,3.3221,0.3642640087043579,2024-02-01 22:49:00+00:00,2025-01-30 20:49:00+00:00,0.497,0.414,0.5456,0.023,2.25,0.54,0.00268,1.96
444,444,"pn_GroupNeutral(Reverse(df['p6_tn5']),get_LINEARREG_ANGLE(df['cci'],4))","451_pn_GroupNeutral(Reverse(df['p6_tn5']),get_LINEARREG_ANGLE(df['cci'],4))",10.3777,0.006,0.8913,3.4944,0.594822152104357,2024-02-01 22:49:00+00:00,2025-01-30 20:49:00+00:00,0.428,0.465,0.4793,-0.22,2.43,0.54,0.0057,2.62
373,373,"get_CCI(ts_Delay(df['p6_tn8'],8),Reverse(df['p2_et4']),pn_FillMin(ts_Delay(df['p6_tn8'],37)),42)","394_get_CCI(ts_Delay(df['p6_tn8'],8),Reverse(df['p2_et4']),pn_FillMin(ts_Delay(df['p6_tn8'],37)),42)",10.3667,0.0055,0.8921,3.5133,0.5085205081524418,2024-02-01 22:49:00+00:00,2025-01-30 20:49:00+00:00,0.334,0.56,0.3736,-0.125,3.41,0.79,0.00666,3.46
356,356,"pn_GroupNeutral(get_DX(df['p2_et4'],pn_Winsor(df['p3_mf7'],2),df['p6_tn4'],16),ts_CorrChg(get_CMO(df['p6_tn4'],16),ts_Quantile(df['p3_mf7'],46,'A'),16))","384_pn_GroupNeutral(get_DX(df['p2_et4'],pn_Winsor(df['p3_mf7'],2),df['p6_tn4'],16),ts_CorrChg(get_CMO(df['p6_tn4'],16),ts_Quantile(df['p3_mf7'],46,'A'),16))",10.3641,0.0038,0.878,4.1838,0.3035338322541273,2024-02-01 22:49:00+00:00,2025-01-30 20:49:00+00:00,0.437,0.443,0.4966,-0.046,2.93,0.61,0.00323,2.39
440,440,"-1*pn_GroupNeutral(ts_Decay(pn_TransStd(df['p4_ms5']),38),ts_Divide(get_HT_DCPHASE(df['p5_to7']),3))","448_-1*pn_GroupNeutral(ts_Decay(pn_TransStd(df['p4_ms5']),38),ts_Divide(get_HT_DCPHASE(df['p5_to7']),3))",10.3637,0.0043,0.9043,3.3139,0.5969808910403429,2024-02-01 22:49:00+00:00,2025-01-30 20:49:00+00:00,0.424,0.474,0.4722,-0.124,1.87,0.45,0.00353,1.89
376,376,"-1*Add(ts_Corr(Max(df['p2_et12'],df['p4_ms0']),df['p6_tn7'],28),get_CCI(ts_Min(df['p2_et1'],36),ts_Regression(df['cci'],df['dm'],41,'C'),pn_FillMin(df['p1_corrs6']),22))","397_-1*Add(ts_Corr(Max(df['p2_et12'],df['p4_ms0']),df['p6_tn7'],28),get_CCI(ts_Min(df['p2_et1'],36),ts_Regression(df['cci'],df['dm'],41,'C'),pn_FillMin(df['p1_corrs6']),22))",10.3633,0.0056,0.8921,3.4844,0.4871207838888088,2024-02-01 22:49:00+00:00,2025-01-30 20:49:00+00:00,0.475,0.411,0.5361,-0.238,3.39,0.73,0.00631,3.3
335,335,"-1*Add(df['p2_et9'],ts_Argmin(FilterInf(df['p1_corrs2']),0.792))","371_-1*Add(df['p2_et9'],ts_Argmin(FilterInf(df['p1_corrs2']),0.792))",10.3587,0.0056,0.8812,3.8061,0.5795719968546307,2024-02-01 22:49:00+00:00,2025-01-30 20:49:00+00:00,0.459,0.415,0.5252,-0.178,2.97,0.65,0.00519,2.84
138,138,"-1*pn_GroupNeutral(pn_Winsor(ts_Delta(df['p2_et0'],40),32),Power(df['p5_to4'],35))","200_-1*pn_GroupNeutral(pn_Winsor(ts_Delta(df['p2_et0'],40),32),Power(df['p5_to4'],35))",10.351,0.0052,0.8833,3.7819,0.585042597068144,2024-02-01 22:49:00+00:00,2025-01-30 20:49:00+00:00,0.363,0.514,0.4139,-0.145,3.3,0.87,0.00649,3.49
327,327,"-1*Min(ts_Decay(Multiply(df['p3_mf9'],df['p1_corrs3']),44),pn_GroupNorm(df['ultosc'],ts_Sum(df['p3_mf7'],6)))","363_-1*Min(ts_Decay(Multiply(df['p3_mf9'],df['p1_corrs3']),44),pn_GroupNorm(df['ultosc'],ts_Sum(df['p3_mf7'],6)))",10.3466,0.0047,0.8891,3.6634,0.4976893669827289,2024-02-01 22:49:00+00:00,2025-01-30 20:49:00+00:00,0.382,0.501,0.4326,-0.264,3.86,0.81,0.00626,3.55
281,281,"-1*Max(Abs(pn_Rank(df['p4_ms1'])),ts_Skewness(ts_Decay2(df['p4_ms1'],6),40))","323_-1*Max(Abs(pn_Rank(df['p4_ms1'])),ts_Skewness(ts_Decay2(df['p4_ms1'],6),40))",10.3445,0.0051,0.8989,3.3086,0.499904843233327,2024-02-01 22:49:00+00:00,2025-01-30 20:49:00+00:00,0.496,0.396,0.5561,-0.169,2.81,0.65,0.00547,2.84
126,126,"-1*pn_Stand(ts_ChgRate(ts_Decay2(df['p6_tn4'],0.232),29))","184_-1*pn_Stand(ts_ChgRate(ts_Decay2(df['p6_tn4'],0.232),29))",10.3445,0.005,0.8786,3.9321,0.597064285158721,2024-02-01 22:49:00+00:00,2025-01-30 20:49:00+00:00,0.5,0.372,0.5734,-0.127,2.95,0.57,0.00473,2.64
273,273,"-1*get_CCI(df['cmo'],pn_Rank(Log(df['p1_corrs5'])),ts_Argmin(pn_Rank(df['p6_tn2']),48),7)","316_-1*get_CCI(df['cmo'],pn_Rank(Log(df['p1_corrs5'])),ts_Argmin(pn_Rank(df['p6_tn2']),48),7)",10.3438,0.0057,0.8804,3.7605,0.5663853809728756,2024-02-01 22:49:00+00:00,2025-01-30 20:49:00+00:00,0.487,0.387,0.5572,-0.206,3.59,0.8,0.00683,3.56
209,209,"-1*Min(ts_Delta(Min(df['p4_ms5'],0.399),36),ts_Partial_corr(df['p6_tn13'],df['cmo'],df['dx'],12))","261_-1*Min(ts_Delta(Min(df['p4_ms5'],0.399),36),ts_Partial_corr(df['p6_tn13'],df['cmo'],df['dx'],12))",10.3159,0.0046,0.8907,3.54,0.4449458353098771,2024-02-01 22:49:00+00:00,2025-01-30 20:49:00+00:00,0.432,0.453,0.4881,-0.117,1.41,0.31,0.0033,1.52
66,66,"-1*pn_GroupNorm(get_LINEARREG_SLOPE(df['p3_mf11'],7),Add(ts_Corr(df['p3_mf5'],df['p2_et10'],44),df['dcperiod']))","121_-1*pn_GroupNorm(get_LINEARREG_SLOPE(df['p3_mf11'],7),Add(ts_Corr(df['p3_mf5'],df['p2_et10'],44),df['dcperiod']))",10.313,0.0048,0.8749,3.9787,0.4994495050365176,2024-02-01 22:49:00+00:00,2025-01-30 20:49:00+00:00,0.334,0.535,0.3843,-0.215,4.29,0.91,0.00633,3.83
380,380,"-1*get_CCI(ts_TransNorm(df['p4_ms1'],30),ts_Scale(df['p5_to0'],31),ts_MeanChg(pn_Stand(df['p1_corrs3']),15),49)","400_-1*get_CCI(ts_TransNorm(df['p4_ms1'],30),ts_Scale(df['p5_to0'],31),ts_MeanChg(pn_Stand(df['p1_corrs3']),15),49)",10.3076,0.0057,0.883,3.5829,0.5937056054733137,2024-02-01 22:49:00+00:00,2025-01-30 20:49:00+00:00,0.571,0.305,0.6518,-0.114,2.73,0.61,0.0049,2.65
95,95,"ts_Scale(get_CCI(pn_FillMax(df['p6_tn12']),pn_TransStd(df['p6_tn6']),Sqrt(df['p1_corrs4']),48),34)","149_ts_Scale(get_CCI(pn_FillMax(df['p6_tn12']),pn_TransStd(df['p6_tn6']),Sqrt(df['p1_corrs4']),48),34)",10.3048,0.0048,0.8818,3.7448,0.5218108112072176,2024-02-01 22:49:00+00:00,2025-01-30 20:49:00+00:00,0.474,0.409,0.5368,-0.19,0.94,0.21,,
22,22,"Minus(pn_GroupRank(ts_Skewness(df['p5_to7'],32),ts_Product(df['p1_corrs3'],29)),pn_GroupRank(df['p4_ms5'],ts_Rank(df['p4_ms4'],20)))","56_Minus(pn_GroupRank(ts_Skewness(df['p5_to7'],32),ts_Product(df['p1_corrs3'],29)),pn_GroupRank(df['p4_ms5'],ts_Rank(df['p4_ms4'],20)))",10.3038,0.0053,0.8626,4.2347,0.5219529566597838,2024-02-01 22:49:00+00:00,2025-01-30 20:49:00+00:00,0.379,0.485,0.4387,-0.132,2.79,0.56,0.00518,2.67
115,115,"-1*Min(Add(Sign(df['p2_et2']),df['p3_mf3']),ts_Scale(ts_Scale(df['p5_to0'],0.217),16))","172_-1*Min(Add(Sign(df['p2_et2']),df['p3_mf3']),ts_Scale(ts_Scale(df['p5_to0'],0.217),16))",10.2854,0.0055,0.8595,4.2458,0.5908745163278176,2024-02-01 22:49:00+00:00,2025-01-30 20:49:00+00:00,0.339,0.514,0.3974,-0.089,3.6,0.86,0.00587,3.45
371,371,"-1*pn_GroupNeutral(pn_GroupNeutral(pn_GroupNorm(df['p6_tn7'],df['dcphase']),pn_TransNorm(df['p1_corrs8'])),ts_Median(df['kama'],33))","393_-1*pn_GroupNeutral(pn_GroupNeutral(pn_GroupNorm(df['p6_tn7'],df['dcphase']),pn_TransNorm(df['p1_corrs8'])),ts_Median(df['kama'],33))",10.284,0.0053,0.8791,3.6862,0.5939784260010252,2024-02-01 22:49:00+00:00,2025-01-30 20:49:00+00:00,0.541,0.332,0.6197,-0.304,2.21,0.44,0.00446,2.18
21,21,"Minus(pn_GroupNeutral(LEthan(df['p2_et12'],df['di']),pn_TransStd(df['p3_mf3'])),ts_Delay(FilterInf(df['p4_ms5']),36))","55_Minus(pn_GroupNeutral(LEthan(df['p2_et12'],df['di']),pn_TransStd(df['p3_mf3'])),ts_Delay(FilterInf(df['p4_ms5']),36))",10.2785,0.0041,0.8799,3.8248,0.0712877540610195,2024-02-01 22:49:00+00:00,2025-01-30 20:49:00+00:00,0.402,0.479,0.4563,0.008,4.04,0.91,0.00545,3.57
114,114,"-1*pn_GroupRank(ts_Decay2(get_CMO(df['p2_et0'],39),38),get_LINEARREG_ANGLE(df['di'],26))","170_-1*pn_GroupRank(ts_Decay2(get_CMO(df['p2_et0'],39),38),get_LINEARREG_ANGLE(df['di'],26))",10.2764,0.0044,0.8722,4.0039,0.5376743831557768,2024-02-01 22:49:00+00:00,2025-01-30 20:49:00+00:00,0.458,0.408,0.5289,-0.146,2.31,0.44,0.0035,2.02
125,125,"-1*get_CCI(df['p2_et11'],df['cmo'],Sign(df['p5_to2']),3)","183_-1*get_CCI(df['p2_et11'],df['cmo'],Sign(df['p5_to2']),3)",10.2645,0.0053,0.8845,3.459,0.5929850477289726,2024-02-01 22:49:00+00:00,2025-01-30 20:49:00+00:00,0.42,0.458,0.4784,-0.141,2.86,0.63,0.00559,2.86
257,257,"-1*Add(ts_Decay2(df['p1_corrs0'],42),get_LINEARREG_SLOPE(df['p6_tn4'],13))","303_-1*Add(ts_Decay2(df['p1_corrs0'],42),get_LINEARREG_SLOPE(df['p6_tn4'],13))",10.2644,0.0062,0.8686,3.8021,0.5512817380637433,2024-02-01 22:49:00+00:00,2025-01-30 20:49:00+00:00,0.51,0.353,0.591,-0.246,3.69,0.73,0.00704,3.55
207,207,"-1*Min(pn_TransNorm(ts_Delay(df['p5_to0'],18)),df['p4_ms1'])","260_-1*Min(pn_TransNorm(ts_Delay(df['p5_to0'],18)),df['p4_ms1'])",10.2618,0.0053,0.8778,3.6621,0.3085697053335817,2024-02-01 22:49:00+00:00,2025-01-30 20:49:00+00:00,0.364,0.508,0.4174,-0.11,2.75,0.58,0.0048,2.6
249,249,"-1*ts_Delta(ts_Delta(df['p6_tn10'],31),40)","298_-1*ts_Delta(ts_Delta(df['p6_tn10'],31),40)",10.2577,0.0058,0.8882,3.251,0.5203949226670176,2024-02-01 22:49:00+00:00,2025-01-30 20:49:00+00:00,0.336,0.546,0.381,-0.168,1.68,0.42,0.00445,1.98
278,278,"-1*Minus(Multiply(df['p6_tn13'],df['p5_to5']),Mthan(df['lislope'],ts_TransNorm(df['dx'],39)))","320_-1*Minus(Multiply(df['p6_tn13'],df['p5_to5']),Mthan(df['lislope'],ts_TransNorm(df['dx'],39)))",10.2575,0.0056,0.8711,3.8026,0.5748383148617769,2024-02-01 22:49:00+00:00,2025-01-30 20:49:00+00:00,0.35,0.515,0.4046,-0.151,1.2,0.24,0.00357,1.41
418,418,"Multiply(pn_GroupRank(df['p6_tn10'],pn_TransNorm(df['p1_corrs7'])),ts_Kurtosis(df['p6_tn7'],19))","429_Multiply(pn_GroupRank(df['p6_tn10'],pn_TransNorm(df['p1_corrs7'])),ts_Kurtosis(df['p6_tn7'],19))",10.2524,0.0056,0.8825,3.4411,0.5064862593380668,2024-02-01 22:49:00+00:00,2025-01-30 20:49:00+00:00,0.515,0.369,0.5826,-0.192,2.52,0.55,0.00547,2.62
152,152,"-1*pn_GroupNeutral(Reverse(ts_Delta(df['p4_ms2'],22)),pn_FillMax(pn_TransNorm(df['dx'])))","217_-1*pn_GroupNeutral(Reverse(ts_Delta(df['p4_ms2'],22)),pn_FillMax(pn_TransNorm(df['dx'])))",10.2524,0.0053,0.8713,3.8188,0.5497982967574583,2024-02-01 22:49:00+00:00,2025-01-30 20:49:00+00:00,0.403,0.462,0.4659,-0.132,1.7,0.33,0.00388,1.76
96,96,"-1*Minus(get_CCI(df['p2_et9'],ts_Sum(df['p2_et9'],37),df['p2_et9'],48),pn_GroupNorm(pn_FillMax(df['p3_mf9']),ts_Rank(df['dcphase'],31)))","150_-1*Minus(get_CCI(df['p2_et9'],ts_Sum(df['p2_et9'],37),df['p2_et9'],48),pn_GroupNorm(pn_FillMax(df['p3_mf9']),ts_Rank(df['dcphase'],31)))",10.2472,0.0045,0.8842,3.5474,0.4860186025443317,2024-02-01 22:49:00+00:00,2025-01-30 20:49:00+00:00,0.425,0.453,0.4841,-0.198,2.94,0.65,0.00498,2.79
409,409,"pn_GroupNeutral(ts_Kurtosis(df['p6_tn7'],24),Min(FilterInf(df['ultosc']),ts_Divide(df['dcphase'],18)))","422_pn_GroupNeutral(ts_Kurtosis(df['p6_tn7'],24),Min(FilterInf(df['ultosc']),ts_Divide(df['dcphase'],18)))",10.2426,0.0039,0.8654,4.1867,0.1210545098549949,2024-02-01 22:49:00+00:00,2025-01-30 20:49:00+00:00,0.654,0.213,0.7543,-0.022,2.91,0.49,0.00322,2.23
364,364,"-1*Min(get_LINEARREG_ANGLE(df['p2_et1'],10),ts_Cov2(df['p2_et7'],df['p4_ms6'],36))","391_-1*Min(get_LINEARREG_ANGLE(df['p2_et1'],10),ts_Cov2(df['p2_et7'],df['p4_ms6'],36))",10.238,0.0048,0.8743,3.7641,0.5084775921933926,2024-02-01 22:49:00+00:00,2025-01-30 20:49:00+00:00,0.331,0.537,0.3813,-0.194,3.93,0.82,0.00625,3.58
220,220,"-1*Add(get_CCI(ts_Min(df['p2_et15'],18),ts_Rank(df['dcphase'],12),ts_Argmin(df['p4_ms1'],12),21),get_CCI(MEthan(df['p2_et1'],df['p2_et9']),df['p6_tn4'],MEthan(df['p2_et1'],df['dcphase']),10))","274_-1*Add(get_CCI(ts_Min(df['p2_et15'],18),ts_Rank(df['dcphase'],12),ts_Argmin(df['p4_ms1'],12),21),get_CCI(MEthan(df['p2_et1'],df['p2_et9']),df['p6_tn4'],MEthan(df['p2_et1'],df['dcphase']),10))",10.233,0.0052,0.8685,3.869,0.5479578878543289,2024-02-01 22:49:00+00:00,2025-01-30 20:49:00+00:00,0.436,0.426,0.5058,-0.099,3.11,0.62,0.00537,2.89
119,119,"-1*pn_GroupRank(ts_Max(df['p4_ms5'],39),ts_CorrChg(df['p3_mf4'],df['p6_tn6'],38))","174_-1*pn_GroupRank(ts_Max(df['p4_ms5'],39),ts_CorrChg(df['p3_mf4'],df['p6_tn6'],38))",10.2322,0.0035,0.8894,3.4839,0.5868453394662996,2024-02-01 22:49:00+00:00,2025-01-30 20:49:00+00:00,0.35,0.533,0.3964,-0.007,2.16,0.52,0.00427,2.22
311,311,"-1*get_CCI(ts_StdevChg(UnEqual(df['p2_et4'],df['cci']),29),get_LINEARREG_ANGLE(ts_TransNorm(df['p4_ms1'],21),11),pn_GroupNeutral(pn_FillMax(df['p4_ms2']),df['p3_mf4']),23)","351_-1*get_CCI(ts_StdevChg(UnEqual(df['p2_et4'],df['cci']),29),get_LINEARREG_ANGLE(ts_TransNorm(df['p4_ms1'],21),11),pn_GroupNeutral(pn_FillMax(df['p4_ms2']),df['p3_mf4']),23)",10.2144,0.0052,0.8699,3.7607,0.4874768149049123,2024-02-01 22:49:00+00:00,2025-01-30 20:49:00+00:00,0.454,0.41,0.5255,-0.213,3.65,0.73,0.00616,3.36
321,321,"pn_CrossFit(Minus(ts_Min(df['p4_ms0'],5),ts_Min(df['p2_et12'],22)),Minus(ts_Regression(df['p3_mf7'],df['p4_ms0'],16,'B'),Sign(df['p4_ms1'])))","358_pn_CrossFit(Minus(ts_Min(df['p4_ms0'],5),ts_Min(df['p2_et12'],22)),Minus(ts_Regression(df['p3_mf7'],df['p4_ms0'],16,'B'),Sign(df['p4_ms1'])))",10.214,0.0052,0.8855,3.3022,0.4438772231706107,2024-02-01 22:49:00+00:00,2025-01-30 20:49:00+00:00,0.362,0.525,0.4081,-0.147,2.78,0.66,0.00554,2.86
422,422,"-1*Multiply(pn_RankCentered(Abs(df['p1_corrs8'])),FilterInf(df['p4_ms1']))","432_-1*Multiply(pn_RankCentered(Abs(df['p1_corrs8'])),FilterInf(df['p4_ms1']))",10.2106,0.0046,0.8845,3.413,0.3568388473816766,2024-02-01 22:49:00+00:00,2025-01-30 20:49:00+00:00,0.499,0.379,0.5683,-0.093,2.74,0.65,0.00456,2.64
196,196,"-1*pn_GroupNorm(pn_CrossFit(df['p2_et5'],ts_Delta(df['p2_et0'],39)),Reverse(ts_Max(df['p3_mf4'],2)))","250_-1*pn_GroupNorm(pn_CrossFit(df['p2_et5'],ts_Delta(df['p2_et0'],39)),Reverse(ts_Max(df['p3_mf4'],2)))",10.2094,0.0048,0.8742,3.6782,0.581056566548917,2024-02-01 22:49:00+00:00,2025-01-30 20:49:00+00:00,0.443,0.425,0.5104,-0.135,2.88,0.69,0.00516,2.85
407,407,"-1*pn_CrossFit(pn_Stand(ts_Mean(df['p2_et12'],6)),Min(pn_TransStd(df['adosc']),pn_Stand(df['p2_et1'])))","420_-1*pn_CrossFit(pn_Stand(ts_Mean(df['p2_et12'],6)),Min(pn_TransStd(df['adosc']),pn_Stand(df['p2_et1'])))",10.2076,0.0052,0.8861,3.2667,0.5778170502107943,2024-02-01 22:49:00+00:00,2025-01-30 20:49:00+00:00,0.478,0.402,0.5432,-0.214,1.17,0.27,0.0032,1.37
426,426,"-1*pn_GroupRank(pn_CrossFit(Power(df['p1_corrs0'],5),df['p4_ms1']),pn_CrossFit(df['cci'],pn_TransNorm(df['p2_et9'])))","436_-1*pn_GroupRank(pn_CrossFit(Power(df['p1_corrs0'],5),df['p4_ms1']),pn_CrossFit(df['cci'],pn_TransNorm(df['p2_et9'])))",10.207,0.005,0.8812,3.4303,0.5871780404993362,2024-02-01 22:49:00+00:00,2025-01-30 20:49:00+00:00,0.456,0.418,0.5217,-0.108,2.12,0.46,0.0047,2.22
427,427,"-1*ts_Regression(df['p2_et1'],Divide(pn_Stand(df['ultosc']),ts_Kurtosis(df['p2_et10'],50)),33,'C')","437_-1*ts_Regression(df['p2_et1'],Divide(pn_Stand(df['ultosc']),ts_Kurtosis(df['p2_et10'],50)),33,'C')",10.1973,0.0047,0.8755,3.624,0.4334898429399847,2024-02-01 22:49:00+00:00,2025-01-30 20:49:00+00:00,0.388,0.481,0.4465,-0.186,2.66,0.61,0.00481,2.61
333,333,"-1*pn_GroupNorm(Min(df['p5_to5'],Min(df['p2_et7'],df['p2_et9'])),df['p1_corrs4'])","369_-1*pn_GroupNorm(Min(df['p5_to5'],Min(df['p2_et7'],df['p2_et9'])),df['p1_corrs4'])",10.1883,0.006,0.8765,3.37,0.5803925417045869,2024-02-01 22:49:00+00:00,2025-01-30 20:49:00+00:00,0.533,0.337,0.6126,-0.487,2.59,0.57,0.00592,2.76
48,48,"-1*pn_CrossFit(ts_Cov2(ts_Max(df['p6_tn9'],32),ts_Max(df['p3_mf5'],13),21),Max(ts_Scale(df['p2_et0'],1),get_KAMA(df['p2_et0'],4)))","97_-1*pn_CrossFit(ts_Cov2(ts_Max(df['p6_tn9'],32),ts_Max(df['p3_mf5'],13),21),Max(ts_Scale(df['p2_et0'],1),get_KAMA(df['p2_et0'],4)))",10.186,0.0051,0.8621,3.9328,0.5764064519512625,2024-02-01 22:49:00+00:00,2025-01-30 20:49:00+00:00,0.268,0.588,0.3131,-0.203,3.73,0.88,0.0062,3.58
443,443,"-1*pn_GroupNeutral(Power(Abs(df['p1_corrs1']),2),df['p2_et7'])","451_-1*pn_GroupNeutral(Power(Abs(df['p1_corrs1']),2),df['p2_et7'])",10.183,0.0042,0.865,3.9709,0.3043629670880609,2024-02-01 22:49:00+00:00,2025-01-30 20:49:00+00:00,0.514,0.345,0.5984,-0.017,4.66,0.86,0.00563,3.75
176,176,"-1*pn_GroupRank(Power(ts_Delta(df['p2_et0'],28),7),ts_Scale(df['p5_to7'],17))","237_-1*pn_GroupRank(Power(ts_Delta(df['p2_et0'],28),7),ts_Scale(df['p5_to7'],17))",10.1792,0.0047,0.8694,3.7454,0.5234287691772244,2024-02-01 22:49:00+00:00,2025-01-30 20:49:00+00:00,0.425,0.438,0.4925,-0.136,2.43,0.58,0.00437,2.41
4,4,"-1*pn_GroupNeutral(ts_TransNorm(ts_Regression(df['p6_tn13'],df['kama'],21,'C'),21),pn_FillMax(df['p6_tn8']))","22_-1*pn_GroupNeutral(ts_TransNorm(ts_Regression(df['p6_tn13'],df['kama'],21,'C'),21),pn_FillMax(df['p6_tn8']))",10.1715,0.0048,0.8672,3.779,0.3764259132274065,2024-02-01 22:49:00+00:00,2025-01-30 20:49:00+00:00,0.57,0.291,0.662,-0.126,3.73,0.7,0.00538,3.19
332,332,"-1*Min(ts_Delta(df['p4_ms1'],23),get_HT_DCPHASE(pn_Rank(df['p5_to0'])))","368_-1*Min(ts_Delta(df['p4_ms1'],23),get_HT_DCPHASE(pn_Rank(df['p5_to0'])))",10.1408,0.0059,0.8699,3.4465,0.5207068830398304,2024-02-01 22:49:00+00:00,2025-01-30 20:49:00+00:00,0.347,0.516,0.4021,-0.189,2.6,0.58,0.00577,2.75
276,276,"-1*get_CCI(ts_Argmax(Power(df['p3_mf12'],46),26),pn_FillMax(Mthan(df['p3_mf12'],df['p2_et13'])),df['ultosc'],45)","319_-1*get_CCI(ts_Argmax(Power(df['p3_mf12'],46),26),pn_FillMax(Mthan(df['p3_mf12'],df['p2_et13'])),df['ultosc'],45)",10.1405,0.0055,0.85,4.0936,0.4917856835929556,2024-02-01 22:49:00+00:00,2025-01-30 20:49:00+00:00,0.429,0.414,0.5089,-0.109,3.14,0.57,0.00499,2.76
358,358,"-1*pn_GroupNeutral(get_CMO(df['p6_tn13'],40),pn_TransNorm(df['p6_tn4']))","386_-1*pn_GroupNeutral(get_CMO(df['p6_tn13'],40),pn_TransNorm(df['p6_tn4']))",10.1371,0.005,0.8657,3.6887,0.5537465739591632,2024-02-01 22:49:00+00:00,2025-01-30 20:49:00+00:00,0.465,0.395,0.5407,-0.093,2.94,0.6,0.00549,2.83
263,263,"ts_Divide(ts_Scale(df['di'],8),11)","308_ts_Divide(ts_Scale(df['di'],8),11)",10.135,0.0062,0.8612,3.6308,0.4902232892373805,2024-02-01 22:49:00+00:00,2025-01-30 20:49:00+00:00,0.292,0.571,0.3384,-0.225,3.99,0.99,0.00755,4.08
123,123,"ts_Regression(get_KAMA(Sqrt(df['p6_tn7']),20),LEthan(pn_GroupRank(df['kama'],df['p2_et14']),pn_GroupNeutral(df['cci'],df['p4_ms0'])),49,'A')","179_ts_Regression(get_KAMA(Sqrt(df['p6_tn7']),20),LEthan(pn_GroupRank(df['kama'],df['p2_et14']),pn_GroupNeutral(df['cci'],df['p4_ms0'])),49,'A')",10.133,0.0042,0.856,4.082,0.1173542079221856,2024-02-01 22:49:00+00:00,2025-01-30 20:49:00+00:00,0.453,0.405,0.528,0.005,2.74,0.47,0.00341,2.18
147,147,"-1*ts_Skewness(df['p3_mf11'],36)","212_-1*ts_Skewness(df['p3_mf11'],36)",10.1206,0.0041,0.8707,3.6215,0.5744880117023888,2024-02-01 22:49:00+00:00,2025-01-30 20:49:00+00:00,0.308,0.556,0.3565,-0.043,2.69,0.56,0.00424,2.44
342,342,"get_CCI(df['p6_tn11'],ts_Cov2(df['p2_et13'],df['liangle'],47),pn_GroupRank(pn_Rank2(df['p6_tn0']),ts_Median(df['p4_ms3'],15)),47)","376_get_CCI(df['p6_tn11'],ts_Cov2(df['p2_et13'],df['liangle'],47),pn_GroupRank(pn_Rank2(df['p6_tn0']),ts_Median(df['p4_ms3'],15)),47)",10.1198,0.0051,0.8691,3.5227,0.5311466827899056,2024-02-01 22:49:00+00:00,2025-01-30 20:49:00+00:00,0.517,0.353,0.5943,-0.327,3.25,0.69,0.00626,3.2
171,171,"Add(ts_CorrChg(ts_Scale(df['p6_tn11'],8),df['p4_ms0'],47),ts_Decay2(df['p2_et11'],6))","234_Add(ts_CorrChg(ts_Scale(df['p6_tn11'],8),df['p4_ms0'],47),ts_Decay2(df['p2_et11'],6))",10.1143,0.0049,0.8772,3.2916,0.5100139998518117,2024-02-01 22:49:00+00:00,2025-01-30 20:49:00+00:00,0.553,0.326,0.6291,-0.408,2.26,0.5,0.00454,2.29
88,88,"-1*pn_CrossFit(ts_Argmax(ts_Delay(df['p2_et10'],29),41),get_CCI(df['p6_tn3'],ts_Scale(df['p1_corrs1'],15),df['p6_tn7'],41))","143_-1*pn_CrossFit(ts_Argmax(ts_Delay(df['p2_et10'],29),41),get_CCI(df['p6_tn3'],ts_Scale(df['p1_corrs1'],15),df['p6_tn7'],41))",10.0988,0.0053,0.8653,3.5391,0.5474513690994394,2024-02-01 22:49:00+00:00,2025-01-30 20:49:00+00:00,0.292,0.567,0.3399,-0.347,2.76,0.57,0.00487,2.61
108,108,"get_MINUS_DM(df['p6_tn6'],ts_Kurtosis(pn_RankCentered(df['p4_ms5']),49),49)","165_get_MINUS_DM(df['p6_tn6'],ts_Kurtosis(pn_RankCentered(df['p4_ms5']),49),49)",10.0976,0.0045,0.8524,4.0515,0.1640915222028788,2024-02-01 22:49:00+00:00,2025-01-30 20:49:00+00:00,0.449,0.405,0.5258,0.036,1.66,0.34,0.00298,1.57
259,259,"-1*Add(ts_Kurtosis(ts_Median(df['p2_et6'],4),23),get_CMO(ts_Scale(df['p6_tn1'],26),45))","305_-1*Add(ts_Kurtosis(ts_Median(df['p2_et6'],4),23),get_CMO(ts_Scale(df['p6_tn1'],26),45))",10.0966,0.0055,0.8594,3.6739,0.583461638264093,2024-02-01 22:49:00+00:00,2025-01-30 20:49:00+00:00,0.385,0.468,0.4513,-0.144,2.27,0.5,0.00504,2.39
204,204,"-1*Min(get_LINEARREG_ANGLE(ts_Decay2(df['p2_et14'],46),12),ts_TransNorm(df['p4_ms1'],47))","257_-1*Min(get_LINEARREG_ANGLE(ts_Decay2(df['p2_et14'],46),12),ts_TransNorm(df['p4_ms1'],47))",10.0944,0.0053,0.8552,3.8372,0.5717212901548497,2024-02-01 22:49:00+00:00,2025-01-30 20:49:00+00:00,0.439,0.41,0.5171,-0.107,2.78,0.56,0.0053,2.69
217,217,"-1*Add(get_CCI(df['p6_tn4'],ts_Argmin(df['p2_et9'],6),ts_Argmin(df['p4_ms1'],5),34),29)","271_-1*Add(get_CCI(df['p6_tn4'],ts_Argmin(df['p2_et9'],6),ts_Argmin(df['p4_ms1'],5),34),29)",10.0931,0.0057,0.8528,3.8415,0.5225535595485782,2024-02-01 22:49:00+00:00,2025-01-30 20:49:00+00:00,0.41,0.437,0.4841,-0.145,3.65,0.74,0.00659,3.46
392,392,"-1*Min(pn_GroupNorm(ts_Decay(df['p4_ms3'],1),df['p1_corrs7']),Min(ts_Decay2(df['p2_et9'],6),Exp(df['ultosc'])))","407_-1*Min(pn_GroupNorm(ts_Decay(df['p4_ms3'],1),df['p1_corrs7']),Min(ts_Decay2(df['p2_et9'],6),Exp(df['ultosc'])))",10.0914,0.0055,0.8672,3.4314,0.538649736913378,2024-02-01 22:49:00+00:00,2025-01-30 20:49:00+00:00,0.373,0.488,0.4332,-0.267,2.02,0.46,0.00457,2.16
368,368,"-1*get_LINEARREG_SLOPE(ts_MeanChg(df['p4_ms5'],40),13)","392_-1*get_LINEARREG_SLOPE(ts_MeanChg(df['p4_ms5'],40),13)",10.0756,0.0036,0.8776,3.3588,0.5865189585140155,2024-02-01 22:49:00+00:00,2025-01-30 20:49:00+00:00,0.354,0.517,0.4064,0.127,3.05,0.72,0.00481,2.88
191,191,"-1*Multiply(ts_Quantile(pn_FillMax(df['p6_tn10']),47,'A'),pn_GroupNeutral(ts_Delta(df['p1_corrs1'],37),get_KAMA(df['p2_et9'],11)))","245_-1*Multiply(ts_Quantile(pn_FillMax(df['p6_tn10']),47,'A'),pn_GroupNeutral(ts_Delta(df['p1_corrs1'],37),get_KAMA(df['p2_et9'],11)))",10.0649,0.0049,0.8523,3.8953,0.467357718450653,2024-02-01 22:49:00+00:00,2025-01-30 20:49:00+00:00,0.298,0.548,0.3522,-0.243,1.77,0.34,0.0031,1.63
347,347,"Divide(MEthan(df['p2_et10'],df['p1_corrs9']),ts_Scale(df['p2_et7'],32))","380_Divide(MEthan(df['p2_et10'],df['p1_corrs9']),ts_Scale(df['p2_et7'],32))",10.0648,0.0057,0.859,3.5702,0.500149668085957,2024-02-01 22:49:00+00:00,2025-01-30 20:49:00+00:00,0.567,0.294,0.6585,-0.436,2.32,0.47,0.00438,2.24
345,345,"get_CCI(inv(Multiply(df['p3_mf0'],0.966)),pn_Winsor(df['p4_ms1'],29),get_MINUS_DM(Softsign(df['kama']),df['p5_to0'],26),29)","378_get_CCI(inv(Multiply(df['p3_mf0'],0.966)),pn_Winsor(df['p4_ms1'],29),get_MINUS_DM(Softsign(df['kama']),df['p5_to0'],26),29)",10.0646,0.0043,0.8727,3.3625,0.5625072623916613,2024-02-01 22:49:00+00:00,2025-01-30 20:49:00+00:00,0.44,0.434,0.5034,0.031,2.47,0.55,0.00423,2.36
287,287,"-1*Multiply(ts_TransNorm(Abs(df['p4_ms0']),23),pn_Winsor(pn_Winsor(df['p5_to0'],24),30))","326_-1*Multiply(ts_TransNorm(Abs(df['p4_ms0']),23),pn_Winsor(pn_Winsor(df['p5_to0'],24),30))",10.062,0.0048,0.8647,3.5206,0.4913742716949226,2024-02-01 22:49:00+00:00,2025-01-30 20:49:00+00:00,0.445,0.413,0.5186,-0.062,3.06,0.64,0.00444,2.71
55,55,"-1*pn_GroupRank(ts_Delta(df['p2_et9'],4),ts_TransNorm(ts_Max(df['p2_et12'],39),13))","108_-1*pn_GroupRank(ts_Delta(df['p2_et9'],4),ts_TransNorm(ts_Max(df['p2_et12'],39),13))",10.0486,0.0049,0.867,3.4078,0.5552702885144715,2024-02-01 22:49:00+00:00,2025-01-30 20:49:00+00:00,0.477,0.383,0.5547,-0.167,4.06,0.89,0.0068,3.83
151,151,"pn_GroupNeutral(Reverse(ts_Kurtosis(df['p3_mf3'],43)),get_CMO(df['dm'],45))","216_pn_GroupNeutral(Reverse(ts_Kurtosis(df['p3_mf3'],43)),get_CMO(df['dm'],45))",10.0462,0.0049,0.8621,3.5382,0.2682399655026328,2024-02-01 22:49:00+00:00,2025-01-30 20:49:00+00:00,0.505,0.359,0.5845,-0.058,4.13,0.87,0.00606,3.68
306,306,"-1*get_CCI(df['p5_to7'],pn_TransStd(df['p4_ms5']),df['p5_to2'],42)","344_-1*get_CCI(df['p5_to7'],pn_TransStd(df['p4_ms5']),df['p5_to2'],42)",10.0423,0.0052,0.8504,3.835,0.5981696039587685,2024-02-01 22:49:00+00:00,2025-01-30 20:49:00+00:00,0.38,0.464,0.4502,-0.154,2.58,0.54,0.00438,2.41
46,46,"Add(df['p2_et8'],ts_Argmin(pn_Winsor(df['di'],8),5))","91_Add(df['p2_et8'],ts_Argmin(pn_Winsor(df['di'],8),5))",10.0409,0.0048,0.8561,3.7253,0.4693599813140709,2024-02-01 22:49:00+00:00,2025-01-30 20:49:00+00:00,0.462,0.395,0.5391,-0.24,4.56,0.95,0.00652,4.01
6,6,"-1*get_CCI(pn_GroupRank(df['p2_et1'],ts_Entropy(df['dm'],19)),df['p3_mf8'],get_MINUS_DM(df['p3_mf7'],ts_Scale(df['dm'],6),33),35)","27_-1*get_CCI(pn_GroupRank(df['p2_et1'],ts_Entropy(df['dm'],19)),df['p3_mf8'],get_MINUS_DM(df['p3_mf7'],ts_Scale(df['dm'],6),33),35)",10.0379,0.0059,0.8417,3.9765,0.4601460733896851,2024-02-01 22:49:00+00:00,2025-01-30 20:49:00+00:00,0.383,0.452,0.4587,-0.073,3.7,0.7,0.00646,3.4
61,61,"-1*ts_Delta(Exp(Exp(df['p6_tn5'])),41)","114_-1*ts_Delta(Exp(Exp(df['p6_tn5'])),41)",10.0258,0.0056,0.8626,3.351,0.5554227189332985,2024-02-01 22:49:00+00:00,2025-01-30 20:49:00+00:00,0.51,0.346,0.5958,-0.192,3.16,0.72,0.00663,3.28
441,441,"-1*pn_GroupNeutral(pn_Stand(Exp(df['p2_et7'])),ts_CovChg(ts_Stdev(df['p3_mf5'],11),ts_CorrChg(df['cci'],df['p3_mf9'],38),11))","449_-1*pn_GroupNeutral(pn_Stand(Exp(df['p2_et7'])),ts_CovChg(ts_Stdev(df['p3_mf5'],11),ts_CorrChg(df['cci'],df['p3_mf9'],38),11))",10.0208,0.006,0.8548,3.521,0.5920069306209672,2024-02-01 22:49:00+00:00,2025-01-30 20:49:00+00:00,0.597,0.252,0.7032,-0.634,4.02,1.16,0.00839,4.47
438,438,"pn_GroupNeutral(get_HT_DCPERIOD(df['p3_mf4']),Sign(df['p4_ms6']))","446_pn_GroupNeutral(get_HT_DCPERIOD(df['p3_mf4']),Sign(df['p4_ms6']))",10.0166,0.0037,0.8422,4.2249,0.2173760125599344,2024-02-01 22:49:00+00:00,2025-01-30 20:49:00+00:00,0.632,0.212,0.7488,0.004,2.01,0.34,0.00141,1.38
415,415,"-1*Min(Reverse(pn_TransStd(df['p2_et8'])),ts_Decay(df['p2_et15'],42))","426_-1*Min(Reverse(pn_TransStd(df['p2_et8'])),ts_Decay(df['p2_et15'],42))",10.0063,0.004,0.8737,3.204,0.594018787623433,2024-02-01 22:49:00+00:00,2025-01-30 20:49:00+00:00,0.474,0.394,0.5461,0.025,4.01,0.99,0.00567,3.71
354,354,"-1*ts_Regression(pn_CrossFit(df['p1_corrs5'],df['p4_ms5']),df['p2_et13'],13,'D')","382_-1*ts_Regression(pn_CrossFit(df['p1_corrs5'],df['p4_ms5']),df['p2_et13'],13,'D')",10.0062,0.0047,0.8659,3.3372,0.4943926194460801,2024-02-01 22:49:00+00:00,2025-01-30 20:49:00+00:00,0.346,0.513,0.4028,-0.034,1.87,0.43,0.00398,1.96
285,285,"-1*Multiply(ts_Median(df['p3_mf5'],16),df['p3_mf11'])","324_-1*Multiply(ts_Median(df['p3_mf5'],16),df['p3_mf11'])",9.9985,0.0048,0.8425,3.9956,0.5395402049113778,2024-02-01 22:49:00+00:00,2025-01-30 20:49:00+00:00,0.335,0.501,0.4007,-0.166,3.47,0.75,0.00521,3.14
305,305,"-1*Min(df['p2_et1'],ts_Decay(pn_TransStd(df['p6_tn1']),14))","343_-1*Min(df['p2_et1'],ts_Decay(pn_TransStd(df['p6_tn1']),14))",9.9917,0.0053,0.8534,3.5766,0.5145998244105262,2024-02-01 22:49:00+00:00,2025-01-30 20:49:00+00:00,0.308,0.539,0.3636,-0.208,3.04,0.7,0.00559,3.01
145,145,"-1*pn_GroupNeutral(Min(ts_Max(df['p2_et0'],46),df['p4_ms1']),pn_GroupNeutral(df['p3_mf11'],pn_TransNorm(df['p5_to0'])))","209_-1*pn_GroupNeutral(Min(ts_Max(df['p2_et0'],46),df['p4_ms1']),pn_GroupNeutral(df['p3_mf11'],pn_TransNorm(df['p5_to0'])))",9.9905,0.0043,0.8643,3.3961,0.4486397190172026,2024-02-01 22:49:00+00:00,2025-01-30 20:49:00+00:00,0.379,0.479,0.4417,-0.056,2.15,0.5,0.00446,2.23
315,315,"-1*get_CCI(ts_Kurtosis(Equal(df['p2_et16'],df['p1_corrs3']),42),pn_FillMin(ts_Divide(df['p6_tn8'],27)),ts_Delta(df['p6_tn1'],11),23)","353_-1*get_CCI(ts_Kurtosis(Equal(df['p2_et16'],df['p1_corrs3']),42),pn_FillMin(ts_Divide(df['p6_tn8'],27)),ts_Delta(df['p6_tn1'],11),23)",9.99,0.0065,0.8459,3.6218,0.4956022879355265,2024-02-01 22:49:00+00:00,2025-01-30 20:49:00+00:00,0.372,0.467,0.4434,-0.133,4.63,0.95,0.00723,4.18
64,64,"-1*ts_Scale(ts_Regression(FilterInf(df['cmo']),df['p2_et2'],10,'D'),12)","119_-1*ts_Scale(ts_Regression(FilterInf(df['cmo']),df['p2_et2'],10,'D'),12)",9.9748,0.0053,0.8544,3.5061,0.5320128847292298,2024-02-01 22:49:00+00:00,2025-01-30 20:49:00+00:00,0.339,0.509,0.3998,-0.198,4.07,0.97,0.00703,3.98
242,242,"-1*get_CCI(ts_Regression(df['p2_et6'],df['p3_mf11'],7,'C'),df['p3_mf9'],pn_GroupNorm(df['p6_tn13'],get_LINEARREG_SLOPE(df['p2_et0'],9)),19)","291_-1*get_CCI(ts_Regression(df['p2_et6'],df['p3_mf11'],7,'C'),df['p3_mf9'],pn_GroupNorm(df['p6_tn13'],get_LINEARREG_SLOPE(df['p2_et0'],9)),19)",9.9734,0.0046,0.8314,4.2889,0.5705622564951928,2024-02-01 22:49:00+00:00,2025-01-30 20:49:00+00:00,0.296,0.529,0.3588,-0.067,1.81,0.32,0.00308,1.62
360,360,"-1*Min(get_LINEARREG_ANGLE(df['p6_tn7'],13),ts_Rank(ts_Median(df['p2_et15'],25),12))","386_-1*Min(get_LINEARREG_ANGLE(df['p6_tn7'],13),ts_Rank(ts_Median(df['p2_et15'],25),12))",9.9726,0.0056,0.8551,3.422,0.5754921686452669,2024-02-01 22:49:00+00:00,2025-01-30 20:49:00+00:00,0.474,0.375,0.5583,-0.359,3.57,0.77,0.00659,3.47
248,248,"-1*Min(ts_Median(df['p1_corrs5'],3),get_LINEARREG_SLOPE(Add(df['p4_ms3'],df['p2_et12']),13))","300_-1*Min(ts_Median(df['p1_corrs5'],3),get_LINEARREG_SLOPE(Add(df['p4_ms3'],df['p2_et12']),13))",9.9699,0.0055,0.8408,3.862,0.5292767467261962,2024-02-01 22:49:00+00:00,2025-01-30 20:49:00+00:00,0.385,0.45,0.4611,-0.224,3.84,0.73,0.00647,3.49
328,328,"-1*Min(get_CMO(Divide(df['p4_ms4'],df['p5_to0']),40),pn_GroupRank(df['cmo'],ts_Cov(df['cmo'],df['p4_ms1'],37)))","364_-1*Min(get_CMO(Divide(df['p4_ms4'],df['p5_to0']),40),pn_GroupRank(df['cmo'],ts_Cov(df['cmo'],df['p4_ms1'],37)))",9.9669,0.0048,0.8293,4.3062,0.3005018666523758,2024-02-01 22:49:00+00:00,2025-01-30 20:49:00+00:00,0.407,0.417,0.4939,-0.132,2.13,0.36,0.00299,1.76
402,402,"-1*pn_GroupNeutral(Sign(df['p2_et9']),ts_StdevChg(df['dx'],16))","416_-1*pn_GroupNeutral(Sign(df['p2_et9']),ts_StdevChg(df['dx'],16))",9.9609,0.0045,0.8338,4.1909,0.4521611858083741,2024-02-01 22:49:00+00:00,2025-01-30 20:49:00+00:00,0.419,0.409,0.506,-0.125,3.17,0.51,0.00381,2.46
247,247,"-1*pn_GroupRank(pn_Rank2(df['p5_to2']),get_HT_DCPERIOD(df['p1_corrs1']))","297_-1*pn_GroupRank(pn_Rank2(df['p5_to2']),get_HT_DCPERIOD(df['p1_corrs1']))",9.9455,0.0034,0.8544,3.7006,0.4933717847301877,2024-02-01 22:49:00+00:00,2025-01-30 20:49:00+00:00,0.34,0.508,0.4009,-0.081,1.72,0.33,0.0016,1.31
344,344,"-1*get_CCI(ts_Divide(inv(df['p6_tn6']),5),ts_Decay2(df['p2_et15'],47),IfThen(ts_Min(df['p6_tn7'],6),50,5),29)","378_-1*get_CCI(ts_Divide(inv(df['p6_tn6']),5),ts_Decay2(df['p2_et15'],47),IfThen(ts_Min(df['p6_tn7'],6),50,5),29)",9.9388,0.0056,0.8465,3.5782,0.5647025587356201,2024-02-01 22:49:00+00:00,2025-01-30 20:49:00+00:00,0.473,0.367,0.5631,-0.206,2.22,0.5,0.00478,2.32
111,111,"-1*pn_GroupNeutral(ts_Cov2(pn_Rank2(df['p2_et0']),df['p4_ms1'],22),df['p2_et8'])","170_-1*pn_GroupNeutral(ts_Cov2(pn_Rank2(df['p2_et0']),df['p4_ms1'],22),df['p2_et8'])",9.9344,0.0033,0.8657,3.3348,0.283288232903227,2024-02-01 22:49:00+00:00,2025-01-30 20:49:00+00:00,0.436,0.424,0.507,0.012,2.26,0.52,0.00327,2.06
406,406,"pn_GroupNeutral(ts_Product(pn_GroupNorm(df['p2_et15'],df['p5_to4']),5),ts_Sum(df['p3_mf7'],22))","419_pn_GroupNeutral(ts_Product(pn_GroupNorm(df['p2_et15'],df['p5_to4']),5),ts_Sum(df['p3_mf7'],22))",9.9332,0.0022,0.87,3.366,0.2676921809465666,2024-02-01 22:49:00+00:00,2025-01-30 20:49:00+00:00,0.501,0.37,0.5752,0.096,4.52,1.02,0.00458,3.7
93,93,"-1*ts_Scale(get_CCI(pn_Rank2(df['p2_et9']),ts_Argmax(df['p2_et12'],31),pn_GroupRank(df['p2_et9'],df['p2_et14']),23),17)","148_-1*ts_Scale(get_CCI(pn_Rank2(df['p2_et9']),ts_Argmax(df['p2_et12'],31),pn_GroupRank(df['p2_et9'],df['p2_et14']),23),17)",9.933,0.0049,0.837,3.9538,0.5948518634381649,2024-02-01 22:49:00+00:00,2025-01-30 20:49:00+00:00,0.536,0.295,0.645,-0.161,2.74,0.49,0.00429,2.38
63,63,"Reverse(get_LINEARREG_SLOPE(ts_Delta(df['p6_tn13'],9),4))","116_Reverse(get_LINEARREG_SLOPE(ts_Delta(df['p6_tn13'],9),4))",9.9278,0.0041,0.8522,3.5978,0.3739342151599785,2024-02-01 22:49:00+00:00,2025-01-30 20:49:00+00:00,0.332,0.522,0.3888,0.002,3.41,0.69,0.00443,2.89
187,187,"-1*ts_MeanChg(get_CCI(ts_MeanChg(df['p5_to5'],19),UnEqual(df['p3_mf5'],df['p1_corrs1']),df['dm'],30),10)","242_-1*ts_MeanChg(get_CCI(ts_MeanChg(df['p5_to5'],19),UnEqual(df['p3_mf5'],df['p1_corrs1']),df['dm'],30),10)",9.9176,0.0047,0.8516,3.5068,0.5748863749694599,2024-02-01 22:49:00+00:00,2025-01-30 20:49:00+00:00,0.43,0.416,0.5083,-0.092,3.17,0.72,0.00536,3.03
421,421,"-1*Multiply(ts_Rank(ts_TransNorm(df['p2_et7'],37),23),ts_Argmax(df['p2_et5'],25))","431_-1*Multiply(ts_Rank(ts_TransNorm(df['p2_et7'],37),23),ts_Argmax(df['p2_et5'],25))",9.9098,0.0044,0.8429,3.786,0.4746269372066802,2024-02-01 22:49:00+00:00,2025-01-30 20:49:00+00:00,0.642,0.194,0.7679,-0.305,1.88,0.37,0.00363,1.82
41,41,"-1*pn_GroupNorm(get_MINUS_DM(get_LINEARREG_SLOPE(df['p6_tn2'],12),ts_Argmax(df['p6_tn13'],8),36),get_LINEARREG_ANGLE(df['p5_to0'],14))","83_-1*pn_GroupNorm(get_MINUS_DM(get_LINEARREG_SLOPE(df['p6_tn2'],12),ts_Argmax(df['p6_tn13'],8),36),get_LINEARREG_ANGLE(df['p5_to0'],14))",9.9087,0.0038,0.8371,4.0365,0.2641286725391572,2024-02-01 22:49:00+00:00,2025-01-30 20:49:00+00:00,0.428,0.403,0.515,-0.073,3.26,0.52,0.00348,2.43
246,246,"Add(pn_Winsor(df['p2_et5'],41),SignedPower(get_CMO(df['p4_ms2'],48),df['p2_et17']))","296_Add(pn_Winsor(df['p2_et5'],41),SignedPower(get_CMO(df['p4_ms2'],48),df['p2_et17']))",9.9069,0.0048,0.8524,3.4274,0.5942723151645374,2024-02-01 22:49:00+00:00,2025-01-30 20:49:00+00:00,0.327,0.527,0.3829,-0.165,2.2,0.73,0.00484,2.61
336,336,"Add(Minus(ts_Rank(df['p6_tn6'],15),FilterInf(df['p2_et16'])),pn_GroupNeutral(df['p6_tn11'],ts_Rank(df['p6_tn6'],15)))","372_Add(Minus(ts_Rank(df['p6_tn6'],15),FilterInf(df['p2_et16'])),pn_GroupNeutral(df['p6_tn11'],ts_Rank(df['p6_tn6'],15)))",9.9047,0.0049,0.8553,3.3229,0.5270574313050577,2024-02-01 22:49:00+00:00,2025-01-30 20:49:00+00:00,0.241,0.616,0.2812,-0.228,1.98,0.51,0.00523,2.34
74,74,"Add(Sign(Reverse(df['p5_to0'])),df['p2_et6'])","128_Add(Sign(Reverse(df['p5_to0'])),df['p2_et6'])",9.9046,0.0054,0.8532,3.3085,0.4774642736411889,2024-02-01 22:49:00+00:00,2025-01-30 20:49:00+00:00,0.59,0.265,0.6901,-0.06,3.24,0.75,0.0054,3.1
219,219,"-1*Add(get_CCI(df['lislope'],ts_Decay2(df['p1_corrs5'],4),MEthan(df['p2_et1'],df['p6_tn4']),21),get_CCI(df['p4_ms5'],df['p6_tn4'],ts_Rank(df['p2_et1'],12),17))","273_-1*Add(get_CCI(df['lislope'],ts_Decay2(df['p1_corrs5'],4),MEthan(df['p2_et1'],df['p6_tn4']),21),get_CCI(df['p4_ms5'],df['p6_tn4'],ts_Rank(df['p2_et1'],12),17))",9.9022,0.0055,0.8422,3.6138,0.4887655940144792,2024-02-01 22:49:00+00:00,2025-01-30 20:49:00+00:00,0.378,0.458,0.4522,-0.119,3.44,0.74,0.00604,3.28
163,163,"pn_GroupNorm(pn_Winsor(ts_Divide(df['p6_tn10'],8),6),ts_Rank(Max(df['p2_et1'],df['p1_corrs0']),45))","229_pn_GroupNorm(pn_Winsor(ts_Divide(df['p6_tn10'],8),6),ts_Rank(Max(df['p2_et1'],df['p1_corrs0']),45))",9.8946,0.0039,0.8416,3.8461,0.0583780578274234,2024-02-01 22:49:00+00:00,2025-01-30 20:49:00+00:00,0.277,0.567,0.3282,-0.005,1.97,0.38,0.0021,1.55
304,304,"get_CCI(Divide(df['p4_ms2'],ts_Rank(df['p1_corrs3'],29)),get_MINUS_DI(And(df['p2_et13'],df['p4_ms2']),ts_Stdev(df['p2_et0'],6),ts_Skewness(df['p3_mf5'],24),12),Xor(Or(df['p2_et2'],df['p6_tn4']),df['p2_et3']),29)","342_get_CCI(Divide(df['p4_ms2'],ts_Rank(df['p1_corrs3'],29)),get_MINUS_DI(And(df['p2_et13'],df['p4_ms2']),ts_Stdev(df['p2_et0'],6),ts_Skewness(df['p3_mf5'],24),12),Xor(Or(df['p2_et2'],df['p6_tn4']),df['p2_et3']),29)",9.886,0.0047,0.8363,3.8692,0.4614912913357764,2024-02-01 22:49:00+00:00,2025-01-30 20:49:00+00:00,0.348,0.49,0.4153,-0.163,1.74,0.35,0.00322,1.66
37,37,"Add(ts_Corr(pn_Winsor(df['p4_ms2'],35),Reverse(df['p6_tn10']),4),ts_Regression(pn_TransStd(df['p2_et8']),df['p2_et7'],49,'D'))","79_Add(ts_Corr(pn_Winsor(df['p4_ms2'],35),Reverse(df['p6_tn10']),4),ts_Regression(pn_TransStd(df['p2_et8']),df['p2_et7'],49,'D'))",9.8778,0.0038,0.8548,3.4226,0.2198225884248782,2024-02-01 22:49:00+00:00,2025-01-30 20:49:00+00:00,0.448,0.409,0.5228,-0.056,3.69,0.8,0.00536,3.3
71,71,"-1*Min(get_CCI(ts_Entropy(df['p6_tn8'],25),df['p3_mf11'],ts_Delay(df['p2_et18'],24),31),ts_TransNorm(df['p6_tn5'],25))","124_-1*Min(get_CCI(ts_Entropy(df['p6_tn8'],25),df['p3_mf11'],ts_Delay(df['p2_et18'],24),31),ts_TransNorm(df['p6_tn5'],25))",9.8754,0.0053,0.8505,3.3122,0.5979552368903576,2024-02-01 22:49:00+00:00,2025-01-30 20:49:00+00:00,0.506,0.338,0.5995,-0.148,2.54,0.56,0.00504,2.55
424,424,"-1*ts_Regression(IfThen(df['p2_et0'],23,8),df['p2_et1'],16,'C')","434_-1*ts_Regression(IfThen(df['p2_et0'],23,8),df['p2_et1'],16,'C')",9.8719,0.0055,0.837,3.6739,0.5468450243465037,2024-02-01 22:49:00+00:00,2025-01-30 20:49:00+00:00,0.252,0.578,0.3036,-0.195,3.07,0.7,0.00535,2.97
180,180,"-1*ts_Scale(ts_StdevChg(ts_Sum(df['p2_et7'],8),2),48)","239_-1*ts_Scale(ts_StdevChg(ts_Sum(df['p2_et7'],8),2),48)",9.8679,0.0042,0.8373,3.853,0.2203365115745976,2024-02-01 22:49:00+00:00,2025-01-30 20:49:00+00:00,0.445,0.386,0.5355,0.034,3.16,0.55,0.00409,2.56
299,299,"-1*pn_GroupNeutral(ts_Delta(df['p6_tn5'],25),Exp(get_LINEARREG_ANGLE(df['ultosc'],42)))","336_-1*pn_GroupNeutral(ts_Delta(df['p6_tn5'],25),Exp(get_LINEARREG_ANGLE(df['ultosc'],42)))",9.8671,0.0055,0.8489,3.3048,0.564908421666823,2024-02-01 22:49:00+00:00,2025-01-30 20:49:00+00:00,0.42,0.422,0.4988,-0.18,3.08,0.69,0.00631,3.15
255,255,"-1*Add(Min(ts_Median(df['p6_tn2'],25),pn_GroupNorm(df['p5_to0'],df['p5_to3'])),df['p2_et14'])","302_-1*Add(Min(ts_Median(df['p6_tn2'],25),pn_GroupNorm(df['p5_to0'],df['p5_to3'])),df['p2_et14'])",9.8502,0.0056,0.8293,3.8332,0.5647647832660067,2024-02-01 22:49:00+00:00,2025-01-30 20:49:00+00:00,0.537,0.286,0.6525,-0.136,4.27,0.79,0.0064,3.69
416,416,"-1*Min(pn_TransStd(df['ultosc']),Abs(df['p1_corrs9']))","427_-1*Min(pn_TransStd(df['ultosc']),Abs(df['p1_corrs9']))",9.8473,0.0046,0.8231,4.1617,0.5637874928522331,2024-02-01 22:49:00+00:00,2025-01-30 20:49:00+00:00,0.282,0.534,0.3456,-0.121,3.28,0.63,0.00404,2.69
10,10,"-1*get_CCI(pn_TransStd(Softsign(df['p6_tn10'])),And(df['p2_et4'],Not(df['p6_tn3'])),ts_Cov2(ts_Entropy(df['p6_tn0'],20),Not(df['p2_et0']),26),32)","34_-1*get_CCI(pn_TransStd(Softsign(df['p6_tn10'])),And(df['p2_et4'],Not(df['p6_tn3'])),ts_Cov2(ts_Entropy(df['p6_tn0'],20),Not(df['p2_et0']),26),32)",9.8407,0.0058,0.8389,3.4851,0.4845833065330947,2024-02-01 22:49:00+00:00,2025-01-30 20:49:00+00:00,0.36,0.473,0.4322,-0.177,2.62,0.57,0.00487,2.56
59,59,"pn_GroupRank(get_LINEARREG_ANGLE(ts_Argmax(df['p3_mf4'],3),12),df['p1_corrs1'])","111_pn_GroupRank(get_LINEARREG_ANGLE(ts_Argmax(df['p3_mf4'],3),12),df['p1_corrs1'])",9.8372,0.0048,0.8331,3.7962,0.5104548505359394,2024-02-01 22:49:00+00:00,2025-01-30 20:49:00+00:00,0.33,0.505,0.3952,-0.259,3.38,0.64,0.00526,2.98
322,322,"-1*get_LINEARREG_SLOPE(df['p2_et0'],10)","359_-1*get_LINEARREG_SLOPE(df['p2_et0'],10)",9.8268,0.0047,0.845,3.4246,0.5463993794809652,2024-02-01 22:49:00+00:00,2025-01-30 20:49:00+00:00,0.281,0.558,0.3349,-0.271,3.5,0.79,0.00583,3.32
43,43,"-1*pn_GroupNorm(get_MINUS_DM(Log(df['di']),pn_Cut(df['p2_et2']),26),ts_Cov2(Abs(df['cci']),get_CMO(df['p6_tn13'],29),35))","85_-1*pn_GroupNorm(get_MINUS_DM(Log(df['di']),pn_Cut(df['p2_et2']),26),ts_Cov2(Abs(df['cci']),get_CMO(df['p6_tn13'],29),35))",9.8265,0.004,0.8498,3.387,0.3243319139262833,2024-02-01 22:49:00+00:00,2025-01-30 20:49:00+00:00,0.51,0.334,0.6043,-0.048,3.24,0.7,0.0039,2.74
201,201,"ts_Regression(df['p3_mf11'],df['p2_et14'],36,'B')","254_ts_Regression(df['p3_mf11'],df['p2_et14'],36,'B')",9.8253,0.0028,0.8509,3.5297,0.2041713577024989,2024-02-01 22:49:00+00:00,2025-01-30 20:49:00+00:00,0.267,0.586,0.313,-0.001,3.81,0.82,0.00337,2.97
258,258,"Add(ts_Kurtosis(ts_Median(df['p5_to0'],4),23),Min(ts_MeanChg(df['p5_to0'],8),Log(df['p2_et15'])))","304_Add(ts_Kurtosis(ts_Median(df['p5_to0'],4),23),Min(ts_MeanChg(df['p5_to0'],8),Log(df['p2_et15'])))",9.8243,0.0045,0.8396,3.6069,0.4322121849539789,2024-02-01 22:49:00+00:00,2025-01-30 20:49:00+00:00,0.503,0.338,0.5981,-0.15,2.85,0.57,0.00359,2.38
202,202,"-1*pn_GroupNeutral(ts_Regression(df['p2_et1'],ts_ChgRate(df['p2_et1'],40),10,'D'),ts_Decay(Softsign(df['p5_to7']),8))","255_-1*pn_GroupNeutral(ts_Regression(df['p2_et1'],ts_ChgRate(df['p2_et1'],40),10,'D'),ts_Decay(Softsign(df['p5_to7']),8))",9.8225,0.0042,0.8392,3.6681,0.5514099583054664,2024-02-01 22:49:00+00:00,2025-01-30 20:49:00+00:00,0.304,0.529,0.3649,-0.088,2.27,0.56,0.0039,2.24
19,19,"Minus(ts_ChgRate(pn_GroupRank(df['p1_corrs7'],df['p4_ms5']),12),Minus(pn_RankCentered(df['p3_mf6']),pn_FillMin(df['p4_ms3'])))","52_Minus(ts_ChgRate(pn_GroupRank(df['p1_corrs7'],df['p4_ms5']),12),Minus(pn_RankCentered(df['p3_mf6']),pn_FillMin(df['p4_ms3'])))",9.8189,0.0029,0.8317,4.0634,0.3073331023102358,2024-02-01 22:49:00+00:00,2025-01-30 20:49:00+00:00,0.263,0.57,0.3157,-0.03,2.03,0.37,0.00224,1.59
50,50,"Minus(df['p2_et2'],get_CCI(IfThen(df['p5_to5'],20,19),get_KAMA(df['cci'],42),Reverse(df['p1_corrs4']),20))","105_Minus(df['p2_et2'],get_CCI(IfThen(df['p5_to5'],20,19),get_KAMA(df['cci'],42),Reverse(df['p1_corrs4']),20))",9.8177,0.0053,0.8452,3.2961,0.5057258907667258,2024-02-01 22:49:00+00:00,2025-01-30 20:49:00+00:00,0.274,0.573,0.3235,-0.362,1.6,0.42,0.00375,1.81
379,379,"get_CCI(df['p6_tn5'],ts_MeanChg(df['p6_tn13'],36),ts_Corr(df['p4_ms4'],df['p4_ms4'],12),13)","400_get_CCI(df['p6_tn5'],ts_MeanChg(df['p6_tn13'],36),ts_Corr(df['p4_ms4'],df['p4_ms4'],12),13)",9.8162,0.0047,0.8347,3.7068,0.5041529135129832,2024-02-01 22:49:00+00:00,2025-01-30 20:49:00+00:00,0.528,0.309,0.6308,-0.193,3.62,0.71,0.00488,3.07
42,42,"-1*pn_GroupNorm(get_MINUS_DM(pn_Cut(df['p2_et2']),pn_Cut(df['p5_to0']),6),ts_Max(df['p4_ms0'],33))","84_-1*pn_GroupNorm(get_MINUS_DM(pn_Cut(df['p2_et2']),pn_Cut(df['p5_to0']),6),ts_Max(df['p4_ms0'],33))",9.8154,0.0044,0.8319,3.8325,0.284882682699472,2024-02-01 22:49:00+00:00,2025-01-30 20:49:00+00:00,0.458,0.367,0.5552,0.013,1.3,0.22,0.00227,1.16
301,301,"ts_MeanChg(ts_Scale(df['p6_tn1'],14),10)","338_ts_MeanChg(ts_Scale(df['p6_tn1'],14),10)",9.8146,0.0046,0.8447,3.4193,0.5527438013319087,2024-02-01 22:49:00+00:00,2025-01-30 20:49:00+00:00,0.414,0.433,0.4888,-0.22,3.5,0.74,0.0055,3.19
116,116,"-1*pn_GroupNeutral(pn_GroupNorm(ts_Mean(df['p6_tn2'],6),ts_Mean(df['p3_mf2'],3)),UnEqual(df['liangle'],df['p6_tn9']))","173_-1*pn_GroupNeutral(pn_GroupNorm(ts_Mean(df['p6_tn2'],6),ts_Mean(df['p3_mf2'],3)),UnEqual(df['liangle'],df['p6_tn9']))",9.8143,0.0048,0.8454,3.3612,0.2052593376937607,2024-02-01 22:49:00+00:00,2025-01-30 20:49:00+00:00,0.396,0.443,0.472,-0.14,2.98,0.64,0.00478,2.75
166,166,"SignedPower(ts_Rank(ts_Rank(df['dm'],23),6),ts_Rank(df['p6_tn10'],0.463))","231_SignedPower(ts_Rank(ts_Rank(df['dm'],23),6),ts_Rank(df['p6_tn10'],0.463))",9.8105,0.0052,0.8421,3.3831,0.5939816285173074,2024-02-01 22:49:00+00:00,2025-01-30 20:49:00+00:00,0.462,0.382,0.5474,0.014,2.7,0.59,0.00481,2.6
357,357,"-1*ts_Regression(pn_RankCentered(df['p3_mf4']),ts_Quantile(pn_Winsor(df['p3_mf7'],46),12,'D'),12,'D')","385_-1*ts_Regression(pn_RankCentered(df['p3_mf4']),ts_Quantile(pn_Winsor(df['p3_mf7'],46),12,'D'),12,'D')",9.8104,0.0042,0.8274,3.9834,0.3612507086216748,2024-02-01 22:49:00+00:00,2025-01-30 20:49:00+00:00,0.321,0.5,0.391,-0.211,2.3,0.48,0.00347,2.06
12,12,"-1*Add(pn_Winsor(df['p3_mf10'],7),Add(FilterInf(df['p5_to0']),Minus(df['p1_corrs2'],df['p2_et16'])))","40_-1*Add(pn_Winsor(df['p3_mf10'],7),Add(FilterInf(df['p5_to0']),Minus(df['p1_corrs2'],df['p2_et16'])))",9.81,0.004,0.8403,3.6156,0.4486619972153523,2024-02-01 22:49:00+00:00,2025-01-30 20:49:00+00:00,0.495,0.339,0.5935,-0.123,3.22,0.72,0.00378,2.73
228,228,"-1*Minus(get_CCI(get_DX(df['p6_tn3'],df['p5_to3'],df['p3_mf11'],13),IfThen(df['p6_tn13'],45,39),ts_Divide(df['p5_to3'],1),26),Exp(pn_CrossFit(df['p5_to6'],df['p6_tn5'])))","284_-1*Minus(get_CCI(get_DX(df['p6_tn3'],df['p5_to3'],df['p3_mf11'],13),IfThen(df['p6_tn13'],45,39),ts_Divide(df['p5_to3'],1),26),Exp(pn_CrossFit(df['p5_to6'],df['p6_tn5'])))",9.8076,0.0054,0.8302,3.7077,0.5959850486979338,2024-02-01 22:49:00+00:00,2025-01-30 20:49:00+00:00,0.26,0.564,0.3155,-0.114,2.92,0.56,0.00492,2.66
155,155,"-1*Multiply(ts_ChgRate(df['p3_mf7'],10),Power(pn_TransNorm(df['p5_to0']),17))","223_-1*Multiply(ts_ChgRate(df['p3_mf7'],10),Power(pn_TransNorm(df['p5_to0']),17))",9.8018,0.0035,0.8459,3.5008,0.3013055783112795,2024-02-01 22:49:00+00:00,2025-01-30 20:49:00+00:00,0.347,0.492,0.4136,-0.019,1.83,0.38,0.002,1.48
7,7,"-1*get_CCI(pn_GroupRank(df['p2_et1'],ts_Entropy(df['p2_et17'],9)),get_LINEARREG_ANGLE(Max(df['p2_et14'],df['p2_et6']),5),get_MINUS_DM(df['p2_et13'],ts_Corr2(df['p2_et7'],df['p3_mf4'],44),35),35)","28_-1*get_CCI(pn_GroupRank(df['p2_et1'],ts_Entropy(df['p2_et17'],9)),get_LINEARREG_ANGLE(Max(df['p2_et14'],df['p2_et6']),5),get_MINUS_DM(df['p2_et13'],ts_Corr2(df['p2_et7'],df['p3_mf4'],44),35),35)",9.8003,0.0047,0.8246,3.9562,0.4590692992280478,2024-02-01 22:49:00+00:00,2025-01-30 20:49:00+00:00,0.286,0.532,0.3496,-0.069,2.43,0.42,0.00374,2.08
120,120,"-1*pn_GroupRank(ts_Decay2(get_CMO(df['p2_et0'],38),6),ts_Argmax(ts_Mean(df['p6_tn2'],6),26))","175_-1*pn_GroupRank(ts_Decay2(get_CMO(df['p2_et0'],38),6),ts_Argmax(ts_Mean(df['p6_tn2'],6),26))",9.798,0.0045,0.8299,3.8148,0.4851971572224494,2024-02-01 22:49:00+00:00,2025-01-30 20:49:00+00:00,0.413,0.411,0.5012,-0.257,3.44,0.63,0.0051,2.95
23,23,"Minus(pn_GroupNeutral(df['di'],ts_Product(df['p4_ms5'],38)),ts_Divide(df['dx'],37))","57_Minus(pn_GroupNeutral(df['di'],ts_Product(df['p4_ms5'],38)),ts_Divide(df['dx'],37))",9.7968,0.0043,0.8428,3.4608,0.547798610773969,2024-02-01 22:49:00+00:00,2025-01-30 20:49:00+00:00,0.522,0.323,0.6178,-0.31,1.65,0.34,0.00292,1.56
51,51,"-1*pn_CrossFit(get_KAMA(df['cci'],42),df['p4_ms3'])","107_-1*pn_CrossFit(get_KAMA(df['cci'],42),df['p4_ms3'])",9.796,0.0053,0.8276,3.7604,0.5474467610224443,2024-02-01 22:49:00+00:00,2025-01-30 20:49:00+00:00,0.398,0.423,0.4848,-0.11,0.47,0.1,0.00163,0.61
16,16,"Minus(ts_Median(get_HT_DCPERIOD(df['p3_mf6']),31),Add(df['p6_tn4'],UnEqual(df['p4_ms6'],df['p3_mf4'])))","46_Minus(ts_Median(get_HT_DCPERIOD(df['p3_mf6']),31),Add(df['p6_tn4'],UnEqual(df['p4_ms6'],df['p3_mf4'])))",9.7925,0.0054,0.8276,3.7354,0.5076322482817509,2024-02-01 22:49:00+00:00,2025-01-30 20:49:00+00:00,0.453,0.376,0.5464,-0.096,2.94,0.54,0.00499,2.65
295,295,"-1*pn_GroupRank(ts_TransNorm(ts_Delta(df['p6_tn13'],27),28),df['p2_et6'])","333_-1*pn_GroupRank(ts_TransNorm(ts_Delta(df['p6_tn13'],27),28),df['p2_et6'])",9.7781,0.0052,0.8232,3.8604,0.5875547965996734,2024-02-01 22:49:00+00:00,2025-01-30 20:49:00+00:00,0.377,0.44,0.4614,-0.113,2.75,0.49,0.00426,2.38
60,60,"-1*Reverse(pn_GroupRank(df['di'],df['p2_et15']))","113_-1*Reverse(pn_GroupRank(df['di'],df['p2_et15']))",9.7738,0.0042,0.8361,3.6022,0.5937974493706396,2024-02-01 22:49:00+00:00,2025-01-30 20:49:00+00:00,0.564,0.266,0.6795,-0.343,2.44,0.49,0.00387,2.2
136,136,"-1*pn_GroupNeutral(pn_Winsor(ts_Delta(df['p3_mf11'],40),44),Minus(pn_TransNorm(df['p3_mf4']),ts_CorrChg(df['p3_mf9'],df['p2_et8'],44)))","197_-1*pn_GroupNeutral(pn_Winsor(ts_Delta(df['p3_mf11'],40),44),Minus(pn_TransNorm(df['p3_mf4']),ts_CorrChg(df['p3_mf9'],df['p2_et8'],44)))",9.7736,0.0052,0.838,3.4045,0.5542547095710143,2024-02-01 22:49:00+00:00,2025-01-30 20:49:00+00:00,0.422,0.41,0.5072,-0.182,2.52,0.64,0.00531,2.7
290,290,"-1*pn_GroupRank(Minus(Minus(df['p1_corrs1'],35),Exp(df['p2_et13'])),ts_Cov(Abs(df['p2_et4']),ts_Median(df['p2_et17'],43),5))","328_-1*pn_GroupRank(Minus(Minus(df['p1_corrs1'],35),Exp(df['p2_et13'])),ts_Cov(Abs(df['p2_et4']),ts_Median(df['p2_et17'],43),5))",9.7732,0.0047,0.8315,3.6664,0.5311518937858204,2024-02-01 22:49:00+00:00,2025-01-30 20:49:00+00:00,0.309,0.516,0.3745,-0.254,2.49,0.47,0.00374,2.17
353,353,"-1*pn_GroupNeutral(Divide(df['p2_et7'],df['p1_corrs5']),pn_CrossFit(df['p1_corrs8'],ts_MeanChg(df['p6_tn4'],33)))","383_-1*pn_GroupNeutral(Divide(df['p2_et7'],df['p1_corrs5']),pn_CrossFit(df['p1_corrs8'],ts_MeanChg(df['p6_tn4'],33)))",9.7718,0.005,0.8442,3.2347,0.592869290272807,2024-02-01 22:49:00+00:00,2025-01-30 20:49:00+00:00,0.548,0.29,0.6539,-0.415,2.18,0.5,0.00494,2.34
413,413,"-1*Min(get_CCI(ts_Max(df['p6_tn2'],47),df['p2_et2'],df['p4_ms4'],30),pn_Rank(df['p4_ms1']))","424_-1*Min(get_CCI(ts_Max(df['p6_tn2'],47),df['p2_et2'],df['p4_ms4'],30),pn_Rank(df['p4_ms1']))",9.7709,0.0047,0.8376,3.4748,0.5542684057183221,2024-02-01 22:49:00+00:00,2025-01-30 20:49:00+00:00,0.248,0.584,0.2981,-0.173,2.26,0.49,0.00423,2.21
79,79,"ts_Scale(get_MINUS_DI(Power(df['p6_tn10'],9),ts_Decay(df['p6_tn4'],5),df['ultosc'],9),28)","131_ts_Scale(get_MINUS_DI(Power(df['p6_tn10'],9),ts_Decay(df['p6_tn4'],5),df['ultosc'],9),28)",9.7691,0.0041,0.8355,3.6331,0.4949937000149041,2024-02-01 22:49:00+00:00,2025-01-30 20:49:00+00:00,0.528,0.309,0.6308,-0.091,3.03,0.59,0.00477,2.7
384,384,"get_CCI(get_LINEARREG_SLOPE(Log(df['p2_et16']),50),Softsign(df['dm']),ts_Divide(df['p2_et11'],42),14)","405_get_CCI(get_LINEARREG_SLOPE(Log(df['p2_et16']),50),Softsign(df['dm']),ts_Divide(df['p2_et11'],42),14)",9.7678,0.0046,0.8263,3.8185,0.4297995289082256,2024-02-01 22:49:00+00:00,2025-01-30 20:49:00+00:00,0.568,0.26,0.686,-0.057,3.66,0.7,0.00521,3.14
291,291,"-1*pn_GroupRank(get_CMO(Sqrt(df['p6_tn13']),48),Minus(get_CMO(df['p6_tn9'],43),ts_Product(df['p5_to2'],15)))","330_-1*pn_GroupRank(get_CMO(Sqrt(df['p6_tn13']),48),Minus(get_CMO(df['p6_tn9'],43),ts_Product(df['p5_to2'],15)))",9.7667,0.0049,0.8308,3.634,0.5905159004167525,2024-02-01 22:49:00+00:00,2025-01-30 20:49:00+00:00,0.492,0.333,0.5964,-0.12,2.55,0.49,0.00423,2.31
280,280,"-1*Max(Abs(pn_Rank(df['ultosc'])),ts_Max(pn_TransStd(df['p2_et0']),2))","322_-1*Max(Abs(pn_Rank(df['ultosc'])),ts_Max(pn_TransStd(df['p2_et0']),2))",9.7628,0.0052,0.8368,3.4116,0.5461762683113316,2024-02-01 22:49:00+00:00,2025-01-30 20:49:00+00:00,0.446,0.384,0.5373,-0.261,2.74,0.63,0.0056,2.82
121,121,"-1*pn_GroupRank(ts_Median(pn_Rank2(df['p2_et4']),1),ts_StdevChg(ts_Cov(df['p5_to6'],df['p3_mf6'],46),49))","176_-1*pn_GroupRank(ts_Median(pn_Rank2(df['p2_et4']),1),ts_StdevChg(ts_Cov(df['p5_to6'],df['p3_mf6'],46),49))",9.7584,0.0049,0.8397,3.3419,0.5129930080767935,2024-02-01 22:49:00+00:00,2025-01-30 20:49:00+00:00,0.453,0.38,0.5438,-0.319,1.79,0.38,0.0037,1.81
233,233,"pn_CrossFit(pn_GroupNorm(df['p3_mf11'],UnEqual(df['p1_corrs1'],df['p2_et7'])),get_DX(ts_Mean(df['cci'],27),Max(df['dcphase'],df['p2_et7']),df['p3_mf10'],27))","285_pn_CrossFit(pn_GroupNorm(df['p3_mf11'],UnEqual(df['p1_corrs1'],df['p2_et7'])),get_DX(ts_Mean(df['cci'],27),Max(df['dcphase'],df['p2_et7']),df['p3_mf10'],27))",9.7529,0.0052,0.8098,4.1857,0.507455877320681,2024-02-01 22:49:00+00:00,2025-01-30 20:49:00+00:00,0.623,0.188,0.7682,-0.127,-0.94,-0.22,-9e-05,-0.61
362,362,"-1*Min(Max(Divide(df['p6_tn13'],0.171),Power(df['p4_ms5'],9)),ts_Decay2(df['dcphase'],2))","389_-1*Min(Max(Divide(df['p6_tn13'],0.171),Power(df['p4_ms5'],9)),ts_Decay2(df['dcphase'],2))",9.7389,0.0054,0.8323,3.4294,0.5120275980309124,2024-02-01 22:49:00+00:00,2025-01-30 20:49:00+00:00,0.44,0.386,0.5327,-0.198,2.7,0.55,0.00488,2.56
14,14,"Minus(ts_Stdev2(df['p5_to7'],47),SignedPower(df['p4_ms5'],Sign(df['p6_tn7'])))","44_Minus(ts_Stdev2(df['p5_to7'],47),SignedPower(df['p4_ms5'],Sign(df['p6_tn7'])))",9.7357,0.0044,0.8184,3.994,0.4253396680807225,2024-02-01 22:49:00+00:00,2025-01-30 20:49:00+00:00,0.727,0.093,0.8866,-0.069,2.69,0.66,0.00531,2.78
172,172,"-1*Add(ts_Min(df['p4_ms3'],27),ts_Scale(df['p2_et9'],8))","235_-1*Add(ts_Min(df['p4_ms3'],27),ts_Scale(df['p2_et9'],8))",9.7229,0.0042,0.8285,3.6826,0.5824707538419387,2024-02-01 22:49:00+00:00,2025-01-30 20:49:00+00:00,0.602,0.221,0.7315,-0.127,3.23,0.61,0.0045,2.74
221,221,"ts_Divide(get_MINUS_DI(df['p1_corrs4'],df['p1_corrs4'],df['p6_tn7'],6),4)","275_ts_Divide(get_MINUS_DI(df['p1_corrs4'],df['p1_corrs4'],df['p6_tn7'],6),4)",9.7197,0.0046,0.8391,3.2929,0.4896121784302624,2024-02-01 22:49:00+00:00,2025-01-30 20:49:00+00:00,0.564,0.277,0.6706,-0.244,2.61,0.55,0.0044,2.44
367,367,"-1*ts_Argmax(ts_TransNorm(pn_CrossFit(df['p6_tn7'],df['p6_tn6']),41),10)","391_-1*ts_Argmax(ts_TransNorm(pn_CrossFit(df['p6_tn7'],df['p6_tn6']),41),10)",9.7196,0.0041,0.8122,4.1778,0.4475749650815818,2024-02-01 22:49:00+00:00,2025-01-30 20:49:00+00:00,0.365,0.441,0.4529,-0.117,0.78,0.12,0.00091,0.59
370,370,"-1*pn_GroupNeutral(ts_Mean(df['p2_et0'],2),ts_CorrChg(ts_Delay(df['p3_mf10'],17),ts_Entropy(df['p2_et4'],11),1))","392_-1*pn_GroupNeutral(ts_Mean(df['p2_et0'],2),ts_CorrChg(ts_Delay(df['p3_mf10'],17),ts_Entropy(df['p2_et4'],11),1))",9.7176,0.0045,0.8251,3.7194,0.5377139674932795,2024-02-01 22:49:00+00:00,2025-01-30 20:49:00+00:00,0.265,0.554,0.3236,-0.215,2.65,0.72,0.0048,2.74
135,135,"-1*pn_GroupNeutral(pn_Winsor(ts_Delta(df['p2_et0'],18),44),Minus(ts_Delta(df['p4_ms1'],40),Power(df['p6_tn6'],11)))","196_-1*pn_GroupNeutral(pn_Winsor(ts_Delta(df['p2_et0'],18),44),Minus(ts_Delta(df['p4_ms1'],40),Power(df['p6_tn6'],11)))",9.7118,0.0044,0.8295,3.5874,0.5181498983988989,2024-02-01 22:49:00+00:00,2025-01-30 20:49:00+00:00,0.389,0.434,0.4727,-0.066,3.17,0.81,0.00507,3.08
142,142,"get_CCI(pn_GroupNeutral(get_HT_DCPERIOD(df['p2_et4']),df['p2_et0']),ts_TransNorm(df['di'],40),get_HT_DCPERIOD(Minus(df['p3_mf11'],df['p3_mf12'])),12)","203_get_CCI(pn_GroupNeutral(get_HT_DCPERIOD(df['p2_et4']),df['p2_et0']),ts_TransNorm(df['di'],40),get_HT_DCPERIOD(Minus(df['p3_mf11'],df['p3_mf12'])),12)",9.7074,0.0046,0.8058,4.2666,0.3260430375181858,2024-02-01 22:49:00+00:00,2025-01-30 20:49:00+00:00,0.249,0.558,0.3086,-0.096,3.15,0.5,0.00431,2.54
338,338,"-1*Add(Minus(df['p2_et9'],ts_Min(df['p3_mf0'],12)),Sqrt(SignedPower(df['p1_corrs5'],df['p2_et9'])))","374_-1*Add(Minus(df['p2_et9'],ts_Min(df['p3_mf0'],12)),Sqrt(SignedPower(df['p1_corrs5'],df['p2_et9'])))",9.6992,0.0047,0.8331,3.403,0.4970825367245516,2024-02-01 22:49:00+00:00,2025-01-30 20:49:00+00:00,0.551,0.276,0.6663,-0.144,2.67,0.62,0.00525,2.72
408,408,"-1*pn_GroupNeutral(Min(pn_Stand(df['p2_et1']),get_HT_DCPHASE(df['dm'])),Minus(df['p3_mf4'],0.025))","421_-1*pn_GroupNeutral(Min(pn_Stand(df['p2_et1']),get_HT_DCPHASE(df['dm'])),Minus(df['p3_mf4'],0.025))",9.6977,0.0046,0.8282,3.5583,0.5448344382642086,2024-02-01 22:49:00+00:00,2025-01-30 20:49:00+00:00,0.307,0.514,0.3739,-0.114,2.84,0.62,0.00474,2.67
324,324,"-1*get_LINEARREG_ANGLE(pn_GroupRank(pn_CrossFit(df['p6_tn9'],df['p6_tn10']),ts_Max(df['p1_corrs1'],33)),8)","361_-1*get_LINEARREG_ANGLE(pn_GroupRank(pn_CrossFit(df['p6_tn9'],df['p6_tn10']),ts_Max(df['p1_corrs1'],33)),8)",9.6977,0.0049,0.8286,3.5046,0.5808879829335887,2024-02-01 22:49:00+00:00,2025-01-30 20:49:00+00:00,0.335,0.488,0.407,-0.247,2.34,0.47,0.00387,2.14
102,102,"-1*SignedPower(get_HT_DCPHASE(df['p6_tn13']),df['p2_et0'])","157_-1*SignedPower(get_HT_DCPHASE(df['p6_tn13']),df['p2_et0'])",9.6959,0.0056,0.8255,3.4757,0.5750044212135227,2024-02-01 22:49:00+00:00,2025-01-30 20:49:00+00:00,0.391,0.428,0.4774,-0.202,3.41,0.78,0.00641,3.39
178,178,"-1*pn_GroupRank(pn_GroupNorm(df['p4_ms3'],ts_Quantile(df['p4_ms1'],32,'D')),pn_Rank(FilterInf(df['p2_et0'])))","237_-1*pn_GroupRank(pn_GroupNorm(df['p4_ms3'],ts_Quantile(df['p4_ms1'],32,'D')),pn_Rank(FilterInf(df['p2_et0'])))",9.6926,0.0041,0.8203,3.8491,0.4546650751556257,2024-02-01 22:49:00+00:00,2025-01-30 20:49:00+00:00,0.516,0.299,0.6331,-0.117,2.36,0.43,0.0032,1.96
400,400,"Min(ts_Scale(df['p6_tn0'],43),get_CMO(get_KAMA(df['p2_et15'],48),44))","414_Min(ts_Scale(df['p6_tn0'],43),get_CMO(get_KAMA(df['p2_et15'],48),44))",9.6903,0.0041,0.8317,3.5103,0.3678184838505419,2024-02-01 22:49:00+00:00,2025-01-30 20:49:00+00:00,0.535,0.298,0.6423,-0.111,3.56,0.72,0.00505,3.1
244,244,"-1*get_CCI(pn_CrossFit(df['p5_to1'],Sqrt(df['p6_tn10'])),ts_Argmax(df['p2_et15'],14),df['ultosc'],45)","294_-1*get_CCI(pn_CrossFit(df['p5_to1'],Sqrt(df['p6_tn10'])),ts_Argmax(df['p2_et15'],14),df['ultosc'],45)",9.6895,0.0044,0.8181,3.8684,0.512318447027384,2024-02-01 22:49:00+00:00,2025-01-30 20:49:00+00:00,0.632,0.18,0.7783,-0.09,3.24,0.62,0.00453,2.76
334,334,"-1*pn_GroupNorm(Min(ts_StdevChg(df['p5_to7'],25),df['p2_et9']),df['p2_et4'])","370_-1*pn_GroupNorm(Min(ts_StdevChg(df['p5_to7'],25),df['p2_et9']),df['p2_et4'])",9.6878,0.0048,0.8142,3.918,0.5705521297617919,2024-02-01 22:49:00+00:00,2025-01-30 20:49:00+00:00,0.349,0.459,0.4319,-0.176,2.82,0.54,0.00431,2.48
203,203,"-1*ts_Median(ts_Regression(ts_TransNorm(df['p6_tn10'],46),get_CMO(df['p3_mf0'],7),10,'D'),6)","256_-1*ts_Median(ts_Regression(ts_TransNorm(df['p6_tn10'],46),get_CMO(df['p3_mf0'],7),10,'D'),6)",9.6861,0.0058,0.8283,3.3385,0.5360949035962912,2024-02-01 22:49:00+00:00,2025-01-30 20:49:00+00:00,0.386,0.436,0.4696,-0.305,3.58,0.74,0.00628,3.37
323,323,"-1*Min(pn_Winsor(df['p4_ms5'],50),Multiply(ts_Min(df['p5_to2'],8),pn_Rank2(df['cmo'])))","360_-1*Min(pn_Winsor(df['p4_ms5'],50),Multiply(ts_Min(df['p5_to2'],8),pn_Rank2(df['cmo'])))",9.6798,0.0047,0.835,3.2916,0.5796417404046166,2024-02-01 22:49:00+00:00,2025-01-30 20:49:00+00:00,0.38,0.449,0.4584,-0.114,2.86,0.65,0.00602,2.97
302,302,"get_CCI(Divide(df['p4_ms2'],ts_Argmin(df['ultosc'],31)),ts_Decay2(ts_Quantile(df['p4_ms2'],36,'B'),44),ts_Decay2(pn_Winsor(df['p4_ms2'],38),44),15)","341_get_CCI(Divide(df['p4_ms2'],ts_Argmin(df['ultosc'],31)),ts_Decay2(ts_Quantile(df['p4_ms2'],36,'B'),44),ts_Decay2(pn_Winsor(df['p4_ms2'],38),44),15)",9.6795,0.005,0.8235,3.5898,0.5506520898506965,2024-02-01 22:49:00+00:00,2025-01-30 20:49:00+00:00,0.377,0.448,0.457,-0.245,3.2,0.63,0.00554,2.96
431,431,"-1*pn_GroupRank(ts_Skewness(df['p2_et8'],32),pn_Winsor(ts_Sum(df['p1_corrs1'],0.062),8))","441_-1*pn_GroupRank(ts_Skewness(df['p2_et8'],32),pn_Winsor(ts_Sum(df['p1_corrs1'],0.062),8))",9.6786,0.0032,0.8185,4.0038,0.3054701110320199,2024-02-01 22:49:00+00:00,2025-01-30 20:49:00+00:00,0.257,0.555,0.3165,-0.036,3.34,0.55,0.00348,2.5
215,215,"-1*Min(pn_TransStd(ts_Scale(df['p3_mf11'],18)),ts_CovChg(df['p6_tn5'],df['p2_et8'],17))","267_-1*Min(pn_TransStd(ts_Scale(df['p3_mf11'],18)),ts_CovChg(df['p6_tn5'],df['p2_et8'],17))",9.6696,0.0046,0.8202,3.7108,0.4857146865413809,2024-02-01 22:49:00+00:00,2025-01-30 20:49:00+00:00,0.47,0.344,0.5774,-0.071,3.1,0.63,0.00486,2.79
361,361,"-1*Min(ts_Product(df['p4_ms3'],17),ts_Delta(df['p6_tn5'],7))","388_-1*Min(ts_Product(df['p4_ms3'],17),ts_Delta(df['p6_tn5'],7))",9.6694,0.0057,0.8318,3.2013,0.559793960343908,2024-02-01 22:49:00+00:00,2025-01-30 20:49:00+00:00,0.308,0.518,0.3729,-0.21,2.34,0.54,0.00551,2.56
419,419,"-1*Multiply(Sign(ts_Min(df['p6_tn5'],9)),get_LINEARREG_SLOPE(df['p6_tn4'],6))","429_-1*Multiply(Sign(ts_Min(df['p6_tn5'],9)),get_LINEARREG_SLOPE(df['p6_tn4'],6))",9.6621,0.0044,0.8231,3.6307,0.5829140388366758,2024-02-01 22:49:00+00:00,2025-01-30 20:49:00+00:00,0.234,0.583,0.2864,-0.124,2.86,0.57,0.0041,2.49
199,199,"-1*pn_GroupNorm(pn_CrossFit(MEthan(df['p1_corrs1'],df['dx']),ts_Delta(df['p2_et1'],13)),ts_Median(ts_Stdev(df['p3_mf7'],26),45))","251_-1*pn_GroupNorm(pn_CrossFit(MEthan(df['p1_corrs1'],df['dx']),ts_Delta(df['p2_et1'],13)),ts_Median(ts_Stdev(df['p3_mf7'],26),45))",9.6612,0.0045,0.8252,3.5459,0.5750354579713315,2024-02-01 22:49:00+00:00,2025-01-30 20:49:00+00:00,0.421,0.398,0.514,-0.141,2.21,0.34,0.00512,2.19
105,105,"-1*SignedPower(get_CMO(df['p6_tn10'],41),get_CMO(ts_Delta(df['p6_tn13'],1),2))","160_-1*SignedPower(get_CMO(df['p6_tn10'],41),get_CMO(ts_Delta(df['p6_tn13'],1),2))",9.6573,0.0056,0.8243,3.4106,0.5585247380272633,2024-02-01 22:49:00+00:00,2025-01-30 20:49:00+00:00,0.371,0.447,0.4535,-0.156,2.39,0.51,0.0047,2.37
355,355,"pn_GroupNeutral(get_DX(ts_Corr2(df['p3_mf8'],df['p3_mf12'],27),pn_Winsor(df['p3_mf7'],46),df['p6_tn4'],16),ts_Scale(df['p6_tn11'],30))","383_pn_GroupNeutral(get_DX(ts_Corr2(df['p3_mf8'],df['p3_mf12'],27),pn_Winsor(df['p3_mf7'],46),df['p6_tn4'],16),ts_Scale(df['p6_tn11'],30))",9.6564,0.0035,0.8352,3.3844,0.5808313471618368,2024-02-01 22:49:00+00:00,2025-01-30 20:49:00+00:00,0.342,0.495,0.4086,-0.023,2.53,0.59,0.00449,2.48
143,143,"pn_GroupNeutral(ts_Delta(pn_CrossFit(df['ultosc'],df['p1_corrs7']),47),ts_MeanChg(get_CMO(df['p1_corrs6'],27),20))","205_pn_GroupNeutral(ts_Delta(pn_CrossFit(df['ultosc'],df['p1_corrs7']),47),ts_MeanChg(get_CMO(df['p1_corrs6'],27),20))",9.6564,0.0048,0.8235,3.5493,0.4766740769589251,2024-02-01 22:49:00+00:00,2025-01-30 20:49:00+00:00,0.471,0.354,0.5709,-0.2,1.75,0.28,0.00201,1.34
57,57,"-1*pn_GroupRank(ts_TransNorm(get_MINUS_DI(df['dx'],df['p1_corrs7'],df['p5_to6'],38),4),Or(Reverse(df['p1_corrs6']),ts_Sum(df['p3_mf2'],8)))","109_-1*pn_GroupRank(ts_TransNorm(get_MINUS_DI(df['dx'],df['p1_corrs7'],df['p5_to6'],38),4),Or(Reverse(df['p1_corrs6']),ts_Sum(df['p3_mf2'],8)))",9.6548,0.0043,0.8168,3.8174,0.2718298769935041,2024-02-01 22:49:00+00:00,2025-01-30 20:49:00+00:00,0.54,0.27,0.6667,-0.011,3.46,0.66,0.00491,2.96
101,101,"SignedPower(Reverse(df['p1_corrs1']),ts_Skewness(df['p5_to7'],9))","156_SignedPower(Reverse(df['p1_corrs1']),ts_Skewness(df['p5_to7'],9))",9.6534,0.0046,0.8011,4.2361,0.411638593674814,2024-02-01 22:49:00+00:00,2025-01-30 20:49:00+00:00,0.252,0.551,0.3138,-0.169,2.47,0.44,0.00306,1.99
109,109,"get_MINUS_DM(ts_Min(pn_Stand(df['p5_to4']),26),pn_CrossFit(pn_TransStd(df['p5_to7']),df['p5_to7']),49)","166_get_MINUS_DM(ts_Min(pn_Stand(df['p5_to4']),26),pn_CrossFit(pn_TransStd(df['p5_to7']),df['p5_to7']),49)",9.652,0.0037,0.8145,3.9725,0.3122131878344935,2024-02-01 22:49:00+00:00,2025-01-30 20:49:00+00:00,0.575,0.242,0.7038,0.011,1.01,0.19,0.00174,0.92
5,5,"pn_GroupNeutral(get_LINEARREG_ANGLE(get_LINEARREG_SLOPE(df['p2_et9'],37),15),get_DX(ts_Quantile(df['p1_corrs7'],38,'A'),df['cci'],Not(df['p3_mf6']),37))","24_pn_GroupNeutral(get_LINEARREG_ANGLE(get_LINEARREG_SLOPE(df['p2_et9'],37),15),get_DX(ts_Quantile(df['p1_corrs7'],38,'A'),df['cci'],Not(df['p3_mf6']),37))",9.6436,0.004,0.8331,3.3429,0.2384754390814009,2024-02-01 22:49:00+00:00,2025-01-30 20:49:00+00:00,0.348,0.487,0.4168,0.111,3.57,0.81,0.00538,3.28
80,80,"-1*get_CCI(get_CMO(ts_Decay(df['p4_ms1'],9),9),pn_Cut(Power(df['p1_corrs9'],48)),ts_Rank(df['p5_to5'],25),9)","133_-1*get_CCI(get_CMO(ts_Decay(df['p4_ms1'],9),9),pn_Cut(Power(df['p1_corrs9'],48)),ts_Rank(df['p5_to5'],25),9)",9.6415,0.004,0.8197,3.7411,0.3485892832319508,2024-02-01 22:49:00+00:00,2025-01-30 20:49:00+00:00,0.33,0.483,0.4059,-0.072,3.41,0.65,0.00438,2.83
148,148,"pn_GroupNeutral(Mthan(df['p2_et16'],df['p5_to0']),get_KAMA(ts_Decay(df['p5_to5'],22),14))","213_pn_GroupNeutral(Mthan(df['p2_et16'],df['p5_to0']),get_KAMA(ts_Decay(df['p5_to5'],22),14))",9.6408,0.005,0.8043,4.0508,0.4735389162912287,2024-02-01 22:49:00+00:00,2025-01-30 20:49:00+00:00,0.46,0.346,0.5707,-0.092,1.76,0.3,0.00326,1.61
442,442,"-1*pn_GroupNeutral(pn_RankCentered(df['p4_ms3']),ts_Argmin(get_KAMA(df['p2_et3'],48),32))","450_-1*pn_GroupNeutral(pn_RankCentered(df['p4_ms3']),ts_Argmin(get_KAMA(df['p2_et3'],48),32))",9.6375,0.005,0.8165,3.6631,0.5688780204345517,2024-02-01 22:49:00+00:00,2025-01-30 20:49:00+00:00,0.527,0.283,0.6506,-0.1,2.41,0.45,0.00446,2.26
395,395,"-1*Min(df['p4_ms5'],ts_Corr2(df['p2_et14'],df['p4_ms6'],26))","410_-1*Min(df['p4_ms5'],ts_Corr2(df['p2_et14'],df['p4_ms6'],26))",9.6347,0.005,0.8167,3.6521,0.3579705862071976,2024-02-01 22:49:00+00:00,2025-01-30 20:49:00+00:00,0.406,0.404,0.5012,-0.134,3.24,0.67,0.00505,2.93
85,85,"-1*ts_Delta(ts_Rank(df['liangle'],9),9)","139_-1*ts_Delta(ts_Rank(df['liangle'],9),9)",9.634,0.0058,0.822,3.373,0.5298194456170344,2024-02-01 22:49:00+00:00,2025-01-30 20:49:00+00:00,0.289,0.527,0.3542,-0.259,4.62,1.02,0.00833,4.48
300,300,"-1*pn_GroupNeutral(ts_TransNorm(ts_Delta(df['p5_to0'],3),7),ts_Sum(get_LINEARREG_SLOPE(df['p2_et8'],44),45))","337_-1*pn_GroupNeutral(ts_TransNorm(ts_Delta(df['p5_to0'],3),7),ts_Sum(get_LINEARREG_SLOPE(df['p2_et8'],44),45))",9.6315,0.0047,0.8255,3.426,0.5892432214313239,2024-02-01 22:49:00+00:00,2025-01-30 20:49:00+00:00,0.5,0.319,0.6105,0.01,2.85,0.59,0.00505,2.7
130,130,"-1*pn_TransStd(ts_Delta(df['p2_et4'],8))","190_-1*pn_TransStd(ts_Delta(df['p2_et4'],8))",9.6296,0.0053,0.8279,3.2586,0.5402823883705724,2024-02-01 22:49:00+00:00,2025-01-30 20:49:00+00:00,0.35,0.472,0.4258,-0.148,3.28,0.89,0.00572,3.35
372,372,"-1*get_CCI(Reverse(ts_Product(df['dm'],38)),df['p6_tn7'],ts_Entropy(ts_Cov(df['ultosc'],df['p2_et12'],37),44),42)","394_-1*get_CCI(Reverse(ts_Product(df['dm'],38)),df['p6_tn7'],ts_Entropy(ts_Cov(df['ultosc'],df['p2_et12'],37),44),42)",9.6288,0.0051,0.8175,3.5936,0.5798343916479094,2024-02-01 22:49:00+00:00,2025-01-30 20:49:00+00:00,0.381,0.43,0.4698,-0.234,1.2,0.26,0.00347,1.42
319,319,"-1*pn_CrossFit(ts_Argmax(ts_Decay(df['p3_mf11'],27),49),Minus(pn_RankCentered(df['p3_mf11']),ts_ChgRate(df['p4_ms2'],34)))","356_-1*pn_CrossFit(ts_Argmax(ts_Decay(df['p3_mf11'],27),49),Minus(pn_RankCentered(df['p3_mf11']),ts_ChgRate(df['p4_ms2'],34)))",9.6232,0.005,0.8275,3.2937,0.5564471262146663,2024-02-01 22:49:00+00:00,2025-01-30 20:49:00+00:00,0.429,0.392,0.5225,-0.214,1.9,0.43,0.00493,2.16
279,279,"-1*ts_Scale(df['p2_et9'],43)","321_-1*ts_Scale(df['p2_et9'],43)",9.5998,0.0048,0.8264,3.2881,0.5801283266077559,2024-02-01 22:49:00+00:00,2025-01-30 20:49:00+00:00,0.458,0.362,0.5585,-0.223,1.9,0.42,0.00349,1.86
274,274,"-1*get_CCI(pn_GroupNeutral(Reverse(df['p6_tn11']),df['p1_corrs0']),df['p4_ms5'],ts_Scale(df['p6_tn3'],29),43)","317_-1*get_CCI(pn_GroupNeutral(Reverse(df['p6_tn11']),df['p1_corrs0']),df['p4_ms5'],ts_Scale(df['p6_tn3'],29),43)",9.5926,0.0049,0.8177,3.5187,0.4962463254849063,2024-02-01 22:49:00+00:00,2025-01-30 20:49:00+00:00,0.272,0.54,0.335,-0.139,2.24,0.46,0.00491,2.3
174,174,"-1*pn_GroupRank(pn_GroupNorm(df['cci'],ts_Sum(df['p6_tn6'],16)),Max(df['cci'],Softsign(df['p2_et6'])))","236_-1*pn_GroupRank(pn_GroupNorm(df['cci'],ts_Sum(df['p6_tn6'],16)),Max(df['cci'],Softsign(df['p2_et6'])))",9.5868,0.0047,0.8096,3.7656,0.352636351943336,2024-02-01 22:49:00+00:00,2025-01-30 20:49:00+00:00,0.432,0.371,0.538,-0.132,3.68,0.65,0.00587,3.21
208,208,"Min(ts_Stdev2(df['p5_to5'],40),ts_Delta(df['di'],7))","260_Min(ts_Stdev2(df['p5_to5'],40),ts_Delta(df['di'],7))",9.5866,0.0054,0.8218,3.2945,0.4646601988563254,2024-02-01 22:49:00+00:00,2025-01-30 20:49:00+00:00,0.453,0.37,0.5504,-0.311,2.94,0.67,0.0058,2.98
298,298,"-1*get_CCI(df['p2_et0'],pn_Cut(df['p2_et10']),pn_Rank(Or(df['p3_mf3'],df['p4_ms1'])),16)","334_-1*get_CCI(df['p2_et0'],pn_Cut(df['p2_et10']),pn_Rank(Or(df['p3_mf3'],df['p4_ms1'])),16)",9.5854,0.0047,0.8028,3.961,0.4751119415154461,2024-02-01 22:49:00+00:00,2025-01-30 20:49:00+00:00,0.253,0.543,0.3178,-0.081,3.65,0.66,0.00502,3.05
261,261,"-1*Add(pn_GroupRank(df['p2_et17'],ts_Kurtosis(df['p5_to7'],39)),df['p5_to0'])","305_-1*Add(pn_GroupRank(df['p2_et17'],ts_Kurtosis(df['p5_to7'],39)),df['p5_to0'])",9.5815,0.0061,0.8098,3.5387,0.5870402168533766,2024-02-01 22:49:00+00:00,2025-01-30 20:49:00+00:00,0.246,0.557,0.3064,-0.166,2.33,0.8,0.00551,2.88
94,94,"ts_Scale(pn_TransStd(df['p2_et8']),41)","148_ts_Scale(pn_TransStd(df['p2_et8']),41)",9.5786,0.0032,0.8195,3.6732,0.5897544590118963,2024-02-01 22:49:00+00:00,2025-01-30 20:49:00+00:00,0.351,0.47,0.4275,0.035,2.67,0.53,0.00287,2.13
106,106,"-1*SignedPower(get_CCI(get_CMO(df['p2_et0'],35),df['kama'],ts_Delay(df['p2_et0'],45),40),ts_TransNorm(df['p3_mf10'],33))","161_-1*SignedPower(get_CCI(get_CMO(df['p2_et0'],35),df['kama'],ts_Delay(df['p2_et0'],45),40),ts_TransNorm(df['p3_mf10'],33))",9.571,0.0051,0.8128,3.5674,0.5522015712273348,2024-02-01 22:49:00+00:00,2025-01-30 20:49:00+00:00,0.362,0.445,0.4486,-0.142,3.11,0.66,0.00549,2.96
3,3,"-1*pn_GroupNeutral(Min(df['p4_ms3'],ts_Entropy(df['p2_et3'],15)),get_LINEARREG_ANGLE(Reverse(df['p5_to5']),47))","20_-1*pn_GroupNeutral(Min(df['p4_ms3'],ts_Entropy(df['p2_et3'],15)),get_LINEARREG_ANGLE(Reverse(df['p5_to5']),47))",9.5698,0.0042,0.8127,3.6966,0.3866310730071436,2024-02-01 22:49:00+00:00,2025-01-30 20:49:00+00:00,0.675,0.132,0.8364,-0.069,2.2,0.38,0.00302,1.81
30,30,"-1*Add(Or(ts_Entropy(df['p6_tn2'],13),Not(df['p2_et19'])),pn_GroupNorm(ts_Delta(df['p2_et0'],19),ts_Scale(df['p2_et8'],21)))","67_-1*Add(Or(ts_Entropy(df['p6_tn2'],13),Not(df['p2_et19'])),pn_GroupNorm(ts_Delta(df['p2_et0'],19),ts_Scale(df['p2_et8'],21)))",9.5602,0.0047,0.8176,3.4529,0.535540107141707,2024-02-01 22:49:00+00:00,2025-01-30 20:49:00+00:00,0.354,0.457,0.4365,-0.135,3.12,0.74,0.00505,2.98
420,420,"-1*Multiply(Multiply(pn_FillMax(df['p3_mf7']),get_HT_DCPHASE(df['p6_tn2'])),pn_GroupRank(df['p5_to0'],ts_Quantile(df['p6_tn13'],3,'A')))","430_-1*Multiply(Multiply(pn_FillMax(df['p3_mf7']),get_HT_DCPHASE(df['p6_tn2'])),pn_GroupRank(df['p5_to0'],ts_Quantile(df['p6_tn13'],3,'A')))",9.5594,0.0041,0.8083,3.8089,0.4105394726803563,2024-02-01 22:49:00+00:00,2025-01-30 20:49:00+00:00,0.344,0.458,0.4289,-0.075,4.49,0.82,0.00603,3.73
146,146,"-1*Min(pn_GroupNorm(df['p3_mf11'],ts_Min(df['p5_to0'],14)),pn_Stand(df['p2_et14']))","212_-1*Min(pn_GroupNorm(df['p3_mf11'],ts_Min(df['p5_to0'],14)),pn_Stand(df['p2_et14']))",9.5559,0.0052,0.8091,3.609,0.5980104168932429,2024-02-01 22:49:00+00:00,2025-01-30 20:49:00+00:00,0.442,0.361,0.5504,-0.162,4.05,0.86,0.00605,3.64
127,127,"-1*get_CCI(pn_GroupNeutral(df['cmo'],Exp(df['adosc'])),df['dcperiod'],Minus(ts_Argmin(df['p4_ms6'],31),0.696),3)","185_-1*get_CCI(pn_GroupNeutral(df['cmo'],Exp(df['adosc'])),df['dcperiod'],Minus(ts_Argmin(df['p4_ms6'],31),0.696),3)",9.5537,0.0047,0.8082,3.7133,0.5029384992359164,2024-02-01 22:49:00+00:00,2025-01-30 20:49:00+00:00,0.362,0.44,0.4514,-0.076,2.8,0.52,0.00432,2.45
149,149,"pn_GroupNeutral(Mthan(ts_Rank(df['p5_to2'],3),df['p5_to0']),Minus(df['p6_tn11'],df['p2_et9']))","214_pn_GroupNeutral(Mthan(ts_Rank(df['p5_to2'],3),df['p5_to0']),Minus(df['p6_tn11'],df['p2_et9']))",9.545,0.0045,0.8138,3.5505,0.4682697355764231,2024-02-01 22:49:00+00:00,2025-01-30 20:49:00+00:00,0.42,0.396,0.5147,-0.113,2.07,0.42,0.00305,1.82
312,312,"get_CCI(IfThen(get_CMO(df['p2_et5'],11),10,5),Reverse(ts_Decay(df['p6_tn4'],2)),df['p2_et4'],23)","352_get_CCI(IfThen(get_CMO(df['p2_et5'],11),10,5),Reverse(ts_Decay(df['p6_tn4'],2)),df['p2_et4'],23)",9.5441,0.0043,0.8082,3.7429,0.3699292000089769,2024-02-01 22:49:00+00:00,2025-01-30 20:49:00+00:00,0.478,0.332,0.5901,0.028,2.74,0.51,0.00398,2.35
272,272,"-1*get_CCI(get_CCI(df['lislope'],df['p4_ms4'],ts_Delta(df['p6_tn13'],26),44),ts_Cov(ts_StdevChg(df['p2_et6'],1),df['p6_tn13'],43),df['p4_ms2'],35)","315_-1*get_CCI(get_CCI(df['lislope'],df['p4_ms4'],ts_Delta(df['p6_tn13'],26),44),ts_Cov(ts_StdevChg(df['p2_et6'],1),df['p6_tn13'],43),df['p4_ms2'],35)",9.5424,0.0043,0.816,3.5019,0.5577543050714392,2024-02-01 22:49:00+00:00,2025-01-30 20:49:00+00:00,0.531,0.279,0.6556,-0.087,2.56,0.52,0.004,2.3
394,394,"-1*Min(pn_GroupNorm(df['p4_ms5'],df['p4_ms5']),ts_Delta(df['cci'],11))","409_-1*Min(pn_GroupNorm(df['p4_ms5'],df['p4_ms5']),ts_Delta(df['cci'],11))",9.538,0.0062,0.8113,3.3454,0.5217824063427365,2024-02-01 22:49:00+00:00,2025-01-30 20:49:00+00:00,0.34,0.465,0.4224,-0.268,2.98,0.63,0.0063,3.04
398,398,"Min(pn_GroupNorm(df['p1_corrs7'],df['p3_mf0']),df['p2_et12'])","413_Min(pn_GroupNorm(df['p1_corrs7'],df['p3_mf0']),df['p2_et12'])",9.5315,0.0034,0.8212,3.445,0.3602257897169892,2024-02-01 22:49:00+00:00,2025-01-30 20:49:00+00:00,0.499,0.324,0.6063,0.013,3.72,0.77,0.00463,3.13
245,245,"-1*get_CCI(ts_Argmin(df['p4_ms5'],26),df['ultosc'],Equal(df['p2_et9'],df['p6_tn12']),45)","295_-1*get_CCI(ts_Argmin(df['p4_ms5'],26),df['ultosc'],Equal(df['p2_et9'],df['p6_tn12']),45)",9.526,0.0043,0.8153,3.4732,0.5262239492945815,2024-02-01 22:49:00+00:00,2025-01-30 20:49:00+00:00,0.545,0.264,0.6737,-0.171,2.56,0.53,0.00384,2.28
2,2,"-1*pn_GroupNeutral(get_LINEARREG_ANGLE(pn_Rank2(df['p4_ms3']),15),get_DX(ts_Entropy(df['p6_tn8'],15),Max(df['p1_corrs5'],37),SignedPower(df['p6_tn1'],df['p2_et6']),37))","18_-1*pn_GroupNeutral(get_LINEARREG_ANGLE(pn_Rank2(df['p4_ms3']),15),get_DX(ts_Entropy(df['p6_tn8'],15),Max(df['p1_corrs5'],37),SignedPower(df['p6_tn1'],df['p2_et6']),37))",9.5211,0.0054,0.8055,3.5927,0.4348537287455664,2024-02-01 22:49:00+00:00,2025-01-30 20:49:00+00:00,0.469,0.331,0.5862,-0.252,3.18,0.59,0.00525,2.85
222,222,"ts_Cov(df['p3_mf8'],pn_FillMax(df['p3_mf2']),18)","276_ts_Cov(df['p3_mf8'],pn_FillMax(df['p3_mf2']),18)",9.5209,0.0037,0.8267,3.2101,0.1738806645028554,2024-02-01 22:49:00+00:00,2025-01-30 20:49:00+00:00,0.383,0.446,0.462,0.029,3.46,0.79,0.00442,3.02
27,27,"-1*Add(ts_Entropy(pn_Rank(df['p1_corrs3']),50),ts_Delta(df['p6_tn13'],19))","63_-1*Add(ts_Entropy(pn_Rank(df['p1_corrs3']),50),ts_Delta(df['p6_tn13'],19))",9.5145,0.005,0.8105,3.482,0.5510576525435293,2024-02-01 22:49:00+00:00,2025-01-30 20:49:00+00:00,0.255,0.549,0.3172,-0.11,0.98,0.2,0.00247,1.07
236,236,"-1*pn_GroupNeutral(df['p2_et7'],Divide(df['p2_et7'],ts_Rank(df['dcphase'],7)))","286_-1*pn_GroupNeutral(df['p2_et7'],Divide(df['p2_et7'],ts_Rank(df['dcphase'],7)))",9.5129,0.004,0.8222,3.2729,0.5323559092052619,2024-02-01 22:49:00+00:00,2025-01-30 20:49:00+00:00,0.521,0.295,0.6385,-0.123,2.39,0.52,0.00431,2.31
282,282,"-1*get_CCI(Multiply(df['p2_et18'],0.996),Min(pn_RankCentered(df['p6_tn4']),pn_Stand(df['p1_corrs5'])),df['p2_et5'],40)","324_-1*get_CCI(Multiply(df['p2_et18'],0.996),Min(pn_RankCentered(df['p6_tn4']),pn_Stand(df['p1_corrs5'])),df['p2_et5'],40)",9.509,0.0049,0.8063,3.5959,0.5918908987355902,2024-02-01 22:49:00+00:00,2025-01-30 20:49:00+00:00,0.493,0.307,0.6162,-0.098,2.92,0.57,0.00439,2.56
377,377,"-1*Add(pn_CrossFit(df['p2_et12'],df['p3_mf3']),get_CCI(ts_Max(df['p3_mf0'],49),inv(df['p6_tn13']),get_KAMA(df['p2_et14'],47),22))","398_-1*Add(pn_CrossFit(df['p2_et12'],df['p3_mf3']),get_CCI(ts_Max(df['p3_mf0'],49),inv(df['p6_tn13']),get_KAMA(df['p2_et14'],47),22))",9.5085,0.0034,0.8092,3.747,0.195235996556259,2024-02-01 22:49:00+00:00,2025-01-30 20:49:00+00:00,0.333,0.47,0.4147,-0.063,2.94,0.58,0.00356,2.42
374,374,"Add(df['p3_mf4'],get_CCI(LEthan(df['p4_ms2'],df['p2_et9']),FilterInf(df['di']),df['p5_to4'],22))","396_Add(df['p3_mf4'],get_CCI(LEthan(df['p4_ms2'],df['p2_et9']),FilterInf(df['di']),df['p5_to4'],22))",9.5076,0.0058,0.8133,3.2615,0.5703700402741743,2024-02-01 22:49:00+00:00,2025-01-30 20:49:00+00:00,0.287,0.528,0.3521,-0.37,2.03,0.47,0.00576,2.42
310,310,"-1*get_CCI(ts_StdevChg(df['p1_corrs2'],42),pn_FillMax(ts_Rank(df['p3_mf7'],25)),get_LINEARREG_ANGLE(df['p6_tn1'],11),42)","349_-1*get_CCI(ts_StdevChg(df['p1_corrs2'],42),pn_FillMax(ts_Rank(df['p3_mf7'],25)),get_LINEARREG_ANGLE(df['p6_tn1'],11),42)",9.5047,0.0039,0.8129,3.537,0.3997689391472963,2024-02-01 22:49:00+00:00,2025-01-30 20:49:00+00:00,0.579,0.228,0.7175,-0.161,3.66,0.72,0.00486,3.09
350,350,"pn_GroupNeutral(ts_Mean(Reverse(df['p4_ms1']),32),pn_GroupNeutral(ts_Divide(df['p2_et1'],39),ts_Min(df['p2_et2'],2)))","381_pn_GroupNeutral(ts_Mean(Reverse(df['p4_ms1']),32),pn_GroupNeutral(ts_Divide(df['p2_et1'],39),ts_Min(df['p2_et2'],2)))",9.4949,0.0033,0.8194,3.4098,0.5717790302887358,2024-02-01 22:49:00+00:00,2025-01-30 20:49:00+00:00,0.215,0.606,0.2619,-0.035,1.94,0.5,0.00316,1.9
189,189,"-1*pn_GroupNorm(Power(df['p4_ms1'],47),get_CCI(ts_MeanChg(df['p2_et7'],21),df['p3_mf3'],Abs(df['p2_et2']),14))","244_-1*pn_GroupNorm(Power(df['p4_ms1'],47),get_CCI(ts_MeanChg(df['p2_et7'],21),df['p3_mf3'],Abs(df['p2_et2']),14))",9.4916,0.0045,0.8108,3.4809,0.5017489512367949,2024-02-01 22:49:00+00:00,2025-01-30 20:49:00+00:00,0.47,0.335,0.5839,-0.018,3.3,0.74,0.00574,3.17
243,243,"-1*get_CCI(get_CMO(pn_Rank2(df['p2_et4']),12),pn_FillMin(df['p6_tn12']),Abs(df['p2_et6']),45)","292_-1*get_CCI(get_CMO(pn_Rank2(df['p2_et4']),12),pn_FillMin(df['p6_tn12']),Abs(df['p2_et6']),45)",9.4814,0.0043,0.8053,3.6468,0.5690854639354191,2024-02-01 22:49:00+00:00,2025-01-30 20:49:00+00:00,0.429,0.37,0.5369,-0.071,3.12,0.61,0.00481,2.76
164,164,"pn_GroupNorm(ts_Rank(df['dm'],1),ts_Rank(ts_Kurtosis(df['p1_corrs9'],4),22))","230_pn_GroupNorm(ts_Rank(df['dm'],1),ts_Rank(ts_Kurtosis(df['p1_corrs9'],4),22))",9.4806,0.0048,0.812,3.3599,0.5342926946339169,2024-02-01 22:49:00+00:00,2025-01-30 20:49:00+00:00,0.421,0.393,0.5172,0.016,2.54,0.5,0.00416,2.3
29,29,"-1*Add(pn_GroupNeutral(get_MINUS_DI(df['p3_mf7'],df['p6_tn5'],df['p3_mf7'],20),df['p2_et3']),pn_GroupNorm(ts_Delta(df['p2_et9'],19),ts_Scale(df['p2_et8'],21)))","65_-1*Add(pn_GroupNeutral(get_MINUS_DI(df['p3_mf7'],df['p6_tn5'],df['p3_mf7'],20),df['p2_et3']),pn_GroupNorm(ts_Delta(df['p2_et9'],19),ts_Scale(df['p2_et8'],21)))",9.4783,0.0039,0.8153,3.3886,0.4323831146484955,2024-02-01 22:49:00+00:00,2025-01-30 20:49:00+00:00,0.426,0.383,0.5266,-0.163,1.98,0.44,0.0034,1.89
195,195,"ts_MeanChg(Minus(df['p3_mf11'],df['p2_et7']),6)","249_ts_MeanChg(Minus(df['p3_mf11'],df['p2_et7']),6)",9.4734,0.0046,0.8057,3.5662,0.5236928500485925,2024-02-01 22:49:00+00:00,2025-01-30 20:49:00+00:00,0.481,0.327,0.5953,-0.143,3.83,1.1,0.00587,3.83
378,378,"-1*ts_StdevChg(Sqrt(df['p2_et15']),24)","399_-1*ts_StdevChg(Sqrt(df['p2_et15']),24)",9.4719,0.0029,0.8213,3.3398,0.3118000283474744,2024-02-01 22:49:00+00:00,2025-01-30 20:49:00+00:00,0.411,0.404,0.5043,0.099,2.27,0.46,0.00267,1.87
307,307,"-1*get_CCI(Xor(df['p6_tn10'],df['adosc']),df['liangle'],Sqrt(ts_TransNorm(df['p2_et1'],45)),23)","345_-1*get_CCI(Xor(df['p6_tn10'],df['adosc']),df['liangle'],Sqrt(ts_TransNorm(df['p2_et1'],45)),23)",9.4631,0.0052,0.8028,3.5256,0.558095001510206,2024-02-01 22:49:00+00:00,2025-01-30 20:49:00+00:00,0.314,0.483,0.394,-0.185,3.07,0.6,0.0052,2.81
103,103,"-1*SignedPower(get_CCI(df['p6_tn13'],df['p6_tn13'],df['p4_ms2'],36),Power(pn_Stand(df['p5_to2']),36))","158_-1*SignedPower(get_CCI(df['p6_tn13'],df['p6_tn13'],df['p4_ms2'],36),Power(pn_Stand(df['p5_to2']),36))",9.4626,0.005,0.8055,3.4736,0.5501298917561731,2024-02-01 22:49:00+00:00,2025-01-30 20:49:00+00:00,0.455,0.345,0.5688,-0.097,1.95,0.34,0.00283,1.64
396,396,"-1*Minus(ts_Delta(ts_Delta(df['cci'],2),22),pn_GroupRank(ts_Decay(df['p4_ms3'],1),ts_Mean(df['p4_ms5'],16)))","411_-1*Minus(ts_Delta(ts_Delta(df['cci'],2),22),pn_GroupRank(ts_Decay(df['p4_ms3'],1),ts_Mean(df['p4_ms5'],16)))",9.4534,0.0056,0.8095,3.2312,0.4785989036215964,2024-02-01 22:49:00+00:00,2025-01-30 20:49:00+00:00,0.335,0.469,0.4167,-0.051,3.03,0.68,0.00601,3.06
156,156,"-1*ts_TransNorm(pn_GroupNeutral(Exp(df['p6_tn5']),df['p4_ms2']),13)","224_-1*ts_TransNorm(pn_GroupNeutral(Exp(df['p6_tn5']),df['p4_ms2']),13)",9.4435,0.0045,0.8022,3.5849,0.5867571649619143,2024-02-01 22:49:00+00:00,2025-01-30 20:49:00+00:00,0.328,0.468,0.4121,-0.159,3.04,0.56,0.00487,2.69
385,385,"-1*get_CCI(ts_Min(df['p1_corrs7'],47),get_KAMA(df['p2_et3'],44),ts_Decay2(ts_Scale(df['p6_tn5'],6),26),14)","406_-1*get_CCI(ts_Min(df['p1_corrs7'],47),get_KAMA(df['p2_et3'],44),ts_Decay2(ts_Scale(df['p6_tn5'],6),26),14)",9.44,0.0052,0.8036,3.4287,0.4631761152638183,2024-02-01 22:49:00+00:00,2025-01-30 20:49:00+00:00,0.438,0.359,0.5496,-0.163,2.7,0.52,0.00452,2.45
434,434,"-1*pn_GroupNeutral(ts_Delay(Log(df['p6_tn5']),36),ts_CovChg(df['p5_to1'],df['p6_tn9'],5))","443_-1*pn_GroupNeutral(ts_Delay(Log(df['p6_tn5']),36),ts_CovChg(df['p5_to1'],df['p6_tn9'],5))",9.4389,0.0038,0.8013,3.7029,0.4392471864965827,2024-02-01 22:49:00+00:00,2025-01-30 20:49:00+00:00,0.315,0.48,0.3962,0.007,4.23,0.83,0.00471,3.39
316,316,"-1*pn_CrossFit(Minus(ts_Scale(df['p2_et14'],34),get_DX(df['p1_corrs9'],df['dcperiod'],df['p2_et5'],21)),ts_ChgRate(df['p6_tn7'],28))","354_-1*pn_CrossFit(Minus(ts_Scale(df['p2_et14'],34),get_DX(df['p1_corrs9'],df['dcperiod'],df['p2_et5'],21)),ts_ChgRate(df['p6_tn7'],28))",9.4377,0.0051,0.8086,3.2893,0.5192488727075407,2024-02-01 22:49:00+00:00,2025-01-30 20:49:00+00:00,0.411,0.392,0.5118,-0.275,3.27,0.68,0.00544,3.03
122,122,"-1*pn_GroupNeutral(pn_GroupNorm(pn_TransNorm(df['p2_et4']),ts_Rank(df['di'],34)),ts_TransNorm(df['p2_et4'],38))","178_-1*pn_GroupNeutral(pn_GroupNorm(pn_TransNorm(df['p2_et4']),ts_Rank(df['di'],34)),ts_TransNorm(df['p2_et4'],38))",9.4332,0.0041,0.8024,3.6067,0.3257519854633838,2024-02-01 22:49:00+00:00,2025-01-30 20:49:00+00:00,0.41,0.386,0.5151,0.016,2.1,0.42,0.00317,1.86
412,412,"-1*Min(df['p4_ms1'],ts_Cov(get_LINEARREG_ANGLE(df['p5_to0'],6),df['p3_mf8'],27))","423_-1*Min(df['p4_ms1'],ts_Cov(get_LINEARREG_ANGLE(df['p5_to0'],6),df['p3_mf8'],27))",9.4312,0.0049,0.8003,3.544,0.3211304312796358,2024-02-01 22:49:00+00:00,2025-01-30 20:49:00+00:00,0.323,0.471,0.4068,-0.129,3.93,0.85,0.00658,3.69
267,267,"FilterInf(ts_MeanChg(pn_Winsor(df['adosc'],30),4))","310_FilterInf(ts_MeanChg(pn_Winsor(df['adosc'],30),4))",9.4311,0.0042,0.8098,3.3717,0.3623386336605512,2024-02-01 22:49:00+00:00,2025-01-30 20:49:00+00:00,0.425,0.387,0.5234,-0.117,1.91,0.39,0.00267,1.66
54,54,"-1*pn_CrossFit(df['p2_et8'],Lthan(df['p6_tn3'],df['p2_et1']))","108_-1*pn_CrossFit(df['p2_et8'],Lthan(df['p6_tn3'],df['p2_et1']))",9.4113,0.0054,0.8026,3.3472,0.3723892805418706,2024-02-01 22:49:00+00:00,2025-01-30 20:49:00+00:00,0.676,0.12,0.8492,-0.155,2.9,0.6,0.00533,2.78
389,389,"-1*get_LINEARREG_ANGLE(pn_GroupRank(df['p4_ms5'],Mthan(df['cci'],df['p2_et5'])),12)","406_-1*get_LINEARREG_ANGLE(pn_GroupRank(df['p4_ms5'],Mthan(df['cci'],df['p2_et5'])),12)",9.3901,0.0048,0.8001,3.4558,0.5703228535282953,2024-02-01 22:49:00+00:00,2025-01-30 20:49:00+00:00,0.397,0.397,0.5,-0.241,2.72,0.56,0.00494,2.59
28,28,"Add(ts_Cov(pn_TransStd(df['p6_tn6']),df['p2_et7'],24),pn_GroupNorm(ts_Delta(df['p2_et11'],19),ts_TransNorm(df['p5_to2'],5)))","64_Add(ts_Cov(pn_TransStd(df['p6_tn6']),df['p2_et7'],24),pn_GroupNorm(ts_Delta(df['p2_et11'],19),ts_TransNorm(df['p5_to2'],5)))",9.3858,0.004,0.8024,3.4888,0.3592336998851846,2024-02-01 22:49:00+00:00,2025-01-30 20:49:00+00:00,0.508,0.296,0.6318,-0.192,2.31,0.43,0.003,1.91
53,53,"-1*pn_GroupRank(get_LINEARREG_SLOPE(ts_MeanChg(df['p2_et11'],17),4),ts_MeanChg(df['p3_mf1'],11))","107_-1*pn_GroupRank(get_LINEARREG_SLOPE(ts_MeanChg(df['p2_et11'],17),4),ts_MeanChg(df['p3_mf1'],11))",9.3779,0.0048,0.8009,3.389,0.3546192519399305,2024-02-01 22:49:00+00:00,2025-01-30 20:49:00+00:00,0.456,0.339,0.5736,-0.2,3.03,0.59,0.0054,2.83
45,45,"-1*Min(get_LINEARREG_ANGLE(df['cmo'],6),ts_Rank(ts_Mean(df['p2_et14'],24),33))","88_-1*Min(get_LINEARREG_ANGLE(df['cmo'],6),ts_Rank(ts_Mean(df['p2_et14'],24),33))",9.3615,0.0049,0.8021,3.2872,0.5763919644601702,2024-02-01 22:49:00+00:00,2025-01-30 20:49:00+00:00,0.452,0.344,0.5678,-0.381,3.54,0.77,0.00633,3.41
346,346,"-1*ts_Divide(df['p4_ms3'],10)","379_-1*ts_Divide(df['p4_ms3'],10)",9.358,0.0046,0.8053,3.2189,0.5123586310646616,2024-02-01 22:49:00+00:00,2025-01-30 20:49:00+00:00,0.531,0.268,0.6646,-0.126,3.96,0.81,0.00637,3.61
264,264,"ts_Scale(Minus(df['p2_et5'],df['p2_et7']),50)","309_ts_Scale(Minus(df['p2_et5'],df['p2_et7']),50)",9.2989,0.0034,0.8061,3.2022,0.5476784651257858,2024-02-01 22:49:00+00:00,2025-01-30 20:49:00+00:00,0.41,0.398,0.5074,-0.021,2.25,0.49,0.00378,2.12
