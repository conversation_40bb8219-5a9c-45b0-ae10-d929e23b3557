,expr,expr2,result,fac_ic,lsret,sharpe,max<PERSON><PERSON>r,<PERSON>ate,<PERSON><PERSON>ate,rets_up,rets_dn,pct,vCorr
0,"-1*ts_<PERSON>(df['p6_tn1'],46)","4_-1*ts_<PERSON>(df['p6_tn1'],46)",16.948,0.0047,1.5473,3.7125,0.5748934396581054,2024-01-01 20:59:00+00:00,2025-01-30 18:59:00+00:00,0.51,1.164,0.3047,-0.13
1,"pn_<PERSON>Rank(ts_Delay(ts_TransNorm(df['p6_tn1'],48),10),<PERSON><PERSON>(ts_Median(df['p2_et15'],25),ts_Regression(df['p1_corrs8'],df['p6_tn12'],16,'B')))","5_pn_<PERSON>Rank(ts_<PERSON>ay(ts_<PERSON>Norm(df['p6_tn1'],48),10),<PERSON><PERSON>(ts_Median(df['p2_et15'],25),ts_Reg<PERSON>(df['p1_corrs8'],df['p6_tn12'],16,'B')))",12.0279,0.0048,1.0494,3.8874,0.0808808326252287,2024-01-01 20:59:00+00:00,2025-01-30 18:59:00+00:00,0.492,0.644,0.4331,-0.004
2,"-1*pn_GroupRank(ts_Cov(ts_Stdev2(df['p2_et7'],42),Abs(df['p1_corrs5']),34),Minus(df['p6_tn2'],ts_TransNorm(df['p6_tn1'],26)))","12_-1*pn_GroupRank(ts_Cov(ts_Stdev2(df['p2_et7'],42),Abs(df['p1_corrs5']),34),Minus(df['p6_tn2'],ts_TransNorm(df['p6_tn1'],26)))",10.238,0.004,0.8964,3.2208,0.0791898208807326,2024-01-01 20:59:00+00:00,2025-01-30 18:59:00+00:00,0.436,0.534,0.4495,-0.002
3,"pn_GroupRank(Minus(ts_Skewness(df['p2_et15'],34),ts_TransNorm(df['p1_corrs1'],26)),pn_GroupRank(df['p2_et8'],ts_MeanChg(df['cci'],19)))","13_pn_GroupRank(Minus(ts_Skewness(df['p2_et15'],34),ts_TransNorm(df['p1_corrs1'],26)),pn_GroupRank(df['p2_et8'],ts_MeanChg(df['cci'],19)))",9.5141,0.0046,0.8121,3.4956,0.22210558717440676,2024-01-01 20:59:00+00:00,2025-01-30 18:59:00+00:00,0.229,0.649,0.2608,-0.191
4,"-1*pn_GroupRank(ts_Delay(ts_TransNorm(df['cmo'],28),32),pn_GroupNeutral(ts_Stdev(df['p6_tn5'],13),IfThen(df['cmo'],26,43)))","18_-1*pn_GroupRank(ts_Delay(ts_TransNorm(df['cmo'],28),32),pn_GroupNeutral(ts_Stdev(df['p6_tn5'],13),IfThen(df['cmo'],26,43)))",10.5627,0.0042,0.9246,3.3205,0.442416473108948,2024-01-01 20:59:00+00:00,2025-01-30 18:59:00+00:00,0.427,0.573,0.427,0.004
5,"-1*pn_GroupRank(ts_Delay(ts_TransNorm(df['p6_tn1'],24),32),pn_GroupNeutral(df['p4_ms5'],IfThen(df['cmo'],26,13)))","21_-1*pn_GroupRank(ts_Delay(ts_TransNorm(df['p6_tn1'],24),32),pn_GroupNeutral(df['p4_ms5'],IfThen(df['cmo'],26,13)))",10.6172,0.0037,0.9168,3.7967,0.44412985719458486,2024-01-01 20:59:00+00:00,2025-01-30 18:59:00+00:00,0.45,0.542,0.4536,0.001
6,"pn_GroupRank(ts_Delay(ts_Rank(df['p6_tn12'],15),26),get_KAMA(pn_Rank2(df['dcphase']),32))","22_pn_GroupRank(ts_Delay(ts_Rank(df['p6_tn12'],15),26),get_KAMA(pn_Rank2(df['dcphase']),32))",9.5938,0.0035,0.8263,3.4699,0.15019345100978743,2024-01-01 20:59:00+00:00,2025-01-30 18:59:00+00:00,0.707,0.187,0.7908,0.001
7,"pn_GroupRank(ts_Delay(Power(df['p4_ms2'],37),37),pn_GroupNorm(ts_Delta(df['p1_corrs1'],36),IfThen(df['p2_et9'],26,43)))","24_pn_GroupRank(ts_Delay(Power(df['p4_ms2'],37),37),pn_GroupNorm(ts_Delta(df['p1_corrs1'],36),IfThen(df['p2_et9'],26,43)))",10.7285,0.0028,0.9345,3.7268,0.0796787182279738,2024-01-01 20:59:00+00:00,2025-01-30 18:59:00+00:00,0.429,0.582,0.4243,-0.011
8,"-1*pn_GroupRank(ts_Mean(get_MINUS_DI(df['kama'],df['di'],df['cci'],34),24),ts_Delay(ts_Scale(df['p5_to2'],43),32))","24_-1*pn_GroupRank(ts_Mean(get_MINUS_DI(df['kama'],df['di'],df['cci'],34),24),ts_Delay(ts_Scale(df['p5_to2'],43),32))",9.5566,0.0031,0.8212,3.5659,0.09986001911216243,2024-01-01 20:59:00+00:00,2025-01-30 18:59:00+00:00,0.522,0.366,0.5878,0.024
9,"-1*pn_GroupRank(ts_Delay(df['p4_ms5'],32),pn_GroupNeutral(df['p4_ms5'],ts_TransNorm(df['p1_corrs7'],42)))","26_-1*pn_GroupRank(ts_Delay(df['p4_ms5'],32),pn_GroupNeutral(df['p4_ms5'],ts_TransNorm(df['p1_corrs7'],42)))",10.6342,0.0037,0.9412,3.1126,0.3921722792564448,2024-01-01 20:59:00+00:00,2025-01-30 18:59:00+00:00,0.367,0.652,0.3602,-0.001
10,"pn_GroupRank(pn_GroupNeutral(pn_Stand(df['di']),ts_TransNorm(df['p5_to2'],26)),Minus(df['p4_ms5'],ts_Quantile(df['p3_mf8'],10,'C')))","24_pn_GroupRank(pn_GroupNeutral(pn_Stand(df['di']),ts_TransNorm(df['p5_to2'],26)),Minus(df['p4_ms5'],ts_Quantile(df['p3_mf8'],10,'C')))",13.6617,0.0062,1.1956,4.1911,0.517314258476087,2024-01-01 20:59:00+00:00,2025-01-30 18:59:00+00:00,0.703,0.591,0.5433,-0.392
11,"-1*pn_GroupRank(ts_Delay(ts_TransNorm(df['p6_tn1'],24),36),ts_Kurtosis(Divide(df['p1_corrs5'],0.119),26))","25_-1*pn_GroupRank(ts_Delay(ts_TransNorm(df['p6_tn1'],24),36),ts_Kurtosis(Divide(df['p1_corrs5'],0.119),26))",13.1382,0.0053,1.1367,4.5186,0.1575275217746666,2024-01-01 20:59:00+00:00,2025-01-30 18:59:00+00:00,0.371,0.859,0.3016,0.005
12,"-1*get_CCI(Multiply(pn_GroupRank(df['p1_corrs9'],df['p1_corrs9']),df['p1_corrs3']),Multiply(df['p1_corrs1'],ts_ChgRate(df['p2_et2'],42)),df['p3_mf7'],12)","30_-1*get_CCI(Multiply(pn_GroupRank(df['p1_corrs9'],df['p1_corrs9']),df['p1_corrs3']),Multiply(df['p1_corrs1'],ts_ChgRate(df['p2_et2'],42)),df['p3_mf7'],12)",10.2787,0.0034,0.9023,3.2616,0.446796553863527,2024-01-01 20:59:00+00:00,2025-01-30 18:59:00+00:00,0.227,0.75,0.2323,-0.136
13,"-1*get_CCI(Multiply(df['p1_corrs1'],ts_ChgRate(df['p2_et2'],37)),ts_Cov2(get_LINEARREG_SLOPE(df['p2_et7'],20),df['p3_mf4'],9),Abs(get_LINEARREG_ANGLE(df['p1_corrs1'],41)),41)","32_-1*get_CCI(Multiply(df['p1_corrs1'],ts_ChgRate(df['p2_et2'],37)),ts_Cov2(get_LINEARREG_SLOPE(df['p2_et7'],20),df['p3_mf4'],9),Abs(get_LINEARREG_ANGLE(df['p1_corrs1'],41)),41)",11.5889,0.0033,1.0099,3.9806,0.29837835874433155,2024-01-01 20:59:00+00:00,2025-01-30 18:59:00+00:00,0.344,0.748,0.315,-0.052
14,"-1*get_CCI(Multiply(df['p1_corrs1'],ts_ChgRate(df['p2_et2'],9)),LEthan(ts_TransNorm(df['cmo'],5),df['p6_tn12']),UnEqual(df['p4_ms0'],ts_Cov2(df['p6_tn3'],df['ultosc'],9)),12)","37_-1*get_CCI(Multiply(df['p1_corrs1'],ts_ChgRate(df['p2_et2'],9)),LEthan(ts_TransNorm(df['cmo'],5),df['p6_tn12']),UnEqual(df['p4_ms0'],ts_Cov2(df['p6_tn3'],df['ultosc'],9)),12)",9.5499,0.0028,0.8387,3.073,0.3333926601883897,2024-01-01 20:59:00+00:00,2025-01-30 18:59:00+00:00,0.316,0.591,0.3484,-0.038
15,"-1*get_CCI(Lthan(pn_Cut(df['p6_tn7']),df['p1_corrs1']),df['p6_tn10'],Lthan(df['ultosc'],ts_Divide(df['p5_to0'],12)),12)","40_-1*get_CCI(Lthan(pn_Cut(df['p6_tn7']),df['p1_corrs1']),df['p6_tn10'],Lthan(df['ultosc'],ts_Divide(df['p5_to0'],12)),12)",17.8636,0.0062,1.6183,4.1076,0.5229385014558434,2024-01-01 20:59:00+00:00,2025-01-30 18:59:00+00:00,0.502,1.249,0.2867,-0.172
16,"-1*get_CCI(Multiply(df['p1_corrs1'],get_MINUS_DM(df['p1_corrs1'],df['p1_corrs4'],14)),ts_Cov2(df['p1_corrs4'],Add(df['p1_corrs6'],df['p4_ms4']),12),pn_Rank(ts_Stdev2(df['p6_tn9'],22)),12)","39_-1*get_CCI(Multiply(df['p1_corrs1'],get_MINUS_DM(df['p1_corrs1'],df['p1_corrs4'],14)),ts_Cov2(df['p1_corrs4'],Add(df['p1_corrs6'],df['p4_ms4']),12),pn_Rank(ts_Stdev2(df['p6_tn9'],22)),12)",13.5529,0.0034,1.2322,3.1841,0.43567039949392194,2024-01-01 20:59:00+00:00,2025-01-30 18:59:00+00:00,0.615,0.719,0.461,-0.177
17,"-1*get_CCI(df['p2_et13'],Multiply(ts_Max(df['ultosc'],44),LEthan(df['p2_et10'],df['p1_corrs4'])),df['ultosc'],12)","40_-1*get_CCI(df['p2_et13'],Multiply(ts_Max(df['ultosc'],44),LEthan(df['p2_et10'],df['p1_corrs4'])),df['ultosc'],12)",9.861,0.0028,0.8686,3.112,0.27717905146662347,2024-01-01 20:59:00+00:00,2025-01-30 18:59:00+00:00,0.341,0.599,0.3628,-0.075
18,"get_CCI(df['p1_corrs9'],Reverse(pn_TransStd(df['p2_et7'])),df['p3_mf7'],12)","41_get_CCI(df['p1_corrs9'],Reverse(pn_TransStd(df['p2_et7'])),df['p3_mf7'],12)",18.9346,0.0075,1.7298,3.7927,0.5857529929600406,2024-01-01 20:59:00+00:00,2025-01-30 18:59:00+00:00,0.831,1.041,0.4439,-0.482
19,"get_CCI(Lthan(ts_Product(df['cmo'],4),df['p1_corrs1']),ts_Stdev2(df['p6_tn9'],43),Lthan(df['adosc'],df['adosc']),8)","43_get_CCI(Lthan(ts_Product(df['cmo'],4),df['p1_corrs1']),ts_Stdev2(df['p6_tn9'],43),Lthan(df['adosc'],df['adosc']),8)",9.8164,0.0029,0.8597,3.2256,0.21165226138150084,2024-01-01 20:59:00+00:00,2025-01-30 18:59:00+00:00,0.265,0.665,0.2849,-0.082
20,"-1*get_CCI(Multiply(df['p1_corrs1'],df['p4_ms2']),Minus(df['p2_et10'],38),get_LINEARREG_SLOPE(pn_CrossFit(df['p6_tn6'],df['p6_tn2']),38),12)","45_-1*get_CCI(Multiply(df['p1_corrs1'],df['p4_ms2']),Minus(df['p2_et10'],38),get_LINEARREG_SLOPE(pn_CrossFit(df['p6_tn6'],df['p6_tn2']),38),12)",17.6813,0.0037,1.6402,3.2857,0.3377272144743134,2024-01-01 20:59:00+00:00,2025-01-30 18:59:00+00:00,0.974,0.801,0.5487,-0.2
21,"-1*get_CCI(ts_Mean(SignedPower(df['p4_ms1'],0.33),19),df['p3_mf4'],df['p6_tn9'],5)","46_-1*get_CCI(ts_Mean(SignedPower(df['p4_ms1'],0.33),19),df['p3_mf4'],df['p6_tn9'],5)",10.8044,0.0043,0.9493,3.292,0.49436400391678575,2024-01-01 20:59:00+00:00,2025-01-30 18:59:00+00:00,0.594,0.434,0.5778,-0.223
22,"-1*get_CCI(df['ultosc'],pn_TransStd(df['p3_mf1']),ts_Divide(df['p2_et2'],45),11)","49_-1*get_CCI(df['ultosc'],pn_TransStd(df['p3_mf1']),ts_Divide(df['p2_et2'],45),11)",19.5029,0.0046,1.8179,3.281,0.48892361901857645,2024-01-01 20:59:00+00:00,2025-01-30 18:59:00+00:00,0.64,1.327,0.3254,-0.164
23,"-1*get_CCI(LEthan(pn_Winsor(df['p2_et17'],0.359),pn_GroupRank(df['p1_corrs6'],df['p2_et0'])),ts_Delay(df['p5_to0'],36),Lthan(df['p5_to4'],Abs(df['p2_et0'])),12)","55_-1*get_CCI(LEthan(pn_Winsor(df['p2_et17'],0.359),pn_GroupRank(df['p1_corrs6'],df['p2_et0'])),ts_Delay(df['p5_to0'],36),Lthan(df['p5_to4'],Abs(df['p2_et0'])),12)",15.2812,0.0052,1.3493,4.5848,0.39373374699499974,2024-01-01 20:59:00+00:00,2025-01-30 18:59:00+00:00,0.67,0.79,0.4589,0.003
24,"-1*get_CCI(Multiply(ts_Regression(df['p1_corrs3'],df['p6_tn3'],33,'C'),pn_FillMax(df['p1_corrs8'])),Multiply(df['p1_corrs3'],ts_Scale(df['p3_mf7'],47)),Lthan(ts_CorrChg(df['p4_ms6'],df['p6_tn10'],12),Min(df['p1_corrs3'],0.79)),12)","56_-1*get_CCI(Multiply(ts_Regression(df['p1_corrs3'],df['p6_tn3'],33,'C'),pn_FillMax(df['p1_corrs8'])),Multiply(df['p1_corrs3'],ts_Scale(df['p3_mf7'],47)),Lthan(ts_CorrChg(df['p4_ms6'],df['p6_tn10'],12),Min(df['p1_corrs3'],0.79)),12)",10.9623,0.0034,0.9766,3.083,0.5684791637935998,2024-01-01 20:59:00+00:00,2025-01-30 18:59:00+00:00,0.358,0.699,0.3387,-0.171
25,"-1*get_CCI(df['p2_et7'],ts_Kurtosis(Exp(df['p4_ms1']),35),pn_Winsor(ts_Sum(df['lislope'],50),43),12)","57_-1*get_CCI(df['p2_et7'],ts_Kurtosis(Exp(df['p4_ms1']),35),pn_Winsor(ts_Sum(df['lislope'],50),43),12)",11.1763,0.0048,0.9764,3.5133,0.25467741582999925,2024-01-01 20:59:00+00:00,2025-01-30 18:59:00+00:00,0.363,0.693,0.3438,-0.226
26,"-1*get_CCI(get_CCI(ts_Skewness(df['p3_mf2'],13),df['p2_et18'],ts_Scale(df['p4_ms6'],42),36),ts_Delta(Not(df['p4_ms0']),2),Lthan(df['p6_tn13'],Min(df['p1_corrs3'],0.79)),12)","58_-1*get_CCI(get_CCI(ts_Skewness(df['p3_mf2'],13),df['p2_et18'],ts_Scale(df['p4_ms6'],42),36),ts_Delta(Not(df['p4_ms0']),2),Lthan(df['p6_tn13'],Min(df['p1_corrs3'],0.79)),12)",14.4702,0.0053,1.2894,3.9283,0.36002845081873586,2024-01-01 20:59:00+00:00,2025-01-30 18:59:00+00:00,0.602,0.794,0.4312,-0.127
27,"-1*get_CCI(df['p2_et7'],Reverse(df['p1_corrs1']),pn_Winsor(ts_Sum(df['p6_tn13'],11),44),12)","59_-1*get_CCI(df['p2_et7'],Reverse(df['p1_corrs1']),pn_Winsor(ts_Sum(df['p6_tn13'],11),44),12)",13.5284,0.0047,1.2189,3.3141,0.45945814673564855,2024-01-01 20:59:00+00:00,2025-01-30 18:59:00+00:00,0.569,0.75,0.4314,-0.24
28,"-1*get_CCI(df['ultosc'],SignedPower(inv(df['p4_ms3']),ts_Argmax(df['p3_mf12'],50)),get_LINEARREG_ANGLE(Min(df['p5_to0'],0.79),12),45)","60_-1*get_CCI(df['ultosc'],SignedPower(inv(df['p4_ms3']),ts_Argmax(df['p3_mf12'],50)),get_LINEARREG_ANGLE(Min(df['p5_to0'],0.79),12),45)",9.6026,0.004,0.8225,3.5347,0.3283458555617457,2024-01-01 20:59:00+00:00,2025-01-30 18:59:00+00:00,0.524,0.366,0.5888,-0.101
29,"-1*get_CCI(df['ultosc'],pn_RankCentered(df['p5_to7']),pn_FillMin(df['p5_to0']),37)","62_-1*get_CCI(df['ultosc'],pn_RankCentered(df['p5_to7']),pn_FillMin(df['p5_to0']),37)",14.1874,0.0054,1.2875,3.1237,0.5627480815070106,2024-01-01 20:59:00+00:00,2025-01-30 18:59:00+00:00,0.607,0.787,0.4354,-0.255
30,"get_CCI(df['p3_mf0'],Lthan(df['p1_corrs1'],Divide(df['p5_to4'],df['p5_to7'])),get_LINEARREG_SLOPE(df['p4_ms2'],11),12)","64_get_CCI(df['p3_mf0'],Lthan(df['p1_corrs1'],Divide(df['p5_to4'],df['p5_to7'])),get_LINEARREG_SLOPE(df['p4_ms2'],11),12)",12.1378,0.0044,1.0893,3.076,0.46684124363679125,2024-01-01 20:59:00+00:00,2025-01-30 18:59:00+00:00,0.508,0.671,0.4309,-0.154
31,"-1*get_CCI(ts_Scale(df['p2_et9'],11),Lthan(get_LINEARREG_SLOPE(df['p4_ms2'],12),Divide(df['p6_tn9'],50)),df['p2_et5'],12)","67_-1*get_CCI(ts_Scale(df['p2_et9'],11),Lthan(get_LINEARREG_SLOPE(df['p4_ms2'],12),Divide(df['p6_tn9'],50)),df['p2_et5'],12)",12.4036,0.0057,1.1051,3.2019,0.5898130106024791,2024-01-01 20:59:00+00:00,2025-01-30 18:59:00+00:00,0.475,0.721,0.3972,-0.095
32,"get_CCI(get_CCI(ts_Cov(df['p4_ms4'],df['p3_mf2'],15),ts_Decay(df['p6_tn6'],5),df['p6_tn10'],12),df['ultosc'],pn_GroupRank(Abs(df['p2_et10']),pn_TransStd(df['p1_corrs1'])),11)","68_get_CCI(get_CCI(ts_Cov(df['p4_ms4'],df['p3_mf2'],15),ts_Decay(df['p6_tn6'],5),df['p6_tn10'],12),df['ultosc'],pn_GroupRank(Abs(df['p2_et10']),pn_TransStd(df['p1_corrs1'])),11)",17.6805,0.004,1.6434,3.1332,0.5083559162717469,2024-01-01 20:59:00+00:00,2025-01-30 18:59:00+00:00,0.776,1.003,0.4362,-0.175
33,"pn_TransStd(Min(ts_Delta(df['di'],44),pn_GroupRank(df['p2_et8'],df['p5_to4'])))","68_pn_TransStd(Min(ts_Delta(df['di'],44),pn_GroupRank(df['p2_et8'],df['p5_to4'])))",16.7039,0.006,1.5363,3.1279,0.43061942116659696,2024-01-01 20:59:00+00:00,2025-01-30 18:59:00+00:00,0.721,0.942,0.4336,-0.353
34,"-1*ts_TransNorm(pn_GroupNorm(df['p3_mf3'],get_MINUS_DI(df['p1_corrs8'],df['p2_et17'],df['p1_corrs0'],30)),46)","70_-1*ts_TransNorm(pn_GroupNorm(df['p3_mf3'],get_MINUS_DI(df['p1_corrs8'],df['p2_et17'],df['p1_corrs0'],30)),46)",14.3988,0.0013,1.326,3.2225,0.3344908113685759,2024-01-01 20:59:00+00:00,2025-01-30 18:59:00+00:00,0.861,0.574,0.6,-0.038
35,"-1*ts_TransNorm(pn_GroupNorm(pn_Winsor(df['cmo'],33),ts_Decay2(df['cmo'],49)),30)","72_-1*ts_TransNorm(pn_GroupNorm(pn_Winsor(df['cmo'],33),ts_Decay2(df['cmo'],49)),30)",13.4776,0.0074,1.2041,3.1967,0.520449810818821,2024-01-01 20:59:00+00:00,2025-01-30 18:59:00+00:00,0.63,0.673,0.4835,-0.435
36,"get_CCI(Min(ts_Delta(df['di'],44),pn_GroupRank(df['p2_et16'],df['p2_et13'])),Min(df['p1_corrs8'],pn_GroupRank(df['p1_corrs8'],df['p1_corrs8'])),ts_Decay2(df['p1_corrs4'],33),38)","74_get_CCI(Min(ts_Delta(df['di'],44),pn_GroupRank(df['p2_et16'],df['p2_et13'])),Min(df['p1_corrs8'],pn_GroupRank(df['p1_corrs8'],df['p1_corrs8'])),ts_Decay2(df['p1_corrs4'],33),38)",15.2711,0.0039,1.3888,3.5573,0.5367498216644252,2024-01-01 20:59:00+00:00,2025-01-30 18:59:00+00:00,0.353,1.15,0.2349,-0.2
37,"ts_TransNorm(pn_GroupNorm(ts_Delta(df['di'],44),ts_CovChg(df['p6_tn6'],df['p1_corrs6'],32)),46)","76_ts_TransNorm(pn_GroupNorm(ts_Delta(df['di'],44),ts_CovChg(df['p6_tn6'],df['p1_corrs6'],32)),46)",16.0461,0.005,1.4515,3.8408,0.5667148677318966,2024-01-01 20:59:00+00:00,2025-01-30 18:59:00+00:00,0.733,0.838,0.4666,-0.265
38,"-1*pn_TransStd(get_CCI(df['p6_tn8'],df['p5_to6'],df['p2_et18'],22))","77_-1*pn_TransStd(get_CCI(df['p6_tn8'],df['p5_to6'],df['p2_et18'],22))",12.0979,0.003,1.0941,3.0233,0.354710928887837,2024-01-01 20:59:00+00:00,2025-01-30 18:59:00+00:00,0.401,0.783,0.3387,-0.09
39,"-1*get_CCI(Min(ts_Divide(df['dx'],49),pn_GroupRank(df['p1_corrs8'],df['p2_et3'])),pn_GroupRank(ts_Argmin(df['p5_to5'],7),df['p1_corrs8']),pn_GroupNorm(df['p3_mf3'],get_MINUS_DI(df['p4_ms6'],df['p3_mf3'],df['p3_mf9'],4)),38)","78_-1*get_CCI(Min(ts_Divide(df['dx'],49),pn_GroupRank(df['p1_corrs8'],df['p2_et3'])),pn_GroupRank(ts_Argmin(df['p5_to5'],7),df['p1_corrs8']),pn_GroupNorm(df['p3_mf3'],get_MINUS_DI(df['p4_ms6'],df['p3_mf3'],df['p3_mf9'],4)),38)",12.9697,0.0022,1.1654,3.6232,0.22818477405610718,2024-01-01 20:59:00+00:00,2025-01-30 18:59:00+00:00,0.536,0.725,0.4251,-0.083
40,"-1*get_CCI(ts_Decay2(df['p2_et1'],26),pn_GroupRank(df['p6_tn4'],df['cmo']),pn_GroupNorm(df['p3_mf3'],get_MINUS_DI(df['p4_ms6'],df['p3_mf3'],df['p3_mf9'],4)),38)","79_-1*get_CCI(ts_Decay2(df['p2_et1'],26),pn_GroupRank(df['p6_tn4'],df['cmo']),pn_GroupNorm(df['p3_mf3'],get_MINUS_DI(df['p4_ms6'],df['p3_mf3'],df['p3_mf9'],4)),38)",12.4134,0.0036,1.1124,3.3299,0.4555583951897859,2024-01-01 20:59:00+00:00,2025-01-30 18:59:00+00:00,0.686,0.518,0.5698,-0.065
41,"-1*get_CCI(Min(Min(df['cmo'],df['p5_to0']),ts_Corr2(df['p6_tn0'],df['p6_tn0'],49)),ts_Max(df['p6_tn1'],2),SignedPower(Min(df['p6_tn8'],df['p5_to0']),pn_TransNorm(df['p4_ms6'])),38)","79_-1*get_CCI(Min(Min(df['cmo'],df['p5_to0']),ts_Corr2(df['p6_tn0'],df['p6_tn0'],49)),ts_Max(df['p6_tn1'],2),SignedPower(Min(df['p6_tn8'],df['p5_to0']),pn_TransNorm(df['p4_ms6'])),38)",12.9856,0.0056,1.1229,4.4222,0.5179203358859121,2024-01-01 20:59:00+00:00,2025-01-30 18:59:00+00:00,0.759,0.456,0.6247,-0.069
42,"-1*get_CCI(Min(get_LINEARREG_ANGLE(df['p1_corrs7'],50),pn_GroupRank(df['p6_tn4'],df['p5_to6'])),pn_Winsor(df['p2_et13'],46),pn_GroupNorm(df['cmo'],get_MINUS_DI(df['p1_corrs0'],df['cmo'],df['p6_tn8'],4)),38)","79_-1*get_CCI(Min(get_LINEARREG_ANGLE(df['p1_corrs7'],50),pn_GroupRank(df['p6_tn4'],df['p5_to6'])),pn_Winsor(df['p2_et13'],46),pn_GroupNorm(df['cmo'],get_MINUS_DI(df['p1_corrs0'],df['cmo'],df['p6_tn8'],4)),38)",18.4967,0.0067,1.6821,4.0267,0.5023115033339892,2024-01-01 20:59:00+00:00,2025-01-30 18:59:00+00:00,1.052,0.769,0.5777,-0.494
43,"-1*get_CCI(Min(df['p4_ms2'],pn_GroupRank(df['p6_tn4'],df['p5_to6'])),pn_Winsor(df['cmo'],46),pn_GroupNorm(pn_CrossFit(df['p3_mf6'],df['p2_et15']),get_MINUS_DI(df['p1_corrs0'],df['cmo'],df['p6_tn8'],4)),38)","80_-1*get_CCI(Min(df['p4_ms2'],pn_GroupRank(df['p6_tn4'],df['p5_to6'])),pn_Winsor(df['cmo'],46),pn_GroupNorm(pn_CrossFit(df['p3_mf6'],df['p2_et15']),get_MINUS_DI(df['p1_corrs0'],df['cmo'],df['p6_tn8'],4)),38)",23.3432,0.0066,2.1867,3.4405,0.5320103071658875,2024-01-01 20:59:00+00:00,2025-01-30 18:59:00+00:00,1.2,1.167,0.507,-0.487
44,"Minus(pn_Cut(ts_Mean(df['p2_et19'],21)),Min(df['p6_tn4'],pn_GroupRank(df['p6_tn13'],df['cmo'])))","82_Minus(pn_Cut(ts_Mean(df['p2_et19'],21)),Min(df['p6_tn4'],pn_GroupRank(df['p6_tn13'],df['cmo'])))",9.5441,0.0046,0.8209,3.3159,0.41595148630907325,2024-01-01 20:59:00+00:00,2025-01-30 18:59:00+00:00,0.473,0.416,0.5321,-0.012
45,"-1*get_CCI(Min(get_CMO(df['p1_corrs8'],44),df['p5_to0']),ts_MeanChg(pn_GroupRank(df['p1_corrs8'],df['p6_tn4']),7),Softsign(ts_Sum(df['p6_tn2'],1)),46)","83_-1*get_CCI(Min(get_CMO(df['p1_corrs8'],44),df['p5_to0']),ts_MeanChg(pn_GroupRank(df['p1_corrs8'],df['p6_tn4']),7),Softsign(ts_Sum(df['p6_tn2'],1)),46)",17.3054,0.0059,1.5951,3.1707,0.50572001804189,2024-01-01 20:59:00+00:00,2025-01-30 18:59:00+00:00,0.691,1.035,0.4003,-0.071
46,"-1*get_CCI(Min(Min(df['cmo'],df['cmo']),pn_GroupRank(df['p6_tn13'],df['cmo'])),ts_MeanChg(ts_Divide(df['p1_corrs8'],16),7),pn_GroupNeutral(pn_GroupRank(df['p6_tn13'],df['cmo']),Power(df['p3_mf12'],3)),46)","84_-1*get_CCI(Min(Min(df['cmo'],df['cmo']),pn_GroupRank(df['p6_tn13'],df['cmo'])),ts_MeanChg(ts_Divide(df['p1_corrs8'],16),7),pn_GroupNeutral(pn_GroupRank(df['p6_tn13'],df['cmo']),Power(df['p3_mf12'],3)),46)",18.0977,0.006,1.6785,3.0454,0.465840107295552,2024-01-01 20:59:00+00:00,2025-01-30 18:59:00+00:00,0.731,1.086,0.4023,-0.292
47,"-1*get_CCI(ts_Mean(df['p6_tn6'],18),df['cmo'],ts_Regression(Exp(df['p6_tn4']),df['p6_tn4'],4,'A'),38)","85_-1*get_CCI(ts_Mean(df['p6_tn6'],18),df['cmo'],ts_Regression(Exp(df['p6_tn4']),df['p6_tn4'],4,'A'),38)",13.4153,0.0045,1.2095,3.2843,0.5265886707025763,2024-01-01 20:59:00+00:00,2025-01-30 18:59:00+00:00,0.5,0.809,0.382,-0.231
48,"get_CCI(Min(Min(df['p1_corrs0'],df['p6_tn0']),ts_ChgRate(df['p4_ms2'],9)),get_KAMA(pn_Winsor(df['cmo'],46),26),get_CMO(df['p4_ms2'],19),38)","86_get_CCI(Min(Min(df['p1_corrs0'],df['p6_tn0']),ts_ChgRate(df['p4_ms2'],9)),get_KAMA(pn_Winsor(df['cmo'],46),26),get_CMO(df['p4_ms2'],19),38)",15.0286,0.0049,1.3694,3.274,0.5611080371644176,2024-01-01 20:59:00+00:00,2025-01-30 18:59:00+00:00,0.567,0.915,0.3826,-0.159
49,"-1*Minus(Xor(Multiply(df['p2_et2'],0.784),ts_MeanChg(df['p3_mf3'],7)),Min(ts_MeanChg(df['p1_corrs0'],7),df['p5_to6']))","90_-1*Minus(Xor(Multiply(df['p2_et2'],0.784),ts_MeanChg(df['p3_mf3'],7)),Min(ts_MeanChg(df['p1_corrs0'],7),df['p5_to6']))",11.9621,0.0036,1.0623,3.4782,0.4605071346333435,2024-01-01 20:59:00+00:00,2025-01-30 18:59:00+00:00,0.453,0.697,0.3939,-0.173
50,"get_CCI(Min(ts_MeanChg(df['p3_mf3'],7),ts_Corr2(df['adosc'],df['p3_mf2'],3)),ts_MeanChg(df['cmo'],7),ts_Argmax(ts_Min(df['cci'],38),46),46)","92_get_CCI(Min(ts_MeanChg(df['p3_mf3'],7),ts_Corr2(df['adosc'],df['p3_mf2'],3)),ts_MeanChg(df['cmo'],7),ts_Argmax(ts_Min(df['cci'],38),46),46)",13.0501,0.0031,1.1829,3.1979,0.3651899165015887,2024-01-01 20:59:00+00:00,2025-01-30 18:59:00+00:00,0.33,0.951,0.2576,-0.106
51,"-1*get_CCI(Min(Not(df['p4_ms6']),ts_Quantile(df['adosc'],43,'C')),Min(df['p1_corrs9'],df['cci']),pn_GroupNorm(ts_Quantile(df['p2_et6'],17,'B'),get_HT_DCPERIOD(df['p2_et7'])),38)","94_-1*get_CCI(Min(Not(df['p4_ms6']),ts_Quantile(df['adosc'],43,'C')),Min(df['p1_corrs9'],df['cci']),pn_GroupNorm(ts_Quantile(df['p2_et6'],17,'B'),get_HT_DCPERIOD(df['p2_et7'])),38)",18.7434,0.0042,1.749,3.13,0.43958114791407715,2024-01-01 20:59:00+00:00,2025-01-30 18:59:00+00:00,0.451,1.442,0.2382,-0.083
52,"-1*get_CCI(Min(Not(df['p4_ms6']),ts_Quantile(df['adosc'],38,'C')),Min(df['cmo'],df['cci']),ts_Divide(ts_Stdev(df['p6_tn8'],12),44),38)","95_-1*get_CCI(Min(Not(df['p4_ms6']),ts_Quantile(df['adosc'],38,'C')),Min(df['cmo'],df['cci']),ts_Divide(ts_Stdev(df['p6_tn8'],12),44),38)",10.6518,0.0047,0.9286,3.3867,0.30851960008984824,2024-01-01 20:59:00+00:00,2025-01-30 18:59:00+00:00,0.538,0.467,0.5353,-0.119
53,"get_CCI(ts_MeanChg(df['cci'],7),Minus(df['p6_tn3'],Softsign(df['p1_corrs4'])),ts_Product(df['p6_tn0'],43),43)","96_get_CCI(ts_MeanChg(df['cci'],7),Minus(df['p6_tn3'],Softsign(df['p1_corrs4'])),ts_Product(df['p6_tn0'],43),43)",11.5274,0.0038,1.0101,3.7072,0.4974518917429157,2024-01-01 20:59:00+00:00,2025-01-30 18:59:00+00:00,0.508,0.586,0.4644,-0.244
54,"-1*get_CCI(Min(ts_Min(df['p1_corrs7'],20),ts_Corr2(df['p3_mf0'],df['cci'],46)),ts_MeanChg(ts_Argmax(df['p3_mf2'],8),7),Min(df['p1_corrs0'],df['p1_corrs7']),46)","98_-1*get_CCI(Min(ts_Min(df['p1_corrs7'],20),ts_Corr2(df['p3_mf0'],df['cci'],46)),ts_MeanChg(ts_Argmax(df['p3_mf2'],8),7),Min(df['p1_corrs0'],df['p1_corrs7']),46)",14.9342,0.0054,1.3372,3.8841,0.46723629373121817,2024-01-01 20:59:00+00:00,2025-01-30 18:59:00+00:00,0.344,1.103,0.2377,-0.285
55,"-1*get_CCI(Min(Min(df['p1_corrs0'],df['p5_to0']),FilterInf(df['p6_tn10'])),Min(pn_FillMin(df['cmo']),df['cmo']),get_CMO(df['p2_et8'],19),38)","100_-1*get_CCI(Min(Min(df['p1_corrs0'],df['p5_to0']),FilterInf(df['p6_tn10'])),Min(pn_FillMin(df['cmo']),df['cmo']),get_CMO(df['p2_et8'],19),38)",18.8159,0.0063,1.7489,3.0325,0.36403879424843155,2024-01-01 20:59:00+00:00,2025-01-30 18:59:00+00:00,1.003,0.889,0.5301,-0.067
56,"-1*get_CCI(ts_Decay(ts_Product(df['p5_to4'],38),3),Min(ts_Max(df['dx'],24),FilterInf(df['cci'])),pn_CrossFit(df['cci'],df['p2_et18']),28)","101_-1*get_CCI(ts_Decay(ts_Product(df['p5_to4'],38),3),Min(ts_Max(df['dx'],24),FilterInf(df['cci'])),pn_CrossFit(df['cci'],df['p2_et18']),28)",22.6126,0.0057,2.1217,3.3357,0.47171288908347103,2024-01-01 20:59:00+00:00,2025-01-30 18:59:00+00:00,0.895,1.401,0.3898,-0.307
57,"-1*ts_Regression(pn_Rank2(df['p2_et0']),ts_CovChg(df['p1_corrs3'],df['p4_ms0'],0.03),3,'B')","103_-1*ts_Regression(pn_Rank2(df['p2_et0']),ts_CovChg(df['p1_corrs3'],df['p4_ms0'],0.03),3,'B')",11.1801,0.0046,0.9761,3.5733,0.4912058576455349,2024-01-01 20:59:00+00:00,2025-01-30 18:59:00+00:00,0.431,0.625,0.4081,-0.219
58,"-1*ts_Regression(pn_Rank2(df['p2_et0']),get_CCI(pn_GroupRank(df['p5_to6'],df['p2_et18']),df['p2_et4'],ts_Regression(df['p5_to6'],df['p3_mf0'],22,'B'),25),9,'B')","105_-1*ts_Regression(pn_Rank2(df['p2_et0']),get_CCI(pn_GroupRank(df['p5_to6'],df['p2_et18']),df['p2_et4'],ts_Regression(df['p5_to6'],df['p3_mf0'],22,'B'),25),9,'B')",16.5456,0.0029,1.5302,3.2919,0.47408110104097406,2024-01-01 20:59:00+00:00,2025-01-30 18:59:00+00:00,0.503,1.153,0.3037,-0.201
59,"-1*ts_Regression(pn_Rank2(df['p2_et0']),get_CCI(df['p2_et4'],pn_Cut(df['p5_to1']),ts_Regression(df['p5_to6'],df['p3_mf0'],22,'B'),25),2,'B')","106_-1*ts_Regression(pn_Rank2(df['p2_et0']),get_CCI(df['p2_et4'],pn_Cut(df['p5_to1']),ts_Regression(df['p5_to6'],df['p3_mf0'],22,'B'),25),2,'B')",11.5242,0.0041,1.0062,3.7684,0.5864342801349024,2024-01-01 20:59:00+00:00,2025-01-30 18:59:00+00:00,0.363,0.726,0.3333,-0.186
60,"-1*ts_TransNorm(pn_GroupRank(pn_Stand(df['cmo']),ts_Product(df['p4_ms3'],47)),1)","111_-1*ts_TransNorm(pn_GroupRank(pn_Stand(df['cmo']),ts_Product(df['p4_ms3'],47)),1)",20.7221,0.0069,1.8884,4.4749,0.5729119592678387,2024-01-01 20:59:00+00:00,2025-01-30 18:59:00+00:00,1.089,0.955,0.5328,-0.455
61,"-1*ts_Regression(get_CCI(df['p5_to0'],ts_Product(df['p3_mf11'],37),pn_Stand(df['p6_tn4']),29),get_CCI(Lthan(df['p3_mf0'],df['p3_mf0']),Or(df['p6_tn0'],df['p1_corrs2']),ts_Regression(df['p5_to6'],df['p3_mf0'],9,'B'),34),9,'B')","114_-1*ts_Regression(get_CCI(df['p5_to0'],ts_Product(df['p3_mf11'],37),pn_Stand(df['p6_tn4']),29),get_CCI(Lthan(df['p3_mf0'],df['p3_mf0']),Or(df['p6_tn0'],df['p1_corrs2']),ts_Regression(df['p5_to6'],df['p3_mf0'],9,'B'),34),9,'B')",9.4218,0.0042,0.812,3.2759,0.2823003707072078,2024-01-01 20:59:00+00:00,2025-01-30 18:59:00+00:00,0.515,0.364,0.5859,-0.131
62,"-1*ts_Regression(pn_Rank2(df['p2_et0']),get_CCI(pn_Rank2(df['p1_corrs8']),df['p3_mf11'],df['liangle'],29),9,'B')","115_-1*ts_Regression(pn_Rank2(df['p2_et0']),get_CCI(pn_Rank2(df['p1_corrs8']),df['p3_mf11'],df['liangle'],29),9,'B')",9.8514,0.0029,0.8701,3.0089,0.5181561924798754,2024-01-01 20:59:00+00:00,2025-01-30 18:59:00+00:00,0.269,0.673,0.2856,-0.159
63,"-1*ts_TransNorm(ts_Median(Min(df['p6_tn13'],0.376),4),41)","116_-1*ts_TransNorm(ts_Median(Min(df['p6_tn13'],0.376),4),41)",16.3373,0.0049,1.4426,4.9994,0.4633883193307342,2024-01-01 20:59:00+00:00,2025-01-30 18:59:00+00:00,0.68,0.882,0.4353,-0.219
64,"-1*ts_Regression(pn_Rank2(df['p2_et0']),get_CCI(df['cmo'],get_CMO(df['dm'],44),Xor(df['p2_et10'],df['p6_tn0']),29),9,'B')","118_-1*ts_Regression(pn_Rank2(df['p2_et0']),get_CCI(df['cmo'],get_CMO(df['dm'],44),Xor(df['p2_et10'],df['p6_tn0']),29),9,'B')",9.7063,0.0036,0.8415,3.3345,0.3290382166700599,2024-01-01 20:59:00+00:00,2025-01-30 18:59:00+00:00,0.272,0.638,0.2989,-0.053
65,"-1*ts_TransNorm(SignedPower(pn_GroupRank(df['p1_corrs7'],df['p5_to7']),df['p6_tn0']),34)","119_-1*ts_TransNorm(SignedPower(pn_GroupRank(df['p1_corrs7'],df['p5_to7']),df['p6_tn0']),34)",14.3087,0.0056,1.2963,3.1989,0.5601921457302688,2024-01-01 20:59:00+00:00,2025-01-30 18:59:00+00:00,0.386,1.017,0.2751,-0.263
66,"-1*ts_Regression(pn_Rank2(df['p2_et0']),get_CCI(pn_GroupNorm(df['p2_et2'],df['liangle']),ts_Argmax(df['p6_tn11'],13),ts_Regression(df['p5_to6'],df['p1_corrs2'],9,'B'),9),9,'B')","120_-1*ts_Regression(pn_Rank2(df['p2_et0']),get_CCI(pn_GroupNorm(df['p2_et2'],df['liangle']),ts_Argmax(df['p6_tn11'],13),ts_Regression(df['p5_to6'],df['p1_corrs2'],9,'B'),9),9,'B')",10.1089,0.003,0.8925,3.0972,0.45498241131037404,2024-01-01 20:59:00+00:00,2025-01-30 18:59:00+00:00,0.269,0.697,0.2785,-0.154
67,"ts_Delta(ts_MeanChg(df['p2_et10'],2),45)","125_ts_Delta(ts_MeanChg(df['p2_et10'],2),45)",13.7334,0.0049,1.2422,3.2033,0.2955820700842654,2024-01-01 20:59:00+00:00,2025-01-30 18:59:00+00:00,0.535,0.809,0.3981,-0.024
68,"-1*pn_GroupRank(ts_Divide(Exp(df['p1_corrs3']),39),get_HT_DCPERIOD(df['p6_tn1']))","127_-1*pn_GroupRank(ts_Divide(Exp(df['p1_corrs3']),39),get_HT_DCPERIOD(df['p6_tn1']))",12.1094,0.0048,1.0619,3.746,0.4395592690220595,2024-01-01 20:59:00+00:00,2025-01-30 18:59:00+00:00,0.352,0.797,0.3064,-0.186
69,"-1*ts_Delta(get_CCI(ts_Delta(df['p6_tn13'],7),ts_Delta(df['p6_tn13'],7),ts_Skewness(df['ultosc'],28),40),11)","131_-1*ts_Delta(get_CCI(ts_Delta(df['p6_tn13'],7),ts_Delta(df['p6_tn13'],7),ts_Skewness(df['ultosc'],28),40),11)",14.3065,0.0051,1.2831,3.6534,0.5627599073712694,2024-01-01 20:59:00+00:00,2025-01-30 18:59:00+00:00,0.404,0.985,0.2909,-0.099
70,"-1*ts_Delta(get_CCI(pn_Cut(df['p3_mf0']),ts_Skewness(df['p3_mf11'],22),df['cmo'],7),28)","131_-1*ts_Delta(get_CCI(pn_Cut(df['p3_mf0']),ts_Skewness(df['p3_mf11'],22),df['cmo'],7),28)",22.1531,0.0067,2.0284,4.5981,0.5818107283682815,2024-01-01 20:59:00+00:00,2025-01-30 18:59:00+00:00,0.664,1.531,0.3025,-0.26
71,"-1*ts_Delta(get_CCI(ts_Delta(df['p6_tn13'],7),ts_Delta(df['p6_tn13'],7),ts_Skewness(df['ultosc'],27),28),40)","131_-1*ts_Delta(get_CCI(ts_Delta(df['p6_tn13'],7),ts_Delta(df['p6_tn13'],7),ts_Skewness(df['ultosc'],27),28),40)",12.9721,0.0044,1.1602,3.4437,0.5959028135644743,2024-01-01 20:59:00+00:00,2025-01-30 18:59:00+00:00,0.585,0.67,0.4661,-0.096
72,"ts_Delta(ts_Delta(ts_ChgRate(df['p6_tn6'],9),32),28)","132_ts_Delta(ts_Delta(ts_ChgRate(df['p6_tn6'],9),32),28)",13.0437,0.005,1.1336,4.3783,0.2124827141233178,2024-01-01 20:59:00+00:00,2025-01-30 18:59:00+00:00,0.78,0.447,0.6357,-0.093
73,"-1*ts_Delta(get_CCI(pn_Rank2(df['p3_mf11']),pn_FillMax(df['p3_mf6']),get_HT_DCPHASE(df['p6_tn9']),40),28)","133_-1*ts_Delta(get_CCI(pn_Rank2(df['p3_mf11']),pn_FillMax(df['p3_mf6']),get_HT_DCPHASE(df['p6_tn9']),40),28)",11.978,0.0038,1.0742,3.1392,0.41997404451650183,2024-01-01 20:59:00+00:00,2025-01-30 18:59:00+00:00,0.498,0.665,0.4282,-0.118
74,"-1*ts_Delta(get_CCI(ts_CorrChg(df['p2_et19'],df['p1_corrs6'],8),ts_Partial_corr(df['p6_tn13'],df['cmo'],df['p2_et11'],26),ts_Delta(df['p6_tn13'],7),40),29)","133_-1*ts_Delta(get_CCI(ts_CorrChg(df['p2_et19'],df['p1_corrs6'],8),ts_Partial_corr(df['p6_tn13'],df['cmo'],df['p2_et11'],26),ts_Delta(df['p6_tn13'],7),40),29)",18.8482,0.007,1.7019,4.4398,0.5817769718522683,2024-01-01 20:59:00+00:00,2025-01-30 18:59:00+00:00,0.786,1.055,0.4269,-0.094
75,"-1*ts_Delta(get_CCI(ts_MeanChg(df['p2_et19'],22),pn_TransNorm(df['p6_tn1']),pn_GroupNeutral(df['p1_corrs3'],df['p1_corrs1']),34),7)","134_-1*ts_Delta(get_CCI(ts_MeanChg(df['p2_et19'],22),pn_TransNorm(df['p6_tn1']),pn_GroupNeutral(df['p1_corrs3'],df['p1_corrs1']),34),7)",17.936,0.0049,1.6393,3.8968,0.54992840081709,2024-01-01 20:59:00+00:00,2025-01-30 18:59:00+00:00,0.619,1.155,0.3489,-0.154
76,"-1*ts_Delta(pn_GroupNorm(pn_TransNorm(df['p6_tn1']),ts_Delta(df['p1_corrs7'],23)),28)","135_-1*ts_Delta(pn_GroupNorm(pn_TransNorm(df['p6_tn1']),ts_Delta(df['p1_corrs7'],23)),28)",9.4958,0.0038,0.8258,3.1508,0.5490214252901303,2024-01-01 20:59:00+00:00,2025-01-30 18:59:00+00:00,0.244,0.65,0.2729,-0.138
77,"-1*ts_Delta(get_CCI(df['p3_mf3'],pn_TransNorm(df['p6_tn13']),ts_Skewness(df['p4_ms1'],7),36),28)","136_-1*ts_Delta(get_CCI(df['p3_mf3'],pn_TransNorm(df['p6_tn13']),ts_Skewness(df['p4_ms1'],7),36),28)",14.9041,0.0036,1.3679,3.1315,0.5407869263360917,2024-01-01 20:59:00+00:00,2025-01-30 18:59:00+00:00,0.37,1.111,0.2498,-0.123
78,"-1*ts_Delta(ts_Scale(df['p2_et7'],17),28)","137_-1*ts_Delta(ts_Scale(df['p2_et7'],17),28)",12.8697,0.0056,1.1393,3.5981,0.5711781280981324,2024-01-01 20:59:00+00:00,2025-01-30 18:59:00+00:00,0.695,0.538,0.5637,-0.406
79,"-1*ts_Delta(get_CCI(pn_FillMax(df['p3_mf0']),Exp(df['p2_et1']),df['p6_tn1'],48),28)","138_-1*ts_Delta(get_CCI(pn_FillMax(df['p3_mf0']),Exp(df['p2_et1']),df['p6_tn1'],48),28)",15.24,0.0024,1.394,3.5428,0.22278962089899637,2024-01-01 20:59:00+00:00,2025-01-30 18:59:00+00:00,0.446,1.062,0.2958,-0.039
80,"-1*Power(get_CCI(Sqrt(df['p1_corrs6']),Multiply(df['p2_et7'],df['dcphase']),ts_Delta(df['p5_to6'],28),40),7)","139_-1*Power(get_CCI(Sqrt(df['p1_corrs6']),Multiply(df['p2_et7'],df['dcphase']),ts_Delta(df['p5_to6'],28),40),7)",11.8866,0.0048,1.0567,3.2378,0.5601184596977781,2024-01-01 20:59:00+00:00,2025-01-30 18:59:00+00:00,0.292,0.851,0.2555,-0.358
81,"-1*ts_Delta(ts_Rank(ts_Skewness(df['p4_ms5'],7),6),28)","143_-1*ts_Delta(ts_Rank(ts_Skewness(df['p4_ms5'],7),6),28)",11.8168,0.0031,1.0425,3.7142,0.34528000294202726,2024-01-01 20:59:00+00:00,2025-01-30 18:59:00+00:00,0.555,0.573,0.492,-0.041
82,"-1*ts_Delta(get_CCI(df['p6_tn1'],LEthan(df['p6_tn13'],df['p2_et11']),ts_Delta(df['p6_tn13'],8),7),28)","145_-1*ts_Delta(get_CCI(df['p6_tn1'],LEthan(df['p6_tn13'],df['p2_et11']),ts_Delta(df['p6_tn13'],8),7),28)",11.9806,0.0041,1.0615,3.4819,0.4778115582557686,2024-01-01 20:59:00+00:00,2025-01-30 18:59:00+00:00,0.425,0.724,0.3699,-0.046
83,"-1*Power(get_CCI(df['p6_tn1'],ts_Delta(df['p2_et16'],28),ts_Skewness(df['p4_ms5'],7),29),29)","146_-1*Power(get_CCI(df['p6_tn1'],ts_Delta(df['p2_et16'],28),ts_Skewness(df['p4_ms5'],7),29),29)",11.6734,0.004,1.0411,3.1896,0.4890494385157663,2024-01-01 20:59:00+00:00,2025-01-30 18:59:00+00:00,0.253,0.873,0.2247,-0.164
84,"-1*Power(get_CCI(ts_Mean(df['dcphase'],10),get_CMO(df['p2_et2'],34),Not(df['kama']),7),7)","148_-1*Power(get_CCI(ts_Mean(df['dcphase'],10),get_CMO(df['p2_et2'],34),Not(df['kama']),7),7)",11.2279,0.0054,0.9859,3.2993,0.5401725784907115,2024-01-01 20:59:00+00:00,2025-01-30 18:59:00+00:00,0.458,0.608,0.4296,-0.186
85,"-1*Power(get_CCI(df['p5_to7'],ts_Delta(df['p6_tn1'],40),ts_Decay2(df['p4_ms5'],39),29),29)","149_-1*Power(get_CCI(df['p5_to7'],ts_Delta(df['p6_tn1'],40),ts_Decay2(df['p4_ms5'],39),29),29)",16.1812,0.0057,1.4798,3.2886,0.5825891407562624,2024-01-01 20:59:00+00:00,2025-01-30 18:59:00+00:00,0.628,0.974,0.392,-0.189
86,"-1*ts_Delta(get_CCI(ts_Divide(df['kama'],7),LEthan(df['p6_tn9'],df['p2_et11']),ts_Delta(df['p6_tn13'],40),28),45)","151_-1*ts_Delta(get_CCI(ts_Divide(df['kama'],7),LEthan(df['p6_tn9'],df['p2_et11']),ts_Delta(df['p6_tn13'],40),28),45)",15.2442,0.0053,1.3534,4.3354,0.5340242664509783,2024-01-01 20:59:00+00:00,2025-01-30 18:59:00+00:00,0.58,0.885,0.3959,-0.088
87,"-1*Power(get_CCI(Min(df['p6_tn0'],7),ts_Delta(df['cmo'],7),pn_FillMax(df['liangle']),40),7)","152_-1*Power(get_CCI(Min(df['p6_tn0'],7),ts_Delta(df['cmo'],7),pn_FillMax(df['liangle']),40),7)",18.3371,0.0057,1.6827,3.6776,0.39592535868260564,2024-01-01 20:59:00+00:00,2025-01-30 18:59:00+00:00,0.631,1.19,0.3465,-0.297
88,"-1*ts_Delta(get_CCI(Power(df['p2_et3'],34),LEthan(df['p6_tn9'],df['p2_et11']),ts_Delta(df['p6_tn13'],7),7),45)","153_-1*ts_Delta(get_CCI(Power(df['p2_et3'],34),LEthan(df['p6_tn9'],df['p2_et11']),ts_Delta(df['p6_tn13'],7),7),45)",12.3403,0.0044,1.0946,3.523,0.5391666013175823,2024-01-01 20:59:00+00:00,2025-01-30 18:59:00+00:00,0.524,0.66,0.4426,-0.047
89,"-1*Power(get_CCI(ts_Skewness(df['p6_tn3'],7),ts_Rank(df['p1_corrs1'],16),ts_Delta(df['p6_tn13'],40),22),7)","154_-1*Power(get_CCI(ts_Skewness(df['p6_tn3'],7),ts_Rank(df['p1_corrs1'],16),ts_Delta(df['p6_tn13'],40),22),7)",18.4189,0.006,1.6972,3.4374,0.5986593092867226,2024-01-01 20:59:00+00:00,2025-01-30 18:59:00+00:00,0.566,1.271,0.3081,-0.111
90,"-1*ts_Delta(get_CCI(ts_Skewness(df['p6_tn8'],2),df['cci'],ts_Delta(df['dcphase'],7),22),29)","153_-1*ts_Delta(get_CCI(ts_Skewness(df['p6_tn8'],2),df['cci'],ts_Delta(df['dcphase'],7),22),29)",17.5915,0.0056,1.5972,4.0163,0.5525057912865968,2024-01-01 20:59:00+00:00,2025-01-30 18:59:00+00:00,0.729,1.0,0.4216,-0.196
91,"-1*Power(get_CCI(pn_FillMax(df['dcphase']),Min(df['p6_tn13'],28),ts_Delta(df['p6_tn13'],26),40),7)","153_-1*Power(get_CCI(pn_FillMax(df['dcphase']),Min(df['p6_tn13'],28),ts_Delta(df['p6_tn13'],26),40),7)",18.5563,0.0063,1.7108,3.4031,0.5679774177603647,2024-01-01 20:59:00+00:00,2025-01-30 18:59:00+00:00,0.562,1.29,0.3035,-0.136
92,"-1*ts_Delta(get_CCI(Sign(df['p1_corrs0']),Max(df['p4_ms0'],df['p2_et19']),ts_Mean(df['p6_tn13'],7),28),28)","154_-1*ts_Delta(get_CCI(Sign(df['p1_corrs0']),Max(df['p4_ms0'],df['p2_et19']),ts_Mean(df['p6_tn13'],7),28),28)",11.8784,0.0034,1.0607,3.3003,0.48359431119975116,2024-01-01 20:59:00+00:00,2025-01-30 18:59:00+00:00,0.281,0.867,0.2448,-0.202
93,"-1*ts_Delta(get_CCI(ts_Regression(df['p2_et5'],df['di'],22,'A'),LEthan(df['p6_tn9'],df['p1_corrs3']),ts_Delta(df['p6_tn13'],40),28),26)","155_-1*ts_Delta(get_CCI(ts_Regression(df['p2_et5'],df['di'],22,'A'),LEthan(df['p6_tn9'],df['p1_corrs3']),ts_Delta(df['p6_tn13'],40),28),26)",13.7608,0.0046,1.2254,3.8359,0.5563351419509366,2024-01-01 20:59:00+00:00,2025-01-30 18:59:00+00:00,0.513,0.813,0.3869,-0.088
94,"-1*ts_Delta(get_CCI(pn_RankCentered(df['p2_et14']),ts_Delta(df['p6_tn13'],40),ts_Delta(df['p6_tn13'],28),29),7)","155_-1*ts_Delta(get_CCI(pn_RankCentered(df['p2_et14']),ts_Delta(df['p6_tn13'],40),ts_Delta(df['p6_tn13'],28),29),7)",18.9053,0.0048,1.7493,3.5167,0.5310071996163547,2024-01-01 20:59:00+00:00,2025-01-30 18:59:00+00:00,0.496,1.397,0.262,-0.094
95,"-1*ts_Delta(get_CCI(ts_Delta(df['p6_tn13'],40),pn_Rank2(df['p1_corrs1']),ts_Delta(df['p6_tn13'],7),7),28)","155_-1*ts_Delta(get_CCI(ts_Delta(df['p6_tn13'],40),pn_Rank2(df['p1_corrs1']),ts_Delta(df['p6_tn13'],7),7),28)",23.6092,0.0038,2.2029,4.1692,0.5100572861028877,2024-01-01 20:59:00+00:00,2025-01-30 18:59:00+00:00,0.756,1.628,0.3171,-0.042
96,"-1*ts_Delta(get_CCI(ts_Median(df['p6_tn11'],48),ts_Divide(df['kama'],7),ts_Rank(df['p6_tn13'],31),22),28)","155_-1*ts_Delta(get_CCI(ts_Median(df['p6_tn11'],48),ts_Divide(df['kama'],7),ts_Rank(df['p6_tn13'],31),22),28)",11.255,0.004,0.9972,3.2463,0.5803025281249974,2024-01-01 20:59:00+00:00,2025-01-30 18:59:00+00:00,0.378,0.701,0.3503,-0.119
97,"-1*ts_Delta(get_CCI(IfThen(df['p1_corrs9'],42,33),get_LINEARREG_ANGLE(df['p5_to6'],27),ts_Delta(df['p4_ms5'],7),7),28)","156_-1*ts_Delta(get_CCI(IfThen(df['p1_corrs9'],42,33),get_LINEARREG_ANGLE(df['p5_to6'],27),ts_Delta(df['p4_ms5'],7),7),28)",10.3625,0.0042,0.9055,3.2903,0.4461811094589602,2024-01-01 20:59:00+00:00,2025-01-30 18:59:00+00:00,0.492,0.488,0.502,-0.118
98,"-1*ts_Delta(get_CCI(df['cci'],ts_Delta(df['p6_tn13'],20),ts_Mean(df['p3_mf0'],13),40),28)","157_-1*ts_Delta(get_CCI(df['cci'],ts_Delta(df['p6_tn13'],20),ts_Mean(df['p3_mf0'],13),40),28)",12.3085,0.0049,1.1031,3.1,0.5481866396499301,2024-01-01 20:59:00+00:00,2025-01-30 18:59:00+00:00,0.457,0.737,0.3827,-0.096
99,"ts_Delta(get_CCI(ts_Delta(df['di'],40),get_LINEARREG_ANGLE(df['p5_to6'],35),ts_Skewness(df['p5_to7'],7),7),45)","158_ts_Delta(get_CCI(ts_Delta(df['di'],40),get_LINEARREG_ANGLE(df['p5_to6'],35),ts_Skewness(df['p5_to7'],7),7),45)",14.3129,0.0049,1.2869,3.5992,0.4393107368996255,2024-01-01 20:59:00+00:00,2025-01-30 18:59:00+00:00,0.475,0.918,0.341,-0.168
100,"-1*Minus(Min(ts_Quantile(df['p6_tn6'],13,'A'),df['p1_corrs0']),pn_RankCentered(Divide(df['adosc'],df['p2_et0'])))","159_-1*Minus(Min(ts_Quantile(df['p6_tn6'],13,'A'),df['p1_corrs0']),pn_RankCentered(Divide(df['adosc'],df['p2_et0'])))",10.0245,0.0036,0.8694,3.4563,0.2840262993091597,2024-01-01 20:59:00+00:00,2025-01-30 18:59:00+00:00,0.723,0.217,0.7691,-0.096
101,"Minus(Min(ts_Rank(df['p2_et5'],45),df['di']),ts_Skewness(df['kama'],9))","160_Minus(Min(ts_Rank(df['p2_et5'],45),df['di']),ts_Skewness(df['kama'],9))",19.7248,0.0049,1.8216,3.7876,0.4910407359938512,2024-01-01 20:59:00+00:00,2025-01-30 18:59:00+00:00,0.641,1.33,0.3252,-0.205
102,"-1*pn_RankCentered(ts_Divide(df['p4_ms3'],10))","162_-1*pn_RankCentered(ts_Divide(df['p4_ms3'],10))",12.142,0.0051,1.0616,3.8098,0.42288901264002304,2024-01-01 20:59:00+00:00,2025-01-30 18:59:00+00:00,0.689,0.46,0.5997,-0.128
103,"-1*pn_RankCentered(Divide(ts_Delta(df['p1_corrs1'],27),ts_Rank(df['p1_corrs1'],32)))","163_-1*pn_RankCentered(Divide(ts_Delta(df['p1_corrs1'],27),ts_Rank(df['p1_corrs1'],32)))",13.5711,0.0047,1.2123,3.6471,0.5372747913587421,2024-01-01 20:59:00+00:00,2025-01-30 18:59:00+00:00,0.368,0.944,0.2805,-0.24
104,"Reverse(pn_GroupNeutral(Minus(df['p1_corrs3'],df['p2_et11']),df['p2_et8']))","164_Reverse(pn_GroupNeutral(Minus(df['p1_corrs3'],df['p2_et11']),df['p2_et8']))",10.7735,0.0051,0.9509,3.0224,0.4946519748854668,2024-01-01 20:59:00+00:00,2025-01-30 18:59:00+00:00,0.637,0.392,0.619,-0.347
105,"Reverse(ts_Rank(ts_Median(df['p3_mf11'],13),5))","165_Reverse(ts_Rank(ts_Median(df['p3_mf11'],13),5))",10.8166,0.0045,0.94,3.5767,0.40672925955015654,2024-01-01 20:59:00+00:00,2025-01-30 18:59:00+00:00,0.28,0.737,0.2753,-0.157
106,"Reverse(get_CCI(ts_Max(df['p2_et19'],47),df['p3_mf11'],df['p2_et9'],22))","171_Reverse(get_CCI(ts_Max(df['p2_et19'],47),df['p3_mf11'],df['p2_et9'],22))",11.4172,0.0043,0.9907,3.8807,0.5467686578103482,2024-01-01 20:59:00+00:00,2025-01-30 18:59:00+00:00,0.487,0.585,0.4543,-0.108
107,"-1*pn_RankCentered(get_LINEARREG_ANGLE(df['p3_mf12'],13))","171_-1*pn_RankCentered(get_LINEARREG_ANGLE(df['p3_mf12'],13))",10.6081,0.0032,0.9253,3.5876,0.2092295146448415,2024-01-01 20:59:00+00:00,2025-01-30 18:59:00+00:00,0.551,0.45,0.5504,-0.053
108,"-1*pn_RankCentered(get_LINEARREG_ANGLE(pn_TransNorm(df['p3_mf11']),12))","173_-1*pn_RankCentered(get_LINEARREG_ANGLE(pn_TransNorm(df['p3_mf11']),12))",11.3334,0.0059,1.0015,3.0753,0.46945572638284067,2024-01-01 20:59:00+00:00,2025-01-30 18:59:00+00:00,0.508,0.575,0.4691,-0.312
109,"Reverse(ts_Delta(df['p6_tn10'],12))","174_Reverse(ts_Delta(df['p6_tn10'],12))",27.2918,0.0101,2.5474,3.9428,0.5618191993768932,2024-01-01 20:59:00+00:00,2025-01-30 18:59:00+00:00,1.212,1.545,0.4396,-0.24
110,"-1*pn_RankCentered(get_CCI(ts_Rank(df['p4_ms1'],15),ts_Stdev(df['ultosc'],19),get_KAMA(df['p2_et14'],3),27))","176_-1*pn_RankCentered(get_CCI(ts_Rank(df['p4_ms1'],15),ts_Stdev(df['ultosc'],19),get_KAMA(df['p2_et14'],3),27))",12.001,0.0034,1.0728,3.3101,0.2516734705808916,2024-01-01 20:59:00+00:00,2025-01-30 18:59:00+00:00,0.305,0.856,0.2627,-0.052
111,"-1*pn_RankCentered(Divide(Power(df['p6_tn4'],39),pn_GroupNorm(df['p6_tn4'],df['p1_corrs7'])))","177_-1*pn_RankCentered(Divide(Power(df['p6_tn4'],39),pn_GroupNorm(df['p6_tn4'],df['p1_corrs7'])))",9.9438,0.0038,0.8608,3.4399,0.5949589834720063,2024-01-01 20:59:00+00:00,2025-01-30 18:59:00+00:00,0.33,0.601,0.3545,-0.124
112,"Reverse(Divide(Power(df['p6_tn4'],26),Max(df['p2_et12'],df['p3_mf5'])))","177_Reverse(Divide(Power(df['p6_tn4'],26),Max(df['p2_et12'],df['p3_mf5'])))",11.4478,0.005,0.9863,3.9979,0.5746804825100377,2024-01-01 20:59:00+00:00,2025-01-30 18:59:00+00:00,0.395,0.672,0.3702,-0.137
113,"Reverse(ts_Regression(df['p3_mf11'],df['kama'],27,'C'))","178_Reverse(ts_Regression(df['p3_mf11'],df['kama'],27,'C'))",11.2782,0.0041,1.0056,3.0467,0.4846740960785599,2024-01-01 20:59:00+00:00,2025-01-30 18:59:00+00:00,0.314,0.774,0.2886,-0.152
114,"-1*ts_Corr2(df['p6_tn3'],get_DX(df['p6_tn7'],LEthan(df['p2_et14'],df['p5_to2']),pn_Winsor(df['cmo'],42),12),17)","179_-1*ts_Corr2(df['p6_tn3'],get_DX(df['p6_tn7'],LEthan(df['p2_et14'],df['p5_to2']),pn_Winsor(df['cmo'],42),12),17)",10.9709,0.0039,0.9485,3.8665,0.3725418144324327,2024-01-01 20:59:00+00:00,2025-01-30 18:59:00+00:00,0.496,0.53,0.4834,-0.14
115,"-1*ts_TransNorm(get_LINEARREG_ANGLE(ts_Scale(df['p6_tn3'],11),30),32)","181_-1*ts_TransNorm(get_LINEARREG_ANGLE(ts_Scale(df['p6_tn3'],11),30),32)",11.5593,0.0045,1.0123,3.6289,0.31440086379302723,2024-01-01 20:59:00+00:00,2025-01-30 18:59:00+00:00,0.586,0.509,0.5352,-0.11
116,"-1*ts_ChgRate(Max(pn_Winsor(df['p1_corrs3'],20),df['p6_tn5']),42)","184_-1*ts_ChgRate(Max(pn_Winsor(df['p1_corrs3'],20),df['p6_tn5']),42)",10.2188,0.0037,0.8973,3.1776,0.5829797746613163,2024-01-01 20:59:00+00:00,2025-01-30 18:59:00+00:00,0.353,0.618,0.3635,-0.216
117,"-1*ts_TransNorm(Min(pn_TransStd(df['cmo']),get_LINEARREG_SLOPE(df['p1_corrs5'],14)),2)","187_-1*ts_TransNorm(Min(pn_TransStd(df['cmo']),get_LINEARREG_SLOPE(df['p1_corrs5'],14)),2)",16.9502,0.0055,1.5446,3.6948,0.5601129350795627,2024-01-01 20:59:00+00:00,2025-01-30 18:59:00+00:00,0.585,1.087,0.3499,-0.367
118,"pn_CrossFit(df['p1_corrs9'],Min(Mthan(df['p6_tn9'],df['cci']),df['lislope']))","188_pn_CrossFit(df['p1_corrs9'],Min(Mthan(df['p6_tn9'],df['cci']),df['lislope']))",13.0271,0.0035,1.1778,3.2294,0.3921892260179173,2024-01-01 20:59:00+00:00,2025-01-30 18:59:00+00:00,0.283,0.992,0.222,-0.135
119,"pn_GroupNeutral(Lthan(Minus(df['cmo'],df['p4_ms1']),Reverse(df['p5_to0'])),ts_Stdev(df['p2_et16'],20))","189_pn_GroupNeutral(Lthan(Minus(df['cmo'],df['p4_ms1']),Reverse(df['p5_to0'])),ts_Stdev(df['p2_et16'],20))",12.0784,0.0059,1.0686,3.2854,0.4050100592810818,2024-01-01 20:59:00+00:00,2025-01-30 18:59:00+00:00,0.501,0.656,0.433,-0.1
120,"-1*ts_TransNorm(Min(pn_GroupRank(df['p6_tn6'],df['p5_to3']),get_LINEARREG_SLOPE(df['cmo'],7)),2)","190_-1*ts_TransNorm(Min(pn_GroupRank(df['p6_tn6'],df['p5_to3']),get_LINEARREG_SLOPE(df['cmo'],7)),2)",15.6193,0.0045,1.4328,3.1964,0.5997610934772266,2024-01-01 20:59:00+00:00,2025-01-30 18:59:00+00:00,0.468,1.083,0.3017,-0.276
121,"-1*pn_GroupNeutral(SignedPower(ts_Scale(df['p1_corrs9'],46),df['p1_corrs6']),ts_TransNorm(pn_Rank2(df['p4_ms1']),39))","193_-1*pn_GroupNeutral(SignedPower(ts_Scale(df['p1_corrs9'],46),df['p1_corrs6']),ts_TransNorm(pn_Rank2(df['p4_ms1']),39))",9.7562,0.0044,0.8432,3.3168,0.5985724298480389,2024-01-01 20:59:00+00:00,2025-01-30 18:59:00+00:00,0.302,0.61,0.3311,-0.294
122,"pn_GroupNeutral(Minus(Minus(df['di'],df['p2_et15']),df['p5_to1']),ts_TransNorm(df['p4_ms1'],39))","194_pn_GroupNeutral(Minus(Minus(df['di'],df['p2_et15']),df['p5_to1']),ts_TransNorm(df['p4_ms1'],39))",9.5502,0.0039,0.8327,3.0893,0.5268069989721564,2024-01-01 20:59:00+00:00,2025-01-30 18:59:00+00:00,0.469,0.433,0.52,-0.231
123,"pn_GroupNeutral(Minus(LEthan(df['p1_corrs1'],df['dm']),ts_Cov(df['cmo'],df['p1_corrs5'],20)),pn_Winsor(pn_CrossFit(df['p4_ms6'],df['p2_et15']),28))","195_pn_GroupNeutral(Minus(LEthan(df['p1_corrs1'],df['dm']),ts_Cov(df['cmo'],df['p1_corrs5'],20)),pn_Winsor(pn_CrossFit(df['p4_ms6'],df['p2_et15']),28))",10.2021,0.0044,0.8919,3.1944,0.39488718509426735,2024-01-01 20:59:00+00:00,2025-01-30 18:59:00+00:00,0.522,0.443,0.5409,-0.156
124,"pn_GroupNeutral(Min(ts_TransNorm(df['dm'],4),ts_Stdev(df['cmo'],14)),ts_Stdev(pn_RankCentered(df['p3_mf1']),15))","196_pn_GroupNeutral(Min(ts_TransNorm(df['dm'],4),ts_Stdev(df['cmo'],14)),ts_Stdev(pn_RankCentered(df['p3_mf1']),15))",13.6936,0.0051,1.2208,3.6964,0.5330800150452457,2024-01-01 20:59:00+00:00,2025-01-30 18:59:00+00:00,0.466,0.855,0.3528,-0.085
125,"pn_GroupNeutral(Minus(df['di'],ts_Min(df['p1_corrs6'],10)),Power(ts_Decay(df['cmo'],16),39))","200_pn_GroupNeutral(Minus(df['di'],ts_Min(df['p1_corrs6'],10)),Power(ts_Decay(df['cmo'],16),39))",13.495,0.0068,1.1969,3.5554,0.5969023986153268,2024-01-01 20:59:00+00:00,2025-01-30 18:59:00+00:00,0.599,0.696,0.4625,-0.303
126,"pn_GroupNeutral(Min(ts_MeanChg(df['dcphase'],29),pn_RankCentered(df['p6_tn0'])),pn_Stand(ts_Skewness(df['p2_et16'],22)))","200_pn_GroupNeutral(Min(ts_MeanChg(df['dcphase'],29),pn_RankCentered(df['p6_tn0'])),pn_Stand(ts_Skewness(df['p2_et16'],22)))",9.5508,0.0037,0.8238,3.3802,0.43624582261795736,2024-01-01 20:59:00+00:00,2025-01-30 18:59:00+00:00,0.37,0.522,0.4148,-0.217
127,"pn_GroupNeutral(Minus(Minus(df['di'],df['di']),ts_Scale(df['ultosc'],15)),get_LINEARREG_ANGLE(df['cmo'],21))","198_pn_GroupNeutral(Minus(Minus(df['di'],df['di']),ts_Scale(df['ultosc'],15)),get_LINEARREG_ANGLE(df['cmo'],21))",13.6235,0.0054,1.199,4.0897,0.5859950166322562,2024-01-01 20:59:00+00:00,2025-01-30 18:59:00+00:00,0.754,0.544,0.5809,-0.22
128,"pn_GroupNeutral(Minus(Minus(df['di'],df['dx']),LEthan(df['p1_corrs1'],df['dcphase'])),IfThen(get_KAMA(df['di'],3),30,15))","199_pn_GroupNeutral(Minus(Minus(df['di'],df['dx']),LEthan(df['p1_corrs1'],df['dcphase'])),IfThen(get_KAMA(df['di'],3),30,15))",10.9442,0.004,0.9737,3.0223,0.3911318557007941,2024-01-01 20:59:00+00:00,2025-01-30 18:59:00+00:00,0.519,0.535,0.4924,-0.155
129,"pn_GroupNeutral(Minus(Minus(df['di'],df['p3_mf1']),ts_Divide(df['p3_mf10'],28)),get_CCI(ts_Corr(df['p1_corrs3'],df['p1_corrs1'],5),df['p1_corrs6'],ts_TransNorm(df['p1_corrs7'],29),4))","201_pn_GroupNeutral(Minus(Minus(df['di'],df['p3_mf1']),ts_Divide(df['p3_mf10'],28)),get_CCI(ts_Corr(df['p1_corrs3'],df['p1_corrs1'],5),df['p1_corrs6'],ts_TransNorm(df['p1_corrs7'],29),4))",13.4905,0.0054,1.1879,4.0197,0.5549931901673562,2024-01-01 20:59:00+00:00,2025-01-30 18:59:00+00:00,0.529,0.757,0.4114,-0.259
130,"-1*pn_GroupNeutral(get_CCI(ts_Max(df['p4_ms1'],2),ts_Delta(df['p6_tn9'],44),ts_TransNorm(df['p1_corrs1'],4),4),pn_RankCentered(df['p1_corrs6']))","202_-1*pn_GroupNeutral(get_CCI(ts_Max(df['p4_ms1'],2),ts_Delta(df['p6_tn9'],44),ts_TransNorm(df['p1_corrs1'],4),4),pn_RankCentered(df['p1_corrs6']))",10.4979,0.0043,0.8998,3.8502,0.35746769325317834,2024-01-01 20:59:00+00:00,2025-01-30 18:59:00+00:00,0.522,0.452,0.5359,-0.097
131,"-1*pn_GroupNeutral(Minus(df['p6_tn5'],ts_Stdev(df['p6_tn6'],20)),ts_TransNorm(pn_Winsor(df['p2_et15'],40),46))","204_-1*pn_GroupNeutral(Minus(df['p6_tn5'],ts_Stdev(df['p6_tn6'],20)),ts_TransNorm(pn_Winsor(df['p2_et15'],40),46))",10.4066,0.0042,0.917,3.0797,0.4488605223139034,2024-01-01 20:59:00+00:00,2025-01-30 18:59:00+00:00,0.414,0.579,0.4169,-0.143
132,"pn_GroupNeutral(ts_TransNorm(df['di'],4),get_CCI(get_KAMA(df['di'],15),MEthan(df['p2_et6'],df['dx']),ts_TransNorm(df['di'],42),36))","206_pn_GroupNeutral(ts_TransNorm(df['di'],4),get_CCI(get_KAMA(df['di'],15),MEthan(df['p2_et6'],df['dx']),ts_TransNorm(df['di'],42),36))",13.5745,0.0067,1.1856,4.1511,0.5199495021336552,2024-01-01 20:59:00+00:00,2025-01-30 18:59:00+00:00,0.364,0.919,0.2837,-0.273
133,"-1*get_LINEARREG_SLOPE(Log(ts_Delta(df['ultosc'],1)),17)","208_-1*get_LINEARREG_SLOPE(Log(ts_Delta(df['ultosc'],1)),17)",9.6828,0.0029,0.8513,3.077,0.37524353140682276,2024-01-01 20:59:00+00:00,2025-01-30 18:59:00+00:00,0.205,0.716,0.2226,-0.07
134,"-1*ts_Median(pn_GroupNeutral(df['p2_et0'],df['p6_tn0']),2)","210_-1*ts_Median(pn_GroupNeutral(df['p2_et0'],df['p6_tn0']),2)",11.3314,0.0041,1.0005,3.365,0.531647679177884,2024-01-01 20:59:00+00:00,2025-01-30 18:59:00+00:00,0.553,0.529,0.5111,-0.145
135,"-1*pn_Winsor(get_LINEARREG_ANGLE(df['dcphase'],22),17)","211_-1*pn_Winsor(get_LINEARREG_ANGLE(df['dcphase'],22),17)",12.2071,0.0037,1.0779,3.7217,0.5492337089090428,2024-01-01 20:59:00+00:00,2025-01-30 18:59:00+00:00,0.514,0.653,0.4404,-0.202
136,"Minus(Multiply(ts_Decay(df['p1_corrs7'],13),0.767),Min(pn_Rank(df['p2_et0']),pn_Cut(df['dcphase'])))","212_Minus(Multiply(ts_Decay(df['p1_corrs7'],13),0.767),Min(pn_Rank(df['p2_et0']),pn_Cut(df['dcphase'])))",20.0578,0.0066,1.8695,3.1018,0.4361866455660929,2024-01-01 20:59:00+00:00,2025-01-30 18:59:00+00:00,0.58,1.444,0.2866,-0.153
137,"-1*Minus(ts_Mean(df['p3_mf3'],3),pn_GroupNeutral(df['p6_tn0'],ts_Median(df['p2_et7'],44)))","214_-1*Minus(ts_Mean(df['p3_mf3'],3),pn_GroupNeutral(df['p6_tn0'],ts_Median(df['p2_et7'],44)))",13.7954,0.0042,1.2148,4.3133,0.5421423936129767,2024-01-01 20:59:00+00:00,2025-01-30 18:59:00+00:00,0.884,0.431,0.6722,-0.27
138,"Minus(LEthan(pn_GroupNorm(df['lislope'],df['p6_tn11']),pn_TransStd(df['p5_to6'])),ts_Scale(df['p4_ms5'],32))","215_Minus(LEthan(pn_GroupNorm(df['lislope'],df['p6_tn11']),pn_TransStd(df['p5_to6'])),ts_Scale(df['p4_ms5'],32))",12.8486,0.005,1.1198,4.1971,0.40265461757007687,2024-01-01 20:59:00+00:00,2025-01-30 18:59:00+00:00,0.548,0.664,0.4521,-0.245
139,"-1*ts_Median(pn_GroupNeutral(pn_TransStd(df['p4_ms1']),ts_Divide(df['p4_ms1'],44)),2)","216_-1*ts_Median(pn_GroupNeutral(pn_TransStd(df['p4_ms1']),ts_Divide(df['p4_ms1'],44)),2)",11.9465,0.0051,1.0645,3.1454,0.415417085482199,2024-01-01 20:59:00+00:00,2025-01-30 18:59:00+00:00,0.568,0.584,0.4931,-0.228
140,"-1*Minus(ts_Scale(pn_TransStd(df['p4_ms1']),32),ts_Entropy(df['p3_mf6'],46))","217_-1*Minus(ts_Scale(pn_TransStd(df['p4_ms1']),32),ts_Entropy(df['p3_mf6'],46))",15.9978,0.0037,1.4648,3.4888,0.5237043070400902,2024-01-01 20:59:00+00:00,2025-01-30 18:59:00+00:00,0.462,1.123,0.2915,-0.07
141,"Minus(LEthan(pn_GroupNorm(df['p2_et15'],df['p6_tn11']),Softsign(df['p3_mf6'])),ts_Scale(ts_Sum(df['p4_ms3'],5),32))","218_Minus(LEthan(pn_GroupNorm(df['p2_et15'],df['p6_tn11']),Softsign(df['p3_mf6'])),ts_Scale(ts_Sum(df['p4_ms3'],5),32))",11.3067,0.0048,0.989,3.524,0.5032033153986075,2024-01-01 20:59:00+00:00,2025-01-30 18:59:00+00:00,0.527,0.543,0.4925,-0.332
142,"Minus(LEthan(ts_Cov2(df['p2_et18'],df['p6_tn10'],22),df['p2_et15']),Multiply(ts_Scale(df['p4_ms5'],16),ts_Divide(df['p3_mf6'],44)))","220_Minus(LEthan(ts_Cov2(df['p2_et18'],df['p6_tn10'],22),df['p2_et15']),Multiply(ts_Scale(df['p4_ms5'],16),ts_Divide(df['p3_mf6'],44)))",9.6849,0.003,0.853,3.0201,0.4625655914860563,2024-01-01 20:59:00+00:00,2025-01-30 18:59:00+00:00,0.344,0.579,0.3727,-0.091
143,"Minus(get_CMO(Max(df['p2_et15'],0.305),17),Multiply(df['p5_to2'],ts_Scale(df['p4_ms5'],16)))","221_Minus(get_CMO(Max(df['p2_et15'],0.305),17),Multiply(df['p5_to2'],ts_Scale(df['p4_ms5'],16)))",9.7705,0.0033,0.8592,3.0414,0.487729173263075,2024-01-01 20:59:00+00:00,2025-01-30 18:59:00+00:00,0.257,0.673,0.2763,-0.071
144,"-1*Minus(ts_Divide(ts_Stdev2(df['p4_ms2'],44),44),pn_Cut(df['p2_et11']))","222_-1*Minus(ts_Divide(ts_Stdev2(df['p4_ms2'],44),44),pn_Cut(df['p2_et11']))",9.4212,0.0039,0.8046,3.5482,0.40306724947294986,2024-01-01 20:59:00+00:00,2025-01-30 18:59:00+00:00,0.367,0.504,0.4214,-0.145
145,"-1*Minus(ts_Divide(inv(df['p4_ms2']),29),get_MINUS_DI(ts_StdevChg(df['p5_to6'],11),ts_ChgRate(df['cmo'],47),pn_FillMin(df['p3_mf6']),44))","223_-1*Minus(ts_Divide(inv(df['p4_ms2']),29),get_MINUS_DI(ts_StdevChg(df['p5_to6'],11),ts_ChgRate(df['cmo'],47),pn_FillMin(df['p3_mf6']),44))",10.5413,0.0048,0.9204,3.2976,0.5398669327738713,2024-01-01 20:59:00+00:00,2025-01-30 18:59:00+00:00,0.401,0.595,0.4026,-0.14
146,"-1*Minus(ts_Divide(SignedPower(df['p4_ms6'],df['p6_tn4']),44),ts_StdevChg(get_DX(df['p1_corrs0'],df['p2_et17'],df['p6_tn10'],16),3))","225_-1*Minus(ts_Divide(SignedPower(df['p4_ms6'],df['p6_tn4']),44),ts_StdevChg(get_DX(df['p1_corrs0'],df['p2_et17'],df['p6_tn10'],16),3))",13.4516,0.0051,1.1963,3.693,0.40075249723995177,2024-01-01 20:59:00+00:00,2025-01-30 18:59:00+00:00,0.551,0.743,0.4258,-0.12
147,"-1*Minus(ts_Divide(pn_RankCentered(df['p6_tn0']),44),get_MINUS_DI(Min(df['p4_ms5'],df['p2_et16']),ts_Divide(df['ultosc'],29),pn_CrossFit(df['p5_to6'],df['dm']),44))","226_-1*Minus(ts_Divide(pn_RankCentered(df['p6_tn0']),44),get_MINUS_DI(Min(df['p4_ms5'],df['p2_et16']),ts_Divide(df['ultosc'],29),pn_CrossFit(df['p5_to6'],df['dm']),44))",12.1678,0.0029,1.1004,3.052,0.16517326254443582,2024-01-01 20:59:00+00:00,2025-01-30 18:59:00+00:00,0.382,0.809,0.3207,0.001
148,"-1*Minus(ts_Divide(get_DX(df['p2_et10'],df['cmo'],df['p2_et7'],4),44),get_MINUS_DI(Min(df['p4_ms5'],df['p2_et16']),ts_Divide(df['ultosc'],29),ts_Skewness(df['p5_to5'],34),44))","228_-1*Minus(ts_Divide(get_DX(df['p2_et10'],df['cmo'],df['p2_et7'],4),44),get_MINUS_DI(Min(df['p4_ms5'],df['p2_et16']),ts_Divide(df['ultosc'],29),ts_Skewness(df['p5_to5'],34),44))",15.7565,0.0069,1.397,4.3187,0.3935306344202577,2024-01-01 20:59:00+00:00,2025-01-30 18:59:00+00:00,0.82,0.692,0.5423,-0.181
149,"-1*Minus(ts_Divide(inv(df['p4_ms2']),1),get_MINUS_DI(ts_Delta(df['dm'],44),ts_Cov(df['p1_corrs3'],df['p1_corrs0'],7),pn_CrossFit(df['p5_to6'],df['dm']),44))","229_-1*Minus(ts_Divide(inv(df['p4_ms2']),1),get_MINUS_DI(ts_Delta(df['dm'],44),ts_Cov(df['p1_corrs3'],df['p1_corrs0'],7),pn_CrossFit(df['p5_to6'],df['dm']),44))",11.1895,0.0044,0.9815,3.4625,0.3744199409432747,2024-01-01 20:59:00+00:00,2025-01-30 18:59:00+00:00,0.517,0.545,0.4868,-0.068
150,"-1*Minus(df['p1_corrs0'],get_MINUS_DI(Max(df['p1_corrs0'],44),ts_Delta(df['lislope'],36),pn_CrossFit(df['p5_to6'],df['dm']),44))","231_-1*Minus(df['p1_corrs0'],get_MINUS_DI(Max(df['p1_corrs0'],44),ts_Delta(df['lislope'],36),pn_CrossFit(df['p5_to6'],df['dm']),44))",12.5589,0.0054,1.1259,3.0852,0.47059475813860613,2024-01-01 20:59:00+00:00,2025-01-30 18:59:00+00:00,0.44,0.778,0.3612,-0.131
151,"-1*Minus(ts_Delta(Log(df['p3_mf6']),8),get_MINUS_DI(ts_Delta(df['dm'],44),df['p3_mf3'],pn_CrossFit(df['p5_to6'],df['dm']),44))","232_-1*Minus(ts_Delta(Log(df['p3_mf6']),8),get_MINUS_DI(ts_Delta(df['dm'],44),df['p3_mf3'],pn_CrossFit(df['p5_to6'],df['dm']),44))",9.7741,0.0023,0.8413,3.7398,0.4548816550957946,2024-01-01 20:59:00+00:00,2025-01-30 18:59:00+00:00,0.481,0.429,0.5286,-0.03
152,"pn_TransStd(ts_CovChg(df['kama'],ts_Max(df['p3_mf6'],33),10))","234_pn_TransStd(ts_CovChg(df['kama'],ts_Max(df['p3_mf6'],33),10))",11.8436,0.0033,1.0482,3.586,0.21008123638895113,2024-01-01 20:59:00+00:00,2025-01-30 18:59:00+00:00,0.335,0.799,0.2954,-0.022
153,"-1*pn_TransStd(get_CCI(Softsign(df['p2_et14']),df['p6_tn1'],df['p3_mf8'],32))","234_-1*pn_TransStd(get_CCI(Softsign(df['p2_et14']),df['p6_tn1'],df['p3_mf8'],32))",14.6411,0.0048,1.2999,4.2108,0.5061689583093877,2024-01-01 20:59:00+00:00,2025-01-30 18:59:00+00:00,0.513,0.893,0.3649,-0.073
154,"-1*pn_TransStd(ts_Skewness(df['cci'],5))","236_-1*pn_TransStd(ts_Skewness(df['cci'],5))",10.9565,0.0043,0.9654,3.2649,0.2291877822207004,2024-01-01 20:59:00+00:00,2025-01-30 18:59:00+00:00,0.381,0.664,0.3646,-0.098
155,"-1*get_CCI(pn_FillMin(df['kama']),Sign(ts_Delay(df['p3_mf5'],17)),df['p2_et1'],23)","237_-1*get_CCI(pn_FillMin(df['kama']),Sign(ts_Delay(df['p3_mf5'],17)),df['p2_et1'],23)",11.6529,0.0038,1.0047,4.2428,0.2934564402869548,2024-01-01 20:59:00+00:00,2025-01-30 18:59:00+00:00,0.375,0.712,0.345,-0.031
156,"-1*get_CCI(get_CMO(df['p6_tn1'],16),get_LINEARREG_ANGLE(ts_CovChg(df['p5_to3'],df['p2_et19'],25),11),IfThen(df['p3_mf7'],3,14),23)","238_-1*get_CCI(get_CMO(df['p6_tn1'],16),get_LINEARREG_ANGLE(ts_CovChg(df['p5_to3'],df['p2_et19'],25),11),IfThen(df['p3_mf7'],3,14),23)",10.5717,0.0037,0.9154,3.6966,0.4041996885480888,2024-01-01 20:59:00+00:00,2025-01-30 18:59:00+00:00,0.255,0.736,0.2573,-0.067
157,"-1*get_CCI(ts_Decay2(pn_RankCentered(df['p3_mf10']),8),FilterInf(get_CCI(df['p5_to0'],df['p3_mf8'],df['p1_corrs7'],32)),df['p5_to7'],23)","241_-1*get_CCI(ts_Decay2(pn_RankCentered(df['p3_mf10']),8),FilterInf(get_CCI(df['p5_to0'],df['p3_mf8'],df['p1_corrs7'],32)),df['p5_to7'],23)",27.4125,0.0077,2.5392,4.9041,0.42774138093639025,2024-01-01 20:59:00+00:00,2025-01-30 18:59:00+00:00,1.046,1.702,0.3806,-0.125
158,"-1*get_CCI(Minus(get_DX(df['p2_et3'],df['p3_mf8'],df['p3_mf0'],3),0.722),df['p6_tn11'],get_CCI(df['p5_to0'],ts_Max(df['p4_ms1'],5),Min(df['p6_tn12'],df['p6_tn12']),32),23)","243_-1*get_CCI(Minus(get_DX(df['p2_et3'],df['p3_mf8'],df['p3_mf0'],3),0.722),df['p6_tn11'],get_CCI(df['p5_to0'],ts_Max(df['p4_ms1'],5),Min(df['p6_tn12'],df['p6_tn12']),32),23)",14.4511,0.0049,1.3105,3.3077,0.5679136582032951,2024-01-01 20:59:00+00:00,2025-01-30 18:59:00+00:00,0.289,1.129,0.2038,-0.138
159,"-1*pn_TransStd(get_CCI(pn_TransNorm(df['kama']),Or(df['cci'],df['p4_ms1']),df['p6_tn1'],32))","244_-1*pn_TransStd(get_CCI(pn_TransNorm(df['kama']),Or(df['cci'],df['p4_ms1']),df['p6_tn1'],32))",15.03,0.0043,1.3788,3.076,0.5004359739416873,2024-01-01 20:59:00+00:00,2025-01-30 18:59:00+00:00,0.861,0.632,0.5767,-0.271
160,"-1*get_CCI(df['p6_tn1'],get_CCI(df['p5_to4'],df['cci'],Lthan(df['p5_to7'],df['p3_mf3']),32),get_CCI(df['p3_mf8'],Equal(df['p6_tn1'],df['p5_to3']),ts_Max(df['dcperiod'],24),5),23)","245_-1*get_CCI(df['p6_tn1'],get_CCI(df['p5_to4'],df['cci'],Lthan(df['p5_to7'],df['p3_mf3']),32),get_CCI(df['p3_mf8'],Equal(df['p6_tn1'],df['p5_to3']),ts_Max(df['dcperiod'],24),5),23)",12.7943,0.0042,1.1445,3.4222,0.5251281711595558,2024-01-01 20:59:00+00:00,2025-01-30 18:59:00+00:00,0.439,0.8,0.3543,-0.233
161,"-1*get_CCI(df['p5_to4'],pn_GroupNeutral(df['p2_et1'],df['p6_tn1']),Abs(Sqrt(df['p5_to0'])),23)","246_-1*get_CCI(df['p5_to4'],pn_GroupNeutral(df['p2_et1'],df['p6_tn1']),Abs(Sqrt(df['p5_to0'])),23)",18.1823,0.0059,1.6845,3.131,0.5214260693654504,2024-01-01 20:59:00+00:00,2025-01-30 18:59:00+00:00,0.544,1.279,0.2984,-0.129
162,"-1*get_CCI(ts_Divide(df['p5_to0'],11),get_CCI(df['p6_tn1'],df['cci'],pn_GroupNeutral(df['p2_et1'],df['liangle']),32),get_CCI(df['p3_mf8'],inv(df['p3_mf1']),Divide(df['p5_to7'],23),32),23)","247_-1*get_CCI(ts_Divide(df['p5_to0'],11),get_CCI(df['p6_tn1'],df['cci'],pn_GroupNeutral(df['p2_et1'],df['liangle']),32),get_CCI(df['p3_mf8'],inv(df['p3_mf1']),Divide(df['p5_to7'],23),32),23)",13.6967,0.0053,1.2403,3.0899,0.4712196569395745,2024-01-01 20:59:00+00:00,2025-01-30 18:59:00+00:00,0.536,0.806,0.3994,-0.188
163,"get_CCI(Reverse(df['p6_tn1']),ts_Entropy(pn_TransStd(df['p3_mf1']),49),FilterInf(pn_Winsor(df['p5_to6'],39)),8)","248_get_CCI(Reverse(df['p6_tn1']),ts_Entropy(pn_TransStd(df['p3_mf1']),49),FilterInf(pn_Winsor(df['p5_to6'],39)),8)",14.6358,0.0035,1.3313,3.4424,0.41884269157054865,2024-01-01 20:59:00+00:00,2025-01-30 18:59:00+00:00,0.333,1.108,0.2311,-0.073
164,"-1*get_CCI(df['p5_to0'],pn_TransStd(df['p3_mf9']),IfThen(get_LINEARREG_SLOPE(df['p5_to0'],13),3,11),23)","249_-1*get_CCI(df['p5_to0'],pn_TransStd(df['p3_mf9']),IfThen(get_LINEARREG_SLOPE(df['p5_to0'],13),3,11),23)",18.9435,0.0084,1.7465,3.1775,0.4956780839128961,2024-01-01 20:59:00+00:00,2025-01-30 18:59:00+00:00,0.487,1.403,0.2577,-0.155
165,"-1*get_CCI(Not(df['liangle']),get_CCI(get_CCI(df['p5_to0'],df['p5_to3'],df['p6_tn1'],32),df['p3_mf8'],ts_Median(df['p2_et10'],17),23),get_CCI(df['p3_mf8'],ts_Max(df['cci'],5),ts_Rank(df['p5_to7'],32),32),23)","249_-1*get_CCI(Not(df['liangle']),get_CCI(get_CCI(df['p5_to0'],df['p5_to3'],df['p6_tn1'],32),df['p3_mf8'],ts_Median(df['p2_et10'],17),23),get_CCI(df['p3_mf8'],ts_Max(df['cci'],5),ts_Rank(df['p5_to7'],32),32),23)",16.431,0.005,1.4962,3.6619,0.49286735067228593,2024-01-01 20:59:00+00:00,2025-01-30 18:59:00+00:00,0.409,1.21,0.2526,-0.257
166,"-1*get_CCI(Min(Mthan(df['p1_corrs4'],df['p3_mf9']),get_HT_DCPERIOD(df['p2_et11'])),get_CMO(df['p2_et3'],45),get_CCI(IfThen(df['p3_mf7'],3,3),pn_CrossFit(df['di'],df['ultosc']),ts_Scale(df['p2_et0'],38),37),45)","254_-1*get_CCI(Min(Mthan(df['p1_corrs4'],df['p3_mf9']),get_HT_DCPERIOD(df['p2_et11'])),get_CMO(df['p2_et3'],45),get_CCI(IfThen(df['p3_mf7'],3,3),pn_CrossFit(df['di'],df['ultosc']),ts_Scale(df['p2_et0'],38),37),45)",16.505,0.006,1.5098,3.3262,0.5825637700581487,2024-01-01 20:59:00+00:00,2025-01-30 18:59:00+00:00,1.107,0.526,0.6779,-0.358
167,"-1*get_CCI(Min(df['cci'],ts_Corr2(df['p3_mf9'],df['p5_to3'],47)),Power(Abs(df['p3_mf8']),25),get_CCI(IfThen(df['p3_mf7'],3,3),pn_CrossFit(df['di'],df['p5_to2']),pn_Winsor(df['p5_to0'],25),37),45)","254_-1*get_CCI(Min(df['cci'],ts_Corr2(df['p3_mf9'],df['p5_to3'],47)),Power(Abs(df['p3_mf8']),25),get_CCI(IfThen(df['p3_mf7'],3,3),pn_CrossFit(df['di'],df['p5_to2']),pn_Winsor(df['p5_to0'],25),37),45)",24.1495,0.0081,2.2536,3.6293,0.5595031794874374,2024-01-01 20:59:00+00:00,2025-01-30 18:59:00+00:00,1.163,1.276,0.4768,-0.25
168,"ts_Divide(df['p2_et7'],27)","256_ts_Divide(df['p2_et7'],27)",9.5242,0.0029,0.8162,3.6471,0.10788786780547562,2024-01-01 20:59:00+00:00,2025-01-30 18:59:00+00:00,0.24,0.643,0.2718,-0.041
169,"-1*pn_GroupNeutral(df['p6_tn13'],Divide(df['p3_mf3'],df['cci']))","258_-1*pn_GroupNeutral(df['p6_tn13'],Divide(df['p3_mf3'],df['cci']))",12.8288,0.0057,1.1243,3.9054,0.5294237735152905,2024-01-01 20:59:00+00:00,2025-01-30 18:59:00+00:00,0.736,0.481,0.6048,-0.049
170,"-1*pn_CrossFit(ts_Corr(FilterInf(df['p4_ms0']),pn_Winsor(df['dx'],13),32),Mthan(pn_CrossFit(df['p3_mf4'],df['p2_et9']),df['p6_tn3']))","259_-1*pn_CrossFit(ts_Corr(FilterInf(df['p4_ms0']),pn_Winsor(df['dx'],13),32),Mthan(pn_CrossFit(df['p3_mf4'],df['p2_et9']),df['p6_tn3']))",10.7475,0.0048,0.9335,3.5249,0.4834179452478404,2024-01-01 20:59:00+00:00,2025-01-30 18:59:00+00:00,0.442,0.568,0.4376,-0.135
171,"pn_CrossFit(ts_Corr(FilterInf(df['p4_ms0']),pn_Winsor(df['dx'],13),32),Mthan(ts_Regression(df['p6_tn8'],df['p1_corrs6'],25,'A'),df['dm']))","260_pn_CrossFit(ts_Corr(FilterInf(df['p4_ms0']),pn_Winsor(df['dx'],13),32),Mthan(ts_Regression(df['p6_tn8'],df['p1_corrs6'],25,'A'),df['dm']))",9.8753,0.0036,0.8585,3.3368,0.07907483857132176,2024-01-01 20:59:00+00:00,2025-01-30 18:59:00+00:00,0.377,0.552,0.4058,-0.002
172,"pn_CrossFit(ts_Skewness(df['p6_tn9'],39),df['p5_to5'])","260_pn_CrossFit(ts_Skewness(df['p6_tn9'],39),df['p5_to5'])",10.1898,0.0027,0.8986,3.2111,0.3930259525275902,2024-01-01 20:59:00+00:00,2025-01-30 18:59:00+00:00,0.383,0.59,0.3936,-0.03
173,"pn_GroupRank(Mthan(ts_Decay(df['p6_tn11'],3),pn_Rank(df['p6_tn4'])),ts_Skewness(df['p2_et13'],38))","262_pn_GroupRank(Mthan(ts_Decay(df['p6_tn11'],3),pn_Rank(df['p6_tn4'])),ts_Skewness(df['p2_et13'],38))",9.7758,0.0027,0.8545,3.2829,0.35422861686577894,2024-01-01 20:59:00+00:00,2025-01-30 18:59:00+00:00,0.274,0.65,0.2965,-0.105
174,"-1*pn_GroupRank(Max(Divide(df['cci'],40),get_LINEARREG_ANGLE(df['p2_et18'],16)),pn_Stand(df['p4_ms2']))","262_-1*pn_GroupRank(Max(Divide(df['cci'],40),get_LINEARREG_ANGLE(df['p2_et18'],16)),pn_Stand(df['p4_ms2']))",12.2767,0.0056,1.0832,3.4971,0.5790755267059652,2024-01-01 20:59:00+00:00,2025-01-30 18:59:00+00:00,0.58,0.592,0.4949,-0.364
175,"pn_GroupRank(get_CMO(df['p2_et5'],31),Max(FilterInf(df['p2_et9']),ts_MeanChg(df['p2_et10'],4)))","264_pn_GroupRank(get_CMO(df['p2_et5'],31),Max(FilterInf(df['p2_et9']),ts_MeanChg(df['p2_et10'],4)))",11.3508,0.0033,1.0154,3.0962,0.425842645995397,2024-01-01 20:59:00+00:00,2025-01-30 18:59:00+00:00,0.412,0.687,0.3749,0.116
176,"-1*pn_GroupRank(get_CMO(ts_Sum(df['p2_et0'],12),3),MEthan(ts_Decay2(df['p3_mf12'],34),inv(df['p2_et7'])))","265_-1*pn_GroupRank(get_CMO(ts_Sum(df['p2_et0'],12),3),MEthan(ts_Decay2(df['p3_mf12'],34),inv(df['p2_et7'])))",12.2488,0.0052,1.0969,3.0646,0.5915437904343748,2024-01-01 20:59:00+00:00,2025-01-30 18:59:00+00:00,0.362,0.825,0.305,-0.216
177,"-1*pn_GroupRank(get_CMO(df['p2_et7'],3),Max(ts_Mean(df['p6_tn11'],43),ts_MeanChg(df['p3_mf9'],4)))","263_-1*pn_GroupRank(get_CMO(df['p2_et7'],3),Max(ts_Mean(df['p6_tn11'],43),ts_MeanChg(df['p3_mf9'],4)))",17.8997,0.0074,1.5918,4.8354,0.5849646618542348,2024-01-01 20:59:00+00:00,2025-01-30 18:59:00+00:00,0.865,0.858,0.502,-0.492
178,"-1*pn_GroupRank(ts_Scale(df['p6_tn4'],16),ts_Decay(ts_Stdev(df['p2_et12'],46),4))","264_-1*pn_GroupRank(ts_Scale(df['p6_tn4'],16),ts_Decay(ts_Stdev(df['p2_et12'],46),4))",11.1107,0.0049,0.9603,3.7877,0.537820485588618,2024-01-01 20:59:00+00:00,2025-01-30 18:59:00+00:00,0.428,0.611,0.4119,-0.122
179,"-1*pn_GroupRank(Max(df['p6_tn5'],df['p3_mf4']),Multiply(Reverse(df['p2_et14']),ts_Scale(df['dm'],39)))","265_-1*pn_GroupRank(Max(df['p6_tn5'],df['p3_mf4']),Multiply(Reverse(df['p2_et14']),ts_Scale(df['dm'],39)))",9.2429,0.0018,0.8036,3.3452,0.527852505354073,2024-01-01 20:59:00+00:00,2025-01-30 18:59:00+00:00,0.606,0.263,0.6974,-0.065
180,"-1*pn_GroupRank(get_CMO(Lthan(df['p3_mf7'],df['p1_corrs9']),3),Max(df['p3_mf8'],pn_TransStd(df['p1_corrs6'])))","267_-1*pn_GroupRank(get_CMO(Lthan(df['p3_mf7'],df['p1_corrs9']),3),Max(df['p3_mf8'],pn_TransStd(df['p1_corrs6'])))",9.4135,0.0041,0.8078,3.389,0.4541647865512454,2024-01-01 20:59:00+00:00,2025-01-30 18:59:00+00:00,0.442,0.432,0.5057,-0.21
181,"-1*pn_GroupRank(get_CMO(ts_Decay(df['p6_tn13'],30),3),Max(pn_Stand(df['p3_mf12']),get_LINEARREG_ANGLE(df['p2_et18'],16)))","269_-1*pn_GroupRank(get_CMO(ts_Decay(df['p6_tn13'],30),3),Max(pn_Stand(df['p3_mf12']),get_LINEARREG_ANGLE(df['p2_et18'],16)))",13.9186,0.0062,1.2024,4.756,0.4956723009813835,2024-01-01 20:59:00+00:00,2025-01-30 18:59:00+00:00,0.669,0.632,0.5142,-0.26
182,"-1*pn_GroupRank(get_CMO(Log(df['cci']),5),inv(df['dcphase']))","269_-1*pn_GroupRank(get_CMO(Log(df['cci']),5),inv(df['dcphase']))",10.922,0.0048,0.9516,3.5034,0.4969130763175969,2024-01-01 20:59:00+00:00,2025-01-30 18:59:00+00:00,0.31,0.72,0.301,-0.189
183,"Abs(pn_CrossFit(df['p3_mf8'],Add(df['p3_mf10'],df['p3_mf9'])))","268_Abs(pn_CrossFit(df['p3_mf8'],Add(df['p3_mf10'],df['p3_mf9'])))",18.8352,0.0035,1.7597,3.1954,0.32248850199350376,2024-01-01 20:59:00+00:00,2025-01-30 18:59:00+00:00,0.675,1.23,0.3543,0.047
184,"-1*Min(pn_CrossFit(df['p2_et17'],df['p3_mf11']),ts_Skewness(FilterInf(df['p4_ms3']),48))","270_-1*Min(pn_CrossFit(df['p2_et17'],df['p3_mf11']),ts_Skewness(FilterInf(df['p4_ms3']),48))",15.3647,0.0045,1.4096,3.1251,0.3196298816347256,2024-01-01 20:59:00+00:00,2025-01-30 18:59:00+00:00,0.411,1.115,0.2693,0.015
185,"-1*Min(df['p2_et7'],ts_StdevChg(df['p4_ms3'],1))","272_-1*Min(df['p2_et7'],ts_StdevChg(df['p4_ms3'],1))",17.1786,0.0043,1.5546,4.2506,0.36798451501326906,2024-01-01 20:59:00+00:00,2025-01-30 18:59:00+00:00,0.411,1.271,0.2444,-0.304
186,"-1*get_CCI(df['p3_mf7'],Max(df['cmo'],Or(df['p6_tn0'],df['p6_tn0'])),get_CCI(LEthan(df['p6_tn5'],df['p2_et0']),df['p5_to6'],pn_TransNorm(df['p2_et7']),9),47)","272_-1*get_CCI(df['p3_mf7'],Max(df['cmo'],Or(df['p6_tn0'],df['p6_tn0'])),get_CCI(LEthan(df['p6_tn5'],df['p2_et0']),df['p5_to6'],pn_TransNorm(df['p2_et7']),9),47)",18.8986,0.007,1.7086,4.392,0.4761181460428262,2024-01-01 20:59:00+00:00,2025-01-30 18:59:00+00:00,0.737,1.112,0.3986,-0.347
187,"-1*get_CCI(ts_Entropy(ts_Delay(df['p1_corrs4'],36),20),get_CCI(Sqrt(df['p5_to7']),ts_ChgRate(df['p6_tn8'],17),ts_ChgRate(df['p6_tn0'],1),30),ts_Regression(df['p6_tn7'],pn_TransNorm(df['p1_corrs3']),45,'D'),47)","276_-1*get_CCI(ts_Entropy(ts_Delay(df['p1_corrs4'],36),20),get_CCI(Sqrt(df['p5_to7']),ts_ChgRate(df['p6_tn8'],17),ts_ChgRate(df['p6_tn0'],1),30),ts_Regression(df['p6_tn7'],pn_TransNorm(df['p1_corrs3']),45,'D'),47)",15.5819,0.0059,1.4147,3.4142,0.365204472753992,2024-01-01 20:59:00+00:00,2025-01-30 18:59:00+00:00,0.674,0.857,0.4402,-0.038
188,"-1*get_CCI(Log(pn_TransNorm(df['p2_et7'])),Max(ts_ChgRate(df['p6_tn8'],17),Or(df['p5_to7'],df['p6_tn0'])),get_CCI(LEthan(df['p6_tn5'],df['p2_et0']),Exp(df['p2_et7']),df['p5_to6'],30),47)","278_-1*get_CCI(Log(pn_TransNorm(df['p2_et7'])),Max(ts_ChgRate(df['p6_tn8'],17),Or(df['p5_to7'],df['p6_tn0'])),get_CCI(LEthan(df['p6_tn5'],df['p2_et0']),Exp(df['p2_et7']),df['p5_to6'],30),47)",18.2514,0.0074,1.6657,3.6694,0.523141933376087,2024-01-01 20:59:00+00:00,2025-01-30 18:59:00+00:00,0.693,1.11,0.3844,-0.532
189,"-1*get_CCI(get_CCI(ts_Argmax(df['p4_ms3'],4),ts_Median(df['p4_ms4'],38),pn_RankCentered(df['p2_et7']),35),get_CCI(Exp(df['p5_to2']),Log(df['cci']),pn_TransNorm(df['p1_corrs1']),47),get_CCI(Or(df['p5_to6'],df['p6_tn5']),pn_TransNorm(df['p2_et7']),pn_TransNorm(df['p5_to3']),35),47)","276_-1*get_CCI(get_CCI(ts_Argmax(df['p4_ms3'],4),ts_Median(df['p4_ms4'],38),pn_RankCentered(df['p2_et7']),35),get_CCI(Exp(df['p5_to2']),Log(df['cci']),pn_TransNorm(df['p1_corrs1']),47),get_CCI(Or(df['p5_to6'],df['p6_tn5']),pn_TransNorm(df['p2_et7']),pn_TransNorm(df['p5_to3']),35),47)",20.9191,0.0067,1.9167,4.2467,0.5997983601890609,2024-01-01 20:59:00+00:00,2025-01-30 18:59:00+00:00,0.81,1.264,0.3905,-0.48
190,"-1*get_CCI(LEthan(ts_Kurtosis(df['p2_et11'],26),get_CMO(df['p1_corrs4'],47)),ts_Decay(ts_Kurtosis(df['p1_corrs4'],30),2),get_CCI(pn_TransNorm(df['p2_et7']),Log(df['cci']),pn_TransNorm(df['p1_corrs1']),47),47)","277_-1*get_CCI(LEthan(ts_Kurtosis(df['p2_et11'],26),get_CMO(df['p1_corrs4'],47)),ts_Decay(ts_Kurtosis(df['p1_corrs4'],30),2),get_CCI(pn_TransNorm(df['p2_et7']),Log(df['cci']),pn_TransNorm(df['p1_corrs1']),47),47)",21.0077,0.007,1.9551,3.3276,0.5892244307628419,2024-01-01 20:59:00+00:00,2025-01-30 18:59:00+00:00,0.753,1.362,0.356,-0.42
191,"-1*get_CCI(Or(pn_Rank2(df['p1_corrs9']),ts_TransNorm(df['cmo'],30)),ts_Corr(get_LINEARREG_SLOPE(df['p6_tn7'],30),ts_Divide(df['p6_tn4'],32),38),get_CCI(Or(df['p2_et7'],df['p2_et7']),df['p2_et7'],pn_FillMax(df['p6_tn7']),30),47)","279_-1*get_CCI(Or(pn_Rank2(df['p1_corrs9']),ts_TransNorm(df['cmo'],30)),ts_Corr(get_LINEARREG_SLOPE(df['p6_tn7'],30),ts_Divide(df['p6_tn4'],32),38),get_CCI(Or(df['p2_et7'],df['p2_et7']),df['p2_et7'],pn_FillMax(df['p6_tn7']),30),47)",18.6006,0.0077,1.6843,4.1239,0.5813835738519613,2024-01-01 20:59:00+00:00,2025-01-30 18:59:00+00:00,0.829,0.993,0.455,-0.563
192,"-1*get_CCI(get_CCI(Sqrt(df['p6_tn8']),ts_Delay(df['p6_tn4'],32),pn_TransNorm(df['p2_et7']),38),ts_Corr(get_LINEARREG_SLOPE(df['p6_tn7'],30),ts_Decay2(df['p1_corrs8'],10),2),get_CCI(Log(df['cci']),pn_FillMax(df['p6_tn7']),get_DX(df['p1_corrs4'],df['p3_mf0'],df['p2_et14'],50),30),47)","279_-1*get_CCI(get_CCI(Sqrt(df['p6_tn8']),ts_Delay(df['p6_tn4'],32),pn_TransNorm(df['p2_et7']),38),ts_Corr(get_LINEARREG_SLOPE(df['p6_tn7'],30),ts_Decay2(df['p1_corrs8'],10),2),get_CCI(Log(df['cci']),pn_FillMax(df['p6_tn7']),get_DX(df['p1_corrs4'],df['p3_mf0'],df['p2_et14'],50),30),47)",21.7614,0.0064,2.0029,4.2408,0.4392384860114132,2024-01-01 20:59:00+00:00,2025-01-30 18:59:00+00:00,0.711,1.456,0.3281,-0.408
193,"-1*get_CCI(get_CCI(ts_Scale(df['p1_corrs8'],34),get_CMO(df['p1_corrs4'],47),ts_Mean(df['p1_corrs7'],8),30),get_CCI(ts_Corr(df['p6_tn6'],df['p2_et0'],23),get_CMO(df['p5_to6'],43),pn_TransNorm(df['p2_et7']),30),get_CCI(df['p5_to2'],get_CMO(df['p1_corrs1'],45),pn_TransNorm(df['p5_to3']),35),47)","279_-1*get_CCI(get_CCI(ts_Scale(df['p1_corrs8'],34),get_CMO(df['p1_corrs4'],47),ts_Mean(df['p1_corrs7'],8),30),get_CCI(ts_Corr(df['p6_tn6'],df['p2_et0'],23),get_CMO(df['p5_to6'],43),pn_TransNorm(df['p2_et7']),30),get_CCI(df['p5_to2'],get_CMO(df['p1_corrs1'],45),pn_TransNorm(df['p5_to3']),35),47)",15.9418,0.0044,1.4569,3.4536,0.5407144540193056,2024-01-01 20:59:00+00:00,2025-01-30 18:59:00+00:00,0.551,1.026,0.3494,-0.341
194,"-1*get_CCI(get_CCI(Or(df['p2_et7'],df['p2_et7']),df['p2_et7'],ts_Quantile(df['p3_mf5'],48,'D'),38),ts_Corr(pn_TransNorm(df['p5_to3']),get_HT_DCPHASE(df['p6_tn4']),2),LEthan(ts_Cov2(df['p3_mf6'],df['p6_tn7'],12),inv(df['p5_to3'])),43)","280_-1*get_CCI(get_CCI(Or(df['p2_et7'],df['p2_et7']),df['p2_et7'],ts_Quantile(df['p3_mf5'],48,'D'),38),ts_Corr(pn_TransNorm(df['p5_to3']),get_HT_DCPHASE(df['p6_tn4']),2),LEthan(ts_Cov2(df['p3_mf6'],df['p6_tn7'],12),inv(df['p5_to3'])),43)",13.5951,0.0056,1.2264,3.1548,0.5909988049392813,2024-01-01 20:59:00+00:00,2025-01-30 18:59:00+00:00,0.606,0.721,0.4567,-0.52
195,"-1*get_CCI(ts_Quantile(Equal(df['p5_to3'],df['p1_corrs7']),20,'A'),LEthan(df['p1_corrs8'],get_CMO(df['p1_corrs4'],47)),get_CCI(ts_Rank(df['p2_et0'],17),ts_Scale(df['p2_et0'],34),ts_Sum(df['p6_tn7'],21),30),30)","281_-1*get_CCI(ts_Quantile(Equal(df['p5_to3'],df['p1_corrs7']),20,'A'),LEthan(df['p1_corrs8'],get_CMO(df['p1_corrs4'],47)),get_CCI(ts_Rank(df['p2_et0'],17),ts_Scale(df['p2_et0'],34),ts_Sum(df['p6_tn7'],21),30),30)",12.6791,0.0041,1.1382,3.2676,0.4370107808266701,2024-01-01 20:59:00+00:00,2025-01-30 18:59:00+00:00,0.308,0.923,0.2502,-0.213
196,"-1*get_LINEARREG_SLOPE(get_LINEARREG_SLOPE(pn_Stand(df['p1_corrs9']),33),4)","282_-1*get_LINEARREG_SLOPE(get_LINEARREG_SLOPE(pn_Stand(df['p1_corrs9']),33),4)",13.4695,0.0054,1.1945,3.7652,0.4154993914627136,2024-01-01 20:59:00+00:00,2025-01-30 18:59:00+00:00,0.337,0.956,0.2606,-0.222
197,"ts_TransNorm(ts_Quantile(pn_Rank(df['p4_ms6']),49,'A'),33)","283_ts_TransNorm(ts_Quantile(pn_Rank(df['p4_ms6']),49,'A'),33)",10.5632,0.0025,0.9434,3.0053,0.2649965213527749,2024-01-01 20:59:00+00:00,2025-01-30 18:59:00+00:00,0.565,0.456,0.5534,0.075
198,"-1*get_LINEARREG_SLOPE(ts_Cov2(ts_Median(df['p6_tn8'],27),FilterInf(df['p3_mf11']),30),4)","284_-1*get_LINEARREG_SLOPE(ts_Cov2(ts_Median(df['p6_tn8'],27),FilterInf(df['p3_mf11']),30),4)",11.9384,0.0043,1.0715,3.022,0.1899808466452214,2024-01-01 20:59:00+00:00,2025-01-30 18:59:00+00:00,0.487,0.673,0.4198,-0.007
199,"ts_TransNorm(ts_MeanChg(pn_Rank2(df['p5_to0']),33),11)","286_ts_TransNorm(ts_MeanChg(pn_Rank2(df['p5_to0']),33),11)",18.6619,0.0029,1.7134,4.1528,0.39885308205044484,2024-01-01 20:59:00+00:00,2025-01-30 18:59:00+00:00,0.375,1.479,0.2023,-0.16
200,"-1*ts_TransNorm(Min(ts_Delay(df['p4_ms4'],9),pn_RankCentered(df['p6_tn3'])),1)","287_-1*ts_TransNorm(Min(ts_Delay(df['p4_ms4'],9),pn_RankCentered(df['p6_tn3'])),1)",14.622,0.0029,1.3367,3.3237,0.4929131388671212,2024-01-01 20:59:00+00:00,2025-01-30 18:59:00+00:00,0.391,1.055,0.2704,-0.068
201,"pn_GroupRank(Divide(ts_MeanChg(df['p4_ms4'],27),df['p1_corrs3']),ts_Delay(df['p6_tn13'],50))","291_pn_GroupRank(Divide(ts_MeanChg(df['p4_ms4'],27),df['p1_corrs3']),ts_Delay(df['p6_tn13'],50))",11.0843,0.0031,0.9801,3.3822,0.3021146580347078,2024-01-01 20:59:00+00:00,2025-01-30 18:59:00+00:00,0.547,0.514,0.5156,-0.033
202,"ts_MeanChg(ts_Decay(ts_Scale(df['p6_tn13'],7),13),3)","292_ts_MeanChg(ts_Decay(ts_Scale(df['p6_tn13'],7),13),3)",9.7636,0.0044,0.8463,3.2413,0.4573587635539525,2024-01-01 20:59:00+00:00,2025-01-30 18:59:00+00:00,0.388,0.528,0.4236,-0.02
203,"-1*pn_GroupNorm(ts_Delta(df['p2_et0'],45),Divide(df['p6_tn13'],df['p6_tn0']))","292_-1*pn_GroupNorm(ts_Delta(df['p2_et0'],45),Divide(df['p6_tn13'],df['p6_tn0']))",17.3964,0.0081,1.5174,5.4528,0.4244223576747071,2024-01-01 20:59:00+00:00,2025-01-30 18:59:00+00:00,0.855,0.787,0.5207,-0.13
204,"-1*ts_Delta(Add(get_LINEARREG_ANGLE(df['p6_tn13'],48),df['p1_corrs9']),39)","295_-1*ts_Delta(Add(get_LINEARREG_ANGLE(df['p6_tn13'],48),df['p1_corrs9']),39)",12.6146,0.0037,1.1292,3.4196,0.5832117381873455,2024-01-01 20:59:00+00:00,2025-01-30 18:59:00+00:00,0.47,0.752,0.3846,-0.192
205,"-1*pn_GroupRank(LEthan(ts_TransNorm(df['p1_corrs3'],33),ts_Delta(df['p6_tn4'],45)),df['p1_corrs3'])","298_-1*pn_GroupRank(LEthan(ts_TransNorm(df['p1_corrs3'],33),ts_Delta(df['p6_tn4'],45)),df['p1_corrs3'])",13.2228,0.0052,1.161,4.061,0.4069859976311857,2024-01-01 20:59:00+00:00,2025-01-30 18:59:00+00:00,0.299,0.958,0.2379,-0.153
206,"-1*pn_GroupRank(df['p1_corrs3'],ts_Argmax(df['p4_ms0'],34))","298_-1*pn_GroupRank(df['p1_corrs3'],ts_Argmax(df['p4_ms0'],34))",12.0916,0.0045,1.0513,4.0589,0.5160992010278214,2024-01-01 20:59:00+00:00,2025-01-30 18:59:00+00:00,0.456,0.682,0.4007,-0.17
207,"-1*pn_GroupNorm(df['p6_tn7'],get_KAMA(df['p3_mf7'],40))","298_-1*pn_GroupNorm(df['p6_tn7'],get_KAMA(df['p3_mf7'],40))",14.8392,0.0067,1.3036,4.4103,0.532822215872744,2024-01-01 20:59:00+00:00,2025-01-30 18:59:00+00:00,0.865,0.546,0.613,-0.398
208,"-1*pn_GroupRank(ts_Skewness(Log(df['p3_mf4']),13),inv(Add(df['di'],0.124)))","297_-1*pn_GroupRank(ts_Skewness(Log(df['p3_mf4']),13),inv(Add(df['di'],0.124)))",11.2017,0.004,0.9715,3.8586,0.3525008992589348,2024-01-01 20:59:00+00:00,2025-01-30 18:59:00+00:00,0.495,0.556,0.471,-0.117
209,"-1*Add(pn_Stand(get_LINEARREG_ANGLE(df['dcphase'],33)),df['p1_corrs3'])","298_-1*Add(pn_Stand(get_LINEARREG_ANGLE(df['dcphase'],33)),df['p1_corrs3'])",12.226,0.0045,1.0799,3.5979,0.5040122914749122,2024-01-01 20:59:00+00:00,2025-01-30 18:59:00+00:00,0.402,0.767,0.3439,-0.237
210,"-1*pn_GroupRank(df['p1_corrs3'],ts_Partial_corr(Exp(df['p2_et14']),df['p2_et6'],df['p3_mf5'],29))","300_-1*pn_GroupRank(df['p1_corrs3'],ts_Partial_corr(Exp(df['p2_et14']),df['p2_et6'],df['p3_mf5'],29))",10.7863,0.0041,0.932,3.779,0.5988518449984018,2024-01-01 20:59:00+00:00,2025-01-30 18:59:00+00:00,0.286,0.722,0.2837,-0.191
211,"pn_GroupRank(pn_GroupNeutral(df['p4_ms2'],ts_Argmin(df['lislope'],26)),ts_Argmin(pn_GroupRank(df['p4_ms2'],df['p4_ms2']),2))","301_pn_GroupRank(pn_GroupNeutral(df['p4_ms2'],ts_Argmin(df['lislope'],26)),ts_Argmin(pn_GroupRank(df['p4_ms2'],df['p4_ms2']),2))",14.5396,0.0058,1.2779,4.4088,0.5290432123475193,2024-01-01 20:59:00+00:00,2025-01-30 18:59:00+00:00,0.452,0.931,0.3268,-0.135
212,"pn_GroupRank(pn_GroupNeutral(df['di'],df['kama']),Add(df['p2_et17'],0.914))","304_pn_GroupRank(pn_GroupNeutral(df['di'],df['kama']),Add(df['p2_et17'],0.914))",11.5894,0.0047,1.029,3.1921,0.5873228004445391,2024-01-01 20:59:00+00:00,2025-01-30 18:59:00+00:00,0.599,0.515,0.5377,-0.289
213,"-1*pn_GroupRank(df['p6_tn13'],ts_StdevChg(pn_Winsor(df['p3_mf5'],4),23))","309_-1*pn_GroupRank(df['p6_tn13'],ts_StdevChg(pn_Winsor(df['p3_mf5'],4),23))",9.9916,0.0046,0.8533,3.6915,0.5652838526134222,2024-01-01 20:59:00+00:00,2025-01-30 18:59:00+00:00,0.589,0.334,0.6381,-0.121
214,"pn_GroupRank(get_LINEARREG_ANGLE(df['di'],7),Min(ts_Divide(df['p4_ms5'],19),df['p1_corrs0']))","309_pn_GroupRank(get_LINEARREG_ANGLE(df['di'],7),Min(ts_Divide(df['p4_ms5'],19),df['p1_corrs0']))",12.7275,0.0057,1.1134,3.9189,0.5313634435305882,2024-01-01 20:59:00+00:00,2025-01-30 18:59:00+00:00,0.524,0.681,0.4349,-0.424
215,"-1*pn_GroupRank(Add(pn_RankCentered(df['p6_tn4']),get_LINEARREG_SLOPE(df['p4_ms3'],11)),Minus(Abs(df['p3_mf8']),0.856))","310_-1*pn_GroupRank(Add(pn_RankCentered(df['p6_tn4']),get_LINEARREG_SLOPE(df['p4_ms3'],11)),Minus(Abs(df['p3_mf8']),0.856))",9.9627,0.0044,0.861,3.398,0.5361399808319088,2024-01-01 20:59:00+00:00,2025-01-30 18:59:00+00:00,0.453,0.479,0.4861,-0.263
216,"-1*pn_GroupRank(Add(pn_RankCentered(df['p3_mf3']),ts_TransNorm(df['dx'],37)),get_CMO(df['p1_corrs3'],20))","311_-1*pn_GroupRank(Add(pn_RankCentered(df['p3_mf3']),ts_TransNorm(df['dx'],37)),get_CMO(df['p1_corrs3'],20))",10.9137,0.0026,0.9576,3.622,0.5621847907080587,2024-01-01 20:59:00+00:00,2025-01-30 18:59:00+00:00,0.589,0.448,0.568,-0.052
217,"-1*pn_GroupRank(SignedPower(df['lislope'],44),Min(ts_Delta(df['p3_mf4'],47),ts_Quantile(df['dm'],50,'D')))","312_-1*pn_GroupRank(SignedPower(df['lislope'],44),Min(ts_Delta(df['p3_mf4'],47),ts_Quantile(df['dm'],50,'D')))",10.2981,0.0044,0.9058,3.0581,0.5769637722348832,2024-01-01 20:59:00+00:00,2025-01-30 18:59:00+00:00,0.548,0.433,0.5586,-0.45
218,"pn_GroupRank(ts_Delta(df['p6_tn11'],10),pn_GroupNeutral(Mthan(df['lislope'],df['p6_tn4']),Softsign(df['p2_et13'])))","313_pn_GroupRank(ts_Delta(df['p6_tn11'],10),pn_GroupNeutral(Mthan(df['lislope'],df['p6_tn4']),Softsign(df['p2_et13'])))",15.235,0.0032,1.3841,3.7063,0.28452734838749044,2024-01-01 20:59:00+00:00,2025-01-30 18:59:00+00:00,0.459,1.039,0.3064,-0.144
219,"-1*pn_GroupRank(Add(pn_RankCentered(df['p3_mf3']),df['p1_corrs0']),ts_Stdev2(pn_TransNorm(df['p6_tn4']),11))","314_-1*pn_GroupRank(Add(pn_RankCentered(df['p3_mf3']),df['p1_corrs0']),ts_Stdev2(pn_TransNorm(df['p6_tn4']),11))",10.663,0.002,0.9404,3.4701,0.5509439367443829,2024-01-01 20:59:00+00:00,2025-01-30 18:59:00+00:00,0.581,0.437,0.5707,-0.138
220,"pn_GroupRank(pn_RankCentered(ts_Delta(df['p4_ms2'],5)),ts_Partial_corr(Power(df['p2_et13'],16),df['p5_to4'],df['ultosc'],32))","314_pn_GroupRank(pn_RankCentered(ts_Delta(df['p4_ms2'],5)),ts_Partial_corr(Power(df['p2_et13'],16),df['p5_to4'],df['ultosc'],32))",10.9074,0.0039,0.9527,3.558,0.48475225598312466,2024-01-01 20:59:00+00:00,2025-01-30 18:59:00+00:00,0.499,0.532,0.484,-0.146
221,"pn_GroupRank(Minus(ts_TransNorm(df['p6_tn0'],25),0.856),ts_TransNorm(df['p3_mf3'],25))","315_pn_GroupRank(Minus(ts_TransNorm(df['p6_tn0'],25),0.856),ts_TransNorm(df['p3_mf3'],25))",12.4298,0.0058,1.0886,3.7654,0.554716782365903,2024-01-01 20:59:00+00:00,2025-01-30 18:59:00+00:00,0.674,0.505,0.5717,-0.222
222,"-1*ts_Divide(Add(get_KAMA(df['p6_tn3'],25),get_DX(df['p2_et19'],df['p2_et2'],df['p2_et7'],16)),4)","317_-1*ts_Divide(Add(get_KAMA(df['p6_tn3'],25),get_DX(df['p2_et19'],df['p2_et2'],df['p2_et7'],16)),4)",13.1404,0.005,1.1643,3.7425,0.5860893656429996,2024-01-01 20:59:00+00:00,2025-01-30 18:59:00+00:00,0.7,0.56,0.5556,-0.242
223,"Multiply(df['p4_ms1'],ts_Delta(df['p2_et16'],39))","318_Multiply(df['p4_ms1'],ts_Delta(df['p2_et16'],39))",13.5452,0.0033,1.2367,3.0375,0.3815389226260539,2024-01-01 20:59:00+00:00,2025-01-30 18:59:00+00:00,0.545,0.794,0.407,-0.034
224,"-1*get_CCI(ts_CorrChg(ts_Max(df['adosc'],6),df['p1_corrs1'],26),ts_CovChg(ts_CovChg(df['dcphase'],df['liangle'],46),df['p2_et19'],27),df['p1_corrs9'],8)","318_-1*get_CCI(ts_CorrChg(ts_Max(df['adosc'],6),df['p1_corrs1'],26),ts_CovChg(ts_CovChg(df['dcphase'],df['liangle'],46),df['p2_et19'],27),df['p1_corrs9'],8)",9.5995,0.004,0.8317,3.2456,0.5086478657203798,2024-01-01 20:59:00+00:00,2025-01-30 18:59:00+00:00,0.251,0.649,0.2789,-0.148
225,"-1*pn_GroupNeutral(pn_CrossFit(pn_Rank2(df['cci']),df['cci']),get_CMO(ts_Min(df['p3_mf9'],31),7))","319_-1*pn_GroupNeutral(pn_CrossFit(pn_Rank2(df['cci']),df['cci']),get_CMO(ts_Min(df['p3_mf9'],31),7))",9.4409,0.0047,0.8153,3.1575,0.34117169638031053,2024-01-01 20:59:00+00:00,2025-01-30 18:59:00+00:00,0.231,0.651,0.2619,-0.12
226,"pn_GroupRank(ts_Scale(ts_Divide(df['dm'],10),8),pn_TransStd(df['p5_to7']))","320_pn_GroupRank(ts_Scale(ts_Divide(df['dm'],10),8),pn_TransStd(df['p5_to7']))",12.6549,0.0054,1.1049,4.0098,0.5011438866952016,2024-01-01 20:59:00+00:00,2025-01-30 18:59:00+00:00,0.539,0.656,0.451,-0.031
227,"-1*Multiply(df['p4_ms1'],get_KAMA(df['lislope'],38))","321_-1*Multiply(df['p4_ms1'],get_KAMA(df['lislope'],38))",12.9224,0.0052,1.1427,3.7018,0.24595410019373548,2024-01-01 20:59:00+00:00,2025-01-30 18:59:00+00:00,0.332,0.904,0.2686,-0.054
228,"-1*get_CCI(ts_Min(ts_StdevChg(df['p1_corrs1'],30),42),Minus(df['p4_ms5'],0.522),ts_Skewness(df['p4_ms1'],5),8)","322_-1*get_CCI(ts_Min(ts_StdevChg(df['p1_corrs1'],30),42),Minus(df['p4_ms5'],0.522),ts_Skewness(df['p4_ms1'],5),8)",12.6384,0.0034,1.1427,3.1184,0.37894271909005106,2024-01-01 20:59:00+00:00,2025-01-30 18:59:00+00:00,0.638,0.598,0.5162,-0.108
229,"-1*get_CCI(FilterInf(get_LINEARREG_ANGLE(df['p1_corrs1'],34)),ts_CovChg(ts_CovChg(df['p2_et7'],df['p4_ms3'],31),ts_Product(df['p6_tn9'],29),8),df['p1_corrs9'],8)","321_-1*get_CCI(FilterInf(get_LINEARREG_ANGLE(df['p1_corrs1'],34)),ts_CovChg(ts_CovChg(df['p2_et7'],df['p4_ms3'],31),ts_Product(df['p6_tn9'],29),8),df['p1_corrs9'],8)",16.2222,0.0063,1.4513,4.1874,0.5564508468794467,2024-01-01 20:59:00+00:00,2025-01-30 18:59:00+00:00,0.479,1.091,0.3051,-0.248
230,"-1*get_CCI(And(df['p1_corrs9'],ts_Scale(df['p2_et12'],6)),ts_CovChg(ts_Stdev(df['p2_et9'],2),ts_Stdev2(df['p6_tn9'],16),2),df['p1_corrs9'],8)","323_-1*get_CCI(And(df['p1_corrs9'],ts_Scale(df['p2_et12'],6)),ts_CovChg(ts_Stdev(df['p2_et9'],2),ts_Stdev2(df['p6_tn9'],16),2),df['p1_corrs9'],8)",10.4219,0.0039,0.9122,3.3106,0.5137584197730928,2024-01-01 20:59:00+00:00,2025-01-30 18:59:00+00:00,0.26,0.727,0.2634,-0.193
231,"-1*get_CCI(df['p3_mf1'],Reverse(df['p6_tn0']),ts_Cov2(df['p6_tn0'],df['p4_ms3'],21),36)","324_-1*get_CCI(df['p3_mf1'],Reverse(df['p6_tn0']),ts_Cov2(df['p6_tn0'],df['p4_ms3'],21),36)",13.7994,0.0045,1.2349,3.6783,0.5592222692929731,2024-01-01 20:59:00+00:00,2025-01-30 18:59:00+00:00,0.526,0.81,0.3937,-0.172
232,"-1*get_CCI(Divide(df['p5_to2'],0.64),ts_Median(ts_Delay(df['p2_et7'],6),29),df['p1_corrs9'],8)","325_-1*get_CCI(Divide(df['p5_to2'],0.64),ts_Median(ts_Delay(df['p2_et7'],6),29),df['p1_corrs9'],8)",10.5041,0.0039,0.9219,3.2731,0.4979626391220785,2024-01-01 20:59:00+00:00,2025-01-30 18:59:00+00:00,0.271,0.727,0.2715,-0.195
233,"get_CCI(SignedPower(df['p6_tn0'],pn_FillMin(df['p3_mf2'])),pn_FillMax(ts_Median(df['p2_et6'],38)),df['p6_tn0'],10)","326_get_CCI(SignedPower(df['p6_tn0'],pn_FillMin(df['p3_mf2'])),pn_FillMax(ts_Median(df['p2_et6'],38)),df['p6_tn0'],10)",24.3592,0.0035,2.304,3.4264,0.3581040488948661,2024-01-01 20:59:00+00:00,2025-01-30 18:59:00+00:00,1.017,1.476,0.4079,-0.156
234,"-1*get_CCI(df['p1_corrs9'],ts_Corr2(LEthan(df['p2_et12'],df['p6_tn2']),Sqrt(df['p3_mf7']),4),ts_Corr(ts_Regression(df['p1_corrs2'],df['p3_mf2'],11,'D'),ts_Product(df['p2_et16'],18),13),29)","327_-1*get_CCI(df['p1_corrs9'],ts_Corr2(LEthan(df['p2_et12'],df['p6_tn2']),Sqrt(df['p3_mf7']),4),ts_Corr(ts_Regression(df['p1_corrs2'],df['p3_mf2'],11,'D'),ts_Product(df['p2_et16'],18),13),29)",9.2481,0.0039,0.8029,3.0695,0.5254266247675642,2024-01-01 20:59:00+00:00,2025-01-30 18:59:00+00:00,0.226,0.643,0.2601,-0.219
235,"-1*pn_Winsor(ts_Delta(pn_RankCentered(df['p4_ms5']),9),12)","330_-1*pn_Winsor(ts_Delta(pn_RankCentered(df['p4_ms5']),9),12)",15.9404,0.0049,1.4683,3.0326,0.4963632551050313,2024-01-01 20:59:00+00:00,2025-01-30 18:59:00+00:00,0.635,0.954,0.3996,-0.169
236,"-1*pn_RankCentered(Multiply(FilterInf(df['p6_tn1']),Divide(df['p2_et5'],0.61)))","331_-1*pn_RankCentered(Multiply(FilterInf(df['p6_tn1']),Divide(df['p2_et5'],0.61)))",12.3985,0.0054,1.1044,3.2476,0.48442781056800727,2024-01-01 20:59:00+00:00,2025-01-30 18:59:00+00:00,0.461,0.734,0.3858,-0.074
237,"pn_RankCentered(ts_Delta(Reverse(df['p1_corrs1']),39))","332_pn_RankCentered(ts_Delta(Reverse(df['p1_corrs1']),39))",11.2315,0.0038,1.0024,3.0455,0.5737387482429678,2024-01-01 20:59:00+00:00,2025-01-30 18:59:00+00:00,0.261,0.823,0.2408,-0.261
238,"-1*pn_Winsor(pn_GroupRank(pn_CrossFit(df['p1_corrs2'],df['p6_tn4']),get_KAMA(df['p2_et18'],39)),17)","331_-1*pn_Winsor(pn_GroupRank(pn_CrossFit(df['p1_corrs2'],df['p6_tn4']),get_KAMA(df['p2_et18'],39)),17)",12.4063,0.0052,1.0768,4.1389,0.5218356112534757,2024-01-01 20:59:00+00:00,2025-01-30 18:59:00+00:00,0.686,0.479,0.5888,-0.126
239,"pn_Winsor(ts_Delay(get_CCI(df['p6_tn8'],df['p1_corrs2'],df['p6_tn4'],9),12),9)","328_pn_Winsor(ts_Delay(get_CCI(df['p6_tn8'],df['p1_corrs2'],df['p6_tn4'],9),12),9)",12.3555,0.0043,1.1034,3.3186,0.38617859164689217,2024-01-01 20:59:00+00:00,2025-01-30 18:59:00+00:00,0.578,0.616,0.4841,-0.003
240,"-1*pn_RankCentered(pn_GroupNeutral(pn_Winsor(df['p5_to0'],48),ts_Argmax(df['p2_et5'],35)))","329_-1*pn_RankCentered(pn_GroupNeutral(pn_Winsor(df['p5_to0'],48),ts_Argmax(df['p2_et5'],35)))",9.7077,0.0048,0.8454,3.0336,0.5638948021090909,2024-01-01 20:59:00+00:00,2025-01-30 18:59:00+00:00,0.425,0.49,0.4645,-0.087
241,"-1*pn_RankCentered(get_CCI(Exp(df['p1_corrs6']),ts_Sum(df['p5_to0'],38),FilterInf(df['cci']),9))","328_-1*pn_RankCentered(get_CCI(Exp(df['p1_corrs6']),ts_Sum(df['p5_to0'],38),FilterInf(df['cci']),9))",16.5582,0.0051,1.4917,4.1608,0.586937366219697,2024-01-01 20:59:00+00:00,2025-01-30 18:59:00+00:00,0.384,1.231,0.2378,-0.223
242,"-1*pn_RankCentered(get_CCI(pn_TransNorm(df['dx']),df['p1_corrs2'],ts_Cov2(df['p5_to0'],df['p3_mf7'],38),9))","329_-1*pn_RankCentered(get_CCI(pn_TransNorm(df['dx']),df['p1_corrs2'],ts_Cov2(df['p5_to0'],df['p3_mf7'],38),9))",13.7841,0.0029,1.2547,3.2787,0.4216847439001424,2024-01-01 20:59:00+00:00,2025-01-30 18:59:00+00:00,0.534,0.824,0.3932,-0.057
243,"-1*pn_GroupNorm(ts_Quantile(df['p6_tn10'],1,'B'),df['p2_et1'])","330_-1*pn_GroupNorm(ts_Quantile(df['p6_tn10'],1,'B'),df['p2_et1'])",14.4411,0.0056,1.2973,3.5606,0.5794700416229014,2024-01-01 20:59:00+00:00,2025-01-30 18:59:00+00:00,0.782,0.622,0.557,-0.486
244,"-1*Min(pn_GroupNorm(df['p3_mf11'],df['p1_corrs5']),ts_Median(Reverse(df['lislope']),8))","331_-1*Min(pn_GroupNorm(df['p3_mf11'],df['p1_corrs5']),ts_Median(Reverse(df['lislope']),8))",10.8438,0.0037,0.9603,3.1631,0.5109914946871476,2024-01-01 20:59:00+00:00,2025-01-30 18:59:00+00:00,0.903,0.137,0.8683,-0.084
245,"-1*pn_GroupNorm(SignedPower(df['p5_to0'],df['p3_mf10']),pn_GroupNeutral(ts_StdevChg(df['p3_mf7'],5),df['cci']))","334_-1*pn_GroupNorm(SignedPower(df['p5_to0'],df['p3_mf10']),pn_GroupNeutral(ts_StdevChg(df['p3_mf7'],5),df['cci']))",10.3026,0.0038,0.908,3.1017,0.47746995788678037,2024-01-01 20:59:00+00:00,2025-01-30 18:59:00+00:00,0.221,0.762,0.2248,-0.101
246,"-1*pn_GroupNorm(MEthan(df['adosc'],df['dcphase']),ts_Skewness(df['p2_et0'],30))","335_-1*pn_GroupNorm(MEthan(df['adosc'],df['dcphase']),ts_Skewness(df['p2_et0'],30))",9.9737,0.0032,0.8728,3.2533,0.2975465058934213,2024-01-01 20:59:00+00:00,2025-01-30 18:59:00+00:00,0.258,0.687,0.273,-0.145
247,"pn_GroupNorm(df['p4_ms2'],ts_Argmin(pn_Winsor(df['p1_corrs8'],50),37))","329_pn_GroupNorm(df['p4_ms2'],ts_Argmin(pn_Winsor(df['p1_corrs8'],50),37))",13.7575,0.005,1.2215,3.8815,0.5552892155220966,2024-01-01 20:59:00+00:00,2025-01-30 18:59:00+00:00,0.359,0.963,0.2716,-0.159
248,"-1*pn_GroupNorm(ts_ChgRate(Exp(df['p1_corrs0']),26),get_DX(get_LINEARREG_SLOPE(df['p3_mf0'],11),df['p1_corrs0'],get_CMO(df['p4_ms1'],50),25))","330_-1*pn_GroupNorm(ts_ChgRate(Exp(df['p1_corrs0']),26),get_DX(get_LINEARREG_SLOPE(df['p3_mf0'],11),df['p1_corrs0'],get_CMO(df['p4_ms1'],50),25))",10.8432,0.0046,0.9456,3.4776,0.5388156720277918,2024-01-01 20:59:00+00:00,2025-01-30 18:59:00+00:00,0.38,0.643,0.3715,-0.187
249,"-1*pn_GroupNorm(Divide(get_CMO(df['p4_ms1'],50),df['p5_to2']),ts_Skewness(df['p1_corrs0'],19))","331_-1*pn_GroupNorm(Divide(get_CMO(df['p4_ms1'],50),df['p5_to2']),ts_Skewness(df['p1_corrs0'],19))",10.5467,0.0051,0.9177,3.3452,0.5345534013007778,2024-01-01 20:59:00+00:00,2025-01-30 18:59:00+00:00,0.535,0.458,0.5388,-0.136
250,"-1*pn_GroupNorm(ts_Decay2(df['ultosc'],9),ts_Kurtosis(get_CMO(df['p2_et11'],7),12))","332_-1*pn_GroupNorm(ts_Decay2(df['ultosc'],9),ts_Kurtosis(get_CMO(df['p2_et11'],7),12))",10.1447,0.0045,0.8915,3.0204,0.5307919052904119,2024-01-01 20:59:00+00:00,2025-01-30 18:59:00+00:00,0.747,0.218,0.7741,-0.349
251,"-1*Min(pn_RankCentered(ts_ChgRate(df['p6_tn4'],35)),df['p1_corrs8'])","333_-1*Min(pn_RankCentered(ts_ChgRate(df['p6_tn4'],35)),df['p1_corrs8'])",10.3554,0.0046,0.9093,3.1036,0.4989421509269689,2024-01-01 20:59:00+00:00,2025-01-30 18:59:00+00:00,0.286,0.698,0.2907,-0.199
252,"-1*pn_GroupNorm(Max(df['p1_corrs1'],df['p2_et0']),ts_MeanChg(df['p4_ms2'],19))","333_-1*pn_GroupNorm(Max(df['p1_corrs1'],df['p2_et0']),ts_MeanChg(df['p4_ms2'],19))",13.1462,0.006,1.1488,4.0795,0.5791056471268915,2024-01-01 20:59:00+00:00,2025-01-30 18:59:00+00:00,0.398,0.845,0.3202,-0.268
253,"-1*pn_GroupNorm(Max(get_CMO(df['p1_corrs0'],50),ts_Kurtosis(df['lislope'],33)),ts_Entropy(df['p2_et8'],12))","334_-1*pn_GroupNorm(Max(get_CMO(df['p1_corrs0'],50),ts_Kurtosis(df['lislope'],33)),ts_Entropy(df['p2_et8'],12))",10.4703,0.0038,0.9174,3.3175,0.5364362570936176,2024-01-01 20:59:00+00:00,2025-01-30 18:59:00+00:00,0.369,0.624,0.3716,-0.188
254,"-1*pn_Rank2(ts_Decay2(get_LINEARREG_SLOPE(df['p2_et0'],5),25))","337_-1*pn_Rank2(ts_Decay2(get_LINEARREG_SLOPE(df['p2_et0'],5),25))",16.1208,0.0064,1.4629,3.5206,0.5345373962589671,2024-01-01 20:59:00+00:00,2025-01-30 18:59:00+00:00,0.586,0.997,0.3702,-0.339
255,"-1*ts_Regression(Exp(df['p1_corrs0']),ts_Rank(df['lislope'],38),25,'A')","339_-1*ts_Regression(Exp(df['p1_corrs0']),ts_Rank(df['lislope'],38),25,'A')",9.7223,0.004,0.8518,3.0108,0.32916748401813534,2024-01-01 20:59:00+00:00,2025-01-30 18:59:00+00:00,0.376,0.546,0.4078,-0.055
256,"-1*pn_Rank2(get_LINEARREG_ANGLE(df['p6_tn5'],11))","339_-1*pn_Rank2(get_LINEARREG_ANGLE(df['p6_tn5'],11))",18.6148,0.0066,1.6929,4.0615,0.5655498534858857,2024-01-01 20:59:00+00:00,2025-01-30 18:59:00+00:00,0.815,1.017,0.4449,-0.336
257,"-1*pn_GroupNeutral(ts_Median(df['p3_mf11'],16),pn_GroupRank(ts_Median(df['p3_mf11'],16),ts_Regression(df['dcperiod'],df['p1_corrs0'],32,'D')))","340_-1*pn_GroupNeutral(ts_Median(df['p3_mf11'],16),pn_GroupRank(ts_Median(df['p3_mf11'],16),ts_Regression(df['dcperiod'],df['p1_corrs0'],32,'D')))",9.5819,0.0036,0.8294,3.3222,0.29210571183057704,2024-01-01 20:59:00+00:00,2025-01-30 18:59:00+00:00,0.359,0.539,0.3998,-0.049
258,"-1*ts_TransNorm(pn_GroupRank(ts_Median(df['p3_mf11'],16),ts_Regression(df['p2_et4'],df['p1_corrs9'],19,'D')),21)","341_-1*ts_TransNorm(pn_GroupRank(ts_Median(df['p3_mf11'],16),ts_Regression(df['p2_et4'],df['p1_corrs9'],19,'D')),21)",11.7653,0.0047,1.0034,4.4915,0.43730769263818514,2024-01-01 20:59:00+00:00,2025-01-30 18:59:00+00:00,0.267,0.819,0.2459,-0.139
259,"-1*pn_GroupNeutral(get_LINEARREG_SLOPE(LEthan(df['dcperiod'],df['p6_tn13']),36),Exp(ts_Sum(df['p3_mf9'],16)))","344_-1*pn_GroupNeutral(get_LINEARREG_SLOPE(LEthan(df['dcperiod'],df['p6_tn13']),36),Exp(ts_Sum(df['p3_mf9'],16)))",10.9713,0.0039,0.9437,4.0224,0.36316025929887347,2024-01-01 20:59:00+00:00,2025-01-30 18:59:00+00:00,0.352,0.67,0.3444,-0.16
260,"-1*pn_GroupNeutral(pn_GroupRank(ts_Regression(df['p3_mf11'],df['p1_corrs0'],33,'D'),ts_Sum(df['p4_ms3'],6)),Power(df['p1_corrs3'],45))","344_-1*pn_GroupNeutral(pn_GroupRank(ts_Regression(df['p3_mf11'],df['p1_corrs0'],33,'D'),ts_Sum(df['p4_ms3'],6)),Power(df['p1_corrs3'],45))",15.5743,0.0065,1.3766,4.4424,0.5751956785697921,2024-01-01 20:59:00+00:00,2025-01-30 18:59:00+00:00,0.733,0.757,0.4919,-0.132
261,"-1*pn_GroupNeutral(LEthan(ts_Divide(df['p2_et1'],44),df['p6_tn13']),ts_Mean(df['p6_tn9'],7))","345_-1*pn_GroupNeutral(LEthan(ts_Divide(df['p2_et1'],44),df['p6_tn13']),ts_Mean(df['p6_tn9'],7))",13.1027,0.0036,1.1724,3.5892,0.4626137237010466,2024-01-01 20:59:00+00:00,2025-01-30 18:59:00+00:00,0.321,0.948,0.253,-0.094
262,"-1*pn_GroupNeutral(LEthan(IfThen(df['p2_et16'],48,39),df['p6_tn13']),pn_Rank2(df['p6_tn3']))","346_-1*pn_GroupNeutral(LEthan(IfThen(df['p2_et16'],48,39),df['p6_tn13']),pn_Rank2(df['p6_tn3']))",10.5199,0.0029,0.9283,3.2784,0.3191041838104957,2024-01-01 20:59:00+00:00,2025-01-30 18:59:00+00:00,0.363,0.641,0.3616,-0.019
263,"-1*pn_GroupNeutral(LEthan(IfThen(df['p3_mf9'],48,39),df['p6_tn13']),Add(ts_TransNorm(df['p1_corrs0'],48),ts_Argmin(df['p4_ms0'],47)))","347_-1*pn_GroupNeutral(LEthan(IfThen(df['p3_mf9'],48,39),df['p6_tn13']),Add(ts_TransNorm(df['p1_corrs0'],48),ts_Argmin(df['p4_ms0'],47)))",10.0223,0.0044,0.8787,3.0444,0.5146837051509658,2024-01-01 20:59:00+00:00,2025-01-30 18:59:00+00:00,0.225,0.726,0.2366,-0.086
264,"-1*pn_GroupNeutral(LEthan(IfThen(df['p2_et16'],48,39),df['p6_tn13']),Add(ts_TransNorm(df['p1_corrs9'],48),ts_Argmin(df['p3_mf1'],5)))","348_-1*pn_GroupNeutral(LEthan(IfThen(df['p2_et16'],48,39),df['p6_tn13']),Add(ts_TransNorm(df['p1_corrs9'],48),ts_Argmin(df['p3_mf1'],5)))",9.3648,0.0024,0.8227,3.0492,0.5198184237713349,2024-01-01 20:59:00+00:00,2025-01-30 18:59:00+00:00,0.349,0.542,0.3917,-0.03
265,"pn_GroupNeutral(ts_MeanChg(df['p4_ms5'],8),get_LINEARREG_ANGLE(df['dcphase'],14))","349_pn_GroupNeutral(ts_MeanChg(df['p4_ms5'],8),get_LINEARREG_ANGLE(df['dcphase'],14))",14.5449,0.0059,1.3035,3.6496,0.49819637125501637,2024-01-01 20:59:00+00:00,2025-01-30 18:59:00+00:00,0.779,0.631,0.5525,-0.169
266,"-1*pn_GroupNeutral(pn_GroupRank(ts_Median(df['p6_tn13'],16),pn_Rank(df['p3_mf0'])),MEthan(get_HT_DCPHASE(df['p4_ms1']),Log(df['p1_corrs8'])))","351_-1*pn_GroupNeutral(pn_GroupRank(ts_Median(df['p6_tn13'],16),pn_Rank(df['p3_mf0'])),MEthan(get_HT_DCPHASE(df['p4_ms1']),Log(df['p1_corrs8'])))",11.544,0.0034,1.0253,3.3568,0.45571156193625945,2024-01-01 20:59:00+00:00,2025-01-30 18:59:00+00:00,0.503,0.606,0.4536,-0.101
267,"-1*pn_GroupNeutral(pn_GroupRank(ts_Median(df['p3_mf11'],4),df['p6_tn6']),get_HT_DCPHASE(Equal(df['p6_tn9'],df['p3_mf11'])))","351_-1*pn_GroupNeutral(pn_GroupRank(ts_Median(df['p3_mf11'],4),df['p6_tn6']),get_HT_DCPHASE(Equal(df['p6_tn9'],df['p3_mf11'])))",14.5397,0.0057,1.2776,4.433,0.5310745995843869,2024-01-01 20:59:00+00:00,2025-01-30 18:59:00+00:00,0.591,0.792,0.4273,-0.273
268,"-1*pn_GroupNeutral(ts_Partial_corr(df['p3_mf11'],Abs(df['p3_mf3']),ts_Quantile(df['p2_et8'],32,'A'),30),ts_Median(df['p3_mf11'],16))","352_-1*pn_GroupNeutral(ts_Partial_corr(df['p3_mf11'],Abs(df['p3_mf3']),ts_Quantile(df['p2_et8'],32,'A'),30),ts_Median(df['p3_mf11'],16))",9.3429,0.0032,0.806,3.3721,0.22661631123639947,2024-01-01 20:59:00+00:00,2025-01-30 18:59:00+00:00,0.325,0.547,0.3727,-0.022
269,"-1*pn_GroupNeutral(pn_GroupRank(ts_Median(df['p3_mf11'],16),ts_Delta(df['cmo'],5)),Lthan(df['p1_corrs9'],df['p6_tn1']))","353_-1*pn_GroupNeutral(pn_GroupRank(ts_Median(df['p3_mf11'],16),ts_Delta(df['cmo'],5)),Lthan(df['p1_corrs9'],df['p6_tn1']))",12.7423,0.0047,1.1287,3.6528,0.5119314474678995,2024-01-01 20:59:00+00:00,2025-01-30 18:59:00+00:00,0.373,0.849,0.3052,-0.166
270,"-1*ts_Sum(ts_Rank(df['p6_tn5'],6),11)","354_-1*ts_Sum(ts_Rank(df['p6_tn5'],6),11)",14.055,0.0053,1.261,3.5371,0.5581438738445759,2024-01-01 20:59:00+00:00,2025-01-30 18:59:00+00:00,0.313,1.052,0.2293,-0.249
271,"-1*ts_Divide(pn_GroupRank(Add(df['p2_et1'],0.021),get_LINEARREG_SLOPE(df['p6_tn9'],48)),22)","356_-1*ts_Divide(pn_GroupRank(Add(df['p2_et1'],0.021),get_LINEARREG_SLOPE(df['p6_tn9'],48)),22)",12.6303,0.0054,1.0951,4.2292,0.45432914454281476,2024-01-01 20:59:00+00:00,2025-01-30 18:59:00+00:00,0.601,0.584,0.5072,-0.116
272,"-1*ts_Divide(df['p6_tn5'],22)","357_-1*ts_Divide(df['p6_tn5'],22)",11.8292,0.0056,1.0404,3.4349,0.5287854616465422,2024-01-01 20:59:00+00:00,2025-01-30 18:59:00+00:00,0.429,0.697,0.381,-0.211
273,"-1*ts_Divide(pn_GroupRank(ts_Rank(df['p6_tn5'],22),ts_Cov2(df['p2_et12'],df['p3_mf5'],47)),22)","359_-1*ts_Divide(pn_GroupRank(ts_Rank(df['p6_tn5'],22),ts_Cov2(df['p2_et12'],df['p3_mf5'],47)),22)",10.9128,0.0048,0.9313,4.0853,0.5861771267475311,2024-01-01 20:59:00+00:00,2025-01-30 18:59:00+00:00,0.465,0.543,0.4613,-0.114
274,"-1*ts_Scale(pn_GroupRank(pn_TransNorm(df['liangle']),ts_Rank(df['p1_corrs1'],9)),18)","361_-1*ts_Scale(pn_GroupRank(pn_TransNorm(df['liangle']),ts_Rank(df['p1_corrs1'],9)),18)",13.3099,0.0027,1.2029,3.4335,0.4294627705681124,2024-01-01 20:59:00+00:00,2025-01-30 18:59:00+00:00,0.633,0.669,0.4862,-0.267
275,"-1*ts_Scale(pn_GroupRank(ts_Rank(df['p6_tn5'],49),ts_Decay2(df['p2_et11'],49)),21)","362_-1*ts_Scale(pn_GroupRank(ts_Rank(df['p6_tn5'],49),ts_Decay2(df['p2_et11'],49)),21)",21.3788,0.004,2.0067,3.3278,0.4747114998934559,2024-01-01 20:59:00+00:00,2025-01-30 18:59:00+00:00,0.735,1.437,0.3384,-0.158
276,"-1*Add(ts_Partial_corr(df['p3_mf9'],Abs(df['cmo']),df['p4_ms5'],29),df['p1_corrs9'])","363_-1*Add(ts_Partial_corr(df['p3_mf9'],Abs(df['cmo']),df['p4_ms5'],29),df['p1_corrs9'])",9.5742,0.0042,0.8356,3.0191,0.5207009368147003,2024-01-01 20:59:00+00:00,2025-01-30 18:59:00+00:00,0.248,0.656,0.2743,-0.279
277,"-1*Add(ts_Corr(df['p6_tn6'],ts_Skewness(df['p5_to7'],19),41),pn_GroupRank(ts_Rank(df['p1_corrs1'],9),ts_Stdev2(df['liangle'],48)))","364_-1*Add(ts_Corr(df['p6_tn6'],ts_Skewness(df['p5_to7'],19),41),pn_GroupRank(ts_Rank(df['p1_corrs1'],9),ts_Stdev2(df['liangle'],48)))",10.152,0.0044,0.8642,3.8709,0.5253487822149705,2024-01-01 20:59:00+00:00,2025-01-30 18:59:00+00:00,0.483,0.452,0.5166,-0.144
278,"-1*ts_Divide(pn_GroupRank(df['p3_mf11'],ts_Corr(df['cmo'],df['p4_ms5'],49)),22)","364_-1*ts_Divide(pn_GroupRank(df['p3_mf11'],ts_Corr(df['cmo'],df['p4_ms5'],49)),22)",11.7107,0.0053,1.0158,3.8544,0.5262356867453941,2024-01-01 20:59:00+00:00,2025-01-30 18:59:00+00:00,0.704,0.395,0.6406,-0.123
279,"-1*ts_Divide(pn_GroupRank(ts_Scale(df['p3_mf11'],32),ts_Corr(df['p2_et7'],df['liangle'],49)),22)","365_-1*ts_Divide(pn_GroupRank(ts_Scale(df['p3_mf11'],32),ts_Corr(df['p2_et7'],df['liangle'],49)),22)",12.3458,0.005,1.0571,4.5765,0.5574584702678683,2024-01-01 20:59:00+00:00,2025-01-30 18:59:00+00:00,0.765,0.379,0.6687,-0.072
280,"ts_Scale(pn_GroupRank(ts_Corr(df['cmo'],df['liangle'],49),pn_TransNorm(df['p6_tn13'])),25)","367_ts_Scale(pn_GroupRank(ts_Corr(df['cmo'],df['liangle'],49),pn_TransNorm(df['p6_tn13'])),25)",10.578,0.0024,0.9213,3.737,0.11666929936648293,2024-01-01 20:59:00+00:00,2025-01-30 18:59:00+00:00,0.541,0.456,0.5426,0.009
281,"ts_Divide(pn_GroupRank(FilterInf(df['p1_corrs7']),df['p6_tn6']),11)","367_ts_Divide(pn_GroupRank(FilterInf(df['p1_corrs7']),df['p6_tn6']),11)",10.5735,0.004,0.8994,4.1322,0.34980519621743134,2024-01-01 20:59:00+00:00,2025-01-30 18:59:00+00:00,0.265,0.708,0.2724,-0.038
282,"-1*ts_Divide(pn_GroupRank(pn_CrossFit(df['p1_corrs7'],df['p2_et0']),df['p3_mf1']),7)","370_-1*ts_Divide(pn_GroupRank(pn_CrossFit(df['p1_corrs7'],df['p2_et0']),df['p3_mf1']),7)",9.7709,0.0042,0.8496,3.1964,0.5082974013921427,2024-01-01 20:59:00+00:00,2025-01-30 18:59:00+00:00,0.542,0.378,0.5891,-0.109
283,"pn_Winsor(pn_GroupRank(ts_Corr(df['cmo'],df['liangle'],49),Minus(df['p6_tn0'],df['cmo'])),18)","371_pn_Winsor(pn_GroupRank(ts_Corr(df['cmo'],df['liangle'],49),Minus(df['p6_tn0'],df['cmo'])),18)",9.1978,0.0022,0.8061,3.0877,0.4439082634942412,2024-01-01 20:59:00+00:00,2025-01-30 18:59:00+00:00,0.366,0.506,0.4197,0.012
284,"-1*Add(pn_Rank(ts_Decay2(df['adosc'],12)),pn_GroupNeutral(ts_Rank(df['p1_corrs1'],9),df['p1_corrs6']))","372_-1*Add(pn_Rank(ts_Decay2(df['adosc'],12)),pn_GroupNeutral(ts_Rank(df['p1_corrs1'],9),df['p1_corrs6']))",13.0599,0.0049,1.155,3.7967,0.5007502630096248,2024-01-01 20:59:00+00:00,2025-01-30 18:59:00+00:00,0.409,0.841,0.3272,-0.276
285,"-1*ts_Divide(pn_GroupRank(FilterInf(df['p6_tn1']),ts_Scale(df['p2_et3'],7)),11)","372_-1*ts_Divide(pn_GroupRank(FilterInf(df['p6_tn1']),ts_Scale(df['p2_et3'],7)),11)",11.3305,0.0048,0.9679,4.2385,0.47170462528090257,2024-01-01 20:59:00+00:00,2025-01-30 18:59:00+00:00,0.539,0.509,0.5143,-0.105
286,"-1*ts_Divide(ts_Delay(df['p4_ms6'],35),11)","373_-1*ts_Divide(ts_Delay(df['p4_ms6'],35),11)",12.994,0.0049,1.1224,4.5815,0.19518918492313597,2024-01-01 20:59:00+00:00,2025-01-30 18:59:00+00:00,0.447,0.767,0.3682,0.008
287,"Add(Minus(inv(df['p5_to2']),Log(df['p6_tn5'])),ts_Entropy(df['p3_mf5'],33))","374_Add(Minus(inv(df['p5_to2']),Log(df['p6_tn5'])),ts_Entropy(df['p3_mf5'],33))",9.6867,0.0036,0.8197,3.9309,0.33491489456279266,2024-01-01 20:59:00+00:00,2025-01-30 18:59:00+00:00,0.284,0.603,0.3202,-0.081
288,"-1*ts_Divide(pn_GroupRank(df['p6_tn13'],IfThen(df['p2_et15'],29,45)),22)","375_-1*ts_Divide(pn_GroupRank(df['p6_tn13'],IfThen(df['p2_et15'],29,45)),22)",12.2025,0.0057,1.0612,3.9191,0.5629008701723133,2024-01-01 20:59:00+00:00,2025-01-30 18:59:00+00:00,0.527,0.621,0.4591,-0.1
289,"-1*Add(pn_Stand(ts_Cov2(df['p3_mf9'],df['p1_corrs7'],41)),get_CCI(df['p2_et17'],df['liangle'],pn_TransNorm(df['p2_et14']),11))","376_-1*Add(pn_Stand(ts_Cov2(df['p3_mf9'],df['p1_corrs7'],41)),get_CCI(df['p2_et17'],df['liangle'],pn_TransNorm(df['p2_et14']),11))",13.739,0.0028,1.2567,3.0936,0.4643463641061889,2024-01-01 20:59:00+00:00,2025-01-30 18:59:00+00:00,0.622,0.738,0.4574,-0.032
290,"-1*Add(pn_FillMax(df['p3_mf11']),get_CMO(ts_Quantile(df['p6_tn13'],7,'B'),7))","377_-1*Add(pn_FillMax(df['p3_mf11']),get_CMO(ts_Quantile(df['p6_tn13'],7,'B'),7))",13.2267,0.0051,1.1747,3.6789,0.5348003941851002,2024-01-01 20:59:00+00:00,2025-01-30 18:59:00+00:00,0.332,0.94,0.261,-0.229
291,"-1*Add(pn_GroupRank(df['p6_tn13'],ts_TransNorm(df['p6_tn3'],16)),get_CCI(pn_TransNorm(df['p2_et0']),get_LINEARREG_SLOPE(df['p1_corrs5'],10),df['p2_et4'],11))","378_-1*Add(pn_GroupRank(df['p6_tn13'],ts_TransNorm(df['p6_tn3'],16)),get_CCI(pn_TransNorm(df['p2_et0']),get_LINEARREG_SLOPE(df['p1_corrs5'],10),df['p2_et4'],11))",14.7123,0.0047,1.3474,3.008,0.5641746579075748,2024-01-01 20:59:00+00:00,2025-01-30 18:59:00+00:00,0.622,0.836,0.4266,-0.084
292,"-1*Add(pn_GroupRank(df['p1_corrs5'],df['p5_to3']),ts_Max(ts_Scale(df['p6_tn10'],18),4))","378_-1*Add(pn_GroupRank(df['p1_corrs5'],df['p5_to3']),ts_Max(ts_Scale(df['p6_tn10'],18),4))",10.9393,0.0046,0.9651,3.1765,0.39090769784186996,2024-01-01 20:59:00+00:00,2025-01-30 18:59:00+00:00,0.33,0.714,0.3161,-0.209
293,"-1*Add(Divide(df['p2_et7'],df['p5_to3']),UnEqual(df['p6_tn0'],get_CMO(df['p1_corrs7'],7)))","378_-1*Add(Divide(df['p2_et7'],df['p5_to3']),UnEqual(df['p6_tn0'],get_CMO(df['p1_corrs7'],7)))",20.0839,0.0093,1.7784,5.5104,0.5760199849283009,2024-01-01 20:59:00+00:00,2025-01-30 18:59:00+00:00,1.14,0.785,0.5922,-0.575
294,"-1*Add(pn_Stand(df['liangle']),ts_Max(ts_Scale(df['p4_ms5'],18),4))","379_-1*Add(pn_Stand(df['liangle']),ts_Max(ts_Scale(df['p4_ms5'],18),4))",10.224,0.0041,0.8994,3.079,0.48010077706544907,2024-01-01 20:59:00+00:00,2025-01-30 18:59:00+00:00,0.345,0.629,0.3542,-0.262
295,"-1*Add(Minus(ts_Scale(df['p2_et4'],25),0.042),get_CMO(df['p2_et14'],49))","381_-1*Add(Minus(ts_Scale(df['p2_et4'],25),0.042),get_CMO(df['p2_et14'],49))",9.4028,0.003,0.8194,3.1715,0.4800821278277076,2024-01-01 20:59:00+00:00,2025-01-30 18:59:00+00:00,0.284,0.603,0.3202,-0.002
296,"-1*Add(Minus(ts_Scale(df['p2_et4'],50),0.042),ts_Max(ts_Scale(df['p6_tn10'],18),4))","382_-1*Add(Minus(ts_Scale(df['p2_et4'],50),0.042),ts_Max(ts_Scale(df['p6_tn10'],18),4))",10.5609,0.0048,0.9291,3.0917,0.5783431471589634,2024-01-01 20:59:00+00:00,2025-01-30 18:59:00+00:00,0.468,0.537,0.4657,-0.288
297,"-1*Add(ts_Delay(df['p3_mf4'],34),get_CMO(df['p2_et0'],7))","383_-1*Add(ts_Delay(df['p3_mf4'],34),get_CMO(df['p2_et0'],7))",14.755,0.0038,1.3069,4.4804,0.5878257382120234,2024-01-01 20:59:00+00:00,2025-01-30 18:59:00+00:00,0.738,0.677,0.5216,-0.107
298,"Add(get_CMO(Lthan(df['p5_to0'],df['p2_et0']),36),get_CMO(get_CMO(df['p1_corrs7'],7),36))","384_Add(get_CMO(Lthan(df['p5_to0'],df['p2_et0']),36),get_CMO(get_CMO(df['p1_corrs7'],7),36))",9.9207,0.004,0.8695,3.0791,0.41273287713120405,2024-01-01 20:59:00+00:00,2025-01-30 18:59:00+00:00,0.276,0.665,0.2933,-0.096
299,"ts_Delta(pn_Rank(df['p4_ms2']),14)","385_ts_Delta(pn_Rank(df['p4_ms2']),14)",10.9826,0.0044,0.9569,3.5796,0.47219058651677714,2024-01-01 20:59:00+00:00,2025-01-30 18:59:00+00:00,0.268,0.767,0.2589,-0.129
300,"ts_StdevChg(Power(df['p1_corrs5'],9),10)","387_ts_StdevChg(Power(df['p1_corrs5'],9),10)",10.4463,0.0033,0.9274,3.024,0.2873411653103498,2024-01-01 20:59:00+00:00,2025-01-30 18:59:00+00:00,0.484,0.52,0.4821,-0.064
301,"-1*pn_GroupNeutral(df['p3_mf10'],df['p1_corrs5'])","388_-1*pn_GroupNeutral(df['p3_mf10'],df['p1_corrs5'])",10.3381,0.0027,0.9192,3.0284,0.4095345978565955,2024-01-01 20:59:00+00:00,2025-01-30 18:59:00+00:00,0.313,0.681,0.3149,-0.031
302,"-1*pn_GroupNeutral(get_CMO(df['p2_et9'],17),ts_Entropy(ts_Kurtosis(df['p5_to7'],39),12))","386_-1*pn_GroupNeutral(get_CMO(df['p2_et9'],17),ts_Entropy(ts_Kurtosis(df['p5_to7'],39),12))",15.2508,0.007,1.3323,4.7318,0.5380262704964541,2024-01-01 20:59:00+00:00,2025-01-30 18:59:00+00:00,0.668,0.774,0.4632,-0.254
303,"pn_GroupNeutral(df['p5_to6'],Minus(Exp(df['p2_et2']),df['p4_ms3']))","388_pn_GroupNeutral(df['p5_to6'],Minus(Exp(df['p2_et2']),df['p4_ms3']))",9.2395,0.0028,0.8011,3.2713,0.5198617043947146,2024-01-01 20:59:00+00:00,2025-01-30 18:59:00+00:00,0.415,0.452,0.4787,-0.026
304,"pn_GroupNeutral(ts_Cov(get_MINUS_DM(df['p5_to4'],df['p3_mf10'],8),df['p2_et0'],39),Minus(get_LINEARREG_ANGLE(df['p1_corrs3'],42),Exp(df['p3_mf7'])))","389_pn_GroupNeutral(ts_Cov(get_MINUS_DM(df['p5_to4'],df['p3_mf10'],8),df['p2_et0'],39),Minus(get_LINEARREG_ANGLE(df['p1_corrs3'],42),Exp(df['p3_mf7'])))",10.3781,0.003,0.9105,3.3695,0.17085715788186806,2024-01-01 20:59:00+00:00,2025-01-30 18:59:00+00:00,0.383,0.602,0.3888,0.01
305,"-1*pn_GroupNeutral(ts_ChgRate(df['p6_tn4'],12),Equal(df['p6_tn13'],get_LINEARREG_ANGLE(df['p4_ms2'],24)))","390_-1*pn_GroupNeutral(ts_ChgRate(df['p6_tn4'],12),Equal(df['p6_tn13'],get_LINEARREG_ANGLE(df['p4_ms2'],24)))",13.1919,0.0056,1.1474,4.3158,0.5599098406992331,2024-01-01 20:59:00+00:00,2025-01-30 18:59:00+00:00,0.593,0.649,0.4775,-0.131
306,"-1*pn_GroupNeutral(ts_Kurtosis(pn_Stand(df['cmo']),39),ts_Mean(df['p2_et10'],9))","391_-1*pn_GroupNeutral(ts_Kurtosis(pn_Stand(df['cmo']),39),ts_Mean(df['p2_et10'],9))",9.4774,0.002,0.8258,3.3621,0.22373937390161078,2024-01-01 20:59:00+00:00,2025-01-30 18:59:00+00:00,0.435,0.458,0.4871,-0.002
307,"-1*pn_GroupNeutral(Min(get_CCI(df['p3_mf3'],df['ultosc'],df['p1_corrs5'],26),df['p2_et4']),Sign(df['p5_to2']))","393_-1*pn_GroupNeutral(Min(get_CCI(df['p3_mf3'],df['ultosc'],df['p1_corrs5'],26),df['p2_et4']),Sign(df['p5_to2']))",12.7657,0.0056,1.1367,3.3604,0.5704978890511655,2024-01-01 20:59:00+00:00,2025-01-30 18:59:00+00:00,0.596,0.634,0.4846,-0.242
308,"-1*pn_GroupNeutral(get_CCI(df['lislope'],df['p6_tn5'],get_KAMA(df['p1_corrs9'],48),26),df['p1_corrs3'])","396_-1*pn_GroupNeutral(get_CCI(df['lislope'],df['p6_tn5'],get_KAMA(df['p1_corrs9'],48),26),df['p1_corrs3'])",10.3576,0.0056,0.9031,3.1394,0.5593347062351964,2024-01-01 20:59:00+00:00,2025-01-30 18:59:00+00:00,0.299,0.679,0.3057,-0.22
309,"-1*pn_GroupNeutral(get_CCI(df['lislope'],Or(df['p6_tn5'],df['p6_tn4']),get_KAMA(df['p1_corrs9'],48),26),df['p2_et5'])","397_-1*pn_GroupNeutral(get_CCI(df['lislope'],Or(df['p6_tn5'],df['p6_tn4']),get_KAMA(df['p1_corrs9'],48),26),df['p2_et5'])",9.5186,0.0038,0.8301,3.0791,0.40296192757911575,2024-01-01 20:59:00+00:00,2025-01-30 18:59:00+00:00,0.37,0.528,0.412,-0.189
310,"-1*pn_GroupNeutral(get_CCI(df['dcphase'],Power(df['p2_et15'],33),pn_Rank2(df['p6_tn4']),36),Minus(Minus(df['p6_tn8'],26),pn_Winsor(df['p1_corrs9'],6)))","398_-1*pn_GroupNeutral(get_CCI(df['dcphase'],Power(df['p2_et15'],33),pn_Rank2(df['p6_tn4']),36),Minus(Minus(df['p6_tn8'],26),pn_Winsor(df['p1_corrs9'],6)))",10.8078,0.005,0.9366,3.5814,0.5871991829849704,2024-01-01 20:59:00+00:00,2025-01-30 18:59:00+00:00,0.506,0.508,0.499,-0.16
311,"-1*pn_GroupNeutral(get_CCI(df['p6_tn13'],df['p6_tn4'],df['p1_corrs5'],23),get_CCI(df['dcphase'],Xor(df['p4_ms4'],df['p5_to2']),df['lislope'],26))","397_-1*pn_GroupNeutral(get_CCI(df['p6_tn13'],df['p6_tn4'],df['p1_corrs5'],23),get_CCI(df['dcphase'],Xor(df['p4_ms4'],df['p5_to2']),df['lislope'],26))",16.9033,0.0075,1.4754,5.3256,0.5999418026182026,2024-01-01 20:59:00+00:00,2025-01-30 18:59:00+00:00,0.638,0.959,0.3995,-0.114
312,"-1*pn_GroupNeutral(get_CCI(FilterInf(df['p5_to2']),FilterInf(df['p3_mf4']),pn_RankCentered(df['p6_tn7']),26),get_HT_DCPHASE(df['p2_et16']))","398_-1*pn_GroupNeutral(get_CCI(FilterInf(df['p5_to2']),FilterInf(df['p3_mf4']),pn_RankCentered(df['p6_tn7']),26),get_HT_DCPHASE(df['p2_et16']))",12.5921,0.0055,1.102,3.8946,0.5973794570423915,2024-01-01 20:59:00+00:00,2025-01-30 18:59:00+00:00,0.533,0.66,0.4468,-0.297
313,"-1*pn_GroupNeutral(df['dcphase'],ts_Cov(pn_Winsor(df['dcphase'],48),ts_Stdev2(df['cmo'],5),26))","400_-1*pn_GroupNeutral(df['dcphase'],ts_Cov(pn_Winsor(df['dcphase'],48),ts_Stdev2(df['cmo'],5),26))",9.9849,0.0037,0.8666,3.3935,0.41266955092826785,2024-01-01 20:59:00+00:00,2025-01-30 18:59:00+00:00,0.469,0.469,0.5,-0.132
314,"ts_Mean(pn_Rank(ts_MeanChg(df['p6_tn5'],8)),41)","401_ts_Mean(pn_Rank(ts_MeanChg(df['p6_tn5'],8)),41)",14.1103,0.0031,1.2731,3.6694,0.33921164035629386,2024-01-01 20:59:00+00:00,2025-01-30 18:59:00+00:00,0.454,0.924,0.3295,-0.23
315,"SignedPower(df['p5_to5'],ts_Divide(df['p6_tn7'],22))","402_SignedPower(df['p5_to5'],ts_Divide(df['p6_tn7'],22))",11.2318,0.0056,0.9861,3.2706,0.5983425815240663,2024-01-01 20:59:00+00:00,2025-01-30 18:59:00+00:00,0.634,0.433,0.5942,-0.286
316,"-1*ts_Regression(df['p6_tn13'],df['p5_to4'],1,'B')","403_-1*ts_Regression(df['p6_tn13'],df['p5_to4'],1,'B')",10.0576,0.0032,0.8835,3.1915,0.2602566782242835,2024-01-01 20:59:00+00:00,2025-01-30 18:59:00+00:00,0.372,0.584,0.3891,-0.098
317,"-1*ts_MeanChg(ts_Regression(df['p2_et11'],df['liangle'],46,'D'),24)","404_-1*ts_MeanChg(ts_Regression(df['p2_et11'],df['liangle'],46,'D'),24)",9.5096,0.0039,0.8277,3.1093,0.2453090624163321,2024-01-01 20:59:00+00:00,2025-01-30 18:59:00+00:00,0.544,0.352,0.6071,-0.092
318,"ts_MeanChg(FilterInf(ts_TransNorm(df['p1_corrs9'],34)),8)","405_ts_MeanChg(FilterInf(ts_TransNorm(df['p1_corrs9'],34)),8)",12.4258,0.0042,1.1187,3.0877,0.5876819913454874,2024-01-01 20:59:00+00:00,2025-01-30 18:59:00+00:00,0.555,0.655,0.4587,-0.249
319,"-1*pn_GroupNeutral(get_CMO(Sqrt(df['p1_corrs0']),38),ts_Skewness(df['p3_mf5'],24))","407_-1*pn_GroupNeutral(get_CMO(Sqrt(df['p1_corrs0']),38),ts_Skewness(df['p3_mf5'],24))",9.4793,0.0033,0.8202,3.3328,0.556370036806058,2024-01-01 20:59:00+00:00,2025-01-30 18:59:00+00:00,0.365,0.522,0.4115,-0.179
320,"-1*pn_GroupNeutral(pn_Winsor(get_CMO(df['p6_tn5'],49),14),IfThen(ts_Min(df['p3_mf5'],25),5,11))","407_-1*pn_GroupNeutral(pn_Winsor(get_CMO(df['p6_tn5'],49),14),IfThen(ts_Min(df['p3_mf5'],25),5,11))",13.4738,0.0068,1.1908,3.6815,0.5774071579144876,2024-01-01 20:59:00+00:00,2025-01-30 18:59:00+00:00,0.562,0.727,0.436,-0.24
321,"-1*pn_GroupNeutral(IfThen(ts_Scale(df['p5_to5'],12),5,11),ts_ChgRate(ts_Argmin(df['p4_ms2'],20),20))","408_-1*pn_GroupNeutral(IfThen(ts_Scale(df['p5_to5'],12),5,11),ts_ChgRate(ts_Argmin(df['p4_ms2'],20),20))",9.5648,0.0037,0.8224,3.4661,0.2298130078716141,2024-01-01 20:59:00+00:00,2025-01-30 18:59:00+00:00,0.511,0.379,0.5742,-0.059
322,"-1*pn_GroupNeutral(ts_Scale(df['p6_tn5'],36),Multiply(ts_MeanChg(df['p6_tn5'],3),df['p2_et17']))","410_-1*pn_GroupNeutral(ts_Scale(df['p6_tn5'],36),Multiply(ts_MeanChg(df['p6_tn5'],3),df['p2_et17']))",10.2911,0.0044,0.9,3.2201,0.4060249387064004,2024-01-01 20:59:00+00:00,2025-01-30 18:59:00+00:00,0.506,0.468,0.5195,-0.197
323,"pn_GroupNeutral(ts_Delay(df['dx'],41),Or(ts_Rank(df['p2_et19'],41),UnEqual(df['p3_mf6'],df['p1_corrs3'])))","412_pn_GroupNeutral(ts_Delay(df['dx'],41),Or(ts_Rank(df['p2_et19'],41),UnEqual(df['p3_mf6'],df['p1_corrs3'])))",10.2791,0.0034,0.9034,3.2246,0.11810726972195001,2024-01-01 20:59:00+00:00,2025-01-30 18:59:00+00:00,0.491,0.487,0.502,-0.008
324,"-1*pn_GroupNeutral(pn_CrossFit(df['p2_et11'],ts_Max(df['p1_corrs3'],27)),ts_TransNorm(df['p4_ms0'],19))","412_-1*pn_GroupNeutral(pn_CrossFit(df['p2_et11'],ts_Max(df['p1_corrs3'],27)),ts_TransNorm(df['p4_ms0'],19))",9.8716,0.004,0.867,3.0095,0.44539746419014425,2024-01-01 20:59:00+00:00,2025-01-30 18:59:00+00:00,0.407,0.531,0.4339,-0.135
325,"-1*pn_GroupNeutral(Lthan(df['p3_mf12'],df['p6_tn13']),ts_Argmax(df['p1_corrs8'],42))","411_-1*pn_GroupNeutral(Lthan(df['p3_mf12'],df['p6_tn13']),ts_Argmax(df['p1_corrs8'],42))",13.3133,0.0046,1.1713,4.1119,0.5117919571031798,2024-01-01 20:59:00+00:00,2025-01-30 18:59:00+00:00,0.544,0.724,0.429,-0.076
326,"-1*pn_GroupNeutral(IfThen(pn_CrossFit(df['p6_tn12'],df['p3_mf11']),44,14),ts_Stdev2(df['p2_et18'],19))","412_-1*pn_GroupNeutral(IfThen(pn_CrossFit(df['p6_tn12'],df['p3_mf11']),44,14),ts_Stdev2(df['p2_et18'],19))",10.3737,0.0041,0.8936,3.7024,0.3694550663035443,2024-01-01 20:59:00+00:00,2025-01-30 18:59:00+00:00,0.471,0.496,0.4871,-0.101
327,"pn_GroupNeutral(ts_Delta(df['p4_ms2'],26),ts_Stdev(df['p1_corrs9'],50))","413_pn_GroupNeutral(ts_Delta(df['p4_ms2'],26),ts_Stdev(df['p1_corrs9'],50))",9.8278,0.0035,0.8594,3.179,0.5245644736417435,2024-01-01 20:59:00+00:00,2025-01-30 18:59:00+00:00,0.39,0.54,0.4194,-0.135
328,"pn_GroupNeutral(ts_MeanChg(df['p1_corrs9'],2),get_CMO(df['p4_ms5'],9))","414_pn_GroupNeutral(ts_MeanChg(df['p1_corrs9'],2),get_CMO(df['p4_ms5'],9))",9.5983,0.0045,0.8304,3.201,0.5959446789920839,2024-01-01 20:59:00+00:00,2025-01-30 18:59:00+00:00,0.402,0.497,0.4472,-0.172
329,"pn_CrossFit(get_HT_DCPERIOD(df['p2_et12']),pn_GroupNeutral(ts_MeanChg(df['p6_tn7'],32),ts_Decay2(df['p2_et15'],38)))","417_pn_CrossFit(get_HT_DCPERIOD(df['p2_et12']),pn_GroupNeutral(ts_MeanChg(df['p6_tn7'],32),ts_Decay2(df['p2_et15'],38)))",10.7164,0.0042,0.945,3.1625,0.47451181012135146,2024-01-01 20:59:00+00:00,2025-01-30 18:59:00+00:00,0.481,0.542,0.4702,-0.298
330,"pn_CrossFit(Multiply(get_LINEARREG_SLOPE(df['p6_tn13'],13),Reverse(df['p6_tn10'])),pn_GroupNeutral(ts_Decay(df['p4_ms2'],8),df['p5_to2']))","418_pn_CrossFit(Multiply(get_LINEARREG_SLOPE(df['p6_tn13'],13),Reverse(df['p6_tn10'])),pn_GroupNeutral(ts_Decay(df['p4_ms2'],8),df['p5_to2']))",12.0647,0.0046,1.0624,3.6267,0.5480021633792431,2024-01-01 20:59:00+00:00,2025-01-30 18:59:00+00:00,0.531,0.619,0.4617,-0.361
331,"pn_CrossFit(Or(ts_Sum(df['p1_corrs6'],14),df['p6_tn8']),pn_GroupNeutral(get_LINEARREG_ANGLE(df['p2_et8'],14),df['p1_corrs9']))","420_pn_CrossFit(Or(ts_Sum(df['p1_corrs6'],14),df['p6_tn8']),pn_GroupNeutral(get_LINEARREG_ANGLE(df['p2_et8'],14),df['p1_corrs9']))",13.0822,0.0035,1.1663,3.7292,0.3212742249059783,2024-01-01 20:59:00+00:00,2025-01-30 18:59:00+00:00,0.493,0.769,0.3906,0.041
332,"-1*pn_CrossFit(Sign(ts_Kurtosis(df['p6_tn10'],41)),pn_GroupNeutral(df['dx'],df['cmo']))","421_-1*pn_CrossFit(Sign(ts_Kurtosis(df['p6_tn10'],41)),pn_GroupNeutral(df['dx'],df['cmo']))",9.8377,0.0037,0.8616,3.1066,0.5842714418410024,2024-01-01 20:59:00+00:00,2025-01-30 18:59:00+00:00,0.377,0.555,0.4045,-0.066
333,"-1*get_CCI(df['p1_corrs8'],df['p3_mf4'],ts_Median(df['p6_tn13'],42),23)","422_-1*get_CCI(df['p1_corrs8'],df['p3_mf4'],ts_Median(df['p6_tn13'],42),23)",12.7047,0.0054,1.0915,4.5581,0.3432581916162302,2024-01-01 20:59:00+00:00,2025-01-30 18:59:00+00:00,0.445,0.737,0.3765,-0.156
334,"ts_Corr(df['p3_mf3'],ts_CovChg(df['ultosc'],df['dx'],1),33)","423_ts_Corr(df['p3_mf3'],ts_CovChg(df['ultosc'],df['dx'],1),33)",9.4119,0.0036,0.8179,3.1625,0.1635676846225308,2024-01-01 20:59:00+00:00,2025-01-30 18:59:00+00:00,0.471,0.414,0.5322,0.001
335,"get_CCI(df['p3_mf3'],ts_Partial_corr(df['p1_corrs0'],inv(df['p2_et12']),df['p3_mf6'],47),get_LINEARREG_ANGLE(df['p4_ms2'],37),23)","424_get_CCI(df['p3_mf3'],ts_Partial_corr(df['p1_corrs0'],inv(df['p2_et12']),df['p3_mf6'],47),get_LINEARREG_ANGLE(df['p4_ms2'],37),23)",10.4913,0.0031,0.9283,3.1562,0.5050374136378688,2024-01-01 20:59:00+00:00,2025-01-30 18:59:00+00:00,0.436,0.569,0.4338,-0.19
336,"-1*get_CCI(get_MINUS_DM(Max(df['p3_mf10'],df['p2_et12']),pn_Stand(df['p3_mf8']),38),df['adosc'],df['p5_to0'],18)","425_-1*get_CCI(get_MINUS_DM(Max(df['p3_mf10'],df['p2_et12']),pn_Stand(df['p3_mf8']),38),df['adosc'],df['p5_to0'],18)",25.4846,0.0067,2.3771,4.1328,0.5355886742459888,2024-01-01 20:59:00+00:00,2025-01-30 18:59:00+00:00,0.97,1.603,0.377,-0.167
337,"-1*get_CCI(pn_CrossFit(ts_Argmin(df['p4_ms0'],22),Reverse(df['cci'])),df['p5_to0'],ts_Stdev(df['adosc'],49),18)","426_-1*get_CCI(pn_CrossFit(ts_Argmin(df['p4_ms0'],22),Reverse(df['cci'])),df['p5_to0'],ts_Stdev(df['adosc'],49),18)",16.2486,0.0074,1.464,3.7215,0.5736813169702547,2024-01-01 20:59:00+00:00,2025-01-30 18:59:00+00:00,0.665,0.919,0.4198,-0.153
338,"-1*get_CCI(Mthan(ts_Divide(df['p2_et7'],14),df['p6_tn11']),ts_Decay(pn_TransNorm(df['p2_et2']),18),df['p5_to0'],18)","427_-1*get_CCI(Mthan(ts_Divide(df['p2_et7'],14),df['p6_tn11']),ts_Decay(pn_TransNorm(df['p2_et2']),18),df['p5_to0'],18)",19.9797,0.0079,1.8509,3.2197,0.5014098476647219,2024-01-01 20:59:00+00:00,2025-01-30 18:59:00+00:00,0.574,1.429,0.2866,-0.142
339,"-1*get_CCI(get_LINEARREG_ANGLE(df['p5_to6'],30),pn_GroupNorm(ts_Rank(df['p3_mf9'],48),pn_GroupNeutral(df['p2_et17'],df['p6_tn13'])),df['p5_to0'],18)","428_-1*get_CCI(get_LINEARREG_ANGLE(df['p5_to6'],30),pn_GroupNorm(ts_Rank(df['p3_mf9'],48),pn_GroupNeutral(df['p2_et17'],df['p6_tn13'])),df['p5_to0'],18)",19.394,0.0067,1.793,3.3806,0.5044367636019103,2024-01-01 20:59:00+00:00,2025-01-30 18:59:00+00:00,0.946,0.994,0.4876,-0.145
340,"-1*get_CCI(Multiply(df['p6_tn3'],ts_Median(df['p3_mf7'],10)),pn_CrossFit(df['p3_mf5'],df['p6_tn3']),df['p4_ms1'],18)","429_-1*get_CCI(Multiply(df['p6_tn3'],ts_Median(df['p3_mf7'],10)),pn_CrossFit(df['p3_mf5'],df['p6_tn3']),df['p4_ms1'],18)",13.328,0.0037,1.201,3.3915,0.4816323879955396,2024-01-01 20:59:00+00:00,2025-01-30 18:59:00+00:00,0.428,0.871,0.3295,-0.073
341,"-1*get_CCI(df['p5_to2'],pn_Rank2(df['p1_corrs4']),df['p5_to0'],48)","430_-1*get_CCI(df['p5_to2'],pn_Rank2(df['p1_corrs4']),df['p5_to0'],48)",20.4315,0.0077,1.8918,3.3833,0.5537742830173569,2024-01-01 20:59:00+00:00,2025-01-30 18:59:00+00:00,0.517,1.53,0.2526,-0.174
342,"get_CCI(df['p6_tn11'],ts_Max(df['p6_tn0'],6),ts_Mean(df['p6_tn2'],39),18)","431_get_CCI(df['p6_tn11'],ts_Max(df['p6_tn0'],6),ts_Mean(df['p6_tn2'],39),18)",15.0774,0.0047,1.3785,3.1722,0.5620863326059765,2024-01-01 20:59:00+00:00,2025-01-30 18:59:00+00:00,0.325,1.166,0.218,-0.282
343,"-1*get_CCI(get_LINEARREG_ANGLE(df['p2_et1'],23),ts_Max(df['p6_tn3'],18),Min(df['dx'],0.872),18)","433_-1*get_CCI(get_LINEARREG_ANGLE(df['p2_et1'],23),ts_Max(df['p6_tn3'],18),Min(df['dx'],0.872),18)",11.647,0.0043,1.0196,3.7154,0.37777303859768196,2024-01-01 20:59:00+00:00,2025-01-30 18:59:00+00:00,0.46,0.643,0.417,-0.145
344,"-1*get_CCI(pn_TransNorm(ts_Argmax(df['p2_et11'],8)),Multiply(df['p2_et4'],Exp(df['p3_mf7'])),df['p5_to0'],18)","435_-1*get_CCI(pn_TransNorm(ts_Argmax(df['p2_et11'],8)),Multiply(df['p2_et4'],Exp(df['p3_mf7'])),df['p5_to0'],18)",23.6333,0.0069,2.186,4.28,0.5734905901313269,2024-01-01 20:59:00+00:00,2025-01-30 18:59:00+00:00,0.901,1.465,0.3808,-0.152
345,"-1*get_CCI(df['p5_to0'],pn_FillMax(ts_Skewness(df['p6_tn3'],45)),df['p5_to0'],47)","439_-1*get_CCI(df['p5_to0'],pn_FillMax(ts_Skewness(df['p6_tn3'],45)),df['p5_to0'],47)",19.6902,0.0077,1.8304,3.0098,0.5662085002208757,2024-01-01 20:59:00+00:00,2025-01-30 18:59:00+00:00,0.739,1.242,0.373,-0.166
346,"-1*get_CCI(get_LINEARREG_ANGLE(df['p5_to0'],23),Power(inv(df['p3_mf3']),9),df['p5_to0'],18)","440_-1*get_CCI(get_LINEARREG_ANGLE(df['p5_to0'],23),Power(inv(df['p3_mf3']),9),df['p5_to0'],18)",11.8702,0.0042,1.0343,3.9521,0.4370260941520448,2024-01-01 20:59:00+00:00,2025-01-30 18:59:00+00:00,0.586,0.533,0.5237,0.079
347,"-1*get_CCI(df['p5_to0'],ts_Sum(ts_ChgRate(df['p2_et6'],50),10),ts_Stdev2(df['p6_tn13'],32),40)","441_-1*get_CCI(df['p5_to0'],ts_Sum(ts_ChgRate(df['p2_et6'],50),10),ts_Stdev2(df['p6_tn13'],32),40)",13.0721,0.0053,1.1587,3.6659,0.5838833532464173,2024-01-01 20:59:00+00:00,2025-01-30 18:59:00+00:00,0.59,0.664,0.4705,-0.153
348,"-1*get_CCI(df['p3_mf9'],pn_FillMax(ts_Skewness(df['p2_et11'],25)),pn_GroupRank(df['cmo'],df['p5_to6']),18)","442_-1*get_CCI(df['p3_mf9'],pn_FillMax(ts_Skewness(df['p2_et11'],25)),pn_GroupRank(df['cmo'],df['p5_to6']),18)",11.5935,0.0057,1.0213,3.2832,0.5463507221547682,2024-01-01 20:59:00+00:00,2025-01-30 18:59:00+00:00,0.416,0.69,0.3761,-0.405
349,"-1*get_CCI(get_CCI(df['p5_to5'],pn_Winsor(df['p2_et7'],4),df['p3_mf8'],24),ts_CovChg(df['p2_et12'],df['p2_et14'],18),Power(df['p5_to4'],9),18)","443_-1*get_CCI(get_CCI(df['p5_to5'],pn_Winsor(df['p2_et7'],4),df['p3_mf8'],24),ts_CovChg(df['p2_et12'],df['p2_et14'],18),Power(df['p5_to4'],9),18)",13.663,0.0052,1.232,3.2554,0.5411551897035827,2024-01-01 20:59:00+00:00,2025-01-30 18:59:00+00:00,0.613,0.72,0.4599,-0.398
350,"-1*get_CCI(UnEqual(df['p3_mf8'],df['p6_tn9']),And(pn_TransStd(df['p5_to3']),Sign(df['p3_mf4'])),df['p2_et14'],18)","444_-1*get_CCI(UnEqual(df['p3_mf8'],df['p6_tn9']),And(pn_TransStd(df['p5_to3']),Sign(df['p3_mf4'])),df['p2_et14'],18)",9.6111,0.0021,0.8459,3.1336,0.42608526125209234,2024-01-01 20:59:00+00:00,2025-01-30 18:59:00+00:00,0.484,0.431,0.529,0.007
351,"-1*get_CCI(df['p5_to0'],ts_Sum(Xor(df['p1_corrs0'],df['p6_tn0']),10),ts_Argmax(pn_TransStd(df['dcperiod']),27),26)","446_-1*get_CCI(df['p5_to0'],ts_Sum(Xor(df['p1_corrs0'],df['p6_tn0']),10),ts_Argmax(pn_TransStd(df['dcperiod']),27),26)",19.837,0.0073,1.8397,3.2236,0.5771829588759909,2024-01-01 20:59:00+00:00,2025-01-30 18:59:00+00:00,0.594,1.397,0.2983,-0.157
352,"get_CCI(Not(df['p5_to1']),FilterInf(ts_Decay(df['p6_tn0'],48)),ts_Decay(pn_CrossFit(df['p2_et5'],df['p3_mf0']),48),7)","447_get_CCI(Not(df['p5_to1']),FilterInf(ts_Decay(df['p6_tn0'],48)),ts_Decay(pn_CrossFit(df['p2_et5'],df['p3_mf0']),48),7)",23.8528,0.0043,2.2476,3.4817,0.49024438986542423,2024-01-01 20:59:00+00:00,2025-01-30 18:59:00+00:00,0.767,1.666,0.3152,-0.345
353,"-1*ts_Rank(ts_Scale(get_LINEARREG_ANGLE(df['p6_tn10'],22),19),23)","448_-1*ts_Rank(ts_Scale(get_LINEARREG_ANGLE(df['p6_tn10'],22),19),23)",10.5356,0.0042,0.9318,3.0248,0.44713740648755795,2024-01-01 20:59:00+00:00,2025-01-30 18:59:00+00:00,0.393,0.616,0.3895,-0.192
354,"get_CCI(ts_StdevChg(ts_CovChg(df['di'],df['p1_corrs2'],48),8),FilterInf(df['di']),ts_Divide(df['dcphase'],43),31)","452_get_CCI(ts_StdevChg(ts_CovChg(df['di'],df['p1_corrs2'],48),8),FilterInf(df['di']),ts_Divide(df['dcphase'],43),31)",9.8774,0.0038,0.8625,3.1856,0.41094662886340994,2024-01-01 20:59:00+00:00,2025-01-30 18:59:00+00:00,0.26,0.674,0.2784,-0.209
355,"get_CCI(ts_Regression(df['p6_tn9'],df['lislope'],28,'B'),pn_TransStd(df['di']),pn_FillMin(df['lislope']),7)","453_get_CCI(ts_Regression(df['p6_tn9'],df['lislope'],28,'B'),pn_TransStd(df['di']),pn_FillMin(df['lislope']),7)",16.7789,0.0079,1.4905,4.4306,0.5484829292241074,2024-01-01 20:59:00+00:00,2025-01-30 18:59:00+00:00,0.566,1.047,0.3509,-0.286
356,"get_CCI(FilterInf(ts_Regression(df['dm'],df['di'],7,'C')),ts_Median(Reverse(df['p5_to0']),6),df['di'],7)","454_get_CCI(FilterInf(ts_Regression(df['dm'],df['di'],7,'C')),ts_Median(Reverse(df['p5_to0']),6),df['di'],7)",12.0761,0.0039,1.074,3.4289,0.5260007606192136,2024-01-01 20:59:00+00:00,2025-01-30 18:59:00+00:00,0.381,0.781,0.3279,-0.195
357,"get_CCI(pn_Stand(ts_Sum(df['p3_mf9'],25)),And(df['p4_ms2'],df['di']),df['di'],26)","455_get_CCI(pn_Stand(ts_Sum(df['p3_mf9'],25)),And(df['p4_ms2'],df['di']),df['di'],26)",21.6216,0.0061,2.0244,3.2239,0.5280344377016611,2024-01-01 20:59:00+00:00,2025-01-30 18:59:00+00:00,0.676,1.515,0.3085,-0.349
358,"get_CCI(Mthan(ts_Cov2(df['p4_ms2'],df['p1_corrs9'],13),pn_Stand(df['liangle'])),df['di'],Power(df['p3_mf4'],2),31)","456_get_CCI(Mthan(ts_Cov2(df['p4_ms2'],df['p1_corrs9'],13),pn_Stand(df['liangle'])),df['di'],Power(df['p3_mf4'],2),31)",16.0276,0.0041,1.478,3.1346,0.596126655811613,2024-01-01 20:59:00+00:00,2025-01-30 18:59:00+00:00,0.371,1.228,0.232,-0.289
359,"-1*get_CCI(ts_StdevChg(ts_Min(df['p6_tn9'],35),7),get_DX(df['p2_et6'],ts_Kurtosis(df['p2_et3'],20),get_HT_DCPHASE(df['p5_to4']),6),get_DX(df['p3_mf8'],df['p2_et7'],df['p2_et8'],18),32)","458_-1*get_CCI(ts_StdevChg(ts_Min(df['p6_tn9'],35),7),get_DX(df['p2_et6'],ts_Kurtosis(df['p2_et3'],20),get_HT_DCPHASE(df['p5_to4']),6),get_DX(df['p3_mf8'],df['p2_et7'],df['p2_et8'],18),32)",9.8041,0.0045,0.8576,3.0126,0.5990500697827346,2024-01-01 20:59:00+00:00,2025-01-30 18:59:00+00:00,0.39,0.538,0.4203,-0.227
360,"-1*get_CCI(ts_Skewness(df['p2_et3'],45),df['p1_corrs1'],Mthan(df['p4_ms2'],ts_Cov2(df['di'],df['p6_tn8'],6)),22)","459_-1*get_CCI(ts_Skewness(df['p2_et3'],45),df['p1_corrs1'],Mthan(df['p4_ms2'],ts_Cov2(df['di'],df['p6_tn8'],6)),22)",9.6134,0.0044,0.8333,3.1811,0.44063349800960905,2024-01-01 20:59:00+00:00,2025-01-30 18:59:00+00:00,0.263,0.639,0.2916,-0.157
361,"get_CCI(df['p3_mf12'],ts_Min(pn_FillMax(df['cmo']),49),pn_TransStd(df['di']),26)","460_get_CCI(df['p3_mf12'],ts_Min(pn_FillMax(df['cmo']),49),pn_TransStd(df['di']),26)",17.3082,0.0074,1.5617,3.9577,0.5568160387532934,2024-01-01 20:59:00+00:00,2025-01-30 18:59:00+00:00,0.849,0.841,0.5024,-0.405
362,"get_CCI(ts_Min(df['cmo'],33),ts_Max(df['liangle'],26),df['di'],7)","460_get_CCI(ts_Min(df['cmo'],33),ts_Max(df['liangle'],26),df['di'],7)",14.8899,0.005,1.352,3.3555,0.5635466335078476,2024-01-01 20:59:00+00:00,2025-01-30 18:59:00+00:00,0.411,1.052,0.2809,-0.176
363,"-1*get_CCI(get_MINUS_DM(df['cmo'],ts_Stdev2(df['dcphase'],2),16),get_HT_DCPERIOD(df['p5_to1']),df['cmo'],7)","461_-1*get_CCI(get_MINUS_DM(df['cmo'],ts_Stdev2(df['dcphase'],2),16),get_HT_DCPERIOD(df['p5_to1']),df['cmo'],7)",10.7014,0.0035,0.9396,3.3885,0.31652326566941025,2024-01-01 20:59:00+00:00,2025-01-30 18:59:00+00:00,0.287,0.73,0.2822,-0.003
364,"-1*get_CCI(LEthan(ts_Cov(df['p2_et4'],df['p6_tn9'],7),df['p3_mf7']),get_CMO(get_LINEARREG_ANGLE(df['p6_tn10'],41),29),ts_CorrChg(UnEqual(df['p3_mf2'],df['p1_corrs6']),df['p2_et19'],35),7)","462_-1*get_CCI(LEthan(ts_Cov(df['p2_et4'],df['p6_tn9'],7),df['p3_mf7']),get_CMO(get_LINEARREG_ANGLE(df['p6_tn10'],41),29),ts_CorrChg(UnEqual(df['p3_mf2'],df['p1_corrs6']),df['p2_et19'],35),7)",16.9037,0.0062,1.5502,3.2697,0.5324360434884408,2024-01-01 20:59:00+00:00,2025-01-30 18:59:00+00:00,0.432,1.246,0.2574,-0.25
365,"get_CCI(ts_StdevChg(Max(df['p2_et19'],0.099),21),pn_Stand(ts_Median(df['p2_et3'],26)),df['di'],26)","463_get_CCI(ts_StdevChg(Max(df['p2_et19'],0.099),21),pn_Stand(ts_Median(df['p2_et3'],26)),df['di'],26)",21.5619,0.0056,2.0196,3.2633,0.47093540489390306,2024-01-01 20:59:00+00:00,2025-01-30 18:59:00+00:00,0.643,1.543,0.2941,-0.355
366,"get_CCI(inv(df['p2_et19']),df['di'],ts_Decay(ts_Argmin(df['dx'],49),1),7)","464_get_CCI(inv(df['p2_et19']),df['di'],ts_Decay(ts_Argmin(df['dx'],49),1),7)",9.4657,0.0045,0.8189,3.1566,0.5039169265590665,2024-01-01 20:59:00+00:00,2025-01-30 18:59:00+00:00,0.281,0.605,0.3172,-0.105
367,"-1*Max(ts_Cov(ts_Divide(df['dm'],37),df['p5_to4'],37),df['liangle'])","466_-1*Max(ts_Cov(ts_Divide(df['dm'],37),df['p5_to4'],37),df['liangle'])",9.5706,0.004,0.8348,3.0666,0.5840123794992689,2024-01-01 20:59:00+00:00,2025-01-30 18:59:00+00:00,0.516,0.387,0.5714,-0.356
368,"-1*pn_GroupRank(df['liangle'],Reverse(df['adosc']))","466_-1*pn_GroupRank(df['liangle'],Reverse(df['adosc']))",12.5661,0.005,1.1105,3.6375,0.5883444800995603,2024-01-01 20:59:00+00:00,2025-01-30 18:59:00+00:00,0.479,0.723,0.3985,-0.437
369,"-1*SignedPower(pn_GroupNeutral(df['ultosc'],df['p1_corrs6']),get_CCI(df['p6_tn0'],get_LINEARREG_ANGLE(df['p1_corrs6'],46),ts_Product(df['p3_mf3'],46),24))","467_-1*SignedPower(pn_GroupNeutral(df['ultosc'],df['p1_corrs6']),get_CCI(df['p6_tn0'],get_LINEARREG_ANGLE(df['p1_corrs6'],46),ts_Product(df['p3_mf3'],46),24))",9.5905,0.0045,0.8247,3.3613,0.4912571167777165,2024-01-01 20:59:00+00:00,2025-01-30 18:59:00+00:00,0.571,0.321,0.6401,-0.181
370,"-1*SignedPower(pn_GroupNorm(df['liangle'],Max(df['p4_ms6'],0.953)),get_CCI(df['p4_ms6'],FilterInf(df['p5_to4']),get_LINEARREG_ANGLE(df['p4_ms0'],13),24))","468_-1*SignedPower(pn_GroupNorm(df['liangle'],Max(df['p4_ms6'],0.953)),get_CCI(df['p4_ms6'],FilterInf(df['p5_to4']),get_LINEARREG_ANGLE(df['p4_ms0'],13),24))",9.8754,0.0034,0.8483,3.666,0.5132314776070461,2024-01-01 20:59:00+00:00,2025-01-30 18:59:00+00:00,0.311,0.607,0.3388,-0.171
371,"-1*pn_GroupRank(ts_Delta(df['p3_mf11'],25),get_CCI(pn_GroupNeutral(df['ultosc'],df['p1_corrs6']),ts_Argmax(df['p4_ms2'],34),ts_Product(df['ultosc'],46),24))","469_-1*pn_GroupRank(ts_Delta(df['p3_mf11'],25),get_CCI(pn_GroupNeutral(df['ultosc'],df['p1_corrs6']),ts_Argmax(df['p4_ms2'],34),ts_Product(df['ultosc'],46),24))",15.5147,0.007,1.38,4.0909,0.5824961946524484,2024-01-01 20:59:00+00:00,2025-01-30 18:59:00+00:00,0.52,0.974,0.3481,-0.173
372,"-1*SignedPower(pn_GroupNorm(df['liangle'],Max(df['p4_ms6'],0.953)),get_CCI(df['liangle'],FilterInf(df['p1_corrs3']),get_LINEARREG_ANGLE(df['p4_ms0'],13),24))","470_-1*SignedPower(pn_GroupNorm(df['liangle'],Max(df['p4_ms6'],0.953)),get_CCI(df['liangle'],FilterInf(df['p1_corrs3']),get_LINEARREG_ANGLE(df['p4_ms0'],13),24))",10.7153,0.0036,0.9233,3.9095,0.5791555238272201,2024-01-01 20:59:00+00:00,2025-01-30 18:59:00+00:00,0.434,0.565,0.4344,-0.22
373,"SignedPower(ts_Skewness(df['p4_ms6'],24),get_CCI(df['p6_tn0'],And(df['p4_ms5'],df['dcphase']),df['liangle'],24))","471_SignedPower(ts_Skewness(df['p4_ms6'],24),get_CCI(df['p6_tn0'],And(df['p4_ms5'],df['dcphase']),df['liangle'],24))",11.4955,0.0052,1.0031,3.613,0.45398887068027033,2024-01-01 20:59:00+00:00,2025-01-30 18:59:00+00:00,0.537,0.549,0.4945,-0.149
374,"-1*pn_GroupRank(get_CCI(ts_Max(df['p3_mf3'],34),Abs(df['p6_tn5']),ts_MeanChg(df['liangle'],24),46),Sqrt(df['p3_mf3']))","473_-1*pn_GroupRank(get_CCI(ts_Max(df['p3_mf3'],34),Abs(df['p6_tn5']),ts_MeanChg(df['liangle'],24),46),Sqrt(df['p3_mf3']))",11.5412,0.0051,1.0226,3.1827,0.5062720058591906,2024-01-01 20:59:00+00:00,2025-01-30 18:59:00+00:00,0.427,0.68,0.3857,-0.193
375,"-1*pn_GroupRank(get_CCI(df['p2_et14'],ts_Scale(df['p1_corrs0'],26),ts_MeanChg(df['p6_tn0'],24),8),ts_Cov(df['p2_et19'],pn_FillMax(df['p6_tn12']),28))","474_-1*pn_GroupRank(get_CCI(df['p2_et14'],ts_Scale(df['p1_corrs0'],26),ts_MeanChg(df['p6_tn0'],24),8),ts_Cov(df['p2_et19'],pn_FillMax(df['p6_tn12']),28))",10.4796,0.0046,0.9183,3.1971,0.46821236032254426,2024-01-01 20:59:00+00:00,2025-01-30 18:59:00+00:00,0.358,0.636,0.3602,-0.257
376,"-1*pn_TransStd(ts_Cov(ts_Skewness(df['p2_et3'],37),df['p6_tn5'],27))","476_-1*pn_TransStd(ts_Cov(ts_Skewness(df['p2_et3'],37),df['p6_tn5'],27))",11.3674,0.004,0.9817,4.0454,0.13829800045004095,2024-01-01 20:59:00+00:00,2025-01-30 18:59:00+00:00,0.475,0.587,0.4473,0.0
377,"-1*ts_Delta(df['p6_tn1'],29)","473_-1*ts_Delta(df['p6_tn1'],29)",16.4716,0.0048,1.5044,3.5656,0.5648413321567641,2024-01-01 20:59:00+00:00,2025-01-30 18:59:00+00:00,0.38,1.248,0.2334,-0.13
378,"-1*Minus(Min(df['ultosc'],df['p2_et18']),pn_GroupNeutral(df['di'],ts_Rank(df['p2_et11'],5)))","474_-1*Minus(Min(df['ultosc'],df['p2_et18']),pn_GroupNeutral(df['di'],ts_Rank(df['p2_et11'],5)))",12.397,0.0027,1.1149,3.3391,0.443912180675284,2024-01-01 20:59:00+00:00,2025-01-30 18:59:00+00:00,0.386,0.82,0.3201,-0.103
379,"-1*Minus(pn_Winsor(df['p2_et9'],21),Lthan(ts_Skewness(df['p2_et4'],34),ts_TransNorm(df['p3_mf2'],17)))","475_-1*Minus(pn_Winsor(df['p2_et9'],21),Lthan(ts_Skewness(df['p2_et4'],34),ts_TransNorm(df['p3_mf2'],17)))",16.7952,0.0067,1.5378,3.2438,0.5579353352238816,2024-01-01 20:59:00+00:00,2025-01-30 18:59:00+00:00,0.882,0.782,0.53,-0.069
380,"-1*Minus(ts_Cov(ts_Mean(df['p6_tn6'],35),df['cmo'],40),ts_CovChg(Power(df['p2_et5'],13),pn_TransNorm(df['cmo']),36))","476_-1*Minus(ts_Cov(ts_Mean(df['p6_tn6'],35),df['cmo'],40),ts_CovChg(Power(df['p2_et5'],13),pn_TransNorm(df['cmo']),36))",18.2998,0.0045,1.6883,3.5787,0.3246176679993419,2024-01-01 20:59:00+00:00,2025-01-30 18:59:00+00:00,0.454,1.373,0.2485,-0.001
381,"Minus(ts_Delay(df['p2_et9'],45),ts_Skewness(MEthan(df['p2_et15'],df['p5_to4']),33))","477_Minus(ts_Delay(df['p2_et9'],45),ts_Skewness(MEthan(df['p2_et15'],df['p5_to4']),33))",14.4003,0.005,1.2873,3.8265,0.2261863806484452,2024-01-01 20:59:00+00:00,2025-01-30 18:59:00+00:00,0.631,0.762,0.453,-0.0
382,"-1*Minus(ts_Scale(df['p1_corrs1'],6),get_MINUS_DM(ts_Partial_corr(df['p1_corrs4'],df['p2_et3'],df['p6_tn7'],14),df['dcperiod'],45))","477_-1*Minus(ts_Scale(df['p1_corrs1'],6),get_MINUS_DM(ts_Partial_corr(df['p1_corrs4'],df['p2_et3'],df['p6_tn7'],14),df['dcperiod'],45))",10.7768,0.0028,0.9427,3.6369,0.5101559107079258,2024-01-01 20:59:00+00:00,2025-01-30 18:59:00+00:00,0.402,0.618,0.3941,0.005
383,"Minus(get_LINEARREG_ANGLE(get_MINUS_DI(df['p6_tn0'],df['p2_et0'],df['p4_ms5'],48),6),ts_Cov2(Sqrt(df['p1_corrs1']),df['p2_et15'],47))","480_Minus(get_LINEARREG_ANGLE(get_MINUS_DI(df['p6_tn0'],df['p2_et0'],df['p4_ms5'],48),6),ts_Cov2(Sqrt(df['p1_corrs1']),df['p2_et15'],47))",12.9578,0.0035,1.1689,3.2795,0.241516499996582,2024-01-01 20:59:00+00:00,2025-01-30 18:59:00+00:00,0.506,0.759,0.4,-0.014
384,"-1*ts_Regression(FilterInf(df['cci']),Min(df['p2_et7'],Mthan(df['p2_et5'],df['p1_corrs8'])),42,'C')","481_-1*ts_Regression(FilterInf(df['cci']),Min(df['p2_et7'],Mthan(df['p2_et5'],df['p1_corrs8'])),42,'C')",13.3039,0.0057,1.1751,3.8026,0.5894401167951613,2024-01-01 20:59:00+00:00,2025-01-30 18:59:00+00:00,0.789,0.483,0.6203,-0.533
385,"-1*get_CCI(df['p6_tn1'],ts_Entropy(df['p5_to2'],10),pn_FillMin(df['p2_et17']),19)","483_-1*get_CCI(df['p6_tn1'],ts_Entropy(df['p5_to2'],10),pn_FillMin(df['p2_et17']),19)",11.6991,0.0032,1.0291,3.7422,0.41159010004408375,2024-01-01 20:59:00+00:00,2025-01-30 18:59:00+00:00,0.373,0.741,0.3348,-0.067
386,"-1*ts_Regression(get_CCI(pn_RankCentered(df['p3_mf11']),df['p1_corrs6'],df['p4_ms1'],15),ts_Rank(Sqrt(df['p5_to5']),42),19,'C')","484_-1*ts_Regression(get_CCI(pn_RankCentered(df['p3_mf11']),df['p1_corrs6'],df['p4_ms1'],15),ts_Rank(Sqrt(df['p5_to5']),42),19,'C')",11.7855,0.0046,1.0128,4.2764,0.43000519766128215,2024-01-01 20:59:00+00:00,2025-01-30 18:59:00+00:00,0.406,0.691,0.3701,-0.18
387,"-1*ts_Regression(get_CCI(df['cci'],df['p1_corrs6'],ts_Stdev(df['cci'],5),15),pn_Rank(df['p3_mf7']),39,'C')","485_-1*ts_Regression(get_CCI(df['cci'],df['p1_corrs6'],ts_Stdev(df['cci'],5),15),pn_Rank(df['p3_mf7']),39,'C')",9.8417,0.0037,0.8617,3.1177,0.4244660500929316,2024-01-01 20:59:00+00:00,2025-01-30 18:59:00+00:00,0.405,0.527,0.4345,-0.238
388,"-1*get_CCI(df['p6_tn1'],ts_Entropy(get_CMO(df['p6_tn0'],18),10),ts_Quantile(ts_Sum(df['p2_et4'],6),18,'C'),19)","486_-1*get_CCI(df['p6_tn1'],ts_Entropy(get_CMO(df['p6_tn0'],18),10),ts_Quantile(ts_Sum(df['p2_et4'],6),18,'C'),19)",11.277,0.0035,0.9982,3.3568,0.5132576790194961,2024-01-01 20:59:00+00:00,2025-01-30 18:59:00+00:00,0.307,0.773,0.2843,-0.087
389,"-1*get_CCI(get_CMO(Softsign(df['p6_tn13']),42),Add(ts_StdevChg(df['p4_ms3'],1),df['p1_corrs5']),get_HT_DCPERIOD(df['p6_tn9']),19)","487_-1*get_CCI(get_CMO(Softsign(df['p6_tn13']),42),Add(ts_StdevChg(df['p4_ms3'],1),df['p1_corrs5']),get_HT_DCPERIOD(df['p6_tn9']),19)",10.4429,0.0045,0.9144,3.2276,0.5025500233310713,2024-01-01 20:59:00+00:00,2025-01-30 18:59:00+00:00,0.255,0.734,0.2578,-0.109
390,"-1*get_CCI(df['p3_mf11'],LEthan(get_HT_DCPHASE(df['p6_tn0']),df['p2_et10']),Xor(pn_Winsor(df['p6_tn6'],30),df['p2_et1']),19)","488_-1*get_CCI(df['p3_mf11'],LEthan(get_HT_DCPHASE(df['p6_tn0']),df['p2_et10']),Xor(pn_Winsor(df['p6_tn6'],30),df['p2_et1']),19)",14.3786,0.0059,1.301,3.2236,0.4978329061838578,2024-01-01 20:59:00+00:00,2025-01-30 18:59:00+00:00,0.718,0.69,0.5099,-0.141
391,"-1*get_CCI(df['p6_tn1'],ts_Entropy(get_CCI(df['cci'],df['p1_corrs5'],df['p4_ms1'],15),24),df['p3_mf8'],10)","490_-1*get_CCI(df['p6_tn1'],ts_Entropy(get_CCI(df['cci'],df['p1_corrs5'],df['p4_ms1'],15),24),df['p3_mf8'],10)",10.7367,0.0034,0.9373,3.5834,0.5141046980829526,2024-01-01 20:59:00+00:00,2025-01-30 18:59:00+00:00,0.27,0.745,0.266,-0.076
392,"-1*get_CCI(FilterInf(df['cci']),pn_CrossFit(df['p6_tn0'],ts_ChgRate(df['p4_ms5'],33)),Add(df['p3_mf9'],0.236),28)","491_-1*get_CCI(FilterInf(df['cci']),pn_CrossFit(df['p6_tn0'],ts_ChgRate(df['p4_ms5'],33)),Add(df['p3_mf9'],0.236),28)",14.4483,0.0059,1.3077,3.2249,0.5262964316647923,2024-01-01 20:59:00+00:00,2025-01-30 18:59:00+00:00,0.293,1.122,0.2071,-0.273
393,"-1*get_CCI(df['p2_et1'],ts_Delta(df['cci'],44),pn_Cut(get_DX(df['p1_corrs6'],df['p1_corrs8'],df['p3_mf8'],29)),21)","493_-1*get_CCI(df['p2_et1'],ts_Delta(df['cci'],44),pn_Cut(get_DX(df['p1_corrs6'],df['p1_corrs8'],df['p3_mf8'],29)),21)",17.5645,0.0059,1.6009,3.7755,0.5717946870480743,2024-01-01 20:59:00+00:00,2025-01-30 18:59:00+00:00,0.619,1.113,0.3574,-0.265
394,"-1*get_CCI(df['p2_et1'],Minus(ts_Cov(df['p2_et8'],df['p3_mf8'],42),Equal(df['p5_to4'],df['p1_corrs3'])),ts_ChgRate(Sqrt(df['p2_et19']),41),30)","494_-1*get_CCI(df['p2_et1'],Minus(ts_Cov(df['p2_et8'],df['p3_mf8'],42),Equal(df['p5_to4'],df['p1_corrs3'])),ts_ChgRate(Sqrt(df['p2_et19']),41),30)",11.2213,0.0041,0.9793,3.6742,0.5532196055878909,2024-01-01 20:59:00+00:00,2025-01-30 18:59:00+00:00,0.413,0.647,0.3896,-0.107
395,"-1*get_CCI(get_HT_DCPERIOD(ts_MeanChg(df['p1_corrs0'],33)),ts_Argmax(ts_ChgRate(df['p6_tn0'],5),24),get_CMO(df['cci'],23),19)","495_-1*get_CCI(get_HT_DCPERIOD(ts_MeanChg(df['p1_corrs0'],33)),ts_Argmax(ts_ChgRate(df['p6_tn0'],5),24),get_CMO(df['cci'],23),19)",11.2907,0.005,0.9792,3.7493,0.4917679849169662,2024-01-01 20:59:00+00:00,2025-01-30 18:59:00+00:00,0.374,0.686,0.3528,-0.139
396,"-1*get_CCI(Max(df['p2_et3'],0.801),pn_TransNorm(ts_Product(df['p3_mf11'],4)),Max(df['p2_et1'],pn_Stand(df['p1_corrs2'])),10)","497_-1*get_CCI(Max(df['p2_et3'],0.801),pn_TransNorm(ts_Product(df['p3_mf11'],4)),Max(df['p2_et1'],pn_Stand(df['p1_corrs2'])),10)",14.8558,0.0026,1.3664,3.1805,0.43030359619230346,2024-01-01 20:59:00+00:00,2025-01-30 18:59:00+00:00,0.578,0.9,0.3911,-0.068
397,"-1*get_CCI(Mthan(pn_TransNorm(df['p3_mf7']),Divide(df['dcphase'],0.716)),pn_TransNorm(df['p3_mf10']),df['cci'],30)","498_-1*get_CCI(Mthan(pn_TransNorm(df['p3_mf7']),Divide(df['dcphase'],0.716)),pn_TransNorm(df['p3_mf10']),df['cci'],30)",24.6566,0.0072,2.2996,3.8991,0.5803563013311369,2024-01-01 20:59:00+00:00,2025-01-30 18:59:00+00:00,0.664,1.825,0.2668,-0.361
398,"-1*get_CCI(ts_Min(pn_CrossFit(df['dx'],df['p1_corrs0']),3),df['cci'],pn_GroupRank(df['p2_et6'],df['p1_corrs0']),35)","499_-1*get_CCI(ts_Min(pn_CrossFit(df['dx'],df['p1_corrs0']),3),df['cci'],pn_GroupRank(df['p2_et6'],df['p1_corrs0']),35)",22.5852,0.0073,2.1048,3.5217,0.48235605936681103,2024-01-01 20:59:00+00:00,2025-01-30 18:59:00+00:00,0.9,1.377,0.3953,-0.376
399,"-1*get_CCI(df['cci'],pn_FillMax(ts_Cov2(df['p4_ms1'],df['p3_mf7'],30)),ts_CovChg(df['p4_ms5'],df['p3_mf10'],49),19)","500_-1*get_CCI(df['cci'],pn_FillMax(ts_Cov2(df['p4_ms1'],df['p3_mf7'],30)),ts_CovChg(df['p4_ms5'],df['p3_mf10'],49),19)",21.8318,0.0062,2.0418,3.314,0.41655790126234404,2024-01-01 20:59:00+00:00,2025-01-30 18:59:00+00:00,0.981,1.229,0.4439,-0.257
400,"-1*get_CCI(ts_Mean(df['dx'],49),ts_Scale(Power(df['p4_ms4'],23),38),Exp(ts_Decay2(df['cci'],19)),19)","501_-1*get_CCI(ts_Mean(df['dx'],49),ts_Scale(Power(df['p4_ms4'],23),38),Exp(ts_Decay2(df['cci'],19)),19)",18.3547,0.0037,1.695,3.658,0.46870207276589226,2024-01-01 20:59:00+00:00,2025-01-30 18:59:00+00:00,0.525,1.309,0.2863,-0.165
401,"-1*get_CCI(pn_Rank(UnEqual(df['p6_tn6'],df['p1_corrs8'])),ts_Median(df['dcphase'],24),df['p2_et5'],15)","502_-1*get_CCI(pn_Rank(UnEqual(df['p6_tn6'],df['p1_corrs8'])),ts_Median(df['dcphase'],24),df['p2_et5'],15)",16.0048,0.0035,1.47,3.3879,0.4906893862239808,2024-01-01 20:59:00+00:00,2025-01-30 18:59:00+00:00,0.783,0.808,0.4921,-0.173
402,"-1*get_CCI(ts_Decay(ts_Rank(df['p1_corrs5'],36),47),Divide(df['p6_tn1'],0.67),ts_Decay(ts_Rank(df['p1_corrs5'],13),47),19)","503_-1*get_CCI(ts_Decay(ts_Rank(df['p1_corrs5'],36),47),Divide(df['p6_tn1'],0.67),ts_Decay(ts_Rank(df['p1_corrs5'],13),47),19)",11.506,0.0037,1.0304,3.0567,0.3910778118637506,2024-01-01 20:59:00+00:00,2025-01-30 18:59:00+00:00,0.309,0.806,0.2771,-0.078
403,"Exp(Multiply(df['p2_et7'],pn_TransNorm(df['p6_tn11'])))","504_Exp(Multiply(df['p2_et7'],pn_TransNorm(df['p6_tn11'])))",12.4631,0.0048,1.104,3.5493,0.24795758226250117,2024-01-01 20:59:00+00:00,2025-01-30 18:59:00+00:00,0.709,0.486,0.5933,-0.164
404,"-1*Min(ts_Scale(Add(df['cmo'],df['p5_to2']),47),ts_Corr(df['p1_corrs2'],df['p2_et9'],39))","505_-1*Min(ts_Scale(Add(df['cmo'],df['p5_to2']),47),ts_Corr(df['p1_corrs2'],df['p2_et9'],39))",12.1276,0.0053,1.0618,3.7328,0.416911604226539,2024-01-01 20:59:00+00:00,2025-01-30 18:59:00+00:00,0.38,0.769,0.3307,-0.22
405,"-1*Min(get_LINEARREG_ANGLE(inv(df['p5_to1']),23),df['p2_et7'])","507_-1*Min(get_LINEARREG_ANGLE(inv(df['p5_to1']),23),df['p2_et7'])",11.4306,0.005,0.9974,3.6267,0.48766565791008665,2024-01-01 20:59:00+00:00,2025-01-30 18:59:00+00:00,0.382,0.697,0.354,-0.313
406,"-1*Min(get_LINEARREG_ANGLE(df['ultosc'],23),df['p2_et7'])","508_-1*Min(get_LINEARREG_ANGLE(df['ultosc'],23),df['p2_et7'])",15.4426,0.0047,1.4054,3.4686,0.5376213006795441,2024-01-01 20:59:00+00:00,2025-01-30 18:59:00+00:00,0.335,1.186,0.2202,-0.469
407,"-1*Min(FilterInf(get_LINEARREG_ANGLE(df['dcphase'],15)),df['p1_corrs0'])","508_-1*Min(FilterInf(get_LINEARREG_ANGLE(df['dcphase'],15)),df['p1_corrs0'])",21.7995,0.0063,2.0082,4.2006,0.5923657469456949,2024-01-01 20:59:00+00:00,2025-01-30 18:59:00+00:00,0.652,1.521,0.3,-0.283
408,"-1*Min(ts_TransNorm(ts_Kurtosis(df['p2_et8'],45),42),ts_Stdev2(ts_Cov(df['p4_ms5'],df['p1_corrs3'],31),26))","509_-1*Min(ts_TransNorm(ts_Kurtosis(df['p2_et8'],45),42),ts_Stdev2(ts_Cov(df['p4_ms5'],df['p1_corrs3'],31),26))",10.3493,0.0043,0.9127,3.0246,0.34776866550270713,2024-01-01 20:59:00+00:00,2025-01-30 18:59:00+00:00,0.508,0.48,0.5142,-0.012
409,"-1*Min(Abs(df['p1_corrs8']),get_KAMA(df['cci'],6))","510_-1*Min(Abs(df['p1_corrs8']),get_KAMA(df['cci'],6))",14.249,0.0048,1.2901,3.3179,0.5140041538486085,2024-01-01 20:59:00+00:00,2025-01-30 18:59:00+00:00,0.378,1.018,0.2708,-0.322
410,"-1*Add(ts_Cov(df['p1_corrs2'],ts_Sum(df['p2_et12'],5),40),Equal(pn_Stand(df['p5_to7']),get_HT_DCPHASE(df['p6_tn0'])))","512_-1*Add(ts_Cov(df['p1_corrs2'],ts_Sum(df['p2_et12'],5),40),Equal(pn_Stand(df['p5_to7']),get_HT_DCPHASE(df['p6_tn0'])))",10.3728,0.003,0.9205,3.0604,0.3994615486702233,2024-01-01 20:59:00+00:00,2025-01-30 18:59:00+00:00,0.558,0.438,0.5602,-0.012
411,"pn_Rank(pn_GroupNorm(LEthan(df['p1_corrs2'],df['p6_tn6']),ts_Scale(df['p2_et12'],34)))","513_pn_Rank(pn_GroupNorm(LEthan(df['p1_corrs2'],df['p6_tn6']),ts_Scale(df['p2_et12'],34)))",10.5984,0.0036,0.9196,3.6728,0.30799216278639857,2024-01-01 20:59:00+00:00,2025-01-30 18:59:00+00:00,0.354,0.641,0.3558,-0.059
412,"ts_Corr2(pn_GroupNorm(Softsign(df['p6_tn11']),Minus(df['p3_mf3'],df['p3_mf12'])),df['p2_et13'],46)","514_ts_Corr2(pn_GroupNorm(Softsign(df['p6_tn11']),Minus(df['p3_mf3'],df['p3_mf12'])),df['p2_et13'],46)",10.642,0.0028,0.9261,3.7179,0.26854423622716755,2024-01-01 20:59:00+00:00,2025-01-30 18:59:00+00:00,0.438,0.564,0.4371,-0.027
413,"-1*Minus(Mthan(df['p1_corrs0'],df['p1_corrs8']),ts_Entropy(Softsign(df['p2_et1']),23))","515_-1*Minus(Mthan(df['p1_corrs0'],df['p1_corrs8']),ts_Entropy(Softsign(df['p2_et1']),23))",10.1885,0.0039,0.897,3.0707,0.48375505197380303,2024-01-01 20:59:00+00:00,2025-01-30 18:59:00+00:00,0.23,0.741,0.2369,-0.114
414,"pn_TransStd(ts_Rank(ts_MeanChg(df['p6_tn3'],16),7))","516_pn_TransStd(ts_Rank(ts_MeanChg(df['p6_tn3'],16),7))",16.0409,0.0042,1.4437,4.1778,0.560411772700064,2024-01-01 20:59:00+00:00,2025-01-30 18:59:00+00:00,0.54,1.022,0.3457,-0.123
415,"Minus(FilterInf(df['p1_corrs7']),ts_Decay(ts_Scale(df['cmo'],5),27))","517_Minus(FilterInf(df['p1_corrs7']),ts_Decay(ts_Scale(df['cmo'],5),27))",11.0714,0.0045,0.9718,3.3798,0.4667156874507668,2024-01-01 20:59:00+00:00,2025-01-30 18:59:00+00:00,0.486,0.566,0.462,-0.34
416,"-1*Minus(df['cmo'],ts_Cov2(ts_Kurtosis(df['p3_mf8'],43),ts_Sum(df['p4_ms2'],44),40))","518_-1*Minus(df['cmo'],ts_Cov2(ts_Kurtosis(df['p3_mf8'],43),ts_Sum(df['p4_ms2'],44),40))",13.0495,0.005,1.1748,3.1608,0.5847235769945146,2024-01-01 20:59:00+00:00,2025-01-30 18:59:00+00:00,0.536,0.736,0.4214,-0.278
417,"Minus(ts_Divide(df['p4_ms2'],19),pn_GroupRank(df['p6_tn13'],df['p2_et2']))","519_Minus(ts_Divide(df['p4_ms2'],19),pn_GroupRank(df['p6_tn13'],df['p2_et2']))",9.947,0.0043,0.8698,3.1029,0.5308197635121593,2024-01-01 20:59:00+00:00,2025-01-30 18:59:00+00:00,0.234,0.707,0.2487,-0.159
418,"-1*Minus(ts_Min(pn_Rank(df['p6_tn13']),3),ts_Regression(ts_Max(df['p2_et14'],44),ts_TransNorm(df['cci'],36),41,'C'))","520_-1*Minus(ts_Min(pn_Rank(df['p6_tn13']),3),ts_Regression(ts_Max(df['p2_et14'],44),ts_TransNorm(df['cci'],36),41,'C'))",10.3127,0.0042,0.9007,3.2937,0.3615936802426155,2024-01-01 20:59:00+00:00,2025-01-30 18:59:00+00:00,0.415,0.56,0.4256,-0.166
419,"-1*Minus(Multiply(ts_Scale(df['p6_tn1'],31),Sqrt(df['p2_et14'])),UnEqual(Max(df['p2_et3'],41),df['p6_tn13']))","522_-1*Minus(Multiply(ts_Scale(df['p6_tn1'],31),Sqrt(df['p2_et14'])),UnEqual(Max(df['p2_et3'],41),df['p6_tn13']))",11.7308,0.0047,1.0417,3.2368,0.4903052290418869,2024-01-01 20:59:00+00:00,2025-01-30 18:59:00+00:00,0.539,0.588,0.4783,-0.148
420,"-1*Minus(UnEqual(get_KAMA(df['p2_et16'],9),Multiply(df['p6_tn1'],df['p2_et3'])),Multiply(ts_Cov(df['p1_corrs2'],df['p1_corrs0'],42),ts_Regression(df['p4_ms2'],df['cmo'],5,'C')))","524_-1*Minus(UnEqual(get_KAMA(df['p2_et16'],9),Multiply(df['p6_tn1'],df['p2_et3'])),Multiply(ts_Cov(df['p1_corrs2'],df['p1_corrs0'],42),ts_Regression(df['p4_ms2'],df['cmo'],5,'C')))",13.3621,0.0055,1.1859,3.6879,0.5358348044635097,2024-01-01 20:59:00+00:00,2025-01-30 18:59:00+00:00,0.449,0.834,0.35,-0.267
421,"Minus(ts_Regression(df['p4_ms2'],df['p1_corrs9'],5,'C'),UnEqual(df['cmo'],df['p2_et3']))","525_Minus(ts_Regression(df['p4_ms2'],df['p1_corrs9'],5,'C'),UnEqual(df['cmo'],df['p2_et3']))",14.5581,0.0064,1.2709,4.5901,0.5618331503376801,2024-01-01 20:59:00+00:00,2025-01-30 18:59:00+00:00,0.524,0.851,0.3811,-0.284
422,"Minus(Multiply(ts_Argmax(df['p2_et3'],20),ts_Regression(df['p4_ms2'],df['cci'],0.378,'C')),Min(df['liangle'],df['p4_ms3']))","526_Minus(Multiply(ts_Argmax(df['p2_et3'],20),ts_Regression(df['p4_ms2'],df['cci'],0.378,'C')),Min(df['liangle'],df['p4_ms3']))",11.0897,0.0051,0.9681,3.4593,0.5632257645623537,2024-01-01 20:59:00+00:00,2025-01-30 18:59:00+00:00,0.427,0.621,0.4074,-0.231
423,"-1*Minus(ts_ChgRate(ts_Scale(df['p6_tn7'],31),44),UnEqual(ts_Entropy(df['p2_et18'],42),Equal(df['p3_mf0'],df['cci'])))","527_-1*Minus(ts_ChgRate(ts_Scale(df['p6_tn7'],31),44),UnEqual(ts_Entropy(df['p2_et18'],42),Equal(df['p3_mf0'],df['cci'])))",11.0934,0.0053,0.9716,3.3405,0.58308020229053,2024-01-01 20:59:00+00:00,2025-01-30 18:59:00+00:00,0.604,0.447,0.5747,-0.254
424,"-1*Minus(ts_ChgRate(ts_CorrChg(df['dx'],df['p6_tn12'],46),44),ts_Regression(df['p4_ms2'],df['cmo'],5,'C'))","528_-1*Minus(ts_ChgRate(ts_CorrChg(df['dx'],df['p6_tn12'],46),44),ts_Regression(df['p4_ms2'],df['cmo'],5,'C'))",11.2824,0.0046,0.9778,3.8196,0.5605775880901781,2024-01-01 20:59:00+00:00,2025-01-30 18:59:00+00:00,0.48,0.578,0.4537,-0.207
425,"-1*ts_Divide(ts_Scale(df['p3_mf11'],49),50)","529_-1*ts_Divide(ts_Scale(df['p3_mf11'],49),50)",10.6718,0.0049,0.9253,3.5286,0.4608835032230715,2024-01-01 20:59:00+00:00,2025-01-30 18:59:00+00:00,0.653,0.349,0.6517,-0.097
426,"-1*Divide(Add(ts_TransNorm(df['p3_mf11'],16),df['p6_tn7']),Sqrt(df['p2_et10']))","530_-1*Divide(Add(ts_TransNorm(df['p3_mf11'],16),df['p6_tn7']),Sqrt(df['p2_et10']))",9.3442,0.0033,0.8095,3.2476,0.37823867811493217,2024-01-01 20:59:00+00:00,2025-01-30 18:59:00+00:00,0.627,0.249,0.7158,-0.139
427,"-1*ts_Divide(Min(df['p6_tn7'],ts_Stdev(df['p6_tn13'],38)),9)","530_-1*ts_Divide(Min(df['p6_tn7'],ts_Stdev(df['p6_tn13'],38)),9)",17.7587,0.0079,1.564,5.1641,0.5621092575839097,2024-01-01 20:59:00+00:00,2025-01-30 18:59:00+00:00,0.83,0.862,0.4905,-0.283
428,"-1*get_CCI(pn_Winsor(df['p3_mf11'],50),ts_StdevChg(ts_Product(df['p6_tn0'],50),44),pn_FillMax(pn_Rank2(df['di'])),11)","532_-1*get_CCI(pn_Winsor(df['p3_mf11'],50),ts_StdevChg(ts_Product(df['p6_tn0'],50),44),pn_FillMax(pn_Rank2(df['di'])),11)",12.1925,0.0042,1.0881,3.3044,0.5972013489361045,2024-01-01 20:59:00+00:00,2025-01-30 18:59:00+00:00,0.445,0.733,0.3778,-0.097
429,"ts_Divide(ts_Decay2(df['p6_tn13'],18),25)","533_ts_Divide(ts_Decay2(df['p6_tn13'],18),25)",11.2877,0.0031,0.9779,4.0574,0.1370623613391276,2024-01-01 20:59:00+00:00,2025-01-30 18:59:00+00:00,0.425,0.633,0.4017,-0.039
430,"-1*Divide(Add(ts_TransNorm(df['p3_mf11'],2),df['p6_tn7']),ts_Scale(get_LINEARREG_ANGLE(df['p2_et6'],12),50))","534_-1*Divide(Add(ts_TransNorm(df['p3_mf11'],2),df['p6_tn7']),ts_Scale(get_LINEARREG_ANGLE(df['p2_et6'],12),50))",9.6587,0.0043,0.8395,3.1381,0.5510638122063257,2024-01-01 20:59:00+00:00,2025-01-30 18:59:00+00:00,0.56,0.348,0.6167,-0.186
431,"-1*Divide(Add(ts_TransNorm(df['p3_mf11'],38),df['p6_tn7']),df['p5_to6'])","535_-1*Divide(Add(ts_TransNorm(df['p3_mf11'],38),df['p6_tn7']),df['p5_to6'])",11.2139,0.0048,0.9866,3.3187,0.5943716622635431,2024-01-01 20:59:00+00:00,2025-01-30 18:59:00+00:00,0.649,0.419,0.6077,-0.202
432,"-1*get_CCI(pn_TransNorm(pn_CrossFit(df['p3_mf0'],df['p6_tn3'])),ts_StdevChg(ts_Product(df['p6_tn0'],50),44),Sqrt(df['cmo']),11)","536_-1*get_CCI(pn_TransNorm(pn_CrossFit(df['p3_mf0'],df['p6_tn3'])),ts_StdevChg(ts_Product(df['p6_tn0'],50),44),Sqrt(df['cmo']),11)",11.7861,0.0048,1.045,3.2951,0.40399771303515747,2024-01-01 20:59:00+00:00,2025-01-30 18:59:00+00:00,0.337,0.794,0.298,-0.161
433,"-1*Min(pn_GroupNorm(pn_TransStd(df['p2_et9']),pn_TransStd(df['p2_et9'])),ts_Argmin(Multiply(df['p2_et7'],0.473),1))","537_-1*Min(pn_GroupNorm(pn_TransStd(df['p2_et9']),pn_TransStd(df['p2_et9'])),ts_Argmin(Multiply(df['p2_et7'],0.473),1))",10.2886,0.0042,0.8849,3.6857,0.4734037579326654,2024-01-01 20:59:00+00:00,2025-01-30 18:59:00+00:00,0.501,0.457,0.523,-0.099
434,"-1*get_CCI(Add(ts_TransNorm(df['p3_mf11'],38),Multiply(df['p6_tn13'],0.473)),get_CMO(ts_Max(df['p2_et19'],14),34),pn_FillMax(pn_Cut(df['p3_mf11'])),35)","537_-1*get_CCI(Add(ts_TransNorm(df['p3_mf11'],38),Multiply(df['p6_tn13'],0.473)),get_CMO(ts_Max(df['p2_et19'],14),34),pn_FillMax(pn_Cut(df['p3_mf11'])),35)",17.2464,0.0073,1.5675,3.6182,0.5655738966620304,2024-01-01 20:59:00+00:00,2025-01-30 18:59:00+00:00,0.614,1.083,0.3618,-0.188
435,"-1*get_CCI(ts_Entropy(df['p2_et3'],16),get_CMO(df['cmo'],34),pn_Stand(ts_Decay2(df['p3_mf1'],31)),26)","538_-1*get_CCI(ts_Entropy(df['p2_et3'],16),get_CMO(df['cmo'],34),pn_Stand(ts_Decay2(df['p3_mf1'],31)),26)",23.1994,0.0068,2.1711,3.4425,0.4787321181993979,2024-01-01 20:59:00+00:00,2025-01-30 18:59:00+00:00,0.735,1.615,0.3128,-0.397
436,"-1*Min(ts_CorrChg(df['p3_mf0'],df['p6_tn7'],41),Multiply(df['p6_tn11'],pn_RankCentered(df['p2_et7'])))","539_-1*Min(ts_CorrChg(df['p3_mf0'],df['p6_tn7'],41),Multiply(df['p6_tn11'],pn_RankCentered(df['p2_et7'])))",12.3372,0.0043,1.0891,3.6894,0.5943150084277584,2024-01-01 20:59:00+00:00,2025-01-30 18:59:00+00:00,0.693,0.486,0.5878,-0.508
437,"-1*Divide(Add(ts_TransNorm(df['p3_mf11'],17),ts_Sum(df['p6_tn7'],7)),get_MINUS_DM(pn_FillMin(df['p2_et4']),df['p1_corrs8'],16))","540_-1*Divide(Add(ts_TransNorm(df['p3_mf11'],17),ts_Sum(df['p6_tn7'],7)),get_MINUS_DM(pn_FillMin(df['p2_et4']),df['p1_corrs8'],16))",14.8258,0.006,1.313,4.1884,0.4911901925808218,2024-01-01 20:59:00+00:00,2025-01-30 18:59:00+00:00,0.666,0.755,0.4687,-0.343
438,"-1*Divide(Add(ts_TransNorm(df['p3_mf11'],12),ts_Mean(df['p3_mf3'],24)),pn_FillMax(get_DX(df['adosc'],df['p2_et14'],df['p1_corrs3'],47)))","541_-1*Divide(Add(ts_TransNorm(df['p3_mf11'],12),ts_Mean(df['p3_mf3'],24)),pn_FillMax(get_DX(df['adosc'],df['p2_et14'],df['p1_corrs3'],47)))",12.1436,0.0026,1.0807,3.6275,0.5476172946819284,2024-01-01 20:59:00+00:00,2025-01-30 18:59:00+00:00,0.926,0.243,0.7921,-0.071
439,"-1*get_CCI(ts_Entropy(df['p2_et3'],16),Sqrt(df['p2_et7']),Exp(ts_Median(df['p6_tn1'],35)),35)","542_-1*get_CCI(ts_Entropy(df['p2_et3'],16),Sqrt(df['p2_et7']),Exp(ts_Median(df['p6_tn1'],35)),35)",9.9863,0.0037,0.8726,3.2254,0.40608504341079626,2024-01-01 20:59:00+00:00,2025-01-30 18:59:00+00:00,0.463,0.482,0.4899,-0.246
440,"-1*Divide(Add(ts_TransNorm(df['p3_mf11'],38),Abs(df['p6_tn7'])),Or(Softsign(df['p5_to2']),Reverse(df['p2_et18'])))","543_-1*Divide(Add(ts_TransNorm(df['p3_mf11'],38),Abs(df['p6_tn7'])),Or(Softsign(df['p5_to2']),Reverse(df['p2_et18'])))",10.6804,0.0044,0.9193,3.8018,0.5960239118072383,2024-01-01 20:59:00+00:00,2025-01-30 18:59:00+00:00,0.733,0.262,0.7367,-0.142
441,"-1*Min(ts_CorrChg(df['p3_mf0'],Abs(df['p2_et7']),5),Add(ts_TransNorm(df['p3_mf11'],17),ts_Delta(df['p3_mf4'],35)))","544_-1*Min(ts_CorrChg(df['p3_mf0'],Abs(df['p2_et7']),5),Add(ts_TransNorm(df['p3_mf11'],17),ts_Delta(df['p3_mf4'],35)))",10.0222,0.0041,0.8564,3.763,0.4001341304557792,2024-01-01 20:59:00+00:00,2025-01-30 18:59:00+00:00,0.469,0.458,0.5059,-0.089
442,"-1*get_CCI(ts_TransNorm(df['p3_mf11'],38),Sqrt(get_CMO(df['p1_corrs4'],11)),Sqrt(df['cmo']),40)","545_-1*get_CCI(ts_TransNorm(df['p3_mf11'],38),Sqrt(get_CMO(df['p1_corrs4'],11)),Sqrt(df['cmo']),40)",12.5833,0.0045,1.1287,3.2212,0.5581563237495188,2024-01-01 20:59:00+00:00,2025-01-30 18:59:00+00:00,0.245,0.976,0.2007,-0.3
443,"-1*Divide(get_CMO(ts_TransNorm(df['p3_mf11'],17),31),Or(Multiply(df['p6_tn13'],0.473),ts_Stdev(df['p6_tn1'],49)))","546_-1*Divide(get_CMO(ts_TransNorm(df['p3_mf11'],17),31),Or(Multiply(df['p6_tn13'],0.473),ts_Stdev(df['p6_tn1'],49)))",11.9477,0.0059,1.0621,3.1004,0.5547641188262725,2024-01-01 20:59:00+00:00,2025-01-30 18:59:00+00:00,0.459,0.691,0.3991,-0.179
444,"-1*Divide(Add(ts_TransNorm(df['p3_mf11'],17),get_CMO(df['p5_to5'],22)),Divide(Add(df['ultosc'],df['ultosc']),df['p2_et12']))","547_-1*Divide(Add(ts_TransNorm(df['p3_mf11'],17),get_CMO(df['p5_to5'],22)),Divide(Add(df['ultosc'],df['ultosc']),df['p2_et12']))",10.279,0.0032,0.9095,3.0738,0.5071512143175495,2024-01-01 20:59:00+00:00,2025-01-30 18:59:00+00:00,0.444,0.541,0.4508,-0.073
445,"-1*get_CCI(ts_Entropy(ts_TransNorm(df['p3_mf11'],0.302),16),pn_GroupNorm(get_CMO(df['p6_tn13'],41),df['p2_et4']),pn_GroupNorm(df['p2_et4'],df['p3_mf11']),34)","548_-1*get_CCI(ts_Entropy(ts_TransNorm(df['p3_mf11'],0.302),16),pn_GroupNorm(get_CMO(df['p6_tn13'],41),df['p2_et4']),pn_GroupNorm(df['p2_et4'],df['p3_mf11']),34)",13.118,0.0065,1.1682,3.3363,0.48816658353844694,2024-01-01 20:59:00+00:00,2025-01-30 18:59:00+00:00,0.371,0.893,0.2935,-0.153
446,"-1*get_CCI(pn_GroupNorm(get_CMO(df['p6_tn13'],41),ts_Mean(df['p3_mf9'],43)),get_CMO(df['p6_tn13'],50),pn_GroupNorm(pn_TransStd(df['p2_et17']),Not(df['p3_mf12'])),35)","547_-1*get_CCI(pn_GroupNorm(get_CMO(df['p6_tn13'],41),ts_Mean(df['p3_mf9'],43)),get_CMO(df['p6_tn13'],50),pn_GroupNorm(pn_TransStd(df['p2_et17']),Not(df['p3_mf12'])),35)",22.0976,0.0075,2.0313,4.2227,0.5810960426438375,2024-01-01 20:59:00+00:00,2025-01-30 18:59:00+00:00,0.826,1.372,0.3758,-0.178
447,"-1*get_CCI(ts_StdevChg(df['p3_mf7'],47),pn_GroupNorm(get_CMO(df['p6_tn13'],41),df['p6_tn2']),pn_GroupNorm(ts_Cov(df['p1_corrs8'],df['p2_et12'],32),get_MINUS_DI(df['p3_mf3'],df['p6_tn0'],df['p5_to2'],28)),34)","548_-1*get_CCI(ts_StdevChg(df['p3_mf7'],47),pn_GroupNorm(get_CMO(df['p6_tn13'],41),df['p6_tn2']),pn_GroupNorm(ts_Cov(df['p1_corrs8'],df['p2_et12'],32),get_MINUS_DI(df['p3_mf3'],df['p6_tn0'],df['p5_to2'],28)),34)",14.6026,0.0057,1.3149,3.5104,0.439402857956037,2024-01-01 20:59:00+00:00,2025-01-30 18:59:00+00:00,0.621,0.802,0.4364,-0.172
448,"-1*get_CCI(Lthan(get_CMO(df['p6_tn13'],27),ts_Kurtosis(df['p3_mf0'],5)),pn_GroupNorm(get_CMO(df['p6_tn13'],41),df['p6_tn2']),pn_GroupNorm(pn_TransStd(df['p3_mf11']),pn_RankCentered(df['p3_mf12'])),34)","549_-1*get_CCI(Lthan(get_CMO(df['p6_tn13'],27),ts_Kurtosis(df['p3_mf0'],5)),pn_GroupNorm(get_CMO(df['p6_tn13'],41),df['p6_tn2']),pn_GroupNorm(pn_TransStd(df['p3_mf11']),pn_RankCentered(df['p3_mf12'])),34)",12.5846,0.0056,1.1283,3.0654,0.542486909040202,2024-01-01 20:59:00+00:00,2025-01-30 18:59:00+00:00,0.476,0.745,0.3898,-0.176
449,"-1*get_CCI(ts_Stdev(df['p5_to6'],9),Reverse(df['p4_ms1']),Add(ts_TransNorm(df['p3_mf11'],0.302),df['p6_tn9']),35)","549_-1*get_CCI(ts_Stdev(df['p5_to6'],9),Reverse(df['p4_ms1']),Add(ts_TransNorm(df['p3_mf11'],0.302),df['p6_tn9']),35)",14.115,0.0052,1.2656,3.5959,0.5853260384457274,2024-01-01 20:59:00+00:00,2025-01-30 18:59:00+00:00,0.515,0.854,0.3762,-0.166
450,"-1*get_CCI(ts_Product(ts_MeanChg(df['cmo'],17),17),Minus(df['p5_to0'],0.759),Add(ts_TransNorm(df['p3_mf11'],0.302),df['p6_tn9']),20)","550_-1*get_CCI(ts_Product(ts_MeanChg(df['cmo'],17),17),Minus(df['p5_to0'],0.759),Add(ts_TransNorm(df['p3_mf11'],0.302),df['p6_tn9']),20)",16.7901,0.0062,1.5332,3.4421,0.5833442942157534,2024-01-01 20:59:00+00:00,2025-01-30 18:59:00+00:00,0.513,1.146,0.3092,-0.088
451,"-1*get_CCI(ts_TransNorm(df['p3_mf11'],38),ts_Kurtosis(df['p2_et4'],47),ts_TransNorm(get_CMO(df['p3_mf11'],41),41),35)","551_-1*get_CCI(ts_TransNorm(df['p3_mf11'],38),ts_Kurtosis(df['p2_et4'],47),ts_TransNorm(get_CMO(df['p3_mf11'],41),41),35)",10.0908,0.004,0.8802,3.2621,0.4692050542170493,2024-01-01 20:59:00+00:00,2025-01-30 18:59:00+00:00,0.655,0.298,0.6873,-0.094
452,"-1*get_CCI(ts_Regression(df['p6_tn4'],df['cmo'],7,'B'),Add(get_LINEARREG_SLOPE(df['p3_mf11'],43),ts_TransNorm(df['p3_mf11'],0.302)),Add(ts_TransNorm(df['p3_mf11'],0.302),df['p6_tn7']),20)","551_-1*get_CCI(ts_Regression(df['p6_tn4'],df['cmo'],7,'B'),Add(get_LINEARREG_SLOPE(df['p3_mf11'],43),ts_TransNorm(df['p3_mf11'],0.302)),Add(ts_TransNorm(df['p3_mf11'],0.302),df['p6_tn7']),20)",15.1327,0.0055,1.3587,3.8067,0.5923340986045115,2024-01-01 20:59:00+00:00,2025-01-30 18:59:00+00:00,0.723,0.747,0.4918,-0.102
453,"-1*get_CCI(ts_Divide(FilterInf(df['p2_et17']),48),ts_TransNorm(get_CMO(df['p6_tn13'],41),0.302),Divide(df['p6_tn3'],0.975),50)","552_-1*get_CCI(ts_Divide(FilterInf(df['p2_et17']),48),ts_TransNorm(get_CMO(df['p6_tn13'],41),0.302),Divide(df['p6_tn3'],0.975),50)",12.0357,0.0066,1.0601,3.3083,0.5993600225003486,2024-01-01 20:59:00+00:00,2025-01-30 18:59:00+00:00,0.522,0.625,0.4551,-0.14
454,"get_CCI(ts_Entropy(ts_TransNorm(df['p3_mf3'],0.302),43),Add(ts_StdevChg(df['dcphase'],28),df['di']),Add(get_HT_DCPERIOD(df['p2_et15']),Equal(df['p6_tn2'],df['p5_to1'])),20)","553_get_CCI(ts_Entropy(ts_TransNorm(df['p3_mf3'],0.302),43),Add(ts_StdevChg(df['dcphase'],28),df['di']),Add(get_HT_DCPERIOD(df['p2_et15']),Equal(df['p6_tn2'],df['p5_to1'])),20)",11.7109,0.0034,1.0493,3.1408,0.3914369867442261,2024-01-01 20:59:00+00:00,2025-01-30 18:59:00+00:00,0.383,0.753,0.3371,-0.093
455,"Divide(Add(Xor(df['p3_mf12'],df['p2_et15']),Multiply(df['p6_tn13'],df['p1_corrs4'])),ts_Decay(inv(df['p6_tn8']),50))","554_Divide(Add(Xor(df['p3_mf12'],df['p2_et15']),Multiply(df['p6_tn13'],df['p1_corrs4'])),ts_Decay(inv(df['p6_tn8']),50))",10.2324,0.0029,0.9059,3.0791,0.13013109165125797,2024-01-01 20:59:00+00:00,2025-01-30 18:59:00+00:00,0.55,0.431,0.5607,-0.027
456,"-1*get_CCI(df['p3_mf11'],get_DX(df['p3_mf3'],pn_GroupNorm(df['p1_corrs8'],df['p6_tn11']),df['p2_et8'],43),inv(df['p5_to3']),40)","555_-1*get_CCI(df['p3_mf11'],get_DX(df['p3_mf3'],pn_GroupNorm(df['p1_corrs8'],df['p6_tn11']),df['p2_et8'],43),inv(df['p5_to3']),40)",13.5608,0.0066,1.1911,3.966,0.5963510673474309,2024-01-01 20:59:00+00:00,2025-01-30 18:59:00+00:00,0.735,0.554,0.5702,-0.191
457,"-1*get_CCI(pn_GroupNorm(df['cmo'],df['cmo']),Sign(df['p5_to0']),pn_GroupNorm(Sqrt(df['p6_tn2']),df['p2_et4']),35)","556_-1*get_CCI(pn_GroupNorm(df['cmo'],df['cmo']),Sign(df['p5_to0']),pn_GroupNorm(Sqrt(df['p6_tn2']),df['p2_et4']),35)",17.8598,0.0046,1.634,3.8666,0.477425804383391,2024-01-01 20:59:00+00:00,2025-01-30 18:59:00+00:00,0.687,1.081,0.3886,-0.141
458,"-1*get_CCI(ts_Product(Multiply(df['cmo'],df['p1_corrs4']),20),ts_TransNorm(get_CMO(df['p6_tn13'],41),0.302),Divide(ts_Entropy(df['p5_to2'],3),0.975),50)","557_-1*get_CCI(ts_Product(Multiply(df['cmo'],df['p1_corrs4']),20),ts_TransNorm(get_CMO(df['p6_tn13'],41),0.302),Divide(ts_Entropy(df['p5_to2'],3),0.975),50)",11.325,0.005,0.9842,3.702,0.574573559845871,2024-01-01 20:59:00+00:00,2025-01-30 18:59:00+00:00,0.333,0.732,0.3127,-0.095
459,"-1*get_CCI(Min(df['p2_et14'],df['ultosc']),Add(Abs(df['p2_et7']),pn_TransStd(df['p3_mf3'])),Max(df['p6_tn5'],0.389),20)","558_-1*get_CCI(Min(df['p2_et14'],df['ultosc']),Add(Abs(df['p2_et7']),pn_TransStd(df['p3_mf3'])),Max(df['p6_tn5'],0.389),20)",11.6992,0.0011,1.063,3.0415,0.41384677137866377,2024-01-01 20:59:00+00:00,2025-01-30 18:59:00+00:00,0.567,0.583,0.493,-0.002
460,"-1*get_CCI(get_CMO(df['p2_et14'],41),Log(ts_Rank(df['p3_mf0'],2)),ts_TransNorm(ts_Scale(df['p2_et7'],19),49),20)","558_-1*get_CCI(get_CMO(df['p2_et14'],41),Log(ts_Rank(df['p3_mf0'],2)),ts_TransNorm(ts_Scale(df['p2_et7'],19),49),20)",12.8695,0.0034,1.1433,3.806,0.5717026998279443,2024-01-01 20:59:00+00:00,2025-01-30 18:59:00+00:00,0.498,0.74,0.4023,-0.134
461,"get_CCI(IfThen(Or(df['p6_tn12'],df['p5_to7']),46,27),ts_Decay(IfThen(df['p5_to0'],20,36),20),df['p2_et14'],20)","559_get_CCI(IfThen(Or(df['p6_tn12'],df['p5_to7']),46,27),ts_Decay(IfThen(df['p5_to0'],20,36),20),df['p2_et14'],20)",11.4691,0.0044,0.9954,3.891,0.5540356011286465,2024-01-01 20:59:00+00:00,2025-01-30 18:59:00+00:00,0.394,0.684,0.3655,-0.225
462,"get_KAMA(get_CMO(df['p6_tn6'],12),5)","560_get_KAMA(get_CMO(df['p6_tn6'],12),5)",13.9777,0.0053,1.2454,3.7748,0.45885819163115427,2024-01-01 20:59:00+00:00,2025-01-30 18:59:00+00:00,0.572,0.776,0.4243,-0.297
463,"-1*SignedPower(ts_Max(df['p4_ms1'],2),Log(df['p6_tn4']))","561_-1*SignedPower(ts_Max(df['p4_ms1'],2),Log(df['p6_tn4']))",12.1349,0.0062,1.0734,3.2717,0.49548546419475564,2024-01-01 20:59:00+00:00,2025-01-30 18:59:00+00:00,0.808,0.354,0.6954,-0.194
464,"-1*SignedPower(ts_Max(ts_Divide(df['p6_tn7'],24),2),get_HT_DCPERIOD(df['p1_corrs3']))","562_-1*SignedPower(ts_Max(ts_Divide(df['p6_tn7'],24),2),get_HT_DCPERIOD(df['p1_corrs3']))",10.478,0.0047,0.9191,3.163,0.4278741156886466,2024-01-01 20:59:00+00:00,2025-01-30 18:59:00+00:00,0.541,0.454,0.5437,-0.271
465,"-1*pn_GroupRank(df['p3_mf11'],df['p4_ms1'])","563_-1*pn_GroupRank(df['p3_mf11'],df['p4_ms1'])",10.1053,0.0044,0.878,3.32,0.5356274318660867,2024-01-01 20:59:00+00:00,2025-01-30 18:59:00+00:00,0.596,0.354,0.6274,-0.12
466,"-1*pn_GroupRank(Minus(get_LINEARREG_SLOPE(df['p6_tn4'],10),ts_Rank(df['p6_tn6'],49)),df['p5_to4'])","564_-1*pn_GroupRank(Minus(get_LINEARREG_SLOPE(df['p6_tn4'],10),ts_Rank(df['p6_tn6'],49)),df['p5_to4'])",9.2094,0.0039,0.8007,3.0165,0.5605978805707235,2024-01-01 20:59:00+00:00,2025-01-30 18:59:00+00:00,0.511,0.356,0.5894,-0.239
467,"-1*pn_GroupRank(Minus(get_KAMA(df['p5_to1'],28),pn_Winsor(df['p2_et11'],17)),ts_Sum(df['p4_ms3'],25))","565_-1*pn_GroupRank(Minus(get_KAMA(df['p5_to1'],28),pn_Winsor(df['p2_et11'],17)),ts_Sum(df['p4_ms3'],25))",9.8223,0.004,0.8516,3.3163,0.5679685589685786,2024-01-01 20:59:00+00:00,2025-01-30 18:59:00+00:00,0.664,0.258,0.7202,-0.207
468,"pn_GroupRank(Minus(get_KAMA(df['p6_tn4'],28),ts_Decay2(df['p6_tn4'],6)),ts_Sum(Abs(df['p2_et15']),25))","566_pn_GroupRank(Minus(get_KAMA(df['p6_tn4'],28),ts_Decay2(df['p6_tn4'],6)),ts_Sum(Abs(df['p2_et15']),25))",9.8046,0.0042,0.849,3.3125,0.55082868901367,2024-01-01 20:59:00+00:00,2025-01-30 18:59:00+00:00,0.516,0.403,0.5615,-0.299
469,"pn_GroupRank(ts_Median(df['p2_et11'],5),ts_CorrChg(ts_Rank(df['p2_et14'],49),get_CMO(df['p6_tn7'],5),5))","567_pn_GroupRank(ts_Median(df['p2_et11'],5),ts_CorrChg(ts_Rank(df['p2_et14'],49),get_CMO(df['p6_tn7'],5),5))",11.7506,0.0044,1.017,4.0808,0.4850306332035893,2024-01-01 20:59:00+00:00,2025-01-30 18:59:00+00:00,0.612,0.488,0.5564,-0.257
470,"-1*pn_GroupRank(Exp(ts_Product(df['p6_tn7'],5)),SignedPower(df['p4_ms1'],0.257))","569_-1*pn_GroupRank(Exp(ts_Product(df['p6_tn7'],5)),SignedPower(df['p4_ms1'],0.257))",13.8334,0.0056,1.2179,4.1244,0.588194182135925,2024-01-01 20:59:00+00:00,2025-01-30 18:59:00+00:00,0.786,0.532,0.5964,-0.483
471,"-1*pn_GroupRank(get_LINEARREG_SLOPE(pn_GroupRank(df['p3_mf11'],df['p4_ms2']),9),ts_Entropy(df['p3_mf6'],20))","570_-1*pn_GroupRank(get_LINEARREG_SLOPE(pn_GroupRank(df['p3_mf11'],df['p4_ms2']),9),ts_Entropy(df['p3_mf6'],20))",9.3483,0.0038,0.8119,3.1181,0.4998499041336603,2024-01-01 20:59:00+00:00,2025-01-30 18:59:00+00:00,0.294,0.584,0.3349,-0.186
472,"-1*pn_GroupRank(Min(df['p1_corrs3'],ts_TransNorm(df['p2_et0'],14)),LEthan(ts_Quantile(df['p4_ms3'],21,'B'),df['p3_mf0']))","570_-1*pn_GroupRank(Min(df['p1_corrs3'],ts_TransNorm(df['p2_et0'],14)),LEthan(ts_Quantile(df['p4_ms3'],21,'B'),df['p3_mf0']))",15.4963,0.0066,1.3606,4.6842,0.5262237986257113,2024-01-01 20:59:00+00:00,2025-01-30 18:59:00+00:00,0.545,0.928,0.37,-0.211
473,"pn_GroupRank(ts_Delta(ts_Delay(df['p2_et9'],45),26),get_CMO(IfThen(df['p3_mf10'],26,1),31))","571_pn_GroupRank(ts_Delta(ts_Delay(df['p2_et9'],45),26),get_CMO(IfThen(df['p3_mf10'],26,1),31))",11.6704,0.0044,1.0228,3.6618,0.5529327632578928,2024-01-01 20:59:00+00:00,2025-01-30 18:59:00+00:00,0.513,0.594,0.4634,-0.004
474,"-1*pn_GroupRank(Exp(ts_Rank(df['p4_ms5'],21)),Exp(ts_Rank(df['p2_et0'],21)))","572_-1*pn_GroupRank(Exp(ts_Rank(df['p4_ms5'],21)),Exp(ts_Rank(df['p2_et0'],21)))",11.1074,0.0039,0.9771,3.4241,0.5534298224936962,2024-01-01 20:59:00+00:00,2025-01-30 18:59:00+00:00,0.732,0.325,0.6925,-0.122
475,"-1*pn_GroupRank(pn_Stand(get_LINEARREG_ANGLE(df['p5_to0'],22)),ts_CovChg(ts_Stdev(df['p3_mf11'],41),df['p5_to1'],21))","572_-1*pn_GroupRank(pn_Stand(get_LINEARREG_ANGLE(df['p5_to0'],22)),ts_CovChg(ts_Stdev(df['p3_mf11'],41),df['p5_to1'],21))",10.5942,0.0037,0.9374,3.1001,0.4796064015857299,2024-01-01 20:59:00+00:00,2025-01-30 18:59:00+00:00,0.307,0.707,0.3028,-0.194
476,"-1*get_CCI(Mthan(ts_Partial_corr(df['p2_et12'],df['p3_mf7'],df['p6_tn13'],6),df['p4_ms5']),pn_RankCentered(df['p2_et17']),Minus(ts_TransNorm(df['p5_to0'],39),ts_StdevChg(df['p3_mf12'],26)),50)","573_-1*get_CCI(Mthan(ts_Partial_corr(df['p2_et12'],df['p3_mf7'],df['p6_tn13'],6),df['p4_ms5']),pn_RankCentered(df['p2_et17']),Minus(ts_TransNorm(df['p5_to0'],39),ts_StdevChg(df['p3_mf12'],26)),50)",17.921,0.0061,1.6441,3.5248,0.5437233562541041,2024-01-01 20:59:00+00:00,2025-01-30 18:59:00+00:00,0.679,1.1,0.3817,-0.17
477,"-1*get_CCI(Mthan(ts_Stdev(df['p2_et19'],15),df['p3_mf7']),df['p3_mf2'],Minus(ts_TransNorm(df['p5_to0'],33),ts_StdevChg(df['p3_mf12'],0.086)),50)","574_-1*get_CCI(Mthan(ts_Stdev(df['p2_et19'],15),df['p3_mf7']),df['p3_mf2'],Minus(ts_TransNorm(df['p5_to0'],33),ts_StdevChg(df['p3_mf12'],0.086)),50)",12.4082,0.0058,1.0914,3.6152,0.5750817065336588,2024-01-01 20:59:00+00:00,2025-01-30 18:59:00+00:00,0.444,0.737,0.376,-0.139
478,"-1*ts_TransNorm(get_DX(pn_Stand(df['p5_to2']),df['p6_tn13'],get_LINEARREG_SLOPE(df['p6_tn1'],13),15),39)","575_-1*ts_TransNorm(get_DX(pn_Stand(df['p5_to2']),df['p6_tn13'],get_LINEARREG_SLOPE(df['p6_tn1'],13),15),39)",12.3559,0.0053,1.0914,3.5322,0.4815268513392498,2024-01-01 20:59:00+00:00,2025-01-30 18:59:00+00:00,0.423,0.758,0.3582,-0.154
479,"-1*ts_TransNorm(pn_GroupNeutral(df['p2_et0'],ts_Delay(df['adosc'],39)),39)","576_-1*ts_TransNorm(pn_GroupNeutral(df['p2_et0'],ts_Delay(df['adosc'],39)),39)",16.3913,0.0061,1.4948,3.4185,0.4882619342376729,2024-01-01 20:59:00+00:00,2025-01-30 18:59:00+00:00,0.76,0.858,0.4697,-0.163
480,"-1*get_CCI(get_LINEARREG_ANGLE(get_LINEARREG_ANGLE(df['p2_et7'],2),11),Abs(df['p1_corrs6']),Add(df['p6_tn13'],FilterInf(df['p2_et19'])),40)","577_-1*get_CCI(get_LINEARREG_ANGLE(get_LINEARREG_ANGLE(df['p2_et7'],2),11),Abs(df['p1_corrs6']),Add(df['p6_tn13'],FilterInf(df['p2_et19'])),40)",9.6839,0.0032,0.8276,3.7368,0.29714466386156685,2024-01-01 20:59:00+00:00,2025-01-30 18:59:00+00:00,0.51,0.386,0.5692,-0.083
481,"-1*get_CCI(ts_Delay(pn_Stand(df['p5_to0']),24),ts_Stdev(df['p2_et11'],30),Add(df['p6_tn13'],get_CCI(df['kama'],df['p2_et4'],df['p5_to3'],26)),40)","579_-1*get_CCI(ts_Delay(pn_Stand(df['p5_to0']),24),ts_Stdev(df['p2_et11'],30),Add(df['p6_tn13'],get_CCI(df['kama'],df['p2_et4'],df['p5_to3'],26)),40)",20.5996,0.0075,1.9074,3.4518,0.5791362140712135,2024-01-01 20:59:00+00:00,2025-01-30 18:59:00+00:00,1.085,0.98,0.5254,-0.172
482,"-1*get_CCI(ts_Min(ts_Decay(df['p5_to5'],48),3),Divide(df['p3_mf10'],ts_Skewness(df['p6_tn10'],41)),Add(df['p6_tn13'],get_CCI(df['p6_tn4'],df['ultosc'],df['p1_corrs8'],26)),40)","580_-1*get_CCI(ts_Min(ts_Decay(df['p5_to5'],48),3),Divide(df['p3_mf10'],ts_Skewness(df['p6_tn10'],41)),Add(df['p6_tn13'],get_CCI(df['p6_tn4'],df['ultosc'],df['p1_corrs8'],26)),40)",9.76,0.0046,0.8512,3.0541,0.49959461042262593,2024-01-01 20:59:00+00:00,2025-01-30 18:59:00+00:00,0.359,0.563,0.3894,-0.118
483,"-1*pn_GroupNorm(pn_GroupRank(df['p6_tn13'],Exp(df['p1_corrs6'])),Minus(df['p4_ms6'],df['p2_et13']))","581_-1*pn_GroupNorm(pn_GroupRank(df['p6_tn13'],Exp(df['p1_corrs6'])),Minus(df['p4_ms6'],df['p2_et13']))",9.4261,0.0041,0.8044,3.5342,0.5631404389980514,2024-01-01 20:59:00+00:00,2025-01-30 18:59:00+00:00,0.543,0.328,0.6234,-0.141
484,"get_CCI(ts_Decay(MEthan(df['p2_et3'],df['p5_to0']),38),Abs(Not(df['p5_to7'])),Add(ts_Entropy(df['p4_ms6'],2),get_CCI(df['kama'],df['p2_et1'],df['p5_to3'],31)),32)","582_get_CCI(ts_Decay(MEthan(df['p2_et3'],df['p5_to0']),38),Abs(Not(df['p5_to7'])),Add(ts_Entropy(df['p4_ms6'],2),get_CCI(df['kama'],df['p2_et1'],df['p5_to3'],31)),32)",18.045,0.0046,1.5906,5.7247,0.5100795946967858,2024-01-01 20:59:00+00:00,2025-01-30 18:59:00+00:00,0.506,1.215,0.294,-0.189
485,"-1*get_CCI(Power(pn_FillMin(df['p3_mf10']),44),Abs(df['p1_corrs6']),get_CCI(IfThen(df['p5_to5'],15,29),df['p1_corrs4'],ts_Quantile(df['p6_tn13'],28,'C'),8),40)","583_-1*get_CCI(Power(pn_FillMin(df['p3_mf10']),44),Abs(df['p1_corrs6']),get_CCI(IfThen(df['p5_to5'],15,29),df['p1_corrs4'],ts_Quantile(df['p6_tn13'],28,'C'),8),40)",10.5535,0.0041,0.9087,3.7848,0.44553184740912466,2024-01-01 20:59:00+00:00,2025-01-30 18:59:00+00:00,0.411,0.572,0.4181,-0.151
486,"-1*get_CCI(Sqrt(ts_Corr2(df['p5_to2'],df['p6_tn13'],25)),ts_Mean(df['p6_tn13'],8),Add(df['p6_tn13'],Abs(df['p6_tn13'])),40)","586_-1*get_CCI(Sqrt(ts_Corr2(df['p5_to2'],df['p6_tn13'],25)),ts_Mean(df['p6_tn13'],8),Add(df['p6_tn13'],Abs(df['p6_tn13'])),40)",17.0609,0.0059,1.5398,4.1062,0.5937951299054328,2024-01-01 20:59:00+00:00,2025-01-30 18:59:00+00:00,0.355,1.312,0.213,-0.279
487,"-1*get_CCI(Multiply(ts_Decay(df['p4_ms2'],48),df['p2_et14']),Add(ts_Median(df['p6_tn13'],30),Not(df['p5_to7'])),get_HT_DCPHASE(df['p3_mf0']),40)","587_-1*get_CCI(Multiply(ts_Decay(df['p4_ms2'],48),df['p2_et14']),Add(ts_Median(df['p6_tn13'],30),Not(df['p5_to7'])),get_HT_DCPHASE(df['p3_mf0']),40)",11.8992,0.003,1.0626,3.369,0.41850305933673093,2024-01-01 20:59:00+00:00,2025-01-30 18:59:00+00:00,0.461,0.688,0.4012,-0.138
488,"-1*get_CCI(df['p2_et8'],get_LINEARREG_ANGLE(df['p5_to5'],35),Add(ts_Median(df['p6_tn13'],30),Not(df['p6_tn13'])),20)","588_-1*get_CCI(df['p2_et8'],get_LINEARREG_ANGLE(df['p5_to5'],35),Add(ts_Median(df['p6_tn13'],30),Not(df['p6_tn13'])),20)",10.8668,0.004,0.9627,3.1143,0.5462811414383089,2024-01-01 20:59:00+00:00,2025-01-30 18:59:00+00:00,0.309,0.733,0.2965,-0.166
489,"-1*get_CCI(Multiply(ts_Divide(df['p4_ms2'],42),df['p2_et14']),Add(ts_Median(df['p6_tn13'],38),pn_GroupNeutral(df['p6_tn0'],df['p6_tn3'])),Add(df['p1_corrs8'],FilterInf(df['p1_corrs8'])),40)","589_-1*get_CCI(Multiply(ts_Divide(df['p4_ms2'],42),df['p2_et14']),Add(ts_Median(df['p6_tn13'],38),pn_GroupNeutral(df['p6_tn0'],df['p6_tn3'])),Add(df['p1_corrs8'],FilterInf(df['p1_corrs8'])),40)",12.0875,0.0038,1.0885,3.0391,0.4487609702133432,2024-01-01 20:59:00+00:00,2025-01-30 18:59:00+00:00,0.602,0.576,0.511,-0.123
490,"-1*get_CCI(Add(pn_GroupNeutral(df['p3_mf5'],df['p1_corrs7']),ts_Min(df['p6_tn13'],18)),MEthan(ts_Sum(df['p6_tn0'],2),df['p6_tn13']),Add(df['p6_tn13'],ts_Delay(df['p6_tn8'],37)),40)","590_-1*get_CCI(Add(pn_GroupNeutral(df['p3_mf5'],df['p1_corrs7']),ts_Min(df['p6_tn13'],18)),MEthan(ts_Sum(df['p6_tn0'],2),df['p6_tn13']),Add(df['p6_tn13'],ts_Delay(df['p6_tn8'],37)),40)",13.066,0.006,1.1737,3.0905,0.5132392107889501,2024-01-01 20:59:00+00:00,2025-01-30 18:59:00+00:00,0.463,0.807,0.3646,-0.151
491,"-1*get_CCI(Add(Abs(df['p2_et4']),df['p2_et13']),ts_Rank(IfThen(df['p2_et0'],29,14),40),get_CCI(df['p6_tn13'],df['ultosc'],pn_GroupNeutral(df['cmo'],df['p6_tn0']),40),40)","591_-1*get_CCI(Add(Abs(df['p2_et4']),df['p2_et13']),ts_Rank(IfThen(df['p2_et0'],29,14),40),get_CCI(df['p6_tn13'],df['ultosc'],pn_GroupNeutral(df['cmo'],df['p6_tn0']),40),40)",18.4683,0.0068,1.7066,3.1891,0.5606737352351017,2024-01-01 20:59:00+00:00,2025-01-30 18:59:00+00:00,0.428,1.419,0.2317,-0.141
492,"-1*pn_GroupNorm(ts_Decay(df['p4_ms1'],3),get_LINEARREG_ANGLE(df['kama'],47))","591_-1*pn_GroupNorm(ts_Decay(df['p4_ms1'],3),get_LINEARREG_ANGLE(df['kama'],47))",17.78,0.0088,1.6003,4.0045,0.5841260366595722,2024-01-01 20:59:00+00:00,2025-01-30 18:59:00+00:00,0.831,0.9,0.4801,-0.37
493,"-1*pn_CrossFit(get_LINEARREG_SLOPE(ts_Stdev2(df['di'],33),50),get_LINEARREG_SLOPE(df['p2_et0'],30))","591_-1*pn_CrossFit(get_LINEARREG_SLOPE(ts_Stdev2(df['di'],33),50),get_LINEARREG_SLOPE(df['p2_et0'],30))",12.8165,0.0051,1.1552,3.0338,0.5103007467554251,2024-01-01 20:59:00+00:00,2025-01-30 18:59:00+00:00,0.341,0.909,0.2728,-0.216
494,"-1*pn_GroupNorm(df['ultosc'],pn_Stand(df['dcphase']))","591_-1*pn_GroupNorm(df['ultosc'],pn_Stand(df['dcphase']))",11.9806,0.0049,1.0464,3.8106,0.5887605915881367,2024-01-01 20:59:00+00:00,2025-01-30 18:59:00+00:00,0.559,0.574,0.4934,-0.223
495,"-1*pn_GroupNorm(df['cmo'],ts_TransNorm(df['p3_mf4'],42))","592_-1*pn_GroupNorm(df['cmo'],ts_TransNorm(df['p3_mf4'],42))",15.904,0.0079,1.4011,4.4902,0.5998857831244752,2024-01-01 20:59:00+00:00,2025-01-30 18:59:00+00:00,0.604,0.913,0.3982,-0.46
496,"-1*pn_CrossFit(ts_Stdev(df['p1_corrs2'],7),pn_Rank(Max(df['p3_mf11'],df['p1_corrs2'])))","594_-1*pn_CrossFit(ts_Stdev(df['p1_corrs2'],7),pn_Rank(Max(df['p3_mf11'],df['p1_corrs2'])))",10.222,0.004,0.9002,3.0527,0.5461420052516389,2024-01-01 20:59:00+00:00,2025-01-30 18:59:00+00:00,0.554,0.42,0.5688,-0.177
497,"-1*pn_CrossFit(ts_MeanChg(df['p4_ms4'],16),Max(df['p1_corrs3'],SignedPower(df['dcperiod'],df['p4_ms1'])))","594_-1*pn_CrossFit(ts_MeanChg(df['p4_ms4'],16),Max(df['p1_corrs3'],SignedPower(df['dcperiod'],df['p4_ms1'])))",24.059,0.0082,2.2187,4.3894,0.528611729677639,2024-01-01 20:59:00+00:00,2025-01-30 18:59:00+00:00,1.079,1.322,0.4494,-0.139
498,"-1*ts_ChgRate(df['p2_et10'],8)","595_-1*ts_ChgRate(df['p2_et10'],8)",10.249,0.0044,0.8951,3.2274,0.567935608581256,2024-01-01 20:59:00+00:00,2025-01-30 18:59:00+00:00,0.511,0.457,0.5279,-0.209
499,"get_CCI(pn_FillMin(Or(df['p2_et17'],df['p5_to5'])),df['p5_to5'],ts_MeanChg(get_CMO(df['p6_tn13'],12),32),5)","596_get_CCI(pn_FillMin(Or(df['p2_et17'],df['p5_to5'])),df['p5_to5'],ts_MeanChg(get_CMO(df['p6_tn13'],12),32),5)",14.7472,0.0053,1.3154,3.9779,0.4759129116749756,2024-01-01 20:59:00+00:00,2025-01-30 18:59:00+00:00,0.401,1.023,0.2816,-0.114
500,"-1*get_CCI(pn_GroupNeutral(Sqrt(df['p2_et10']),ts_Delay(df['p2_et4'],49)),Exp(ts_MeanChg(df['p2_et9'],13)),df['p6_tn1'],30)","598_-1*get_CCI(pn_GroupNeutral(Sqrt(df['p2_et10']),ts_Delay(df['p2_et4'],49)),Exp(ts_MeanChg(df['p2_et9'],13)),df['p6_tn1'],30)",18.0071,0.0059,1.6628,3.2564,0.5178122401975432,2024-01-01 20:59:00+00:00,2025-01-30 18:59:00+00:00,0.868,0.931,0.4825,-0.242
501,"get_CCI(df['p5_to5'],df['p5_to5'],ts_MeanChg(get_CMO(df['p6_tn13'],30),34),7)","599_get_CCI(df['p5_to5'],df['p5_to5'],ts_MeanChg(get_CMO(df['p6_tn13'],30),34),7)",11.7535,0.0047,1.0464,3.1604,0.5675666674212947,2024-01-01 20:59:00+00:00,2025-01-30 18:59:00+00:00,0.316,0.817,0.2789,-0.147
502,"-1*get_CCI(df['p2_et17'],df['cmo'],ts_Max(pn_CrossFit(df['p4_ms0'],df['p1_corrs0']),24),6)","600_-1*get_CCI(df['p2_et17'],df['cmo'],ts_Max(pn_CrossFit(df['p4_ms0'],df['p1_corrs0']),24),6)",22.1431,0.0061,2.068,3.4773,0.5618548882346883,2024-01-01 20:59:00+00:00,2025-01-30 18:59:00+00:00,0.7,1.537,0.3129,-0.298
503,"-1*get_CCI(ts_Cov(df['p3_mf4'],df['p2_et19'],32),Sign(df['cmo']),ts_Delay(df['p2_et19'],16),30)","602_-1*get_CCI(ts_Cov(df['p3_mf4'],df['p2_et19'],32),Sign(df['cmo']),ts_Delay(df['p2_et19'],16),30)",11.2577,0.0039,0.9963,3.291,0.538279031053955,2024-01-01 20:59:00+00:00,2025-01-30 18:59:00+00:00,0.464,0.614,0.4304,-0.262
504,"-1*get_CCI(df['p2_et17'],df['cmo'],ts_Max(pn_CrossFit(df['p2_et13'],df['di']),7),30)","603_-1*get_CCI(df['p2_et17'],df['cmo'],ts_Max(pn_CrossFit(df['p2_et13'],df['di']),7),30)",22.0051,0.0062,2.0552,3.435,0.5590864701421981,2024-01-01 20:59:00+00:00,2025-01-30 18:59:00+00:00,0.594,1.63,0.2671,-0.422
505,"-1*get_CCI(ts_Sum(Log(df['p6_tn4']),39),ts_Cov(df['p4_ms5'],Or(df['p5_to7'],df['p6_tn3']),27),df['p1_corrs2'],20)","604_-1*get_CCI(ts_Sum(Log(df['p6_tn4']),39),ts_Cov(df['p4_ms5'],Or(df['p5_to7'],df['p6_tn3']),27),df['p1_corrs2'],20)",12.867,0.0042,1.1451,3.6246,0.45802534676011464,2024-01-01 20:59:00+00:00,2025-01-30 18:59:00+00:00,0.545,0.694,0.4399,-0.208
506,"-1*get_CCI(df['p2_et19'],ts_Scale(df['cmo'],30),get_KAMA(df['dcphase'],7),20)","605_-1*get_CCI(df['p2_et19'],ts_Scale(df['cmo'],30),get_KAMA(df['dcphase'],7),20)",10.196,0.0033,0.8857,3.5253,0.5211792152304102,2024-01-01 20:59:00+00:00,2025-01-30 18:59:00+00:00,0.48,0.478,0.501,-0.19
507,"-1*get_CCI(Sqrt(df['cmo']),Reverse(ts_Skewness(df['lislope'],21)),get_MINUS_DM(Equal(df['p2_et6'],df['p1_corrs2']),get_MINUS_DM(df['p4_ms3'],df['p3_mf0'],16),20),14)","605_-1*get_CCI(Sqrt(df['cmo']),Reverse(ts_Skewness(df['lislope'],21)),get_MINUS_DM(Equal(df['p2_et6'],df['p1_corrs2']),get_MINUS_DM(df['p4_ms3'],df['p3_mf0'],16),20),14)",16.1424,0.0056,1.4594,3.8012,0.5405886760193618,2024-01-01 20:59:00+00:00,2025-01-30 18:59:00+00:00,0.519,1.06,0.3287,-0.292
508,"-1*get_CCI(df['cmo'],get_MINUS_DM(pn_Rank(df['cmo']),ts_ChgRate(df['p1_corrs9'],18),20),UnEqual(df['cmo'],ts_Argmin(df['cmo'],15)),30)","606_-1*get_CCI(df['cmo'],get_MINUS_DM(pn_Rank(df['cmo']),ts_ChgRate(df['p1_corrs9'],18),20),UnEqual(df['cmo'],ts_Argmin(df['cmo'],15)),30)",12.6001,0.0048,1.0937,4.276,0.4828055990316299,2024-01-01 20:59:00+00:00,2025-01-30 18:59:00+00:00,0.558,0.626,0.4713,-0.214
509,"get_CCI(Sign(ts_Divide(df['p6_tn1'],8)),ts_MeanChg(df['p2_et6'],30),get_LINEARREG_SLOPE(df['p2_et11'],30),20)","607_get_CCI(Sign(ts_Divide(df['p6_tn1'],8)),ts_MeanChg(df['p2_et6'],30),get_LINEARREG_SLOPE(df['p2_et11'],30),20)",13.9452,0.004,1.2609,3.4153,0.44599890598268227,2024-01-01 20:59:00+00:00,2025-01-30 18:59:00+00:00,0.34,1.024,0.2493,-0.19
510,"get_CCI(df['dm'],LEthan(df['p6_tn7'],ts_Product(df['p2_et9'],2)),ts_Entropy(df['cmo'],14),30)","608_get_CCI(df['dm'],LEthan(df['p6_tn7'],ts_Product(df['p2_et9'],2)),ts_Entropy(df['cmo'],14),30)",13.0454,0.0031,1.1776,3.3507,0.4063164818275766,2024-01-01 20:59:00+00:00,2025-01-30 18:59:00+00:00,0.513,0.761,0.4027,-0.035
511,"-1*get_CCI(UnEqual(ts_Entropy(df['p2_et19'],20),ts_Decay2(df['p5_to0'],7)),df['cmo'],get_MINUS_DI(df['p2_et10'],ts_Quantile(df['p1_corrs1'],7,'B'),pn_Rank(df['p6_tn1']),29),17)","611_-1*get_CCI(UnEqual(ts_Entropy(df['p2_et19'],20),ts_Decay2(df['p5_to0'],7)),df['cmo'],get_MINUS_DI(df['p2_et10'],ts_Quantile(df['p1_corrs1'],7,'B'),pn_Rank(df['p6_tn1']),29),17)",17.8483,0.0067,1.644,3.2153,0.5271650910419308,2024-01-01 20:59:00+00:00,2025-01-30 18:59:00+00:00,0.443,1.336,0.249,-0.44
512,"-1*get_CCI(pn_Rank(pn_Cut(df['p5_to0'])),df['cmo'],ts_Delay(Or(df['p3_mf11'],df['p6_tn10']),29),21)","607_-1*get_CCI(pn_Rank(pn_Cut(df['p5_to0'])),df['cmo'],ts_Delay(Or(df['p3_mf11'],df['p6_tn10']),29),21)",30.1746,0.0082,2.8303,4.3879,0.5891448409119568,2024-01-01 20:59:00+00:00,2025-01-30 18:59:00+00:00,0.831,2.232,0.2713,-0.435
513,"-1*get_CCI(ts_Cov(df['p1_corrs2'],df['cmo'],29),ts_Stdev(df['liangle'],29),get_MINUS_DM(ts_Divide(df['p2_et3'],30),get_MINUS_DM(df['p2_et8'],df['cmo'],41),20),29)","608_-1*get_CCI(ts_Cov(df['p1_corrs2'],df['cmo'],29),ts_Stdev(df['liangle'],29),get_MINUS_DM(ts_Divide(df['p2_et3'],30),get_MINUS_DM(df['p2_et8'],df['cmo'],41),20),29)",11.685,0.0047,1.0358,3.2762,0.4085815177605513,2024-01-01 20:59:00+00:00,2025-01-30 18:59:00+00:00,0.378,0.743,0.3372,-0.139
514,"-1*get_CCI(ts_Regression(df['p3_mf11'],df['kama'],1,'A'),df['cmo'],df['cmo'],29)","609_-1*get_CCI(ts_Regression(df['p3_mf11'],df['kama'],1,'A'),df['cmo'],df['cmo'],29)",12.3159,0.0044,1.0768,3.9873,0.48545866130887755,2024-01-01 20:59:00+00:00,2025-01-30 18:59:00+00:00,0.415,0.75,0.3562,-0.239
515,"-1*get_CCI(pn_Winsor(df['cmo'],5),df['p2_et8'],ts_Entropy(pn_Stand(df['p6_tn7']),26),14)","610_-1*get_CCI(pn_Winsor(df['cmo'],5),df['p2_et8'],ts_Entropy(pn_Stand(df['p6_tn7']),26),14)",25.2775,0.0059,2.3728,3.769,0.5621844256650306,2024-01-01 20:59:00+00:00,2025-01-30 18:59:00+00:00,0.793,1.775,0.3088,-0.396
516,"-1*get_CCI(Log(pn_FillMin(df['p3_mf1'])),ts_Scale(df['cmo'],21),df['p2_et19'],29)","612_-1*get_CCI(Log(pn_FillMin(df['p3_mf1'])),ts_Scale(df['cmo'],21),df['p2_et19'],29)",9.7363,0.0033,0.8511,3.1877,0.5163447248592328,2024-01-01 20:59:00+00:00,2025-01-30 18:59:00+00:00,0.364,0.557,0.3952,-0.159
517,"get_CCI(get_MINUS_DM(Xor(df['p6_tn10'],df['p2_et16']),df['p5_to5'],16),get_MINUS_DM(df['p5_to0'],df['cmo'],36),pn_Stand(df['p2_et9']),20)","614_get_CCI(get_MINUS_DM(Xor(df['p6_tn10'],df['p2_et16']),df['p5_to5'],16),get_MINUS_DM(df['p5_to0'],df['cmo'],36),pn_Stand(df['p2_et9']),20)",16.6086,0.0037,1.5337,3.2523,0.3856042747005807,2024-01-01 20:59:00+00:00,2025-01-30 18:59:00+00:00,0.798,0.862,0.4807,-0.089
518,"-1*get_CCI(df['cmo'],ts_TransNorm(ts_Kurtosis(df['p1_corrs2'],29),9),get_CMO(get_HT_DCPERIOD(df['lislope']),47),20)","615_-1*get_CCI(df['cmo'],ts_TransNorm(ts_Kurtosis(df['p1_corrs2'],29),9),get_CMO(get_HT_DCPERIOD(df['lislope']),47),20)",12.6779,0.0055,1.1344,3.1769,0.5006105806741774,2024-01-01 20:59:00+00:00,2025-01-30 18:59:00+00:00,0.403,0.825,0.3282,-0.273
519,"-1*get_CCI(FilterInf(df['cmo']),ts_CovChg(ts_Divide(df['p1_corrs3'],1),Divide(df['cmo'],0.459),30),df['cmo'],29)","616_-1*get_CCI(FilterInf(df['cmo']),ts_CovChg(ts_Divide(df['p1_corrs3'],1),Divide(df['cmo'],0.459),30),df['cmo'],29)",10.2537,0.0039,0.8856,3.6159,0.5003919149548763,2024-01-01 20:59:00+00:00,2025-01-30 18:59:00+00:00,0.304,0.655,0.317,-0.225
520,"-1*get_CCI(ts_TransNorm(df['p5_to0'],18),ts_TransNorm(ts_Kurtosis(df['p6_tn0'],29),9),ts_Decay2(df['p4_ms0'],13),29)","617_-1*get_CCI(ts_TransNorm(df['p5_to0'],18),ts_TransNorm(ts_Kurtosis(df['p6_tn0'],29),9),ts_Decay2(df['p4_ms0'],13),29)",9.6193,0.0044,0.8388,3.032,0.45715364821785015,2024-01-01 20:59:00+00:00,2025-01-30 18:59:00+00:00,0.451,0.457,0.4967,-0.069
521,"get_CCI(pn_FillMax(ts_Divide(df['p2_et11'],19)),ts_CovChg(df['cmo'],ts_Cov(df['dm'],df['p3_mf12'],43),2),ts_Skewness(df['dcphase'],7),29)","618_get_CCI(pn_FillMax(ts_Divide(df['p2_et11'],19)),ts_CovChg(df['cmo'],ts_Cov(df['dm'],df['p3_mf12'],43),2),ts_Skewness(df['dcphase'],7),29)",11.1544,0.0043,0.9828,3.3317,0.4164617222982447,2024-01-01 20:59:00+00:00,2025-01-30 18:59:00+00:00,0.477,0.587,0.4483,-0.257
522,"-1*get_CCI(df['p2_et7'],ts_Quantile(df['p2_et16'],13,'D'),df['p6_tn1'],29)","617_-1*get_CCI(df['p2_et7'],ts_Quantile(df['p2_et16'],13,'D'),df['p6_tn1'],29)",15.0281,0.0063,1.3396,3.9555,0.5962104821209558,2024-01-01 20:59:00+00:00,2025-01-30 18:59:00+00:00,0.302,1.147,0.2084,-0.247
523,"-1*ts_Cov(df['p1_corrs9'],df['p2_et8'],1)","618_-1*ts_Cov(df['p1_corrs9'],df['p2_et8'],1)",9.9884,0.0032,0.8815,3.0336,0.35016873149331,2024-01-01 20:59:00+00:00,2025-01-30 18:59:00+00:00,0.417,0.537,0.4371,-0.243
524,"-1*get_LINEARREG_SLOPE(pn_GroupNorm(df['p4_ms5'],df['p2_et9']),24)","619_-1*get_LINEARREG_SLOPE(pn_GroupNorm(df['p4_ms5'],df['p2_et9']),24)",10.6733,0.0046,0.9434,3.0232,0.4166580400351017,2024-01-01 20:59:00+00:00,2025-01-30 18:59:00+00:00,0.482,0.539,0.4721,-0.207
525,"ts_Rank(Divide(df['p4_ms2'],get_MINUS_DI(df['p5_to5'],df['p2_et17'],df['p2_et16'],17)),16)","619_ts_Rank(Divide(df['p4_ms2'],get_MINUS_DI(df['p5_to5'],df['p2_et17'],df['p2_et16'],17)),16)",10.6378,0.0048,0.9252,3.4406,0.5614156803758054,2024-01-01 20:59:00+00:00,2025-01-30 18:59:00+00:00,0.34,0.662,0.3393,-0.08
526,"-1*get_LINEARREG_SLOPE(pn_CrossFit(ts_Product(df['cmo'],49),df['p6_tn4']),16)","619_-1*get_LINEARREG_SLOPE(pn_CrossFit(ts_Product(df['cmo'],49),df['p6_tn4']),16)",15.4171,0.0061,1.367,4.3312,0.5298010583376411,2024-01-01 20:59:00+00:00,2025-01-30 18:59:00+00:00,0.683,0.797,0.4615,-0.235
527,"Add(Multiply(ts_Divide(df['p2_et10'],43),ts_Scale(df['p2_et1'],20)),Divide(df['p4_ms2'],ts_Rank(df['p4_ms5'],20)))","620_Add(Multiply(ts_Divide(df['p2_et10'],43),ts_Scale(df['p2_et1'],20)),Divide(df['p4_ms2'],ts_Rank(df['p4_ms5'],20)))",10.0051,0.0044,0.8724,3.1803,0.5426769171919996,2024-01-01 20:59:00+00:00,2025-01-30 18:59:00+00:00,0.423,0.521,0.4481,-0.142
528,"-1*Add(pn_Winsor(get_MINUS_DI(df['p2_et10'],df['cmo'],df['p6_tn8'],12),28),Divide(get_CCI(df['p2_et10'],df['p2_et6'],df['cmo'],28),ts_Skewness(df['p3_mf5'],50)))","622_-1*Add(pn_Winsor(get_MINUS_DI(df['p2_et10'],df['cmo'],df['p6_tn8'],12),28),Divide(get_CCI(df['p2_et10'],df['p2_et6'],df['cmo'],28),ts_Skewness(df['p3_mf5'],50)))",12.2454,0.0053,1.0588,4.1786,0.4795462005870232,2024-01-01 20:59:00+00:00,2025-01-30 18:59:00+00:00,0.426,0.72,0.3717,-0.3
529,"-1*Add(pn_Rank(df['p4_ms5']),ts_Regression(df['lislope'],SignedPower(df['p2_et6'],0.933),41,'A'))","623_-1*Add(pn_Rank(df['p4_ms5']),ts_Regression(df['lislope'],SignedPower(df['p2_et6'],0.933),41,'A'))",9.6923,0.0036,0.8408,3.3072,0.39916314371267736,2024-01-01 20:59:00+00:00,2025-01-30 18:59:00+00:00,0.363,0.547,0.3989,-0.085
530,"-1*Add(ts_Corr(ts_Argmin(df['p3_mf11'],46),pn_FillMin(df['cmo']),34),Divide(get_CCI(df['p2_et10'],df['p2_et6'],df['cmo'],44),pn_Rank2(df['p1_corrs4'])))","624_-1*Add(ts_Corr(ts_Argmin(df['p3_mf11'],46),pn_FillMin(df['cmo']),34),Divide(get_CCI(df['p2_et10'],df['p2_et6'],df['cmo'],44),pn_Rank2(df['p1_corrs4'])))",10.3667,0.0053,0.9051,3.1577,0.5355636893657579,2024-01-01 20:59:00+00:00,2025-01-30 18:59:00+00:00,0.244,0.735,0.2492,-0.335
531,"-1*Add(Divide(Power(df['p6_tn9'],37),IfThen(df['dx'],44,6)),Divide(get_CMO(df['cmo'],46),ts_Delay(df['p2_et6'],13)))","626_-1*Add(Divide(Power(df['p6_tn9'],37),IfThen(df['dx'],44,6)),Divide(get_CMO(df['cmo'],46),ts_Delay(df['p2_et6'],13)))",11.5303,0.006,1.0185,3.1411,0.5588731033313495,2024-01-01 20:59:00+00:00,2025-01-30 18:59:00+00:00,0.43,0.672,0.3902,-0.399
532,"-1*Add(get_CCI(get_CCI(df['p2_et10'],df['cmo'],df['liangle'],28),Log(df['p2_et7']),df['cmo'],46),Divide(get_CMO(df['p6_tn4'],46),df['p5_to7']))","628_-1*Add(get_CCI(get_CCI(df['p2_et10'],df['cmo'],df['liangle'],28),Log(df['p2_et7']),df['cmo'],46),Divide(get_CMO(df['p6_tn4'],46),df['p5_to7']))",11.3026,0.0054,0.9998,3.1005,0.569784967379233,2024-01-01 20:59:00+00:00,2025-01-30 18:59:00+00:00,0.219,0.863,0.2024,-0.124
533,"-1*Add(ts_Entropy(df['p4_ms3'],42),Divide(get_CCI(df['dx'],df['p4_ms1'],df['p6_tn4'],46),ts_Delay(df['p3_mf2'],5)))","629_-1*Add(ts_Entropy(df['p4_ms3'],42),Divide(get_CCI(df['dx'],df['p4_ms1'],df['p6_tn4'],46),ts_Delay(df['p3_mf2'],5)))",10.9877,0.0042,0.964,3.4103,0.5602190055569936,2024-01-01 20:59:00+00:00,2025-01-30 18:59:00+00:00,0.586,0.457,0.5618,-0.061
534,"-1*Minus(df['liangle'],Minus(UnEqual(df['di'],df['liangle']),ts_Scale(df['p6_tn13'],15)))","630_-1*Minus(df['liangle'],Minus(UnEqual(df['di'],df['liangle']),ts_Scale(df['p6_tn13'],15)))",13.6341,0.0058,1.1835,4.5221,0.5720885622244647,2024-01-01 20:59:00+00:00,2025-01-30 18:59:00+00:00,0.514,0.766,0.4016,-0.189
535,"-1*pn_TransNorm(ts_TransNorm(get_LINEARREG_ANGLE(df['dcphase'],16),16))","631_-1*pn_TransNorm(ts_TransNorm(get_LINEARREG_ANGLE(df['dcphase'],16),16))",10.1002,0.0036,0.8868,3.1552,0.5680002608014195,2024-01-01 20:59:00+00:00,2025-01-30 18:59:00+00:00,0.51,0.449,0.5318,-0.184
536,"get_CCI(df['p3_mf7'],ts_Kurtosis(pn_TransStd(df['p5_to7']),24),pn_GroupNeutral(df['p6_tn1'],df['p3_mf9']),3)","632_get_CCI(df['p3_mf7'],ts_Kurtosis(pn_TransStd(df['p5_to7']),24),pn_GroupNeutral(df['p6_tn1'],df['p3_mf9']),3)",13.7076,0.0014,1.2633,3.0193,0.22075679534920295,2024-01-01 20:59:00+00:00,2025-01-30 18:59:00+00:00,0.647,0.72,0.4733,-0.005
537,"-1*Minus(df['p5_to2'],Minus(UnEqual(df['p5_to0'],df['liangle']),ts_Scale(df['p6_tn13'],15)))","633_-1*Minus(df['p5_to2'],Minus(UnEqual(df['p5_to0'],df['liangle']),ts_Scale(df['p6_tn13'],15)))",14.3005,0.0059,1.2495,4.5247,0.5779684221503274,2024-01-01 20:59:00+00:00,2025-01-30 18:59:00+00:00,0.544,0.808,0.4024,-0.127
538,"get_CCI(ts_Sum(UnEqual(df['p6_tn12'],df['p5_to1']),44),ts_Argmax(df['p1_corrs0'],19),ts_MeanChg(df['p5_to0'],17),3)","634_get_CCI(ts_Sum(UnEqual(df['p6_tn12'],df['p5_to1']),44),ts_Argmax(df['p1_corrs0'],19),ts_MeanChg(df['p5_to0'],17),3)",14.1326,0.0044,1.2657,3.7681,0.5275499176332905,2024-01-01 20:59:00+00:00,2025-01-30 18:59:00+00:00,0.512,0.858,0.3737,-0.062
539,"Exp(get_LINEARREG_ANGLE(ts_Median(df['p2_et7'],30),11))","637_Exp(get_LINEARREG_ANGLE(ts_Median(df['p2_et7'],30),11))",12.3598,0.0042,1.1114,3.1026,0.31180400796522345,2024-01-01 20:59:00+00:00,2025-01-30 18:59:00+00:00,0.531,0.672,0.4414,0.155
540,"-1*pn_TransStd(get_LINEARREG_ANGLE(ts_Delta(df['p2_et12'],26),7))","638_-1*pn_TransStd(get_LINEARREG_ANGLE(ts_Delta(df['p2_et12'],26),7))",11.2447,0.0041,1.0023,3.0466,0.3549513108710944,2024-01-01 20:59:00+00:00,2025-01-30 18:59:00+00:00,0.324,0.761,0.2986,-0.18
541,"Minus(get_LINEARREG_ANGLE(LEthan(df['p5_to0'],df['p2_et2']),10),ts_Kurtosis(get_HT_DCPHASE(df['p6_tn6']),43))","639_Minus(get_LINEARREG_ANGLE(LEthan(df['p5_to0'],df['p2_et2']),10),ts_Kurtosis(get_HT_DCPHASE(df['p6_tn6']),43))",10.2518,0.0035,0.8972,3.321,0.5575686901260485,2024-01-01 20:59:00+00:00,2025-01-30 18:59:00+00:00,0.409,0.562,0.4212,-0.164
542,"-1*Minus(pn_GroupRank(df['p1_corrs1'],df['p2_et2']),pn_GroupNorm(IfThen(df['p4_ms5'],35,37),df['p2_et2']))","640_-1*Minus(pn_GroupRank(df['p1_corrs1'],df['p2_et2']),pn_GroupNorm(IfThen(df['p4_ms5'],35,37),df['p2_et2']))",9.7074,0.0042,0.8483,3.0412,0.5465765052510697,2024-01-01 20:59:00+00:00,2025-01-30 18:59:00+00:00,0.274,0.644,0.2985,-0.285
543,"get_CCI(pn_FillMin(pn_CrossFit(df['p2_et0'],df['p2_et11'])),df['di'],ts_Entropy(df['p2_et11'],44),3)","641_get_CCI(pn_FillMin(pn_CrossFit(df['p2_et0'],df['p2_et11'])),df['di'],ts_Entropy(df['p2_et11'],44),3)",14.6896,0.006,1.3306,3.2557,0.45112894335614634,2024-01-01 20:59:00+00:00,2025-01-30 18:59:00+00:00,0.668,0.772,0.4639,-0.107
544,"get_CCI(inv(df['p6_tn6']),df['di'],ts_Entropy(df['di'],22),3)","643_get_CCI(inv(df['p6_tn6']),df['di'],ts_Entropy(df['di'],22),3)",9.5163,0.0043,0.8294,3.0198,0.30193245834432075,2024-01-01 20:59:00+00:00,2025-01-30 18:59:00+00:00,0.45,0.448,0.5011,-0.12
545,"-1*Minus(FilterInf(df['p1_corrs9']),Minus(ts_MeanChg(df['p6_tn5'],15),ts_Scale(df['p6_tn13'],29)))","640_-1*Minus(FilterInf(df['p1_corrs9']),Minus(ts_MeanChg(df['p6_tn5'],15),ts_Scale(df['p6_tn13'],29)))",15.1272,0.0073,1.3243,4.5556,0.5979326677949865,2024-01-01 20:59:00+00:00,2025-01-30 18:59:00+00:00,0.356,1.077,0.2484,-0.357
546,"-1*Minus(ts_Argmax(ts_MeanChg(df['p4_ms5'],16),4),Minus(pn_GroupNorm(df['p1_corrs7'],df['p2_et7']),df['p1_corrs0']))","641_-1*Minus(ts_Argmax(ts_MeanChg(df['p4_ms5'],16),4),Minus(pn_GroupNorm(df['p1_corrs7'],df['p2_et7']),df['p1_corrs0']))",22.6621,0.0065,2.0989,4.0474,0.5487719797923187,2024-01-01 20:59:00+00:00,2025-01-30 18:59:00+00:00,0.721,1.551,0.3173,-0.095
547,"-1*Minus(get_CCI(ts_Scale(df['p1_corrs3'],20),df['p1_corrs9'],ts_Decay(df['p5_to6'],39),18),ts_Scale(df['p4_ms5'],3))","642_-1*Minus(get_CCI(ts_Scale(df['p1_corrs3'],20),df['p1_corrs9'],ts_Decay(df['p5_to6'],39),18),ts_Scale(df['p4_ms5'],3))",9.6748,0.0048,0.8428,3.024,0.5924564676927367,2024-01-01 20:59:00+00:00,2025-01-30 18:59:00+00:00,0.2,0.712,0.2193,-0.269
548,"-1*Minus(ts_Rank(df['p3_mf10'],3),Minus(Or(df['p2_et0'],df['p6_tn13']),ts_Scale(df['p6_tn13'],7)))","643_-1*Minus(ts_Rank(df['p3_mf10'],3),Minus(Or(df['p2_et0'],df['p6_tn13']),ts_Scale(df['p6_tn13'],7)))",12.6678,0.0047,1.1036,4.1953,0.45447992507481877,2024-01-01 20:59:00+00:00,2025-01-30 18:59:00+00:00,0.543,0.651,0.4548,-0.046
549,"get_CCI(Abs(Log(df['p4_ms5'])),df['di'],df['p4_ms2'],21)","644_get_CCI(Abs(Log(df['p4_ms5'])),df['di'],df['p4_ms2'],21)",11.6177,0.0053,1.0257,3.2878,0.4911005056246609,2024-01-01 20:59:00+00:00,2025-01-30 18:59:00+00:00,0.413,0.697,0.3721,-0.199
550,"-1*Minus(Softsign(df['kama']),get_CCI(df['p4_ms2'],pn_Rank(df['p2_et3']),ts_Corr2(df['p1_corrs9'],df['p3_mf9'],8),49))","647_-1*Minus(Softsign(df['kama']),get_CCI(df['p4_ms2'],pn_Rank(df['p2_et3']),ts_Corr2(df['p1_corrs9'],df['p3_mf9'],8),49))",17.6797,0.0045,1.6441,3.0422,0.4542345298752626,2024-01-01 20:59:00+00:00,2025-01-30 18:59:00+00:00,0.432,1.347,0.2428,-0.103
551,"get_CCI(ts_Scale(pn_Rank2(df['liangle']),27),get_CCI(df['di'],Abs(df['di']),ts_CorrChg(df['p6_tn2'],df['p6_tn6'],21),49),ts_Corr2(df['p2_et2'],df['p1_corrs8'],22),21)","648_get_CCI(ts_Scale(pn_Rank2(df['liangle']),27),get_CCI(df['di'],Abs(df['di']),ts_CorrChg(df['p6_tn2'],df['p6_tn6'],21),49),ts_Corr2(df['p2_et2'],df['p1_corrs8'],22),21)",15.4576,0.0059,1.4099,3.1929,0.5125450486332463,2024-01-01 20:59:00+00:00,2025-01-30 18:59:00+00:00,0.315,1.211,0.2064,-0.377
552,"get_CCI(df['di'],ts_Entropy(df['p1_corrs6'],36),pn_FillMax(pn_Rank(df['di'])),13)","646_get_CCI(df['di'],ts_Entropy(df['p1_corrs6'],36),pn_FillMax(pn_Rank(df['di'])),13)",21.9222,0.007,2.0423,3.4437,0.5925730025119903,2024-01-01 20:59:00+00:00,2025-01-30 18:59:00+00:00,0.925,1.285,0.4186,-0.355
553,"get_CCI(Not(Exp(df['p5_to6'])),pn_GroupNeutral(df['di'],df['liangle']),ts_CorrChg(Exp(df['p2_et7']),get_LINEARREG_ANGLE(df['p5_to1'],27),41),49)","647_get_CCI(Not(Exp(df['p5_to6'])),pn_GroupNeutral(df['di'],df['liangle']),ts_CorrChg(Exp(df['p2_et7']),get_LINEARREG_ANGLE(df['p5_to1'],27),41),49)",10.9667,0.0052,0.9651,3.1712,0.5456523916903918,2024-01-01 20:59:00+00:00,2025-01-30 18:59:00+00:00,0.431,0.614,0.4124,-0.264
554,"-1*get_CCI(pn_CrossFit(ts_Scale(df['p3_mf9'],42),pn_GroupNorm(df['p3_mf9'],df['p6_tn13'])),ts_Median(df['p5_to0'],21),df['p2_et2'],49)","648_-1*get_CCI(pn_CrossFit(ts_Scale(df['p3_mf9'],42),pn_GroupNorm(df['p3_mf9'],df['p6_tn13'])),ts_Median(df['p5_to0'],21),df['p2_et2'],49)",11.18,0.0041,0.9762,3.6379,0.4967745887804259,2024-01-01 20:59:00+00:00,2025-01-30 18:59:00+00:00,0.31,0.747,0.2933,-0.14
555,"-1*get_CCI(ts_Entropy(pn_GroupRank(df['p6_tn12'],df['p1_corrs5']),36),df['p6_tn13'],get_MINUS_DM(df['p2_et6'],df['p3_mf2'],25),27)","649_-1*get_CCI(ts_Entropy(pn_GroupRank(df['p6_tn12'],df['p1_corrs5']),36),df['p6_tn13'],get_MINUS_DM(df['p2_et6'],df['p3_mf2'],25),27)",21.4613,0.0073,1.9982,3.3364,0.48220355369226514,2024-01-01 20:59:00+00:00,2025-01-30 18:59:00+00:00,0.535,1.627,0.2475,-0.149
556,"get_CCI(ts_StdevChg(df['p4_ms2'],38),df['p6_tn7'],ts_Decay(df['p4_ms2'],40),10)","651_get_CCI(ts_StdevChg(df['p4_ms2'],38),df['p6_tn7'],ts_Decay(df['p4_ms2'],40),10)",10.9378,0.0043,0.9661,3.1939,0.5421870563643066,2024-01-01 20:59:00+00:00,2025-01-30 18:59:00+00:00,0.306,0.739,0.2928,-0.24
557,"-1*get_CCI(df['cci'],pn_TransNorm(df['p3_mf4']),ts_CorrChg(df['p4_ms3'],Exp(df['p4_ms3']),30),10)","652_-1*get_CCI(df['cci'],pn_TransNorm(df['p3_mf4']),ts_CorrChg(df['p4_ms3'],Exp(df['p4_ms3']),30),10)",18.4495,0.0056,1.7134,3.1098,0.586127499425312,2024-01-01 20:59:00+00:00,2025-01-30 18:59:00+00:00,0.701,1.153,0.3781,-0.244
558,"get_CCI(ts_Median(pn_Cut(df['p3_mf5']),11),pn_TransStd(df['dcperiod']),Not(df['p6_tn7']),10)","653_get_CCI(ts_Median(pn_Cut(df['p3_mf5']),11),pn_TransStd(df['dcperiod']),Not(df['p6_tn7']),10)",10.3913,0.001,0.9268,3.2127,0.1874912754487234,2024-01-01 20:59:00+00:00,2025-01-30 18:59:00+00:00,0.292,0.711,0.2911,0.005
559,"get_CCI(df['p4_ms2'],ts_MeanChg(ts_Mean(df['p1_corrs6'],38),44),Sqrt(get_LINEARREG_SLOPE(df['p3_mf5'],37)),19)","654_get_CCI(df['p4_ms2'],ts_MeanChg(ts_Mean(df['p1_corrs6'],38),44),Sqrt(get_LINEARREG_SLOPE(df['p3_mf5'],37)),19)",10.1067,0.004,0.8748,3.4835,0.573592678885915,2024-01-01 20:59:00+00:00,2025-01-30 18:59:00+00:00,0.329,0.618,0.3474,-0.083
560,"-1*get_CCI(ts_ChgRate(df['p3_mf4'],7),df['p2_et16'],Max(pn_TransNorm(df['p6_tn4']),0.779),10)","655_-1*get_CCI(ts_ChgRate(df['p3_mf4'],7),df['p2_et16'],Max(pn_TransNorm(df['p6_tn4']),0.779),10)",13.7046,0.0055,1.2252,3.529,0.5072794193396328,2024-01-01 20:59:00+00:00,2025-01-30 18:59:00+00:00,0.561,0.765,0.4231,-0.246
561,"-1*get_CCI(get_HT_DCPHASE(pn_GroupNorm(df['p3_mf10'],df['dm'])),df['p4_ms2'],df['cci'],37)","656_-1*get_CCI(get_HT_DCPHASE(pn_GroupNorm(df['p3_mf10'],df['dm'])),df['p4_ms2'],df['cci'],37)",18.0417,0.0063,1.6665,3.186,0.43748697216850496,2024-01-01 20:59:00+00:00,2025-01-30 18:59:00+00:00,0.565,1.239,0.3132,-0.256
562,"get_CCI(df['p6_tn7'],get_HT_DCPHASE(df['dcperiod']),pn_Rank2(df['p2_et11']),19)","657_get_CCI(df['p6_tn7'],get_HT_DCPHASE(df['dcperiod']),pn_Rank2(df['p2_et11']),19)",14.0489,0.0033,1.2844,3.1169,0.4116580914653075,2024-01-01 20:59:00+00:00,2025-01-30 18:59:00+00:00,0.435,0.955,0.3129,-0.128
563,"get_CCI(df['p5_to6'],pn_FillMax(ts_Stdev(df['p3_mf3'],35)),get_KAMA(df['p4_ms2'],11),19)","658_get_CCI(df['p5_to6'],pn_FillMax(ts_Stdev(df['p3_mf3'],35)),get_KAMA(df['p4_ms2'],11),19)",15.204,0.0057,1.3566,4.0548,0.552594652816229,2024-01-01 20:59:00+00:00,2025-01-30 18:59:00+00:00,0.672,0.796,0.4578,-0.223
564,"get_CCI(df['p4_ms2'],inv(df['p3_mf3']),df['p2_et11'],19)","659_get_CCI(df['p4_ms2'],inv(df['p3_mf3']),df['p2_et11'],19)",10.3872,0.0044,0.9073,3.2857,0.5671430125933594,2024-01-01 20:59:00+00:00,2025-01-30 18:59:00+00:00,0.633,0.349,0.6446,-0.209
565,"-1*get_CCI(df['p6_tn13'],And(get_HT_DCPERIOD(df['dcperiod']),df['p6_tn6']),pn_Rank2(get_HT_DCPHASE(df['p4_ms0'])),19)","659_-1*get_CCI(df['p6_tn13'],And(get_HT_DCPERIOD(df['dcperiod']),df['p6_tn6']),pn_Rank2(get_HT_DCPHASE(df['p4_ms0'])),19)",21.2424,0.0076,1.9703,3.4773,0.5657505863181582,2024-01-01 20:59:00+00:00,2025-01-30 18:59:00+00:00,0.938,1.194,0.44,-0.151
566,"-1*get_CCI(ts_Stdev(df['p2_et11'],35),get_KAMA(SignedPower(df['p6_tn13'],0.224),12),ts_Decay2(Max(df['p1_corrs7'],0.974),14),19)","660_-1*get_CCI(ts_Stdev(df['p2_et11'],35),get_KAMA(SignedPower(df['p6_tn13'],0.224),12),ts_Decay2(Max(df['p1_corrs7'],0.974),14),19)",14.3185,0.0039,1.288,3.7245,0.5325351629219454,2024-01-01 20:59:00+00:00,2025-01-30 18:59:00+00:00,0.362,1.032,0.2597,-0.223
567,"-1*get_CCI(ts_Mean(df['p5_to0'],43),Sqrt(ts_Corr(df['p5_to0'],df['p3_mf10'],11)),Softsign(Abs(df['p3_mf4'])),19)","660_-1*get_CCI(ts_Mean(df['p5_to0'],43),Sqrt(ts_Corr(df['p5_to0'],df['p3_mf10'],11)),Softsign(Abs(df['p3_mf4'])),19)",12.3532,0.0045,1.11,3.0878,0.598530837602199,2024-01-01 20:59:00+00:00,2025-01-30 18:59:00+00:00,0.591,0.611,0.4917,-0.223
568,"-1*get_CCI(pn_Winsor(df['cci'],31),df['p2_et15'],pn_FillMax(ts_Regression(df['p6_tn7'],df['p1_corrs6'],34,'D')),10)","661_-1*get_CCI(pn_Winsor(df['cci'],31),df['p2_et15'],pn_FillMax(ts_Regression(df['p6_tn7'],df['p1_corrs6'],34,'D')),10)",19.4272,0.0056,1.7951,3.5845,0.5823508342860887,2024-01-01 20:59:00+00:00,2025-01-30 18:59:00+00:00,0.521,1.422,0.2681,-0.262
569,"get_CCI(ts_Corr2(df['p1_corrs6'],Not(df['dcperiod']),26),Reverse(df['p2_et2']),df['dm'],12)","663_get_CCI(ts_Corr2(df['p1_corrs6'],Not(df['dcperiod']),26),Reverse(df['p2_et2']),df['dm'],12)",15.4638,0.0051,1.3814,4.1815,0.441649526805217,2024-01-01 20:59:00+00:00,2025-01-30 18:59:00+00:00,0.687,0.808,0.4595,-0.101
570,"-1*get_CCI(ts_Quantile(df['p2_et16'],31,'C'),Equal(df['p2_et3'],df['p2_et13']),df['p2_et4'],19)","664_-1*get_CCI(ts_Quantile(df['p2_et16'],31,'C'),Equal(df['p2_et3'],df['p2_et13']),df['p2_et4'],19)",15.5825,0.0045,1.4303,3.1562,0.5766495759702593,2024-01-01 20:59:00+00:00,2025-01-30 18:59:00+00:00,0.314,1.234,0.2028,-0.119
571,"-1*get_CCI(ts_Sum(df['p3_mf11'],50),Min(df['p4_ms2'],df['p3_mf7']),ts_Min(df['p2_et12'],14),12)","665_-1*get_CCI(ts_Sum(df['p3_mf11'],50),Min(df['p4_ms2'],df['p3_mf7']),ts_Min(df['p2_et12'],14),12)",13.8628,0.0053,1.2466,3.3994,0.5603928734618197,2024-01-01 20:59:00+00:00,2025-01-30 18:59:00+00:00,0.339,1.01,0.2513,-0.258
572,"-1*get_CCI(pn_Rank2(ts_Quantile(df['p6_tn5'],21,'B')),ts_Stdev(pn_Rank2(df['p6_tn1']),35),df['cci'],30)","665_-1*get_CCI(pn_Rank2(ts_Quantile(df['p6_tn5'],21,'B')),ts_Stdev(pn_Rank2(df['p6_tn1']),35),df['cci'],30)",22.8865,0.0069,2.1339,3.6095,0.4721818950666711,2024-01-01 20:59:00+00:00,2025-01-30 18:59:00+00:00,0.841,1.468,0.3642,-0.306
573,"-1*get_CCI(pn_CrossFit(df['p2_et17'],df['p2_et3']),Sign(And(df['p2_et4'],df['dm'])),df['cci'],30)","666_-1*get_CCI(pn_CrossFit(df['p2_et17'],df['p2_et3']),Sign(And(df['p2_et4'],df['dm'])),df['cci'],30)",22.3724,0.0077,2.0868,3.3537,0.5460058579153945,2024-01-01 20:59:00+00:00,2025-01-30 18:59:00+00:00,0.595,1.663,0.2635,-0.359
574,"-1*get_CCI(df['cci'],ts_Decay(df['p1_corrs6'],14),Softsign(df['p3_mf5']),19)","667_-1*get_CCI(df['cci'],ts_Decay(df['p1_corrs6'],14),Softsign(df['p3_mf5']),19)",12.2732,0.0044,1.094,3.3337,0.5509136962463991,2024-01-01 20:59:00+00:00,2025-01-30 18:59:00+00:00,0.62,0.564,0.5236,-0.178
575,"-1*get_KAMA(get_CCI(df['adosc'],df['dx'],df['p6_tn10'],43),40)","669_-1*get_KAMA(get_CCI(df['adosc'],df['dx'],df['p6_tn10'],43),40)",11.0506,0.0041,0.9618,3.6784,0.21613030968912753,2024-01-01 20:59:00+00:00,2025-01-30 18:59:00+00:00,0.493,0.548,0.4736,-0.041
576,"-1*pn_Winsor(ts_MeanChg(df['p4_ms2'],16),37)","670_-1*pn_Winsor(ts_MeanChg(df['p4_ms2'],16),37)",14.8887,0.0055,1.3328,3.8584,0.5970220854399237,2024-01-01 20:59:00+00:00,2025-01-30 18:59:00+00:00,0.55,0.893,0.3812,-0.282
577,"-1*pn_CrossFit(Abs(pn_Rank(df['p6_tn1'])),ts_Delta(df['p6_tn4'],10))","671_-1*pn_CrossFit(Abs(pn_Rank(df['p6_tn1'])),ts_Delta(df['p6_tn4'],10))",12.445,0.0056,1.082,4.0294,0.49764136496254463,2024-01-01 20:59:00+00:00,2025-01-30 18:59:00+00:00,0.653,0.517,0.5581,-0.087
578,"-1*pn_CrossFit(Minus(df['p1_corrs3'],7),get_LINEARREG_SLOPE(pn_Rank(df['p6_tn1']),9))","672_-1*pn_CrossFit(Minus(df['p1_corrs3'],7),get_LINEARREG_SLOPE(pn_Rank(df['p6_tn1']),9))",12.406,0.0035,1.1121,3.3233,0.4856804528919156,2024-01-01 20:59:00+00:00,2025-01-30 18:59:00+00:00,0.425,0.779,0.353,-0.211
579,"pn_Stand(pn_GroupRank(ts_Delta(df['di'],42),pn_GroupNeutral(df['liangle'],df['p4_ms4'])))","672_pn_Stand(pn_GroupRank(ts_Delta(df['di'],42),pn_GroupNeutral(df['liangle'],df['p4_ms4'])))",11.4068,0.0047,1.0064,3.3248,0.5631880446911635,2024-01-01 20:59:00+00:00,2025-01-30 18:59:00+00:00,0.385,0.705,0.3532,-0.294
580,"pn_CrossFit(ts_Corr2(df['p6_tn1'],df['p2_et8'],8),pn_GroupNorm(get_LINEARREG_SLOPE(df['p6_tn0'],10),get_MINUS_DM(df['p3_mf5'],df['p1_corrs3'],22)))","671_pn_CrossFit(ts_Corr2(df['p6_tn1'],df['p2_et8'],8),pn_GroupNorm(get_LINEARREG_SLOPE(df['p6_tn0'],10),get_MINUS_DM(df['p3_mf5'],df['p1_corrs3'],22)))",16.2896,0.0069,1.4339,4.813,0.5860956202130126,2024-01-01 20:59:00+00:00,2025-01-30 18:59:00+00:00,0.682,0.87,0.4394,-0.4
581,"get_CMO(pn_Rank(ts_Argmax(df['p1_corrs1'],9)),9)","672_get_CMO(pn_Rank(ts_Argmax(df['p1_corrs1'],9)),9)",14.5884,0.0037,1.3119,3.8526,0.32382123697849935,2024-01-01 20:59:00+00:00,2025-01-30 18:59:00+00:00,0.325,1.095,0.2289,-0.105
582,"-1*pn_CrossFit(Minus(get_CMO(df['p2_et16'],5),0.116),pn_GroupNorm(ts_Quantile(df['p6_tn13'],0.116,'A'),get_MINUS_DM(df['p3_mf5'],df['p1_corrs3'],22)))","672_-1*pn_CrossFit(Minus(get_CMO(df['p2_et16'],5),0.116),pn_GroupNorm(ts_Quantile(df['p6_tn13'],0.116,'A'),get_MINUS_DM(df['p3_mf5'],df['p1_corrs3'],22)))",11.9032,0.0041,1.0538,3.4863,0.482913874417929,2024-01-01 20:59:00+00:00,2025-01-30 18:59:00+00:00,0.555,0.586,0.4864,-0.165
583,"-1*pn_CrossFit(ts_Argmax(ts_Kurtosis(df['p4_ms5'],19),10),Max(get_LINEARREG_SLOPE(df['cci'],7),df['cci']))","673_-1*pn_CrossFit(ts_Argmax(ts_Kurtosis(df['p4_ms5'],19),10),Max(get_LINEARREG_SLOPE(df['cci'],7),df['cci']))",19.0312,0.0068,1.7405,3.8591,0.5853846326590313,2024-01-01 20:59:00+00:00,2025-01-30 18:59:00+00:00,0.891,0.992,0.4732,-0.434
584,"-1*pn_CrossFit(ts_Stdev(df['dx'],50),Max(get_LINEARREG_SLOPE(df['cci'],10),ts_ChgRate(df['p4_ms3'],7)))","674_-1*pn_CrossFit(ts_Stdev(df['dx'],50),Max(get_LINEARREG_SLOPE(df['cci'],10),ts_ChgRate(df['p4_ms3'],7)))",15.4761,0.0063,1.3942,3.6509,0.5320102583824899,2024-01-01 20:59:00+00:00,2025-01-30 18:59:00+00:00,0.369,1.139,0.2447,-0.282
585,"-1*pn_CrossFit(Minus(df['p6_tn0'],Lthan(df['cci'],df['p2_et19'])),pn_GroupNorm(get_CMO(df['cci'],5),df['p4_ms0']))","675_-1*pn_CrossFit(Minus(df['p6_tn0'],Lthan(df['cci'],df['p2_et19'])),pn_GroupNorm(get_CMO(df['cci'],5),df['p4_ms0']))",9.313,0.0036,0.8129,3.0054,0.544028766447326,2024-01-01 20:59:00+00:00,2025-01-30 18:59:00+00:00,0.489,0.391,0.5557,-0.076
586,"-1*pn_CrossFit(Softsign(df['p4_ms0']),pn_GroupNorm(get_LINEARREG_SLOPE(df['cci'],7),ts_Max(df['adosc'],27)))","676_-1*pn_CrossFit(Softsign(df['p4_ms0']),pn_GroupNorm(get_LINEARREG_SLOPE(df['cci'],7),ts_Max(df['adosc'],27)))",13.1489,0.0049,1.1661,3.7241,0.5994235809552362,2024-01-01 20:59:00+00:00,2025-01-30 18:59:00+00:00,0.499,0.763,0.3954,-0.396
587,"-1*pn_Stand(pn_GroupRank(get_CMO(df['p3_mf3'],2),ts_Cov2(df['p6_tn4'],df['p3_mf8'],45)))","677_-1*pn_Stand(pn_GroupRank(get_CMO(df['p3_mf3'],2),ts_Cov2(df['p6_tn4'],df['p3_mf8'],45)))",9.3855,0.0042,0.8172,3.014,0.5468288181952308,2024-01-01 20:59:00+00:00,2025-01-30 18:59:00+00:00,0.506,0.378,0.5724,-0.336
588,"-1*pn_CrossFit(get_CMO(df['p6_tn0'],2),Minus(df['cci'],ts_Mean(df['dx'],20)))","678_-1*pn_CrossFit(get_CMO(df['p6_tn0'],2),Minus(df['cci'],ts_Mean(df['dx'],20)))",16.9922,0.0065,1.5499,3.5025,0.582640436727633,2024-01-01 20:59:00+00:00,2025-01-30 18:59:00+00:00,0.67,1.008,0.3993,-0.513
589,"pn_CrossFit(ts_Argmax(get_CMO(df['cci'],5),10),pn_GroupNorm(df['di'],df['p6_tn0']))","680_pn_CrossFit(ts_Argmax(get_CMO(df['cci'],5),10),pn_GroupNorm(df['di'],df['p6_tn0']))",10.2657,0.0054,0.8964,3.088,0.5419425247953195,2024-01-01 20:59:00+00:00,2025-01-30 18:59:00+00:00,0.532,0.438,0.5485,-0.257
590,"-1*pn_CrossFit(Sign(ts_ChgRate(df['liangle'],6)),pn_GroupRank(get_CMO(df['liangle'],19),Min(df['p3_mf4'],df['p4_ms4'])))","680_-1*pn_CrossFit(Sign(ts_ChgRate(df['liangle'],6)),pn_GroupRank(get_CMO(df['liangle'],19),Min(df['p3_mf4'],df['p4_ms4'])))",10.7801,0.0044,0.9415,3.4359,0.5555525414975112,2024-01-01 20:59:00+00:00,2025-01-30 18:59:00+00:00,0.536,0.483,0.526,-0.479
591,"-1*pn_CrossFit(ts_Stdev(df['p6_tn0'],14),ts_Argmax(Minus(df['p6_tn0'],23),10))","681_-1*pn_CrossFit(ts_Stdev(df['p6_tn0'],14),ts_Argmax(Minus(df['p6_tn0'],23),10))",9.4456,0.0044,0.8193,3.095,0.41275063905282877,2024-01-01 20:59:00+00:00,2025-01-30 18:59:00+00:00,0.303,0.583,0.342,-0.171
592,"-1*pn_CrossFit(Max(get_CCI(df['p3_mf3'],df['p4_ms0'],df['p4_ms3'],26),Min(df['p6_tn1'],df['p3_mf3'])),Max(get_CCI(df['p3_mf3'],df['p6_tn13'],df['p4_ms3'],2),df['cci']))","683_-1*pn_CrossFit(Max(get_CCI(df['p3_mf3'],df['p4_ms0'],df['p4_ms3'],26),Min(df['p6_tn1'],df['p3_mf3'])),Max(get_CCI(df['p3_mf3'],df['p6_tn13'],df['p4_ms3'],2),df['cci']))",14.6131,0.0056,1.3307,3.0708,0.5727683265132303,2024-01-01 20:59:00+00:00,2025-01-30 18:59:00+00:00,0.811,0.629,0.5632,-0.292
593,"-1*pn_CrossFit(pn_GroupNeutral(df['p5_to5'],get_CCI(df['p1_corrs0'],df['liangle'],df['p6_tn10'],10)),pn_GroupNorm(get_LINEARREG_SLOPE(df['p2_et4'],10),pn_FillMax(df['p6_tn10'])))","684_-1*pn_CrossFit(pn_GroupNeutral(df['p5_to5'],get_CCI(df['p1_corrs0'],df['liangle'],df['p6_tn10'],10)),pn_GroupNorm(get_LINEARREG_SLOPE(df['p2_et4'],10),pn_FillMax(df['p6_tn10'])))",13.1419,0.0046,1.1899,3.0446,0.5652772096326063,2024-01-01 20:59:00+00:00,2025-01-30 18:59:00+00:00,0.41,0.878,0.3183,-0.26
594,"-1*pn_CrossFit(get_LINEARREG_SLOPE(ts_Max(df['p4_ms5'],7),11),pn_GroupNorm(get_LINEARREG_SLOPE(df['p2_et4'],7),FilterInf(df['p1_corrs6'])))","684_-1*pn_CrossFit(get_LINEARREG_SLOPE(ts_Max(df['p4_ms5'],7),11),pn_GroupNorm(get_LINEARREG_SLOPE(df['p2_et4'],7),FilterInf(df['p1_corrs6'])))",11.5679,0.0044,1.0252,3.2865,0.518034496729643,2024-01-01 20:59:00+00:00,2025-01-30 18:59:00+00:00,0.507,0.603,0.4568,-0.195
595,"-1*pn_CrossFit(ts_Stdev2(ts_Kurtosis(df['p2_et19'],47),41),pn_GroupNorm(get_LINEARREG_SLOPE(df['p6_tn13'],7),get_MINUS_DM(df['p3_mf9'],df['p3_mf3'],22)))","685_-1*pn_CrossFit(ts_Stdev2(ts_Kurtosis(df['p2_et19'],47),41),pn_GroupNorm(get_LINEARREG_SLOPE(df['p6_tn13'],7),get_MINUS_DM(df['p3_mf9'],df['p3_mf3'],22)))",11.4152,0.0052,1.0051,3.3172,0.48445347737528527,2024-01-01 20:59:00+00:00,2025-01-30 18:59:00+00:00,0.364,0.724,0.3346,-0.158
596,"-1*Min(df['p2_et4'],ts_Cov2(Power(df['p4_ms5'],30),ts_Sum(df['p2_et7'],39),4))","688_-1*Min(df['p2_et4'],ts_Cov2(Power(df['p4_ms5'],30),ts_Sum(df['p2_et7'],39),4))",13.2861,0.0046,1.2034,3.0615,0.5974876110115522,2024-01-01 20:59:00+00:00,2025-01-30 18:59:00+00:00,0.496,0.806,0.381,-0.107
597,"-1*Min(df['p6_tn1'],pn_GroupNorm(ts_TransNorm(df['p6_tn7'],38),ts_TransNorm(df['dcphase'],38)))","689_-1*Min(df['p6_tn1'],pn_GroupNorm(ts_TransNorm(df['p6_tn7'],38),ts_TransNorm(df['dcphase'],38)))",10.6913,0.0055,0.9373,3.1361,0.5630317311166255,2024-01-01 20:59:00+00:00,2025-01-30 18:59:00+00:00,0.293,0.722,0.2887,-0.272
598,"-1*Min(ts_TransNorm(pn_Rank2(df['p6_tn4']),23),pn_GroupNorm(df['cmo'],IfThen(df['p5_to3'],49,28)))","688_-1*Min(ts_TransNorm(pn_Rank2(df['p6_tn4']),23),pn_GroupNorm(df['cmo'],IfThen(df['p5_to3'],49,28)))",14.5373,0.0058,1.3035,3.632,0.5722085885439465,2024-01-01 20:59:00+00:00,2025-01-30 18:59:00+00:00,0.894,0.517,0.6336,-0.423
599,"-1*Min(get_CMO(df['dx'],13),pn_Stand(df['p4_ms5']))","689_-1*Min(get_CMO(df['dx'],13),pn_Stand(df['p4_ms5']))",16.4625,0.0061,1.5024,3.3993,0.47304647127544897,2024-01-01 20:59:00+00:00,2025-01-30 18:59:00+00:00,0.54,1.086,0.3321,-0.182
600,"-1*Min(Min(df['p3_mf6'],df['p4_ms5']),ts_Median(ts_TransNorm(df['p3_mf11'],13),39))","689_-1*Min(Min(df['p3_mf6'],df['p4_ms5']),ts_Median(ts_TransNorm(df['p3_mf11'],13),39))",18.1971,0.0059,1.6705,3.5871,0.563015091684309,2024-01-01 20:59:00+00:00,2025-01-30 18:59:00+00:00,0.414,1.394,0.229,-0.197
601,"-1*Min(ts_Delay(df['p3_mf11'],35),pn_GroupNorm(df['p2_et1'],df['p6_tn12']))","689_-1*Min(ts_Delay(df['p3_mf11'],35),pn_GroupNorm(df['p2_et1'],df['p6_tn12']))",11.5782,0.0047,1.016,3.5445,0.5453119356913423,2024-01-01 20:59:00+00:00,2025-01-30 18:59:00+00:00,0.525,0.574,0.4777,-0.125
602,"Min(get_HT_DCPHASE(df['p3_mf6']),Reverse(df['p3_mf11']))","690_Min(get_HT_DCPHASE(df['p3_mf6']),Reverse(df['p3_mf11']))",12.8579,0.0045,1.1627,3.011,0.5938485973236765,2024-01-01 20:59:00+00:00,2025-01-30 18:59:00+00:00,0.624,0.634,0.496,-0.096
603,"-1*Min(pn_TransNorm(ts_Delay(df['p3_mf11'],35)),pn_TransNorm(df['p4_ms5']))","691_-1*Min(pn_TransNorm(ts_Delay(df['p3_mf11'],35)),pn_TransNorm(df['p4_ms5']))",19.8221,0.0056,1.8303,3.7157,0.5342686498853592,2024-01-01 20:59:00+00:00,2025-01-30 18:59:00+00:00,0.447,1.534,0.2256,-0.12
604,"-1*Min(ts_Delay(df['p1_corrs0'],2),get_CMO(df['p4_ms5'],49))","692_-1*Min(ts_Delay(df['p1_corrs0'],2),get_CMO(df['p4_ms5'],49))",17.9262,0.006,1.6538,3.2678,0.5802074903088512,2024-01-01 20:59:00+00:00,2025-01-30 18:59:00+00:00,0.559,1.23,0.3125,-0.145
605,"get_CCI(df['dm'],MEthan(df['dcphase'],df['adosc']),ts_CorrChg(ts_Kurtosis(df['p2_et10'],47),df['p6_tn0'],16),32)","693_get_CCI(df['dm'],MEthan(df['dcphase'],df['adosc']),ts_CorrChg(ts_Kurtosis(df['p2_et10'],47),df['p6_tn0'],16),32)",12.3394,0.0027,1.1147,3.1707,0.512712427973831,2024-01-01 20:59:00+00:00,2025-01-30 18:59:00+00:00,0.248,0.958,0.2056,-0.132
606,"get_CCI(df['dm'],pn_CrossFit(df['p2_et7'],df['p2_et5']),ts_CorrChg(Min(df['p2_et19'],0.461),ts_Sum(df['p2_et2'],3),50),32)","694_get_CCI(df['dm'],pn_CrossFit(df['p2_et7'],df['p2_et5']),ts_CorrChg(Min(df['p2_et19'],0.461),ts_Sum(df['p2_et2'],3),50),32)",18.4393,0.0047,1.7153,3.1574,0.4460622825369402,2024-01-01 20:59:00+00:00,2025-01-30 18:59:00+00:00,0.858,0.998,0.4623,-0.091
607,"ts_StdevChg(get_DX(df['p3_mf8'],ts_Corr2(df['p3_mf10'],df['p1_corrs8'],37),Lthan(df['p6_tn0'],df['p1_corrs8']),42),11)","695_ts_StdevChg(get_DX(df['p3_mf8'],ts_Corr2(df['p3_mf10'],df['p1_corrs8'],37),Lthan(df['p6_tn0'],df['p1_corrs8']),42),11)",10.1496,0.0028,0.8992,3.0579,0.2959838684872757,2024-01-01 20:59:00+00:00,2025-01-30 18:59:00+00:00,0.34,0.633,0.3494,-0.069
608,"get_CCI(df['dm'],MEthan(ts_Mean(df['dx'],17),SignedPower(df['p1_corrs8'],df['p2_et6'])),pn_FillMin(pn_RankCentered(df['dcphase'])),32)","696_get_CCI(df['dm'],MEthan(ts_Mean(df['dx'],17),SignedPower(df['p1_corrs8'],df['p2_et6'])),pn_FillMin(pn_RankCentered(df['dcphase'])),32)",21.0591,0.0048,1.9598,3.6699,0.5366520016930031,2024-01-01 20:59:00+00:00,2025-01-30 18:59:00+00:00,0.681,1.439,0.3212,-0.081
609,"-1*Multiply(ts_TransNorm(ts_Sum(df['p2_et2'],3),0.461),df['p6_tn10'])","698_-1*Multiply(ts_TransNorm(ts_Sum(df['p2_et2'],3),0.461),df['p6_tn10'])",15.8085,0.0058,1.4305,3.639,0.4227391692392164,2024-01-01 20:59:00+00:00,2025-01-30 18:59:00+00:00,0.826,0.722,0.5336,-0.036
610,"-1*Multiply(get_HT_DCPHASE(pn_GroupNeutral(df['adosc'],df['p2_et9'])),ts_Regression(df['p5_to0'],df['p6_tn10'],50,'C'))","699_-1*Multiply(get_HT_DCPHASE(pn_GroupNeutral(df['adosc'],df['p2_et9'])),ts_Regression(df['p5_to0'],df['p6_tn10'],50,'C'))",11.6235,0.0056,1.0264,3.2447,0.5695821672227731,2024-01-01 20:59:00+00:00,2025-01-30 18:59:00+00:00,0.295,0.816,0.2655,-0.184
611,"-1*Add(pn_GroupRank(pn_Winsor(df['p1_corrs0'],35),ts_Decay(df['p6_tn10'],13)),pn_GroupRank(get_CMO(df['p2_et1'],34),Power(df['p5_to4'],47)))","700_-1*Add(pn_GroupRank(pn_Winsor(df['p1_corrs0'],35),ts_Decay(df['p6_tn10'],13)),pn_GroupRank(get_CMO(df['p2_et1'],34),Power(df['p5_to4'],47)))",12.9898,0.0056,1.1549,3.4774,0.5211453140336039,2024-01-01 20:59:00+00:00,2025-01-30 18:59:00+00:00,0.574,0.676,0.4592,-0.179
612,"-1*Add(pn_TransStd(get_CMO(df['p3_mf11'],39)),inv(Log(df['p3_mf3'])))","701_-1*Add(pn_TransStd(get_CMO(df['p3_mf11'],39)),inv(Log(df['p3_mf3'])))",10.5021,0.0045,0.9008,3.8065,0.5216596877606687,2024-01-01 20:59:00+00:00,2025-01-30 18:59:00+00:00,0.469,0.506,0.481,-0.121
613,"-1*Multiply(df['p2_et9'],get_LINEARREG_ANGLE(df['p6_tn9'],47))","702_-1*Multiply(df['p2_et9'],get_LINEARREG_ANGLE(df['p6_tn9'],47))",11.883,0.0039,1.064,3.1468,0.3585507006481716,2024-01-01 20:59:00+00:00,2025-01-30 18:59:00+00:00,0.333,0.818,0.2893,-0.044
614,"Multiply(Reverse(get_CMO(df['p2_et1'],17)),Power(df['p5_to0'],26))","703_Multiply(Reverse(get_CMO(df['p2_et1'],17)),Power(df['p5_to0'],26))",17.7301,0.0073,1.6063,3.9009,0.5886078890930152,2024-01-01 20:59:00+00:00,2025-01-30 18:59:00+00:00,0.703,1.035,0.4045,-0.134
615,"-1*Multiply(Exp(pn_RankCentered(df['p1_corrs0'])),pn_GroupRank(ts_Decay(df['p6_tn10'],44),ts_Mean(df['p6_tn10'],32)))","704_-1*Multiply(Exp(pn_RankCentered(df['p1_corrs0'])),pn_GroupRank(ts_Decay(df['p6_tn10'],44),ts_Mean(df['p6_tn10'],32)))",15.5088,0.0061,1.389,3.9426,0.550055382986836,2024-01-01 20:59:00+00:00,2025-01-30 18:59:00+00:00,0.638,0.865,0.4245,-0.358
616,"-1*Multiply(Exp(pn_GroupNorm(df['p6_tn2'],df['cci'])),ts_ChgRate(df['p6_tn7'],46))","705_-1*Multiply(Exp(pn_GroupNorm(df['p6_tn2'],df['cci'])),ts_ChgRate(df['p6_tn7'],46))",9.7314,0.0052,0.8441,3.0954,0.48305938985531793,2024-01-01 20:59:00+00:00,2025-01-30 18:59:00+00:00,0.638,0.276,0.698,-0.21
617,"-1*Add(ts_Partial_corr(ts_MeanChg(df['dx'],32),df['p1_corrs9'],ts_Delta(df['p4_ms4'],13),27),pn_TransStd(IfThen(df['p1_corrs9'],37,26)))","705_-1*Add(ts_Partial_corr(ts_MeanChg(df['dx'],32),df['p1_corrs9'],ts_Delta(df['p4_ms4'],13),27),pn_TransStd(IfThen(df['p1_corrs9'],37,26)))",10.7524,0.0046,0.9275,3.7443,0.48736455360855496,2024-01-01 20:59:00+00:00,2025-01-30 18:59:00+00:00,0.58,0.424,0.5777,-0.201
618,"-1*get_CCI(ts_Partial_corr(pn_TransStd(df['cci']),ts_Argmax(df['adosc'],47),ts_Delta(df['p4_ms4'],13),27),Lthan(get_HT_DCPHASE(df['p2_et10']),df['p3_mf12']),ts_TransNorm(pn_Stand(df['p2_et7']),35),37)","707_-1*get_CCI(ts_Partial_corr(pn_TransStd(df['cci']),ts_Argmax(df['adosc'],47),ts_Delta(df['p4_ms4'],13),27),Lthan(get_HT_DCPHASE(df['p2_et10']),df['p3_mf12']),ts_TransNorm(pn_Stand(df['p2_et7']),35),37)",13.9707,0.005,1.2627,3.276,0.5981265515321796,2024-01-01 20:59:00+00:00,2025-01-30 18:59:00+00:00,0.523,0.844,0.3826,-0.502
619,"-1*Multiply(ts_Quantile(ts_ChgRate(df['p6_tn7'],33),47,'C'),df['p1_corrs9'])","710_-1*Multiply(ts_Quantile(ts_ChgRate(df['p6_tn7'],33),47,'C'),df['p1_corrs9'])",9.8543,0.0042,0.8584,3.183,0.5390089650970488,2024-01-01 20:59:00+00:00,2025-01-30 18:59:00+00:00,0.284,0.645,0.3057,-0.273
620,"-1*Multiply(pn_Winsor(df['p1_corrs0'],3),get_HT_DCPHASE(df['p3_mf2']))","711_-1*Multiply(pn_Winsor(df['p1_corrs0'],3),get_HT_DCPHASE(df['p3_mf2']))",10.1392,0.0045,0.8897,3.0581,0.5886462808642879,2024-01-01 20:59:00+00:00,2025-01-30 18:59:00+00:00,0.292,0.671,0.3032,-0.184
621,"Multiply(df['p4_ms5'],ts_Partial_corr(df['p5_to5'],df['p2_et11'],df['p6_tn7'],14))","712_Multiply(df['p4_ms5'],ts_Partial_corr(df['p5_to5'],df['p2_et11'],df['p6_tn7'],14))",11.7859,0.0054,1.0456,3.1796,0.4947047549126701,2024-01-01 20:59:00+00:00,2025-01-30 18:59:00+00:00,0.572,0.56,0.5053,-0.146
622,"Multiply(ts_Kurtosis(df['p1_corrs2'],39),df['cci'])","713_Multiply(ts_Kurtosis(df['p1_corrs2'],39),df['cci'])",10.0224,0.0031,0.8694,3.5206,0.37216825555083316,2024-01-01 20:59:00+00:00,2025-01-30 18:59:00+00:00,0.392,0.549,0.4166,-0.168
623,"-1*Multiply(Divide(df['p2_et4'],df['cci']),df['p3_mf11'])","714_-1*Multiply(Divide(df['p2_et4'],df['cci']),df['p3_mf11'])",10.7537,0.005,0.947,3.0931,0.5147886183878062,2024-01-01 20:59:00+00:00,2025-01-30 18:59:00+00:00,0.364,0.661,0.3551,-0.164
624,"Multiply(ts_Corr2(df['p1_corrs2'],ts_MeanChg(df['p6_tn10'],8),20),df['cci'])","715_Multiply(ts_Corr2(df['p1_corrs2'],ts_MeanChg(df['p6_tn10'],8),20),df['cci'])",11.8143,0.0049,1.0324,3.735,0.44581900547618825,2024-01-01 20:59:00+00:00,2025-01-30 18:59:00+00:00,0.549,0.568,0.4915,-0.199
625,"-1*Multiply(ts_Cov(ts_Kurtosis(df['p2_et7'],14),df['p2_et17'],20),get_CMO(df['p5_to0'],14))","716_-1*Multiply(ts_Cov(ts_Kurtosis(df['p2_et7'],14),df['p2_et17'],20),get_CMO(df['p5_to0'],14))",10.3316,0.0043,0.9016,3.2996,0.352552091299846,2024-01-01 20:59:00+00:00,2025-01-30 18:59:00+00:00,0.384,0.592,0.3934,-0.034
626,"-1*pn_Stand(ts_Delta(pn_CrossFit(df['p1_corrs2'],df['p3_mf11']),46))","717_-1*pn_Stand(ts_Delta(pn_CrossFit(df['p1_corrs2'],df['p3_mf11']),46))",21.027,0.0084,1.8772,5.5084,0.5774825800470892,2024-01-01 20:59:00+00:00,2025-01-30 18:59:00+00:00,0.84,1.192,0.4134,-0.157
627,"-1*pn_GroupNeutral(Multiply(df['cci'],ts_Divide(df['p5_to4'],35)),df['p3_mf10'])","717_-1*pn_GroupNeutral(Multiply(df['cci'],ts_Divide(df['p5_to4'],35)),df['p3_mf10'])",13.8633,0.0062,1.2104,4.3517,0.5820591672836135,2024-01-01 20:59:00+00:00,2025-01-30 18:59:00+00:00,0.608,0.702,0.4641,-0.328
628,"pn_GroupRank(Sqrt(pn_Stand(df['p2_et11'])),Power(df['p5_to2'],47))","718_pn_GroupRank(Sqrt(pn_Stand(df['p2_et11'])),Power(df['p5_to2'],47))",10.6157,0.0032,0.9326,3.3879,0.5966406444646801,2024-01-01 20:59:00+00:00,2025-01-30 18:59:00+00:00,0.923,0.086,0.9148,-0.192
629,"pn_GroupRank(df['di'],pn_GroupNorm(df['di'],ts_ChgRate(df['di'],1)))","719_pn_GroupRank(df['di'],pn_GroupNorm(df['di'],ts_ChgRate(df['di'],1)))",9.9464,0.0052,0.8633,3.1577,0.5368376816989873,2024-01-01 20:59:00+00:00,2025-01-30 18:59:00+00:00,0.216,0.718,0.2313,-0.203
630,"ts_Rank(pn_GroupNorm(df['di'],get_MINUS_DI(df['p2_et5'],df['p2_et4'],df['p4_ms1'],47)),16)","720_ts_Rank(pn_GroupNorm(df['di'],get_MINUS_DI(df['p2_et5'],df['p2_et4'],df['p4_ms1'],47)),16)",14.4358,0.0057,1.2771,4.1322,0.49177166908143605,2024-01-01 20:59:00+00:00,2025-01-30 18:59:00+00:00,0.677,0.705,0.4899,-0.347
631,"pn_GroupRank(get_KAMA(Abs(df['dcperiod']),7),pn_GroupNorm(Sign(df['p5_to2']),df['p2_et4']))","720_pn_GroupRank(get_KAMA(Abs(df['dcperiod']),7),pn_GroupNorm(Sign(df['p5_to2']),df['p2_et4']))",12.9953,0.0034,1.1348,4.4268,0.38888826063318965,2024-01-01 20:59:00+00:00,2025-01-30 18:59:00+00:00,0.692,0.536,0.5635,0.01
632,"pn_GroupRank(get_KAMA(df['p2_et11'],5),pn_GroupNorm(ts_Scale(df['ultosc'],4),ts_MeanChg(df['di'],29)))","721_pn_GroupRank(get_KAMA(df['p2_et11'],5),pn_GroupNorm(ts_Scale(df['ultosc'],4),ts_MeanChg(df['di'],29)))",9.762,0.0035,0.8584,3.0047,0.5268144693647315,2024-01-01 20:59:00+00:00,2025-01-30 18:59:00+00:00,0.573,0.356,0.6168,-0.291
633,"-1*pn_GroupRank(get_KAMA(df['cci'],5),ts_Min(df['p3_mf11'],36))","722_-1*pn_GroupRank(get_KAMA(df['cci'],5),ts_Min(df['p3_mf11'],36))",9.8991,0.0045,0.8667,3.0174,0.516531511883776,2024-01-01 20:59:00+00:00,2025-01-30 18:59:00+00:00,0.505,0.433,0.5384,-0.381
634,"pn_GroupRank(df['p2_et11'],ts_CovChg(pn_Stand(df['p6_tn8']),ts_Kurtosis(df['p1_corrs4'],26),35))","724_pn_GroupRank(df['p2_et11'],ts_CovChg(pn_Stand(df['p6_tn8']),ts_Kurtosis(df['p1_corrs4'],26),35))",12.4566,0.0041,1.1032,3.6533,0.5986305242577982,2024-01-01 20:59:00+00:00,2025-01-30 18:59:00+00:00,0.821,0.373,0.6876,-0.245
635,"pn_GroupRank(IfThen(df['p6_tn10'],3,46),ts_Corr2(df['p1_corrs2'],df['kama'],18))","725_pn_GroupRank(IfThen(df['p6_tn10'],3,46),ts_Corr2(df['p1_corrs2'],df['kama'],18))",9.4327,0.004,0.8147,3.2563,0.4835006153803305,2024-01-01 20:59:00+00:00,2025-01-30 18:59:00+00:00,0.251,0.631,0.2846,-0.144
636,"-1*pn_GroupRank(ts_ChgRate(Softsign(df['p6_tn4']),45),Minus(df['p1_corrs2'],ts_Sum(df['p5_to5'],42)))","726_-1*pn_GroupRank(ts_ChgRate(Softsign(df['p6_tn4']),45),Minus(df['p1_corrs2'],ts_Sum(df['p5_to5'],42)))",11.0689,0.0048,0.9516,3.9458,0.498563182893704,2024-01-01 20:59:00+00:00,2025-01-30 18:59:00+00:00,0.55,0.48,0.534,-0.114
637,"-1*SignedPower(df['p6_tn13'],ts_Delay(ts_Partial_corr(df['p3_mf1'],df['p4_ms3'],df['p1_corrs4'],41),50))","727_-1*SignedPower(df['p6_tn13'],ts_Delay(ts_Partial_corr(df['p3_mf1'],df['p4_ms3'],df['p1_corrs4'],41),50))",10.7432,0.0048,0.9351,3.4608,0.583404187423152,2024-01-01 20:59:00+00:00,2025-01-30 18:59:00+00:00,0.325,0.686,0.3215,-0.127
638,"-1*ts_Delay(Sqrt(Exp(df['p4_ms5'])),36)","729_-1*ts_Delay(Sqrt(Exp(df['p4_ms5'])),36)",16.0455,0.0051,1.4639,3.4577,0.4909412326402554,2024-01-01 20:59:00+00:00,2025-01-30 18:59:00+00:00,0.633,0.951,0.3996,0.007
639,"-1*SignedPower(df['p6_tn13'],ts_Partial_corr(ts_Skewness(df['p3_mf1'],36),ts_TransNorm(df['p4_ms3'],14),pn_TransNorm(df['p1_corrs3']),46))","730_-1*SignedPower(df['p6_tn13'],ts_Partial_corr(ts_Skewness(df['p3_mf1'],36),ts_TransNorm(df['p4_ms3'],14),pn_TransNorm(df['p1_corrs3']),46))",10.9956,0.0047,0.9488,3.8135,0.5464049224507598,2024-01-01 20:59:00+00:00,2025-01-30 18:59:00+00:00,0.276,0.751,0.2687,-0.117
640,"-1*pn_GroupNorm(ts_Rank(get_LINEARREG_SLOPE(df['p6_tn13'],14),34),df['p1_corrs9'])","733_-1*pn_GroupNorm(ts_Rank(get_LINEARREG_SLOPE(df['p6_tn13'],14),34),df['p1_corrs9'])",9.7577,0.0041,0.8443,3.3307,0.4591473726147579,2024-01-01 20:59:00+00:00,2025-01-30 18:59:00+00:00,0.368,0.545,0.4031,-0.138
641,"-1*SignedPower(df['p6_tn13'],ts_Partial_corr(df['p2_et13'],get_DX(df['adosc'],df['adosc'],df['p2_et2'],25),df['p4_ms3'],36))","734_-1*SignedPower(df['p6_tn13'],ts_Partial_corr(df['p2_et13'],get_DX(df['adosc'],df['adosc'],df['p2_et2'],25),df['p4_ms3'],36))",9.7074,0.0048,0.8328,3.4216,0.5568935902263897,2024-01-01 20:59:00+00:00,2025-01-30 18:59:00+00:00,0.329,0.573,0.3647,-0.113
642,"-1*SignedPower(df['p6_tn13'],ts_Partial_corr(df['p2_et2'],get_DX(df['adosc'],df['p2_et1'],df['p2_et2'],25),df['p4_ms3'],46))","735_-1*SignedPower(df['p6_tn13'],ts_Partial_corr(df['p2_et2'],get_DX(df['adosc'],df['p2_et1'],df['p2_et2'],25),df['p4_ms3'],46))",9.3787,0.0044,0.8034,3.3701,0.5512789604797549,2024-01-01 20:59:00+00:00,2025-01-30 18:59:00+00:00,0.207,0.662,0.2382,-0.107
643,"-1*SignedPower(df['p6_tn13'],ts_Partial_corr(ts_Sum(df['p1_corrs0'],10),Exp(df['p6_tn8']),df['p6_tn13'],8))","736_-1*SignedPower(df['p6_tn13'],ts_Partial_corr(ts_Sum(df['p1_corrs0'],10),Exp(df['p6_tn8']),df['p6_tn13'],8))",11.0664,0.005,0.9569,3.7402,0.5690031044720198,2024-01-01 20:59:00+00:00,2025-01-30 18:59:00+00:00,0.453,0.583,0.4373,-0.106
644,"-1*SignedPower(df['dcphase'],ts_Partial_corr(df['p6_tn8'],df['dcphase'],ts_StdevChg(df['dcphase'],39),46))","738_-1*SignedPower(df['dcphase'],ts_Partial_corr(df['p6_tn8'],df['dcphase'],ts_StdevChg(df['dcphase'],39),46))",10.1845,0.0045,0.8931,3.0802,0.36227991192116793,2024-01-01 20:59:00+00:00,2025-01-30 18:59:00+00:00,0.33,0.636,0.3416,-0.086
645,"-1*SignedPower(df['p6_tn13'],ts_Partial_corr(ts_Stdev2(df['p1_corrs9'],5),Abs(df['p6_tn13']),pn_Rank2(df['p4_ms2']),46))","738_-1*SignedPower(df['p6_tn13'],ts_Partial_corr(ts_Stdev2(df['p1_corrs9'],5),Abs(df['p6_tn13']),pn_Rank2(df['p4_ms2']),46))",10.8625,0.0047,0.9392,3.7025,0.58993847928645,2024-01-01 20:59:00+00:00,2025-01-30 18:59:00+00:00,0.395,0.621,0.3888,-0.13
646,"-1*SignedPower(df['p6_tn13'],ts_Partial_corr(df['p2_et6'],ts_Mean(df['p3_mf6'],32),Exp(df['p3_mf2']),14))","738_-1*SignedPower(df['p6_tn13'],ts_Partial_corr(df['p2_et6'],ts_Mean(df['p3_mf6'],32),Exp(df['p3_mf2']),14))",11.4209,0.0053,0.9906,3.7497,0.5643873286003365,2024-01-01 20:59:00+00:00,2025-01-30 18:59:00+00:00,0.458,0.614,0.4272,-0.121
647,"-1*SignedPower(df['p6_tn13'],ts_Partial_corr(ts_Stdev(df['p1_corrs3'],29),ts_StdevChg(df['p6_tn13'],39),Exp(df['p4_ms5']),34))","739_-1*SignedPower(df['p6_tn13'],ts_Partial_corr(ts_Stdev(df['p1_corrs3'],29),ts_StdevChg(df['p6_tn13'],39),Exp(df['p4_ms5']),34))",9.6774,0.0042,0.8311,3.4705,0.5050276441993752,2024-01-01 20:59:00+00:00,2025-01-30 18:59:00+00:00,0.419,0.481,0.4656,-0.109
648,"-1*SignedPower(df['p6_tn13'],ts_Partial_corr(ts_Stdev2(df['p6_tn10'],5),Abs(df['dcphase']),df['p6_tn13'],46))","740_-1*SignedPower(df['p6_tn13'],ts_Partial_corr(ts_Stdev2(df['p6_tn10'],5),Abs(df['dcphase']),df['p6_tn13'],46))",10.3657,0.0046,0.8997,3.4101,0.586020188186341,2024-01-01 20:59:00+00:00,2025-01-30 18:59:00+00:00,0.264,0.709,0.2713,-0.122
649,"-1*SignedPower(df['p6_tn13'],ts_Partial_corr(Minus(df['p5_to3'],df['p3_mf10']),ts_Skewness(df['p1_corrs9'],29),ts_Entropy(df['p6_tn10'],47),36))","741_-1*SignedPower(df['p6_tn13'],ts_Partial_corr(Minus(df['p5_to3'],df['p3_mf10']),ts_Skewness(df['p1_corrs9'],29),ts_Entropy(df['p6_tn10'],47),36))",11.4683,0.0047,0.9844,4.1739,0.5327282850148903,2024-01-01 20:59:00+00:00,2025-01-30 18:59:00+00:00,0.419,0.646,0.3934,-0.112
650,"-1*SignedPower(df['p6_tn13'],ts_Partial_corr(ts_Skewness(df['p3_mf1'],36),df['p6_tn13'],ts_Entropy(df['p6_tn10'],47),36))","742_-1*SignedPower(df['p6_tn13'],ts_Partial_corr(ts_Skewness(df['p3_mf1'],36),df['p6_tn13'],ts_Entropy(df['p6_tn10'],47),36))",9.8864,0.0044,0.847,3.587,0.5949811768253854,2024-01-01 20:59:00+00:00,2025-01-30 18:59:00+00:00,0.275,0.642,0.2999,-0.119
651,"-1*SignedPower(df['p6_tn13'],ts_Partial_corr(df['p1_corrs9'],ts_Sum(df['p1_corrs0'],16),ts_Entropy(df['p6_tn10'],47),36))","743_-1*SignedPower(df['p6_tn13'],ts_Partial_corr(df['p1_corrs9'],ts_Sum(df['p1_corrs0'],16),ts_Entropy(df['p6_tn10'],47),36))",9.3173,0.0041,0.8014,3.2868,0.5573480238836409,2024-01-01 20:59:00+00:00,2025-01-30 18:59:00+00:00,0.316,0.551,0.3645,-0.117
652,"-1*pn_GroupNorm(df['p6_tn13'],pn_GroupNorm(df['p6_tn13'],pn_Rank2(df['di'])))","744_-1*pn_GroupNorm(df['p6_tn13'],pn_GroupNorm(df['p6_tn13'],pn_Rank2(df['di'])))",9.8964,0.0052,0.8572,3.1977,0.5170835834018057,2024-01-01 20:59:00+00:00,2025-01-30 18:59:00+00:00,0.396,0.531,0.4272,-0.186
653,"-1*SignedPower(df['p6_tn13'],ts_Partial_corr(ts_Skewness(df['p3_mf1'],36),ts_Mean(df['p3_mf6'],32),ts_Cov(df['p2_et2'],df['p6_tn5'],1),14))","745_-1*SignedPower(df['p6_tn13'],ts_Partial_corr(ts_Skewness(df['p3_mf1'],36),ts_Mean(df['p3_mf6'],32),ts_Cov(df['p2_et2'],df['p6_tn5'],1),14))",10.9343,0.0048,0.9389,3.9228,0.5433187855939839,2024-01-01 20:59:00+00:00,2025-01-30 18:59:00+00:00,0.434,0.582,0.4272,-0.118
654,"-1*SignedPower(df['p6_tn13'],ts_Partial_corr(pn_Rank(df['p5_to3']),ts_Mean(df['p3_mf6'],36),Exp(df['p3_mf2']),14))","746_-1*SignedPower(df['p6_tn13'],ts_Partial_corr(pn_Rank(df['p5_to3']),ts_Mean(df['p3_mf6'],36),Exp(df['p3_mf2']),14))",11.7373,0.0051,1.0121,4.0849,0.5961024857250085,2024-01-01 20:59:00+00:00,2025-01-30 18:59:00+00:00,0.458,0.637,0.4183,-0.117
655,"-1*SignedPower(df['p6_tn13'],ts_Partial_corr(df['p6_tn13'],ts_Decay2(df['p6_tn8'],23),ts_Cov(df['p5_to3'],df['p3_mf12'],15),34))","747_-1*SignedPower(df['p6_tn13'],ts_Partial_corr(df['p6_tn13'],ts_Decay2(df['p6_tn8'],23),ts_Cov(df['p5_to3'],df['p3_mf12'],15),34))",9.3791,0.0043,0.8121,3.1262,0.533020569924022,2024-01-01 20:59:00+00:00,2025-01-30 18:59:00+00:00,0.274,0.605,0.3117,-0.131
656,"-1*SignedPower(df['p6_tn13'],ts_Partial_corr(Abs(df['p1_corrs5']),ts_Skewness(df['p1_corrs9'],36),ts_Stdev2(df['p6_tn13'],22),34))","748_-1*SignedPower(df['p6_tn13'],ts_Partial_corr(Abs(df['p1_corrs5']),ts_Skewness(df['p1_corrs9'],36),ts_Stdev2(df['p6_tn13'],22),34))",9.9365,0.0046,0.8583,3.3775,0.5493133544836576,2024-01-01 20:59:00+00:00,2025-01-30 18:59:00+00:00,0.457,0.472,0.4919,-0.115
657,"-1*SignedPower(df['p6_tn13'],ts_Partial_corr(pn_Rank(df['p2_et15']),df['p6_tn13'],ts_Stdev2(df['p6_tn13'],22),34))","749_-1*SignedPower(df['p6_tn13'],ts_Partial_corr(pn_Rank(df['p2_et15']),df['p6_tn13'],ts_Stdev2(df['p6_tn13'],22),34))",9.3416,0.0048,0.8071,3.0961,0.589649233645304,2024-01-01 20:59:00+00:00,2025-01-30 18:59:00+00:00,0.19,0.683,0.2176,-0.135
658,"-1*SignedPower(df['p6_tn13'],ts_Partial_corr(Add(df['cmo'],0.737),ts_Decay2(df['p6_tn8'],23),get_CMO(df['liangle'],29),34))","750_-1*SignedPower(df['p6_tn13'],ts_Partial_corr(Add(df['cmo'],0.737),ts_Decay2(df['p6_tn8'],23),get_CMO(df['liangle'],29),34))",14.834,0.0055,1.2992,4.7087,0.5944614174591565,2024-01-01 20:59:00+00:00,2025-01-30 18:59:00+00:00,0.707,0.699,0.5028,-0.135
659,"-1*SignedPower(df['p6_tn13'],ts_Partial_corr(pn_Rank(df['p6_tn13']),ts_Decay2(df['p5_to3'],23),df['p6_tn4'],22))","751_-1*SignedPower(df['p6_tn13'],ts_Partial_corr(pn_Rank(df['p6_tn13']),ts_Decay2(df['p5_to3'],23),df['p6_tn4'],22))",9.6696,0.0043,0.8299,3.4719,0.5534290250365043,2024-01-01 20:59:00+00:00,2025-01-30 18:59:00+00:00,0.243,0.655,0.2706,-0.133
660,"-1*SignedPower(df['p6_tn13'],ts_Partial_corr(ts_Argmax(df['p6_tn5'],22),ts_Decay2(df['p6_tn8'],24),ts_Stdev2(df['p6_tn13'],22),34))","752_-1*SignedPower(df['p6_tn13'],ts_Partial_corr(ts_Argmax(df['p6_tn5'],22),ts_Decay2(df['p6_tn8'],24),ts_Stdev2(df['p6_tn13'],22),34))",9.4037,0.0043,0.8115,3.2249,0.544868320338669,2024-01-01 20:59:00+00:00,2025-01-30 18:59:00+00:00,0.272,0.607,0.3094,-0.117
661,"pn_GroupNorm(pn_GroupNorm(df['p2_et11'],ts_Decay2(df['p6_tn8'],23)),ts_Corr2(get_KAMA(df['p5_to6'],48),df['p5_to5'],39))","753_pn_GroupNorm(pn_GroupNorm(df['p2_et11'],ts_Decay2(df['p6_tn8'],23)),ts_Corr2(get_KAMA(df['p5_to6'],48),df['p5_to5'],39))",11.4779,0.0054,1.0074,3.4039,0.54353803532202,2024-01-01 20:59:00+00:00,2025-01-30 18:59:00+00:00,0.61,0.48,0.5596,-0.239
662,"pn_GroupNorm(Minus(ts_Stdev2(df['p6_tn5'],38),df['p2_et1']),ts_StdevChg(df['dx'],11))","754_pn_GroupNorm(Minus(ts_Stdev2(df['p6_tn5'],38),df['p2_et1']),ts_StdevChg(df['dx'],11))",10.3988,0.0039,0.9118,3.2636,0.3143576236830894,2024-01-01 20:59:00+00:00,2025-01-30 18:59:00+00:00,0.458,0.528,0.4645,0.022
663,"-1*ts_Max(get_CCI(df['p1_corrs1'],ts_CorrChg(df['p4_ms3'],df['p5_to2'],17),ts_Entropy(df['p2_et14'],48),31),2)","755_-1*ts_Max(get_CCI(df['p1_corrs1'],ts_CorrChg(df['p4_ms3'],df['p5_to2'],17),ts_Entropy(df['p2_et14'],48),31),2)",13.1645,0.0047,1.1679,3.7469,0.507726663099733,2024-01-01 20:59:00+00:00,2025-01-30 18:59:00+00:00,0.295,0.969,0.2334,-0.182
664,"Multiply(ts_Divide(df['p2_et12'],19),ts_Min(df['p1_corrs4'],21))","756_Multiply(ts_Divide(df['p2_et12'],19),ts_Min(df['p1_corrs4'],21))",10.6054,0.0033,0.9375,3.201,0.43669140048238375,2024-01-01 20:59:00+00:00,2025-01-30 18:59:00+00:00,0.305,0.71,0.3005,-0.163
665,"-1*get_LINEARREG_SLOPE(Add(Lthan(df['liangle'],df['p6_tn13']),pn_Stand(df['p6_tn12'])),15)","757_-1*get_LINEARREG_SLOPE(Add(Lthan(df['liangle'],df['p6_tn13']),pn_Stand(df['p6_tn12'])),15)",10.5088,0.0051,0.9108,3.4347,0.49418964389485937,2024-01-01 20:59:00+00:00,2025-01-30 18:59:00+00:00,0.433,0.552,0.4396,-0.211
666,"-1*Multiply(get_CCI(df['p1_corrs1'],pn_TransStd(df['p4_ms5']),df['p5_to2'],31),ts_Skewness(df['p6_tn2'],11))","758_-1*Multiply(get_CCI(df['p1_corrs1'],pn_TransStd(df['p4_ms5']),df['p5_to2'],31),ts_Skewness(df['p6_tn2'],11))",9.7962,0.0049,0.8438,3.3374,0.36050960697251244,2024-01-01 20:59:00+00:00,2025-01-30 18:59:00+00:00,0.216,0.697,0.2366,-0.15
667,"Multiply(df['p1_corrs1'],pn_Winsor(ts_Quantile(df['p6_tn0'],19,'B'),20))","760_Multiply(df['p1_corrs1'],pn_Winsor(ts_Quantile(df['p6_tn0'],19,'B'),20))",10.2895,0.0038,0.9018,3.242,0.30907404593914306,2024-01-01 20:59:00+00:00,2025-01-30 18:59:00+00:00,0.385,0.591,0.3945,-0.134
668,"Multiply(df['p1_corrs1'],ts_Regression(df['p1_corrs1'],df['dx'],49,'B'))","761_Multiply(df['p1_corrs1'],ts_Regression(df['p1_corrs1'],df['dx'],49,'B'))",9.7327,0.0029,0.8402,3.554,0.3008123520066354,2024-01-01 20:59:00+00:00,2025-01-30 18:59:00+00:00,0.244,0.666,0.2681,-0.136
669,"-1*Multiply(ts_Argmax(df['dm'],3),get_CCI(df['p2_et14'],df['p3_mf4'],ts_Quantile(df['p1_corrs5'],36,'A'),26))","762_-1*Multiply(ts_Argmax(df['dm'],3),get_CCI(df['p2_et14'],df['p3_mf4'],ts_Quantile(df['p1_corrs5'],36,'A'),26))",12.0348,0.0039,1.0562,3.8276,0.4104671513470099,2024-01-01 20:59:00+00:00,2025-01-30 18:59:00+00:00,0.41,0.733,0.3587,-0.107
670,"Multiply(df['p1_corrs1'],pn_GroupNeutral(df['p3_mf10'],df['p3_mf2']))","763_Multiply(df['p1_corrs1'],pn_GroupNeutral(df['p3_mf10'],df['p3_mf2']))",9.9995,0.0036,0.8674,3.4312,0.22424187543743374,2024-01-01 20:59:00+00:00,2025-01-30 18:59:00+00:00,0.332,0.607,0.3536,-0.03
671,"Multiply(ts_MeanChg(df['p2_et12'],27),df['p1_corrs1'])","764_Multiply(ts_MeanChg(df['p2_et12'],27),df['p1_corrs1'])",12.0396,0.0044,1.0598,3.6585,0.3259181256986305,2024-01-01 20:59:00+00:00,2025-01-30 18:59:00+00:00,0.332,0.815,0.2895,-0.091
672,"-1*Multiply(df['p1_corrs1'],ts_TransNorm(df['p6_tn12'],35))","765_-1*Multiply(df['p1_corrs1'],ts_TransNorm(df['p6_tn12'],35))",11.7864,0.0044,1.0493,3.2269,0.48162801930526317,2024-01-01 20:59:00+00:00,2025-01-30 18:59:00+00:00,0.272,0.863,0.2396,-0.107
673,"-1*Multiply(pn_Rank(df['p1_corrs1']),pn_GroupRank(df['p3_mf6'],df['p6_tn12']))","766_-1*Multiply(pn_Rank(df['p1_corrs1']),pn_GroupRank(df['p3_mf6'],df['p6_tn12']))",10.5409,0.0051,0.9147,3.4195,0.48686131010679456,2024-01-01 20:59:00+00:00,2025-01-30 18:59:00+00:00,0.3,0.69,0.303,-0.255
674,"-1*Multiply(df['p1_corrs1'],Max(pn_GroupNorm(df['di'],df['p1_corrs1']),pn_RankCentered(df['p1_corrs3'])))","767_-1*Multiply(df['p1_corrs1'],Max(pn_GroupNorm(df['di'],df['p1_corrs1']),pn_RankCentered(df['p1_corrs3'])))",11.0734,0.0054,0.955,3.7672,0.5057703013502384,2024-01-01 20:59:00+00:00,2025-01-30 18:59:00+00:00,0.346,0.688,0.3346,-0.258
675,"-1*Multiply(get_CCI(df['p2_et13'],get_KAMA(df['p2_et5'],11),df['p3_mf12'],34),df['p1_corrs1'])","768_-1*Multiply(get_CCI(df['p2_et13'],get_KAMA(df['p2_et5'],11),df['p3_mf12'],34),df['p1_corrs1'])",9.7653,0.0036,0.8414,3.5134,0.36324294731799345,2024-01-01 20:59:00+00:00,2025-01-30 18:59:00+00:00,0.283,0.628,0.3106,-0.067
676,"-1*pn_GroupNorm(Sign(pn_TransStd(df['p2_et1'])),get_MINUS_DM(Exp(df['p2_et16']),pn_Stand(df['p6_tn2']),5))","769_-1*pn_GroupNorm(Sign(pn_TransStd(df['p2_et1'])),get_MINUS_DM(Exp(df['p2_et16']),pn_Stand(df['p6_tn2']),5))",10.0788,0.004,0.8657,3.6639,0.516611978129222,2024-01-01 20:59:00+00:00,2025-01-30 18:59:00+00:00,0.295,0.642,0.3148,-0.117
677,"-1*pn_GroupNeutral(ts_Argmin(df['p2_et7'],10),Add(df['p6_tn11'],0.25))","770_-1*pn_GroupNeutral(ts_Argmin(df['p2_et7'],10),Add(df['p6_tn11'],0.25))",10.4539,0.0049,0.906,3.452,0.4892736273643097,2024-01-01 20:59:00+00:00,2025-01-30 18:59:00+00:00,0.774,0.206,0.7898,-0.316
678,"-1*pn_GroupNeutral(ts_Rank(df['p1_corrs1'],15),df['p2_et15'])","770_-1*pn_GroupNeutral(ts_Rank(df['p1_corrs1'],15),df['p2_et15'])",13.4413,0.0055,1.1681,4.4526,0.5145166152029786,2024-01-01 20:59:00+00:00,2025-01-30 18:59:00+00:00,0.37,0.894,0.2927,-0.261
679,"-1*pn_GroupNeutral(ts_Rank(df['p2_et4'],22),get_MINUS_DM(df['p2_et0'],df['p4_ms0'],13))","768_-1*pn_GroupNeutral(ts_Rank(df['p2_et4'],22),get_MINUS_DM(df['p2_et0'],df['p4_ms0'],13))",13.5576,0.0059,1.1858,4.2134,0.5806982755938345,2024-01-01 20:59:00+00:00,2025-01-30 18:59:00+00:00,0.421,0.862,0.3281,-0.165
680,"-1*pn_GroupNeutral(ts_Argmin(df['p6_tn5'],13),And(df['p2_et7'],df['p5_to6']))","769_-1*pn_GroupNeutral(ts_Argmin(df['p6_tn5'],13),And(df['p2_et7'],df['p5_to6']))",10.0355,0.0042,0.8819,3.0237,0.3805053221213468,2024-01-01 20:59:00+00:00,2025-01-30 18:59:00+00:00,0.517,0.438,0.5414,-0.13
681,"pn_GroupNeutral(df['p5_to5'],get_MINUS_DM(df['p5_to5'],Sqrt(df['p4_ms0']),36))","770_pn_GroupNeutral(df['p5_to5'],get_MINUS_DM(df['p5_to5'],Sqrt(df['p4_ms0']),36))",9.5294,0.002,0.8224,3.621,0.47108189209994383,2024-01-01 20:59:00+00:00,2025-01-30 18:59:00+00:00,0.4,0.49,0.4494,-0.024
682,"pn_GroupNeutral(df['p5_to5'],get_DX(df['dm'],df['p2_et11'],pn_Rank(df['p3_mf7']),46))","771_pn_GroupNeutral(df['p5_to5'],get_DX(df['dm'],df['p2_et11'],pn_Rank(df['p3_mf7']),46))",9.4862,0.0027,0.814,3.6262,0.5055772595175831,2024-01-01 20:59:00+00:00,2025-01-30 18:59:00+00:00,0.347,0.534,0.3939,-0.043
683,"-1*pn_GroupNeutral(ts_Delta(df['cmo'],46),ts_Partial_corr(df['p4_ms3'],pn_Cut(df['p2_et5']),pn_RankCentered(df['p1_corrs6']),44))","771_-1*pn_GroupNeutral(ts_Delta(df['cmo'],46),ts_Partial_corr(df['p4_ms3'],pn_Cut(df['p2_et5']),pn_RankCentered(df['p1_corrs6']),44))",12.2184,0.0062,1.0623,3.8524,0.5630415992413578,2024-01-01 20:59:00+00:00,2025-01-30 18:59:00+00:00,0.307,0.843,0.267,-0.405
684,"pn_GroupNeutral(df['p2_et11'],ts_Min(ts_Min(df['p4_ms0'],26),32))","771_pn_GroupNeutral(df['p2_et11'],ts_Min(ts_Min(df['p4_ms0'],26),32))",13.484,0.0055,1.2047,3.4811,0.5976915708016016,2024-01-01 20:59:00+00:00,2025-01-30 18:59:00+00:00,0.715,0.588,0.5487,-0.245
685,"-1*pn_GroupNeutral(ts_Delta(df['p4_ms5'],46),ts_Delta(df['p1_corrs8'],46))","772_-1*pn_GroupNeutral(ts_Delta(df['p4_ms5'],46),ts_Delta(df['p1_corrs8'],46))",14.4934,0.0067,1.2891,3.8095,0.5780705229419233,2024-01-01 20:59:00+00:00,2025-01-30 18:59:00+00:00,0.664,0.731,0.476,-0.064
686,"-1*pn_GroupNeutral(ts_Min(ts_Divide(df['p6_tn7'],41),5),Divide(df['p2_et16'],Max(df['p5_to5'],df['p2_et4'])))","773_-1*pn_GroupNeutral(ts_Min(ts_Divide(df['p6_tn7'],41),5),Divide(df['p2_et16'],Max(df['p5_to5'],df['p2_et4'])))",10.5085,0.0041,0.917,3.405,0.5781202380090319,2024-01-01 20:59:00+00:00,2025-01-30 18:59:00+00:00,0.538,0.454,0.5423,-0.299
687,"-1*pn_GroupNeutral(ts_Delta(ts_TransNorm(df['dx'],40),46),df['cmo'])","774_-1*pn_GroupNeutral(ts_Delta(ts_TransNorm(df['dx'],40),46),df['cmo'])",10.562,0.0033,0.9157,3.7154,0.37981958934718174,2024-01-01 20:59:00+00:00,2025-01-30 18:59:00+00:00,0.574,0.417,0.5792,-0.031
688,"-1*pn_GroupNeutral(df['cmo'],Min(ts_Regression(df['cmo'],df['p4_ms4'],3,'C'),ts_Decay(df['p2_et1'],13)))","775_-1*pn_GroupNeutral(df['cmo'],Min(ts_Regression(df['cmo'],df['p4_ms4'],3,'C'),ts_Decay(df['p2_et1'],13)))",10.754,0.0049,0.9423,3.2601,0.5667217498754963,2024-01-01 20:59:00+00:00,2025-01-30 18:59:00+00:00,0.495,0.525,0.4853,-0.259
689,"-1*pn_GroupNeutral(ts_Rank(pn_Winsor(df['cmo'],32),9),ts_Skewness(df['p3_mf12'],32))","770_-1*pn_GroupNeutral(ts_Rank(pn_Winsor(df['cmo'],32),9),ts_Skewness(df['p3_mf12'],32))",18.1202,0.0073,1.6139,4.8532,0.5972900856259186,2024-01-01 20:59:00+00:00,2025-01-30 18:59:00+00:00,0.535,1.212,0.3062,-0.388
690,"-1*pn_GroupNeutral(df['p4_ms5'],ts_Max(df['p2_et10'],18))","771_-1*pn_GroupNeutral(df['p4_ms5'],ts_Max(df['p2_et10'],18))",14.5424,0.0062,1.3058,3.5168,0.5910142998237732,2024-01-01 20:59:00+00:00,2025-01-30 18:59:00+00:00,0.639,0.774,0.4522,-0.2
691,"-1*pn_GroupNeutral(ts_Delta(df['p6_tn1'],50),ts_Max(df['p4_ms6'],49))","772_-1*pn_GroupNeutral(ts_Delta(df['p6_tn1'],50),ts_Max(df['p4_ms6'],49))",10.145,0.0039,0.8914,3.113,0.38419102334052685,2024-01-01 20:59:00+00:00,2025-01-30 18:59:00+00:00,0.399,0.566,0.4135,-0.073
692,"-1*pn_GroupNeutral(ts_Delta(ts_Regression(df['cmo'],df['p4_ms4'],3,'C'),26),pn_GroupRank(pn_FillMax(df['p1_corrs8']),df['p2_et4']))","773_-1*pn_GroupNeutral(ts_Delta(ts_Regression(df['cmo'],df['p4_ms4'],3,'C'),26),pn_GroupRank(pn_FillMax(df['p1_corrs8']),df['p2_et4']))",11.4089,0.0053,1.0135,3.0293,0.5626699981962294,2024-01-01 20:59:00+00:00,2025-01-30 18:59:00+00:00,0.355,0.742,0.3236,-0.404
693,"-1*pn_GroupNeutral(df['p1_corrs8'],SignedPower(df['p1_corrs8'],pn_Rank(df['p6_tn1'])))","774_-1*pn_GroupNeutral(df['p1_corrs8'],SignedPower(df['p1_corrs8'],pn_Rank(df['p6_tn1'])))",9.3543,0.0044,0.8109,3.0756,0.5038544425681976,2024-01-01 20:59:00+00:00,2025-01-30 18:59:00+00:00,0.348,0.529,0.3968,-0.143
694,"-1*pn_GroupNeutral(pn_Winsor(df['cmo'],3),df['cmo'])","775_-1*pn_GroupNeutral(pn_Winsor(df['cmo'],3),df['cmo'])",9.7382,0.0033,0.8491,3.2421,0.4364810630959467,2024-01-01 20:59:00+00:00,2025-01-30 18:59:00+00:00,0.428,0.491,0.4657,-0.066
695,"pn_GroupNeutral(ts_Decay2(ts_Delta(df['p2_et11'],41),5),ts_Skewness(df['p3_mf9'],16))","776_pn_GroupNeutral(ts_Decay2(ts_Delta(df['p2_et11'],41),5),ts_Skewness(df['p3_mf9'],16))",11.1574,0.0048,0.9794,3.3758,0.48798874301462547,2024-01-01 20:59:00+00:00,2025-01-30 18:59:00+00:00,0.672,0.388,0.634,-0.316
696,"-1*ts_Mean(ts_Cov2(ts_Skewness(df['adosc'],19),df['p3_mf8'],9),33)","777_-1*ts_Mean(ts_Cov2(ts_Skewness(df['adosc'],19),df['p3_mf8'],9),33)",9.5878,0.0029,0.8371,3.2214,0.413107647203449,2024-01-01 20:59:00+00:00,2025-01-30 18:59:00+00:00,0.259,0.647,0.2859,-0.02
697,"-1*get_CCI(pn_Winsor(df['p3_mf12'],5),pn_FillMax(Max(df['p2_et10'],0.081)),df['p6_tn1'],22)","779_-1*get_CCI(pn_Winsor(df['p3_mf12'],5),pn_FillMax(Max(df['p2_et10'],0.081)),df['p6_tn1'],22)",16.3301,0.0054,1.4877,3.5462,0.592734923148923,2024-01-01 20:59:00+00:00,2025-01-30 18:59:00+00:00,0.71,0.9,0.441,-0.137
698,"-1*get_CCI(Divide(df['p4_ms0'],df['p1_corrs0']),Power(df['p3_mf9'],4),df['p6_tn1'],22)","780_-1*get_CCI(Divide(df['p4_ms0'],df['p1_corrs0']),Power(df['p3_mf9'],4),df['p6_tn1'],22)",11.2765,0.0043,0.9934,3.3752,0.5446227501975598,2024-01-01 20:59:00+00:00,2025-01-30 18:59:00+00:00,0.37,0.705,0.3442,-0.163
699,"-1*get_CCI(pn_Winsor(pn_FillMax(df['p3_mf1']),5),df['dcphase'],ts_MeanChg(df['p6_tn7'],47),15)","781_-1*get_CCI(pn_Winsor(pn_FillMax(df['p3_mf1']),5),df['dcphase'],ts_MeanChg(df['p6_tn7'],47),15)",17.7536,0.0034,1.6583,3.0002,0.3936954533602799,2024-01-01 20:59:00+00:00,2025-01-30 18:59:00+00:00,0.73,1.065,0.4067,-0.052
700,"-1*ts_Scale(Max(df['p1_corrs3'],FilterInf(df['p6_tn1'])),47)","782_-1*ts_Scale(Max(df['p1_corrs3'],FilterInf(df['p6_tn1'])),47)",11.5558,0.0042,1.0104,3.7196,0.5816176357655732,2024-01-01 20:59:00+00:00,2025-01-30 18:59:00+00:00,0.59,0.503,0.5398,-0.215
701,"-1*get_CCI(df['cmo'],df['p6_tn1'],ts_Argmax(df['p1_corrs8'],27),22)","780_-1*get_CCI(df['cmo'],df['p6_tn1'],ts_Argmax(df['p1_corrs8'],27),22)",15.4728,0.0055,1.398,3.6517,0.5809593658492918,2024-01-01 20:59:00+00:00,2025-01-30 18:59:00+00:00,0.389,1.124,0.2571,-0.322
702,"-1*get_CCI(pn_Rank(ts_Quantile(df['ultosc'],9,'C')),pn_Rank(df['p6_tn1']),ts_Corr(pn_Cut(df['p5_to4']),df['p6_tn1'],22),17)","781_-1*get_CCI(pn_Rank(ts_Quantile(df['ultosc'],9,'C')),pn_Rank(df['p6_tn1']),ts_Corr(pn_Cut(df['p5_to4']),df['p6_tn1'],22),17)",13.0451,0.0047,1.173,3.2473,0.5007725796792665,2024-01-01 20:59:00+00:00,2025-01-30 18:59:00+00:00,0.316,0.953,0.249,-0.146
703,"-1*get_CCI(pn_GroupNeutral(df['p6_tn4'],ts_Corr(df['p4_ms6'],df['p1_corrs3'],17)),pn_Rank(Power(df['p6_tn3'],43)),df['p6_tn1'],17)","782_-1*get_CCI(pn_GroupNeutral(df['p6_tn4'],ts_Corr(df['p4_ms6'],df['p1_corrs3'],17)),pn_Rank(Power(df['p6_tn3'],43)),df['p6_tn1'],17)",13.1805,0.0038,1.1915,3.2326,0.5514318624650683,2024-01-01 20:59:00+00:00,2025-01-30 18:59:00+00:00,0.645,0.645,0.5,-0.125
704,"get_CCI(ts_Corr(df['p4_ms6'],ts_Min(df['p6_tn12'],30),7),ts_Rank(df['p6_tn11'],28),df['p6_tn11'],28)","783_get_CCI(ts_Corr(df['p4_ms6'],ts_Min(df['p6_tn12'],30),7),ts_Rank(df['p6_tn11'],28),df['p6_tn11'],28)",11.7904,0.0028,1.0636,3.0456,0.5897810540182322,2024-01-01 20:59:00+00:00,2025-01-30 18:59:00+00:00,0.279,0.872,0.2424,-0.117
705,"get_CCI(pn_Winsor(df['p6_tn0'],12),pn_FillMax(pn_Winsor(df['p6_tn0'],17)),ts_MeanChg(IfThen(df['ultosc'],50,39),7),45)","784_get_CCI(pn_Winsor(df['p6_tn0'],12),pn_FillMax(pn_Winsor(df['p6_tn0'],17)),ts_MeanChg(IfThen(df['ultosc'],50,39),7),45)",17.3501,0.0043,1.6064,3.213,0.5136047030107139,2024-01-01 20:59:00+00:00,2025-01-30 18:59:00+00:00,0.783,0.956,0.4503,-0.349
706,"-1*get_CCI(pn_Winsor(df['cmo'],5),pn_FillMax(pn_Winsor(df['p5_to5'],47)),Xor(df['lislope'],df['p6_tn12']),22)","784_-1*get_CCI(pn_Winsor(df['cmo'],5),pn_FillMax(pn_Winsor(df['p5_to5'],47)),Xor(df['lislope'],df['p6_tn12']),22)",21.0626,0.0072,1.9563,3.4191,0.5548376631927379,2024-01-01 20:59:00+00:00,2025-01-30 18:59:00+00:00,0.625,1.492,0.2952,-0.464
707,"-1*get_CCI(pn_Rank(df['p2_et6']),get_KAMA(df['p6_tn13'],32),df['p3_mf11'],17)","785_-1*get_CCI(pn_Rank(df['p2_et6']),get_KAMA(df['p6_tn13'],32),df['p3_mf11'],17)",15.1687,0.0031,1.3955,3.1779,0.586857406951961,2024-01-01 20:59:00+00:00,2025-01-30 18:59:00+00:00,0.942,0.568,0.6238,-0.237
708,"-1*get_CCI(pn_Winsor(Max(df['p1_corrs8'],df['p2_et14']),5),pn_FillMax(pn_Winsor(df['p6_tn0'],47)),ts_Scale(df['p5_to0'],26),22)","786_-1*get_CCI(pn_Winsor(Max(df['p1_corrs8'],df['p2_et14']),5),pn_FillMax(pn_Winsor(df['p6_tn0'],47)),ts_Scale(df['p5_to0'],26),22)",15.2251,0.0067,1.362,3.8154,0.5420855076169488,2024-01-01 20:59:00+00:00,2025-01-30 18:59:00+00:00,0.58,0.894,0.3935,-0.157
709,"-1*get_CCI(ts_Delay(df['p6_tn1'],42),pn_Rank(ts_Cov2(df['p2_et6'],df['ultosc'],29)),df['cmo'],17)","787_-1*get_CCI(ts_Delay(df['p6_tn1'],42),pn_Rank(ts_Cov2(df['p2_et6'],df['ultosc'],29)),df['cmo'],17)",20.5599,0.0055,1.9273,3.0432,0.5504950126281427,2024-01-01 20:59:00+00:00,2025-01-30 18:59:00+00:00,0.707,1.378,0.3391,-0.412
710,"-1*get_CCI(ts_Delay(df['p5_to7'],16),get_CMO(ts_Quantile(df['p1_corrs0'],36,'D'),26),df['cmo'],17)","788_-1*get_CCI(ts_Delay(df['p5_to7'],16),get_CMO(ts_Quantile(df['p1_corrs0'],36,'D'),26),df['cmo'],17)",10.6693,0.0037,0.9425,3.1831,0.4687122112017386,2024-01-01 20:59:00+00:00,2025-01-30 18:59:00+00:00,0.209,0.811,0.2049,-0.258
711,"-1*get_CCI(MEthan(df['p4_ms3'],df['p6_tn3']),df['dx'],df['cmo'],17)","788_-1*get_CCI(MEthan(df['p4_ms3'],df['p6_tn3']),df['dx'],df['cmo'],17)",16.1453,0.0045,1.4788,3.4024,0.571378827522506,2024-01-01 20:59:00+00:00,2025-01-30 18:59:00+00:00,0.348,1.253,0.2174,-0.255
712,"-1*get_CCI(get_MINUS_DI(Softsign(df['dm']),df['p6_tn1'],ts_Sum(df['p2_et9'],12),17),pn_Rank2(df['lislope']),pn_Rank2(ts_Delta(df['p5_to0'],29)),17)","789_-1*get_CCI(get_MINUS_DI(Softsign(df['dm']),df['p6_tn1'],ts_Sum(df['p2_et9'],12),17),pn_Rank2(df['lislope']),pn_Rank2(ts_Delta(df['p5_to0'],29)),17)",17.1306,0.0057,1.5792,3.1631,0.589811223904446,2024-01-01 20:59:00+00:00,2025-01-30 18:59:00+00:00,0.44,1.269,0.2575,-0.27
713,"-1*get_CCI(MEthan(Not(df['p3_mf11']),ts_CovChg(df['p6_tn3'],df['p6_tn0'],40)),pn_FillMin(ts_CorrChg(df['p3_mf7'],df['lislope'],34)),df['cmo'],17)","790_-1*get_CCI(MEthan(Not(df['p3_mf11']),ts_CovChg(df['p6_tn3'],df['p6_tn0'],40)),pn_FillMin(ts_CorrChg(df['p3_mf7'],df['lislope'],34)),df['cmo'],17)",23.6845,0.0064,2.2185,3.5415,0.548102738014755,2024-01-01 20:59:00+00:00,2025-01-30 18:59:00+00:00,0.487,1.914,0.2028,-0.419
714,"-1*get_CCI(LEthan(ts_Scale(df['p6_tn9'],17),df['p3_mf1']),ts_Stdev2(get_KAMA(df['p3_mf0'],2),38),Mthan(Sqrt(df['cmo']),ts_ChgRate(df['cmo'],50)),17)","791_-1*get_CCI(LEthan(ts_Scale(df['p6_tn9'],17),df['p3_mf1']),ts_Stdev2(get_KAMA(df['p3_mf0'],2),38),Mthan(Sqrt(df['cmo']),ts_ChgRate(df['cmo'],50)),17)",11.8129,0.0039,1.0605,3.0338,0.4859783237744176,2024-01-01 20:59:00+00:00,2025-01-30 18:59:00+00:00,0.594,0.554,0.5174,-0.184
715,"-1*get_CCI(MEthan(get_HT_DCPHASE(df['dx']),ts_Argmax(df['p6_tn3'],32)),ts_ChgRate(df['cmo'],22),df['cmo'],17)","791_-1*get_CCI(MEthan(get_HT_DCPHASE(df['dx']),ts_Argmax(df['p6_tn3'],32)),ts_ChgRate(df['cmo'],22),df['cmo'],17)",20.092,0.0049,1.8652,3.5919,0.507591644106258,2024-01-01 20:59:00+00:00,2025-01-30 18:59:00+00:00,0.727,1.292,0.3601,-0.344
716,"-1*get_CCI(ts_Cov(df['p2_et3'],df['p3_mf9'],22),df['cmo'],ts_Delta(ts_Argmax(df['dcperiod'],40),39),17)","792_-1*get_CCI(ts_Cov(df['p2_et3'],df['p3_mf9'],22),df['cmo'],ts_Delta(ts_Argmax(df['dcperiod'],40),39),17)",11.3112,0.0045,0.9944,3.4238,0.5676413283659796,2024-01-01 20:59:00+00:00,2025-01-30 18:59:00+00:00,0.486,0.59,0.4517,-0.24
717,"-1*get_CCI(MEthan(pn_Rank2(df['p5_to6']),Log(df['p1_corrs0'])),pn_FillMin(df['p5_to5']),ts_Median(df['p5_to0'],39),17)","793_-1*get_CCI(MEthan(pn_Rank2(df['p5_to6']),Log(df['p1_corrs0'])),pn_FillMin(df['p5_to5']),ts_Median(df['p5_to0'],39),17)",12.184,0.0043,1.0491,4.4332,0.5879150971754374,2024-01-01 20:59:00+00:00,2025-01-30 18:59:00+00:00,0.593,0.542,0.5225,-0.16
718,"-1*get_CCI(ts_Median(df['p3_mf12'],46),ts_Corr2(df['p6_tn8'],get_LINEARREG_ANGLE(df['p4_ms2'],5),37),Mthan(df['cmo'],ts_ChgRate(df['cmo'],50)),29)","794_-1*get_CCI(ts_Median(df['p3_mf12'],46),ts_Corr2(df['p6_tn8'],get_LINEARREG_ANGLE(df['p4_ms2'],5),37),Mthan(df['cmo'],ts_ChgRate(df['cmo'],50)),29)",14.1254,0.0049,1.253,4.0525,0.5431345581967342,2024-01-01 20:59:00+00:00,2025-01-30 18:59:00+00:00,0.731,0.625,0.5391,-0.236
719,"-1*get_CCI(LEthan(ts_Skewness(df['p5_to0'],17),df['cmo']),ts_Entropy(df['p6_tn7'],11),MEthan(ts_Skewness(df['p5_to0'],17),get_CMO(df['p2_et6'],34)),17)","795_-1*get_CCI(LEthan(ts_Skewness(df['p5_to0'],17),df['cmo']),ts_Entropy(df['p6_tn7'],11),MEthan(ts_Skewness(df['p5_to0'],17),get_CMO(df['p2_et6'],34)),17)",9.2696,0.0029,0.8039,3.2548,0.37436018853890884,2024-01-01 20:59:00+00:00,2025-01-30 18:59:00+00:00,0.44,0.43,0.5057,-0.177
720,"-1*ts_Rank(pn_CrossFit(df['dcperiod'],FilterInf(df['cci'])),23)","796_-1*ts_Rank(pn_CrossFit(df['dcperiod'],FilterInf(df['cci'])),23)",19.2254,0.0068,1.7579,3.9237,0.5385817595812603,2024-01-01 20:59:00+00:00,2025-01-30 18:59:00+00:00,0.76,1.143,0.3994,-0.334
721,"-1*ts_Rank(ts_Delay(pn_Rank2(df['cmo']),36),23)","797_-1*ts_Rank(ts_Delay(pn_Rank2(df['cmo']),36),23)",16.6353,0.005,1.5307,3.2332,0.468874726273463,2024-01-01 20:59:00+00:00,2025-01-30 18:59:00+00:00,0.604,1.052,0.3647,0.008
722,"-1*Min(pn_CrossFit(pn_TransNorm(df['p2_et14']),pn_CrossFit(df['p6_tn0'],df['cci'])),get_DX(pn_Cut(df['p6_tn0']),ts_MeanChg(df['p1_corrs3'],5),df['p6_tn5'],4))","798_-1*Min(pn_CrossFit(pn_TransNorm(df['p2_et14']),pn_CrossFit(df['p6_tn0'],df['cci'])),get_DX(pn_Cut(df['p6_tn0']),ts_MeanChg(df['p1_corrs3'],5),df['p6_tn5'],4))",10.6127,0.0052,0.9261,3.2801,0.5211480482648997,2024-01-01 20:59:00+00:00,2025-01-30 18:59:00+00:00,0.39,0.612,0.3892,-0.266
723,"-1*Min(ts_Delta(df['p6_tn5'],45),Power(df['p2_et10'],36))","798_-1*Min(ts_Delta(df['p6_tn5'],45),Power(df['p2_et10'],36))",17.9517,0.0085,1.6354,3.5202,0.4847290330203603,2024-01-01 20:59:00+00:00,2025-01-30 18:59:00+00:00,0.524,1.246,0.296,-0.243
724,"-1*Min(pn_CrossFit(df['p1_corrs2'],pn_CrossFit(df['p2_et5'],df['cci'])),ts_Min(ts_Kurtosis(df['p3_mf5'],48),1))","798_-1*Min(pn_CrossFit(df['p1_corrs2'],pn_CrossFit(df['p2_et5'],df['cci'])),ts_Min(ts_Kurtosis(df['p3_mf5'],48),1))",14.1483,0.0056,1.2689,3.5402,0.5233590074224028,2024-01-01 20:59:00+00:00,2025-01-30 18:59:00+00:00,0.715,0.658,0.5208,-0.376
725,"-1*Min(pn_TransNorm(pn_Rank2(df['cmo'])),pn_CrossFit(df['di'],df['p3_mf6']))","799_-1*Min(pn_TransNorm(pn_Rank2(df['cmo'])),pn_CrossFit(df['di'],df['p3_mf6']))",15.8583,0.004,1.4523,3.4064,0.33791732384561524,2024-01-01 20:59:00+00:00,2025-01-30 18:59:00+00:00,0.917,0.655,0.5833,-0.169
726,"-1*ts_Regression(pn_GroupNorm(pn_GroupRank(df['cci'],df['p6_tn9']),inv(df['p3_mf2'])),get_DX(ts_Entropy(df['p1_corrs9'],38),get_CMO(df['dcphase'],12),get_HT_DCPHASE(df['p4_ms1']),16),17,'D')","801_-1*ts_Regression(pn_GroupNorm(pn_GroupRank(df['cci'],df['p6_tn9']),inv(df['p3_mf2'])),get_DX(ts_Entropy(df['p1_corrs9'],38),get_CMO(df['dcphase'],12),get_HT_DCPHASE(df['p4_ms1']),16),17,'D')",11.8796,0.0054,1.033,3.8424,0.5664347118465247,2024-01-01 20:59:00+00:00,2025-01-30 18:59:00+00:00,0.388,0.73,0.347,-0.258
727,"Add(Divide(ts_Scale(df['p3_mf1'],33),ts_Rank(df['p4_ms5'],11)),ts_Divide(df['p1_corrs6'],7))","802_Add(Divide(ts_Scale(df['p3_mf1'],33),ts_Rank(df['p4_ms5'],11)),ts_Divide(df['p1_corrs6'],7))",9.2515,0.0036,0.802,3.1531,0.35714709620939655,2024-01-01 20:59:00+00:00,2025-01-30 18:59:00+00:00,0.377,0.491,0.4343,-0.069
728,"-1*get_LINEARREG_ANGLE(Softsign(get_CMO(df['p3_mf11'],40)),10)","803_-1*get_LINEARREG_ANGLE(Softsign(get_CMO(df['p3_mf11'],40)),10)",14.3418,0.0056,1.274,3.9601,0.5763387624688998,2024-01-01 20:59:00+00:00,2025-01-30 18:59:00+00:00,0.588,0.79,0.4267,-0.284
729,"-1*get_LINEARREG_ANGLE(ts_Scale(df['p6_tn3'],47),10)","804_-1*get_LINEARREG_ANGLE(ts_Scale(df['p6_tn3'],47),10)",15.5193,0.0037,1.4013,3.9567,0.5635899242281619,2024-01-01 20:59:00+00:00,2025-01-30 18:59:00+00:00,0.814,0.703,0.5366,-0.211
730,"-1*get_LINEARREG_ANGLE(ts_Scale(ts_Delta(df['p4_ms5'],44),32),10)","806_-1*get_LINEARREG_ANGLE(ts_Scale(ts_Delta(df['p4_ms5'],44),32),10)",11.7056,0.0033,1.0466,3.2184,0.42171380650487533,2024-01-01 20:59:00+00:00,2025-01-30 18:59:00+00:00,0.382,0.75,0.3375,-0.064
731,"-1*pn_GroupNorm(df['liangle'],ts_StdevChg(Sqrt(df['p3_mf5']),32))","805_-1*pn_GroupNorm(df['liangle'],ts_StdevChg(Sqrt(df['p3_mf5']),32))",10.7275,0.0043,0.9175,4.015,0.5413959522065921,2024-01-01 20:59:00+00:00,2025-01-30 18:59:00+00:00,0.426,0.567,0.429,-0.319
732,"-1*pn_GroupNorm(Add(df['liangle'],df['p4_ms3']),FilterInf(FilterInf(df['p3_mf0'])))","804_-1*pn_GroupNorm(Add(df['liangle'],df['p4_ms3']),FilterInf(FilterInf(df['p3_mf0'])))",10.7005,0.0049,0.9414,3.1302,0.4912492970362502,2024-01-01 20:59:00+00:00,2025-01-30 18:59:00+00:00,0.56,0.458,0.5501,-0.309
733,"-1*Minus(df['p1_corrs1'],ts_CorrChg(Exp(df['p1_corrs4']),get_LINEARREG_SLOPE(df['p1_corrs5'],4),21))","805_-1*Minus(df['p1_corrs1'],ts_CorrChg(Exp(df['p1_corrs4']),get_LINEARREG_SLOPE(df['p1_corrs5'],4),21))",9.426,0.0043,0.817,3.1247,0.5288205227439438,2024-01-01 20:59:00+00:00,2025-01-30 18:59:00+00:00,0.22,0.664,0.2489,-0.252
734,"-1*pn_GroupNorm(ts_Quantile(Minus(df['p6_tn13'],df['p2_et13']),4,'C'),get_LINEARREG_SLOPE(ts_TransNorm(df['cci'],8),33))","806_-1*pn_GroupNorm(ts_Quantile(Minus(df['p6_tn13'],df['p2_et13']),4,'C'),get_LINEARREG_SLOPE(ts_TransNorm(df['cci'],8),33))",10.0292,0.0043,0.8775,3.1116,0.4569108462855233,2024-01-01 20:59:00+00:00,2025-01-30 18:59:00+00:00,0.597,0.353,0.6284,-0.239
735,"Minus(Reverse(ts_Scale(df['p4_ms5'],9)),df['p1_corrs0'])","807_Minus(Reverse(ts_Scale(df['p4_ms5'],9)),df['p1_corrs0'])",10.5721,0.0046,0.9322,3.0532,0.5686790174652326,2024-01-01 20:59:00+00:00,2025-01-30 18:59:00+00:00,0.511,0.498,0.5064,-0.189
736,"-1*Minus(df['p1_corrs1'],ts_Quantile(ts_Scale(df['p2_et8'],43),4,'C'))","808_-1*Minus(df['p1_corrs1'],ts_Quantile(ts_Scale(df['p2_et8'],43),4,'C'))",10.2196,0.0044,0.8866,3.3986,0.4323241750536964,2024-01-01 20:59:00+00:00,2025-01-30 18:59:00+00:00,0.371,0.588,0.3869,-0.202
737,"-1*pn_GroupNorm(pn_TransNorm(pn_GroupNeutral(df['p6_tn13'],df['p2_et14'])),FilterInf(ts_Decay(df['p6_tn3'],44)))","809_-1*pn_GroupNorm(pn_TransNorm(pn_GroupNeutral(df['p6_tn13'],df['p2_et14'])),FilterInf(ts_Decay(df['p6_tn3'],44)))",10.3465,0.0049,0.9022,3.2349,0.586661174519539,2024-01-01 20:59:00+00:00,2025-01-30 18:59:00+00:00,0.269,0.707,0.2756,-0.131
738,"-1*Minus(get_CCI(pn_GroupNorm(df['p4_ms5'],df['p6_tn4']),df['p6_tn4'],pn_Rank(df['p4_ms3']),27),ts_Quantile(pn_TransNorm(df['liangle']),23,'C'))","810_-1*Minus(get_CCI(pn_GroupNorm(df['p4_ms5'],df['p6_tn4']),df['p6_tn4'],pn_Rank(df['p4_ms3']),27),ts_Quantile(pn_TransNorm(df['liangle']),23,'C'))",17.744,0.006,1.6423,3.0602,0.5442475007251679,2024-01-01 20:59:00+00:00,2025-01-30 18:59:00+00:00,1.148,0.629,0.646,-0.208
739,"-1*Minus(get_CCI(ts_Kurtosis(df['p5_to1'],44),get_LINEARREG_ANGLE(df['cci'],11),df['p1_corrs9'],27),ts_Scale(df['dx'],22))","811_-1*Minus(get_CCI(ts_Kurtosis(df['p5_to1'],44),get_LINEARREG_ANGLE(df['cci'],11),df['p1_corrs9'],27),ts_Scale(df['dx'],22))",13.3694,0.0059,1.1758,3.9539,0.5919544032229074,2024-01-01 20:59:00+00:00,2025-01-30 18:59:00+00:00,0.526,0.746,0.4135,-0.24
740,"-1*Minus(get_CCI(IfThen(df['p1_corrs1'],41,24),df['p6_tn4'],df['p1_corrs9'],27),ts_Scale(df['dx'],22))","812_-1*Minus(get_CCI(IfThen(df['p1_corrs1'],41,24),df['p6_tn4'],df['p1_corrs9'],27),ts_Scale(df['dx'],22))",9.9366,0.0052,0.8563,3.339,0.5598437227812215,2024-01-01 20:59:00+00:00,2025-01-30 18:59:00+00:00,0.277,0.649,0.2991,-0.243
741,"-1*Minus(get_CCI(ts_Stdev(df['p1_corrs9'],39),df['p6_tn4'],pn_Rank(df['p4_ms3']),27),get_CCI(pn_TransNorm(df['dcperiod']),FilterInf(df['p1_corrs8']),pn_Rank2(df['p1_corrs7']),27))","813_-1*Minus(get_CCI(ts_Stdev(df['p1_corrs9'],39),df['p6_tn4'],pn_Rank(df['p4_ms3']),27),get_CCI(pn_TransNorm(df['dcperiod']),FilterInf(df['p1_corrs8']),pn_Rank2(df['p1_corrs7']),27))",11.1535,0.0049,0.9873,3.1101,0.42626616336652573,2024-01-01 20:59:00+00:00,2025-01-30 18:59:00+00:00,0.348,0.721,0.3255,-0.137
742,"-1*Minus(get_CCI(Xor(df['p6_tn1'],df['p3_mf12']),df['p6_tn3'],df['p4_ms5'],35),ts_Kurtosis(df['dcphase'],20))","812_-1*Minus(get_CCI(Xor(df['p6_tn1'],df['p3_mf12']),df['p6_tn3'],df['p4_ms5'],35),ts_Kurtosis(df['dcphase'],20))",16.1846,0.0049,1.4603,4.0075,0.5679322265452759,2024-01-01 20:59:00+00:00,2025-01-30 18:59:00+00:00,0.677,0.904,0.4282,-0.142
743,"-1*pn_GroupNorm(get_CCI(ts_Kurtosis(df['p6_tn2'],44),Softsign(df['cci']),Abs(df['p4_ms5']),27),FilterInf(df['p5_to4']))","813_-1*pn_GroupNorm(get_CCI(ts_Kurtosis(df['p6_tn2'],44),Softsign(df['cci']),Abs(df['p4_ms5']),27),FilterInf(df['p5_to4']))",12.2315,0.0054,1.0739,3.6699,0.583592632651263,2024-01-01 20:59:00+00:00,2025-01-30 18:59:00+00:00,0.331,0.832,0.2846,-0.275
744,"-1*Minus(get_CCI(ts_Kurtosis(df['p6_tn13'],35),get_CMO(df['cci'],27),df['p4_ms5'],35),Power(Max(df['p4_ms5'],df['p1_corrs2']),29))","814_-1*Minus(get_CCI(ts_Kurtosis(df['p6_tn13'],35),get_CMO(df['cci'],27),df['p4_ms5'],35),Power(Max(df['p4_ms5'],df['p1_corrs2']),29))",14.1972,0.0059,1.2677,3.674,0.5877233843050368,2024-01-01 20:59:00+00:00,2025-01-30 18:59:00+00:00,0.643,0.729,0.4687,-0.266
745,"-1*pn_GroupNorm(get_CCI(df['p4_ms5'],pn_Rank(df['p6_tn3']),pn_Cut(df['p3_mf1']),23),ts_Corr2(df['dx'],ts_Decay(df['p6_tn13'],45),18))","815_-1*pn_GroupNorm(get_CCI(df['p4_ms5'],pn_Rank(df['p6_tn3']),pn_Cut(df['p3_mf1']),23),ts_Corr2(df['dx'],ts_Decay(df['p6_tn13'],45),18))",9.6947,0.0041,0.8363,3.3852,0.46240998971221636,2024-01-01 20:59:00+00:00,2025-01-30 18:59:00+00:00,0.388,0.517,0.4287,-0.082
746,"-1*Minus(get_CCI(pn_TransNorm(df['p2_et0']),pn_GroupNorm(df['dcphase'],df['p1_corrs9']),df['p1_corrs9'],33),pn_GroupNorm(ts_Divide(df['p2_et15'],1),df['p2_et11']))","816_-1*Minus(get_CCI(pn_TransNorm(df['p2_et0']),pn_GroupNorm(df['dcphase'],df['p1_corrs9']),df['p1_corrs9'],33),pn_GroupNorm(ts_Divide(df['p2_et15'],1),df['p2_et11']))",12.9073,0.0064,1.1172,4.2518,0.5753946161104435,2024-01-01 20:59:00+00:00,2025-01-30 18:59:00+00:00,0.419,0.79,0.3466,-0.214
747,"-1*SignedPower(df['p2_et0'],pn_GroupRank(ts_Corr(df['p5_to4'],df['kama'],43),df['p1_corrs4']))","818_-1*SignedPower(df['p2_et0'],pn_GroupRank(ts_Corr(df['p5_to4'],df['kama'],43),df['p1_corrs4']))",15.02,0.0068,1.3109,4.7197,0.5476453531917074,2024-01-01 20:59:00+00:00,2025-01-30 18:59:00+00:00,0.51,0.909,0.3594,-0.143
748,"-1*ts_TransNorm(pn_GroupNorm(df['p6_tn13'],df['p6_tn1']),15)","819_-1*ts_TransNorm(pn_GroupNorm(df['p6_tn13'],df['p6_tn1']),15)",14.2243,0.0055,1.28,3.4422,0.45484942900816544,2024-01-01 20:59:00+00:00,2025-01-30 18:59:00+00:00,0.899,0.486,0.6491,-0.085
749,"-1*Add(ts_Delta(df['p6_tn1'],19),0.576)","820_-1*Add(ts_Delta(df['p6_tn1'],19),0.576)",15.9009,0.0047,1.4492,3.524,0.5923096620593284,2024-01-01 20:59:00+00:00,2025-01-30 18:59:00+00:00,0.593,0.976,0.3779,-0.131
750,"-1*ts_Delta(df['p2_et0'],8)","821_-1*ts_Delta(df['p2_et0'],8)",19.2097,0.0069,1.738,4.4516,0.5325648527057275,2024-01-01 20:59:00+00:00,2025-01-30 18:59:00+00:00,0.858,1.023,0.4561,-0.158
751,"-1*ts_Delta(Min(df['p6_tn1'],df['p3_mf4']),8)","822_-1*ts_Delta(Min(df['p6_tn1'],df['p3_mf4']),8)",13.7773,0.0026,1.259,3.166,0.561114754774835,2024-01-01 20:59:00+00:00,2025-01-30 18:59:00+00:00,0.383,0.98,0.281,-0.136
752,"get_CCI(ts_Argmin(df['p6_tn0'],15),Reverse(Max(df['p2_et1'],12)),ts_Rank(ts_Rank(df['p2_et17'],44),44),22)","823_get_CCI(ts_Argmin(df['p6_tn0'],15),Reverse(Max(df['p2_et1'],12)),ts_Rank(ts_Rank(df['p2_et17'],44),44),22)",14.1497,0.0034,1.2925,3.1613,0.5689428539088414,2024-01-01 20:59:00+00:00,2025-01-30 18:59:00+00:00,0.309,1.089,0.221,-0.102
753,"-1*get_CCI(pn_Rank2(ts_Scale(df['lislope'],13)),ts_Min(df['p3_mf1'],24),ts_Rank(df['p6_tn5'],44),22)","824_-1*get_CCI(pn_Rank2(ts_Scale(df['lislope'],13)),ts_Min(df['p3_mf1'],24),ts_Rank(df['p6_tn5'],44),22)",9.909,0.0045,0.868,3.0116,0.49555780273818345,2024-01-01 20:59:00+00:00,2025-01-30 18:59:00+00:00,0.382,0.558,0.4064,-0.287
754,"-1*get_CCI(ts_Stdev(Softsign(df['p2_et0']),8),df['p4_ms5'],df['p2_et4'],22)","825_-1*get_CCI(ts_Stdev(Softsign(df['p2_et0']),8),df['p4_ms5'],df['p2_et4'],22)",11.5908,0.0048,1.0156,3.5833,0.5677589234451295,2024-01-01 20:59:00+00:00,2025-01-30 18:59:00+00:00,0.459,0.64,0.4177,-0.189
755,"-1*ts_Delta(Min(ts_Scale(df['lislope'],13),inv(df['p4_ms4'])),20)","826_-1*ts_Delta(Min(ts_Scale(df['lislope'],13),inv(df['p4_ms4'])),20)",11.5622,0.0057,1.0185,3.2776,0.5636942186417933,2024-01-01 20:59:00+00:00,2025-01-30 18:59:00+00:00,0.577,0.526,0.5231,-0.328
756,"-1*get_CCI(get_LINEARREG_ANGLE(df['p6_tn1'],19),df['p2_et14'],ts_Mean(df['p2_et0'],18),22)","827_-1*get_CCI(get_LINEARREG_ANGLE(df['p6_tn1'],19),df['p2_et14'],ts_Mean(df['p2_et0'],18),22)",10.8937,0.0033,0.9696,3.1018,0.4797622065973487,2024-01-01 20:59:00+00:00,2025-01-30 18:59:00+00:00,0.259,0.79,0.2469,-0.148
757,"-1*Add(Reverse(Max(df['p2_et1'],22)),Min(ts_Scale(df['lislope'],15),ts_Rank(df['p4_ms5'],44)))","828_-1*Add(Reverse(Max(df['p2_et1'],22)),Min(ts_Scale(df['lislope'],15),ts_Rank(df['p4_ms5'],44)))",13.0153,0.0055,1.1522,3.6486,0.5982678249791016,2024-01-01 20:59:00+00:00,2025-01-30 18:59:00+00:00,0.534,0.713,0.4282,-0.362
758,"-1*get_CCI(FilterInf(Sqrt(df['p2_et1'])),Sign(df['p2_et14']),ts_Rank(ts_Max(df['p2_et0'],50),9),22)","830_-1*get_CCI(FilterInf(Sqrt(df['p2_et1'])),Sign(df['p2_et14']),ts_Rank(ts_Max(df['p2_et0'],50),9),22)",14.1253,0.0046,1.2717,3.5332,0.4718175906942813,2024-01-01 20:59:00+00:00,2025-01-30 18:59:00+00:00,0.476,0.9,0.3459,-0.12
759,"-1*get_CCI(pn_Winsor(Sqrt(df['p5_to0']),1),pn_Winsor(Sqrt(df['p5_to0']),1),pn_RankCentered(df['adosc']),44)","831_-1*get_CCI(pn_Winsor(Sqrt(df['p5_to0']),1),pn_Winsor(Sqrt(df['p5_to0']),1),pn_RankCentered(df['adosc']),44)",20.5818,0.0055,1.9281,3.0728,0.5616266390311655,2024-01-01 20:59:00+00:00,2025-01-30 18:59:00+00:00,0.486,1.601,0.2329,-0.122
760,"-1*get_CCI(ts_Regression(df['p5_to7'],get_KAMA(df['p4_ms6'],22),16,'A'),ts_Delta(df['p6_tn1'],22),Not(ts_Rank(df['p2_et19'],35)),16)","832_-1*get_CCI(ts_Regression(df['p5_to7'],get_KAMA(df['p4_ms6'],22),16,'A'),ts_Delta(df['p6_tn1'],22),Not(ts_Rank(df['p2_et19'],35)),16)",17.2949,0.0068,1.5813,3.421,0.5501759606091233,2024-01-01 20:59:00+00:00,2025-01-30 18:59:00+00:00,0.468,1.243,0.2735,-0.163
761,"get_CCI(Sqrt(df['kama']),Not(pn_GroupNorm(df['p3_mf0'],df['adosc'])),Reverse(ts_Scale(df['p2_et0'],13)),34)","833_get_CCI(Sqrt(df['kama']),Not(pn_GroupNorm(df['p3_mf0'],df['adosc'])),Reverse(ts_Scale(df['p2_et0'],13)),34)",10.8521,0.0041,0.9545,3.301,0.49102794072595624,2024-01-01 20:59:00+00:00,2025-01-30 18:59:00+00:00,0.325,0.708,0.3146,-0.051
762,"-1*get_CCI(Softsign(ts_Scale(df['lislope'],13)),ts_Corr2(pn_Winsor(df['p2_et12'],16),ts_Kurtosis(df['p2_et17'],19),9),ts_StdevChg(df['liangle'],24),21)","834_-1*get_CCI(Softsign(ts_Scale(df['lislope'],13)),ts_Corr2(pn_Winsor(df['p2_et12'],16),ts_Kurtosis(df['p2_et17'],19),9),ts_StdevChg(df['liangle'],24),21)",10.0381,0.0035,0.8542,3.9714,0.2957895095847933,2024-01-01 20:59:00+00:00,2025-01-30 18:59:00+00:00,0.385,0.54,0.4162,-0.096
763,"-1*get_CCI(Softsign(ts_Scale(df['p5_to4'],13)),ts_Decay(df['p4_ms3'],44),pn_Rank(df['p6_tn5']),11)","835_-1*get_CCI(Softsign(ts_Scale(df['p5_to4'],13)),ts_Decay(df['p4_ms3'],44),pn_Rank(df['p6_tn5']),11)",18.0935,0.0046,1.658,3.8531,0.5929603158988533,2024-01-01 20:59:00+00:00,2025-01-30 18:59:00+00:00,0.888,0.906,0.495,-0.238
764,"get_CCI(ts_Median(pn_FillMax(df['p3_mf2']),19),ts_Sum(pn_Winsor(df['p6_tn6'],8),19),pn_Rank(ts_Median(df['p2_et17'],26)),13)","836_get_CCI(ts_Median(pn_FillMax(df['p3_mf2']),19),ts_Sum(pn_Winsor(df['p6_tn6'],8),19),pn_Rank(ts_Median(df['p2_et17'],26)),13)",15.794,0.0053,1.4525,3.0042,0.5300767891221478,2024-01-01 20:59:00+00:00,2025-01-30 18:59:00+00:00,0.571,1.001,0.3632,-0.255
765,"-1*get_CCI(Softsign(df['p2_et12']),ts_Stdev(Softsign(df['lislope']),9),ts_StdevChg(df['liangle'],24),21)","837_-1*get_CCI(Softsign(df['p2_et12']),ts_Stdev(Softsign(df['lislope']),9),ts_StdevChg(df['liangle'],24),21)",10.18,0.0036,0.8926,3.2205,0.26094449461507935,2024-01-01 20:59:00+00:00,2025-01-30 18:59:00+00:00,0.364,0.602,0.3768,-0.094
766,"get_CCI(ts_Delta(df['p6_tn0'],22),Power(df['p3_mf12'],16),ts_ChgRate(df['p1_corrs1'],26),21)","838_get_CCI(ts_Delta(df['p6_tn0'],22),Power(df['p3_mf12'],16),ts_ChgRate(df['p1_corrs1'],26),21)",12.1083,0.0061,1.075,3.1546,0.5549188231246864,2024-01-01 20:59:00+00:00,2025-01-30 18:59:00+00:00,0.411,0.752,0.3534,-0.186
767,"get_CCI(MEthan(ts_Scale(df['p3_mf12'],1),pn_RankCentered(df['p5_to0'])),Min(df['p2_et3'],df['p5_to7']),ts_Corr2(Exp(df['p1_corrs4']),ts_Kurtosis(df['p3_mf0'],19),9),22)","839_get_CCI(MEthan(ts_Scale(df['p3_mf12'],1),pn_RankCentered(df['p5_to0'])),Min(df['p2_et3'],df['p5_to7']),ts_Corr2(Exp(df['p1_corrs4']),ts_Kurtosis(df['p3_mf0'],19),9),22)",9.9672,0.0027,0.8828,3.0075,0.4507496585263448,2024-01-01 20:59:00+00:00,2025-01-30 18:59:00+00:00,0.292,0.663,0.3058,-0.062
768,"-1*get_CCI(ts_Corr2(Max(df['p6_tn10'],df['p3_mf5']),ts_Median(df['p5_to2'],7),9),ts_Rank(ts_Product(df['p6_tn5'],45),35),ts_Corr2(ts_Argmax(df['p4_ms6'],6),Power(df['p2_et7'],45),16),22)","840_-1*get_CCI(ts_Corr2(Max(df['p6_tn10'],df['p3_mf5']),ts_Median(df['p5_to2'],7),9),ts_Rank(ts_Product(df['p6_tn5'],45),35),ts_Corr2(ts_Argmax(df['p4_ms6'],6),Power(df['p2_et7'],45),16),22)",10.5442,0.0024,0.9387,3.114,0.41543752582518684,2024-01-01 20:59:00+00:00,2025-01-30 18:59:00+00:00,0.288,0.728,0.2835,-0.099
769,"-1*get_CCI(Min(ts_Scale(df['lislope'],45),35),Sign(Sqrt(df['p5_to0'])),ts_Corr2(ts_Argmax(df['p4_ms6'],16),pn_Stand(df['p6_tn2']),16),22)","841_-1*get_CCI(Min(ts_Scale(df['lislope'],45),35),Sign(Sqrt(df['p5_to0'])),ts_Corr2(ts_Argmax(df['p4_ms6'],16),pn_Stand(df['p6_tn2']),16),22)",12.3223,0.0046,1.0843,3.7459,0.5558508288160072,2024-01-01 20:59:00+00:00,2025-01-30 18:59:00+00:00,0.398,0.776,0.339,-0.217
770,"-1*get_CCI(Lthan(ts_Median(df['p5_to4'],26),ts_Regression(df['p5_to0'],df['p5_to3'],9,'B')),pn_FillMax(df['p3_mf0']),Add(df['p4_ms3'],ts_Corr2(df['dcperiod'],df['p1_corrs6'],29)),50)","842_-1*get_CCI(Lthan(ts_Median(df['p5_to4'],26),ts_Regression(df['p5_to0'],df['p5_to3'],9,'B')),pn_FillMax(df['p3_mf0']),Add(df['p4_ms3'],ts_Corr2(df['dcperiod'],df['p1_corrs6'],29)),50)",26.0332,0.0037,2.4476,4.1106,0.4466187946801075,2024-01-01 20:59:00+00:00,2025-01-30 18:59:00+00:00,0.855,1.793,0.3229,-0.09
771,"-1*get_CCI(ts_ChgRate(df['p2_et0'],0.97),pn_FillMin(Min(df['p5_to0'],35)),get_LINEARREG_ANGLE(pn_Stand(df['p2_et10']),9),35)","843_-1*get_CCI(ts_ChgRate(df['p2_et0'],0.97),pn_FillMin(Min(df['p5_to0'],35)),get_LINEARREG_ANGLE(pn_Stand(df['p2_et10']),9),35)",13.1195,0.005,1.1529,4.023,0.520413533946806,2024-01-01 20:59:00+00:00,2025-01-30 18:59:00+00:00,0.658,0.589,0.5277,-0.184
772,"-1*Multiply(ts_Regression(Multiply(df['p2_et7'],df['p3_mf9']),ts_Divide(df['p3_mf11'],15),22,'C'),df['p3_mf9'])","844_-1*Multiply(ts_Regression(Multiply(df['p2_et7'],df['p3_mf9']),ts_Divide(df['p3_mf11'],15),22,'C'),df['p3_mf9'])",11.2252,0.0038,0.9941,3.288,0.487620261614906,2024-01-01 20:59:00+00:00,2025-01-30 18:59:00+00:00,0.386,0.69,0.3587,-0.223
773,"ts_Cov(ts_Mean(pn_GroupNeutral(df['p4_ms5'],df['p2_et13']),8),ts_Argmax(pn_GroupNeutral(df['p4_ms5'],df['p2_et13']),22),22)","845_ts_Cov(ts_Mean(pn_GroupNeutral(df['p4_ms5'],df['p2_et13']),8),ts_Argmax(pn_GroupNeutral(df['p4_ms5'],df['p2_et13']),22),22)",12.9436,0.0052,1.1499,3.5536,0.3445345716962444,2024-01-01 20:59:00+00:00,2025-01-30 18:59:00+00:00,0.398,0.846,0.3199,-0.02
774,"pn_TransNorm(Multiply(df['p2_et9'],ts_MeanChg(df['p2_et15'],26)))","846_pn_TransNorm(Multiply(df['p2_et9'],ts_MeanChg(df['p2_et15'],26)))",13.6406,0.0058,1.2034,3.95,0.4931847267048824,2024-01-01 20:59:00+00:00,2025-01-30 18:59:00+00:00,0.418,0.885,0.3208,-0.06
775,"-1*pn_Winsor(ts_Corr(df['p2_et14'],df['cci'],34),49)","846_-1*pn_Winsor(ts_Corr(df['p2_et14'],df['cci'],34),49)",11.4365,0.0033,0.9979,3.8818,0.2838985409796168,2024-01-01 20:59:00+00:00,2025-01-30 18:59:00+00:00,0.377,0.703,0.3491,-0.045
776,"-1*pn_TransNorm(Multiply(pn_RankCentered(df['p1_corrs2']),Abs(df['p6_tn6'])))","847_-1*pn_TransNorm(Multiply(pn_RankCentered(df['p1_corrs2']),Abs(df['p6_tn6'])))",9.5436,0.0033,0.8349,3.0842,0.5440511485826286,2024-01-01 20:59:00+00:00,2025-01-30 18:59:00+00:00,0.479,0.424,0.5305,-0.09
777,"-1*pn_TransNorm(Min(ts_Cov(df['p2_et0'],df['p2_et2'],7),ts_Min(df['p2_et3'],4)))","848_-1*pn_TransNorm(Min(ts_Cov(df['p2_et0'],df['p2_et2'],7),ts_Min(df['p2_et3'],4)))",12.9285,0.0042,1.1705,3.0405,0.46373356268804755,2024-01-01 20:59:00+00:00,2025-01-30 18:59:00+00:00,0.324,0.942,0.2559,-0.148
778,"-1*pn_Winsor(Min(ts_Regression(df['p1_corrs2'],df['p2_et9'],49,'C'),Multiply(df['p1_corrs2'],df['p2_et9'])),49)","848_-1*pn_Winsor(Min(ts_Regression(df['p1_corrs2'],df['p2_et9'],49,'C'),Multiply(df['p1_corrs2'],df['p2_et9'])),49)",13.135,0.0058,1.1579,3.7917,0.5228303806175173,2024-01-01 20:59:00+00:00,2025-01-30 18:59:00+00:00,0.277,0.976,0.2211,-0.219
779,"-1*pn_Winsor(Multiply(ts_Scale(df['p3_mf3'],13),df['p2_et9']),5)","850_-1*pn_Winsor(Multiply(ts_Scale(df['p3_mf3'],13),df['p2_et9']),5)",12.7711,0.0057,1.137,3.3497,0.5091533269557286,2024-01-01 20:59:00+00:00,2025-01-30 18:59:00+00:00,0.379,0.851,0.3081,-0.214
780,"-1*pn_Winsor(Multiply(Multiply(df['p4_ms5'],df['p1_corrs0']),Divide(df['p2_et9'],17)),5)","851_-1*pn_Winsor(Multiply(Multiply(df['p4_ms5'],df['p1_corrs0']),Divide(df['p2_et9'],17)),5)",14.6102,0.0058,1.3157,3.4865,0.4880830038195098,2024-01-01 20:59:00+00:00,2025-01-30 18:59:00+00:00,0.588,0.836,0.4129,-0.17
781,"-1*get_LINEARREG_ANGLE(df['p4_ms3'],15)","851_-1*get_LINEARREG_ANGLE(df['p4_ms3'],15)",16.191,0.0065,1.4442,4.275,0.5506627527062179,2024-01-01 20:59:00+00:00,2025-01-30 18:59:00+00:00,0.608,0.955,0.389,-0.274
782,"-1*ts_Divide(df['p2_et10'],25)","851_-1*ts_Divide(df['p2_et10'],25)",11.2994,0.0047,0.9879,3.5615,0.5257923933059109,2024-01-01 20:59:00+00:00,2025-01-30 18:59:00+00:00,0.573,0.496,0.536,-0.205
783,"-1*pn_GroupNeutral(df['p6_tn7'],pn_GroupNeutral(df['p6_tn7'],df['p2_et12']))","852_-1*pn_GroupNeutral(df['p6_tn7'],pn_GroupNeutral(df['p6_tn7'],df['p2_et12']))",11.6905,0.0045,1.0348,3.3559,0.3842174004879172,2024-01-01 20:59:00+00:00,2025-01-30 18:59:00+00:00,0.381,0.738,0.3405,-0.097
784,"-1*ts_ChgRate(Max(ts_Scale(df['p6_tn5'],31),Power(df['p3_mf8'],11)),7)","853_-1*ts_ChgRate(Max(ts_Scale(df['p6_tn5'],31),Power(df['p3_mf8'],11)),7)",10.1322,0.0049,0.8845,3.1247,0.5152850485434082,2024-01-01 20:59:00+00:00,2025-01-30 18:59:00+00:00,0.406,0.552,0.4238,-0.161
785,"-1*pn_GroupNeutral(Add(df['p3_mf10'],0.992),ts_Delta(df['p2_et14'],40))","854_-1*pn_GroupNeutral(Add(df['p3_mf10'],0.992),ts_Delta(df['p2_et14'],40))",10.186,0.0034,0.8978,3.1116,0.5948072466014337,2024-01-01 20:59:00+00:00,2025-01-30 18:59:00+00:00,0.333,0.639,0.3426,-0.058
786,"-1*Add(ts_Corr2(df['adosc'],df['p2_et9'],37),df['p1_corrs9'])","855_-1*Add(ts_Corr2(df['adosc'],df['p2_et9'],37),df['p1_corrs9'])",9.8212,0.0048,0.857,3.0349,0.5838654092262626,2024-01-01 20:59:00+00:00,2025-01-30 18:59:00+00:00,0.251,0.676,0.2708,-0.279
787,"pn_GroupNeutral(get_MINUS_DM(df['p2_et7'],df['cmo'],6),pn_Rank(df['p6_tn8']))","855_pn_GroupNeutral(get_MINUS_DM(df['p2_et7'],df['cmo'],6),pn_Rank(df['p6_tn8']))",13.0496,0.0054,1.1536,3.7225,0.48137835352575936,2024-01-01 20:59:00+00:00,2025-01-30 18:59:00+00:00,0.629,0.619,0.504,-0.249
788,"-1*pn_GroupNeutral(df['p1_corrs9'],ts_Regression(df['p6_tn8'],ts_StdevChg(df['p2_et2'],1),43,'D'))","855_-1*pn_GroupNeutral(df['p1_corrs9'],ts_Regression(df['p6_tn8'],ts_StdevChg(df['p2_et2'],1),43,'D'))",11.5453,0.0055,1.0014,3.77,0.5763725265035652,2024-01-01 20:59:00+00:00,2025-01-30 18:59:00+00:00,0.335,0.749,0.309,-0.304
789,"pn_GroupNeutral(df['p4_ms2'],get_KAMA(Min(df['p2_et13'],0.498),13))","854_pn_GroupNeutral(df['p4_ms2'],get_KAMA(Min(df['p2_et13'],0.498),13))",17.8936,0.0079,1.5895,4.8148,0.5755304409734758,2024-01-01 20:59:00+00:00,2025-01-30 18:59:00+00:00,0.641,1.08,0.3725,-0.183
790,"-1*pn_GroupNeutral(ts_Delta(df['p3_mf11'],21),get_KAMA(df['p1_corrs0'],44))","854_-1*pn_GroupNeutral(ts_Delta(df['p3_mf11'],21),get_KAMA(df['p1_corrs0'],44))",17.6257,0.0074,1.5674,4.7429,0.5779893820439136,2024-01-01 20:59:00+00:00,2025-01-30 18:59:00+00:00,0.827,0.869,0.4876,-0.187
791,"-1*pn_GroupNeutral(df['ultosc'],ts_Cov2(df['p4_ms4'],SignedPower(df['p2_et17'],df['p2_et18']),34))","854_-1*pn_GroupNeutral(df['ultosc'],ts_Cov2(df['p4_ms4'],SignedPower(df['p2_et17'],df['p2_et18']),34))",15.3286,0.0053,1.3805,3.7801,0.5620949417205314,2024-01-01 20:59:00+00:00,2025-01-30 18:59:00+00:00,0.843,0.651,0.5643,-0.294
792,"get_LINEARREG_ANGLE(get_KAMA(df['p6_tn0'],36),6)","854_get_LINEARREG_ANGLE(get_KAMA(df['p6_tn0'],36),6)",17.562,0.0052,1.614,3.4817,0.5925521385331808,2024-01-01 20:59:00+00:00,2025-01-30 18:59:00+00:00,0.554,1.193,0.3171,-0.498
793,"ts_Regression(ts_Mean(df['p2_et11'],6),ts_Argmin(df['p2_et13'],16),31,'D')","854_ts_Regression(ts_Mean(df['p2_et11'],6),ts_Argmin(df['p2_et13'],16),31,'D')",11.9137,0.0051,1.0385,3.8237,0.511305437904351,2024-01-01 20:59:00+00:00,2025-01-30 18:59:00+00:00,0.453,0.671,0.403,-0.311
794,"-1*get_LINEARREG_ANGLE(pn_TransNorm(df['p2_et0']),6)","854_-1*get_LINEARREG_ANGLE(pn_TransNorm(df['p2_et0']),6)",13.2509,0.005,1.1814,3.5539,0.4867483036591358,2024-01-01 20:59:00+00:00,2025-01-30 18:59:00+00:00,0.546,0.733,0.4269,-0.159
795,"-1*get_LINEARREG_ANGLE(pn_Rank2(df['p6_tn13']),31)","856_-1*get_LINEARREG_ANGLE(pn_Rank2(df['p6_tn13']),31)",14.5724,0.0045,1.2995,4.0605,0.5292101273478382,2024-01-01 20:59:00+00:00,2025-01-30 18:59:00+00:00,0.355,1.051,0.2525,-0.189
796,"-1*Max(ts_Scale(ts_TransNorm(df['p3_mf11'],30),21),pn_TransNorm(df['p5_to1']))","857_-1*Max(ts_Scale(ts_TransNorm(df['p3_mf11'],30),21),pn_TransNorm(df['p5_to1']))",9.3785,0.0035,0.8057,3.445,0.4277211451677158,2024-01-01 20:59:00+00:00,2025-01-30 18:59:00+00:00,0.539,0.333,0.6181,-0.081
797,"ts_Regression(Reverse(df['p2_et0']),pn_FillMax(Reverse(df['p6_tn13'])),18,'D')","857_ts_Regression(Reverse(df['p2_et0']),pn_FillMax(Reverse(df['p6_tn13'])),18,'D')",18.7724,0.0073,1.6972,4.3124,0.5919230911565414,2024-01-01 20:59:00+00:00,2025-01-30 18:59:00+00:00,0.967,0.87,0.5264,-0.169
798,"get_LINEARREG_ANGLE(LEthan(Min(df['p5_to0'],6),df['p3_mf12']),42)","857_get_LINEARREG_ANGLE(LEthan(Min(df['p5_to0'],6),df['p3_mf12']),42)",10.7822,0.0033,0.9287,3.9919,0.49126222635233374,2024-01-01 20:59:00+00:00,2025-01-30 18:59:00+00:00,0.337,0.669,0.335,-0.139
799,"-1*ts_Regression(ts_Mean(ts_TransNorm(df['cci'],6),6),ts_Argmin(df['p2_et6'],26),42,'D')","857_-1*ts_Regression(ts_Mean(ts_TransNorm(df['cci'],6),6),ts_Argmin(df['p2_et6'],26),42,'D')",11.9507,0.0054,1.0487,3.5808,0.5805027211102165,2024-01-01 20:59:00+00:00,2025-01-30 18:59:00+00:00,0.475,0.66,0.4185,-0.385
800,"ts_Regression(Add(Exp(df['p6_tn8']),get_LINEARREG_ANGLE(df['p2_et11'],16)),Max(ts_TransNorm(df['p5_to0'],41),ts_Rank(df['p6_tn0'],16)),45,'D')","856_ts_Regression(Add(Exp(df['p6_tn8']),get_LINEARREG_ANGLE(df['p2_et11'],16)),Max(ts_TransNorm(df['p5_to0'],41),ts_Rank(df['p6_tn0'],16)),45,'D')",13.9004,0.0059,1.2197,4.2195,0.5337532258631154,2024-01-01 20:59:00+00:00,2025-01-30 18:59:00+00:00,0.774,0.546,0.5864,-0.29
801,"-1*ts_Regression(ts_Mean(ts_TransNorm(df['cci'],49),49),ts_Corr2(df['p6_tn10'],df['p6_tn1'],13),6,'D')","857_-1*ts_Regression(ts_Mean(ts_TransNorm(df['cci'],49),49),ts_Corr2(df['p6_tn10'],df['p6_tn1'],13),6,'D')",10.2274,0.0044,0.8933,3.2292,0.41450796926827066,2024-01-01 20:59:00+00:00,2025-01-30 18:59:00+00:00,0.357,0.61,0.3692,-0.291
802,"ts_Regression(Add(pn_TransNorm(df['p4_ms6']),get_LINEARREG_ANGLE(df['p2_et11'],16)),pn_Stand(df['p5_to5']),16,'D')","858_ts_Regression(Add(pn_TransNorm(df['p4_ms6']),get_LINEARREG_ANGLE(df['p2_et11'],16)),pn_Stand(df['p5_to5']),16,'D')",13.0143,0.0044,1.1741,3.1675,0.5674061672238033,2024-01-01 20:59:00+00:00,2025-01-30 18:59:00+00:00,0.48,0.791,0.3777,-0.202
803,"-1*ts_Regression(Add(df['p1_corrs3'],df['p6_tn1']),ts_Corr2(df['p2_et12'],df['p6_tn4'],17),17,'D')","859_-1*ts_Regression(Add(df['p1_corrs3'],df['p6_tn1']),ts_Corr2(df['p2_et12'],df['p6_tn4'],17),17,'D')",9.2829,0.0038,0.8088,3.0169,0.5809823984012225,2024-01-01 20:59:00+00:00,2025-01-30 18:59:00+00:00,0.237,0.638,0.2709,-0.166
804,"-1*Max(ts_Scale(pn_Winsor(df['cci'],16),21),ts_Mean(df['p6_tn11'],43))","860_-1*Max(ts_Scale(pn_Winsor(df['cci'],16),21),ts_Mean(df['p6_tn11'],43))",11.8705,0.005,1.0618,3.0048,0.4903211005081588,2024-01-01 20:59:00+00:00,2025-01-30 18:59:00+00:00,0.482,0.667,0.4195,-0.241
805,"-1*ts_Regression(pn_GroupRank(pn_TransStd(df['p4_ms6']),ts_Sum(df['p6_tn0'],14)),ts_Stdev2(get_LINEARREG_ANGLE(df['p2_et11'],49),49),49,'D')","861_-1*ts_Regression(pn_GroupRank(pn_TransStd(df['p4_ms6']),ts_Sum(df['p6_tn0'],14)),ts_Stdev2(get_LINEARREG_ANGLE(df['p2_et11'],49),49),49,'D')",9.6103,0.0043,0.8288,3.327,0.38713400742788384,2024-01-01 20:59:00+00:00,2025-01-30 18:59:00+00:00,0.205,0.692,0.2285,-0.146
806,"-1*ts_Regression(Add(df['p2_et0'],ts_Decay2(df['ultosc'],5)),Power(ts_Decay2(df['ultosc'],5),45),36,'D')","861_-1*ts_Regression(Add(df['p2_et0'],ts_Decay2(df['ultosc'],5)),Power(ts_Decay2(df['ultosc'],5),45),36,'D')",10.6149,0.0046,0.925,3.4101,0.36707444336766404,2024-01-01 20:59:00+00:00,2025-01-30 18:59:00+00:00,0.422,0.579,0.4216,-0.261
807,"-1*ts_Regression(Add(df['p2_et0'],ts_Sum(df['cci'],42)),ts_Stdev2(df['p2_et18'],39),5,'D')","862_-1*ts_Regression(Add(df['p2_et0'],ts_Sum(df['cci'],42)),ts_Stdev2(df['p2_et18'],39),5,'D')",9.4899,0.0049,0.8174,3.2115,0.4460296172008787,2024-01-01 20:59:00+00:00,2025-01-30 18:59:00+00:00,0.368,0.516,0.4163,-0.254
808,"-1*ts_Regression(Add(df['p1_corrs3'],pn_TransStd(df['p4_ms6'])),ts_Corr2(ts_Decay2(df['ultosc'],5),get_CCI(df['p4_ms6'],df['dcphase'],df['p3_mf5'],6),45),17,'D')","864_-1*ts_Regression(Add(df['p1_corrs3'],pn_TransStd(df['p4_ms6'])),ts_Corr2(ts_Decay2(df['ultosc'],5),get_CCI(df['p4_ms6'],df['dcphase'],df['p3_mf5'],6),45),17,'D')",10.3885,0.0048,0.9056,3.2779,0.5908985161532365,2024-01-01 20:59:00+00:00,2025-01-30 18:59:00+00:00,0.345,0.635,0.352,-0.174
809,"-1*ts_Regression(ts_Median(df['p2_et18'],3),ts_Regression(df['p3_mf4'],get_LINEARREG_ANGLE(df['cci'],17),32,'A'),49,'D')","865_-1*ts_Regression(ts_Median(df['p2_et18'],3),ts_Regression(df['p3_mf4'],get_LINEARREG_ANGLE(df['cci'],17),32,'A'),49,'D')",11.3219,0.0032,1.0088,3.2252,0.5487374990390735,2024-01-01 20:59:00+00:00,2025-01-30 18:59:00+00:00,0.368,0.724,0.337,0.026
810,"-1*ts_Regression(Add(df['p4_ms0'],ts_Decay2(df['ultosc'],21)),ts_Stdev2(Min(df['p3_mf7'],df['ultosc']),39),27,'D')","866_-1*ts_Regression(Add(df['p4_ms0'],ts_Decay2(df['ultosc'],21)),ts_Stdev2(Min(df['p3_mf7'],df['ultosc']),39),27,'D')",9.6946,0.0043,0.8437,3.1276,0.49751318122781313,2024-01-01 20:59:00+00:00,2025-01-30 18:59:00+00:00,0.441,0.473,0.4825,-0.262
811,"-1*ts_Regression(pn_TransStd(df['p4_ms6']),ts_Corr2(ts_Stdev2(df['p2_et18'],39),df['ultosc'],42),17,'D')","867_-1*ts_Regression(pn_TransStd(df['p4_ms6']),ts_Corr2(ts_Stdev2(df['p2_et18'],39),df['ultosc'],42),17,'D')",10.546,0.005,0.9224,3.2138,0.39388224032611063,2024-01-01 20:59:00+00:00,2025-01-30 18:59:00+00:00,0.389,0.609,0.3898,-0.098
812,"-1*ts_Regression(Reverse(df['p6_tn11']),pn_GroupRank(ts_Scale(df['p2_et4'],18),ts_Skewness(df['cci'],26)),16,'D')","868_-1*ts_Regression(Reverse(df['p6_tn11']),pn_GroupRank(ts_Scale(df['p2_et4'],18),ts_Skewness(df['cci'],26)),16,'D')",10.0235,0.0036,0.8795,3.1383,0.56505811945324,2024-01-01 20:59:00+00:00,2025-01-30 18:59:00+00:00,0.276,0.675,0.2902,-0.16
813,"ts_Regression(ts_MeanChg(df['ultosc'],12),ts_Regression(Max(df['p5_to2'],df['p2_et4']),get_LINEARREG_ANGLE(df['p2_et11'],17),6,'A'),49,'D')","867_ts_Regression(ts_MeanChg(df['ultosc'],12),ts_Regression(Max(df['p5_to2'],df['p2_et4']),get_LINEARREG_ANGLE(df['p2_et11'],17),6,'A'),49,'D')",14.1768,0.0063,1.2423,4.3227,0.5358251443501663,2024-01-01 20:59:00+00:00,2025-01-30 18:59:00+00:00,0.736,0.608,0.5476,-0.269
814,"-1*ts_Regression(Add(Add(df['p6_tn4'],df['p3_mf4']),ts_Sum(df['cci'],42)),pn_GroupRank(get_HT_DCPERIOD(df['p3_mf10']),Multiply(df['p4_ms2'],df['p5_to4'])),16,'D')","867_-1*ts_Regression(Add(Add(df['p6_tn4'],df['p3_mf4']),ts_Sum(df['cci'],42)),pn_GroupRank(get_HT_DCPERIOD(df['p3_mf10']),Multiply(df['p4_ms2'],df['p5_to4'])),16,'D')",12.0234,0.0052,1.0584,3.5403,0.5062323625987526,2024-01-01 20:59:00+00:00,2025-01-30 18:59:00+00:00,0.34,0.806,0.2967,-0.273
815,"-1*ts_Regression(ts_Mean(get_HT_DCPERIOD(df['p3_mf10']),6),pn_GroupRank(Reverse(df['cmo']),Multiply(df['p4_ms2'],df['p5_to4'])),32,'D')","868_-1*ts_Regression(ts_Mean(get_HT_DCPERIOD(df['p3_mf10']),6),pn_GroupRank(Reverse(df['cmo']),Multiply(df['p4_ms2'],df['p5_to4'])),32,'D')",9.3841,0.0032,0.8137,3.2572,0.20548886136330474,2024-01-01 20:59:00+00:00,2025-01-30 18:59:00+00:00,0.49,0.39,0.5568,-0.003
816,"-1*ts_Regression(Add(ts_Mean(df['p3_mf8'],49),get_LINEARREG_SLOPE(df['p4_ms3'],49)),pn_GroupRank(Max(df['p2_et13'],df['p3_mf7']),ts_Cov(df['p2_et19'],df['ultosc'],32)),16,'D')","869_-1*ts_Regression(Add(ts_Mean(df['p3_mf8'],49),get_LINEARREG_SLOPE(df['p4_ms3'],49)),pn_GroupRank(Max(df['p2_et13'],df['p3_mf7']),ts_Cov(df['p2_et19'],df['ultosc'],32)),16,'D')",9.705,0.0023,0.8578,3.0306,0.4258289382572014,2024-01-01 20:59:00+00:00,2025-01-30 18:59:00+00:00,0.273,0.655,0.2942,-0.171
817,"ts_Regression(ts_Sum(df['p2_et11'],8),pn_Rank2(Softsign(df['p2_et6'])),16,'D')","870_ts_Regression(ts_Sum(df['p2_et11'],8),pn_Rank2(Softsign(df['p2_et6'])),16,'D')",10.667,0.0037,0.9408,3.2184,0.5704760357459431,2024-01-01 20:59:00+00:00,2025-01-30 18:59:00+00:00,0.581,0.437,0.5707,-0.22
818,"-1*Min(pn_GroupNorm(get_CMO(df['cmo'],9),df['p3_mf0']),ts_CovChg(df['p5_to4'],df['p6_tn7'],36))","871_-1*Min(pn_GroupNorm(get_CMO(df['cmo'],9),df['p3_mf0']),ts_CovChg(df['p5_to4'],df['p6_tn7'],36))",11.781,0.006,1.0297,3.548,0.5821375080291953,2024-01-01 20:59:00+00:00,2025-01-30 18:59:00+00:00,0.563,0.551,0.5054,-0.407
819,"-1*Min(pn_GroupNorm(get_CMO(df['liangle'],9),ts_Quantile(df['p6_tn3'],32,'A')),ts_CovChg(df['p5_to4'],ts_Entropy(df['p3_mf2'],40),36))","870_-1*Min(pn_GroupNorm(get_CMO(df['liangle'],9),ts_Quantile(df['p6_tn3'],32,'A')),ts_CovChg(df['p5_to4'],ts_Entropy(df['p3_mf2'],40),36))",10.4671,0.0051,0.9099,3.3432,0.5881179657597428,2024-01-01 20:59:00+00:00,2025-01-30 18:59:00+00:00,0.689,0.296,0.6995,-0.38
820,"-1*Min(pn_GroupNorm(ts_MeanChg(df['di'],9),Lthan(df['p4_ms2'],df['p6_tn3'])),df['p2_et0'])","870_-1*Min(pn_GroupNorm(ts_MeanChg(df['di'],9),Lthan(df['p4_ms2'],df['p6_tn3'])),df['p2_et0'])",20.2188,0.0077,1.853,3.91,0.5977642082010659,2024-01-01 20:59:00+00:00,2025-01-30 18:59:00+00:00,0.526,1.479,0.2623,-0.379
821,"Min(ts_Delay(df['p1_corrs6'],36),pn_GroupNorm(ts_Entropy(df['p4_ms1'],23),df['p5_to4']))","870_Min(ts_Delay(df['p1_corrs6'],36),pn_GroupNorm(ts_Entropy(df['p4_ms1'],23),df['p5_to4']))",10.0254,0.0024,0.8748,3.4765,0.259475472107088,2024-01-01 20:59:00+00:00,2025-01-30 18:59:00+00:00,0.423,0.524,0.4467,-0.006
822,"Min(pn_GroupNorm(get_CMO(df['p4_ms2'],9),df['p3_mf0']),ts_CovChg(df['p5_to4'],ts_Entropy(df['p3_mf2'],40),36))","871_Min(pn_GroupNorm(get_CMO(df['p4_ms2'],9),df['p3_mf0']),ts_CovChg(df['p5_to4'],ts_Entropy(df['p3_mf2'],40),36))",9.3796,0.0041,0.8078,3.2933,0.5662087848239439,2024-01-01 20:59:00+00:00,2025-01-30 18:59:00+00:00,0.269,0.605,0.3078,-0.077
823,"-1*Min(pn_GroupNorm(ts_MeanChg(df['di'],1),ts_Argmax(df['p1_corrs1'],1)),ts_CovChg(pn_TransStd(df['p4_ms1']),ts_Delay(df['liangle'],3),36))","872_-1*Min(pn_GroupNorm(ts_MeanChg(df['di'],1),ts_Argmax(df['p1_corrs1'],1)),ts_CovChg(pn_TransStd(df['p4_ms1']),ts_Delay(df['liangle'],3),36))",12.2501,0.0056,1.0895,3.2193,0.5923707559697158,2024-01-01 20:59:00+00:00,2025-01-30 18:59:00+00:00,0.479,0.7,0.4063,-0.121
824,"-1*Min(pn_GroupNorm(ts_ChgRate(df['p6_tn5'],44),ts_Mean(df['p4_ms2'],36)),ts_CovChg(pn_TransStd(df['p4_ms1']),ts_Delay(df['liangle'],3),36))","873_-1*Min(pn_GroupNorm(ts_ChgRate(df['p6_tn5'],44),ts_Mean(df['p4_ms2'],36)),ts_CovChg(pn_TransStd(df['p4_ms1']),ts_Delay(df['liangle'],3),36))",9.8273,0.0043,0.8556,3.1625,0.4029405927151312,2024-01-01 20:59:00+00:00,2025-01-30 18:59:00+00:00,0.564,0.362,0.6091,-0.129
825,"Min(ts_Max(Log(df['p6_tn6']),39),ts_CovChg(ts_Delta(df['liangle'],40),SignedPower(df['p3_mf12'],df['p2_et4']),36))","874_Min(ts_Max(Log(df['p6_tn6']),39),ts_CovChg(ts_Delta(df['liangle'],40),SignedPower(df['p3_mf12'],df['p2_et4']),36))",9.7537,0.0023,0.8586,3.152,0.28294105187190643,2024-01-01 20:59:00+00:00,2025-01-30 18:59:00+00:00,0.541,0.388,0.5823,-0.059
826,"-1*Min(pn_GroupNorm(ts_MeanChg(df['di'],1),ts_Delta(df['p4_ms3'],1)),ts_CovChg(get_CMO(df['p5_to3'],21),ts_Stdev2(df['p4_ms4'],46),36))","875_-1*Min(pn_GroupNorm(ts_MeanChg(df['di'],1),ts_Delta(df['p4_ms3'],1)),ts_CovChg(get_CMO(df['p5_to3'],21),ts_Stdev2(df['p4_ms4'],46),36))",9.8886,0.0043,0.8621,3.1507,0.5384429925049246,2024-01-01 20:59:00+00:00,2025-01-30 18:59:00+00:00,0.429,0.504,0.4598,-0.083
827,"Min(pn_GroupNorm(get_CMO(df['di'],9),ts_Mean(df['p4_ms1'],36)),get_HT_DCPHASE(ts_Quantile(df['p2_et7'],35,'D')))","876_Min(pn_GroupNorm(get_CMO(df['di'],9),ts_Mean(df['p4_ms1'],36)),get_HT_DCPHASE(ts_Quantile(df['p2_et7'],35,'D')))",10.5494,0.005,0.9182,3.357,0.546101353211816,2024-01-01 20:59:00+00:00,2025-01-30 18:59:00+00:00,0.533,0.46,0.5368,-0.206
828,"-1*Min(pn_GroupNorm(Sign(df['p5_to0']),ts_Mean(df['p4_ms2'],36)),ts_CovChg(pn_TransStd(df['p4_ms1']),ts_MeanChg(df['p4_ms1'],1),36))","877_-1*Min(pn_GroupNorm(Sign(df['p5_to0']),ts_Mean(df['p4_ms2'],36)),ts_CovChg(pn_TransStd(df['p4_ms1']),ts_MeanChg(df['p4_ms1'],1),36))",12.0039,0.0052,1.0679,3.1898,0.5466147383390586,2024-01-01 20:59:00+00:00,2025-01-30 18:59:00+00:00,0.259,0.896,0.2242,-0.111
829,"Min(pn_GroupNorm(ts_MeanChg(df['p3_mf2'],1),ts_CovChg(df['p3_mf12'],df['liangle'],36)),ts_CovChg(ts_CovChg(df['p5_to4'],df['di'],36),df['p2_et5'],36))","878_Min(pn_GroupNorm(ts_MeanChg(df['p3_mf2'],1),ts_CovChg(df['p3_mf12'],df['liangle'],36)),ts_CovChg(ts_CovChg(df['p5_to4'],df['di'],36),df['p2_et5'],36))",10.8834,0.0041,0.9461,3.6457,0.4143825912452309,2024-01-01 20:59:00+00:00,2025-01-30 18:59:00+00:00,0.469,0.555,0.458,-0.223
830,"-1*Min(pn_GroupNorm(ts_MeanChg(df['di'],1),get_MINUS_DI(df['p6_tn0'],df['p3_mf0'],df['p2_et9'],30)),Exp(Reverse(df['p5_to7'])))","878_-1*Min(pn_GroupNorm(ts_MeanChg(df['di'],1),get_MINUS_DI(df['p6_tn0'],df['p3_mf0'],df['p2_et9'],30)),Exp(Reverse(df['p5_to7'])))",16.2662,0.0077,1.4323,4.6716,0.5976714819165728,2024-01-01 20:59:00+00:00,2025-01-30 18:59:00+00:00,0.724,0.826,0.4671,-0.196
831,"-1*Min(pn_GroupNorm(ts_MeanChg(df['di'],1),inv(df['di'])),pn_GroupNorm(ts_MeanChg(df['di'],9),ts_CovChg(df['p3_mf12'],df['p2_et8'],27)))","878_-1*Min(pn_GroupNorm(ts_MeanChg(df['di'],1),inv(df['di'])),pn_GroupNorm(ts_MeanChg(df['di'],9),ts_CovChg(df['p3_mf12'],df['p2_et8'],27)))",15.4316,0.0073,1.3526,4.615,0.5956876191191915,2024-01-01 20:59:00+00:00,2025-01-30 18:59:00+00:00,0.642,0.821,0.4388,-0.286
832,"Min(pn_GroupNorm(get_CMO(df['di'],9),ts_Mean(df['p4_ms2'],36)),Divide(df['p3_mf12'],get_LINEARREG_SLOPE(df['p3_mf2'],28)))","880_Min(pn_GroupNorm(get_CMO(df['di'],9),ts_Mean(df['p4_ms2'],36)),Divide(df['p3_mf12'],get_LINEARREG_SLOPE(df['p3_mf2'],28)))",9.3428,0.0042,0.8121,3.0366,0.5103972999411662,2024-01-01 20:59:00+00:00,2025-01-30 18:59:00+00:00,0.378,0.501,0.43,-0.241
833,"-1*Min(pn_GroupNorm(Mthan(df['p2_et0'],df['p2_et5']),ts_CovChg(df['p3_mf12'],df['di'],27)),pn_Winsor(df['p2_et9'],1))","881_-1*Min(pn_GroupNorm(Mthan(df['p2_et0'],df['p2_et5']),ts_CovChg(df['p3_mf12'],df['di'],27)),pn_Winsor(df['p2_et9'],1))",15.2845,0.0061,1.3476,4.5095,0.5189833130992837,2024-01-01 20:59:00+00:00,2025-01-30 18:59:00+00:00,0.405,1.053,0.2778,-0.197
834,"-1*Min(pn_GroupNorm(ts_ChgRate(df['p6_tn5'],46),ts_Stdev2(df['p4_ms4'],36)),Exp(df['liangle']))","882_-1*Min(pn_GroupNorm(ts_ChgRate(df['p6_tn5'],46),ts_Stdev2(df['p4_ms4'],36)),Exp(df['liangle']))",11.6266,0.0059,1.006,3.8193,0.553888850925359,2024-01-01 20:59:00+00:00,2025-01-30 18:59:00+00:00,0.353,0.736,0.3242,-0.238
835,"Min(pn_GroupNorm(ts_MeanChg(df['p1_corrs1'],1),get_LINEARREG_SLOPE(df['p1_corrs3'],48)),Divide(ts_Decay(df['p5_to4'],35),0.909))","884_Min(pn_GroupNorm(ts_MeanChg(df['p1_corrs1'],1),get_LINEARREG_SLOPE(df['p1_corrs3'],48)),Divide(ts_Decay(df['p5_to4'],35),0.909))",13.2988,0.0054,1.1457,4.7133,0.5009023618393055,2024-01-01 20:59:00+00:00,2025-01-30 18:59:00+00:00,0.642,0.598,0.5177,-0.114
836,"-1*Min(pn_GroupNorm(pn_RankCentered(df['cmo']),ts_Mean(df['p4_ms2'],36)),ts_CovChg(pn_TransStd(df['p4_ms1']),ts_MeanChg(df['di'],36),36))","885_-1*Min(pn_GroupNorm(pn_RankCentered(df['cmo']),ts_Mean(df['p4_ms2'],36)),ts_CovChg(pn_TransStd(df['p4_ms1']),ts_MeanChg(df['di'],36),36))",10.5369,0.0059,0.9226,3.0528,0.588226947233593,2024-01-01 20:59:00+00:00,2025-01-30 18:59:00+00:00,0.508,0.49,0.509,-0.445
837,"-1*Min(pn_GroupNorm(pn_RankCentered(df['cmo']),ts_CovChg(df['p2_et9'],df['p3_mf11'],41)),ts_CovChg(Softsign(df['dcperiod']),ts_MeanChg(df['di'],50),1))","886_-1*Min(pn_GroupNorm(pn_RankCentered(df['cmo']),ts_CovChg(df['p2_et9'],df['p3_mf11'],41)),ts_CovChg(Softsign(df['dcperiod']),ts_MeanChg(df['di'],50),1))",10.7477,0.0051,0.9353,3.4254,0.5902415597148095,2024-01-01 20:59:00+00:00,2025-01-30 18:59:00+00:00,0.705,0.307,0.6966,-0.444
838,"-1*Min(pn_GroupNorm(ts_Rank(df['p3_mf11'],29),ts_Median(df['p1_corrs5'],47)),ts_CovChg(df['p5_to4'],ts_TransNorm(df['p1_corrs3'],1),1))","887_-1*Min(pn_GroupNorm(ts_Rank(df['p3_mf11'],29),ts_Median(df['p1_corrs5'],47)),ts_CovChg(df['p5_to4'],ts_TransNorm(df['p1_corrs3'],1),1))",12.3815,0.0045,1.0708,4.3483,0.5396645255844295,2024-01-01 20:59:00+00:00,2025-01-30 18:59:00+00:00,0.791,0.368,0.6825,-0.128
839,"Min(pn_GroupNorm(ts_MeanChg(df['p3_mf2'],1),ts_Kurtosis(df['p3_mf2'],1)),ts_Skewness(df['p3_mf12'],16))","888_Min(pn_GroupNorm(ts_MeanChg(df['p3_mf2'],1),ts_Kurtosis(df['p3_mf2'],1)),ts_Skewness(df['p3_mf12'],16))",10.3121,0.0033,0.89,3.741,0.5996946062677386,2024-01-01 20:59:00+00:00,2025-01-30 18:59:00+00:00,0.566,0.398,0.5871,-0.164
840,"-1*Min(pn_GroupNorm(get_CMO(df['liangle'],40),get_LINEARREG_SLOPE(df['cci'],45)),ts_Cov(get_LINEARREG_SLOPE(df['p4_ms5'],45),df['p4_ms4'],36))","888_-1*Min(pn_GroupNorm(get_CMO(df['liangle'],40),get_LINEARREG_SLOPE(df['cci'],45)),ts_Cov(get_LINEARREG_SLOPE(df['p4_ms5'],45),df['p4_ms4'],36))",13.4811,0.0062,1.1961,3.6374,0.5461319504719353,2024-01-01 20:59:00+00:00,2025-01-30 18:59:00+00:00,0.527,0.767,0.4073,-0.457
841,"Min(pn_GroupNorm(ts_MeanChg(df['p3_mf2'],1),ts_Kurtosis(df['p4_ms4'],28)),ts_Mean(df['p5_to4'],31))","888_Min(pn_GroupNorm(ts_MeanChg(df['p3_mf2'],1),ts_Kurtosis(df['p4_ms4'],28)),ts_Mean(df['p5_to4'],31))",10.3882,0.0039,0.8992,3.6052,0.5695490821121622,2024-01-01 20:59:00+00:00,2025-01-30 18:59:00+00:00,0.689,0.284,0.7081,-0.182
842,"-1*Min(pn_GroupNorm(get_CMO(df['liangle'],40),ts_ChgRate(df['cmo'],36)),LEthan(df['p2_et9'],ts_MeanChg(df['p4_ms2'],1)))","889_-1*Min(pn_GroupNorm(get_CMO(df['liangle'],40),ts_ChgRate(df['cmo'],36)),LEthan(df['p2_et9'],ts_MeanChg(df['p4_ms2'],1)))",11.7623,0.0061,1.0288,3.509,0.5944202899331776,2024-01-01 20:59:00+00:00,2025-01-30 18:59:00+00:00,0.591,0.523,0.5305,-0.422
843,"-1*Min(pn_GroupNorm(ts_MeanChg(df['di'],1),get_KAMA(df['p3_mf0'],2)),get_MINUS_DI(get_MINUS_DI(df['p2_et2'],df['cci'],df['p5_to1'],47),Power(df['p3_mf8'],49),df['p6_tn3'],1))","890_-1*Min(pn_GroupNorm(ts_MeanChg(df['di'],1),get_KAMA(df['p3_mf0'],2)),get_MINUS_DI(get_MINUS_DI(df['p2_et2'],df['cci'],df['p5_to1'],47),Power(df['p3_mf8'],49),df['p6_tn3'],1))",9.751,0.0039,0.8461,3.2799,0.5821344439825359,2024-01-01 20:59:00+00:00,2025-01-30 18:59:00+00:00,0.625,0.29,0.6831,-0.132
844,"-1*Min(pn_GroupNorm(get_CMO(df['liangle'],40),ts_Median(df['p5_to4'],39)),Minus(FilterInf(df['p1_corrs1']),0.641))","890_-1*Min(pn_GroupNorm(get_CMO(df['liangle'],40),ts_Median(df['p5_to4'],39)),Minus(FilterInf(df['p1_corrs1']),0.641))",15.6943,0.007,1.4171,3.5122,0.5789171261151235,2024-01-01 20:59:00+00:00,2025-01-30 18:59:00+00:00,0.512,1.022,0.3338,-0.516
845,"-1*Divide(Abs(SignedPower(df['p6_tn1'],0.909)),get_CMO(df['cci'],20))","892_-1*Divide(Abs(SignedPower(df['p6_tn1'],0.909)),get_CMO(df['cci'],20))",13.1495,0.0058,1.1446,4.2429,0.4912984445113618,2024-01-01 20:59:00+00:00,2025-01-30 18:59:00+00:00,0.456,0.782,0.3683,-0.206
846,"-1*Divide(ts_Divide(pn_Rank(df['p6_tn10']),9),pn_GroupRank(Divide(df['p2_et18'],df['cci']),get_HT_DCPERIOD(df['p5_to7'])))","893_-1*Divide(ts_Divide(pn_Rank(df['p6_tn10']),9),pn_GroupRank(Divide(df['p2_et18'],df['cci']),get_HT_DCPERIOD(df['p5_to7'])))",14.3763,0.0064,1.2778,3.8392,0.5680880797705209,2024-01-01 20:59:00+00:00,2025-01-30 18:59:00+00:00,0.804,0.579,0.5813,-0.238
847,"-1*Min(Min(ts_Corr(df['lislope'],df['p1_corrs9'],3),df['lislope']),ts_Entropy(ts_Max(df['p2_et8'],32),9))","894_-1*Min(Min(ts_Corr(df['lislope'],df['p1_corrs9'],3),df['lislope']),ts_Entropy(ts_Max(df['p2_et8'],32),9))",9.9919,0.0034,0.8676,3.4403,0.40406027352903656,2024-01-01 20:59:00+00:00,2025-01-30 18:59:00+00:00,0.272,0.667,0.2897,-0.172
848,"-1*ts_Delay(ts_Divide(Abs(df['p2_et3']),29),35)","895_-1*ts_Delay(ts_Divide(Abs(df['p2_et3']),29),35)",9.5518,0.0037,0.8292,3.2181,0.28711553731007766,2024-01-01 20:59:00+00:00,2025-01-30 18:59:00+00:00,0.403,0.495,0.4488,0.005
849,"ts_Delay(ts_MeanChg(ts_TransNorm(df['cci'],30),12),35)","896_ts_Delay(ts_MeanChg(ts_TransNorm(df['cci'],30),12),35)",17.1699,0.0047,1.5627,3.9203,0.40375764586916185,2024-01-01 20:59:00+00:00,2025-01-30 18:59:00+00:00,0.548,1.143,0.3241,0.005
850,"Min(get_LINEARREG_SLOPE(df['p4_ms0'],14),Reverse(df['p2_et4']))","897_Min(get_LINEARREG_SLOPE(df['p4_ms0'],14),Reverse(df['p2_et4']))",18.8438,0.0078,1.7427,3.0766,0.5552262090617935,2024-01-01 20:59:00+00:00,2025-01-30 18:59:00+00:00,0.932,0.954,0.4942,-0.123
851,"-1*Min(df['p2_et4'],ts_TransNorm(pn_Stand(df['p5_to0']),28))","895_-1*Min(df['p2_et4'],ts_TransNorm(pn_Stand(df['p5_to0']),28))",19.5289,0.0081,1.7932,3.5801,0.5913804867354427,2024-01-01 20:59:00+00:00,2025-01-30 18:59:00+00:00,0.534,1.406,0.2753,-0.178
852,"-1*Min(pn_GroupNorm(df['p5_to0'],df['p5_to7']),Max(ts_Corr2(df['p2_et14'],df['p3_mf12'],27),Sqrt(df['p6_tn6'])))","897_-1*Min(pn_GroupNorm(df['p5_to0'],df['p5_to7']),Max(ts_Corr2(df['p2_et14'],df['p3_mf12'],27),Sqrt(df['p6_tn6'])))",11.1855,0.0048,0.9706,3.7255,0.5762683376228217,2024-01-01 20:59:00+00:00,2025-01-30 18:59:00+00:00,0.629,0.421,0.599,-0.06
853,"-1*Min(ts_Delta(Power(df['p2_et2'],35),18),SignedPower(ts_Regression(df['p2_et9'],df['p3_mf10'],3,'A'),ts_Median(df['p3_mf4'],31)))","898_-1*Min(ts_Delta(Power(df['p2_et2'],35),18),SignedPower(ts_Regression(df['p2_et9'],df['p3_mf10'],3,'A'),ts_Median(df['p3_mf4'],31)))",15.4196,0.0053,1.4059,3.2913,0.4447292126563594,2024-01-01 20:59:00+00:00,2025-01-30 18:59:00+00:00,0.387,1.135,0.2543,-0.139
854,"-1*Min(ts_CorrChg(df['cci'],df['p2_et8'],18),df['cci'])","899_-1*Min(ts_CorrChg(df['cci'],df['p2_et8'],18),df['cci'])",11.9153,0.0059,1.0449,3.5063,0.5686013234352908,2024-01-01 20:59:00+00:00,2025-01-30 18:59:00+00:00,0.563,0.568,0.4978,-0.381
855,"-1*Min(get_CCI(df['cci'],ts_Kurtosis(df['dcperiod'],1),ts_Divide(df['dx'],43),36),ts_Corr(df['cci'],ts_Stdev(df['p4_ms6'],3),24))","898_-1*Min(get_CCI(df['cci'],ts_Kurtosis(df['dcperiod'],1),ts_Divide(df['dx'],43),36),ts_Corr(df['cci'],ts_Stdev(df['p4_ms6'],3),24))",19.6645,0.0077,1.8042,3.7094,0.5979976058419149,2024-01-01 20:59:00+00:00,2025-01-30 18:59:00+00:00,0.495,1.458,0.2535,-0.365
856,"-1*Min(get_CCI(ts_CovChg(df['p2_et16'],df['cci'],21),ts_Scale(df['p2_et7'],25),df['p1_corrs0'],25),ts_Skewness(pn_Stand(df['p2_et11']),3))","899_-1*Min(get_CCI(ts_CovChg(df['p2_et16'],df['cci'],21),ts_Scale(df['p2_et7'],25),df['p1_corrs0'],25),ts_Skewness(pn_Stand(df['p2_et11']),3))",11.1518,0.0041,0.9874,3.2181,0.4493987095028687,2024-01-01 20:59:00+00:00,2025-01-30 18:59:00+00:00,0.574,0.495,0.537,-0.284
857,"-1*Min(get_CCI(ts_Rank(df['p2_et18'],12),ts_Scale(df['p2_et7'],25),ts_Delta(df['p6_tn2'],24),40),get_MINUS_DM(df['cci'],get_LINEARREG_SLOPE(df['cci'],24),24))","900_-1*Min(get_CCI(ts_Rank(df['p2_et18'],12),ts_Scale(df['p2_et7'],25),ts_Delta(df['p6_tn2'],24),40),get_MINUS_DM(df['cci'],get_LINEARREG_SLOPE(df['cci'],24),24))",14.5655,0.0062,1.2954,3.9095,0.5331303656668517,2024-01-01 20:59:00+00:00,2025-01-30 18:59:00+00:00,0.691,0.71,0.4932,-0.354
858,"-1*pn_Winsor(ts_Delta(pn_Rank(df['p2_et7']),43),15)","901_-1*pn_Winsor(ts_Delta(pn_Rank(df['p2_et7']),43),15)",17.6099,0.0077,1.5771,4.3594,0.5763415471002666,2024-01-01 20:59:00+00:00,2025-01-30 18:59:00+00:00,0.708,0.999,0.4148,-0.551
859,"Minus(Exp(ts_Divide(df['dm'],3)),Exp(df['dm']))","902_Minus(Exp(ts_Divide(df['dm'],3)),Exp(df['dm']))",11.7637,0.0029,1.0415,3.6104,0.476629567318358,2024-01-01 20:59:00+00:00,2025-01-30 18:59:00+00:00,0.767,0.36,0.6806,-0.062
860,"-1*Add(ts_TransNorm(ts_Scale(df['p1_corrs9'],22),29),ts_TransNorm(df['p2_et14'],28))","903_-1*Add(ts_TransNorm(ts_Scale(df['p1_corrs9'],22),29),ts_TransNorm(df['p2_et14'],28))",10.6237,0.0044,0.9154,3.7514,0.5498079189989367,2024-01-01 20:59:00+00:00,2025-01-30 18:59:00+00:00,0.281,0.709,0.2838,-0.174
861,"ts_TransNorm(ts_TransNorm(ts_Scale(df['dm'],22),29),48)","904_ts_TransNorm(ts_TransNorm(ts_Scale(df['dm'],22),29),48)",10.6156,0.0041,0.9362,3.1486,0.5593893798785989,2024-01-01 20:59:00+00:00,2025-01-30 18:59:00+00:00,0.407,0.606,0.4018,-0.028
862,"-1*Add(ts_Mean(df['p3_mf8'],41),ts_ChgRate(df['p2_et10'],19))","905_-1*Add(ts_Mean(df['p3_mf8'],41),ts_ChgRate(df['p2_et10'],19))",10.2732,0.0044,0.899,3.1963,0.5976098417030381,2024-01-01 20:59:00+00:00,2025-01-30 18:59:00+00:00,0.405,0.568,0.4162,-0.201
863,"-1*Minus(ts_Delta(pn_GroupRank(df['p1_corrs1'],df['p2_et11']),41),df['p6_tn5'])","906_-1*Minus(ts_Delta(pn_GroupRank(df['p1_corrs1'],df['p2_et11']),41),df['p6_tn5'])",10.4969,0.0041,0.9179,3.3333,0.5499013697830079,2024-01-01 20:59:00+00:00,2025-01-30 18:59:00+00:00,0.46,0.534,0.4628,-0.189
864,"Minus(pn_Rank(ts_ChgRate(df['p2_et2'],25)),ts_Sum(pn_Stand(df['p6_tn10']),1))","907_Minus(pn_Rank(ts_ChgRate(df['p2_et2'],25)),ts_Sum(pn_Stand(df['p6_tn10']),1))",14.2657,0.0064,1.2812,3.4051,0.5999303335358658,2024-01-01 20:59:00+00:00,2025-01-30 18:59:00+00:00,0.715,0.671,0.5159,-0.499
865,"-1*pn_GroupNeutral(df['p6_tn13'],ts_Divide(FilterInf(df['p5_to1']),31))","908_-1*pn_GroupNeutral(df['p6_tn13'],ts_Divide(FilterInf(df['p5_to1']),31))",10.1653,0.0046,0.875,3.558,0.5478859213043494,2024-01-01 20:59:00+00:00,2025-01-30 18:59:00+00:00,0.519,0.428,0.548,-0.079
866,"-1*pn_GroupNeutral(pn_Winsor(df['p1_corrs3'],49),pn_GroupRank(pn_Winsor(df['p1_corrs3'],49),pn_Winsor(df['p5_to0'],8)))","909_-1*pn_GroupNeutral(pn_Winsor(df['p1_corrs3'],49),pn_GroupRank(pn_Winsor(df['p1_corrs3'],49),pn_Winsor(df['p5_to0'],8)))",9.9416,0.0043,0.8715,3.0404,0.5163112577721533,2024-01-01 20:59:00+00:00,2025-01-30 18:59:00+00:00,0.241,0.702,0.2556,-0.168
867,"-1*get_CCI(pn_Rank(Add(df['p3_mf12'],df['p4_ms5'])),pn_GroupRank(pn_Winsor(df['p5_to0'],13),pn_Winsor(df['p5_to2'],7)),pn_FillMin(df['p3_mf1']),38)","910_-1*get_CCI(pn_Rank(Add(df['p3_mf12'],df['p4_ms5'])),pn_GroupRank(pn_Winsor(df['p5_to0'],13),pn_Winsor(df['p5_to2'],7)),pn_FillMin(df['p3_mf1']),38)",14.7198,0.0051,1.3448,3.0538,0.47712807570127647,2024-01-01 20:59:00+00:00,2025-01-30 18:59:00+00:00,0.414,1.042,0.2843,-0.112
868,"Abs(pn_Stand(pn_Winsor(df['p5_to6'],11)))","911_Abs(pn_Stand(pn_Winsor(df['p5_to6'],11)))",9.8322,0.0034,0.8558,3.3102,0.4700992947936304,2024-01-01 20:59:00+00:00,2025-01-30 18:59:00+00:00,0.322,0.604,0.3477,-0.014
869,"-1*get_CCI(pn_Rank(Add(df['kama'],df['p4_ms5'])),pn_GroupRank(pn_Winsor(df['p5_to0'],13),pn_GroupNorm(df['p3_mf12'],df['p5_to2'])),pn_FillMin(df['p3_mf1']),38)","912_-1*get_CCI(pn_Rank(Add(df['kama'],df['p4_ms5'])),pn_GroupRank(pn_Winsor(df['p5_to0'],13),pn_GroupNorm(df['p3_mf12'],df['p5_to2'])),pn_FillMin(df['p3_mf1']),38)",15.7899,0.0056,1.4472,3.1078,0.3842120825088949,2024-01-01 20:59:00+00:00,2025-01-30 18:59:00+00:00,0.617,0.95,0.3937,-0.089
870,"-1*ts_ChgRate(pn_GroupRank(pn_Winsor(df['p2_et7'],8),Min(df['kama'],0.635)),20)","913_-1*ts_ChgRate(pn_GroupRank(pn_Winsor(df['p2_et7'],8),Min(df['kama'],0.635)),20)",17.3641,0.0073,1.5506,4.4803,0.5460182891693162,2024-01-01 20:59:00+00:00,2025-01-30 18:59:00+00:00,0.957,0.721,0.5703,-0.501
871,"-1*get_CCI(pn_Rank(Sign(df['p6_tn7'])),pn_GroupRank(pn_Winsor(df['p5_to0'],13),get_MINUS_DI(df['p1_corrs3'],df['p6_tn10'],df['p6_tn10'],11)),pn_GroupRank(Not(df['p2_et3']),df['p6_tn13']),10)","914_-1*get_CCI(pn_Rank(Sign(df['p6_tn7'])),pn_GroupRank(pn_Winsor(df['p5_to0'],13),get_MINUS_DI(df['p1_corrs3'],df['p6_tn10'],df['p6_tn10'],11)),pn_GroupRank(Not(df['p2_et3']),df['p6_tn13']),10)",13.5398,0.0056,1.2072,3.5648,0.4021161347629399,2024-01-01 20:59:00+00:00,2025-01-30 18:59:00+00:00,0.496,0.811,0.3795,-0.037
872,"-1*get_CCI(pn_Rank(Add(df['kama'],df['p4_ms5'])),pn_GroupRank(pn_Winsor(df['p2_et7'],49),ts_Argmin(df['p2_et12'],40)),Min(df['p2_et2'],0.635),38)","915_-1*get_CCI(pn_Rank(Add(df['kama'],df['p4_ms5'])),pn_GroupRank(pn_Winsor(df['p2_et7'],49),ts_Argmin(df['p2_et12'],40)),Min(df['p2_et2'],0.635),38)",14.6373,0.0058,1.3315,3.0993,0.5188335619790494,2024-01-01 20:59:00+00:00,2025-01-30 18:59:00+00:00,0.668,0.773,0.4636,-0.505
873,"-1*pn_GroupNeutral(pn_Rank(pn_Winsor(df['p2_et1'],13)),pn_GroupRank(pn_Winsor(df['p2_et7'],7),pn_Winsor(df['p1_corrs3'],8)))","916_-1*pn_GroupNeutral(pn_Rank(pn_Winsor(df['p2_et1'],13)),pn_GroupRank(pn_Winsor(df['p2_et7'],7),pn_Winsor(df['p1_corrs3'],8)))",10.4579,0.0045,0.921,3.0714,0.5855833882690763,2024-01-01 20:59:00+00:00,2025-01-30 18:59:00+00:00,0.411,0.586,0.4122,0.044
874,"-1*pn_GroupNeutral(ts_Scale(df['p2_et1'],31),ts_Sum(df['p3_mf7'],50))","916_-1*pn_GroupNeutral(ts_Scale(df['p2_et1'],31),ts_Sum(df['p3_mf7'],50))",13.3153,0.0053,1.1569,4.4474,0.5519368920121456,2024-01-01 20:59:00+00:00,2025-01-30 18:59:00+00:00,0.559,0.693,0.4465,-0.126
875,"-1*ts_ChgRate(pn_GroupRank(pn_Winsor(df['p5_to0'],8),pn_Rank(df['p5_to2'])),20)","917_-1*ts_ChgRate(pn_GroupRank(pn_Winsor(df['p5_to0'],8),pn_Rank(df['p5_to2'])),20)",10.4542,0.0041,0.9091,3.4706,0.48475890471628985,2024-01-01 20:59:00+00:00,2025-01-30 18:59:00+00:00,0.318,0.666,0.3232,-0.102
876,"-1*pn_GroupNeutral(pn_Rank(df['p6_tn10']),pn_GroupRank(get_KAMA(df['p3_mf7'],50),pn_Winsor(df['p1_corrs3'],8)))","914_-1*pn_GroupNeutral(pn_Rank(df['p6_tn10']),pn_GroupRank(get_KAMA(df['p3_mf7'],50),pn_Winsor(df['p1_corrs3'],8)))",22.7993,0.0107,2.0495,5.3039,0.5986070177059002,2024-01-01 20:59:00+00:00,2025-01-30 18:59:00+00:00,1.036,1.182,0.4671,-0.32
877,"-1*ts_ChgRate(pn_GroupRank(pn_Winsor(df['p2_et1'],8),Exp(df['kama'])),20)","915_-1*ts_ChgRate(pn_GroupRank(pn_Winsor(df['p2_et1'],8),Exp(df['kama'])),20)",12.7077,0.005,1.1153,3.9154,0.5535054872009715,2024-01-01 20:59:00+00:00,2025-01-30 18:59:00+00:00,0.764,0.443,0.633,-0.118
878,"-1*pn_GroupNeutral(pn_Rank(pn_GroupNorm(df['p4_ms1'],df['p4_ms1'])),ts_Skewness(df['p6_tn6'],19))","916_-1*pn_GroupNeutral(pn_Rank(pn_GroupNorm(df['p4_ms1'],df['p4_ms1'])),ts_Skewness(df['p6_tn6'],19))",10.4385,0.0042,0.8941,3.8628,0.4388791753812038,2024-01-01 20:59:00+00:00,2025-01-30 18:59:00+00:00,0.47,0.498,0.4855,-0.047
879,"-1*ts_ChgRate(pn_Rank(pn_Rank(df['p2_et7'])),7)","917_-1*ts_ChgRate(pn_Rank(pn_Rank(df['p2_et7'])),7)",18.0564,0.0081,1.6154,4.489,0.5830028497348322,2024-01-01 20:59:00+00:00,2025-01-30 18:59:00+00:00,0.901,0.847,0.5154,-0.518
880,"-1*ts_ChgRate(pn_GroupRank(pn_Winsor(df['p2_et7'],13),Min(df['p4_ms4'],0.635)),31)","918_-1*ts_ChgRate(pn_GroupRank(pn_Winsor(df['p2_et7'],13),Min(df['p4_ms4'],0.635)),31)",10.9469,0.0049,0.9553,3.4436,0.4673995980857157,2024-01-01 20:59:00+00:00,2025-01-30 18:59:00+00:00,0.865,0.168,0.8374,-0.454
881,"-1*pn_GroupNeutral(ts_Scale(pn_Winsor(df['p6_tn10'],13),31),pn_Rank2(ts_Divide(df['p6_tn10'],13)))","919_-1*pn_GroupNeutral(ts_Scale(pn_Winsor(df['p6_tn10'],13),31),pn_Rank2(ts_Divide(df['p6_tn10'],13)))",14.505,0.0063,1.2847,4.0268,0.5739653842474451,2024-01-01 20:59:00+00:00,2025-01-30 18:59:00+00:00,0.55,0.841,0.3954,-0.15
882,"-1*ts_ChgRate(pn_GroupRank(pn_Winsor(df['p6_tn10'],8),Min(df['p4_ms0'],20)),20)","920_-1*ts_ChgRate(pn_GroupRank(pn_Winsor(df['p6_tn10'],8),Min(df['p4_ms0'],20)),20)",9.4143,0.0038,0.8197,3.0792,0.5849400188535638,2024-01-01 20:59:00+00:00,2025-01-30 18:59:00+00:00,0.675,0.212,0.761,-0.177
883,"-1*pn_GroupNeutral(pn_Winsor(df['p2_et7'],7),pn_GroupRank(pn_Winsor(df['p2_et7'],7),pn_Winsor(df['p1_corrs3'],21)))","921_-1*pn_GroupNeutral(pn_Winsor(df['p2_et7'],7),pn_GroupRank(pn_Winsor(df['p2_et7'],7),pn_Winsor(df['p1_corrs3'],21)))",12.44,0.0054,1.1022,3.4438,0.5667305627564929,2024-01-01 20:59:00+00:00,2025-01-30 18:59:00+00:00,0.459,0.734,0.3847,-0.239
884,"-1*pn_GroupNeutral(pn_Rank(pn_Winsor(df['p6_tn10'],13)),pn_Rank(pn_Winsor(df['p6_tn10'],13)))","922_-1*pn_GroupNeutral(pn_Rank(pn_Winsor(df['p6_tn10'],13)),pn_Rank(pn_Winsor(df['p6_tn10'],13)))",10.2753,0.0048,0.8815,3.6624,0.4666702568077614,2024-01-01 20:59:00+00:00,2025-01-30 18:59:00+00:00,0.63,0.324,0.6604,-0.069
885,"-1*get_CCI(get_LINEARREG_SLOPE(Divide(df['p2_et18'],11),16),pn_GroupRank(pn_Winsor(df['p5_to0'],13),get_MINUS_DI(df['p1_corrs3'],df['p6_tn10'],df['p6_tn10'],11)),FilterInf(Lthan(df['p2_et2'],df['p6_tn10'])),49)","924_-1*get_CCI(get_LINEARREG_SLOPE(Divide(df['p2_et18'],11),16),pn_GroupRank(pn_Winsor(df['p5_to0'],13),get_MINUS_DI(df['p1_corrs3'],df['p6_tn10'],df['p6_tn10'],11)),FilterInf(Lthan(df['p2_et2'],df['p6_tn10'])),49)",17.8654,0.0084,1.599,4.3677,0.44984761344295937,2024-01-01 20:59:00+00:00,2025-01-30 18:59:00+00:00,0.697,1.033,0.4029,-0.12
886,"get_CCI(pn_Rank(pn_GroupNorm(df['p2_et6'],df['p3_mf7'])),pn_GroupRank(Not(df['p6_tn6']),get_MINUS_DI(df['p1_corrs3'],df['p6_tn10'],df['p6_tn10'],11)),get_LINEARREG_ANGLE(ts_TransNorm(df['p4_ms2'],15),14),21)","925_get_CCI(pn_Rank(pn_GroupNorm(df['p2_et6'],df['p3_mf7'])),pn_GroupRank(Not(df['p6_tn6']),get_MINUS_DI(df['p1_corrs3'],df['p6_tn10'],df['p6_tn10'],11)),get_LINEARREG_ANGLE(ts_TransNorm(df['p4_ms2'],15),14),21)",18.3655,0.0039,1.694,3.6945,0.38115868548699505,2024-01-01 20:59:00+00:00,2025-01-30 18:59:00+00:00,1.006,0.827,0.5488,-0.161
887,"-1*pn_GroupNeutral(pn_Rank(pn_Winsor(df['p5_to0'],18)),ts_Delay(df['p4_ms6'],15))","917_-1*pn_GroupNeutral(pn_Rank(pn_Winsor(df['p5_to0'],18)),ts_Delay(df['p4_ms6'],15))",18.157,0.0079,1.5986,5.3301,0.5899668634732477,2024-01-01 20:59:00+00:00,2025-01-30 18:59:00+00:00,0.55,1.18,0.3179,-0.17
888,"-1*get_CCI(pn_Rank(df['kama']),pn_GroupRank(pn_Winsor(df['p5_to0'],10),ts_Corr(df['p6_tn6'],df['p2_et9'],1)),ts_CovChg(df['p2_et6'],ts_Mean(df['p2_et6'],13),13),10)","916_-1*get_CCI(pn_Rank(df['kama']),pn_GroupRank(pn_Winsor(df['p5_to0'],10),ts_Corr(df['p6_tn6'],df['p2_et9'],1)),ts_CovChg(df['p2_et6'],ts_Mean(df['p2_et6'],13),13),10)",17.0297,0.0054,1.5481,3.836,0.5803243768781249,2024-01-01 20:59:00+00:00,2025-01-30 18:59:00+00:00,0.855,0.821,0.5101,-0.041
889,"-1*get_CCI(get_LINEARREG_ANGLE(ts_Delta(df['p5_to5'],19),41),pn_GroupRank(pn_Winsor(df['p5_to0'],13),ts_Decay2(df['p5_to0'],34)),pn_GroupRank(pn_Winsor(df['p5_to0'],10),ts_Rank(df['p5_to0'],13)),10)","917_-1*get_CCI(get_LINEARREG_ANGLE(ts_Delta(df['p5_to5'],19),41),pn_GroupRank(pn_Winsor(df['p5_to0'],13),ts_Decay2(df['p5_to0'],34)),pn_GroupRank(pn_Winsor(df['p5_to0'],10),ts_Rank(df['p5_to0'],13)),10)",14.7717,0.0048,1.3428,3.3148,0.3457252534567969,2024-01-01 20:59:00+00:00,2025-01-30 18:59:00+00:00,0.649,0.804,0.4467,-0.043
890,"get_CCI(get_LINEARREG_SLOPE(pn_Stand(df['p6_tn0']),32),pn_GroupRank(pn_Winsor(df['p2_et10'],18),get_MINUS_DI(df['p1_corrs3'],df['p6_tn10'],df['dm'],11)),ts_CovChg(ts_Partial_corr(df['dm'],df['p6_tn6'],df['cci'],21),ts_Mean(df['p2_et6'],13),13),10)","918_get_CCI(get_LINEARREG_SLOPE(pn_Stand(df['p6_tn0']),32),pn_GroupRank(pn_Winsor(df['p2_et10'],18),get_MINUS_DI(df['p1_corrs3'],df['p6_tn10'],df['dm'],11)),ts_CovChg(ts_Partial_corr(df['dm'],df['p6_tn6'],df['cci'],21),ts_Mean(df['p2_et6'],13),13),10)",15.5267,0.004,1.4286,3.1287,0.48034961175861723,2024-01-01 20:59:00+00:00,2025-01-30 18:59:00+00:00,0.516,1.03,0.3338,-0.241
891,"pn_GroupNeutral(pn_Winsor(df['p5_to0'],22),pn_Rank(df['p5_to0']))","919_pn_GroupNeutral(pn_Winsor(df['p5_to0'],22),pn_Rank(df['p5_to0']))",13.9585,0.005,1.2193,4.54,0.38176598826337066,2024-01-01 20:59:00+00:00,2025-01-30 18:59:00+00:00,0.587,0.733,0.4447,-0.097
892,"-1*get_CCI(pn_Rank(df['kama']),ts_Mean(df['p6_tn13'],49),ts_CovChg(ts_Partial_corr(df['dm'],df['p6_tn6'],df['cci'],21),ts_Mean(df['p2_et6'],13),13),10)","920_-1*get_CCI(pn_Rank(df['kama']),ts_Mean(df['p6_tn13'],49),ts_CovChg(ts_Partial_corr(df['dm'],df['p6_tn6'],df['cci'],21),ts_Mean(df['p2_et6'],13),13),10)",16.0519,0.0045,1.4639,3.5629,0.5656243411544654,2024-01-01 20:59:00+00:00,2025-01-30 18:59:00+00:00,0.742,0.842,0.4684,-0.211
893,"-1*get_CCI(ts_Stdev2(ts_Rank(df['p2_et16'],19),39),pn_GroupRank(pn_Winsor(df['p5_to0'],10),Or(df['p2_et2'],df['dcperiod'])),pn_GroupRank(pn_Winsor(df['p5_to0'],10),IfThen(df['p5_to0'],18,37)),11)","921_-1*get_CCI(ts_Stdev2(ts_Rank(df['p2_et16'],19),39),pn_GroupRank(pn_Winsor(df['p5_to0'],10),Or(df['p2_et2'],df['dcperiod'])),pn_GroupRank(pn_Winsor(df['p5_to0'],10),IfThen(df['p5_to0'],18,37)),11)",13.4455,0.0054,1.2119,3.1638,0.5812007270300132,2024-01-01 20:59:00+00:00,2025-01-30 18:59:00+00:00,0.545,0.767,0.4154,-0.054
894,"-1*get_CCI(pn_Rank(df['kama']),ts_Partial_corr(df['p4_ms6'],get_CMO(df['dcperiod'],33),df['p6_tn2'],33),pn_Winsor(df['p5_to0'],10),10)","919_-1*get_CCI(pn_Rank(df['kama']),ts_Partial_corr(df['p4_ms6'],get_CMO(df['dcperiod'],33),df['p6_tn2'],33),pn_Winsor(df['p5_to0'],10),10)",21.906,0.0062,2.0351,3.7369,0.5812235912289865,2024-01-01 20:59:00+00:00,2025-01-30 18:59:00+00:00,1.174,1.029,0.5329,-0.12
895,"-1*get_CCI(ts_Stdev2(ts_Rank(df['p2_et16'],19),39),pn_GroupRank(pn_Winsor(df['p5_to0'],10),get_CMO(df['p3_mf11'],43)),pn_GroupRank(pn_Winsor(df['p5_to0'],10),ts_Delay(df['p3_mf9'],37)),11)","920_-1*get_CCI(ts_Stdev2(ts_Rank(df['p2_et16'],19),39),pn_GroupRank(pn_Winsor(df['p5_to0'],10),get_CMO(df['p3_mf11'],43)),pn_GroupRank(pn_Winsor(df['p5_to0'],10),ts_Delay(df['p3_mf9'],37)),11)",12.9272,0.0052,1.1591,3.2352,0.5058971281226672,2024-01-01 20:59:00+00:00,2025-01-30 18:59:00+00:00,0.69,0.564,0.5502,-0.029
896,"-1*get_CCI(pn_Rank(df['kama']),pn_GroupRank(pn_Winsor(df['p5_to0'],10),get_MINUS_DI(df['p6_tn6'],df['p6_tn10'],df['dm'],11)),ts_Cov(Power(df['dm'],11),Sign(df['dcperiod']),11),21)","920_-1*get_CCI(pn_Rank(df['kama']),pn_GroupRank(pn_Winsor(df['p5_to0'],10),get_MINUS_DI(df['p6_tn6'],df['p6_tn10'],df['dm'],11)),ts_Cov(Power(df['dm'],11),Sign(df['dcperiod']),11),21)",16.4841,0.0062,1.4901,3.8245,0.441112999023596,2024-01-01 20:59:00+00:00,2025-01-30 18:59:00+00:00,0.622,0.99,0.3859,-0.093
897,"-1*get_CCI(ts_Stdev2(IfThen(df['p2_et3'],18,18),39),pn_GroupRank(pn_Winsor(df['cci'],10),pn_FillMax(df['p1_corrs3'])),pn_GroupRank(ts_StdevChg(df['p1_corrs2'],31),pn_Winsor(df['p5_to0'],18)),11)","920_-1*get_CCI(ts_Stdev2(IfThen(df['p2_et3'],18,18),39),pn_GroupRank(pn_Winsor(df['cci'],10),pn_FillMax(df['p1_corrs3'])),pn_GroupRank(ts_StdevChg(df['p1_corrs2'],31),pn_Winsor(df['p5_to0'],18)),11)",16.3527,0.0051,1.4974,3.369,0.5581053477994401,2024-01-01 20:59:00+00:00,2025-01-30 18:59:00+00:00,0.788,0.832,0.4864,-0.147
898,"-1*get_CCI(pn_GroupRank(df['p2_et7'],pn_Winsor(df['p5_to0'],10)),pn_GroupRank(pn_Winsor(df['p5_to0'],10),get_MINUS_DI(df['p1_corrs3'],df['p6_tn10'],df['dm'],11)),pn_GroupRank(ts_Sum(df['p6_tn2'],31),get_MINUS_DI(df['p4_ms6'],df['p3_mf6'],df['p2_et13'],37)),11)","921_-1*get_CCI(pn_GroupRank(df['p2_et7'],pn_Winsor(df['p5_to0'],10)),pn_GroupRank(pn_Winsor(df['p5_to0'],10),get_MINUS_DI(df['p1_corrs3'],df['p6_tn10'],df['dm'],11)),pn_GroupRank(ts_Sum(df['p6_tn2'],31),get_MINUS_DI(df['p4_ms6'],df['p3_mf6'],df['p2_et13'],37)),11)",12.787,0.0082,1.1358,3.0582,0.5383272744451746,2024-01-01 20:59:00+00:00,2025-01-30 18:59:00+00:00,0.672,0.557,0.5468,-0.367
899,"-1*pn_GroupNeutral(df['p5_to0'],ts_Cov(pn_Cut(df['p2_et1']),pn_Cut(df['p2_et1']),21))","922_-1*pn_GroupNeutral(df['p5_to0'],ts_Cov(pn_Cut(df['p2_et1']),pn_Cut(df['p2_et1']),21))",11.3009,0.0049,0.9908,3.44,0.5841183218982258,2024-01-01 20:59:00+00:00,2025-01-30 18:59:00+00:00,0.456,0.616,0.4254,-0.11
900,"get_CCI(ts_Stdev2(get_HT_DCPHASE(df['dcphase']),39),pn_GroupRank(pn_Winsor(df['p5_to0'],22),pn_FillMax(df['p6_tn10'])),pn_GroupRank(Sign(df['p2_et13']),get_MINUS_DI(df['p2_et12'],df['p6_tn10'],df['dm'],11)),11)","923_get_CCI(ts_Stdev2(get_HT_DCPHASE(df['dcphase']),39),pn_GroupRank(pn_Winsor(df['p5_to0'],22),pn_FillMax(df['p6_tn10'])),pn_GroupRank(Sign(df['p2_et13']),get_MINUS_DI(df['p2_et12'],df['p6_tn10'],df['dm'],11)),11)",10.839,0.0041,0.9612,3.0688,0.2978861778599554,2024-01-01 20:59:00+00:00,2025-01-30 18:59:00+00:00,0.318,0.722,0.3058,-0.09
901,"get_CCI(Softsign(df['p4_ms2']),pn_GroupRank(pn_Winsor(df['p5_to0'],37),get_MINUS_DI(df['p1_corrs3'],df['p6_tn10'],df['p2_et12'],11)),ts_CovChg(ts_Partial_corr(df['dcphase'],df['p5_to1'],df['cci'],43),ts_CorrChg(df['p2_et1'],df['p2_et10'],6),13),10)","924_get_CCI(Softsign(df['p4_ms2']),pn_GroupRank(pn_Winsor(df['p5_to0'],37),get_MINUS_DI(df['p1_corrs3'],df['p6_tn10'],df['p2_et12'],11)),ts_CovChg(ts_Partial_corr(df['dcphase'],df['p5_to1'],df['cci'],43),ts_CorrChg(df['p2_et1'],df['p2_et10'],6),13),10)",15.3665,0.0009,1.4276,3.134,0.36133049141756307,2024-01-01 20:59:00+00:00,2025-01-30 18:59:00+00:00,0.737,0.808,0.477,-0.033
902,"-1*get_CCI(ts_Stdev2(get_KAMA(df['p2_et15'],22),39),pn_Rank(Sign(df['p2_et13'])),ts_MeanChg(Abs(df['p3_mf2']),26),11)","925_-1*get_CCI(ts_Stdev2(get_KAMA(df['p2_et15'],22),39),pn_Rank(Sign(df['p2_et13'])),ts_MeanChg(Abs(df['p3_mf2']),26),11)",10.1043,0.0031,0.8853,3.289,0.25170646642477723,2024-01-01 20:59:00+00:00,2025-01-30 18:59:00+00:00,0.33,0.629,0.3441,0.079
903,"-1*get_CCI(get_HT_DCPERIOD(df['p3_mf7']),pn_GroupRank(pn_Winsor(df['p5_to0'],10),pn_FillMax(df['p6_tn10'])),pn_GroupRank(get_LINEARREG_ANGLE(df['p2_et16'],37),pn_Winsor(df['p5_to0'],22)),21)","926_-1*get_CCI(get_HT_DCPERIOD(df['p3_mf7']),pn_GroupRank(pn_Winsor(df['p5_to0'],10),pn_FillMax(df['p6_tn10'])),pn_GroupRank(get_LINEARREG_ANGLE(df['p2_et16'],37),pn_Winsor(df['p5_to0'],22)),21)",11.6471,0.0035,1.0459,3.0368,0.40300626961325464,2024-01-01 20:59:00+00:00,2025-01-30 18:59:00+00:00,0.31,0.822,0.2739,-0.016
904,"-1*pn_GroupRank(get_LINEARREG_ANGLE(get_CMO(df['p2_et9'],15),6),ts_Product(df['p2_et3'],46))","928_-1*pn_GroupRank(get_LINEARREG_ANGLE(get_CMO(df['p2_et9'],15),6),ts_Product(df['p2_et3'],46))",9.7498,0.0042,0.8518,3.0716,0.5468897982864946,2024-01-01 20:59:00+00:00,2025-01-30 18:59:00+00:00,0.48,0.442,0.5206,-0.188
905,"get_LINEARREG_ANGLE(get_KAMA(df['p6_tn6'],7),6)","928_get_LINEARREG_ANGLE(get_KAMA(df['p6_tn6'],7),6)",17.4896,0.0066,1.592,3.7185,0.589340258304309,2024-01-01 20:59:00+00:00,2025-01-30 18:59:00+00:00,0.945,0.778,0.5485,-0.364
906,"-1*ts_Divide(ts_Divide(df['p6_tn5'],3),31)","928_-1*ts_Divide(ts_Divide(df['p6_tn5'],3),31)",14.0742,0.0056,1.2368,4.278,0.4276340206360588,2024-01-01 20:59:00+00:00,2025-01-30 18:59:00+00:00,0.673,0.665,0.503,-0.154
907,"-1*pn_CrossFit(pn_GroupRank(pn_TransNorm(df['cci']),ts_Stdev2(df['p2_et1'],1)),get_CCI(df['p2_et14'],Equal(df['p2_et0'],df['p5_to4']),df['p2_et17'],26))","930_-1*pn_CrossFit(pn_GroupRank(pn_TransNorm(df['cci']),ts_Stdev2(df['p2_et1'],1)),get_CCI(df['p2_et14'],Equal(df['p2_et0'],df['p5_to4']),df['p2_et17'],26))",9.7946,0.0032,0.8559,3.231,0.4805546012391676,2024-01-01 20:59:00+00:00,2025-01-30 18:59:00+00:00,0.247,0.679,0.2667,-0.056
908,"-1*pn_CrossFit(pn_GroupRank(FilterInf(df['p6_tn11']),FilterInf(df['dcphase'])),ts_Corr2(pn_CrossFit(df['p2_et18'],df['cci']),df['p2_et14'],49))","931_-1*pn_CrossFit(pn_GroupRank(FilterInf(df['p6_tn11']),FilterInf(df['dcphase'])),ts_Corr2(pn_CrossFit(df['p2_et18'],df['cci']),df['p2_et14'],49))",9.8867,0.003,0.8548,3.5687,0.5071445997136954,2024-01-01 20:59:00+00:00,2025-01-30 18:59:00+00:00,0.434,0.491,0.4692,-0.056
909,"-1*pn_CrossFit(ts_Max(ts_Median(df['dcperiod'],19),19),pn_Winsor(ts_Delta(df['ultosc'],45),33))","931_-1*pn_CrossFit(ts_Max(ts_Median(df['dcperiod'],19),19),pn_Winsor(ts_Delta(df['ultosc'],45),33))",10.7195,0.0055,0.9369,3.228,0.558987416693039,2024-01-01 20:59:00+00:00,2025-01-30 18:59:00+00:00,0.447,0.567,0.4408,-0.213
910,"pn_CrossFit(ts_Max(get_CMO(df['p6_tn7'],19),4),ts_Decay(df['p5_to5'],19))","932_pn_CrossFit(ts_Max(get_CMO(df['p6_tn7'],19),4),ts_Decay(df['p5_to5'],19))",11.6243,0.0026,1.0469,3.0776,0.4862906271683182,2024-01-01 20:59:00+00:00,2025-01-30 18:59:00+00:00,0.564,0.569,0.4978,-0.11
911,"-1*pn_CrossFit(FilterInf(df['p1_corrs1']),get_CCI(df['cci'],get_HT_DCPHASE(df['p2_et13']),IfThen(df['p4_ms3'],19,16),47))","932_-1*pn_CrossFit(FilterInf(df['p1_corrs1']),get_CCI(df['cci'],get_HT_DCPHASE(df['p2_et13']),IfThen(df['p4_ms3'],19,16),47))",15.7038,0.0052,1.4256,3.5664,0.5135874583682627,2024-01-01 20:59:00+00:00,2025-01-30 18:59:00+00:00,0.521,1.022,0.3377,-0.207
912,"-1*pn_GroupRank(ts_Scale(df['p4_ms1'],19),pn_Rank(Sqrt(df['p3_mf6'])))","932_-1*pn_GroupRank(ts_Scale(df['p4_ms1'],19),pn_Rank(Sqrt(df['p3_mf6'])))",11.8309,0.004,1.0425,3.621,0.5494259588375753,2024-01-01 20:59:00+00:00,2025-01-30 18:59:00+00:00,0.602,0.526,0.5337,-0.068
913,"-1*pn_CrossFit(Multiply(ts_Product(df['p2_et17'],47),pn_FillMax(df['p5_to1'])),ts_Cov(Min(df['dcperiod'],df['p4_ms5']),df['p2_et14'],19))","933_-1*pn_CrossFit(Multiply(ts_Product(df['p2_et17'],47),pn_FillMax(df['p5_to1'])),ts_Cov(Min(df['dcperiod'],df['p4_ms5']),df['p2_et14'],19))",10.458,0.0027,0.9301,3.0723,0.31258047475640965,2024-01-01 20:59:00+00:00,2025-01-30 18:59:00+00:00,0.261,0.746,0.2592,-0.057
914,"pn_CrossFit(ts_Delay(df['p2_et6'],21),FilterInf(ts_Argmax(df['p2_et7'],8)))","934_pn_CrossFit(ts_Delay(df['p2_et6'],21),FilterInf(ts_Argmax(df['p2_et7'],8)))",12.8747,0.0056,1.1407,3.569,0.5411647181999479,2024-01-01 20:59:00+00:00,2025-01-30 18:59:00+00:00,0.407,0.828,0.3296,-0.31
915,"-1*pn_CrossFit(ts_Max(df['p2_et18'],49),get_CCI(pn_Stand(df['p3_mf11']),ts_Divide(df['p5_to3'],4),ts_StdevChg(df['p1_corrs5'],6),47))","934_-1*pn_CrossFit(ts_Max(df['p2_et18'],49),get_CCI(pn_Stand(df['p3_mf11']),ts_Divide(df['p5_to3'],4),ts_StdevChg(df['p1_corrs5'],6),47))",12.3374,0.0055,1.0891,3.5144,0.5908564318298637,2024-01-01 20:59:00+00:00,2025-01-30 18:59:00+00:00,0.456,0.722,0.3871,-0.211
916,"pn_CrossFit(get_CCI(pn_Stand(df['p3_mf1']),ts_Divide(df['p5_to3'],4),ts_Decay(df['p1_corrs8'],31),47),Max(pn_RankCentered(df['p6_tn6']),0.555))","935_pn_CrossFit(get_CCI(pn_Stand(df['p3_mf1']),ts_Divide(df['p5_to3'],4),ts_Decay(df['p1_corrs8'],31),47),Max(pn_RankCentered(df['p6_tn6']),0.555))",9.9252,0.0037,0.8728,3.037,0.5225453622870324,2024-01-01 20:59:00+00:00,2025-01-30 18:59:00+00:00,0.437,0.508,0.4624,-0.263
917,"-1*pn_CrossFit(get_CCI(ts_StdevChg(df['p6_tn7'],8),ts_Divide(df['p2_et15'],4),ts_Decay(df['p1_corrs8'],31),19),ts_Median(ts_TransNorm(df['dcphase'],35),6))","936_-1*pn_CrossFit(get_CCI(ts_StdevChg(df['p6_tn7'],8),ts_Divide(df['p2_et15'],4),ts_Decay(df['p1_corrs8'],31),19),ts_Median(ts_TransNorm(df['dcphase'],35),6))",10.6386,0.0046,0.9365,3.1337,0.5498951193497104,2024-01-01 20:59:00+00:00,2025-01-30 18:59:00+00:00,0.297,0.716,0.2932,-0.23
918,"-1*pn_CrossFit(get_MINUS_DM(pn_GroupNorm(df['p2_et15'],df['p2_et15']),Sign(df['cci']),50),ts_Delta(pn_Stand(df['p3_mf11']),19))","937_-1*pn_CrossFit(get_MINUS_DM(pn_GroupNorm(df['p2_et15'],df['p2_et15']),Sign(df['cci']),50),ts_Delta(pn_Stand(df['p3_mf11']),19))",19.7615,0.0083,1.7574,5.3224,0.5737823104746509,2024-01-01 20:59:00+00:00,2025-01-30 18:59:00+00:00,0.922,0.98,0.4848,-0.194
919,"-1*get_CCI(df['cci'],Power(get_CMO(df['p2_et1'],15),25),ts_Sum(inv(df['p6_tn8']),41),14)","938_-1*get_CCI(df['cci'],Power(get_CMO(df['p2_et1'],15),25),ts_Sum(inv(df['p6_tn8']),41),14)",10.2511,0.0033,0.9039,3.1352,0.38134037654735436,2024-01-01 20:59:00+00:00,2025-01-30 18:59:00+00:00,0.392,0.586,0.4008,-0.006
920,"-1*ts_Max(df['p2_et1'],2)","938_-1*ts_Max(df['p2_et1'],2)",15.4762,0.0068,1.4024,3.3348,0.5622887120176088,2024-01-01 20:59:00+00:00,2025-01-30 18:59:00+00:00,0.698,0.82,0.4598,-0.274
921,"get_CCI(pn_RankCentered(ts_CorrChg(df['p4_ms4'],df['p4_ms6'],36)),ts_Argmax(pn_Rank(df['p2_et7']),10),Power(df['p2_et1'],33),39)","939_get_CCI(pn_RankCentered(ts_CorrChg(df['p4_ms4'],df['p4_ms6'],36)),ts_Argmax(pn_Rank(df['p2_et7']),10),Power(df['p2_et1'],33),39)",12.2477,0.0031,1.1078,3.046,0.44563019566375994,2024-01-01 20:59:00+00:00,2025-01-30 18:59:00+00:00,0.431,0.768,0.3595,-0.248
922,"-1*get_CCI(Power(pn_GroupNeutral(df['liangle'],df['p2_et13']),25),SignedPower(df['adosc'],df['p6_tn10']),df['cci'],14)","940_-1*get_CCI(Power(pn_GroupNeutral(df['liangle'],df['p2_et13']),25),SignedPower(df['adosc'],df['p6_tn10']),df['cci'],14)",9.8262,0.0041,0.8615,3.0153,0.32370252456949283,2024-01-01 20:59:00+00:00,2025-01-30 18:59:00+00:00,0.374,0.558,0.4013,-0.161
923,"-1*get_CCI(ts_Corr(df['p3_mf5'],ts_Argmin(df['dcperiod'],45),26),df['p5_to0'],MEthan(pn_Rank(df['adosc']),pn_Stand(df['cci'])),39)","941_-1*get_CCI(ts_Corr(df['p3_mf5'],ts_Argmin(df['dcperiod'],45),26),df['p5_to0'],MEthan(pn_Rank(df['adosc']),pn_Stand(df['cci'])),39)",17.0277,0.0073,1.5547,3.3491,0.5788025043348527,2024-01-01 20:59:00+00:00,2025-01-30 18:59:00+00:00,0.622,1.06,0.3698,-0.182
924,"-1*get_CCI(pn_Stand(df['p2_et16']),df['p5_to0'],ts_Divide(Xor(df['p6_tn13'],df['p4_ms4']),36),39)","939_-1*get_CCI(pn_Stand(df['p2_et16']),df['p5_to0'],ts_Divide(Xor(df['p6_tn13'],df['p4_ms4']),36),39)",25.8285,0.0077,2.4116,3.9838,0.5995686655524579,2024-01-01 20:59:00+00:00,2025-01-30 18:59:00+00:00,0.63,1.98,0.2414,-0.165
925,"-1*get_CCI(pn_GroupNorm(df['p2_et1'],get_CMO(df['p5_to1'],44)),ts_Rank(pn_GroupNorm(df['cmo'],df['p2_et9']),5),ts_Min(ts_TransNorm(df['cmo'],7),43),39)","940_-1*get_CCI(pn_GroupNorm(df['p2_et1'],get_CMO(df['p5_to1'],44)),ts_Rank(pn_GroupNorm(df['cmo'],df['p2_et9']),5),ts_Min(ts_TransNorm(df['cmo'],7),43),39)",16.4559,0.0061,1.4837,3.9356,0.5557953810619038,2024-01-01 20:59:00+00:00,2025-01-30 18:59:00+00:00,0.756,0.849,0.471,-0.22
926,"-1*get_CCI(ts_ChgRate(ts_ChgRate(df['p2_et11'],7),2),pn_Rank(df['dm']),ts_TransNorm(pn_Stand(df['p2_et7']),43),14)","941_-1*get_CCI(ts_ChgRate(ts_ChgRate(df['p2_et11'],7),2),pn_Rank(df['dm']),ts_TransNorm(pn_Stand(df['p2_et7']),43),14)",18.1218,0.0052,1.6596,3.8011,0.5560348192978508,2024-01-01 20:59:00+00:00,2025-01-30 18:59:00+00:00,0.91,0.886,0.5067,-0.427
927,"-1*get_CCI(ts_Min(df['p2_et0'],39),inv(df['dm']),df['p3_mf4'],8)","941_-1*get_CCI(ts_Min(df['p2_et0'],39),inv(df['dm']),df['p3_mf4'],8)",13.7295,0.006,1.2011,4.253,0.5379941817192748,2024-01-01 20:59:00+00:00,2025-01-30 18:59:00+00:00,0.671,0.629,0.5162,-0.085
928,"-1*get_CCI(ts_Min(df['p3_mf4'],43),df['p5_to6'],df['p2_et0'],14)","942_-1*get_CCI(ts_Min(df['p3_mf4'],43),df['p5_to6'],df['p2_et0'],14)",10.9629,0.0044,0.9714,3.0828,0.5373674274866066,2024-01-01 20:59:00+00:00,2025-01-30 18:59:00+00:00,0.521,0.531,0.4952,-0.136
929,"-1*get_CCI(df['p3_mf4'],ts_Mean(df['p3_mf7'],12),df['p2_et0'],22)","943_-1*get_CCI(df['p3_mf4'],ts_Mean(df['p3_mf7'],12),df['p2_et0'],22)",13.6849,0.0057,1.222,3.5415,0.5336263978339687,2024-01-01 20:59:00+00:00,2025-01-30 18:59:00+00:00,0.426,0.896,0.3222,-0.293
930,"get_CCI(FilterInf(df['di']),inv(df['p6_tn4']),ts_Divide(pn_GroupRank(df['cci'],df['p2_et4']),40),14)","944_get_CCI(FilterInf(df['di']),inv(df['p6_tn4']),ts_Divide(pn_GroupRank(df['cci'],df['p2_et4']),40),14)",9.4445,0.0047,0.8202,3.0286,0.49478282380250077,2024-01-01 20:59:00+00:00,2025-01-30 18:59:00+00:00,0.238,0.649,0.2683,-0.254
931,"-1*get_CCI(pn_RankCentered(df['cci']),df['p3_mf4'],MEthan(ts_Min(df['p2_et0'],18),df['cci']),14)","945_-1*get_CCI(pn_RankCentered(df['cci']),df['p3_mf4'],MEthan(ts_Min(df['p2_et0'],18),df['cci']),14)",15.3957,0.0058,1.3688,4.2618,0.5357749345937942,2024-01-01 20:59:00+00:00,2025-01-30 18:59:00+00:00,0.558,0.923,0.3768,-0.224
932,"-1*get_CCI(ts_Min(ts_Min(df['p2_et14'],39),43),df['p3_mf4'],ts_ChgRate(df['p2_et4'],46),22)","946_-1*get_CCI(ts_Min(ts_Min(df['p2_et14'],39),43),df['p3_mf4'],ts_ChgRate(df['p2_et4'],46),22)",9.474,0.0039,0.806,3.6644,0.18078191992654133,2024-01-01 20:59:00+00:00,2025-01-30 18:59:00+00:00,0.365,0.507,0.4186,-0.041
933,"-1*get_CCI(ts_Delta(Not(df['dm']),11),df['p5_to0'],pn_TransNorm(df['p2_et7']),14)","947_-1*get_CCI(ts_Delta(Not(df['dm']),11),df['p5_to0'],pn_TransNorm(df['p2_et7']),14)",19.9089,0.008,1.8396,3.3455,0.5405886915339881,2024-01-01 20:59:00+00:00,2025-01-30 18:59:00+00:00,0.414,1.577,0.2079,-0.137
934,"-1*get_CCI(ts_Argmax(ts_Min(df['adosc'],12),27),FilterInf(FilterInf(df['p6_tn13'])),ts_Mean(ts_Delta(df['p5_to0'],11),12),8)","948_-1*get_CCI(ts_Argmax(ts_Min(df['adosc'],12),27),FilterInf(FilterInf(df['p6_tn13'])),ts_Mean(ts_Delta(df['p5_to0'],11),12),8)",10.8381,0.0048,0.954,3.1745,0.5363551690780055,2024-01-01 20:59:00+00:00,2025-01-30 18:59:00+00:00,0.289,0.743,0.28,-0.133
935,"-1*get_CCI(ts_Delta(ts_Argmax(df['cci'],2),11),ts_StdevChg(df['p4_ms1'],3),pn_TransNorm(ts_Scale(df['p5_to0'],42)),39)","949_-1*get_CCI(ts_Delta(ts_Argmax(df['cci'],2),11),ts_StdevChg(df['p4_ms1'],3),pn_TransNorm(ts_Scale(df['p5_to0'],42)),39)",11.5054,0.0045,1.0011,3.8055,0.5285653252185036,2024-01-01 20:59:00+00:00,2025-01-30 18:59:00+00:00,0.418,0.665,0.386,-0.103
936,"-1*get_CCI(ts_Min(df['p5_to0'],4),FilterInf(pn_CrossFit(df['p3_mf0'],df['p5_to0'])),ts_Mean(pn_Stand(df['p3_mf0']),12),8)","950_-1*get_CCI(ts_Min(df['p5_to0'],4),FilterInf(pn_CrossFit(df['p3_mf0'],df['p5_to0'])),ts_Mean(pn_Stand(df['p3_mf0']),12),8)",11.81,0.0049,1.0524,3.1186,0.5017983871277366,2024-01-01 20:59:00+00:00,2025-01-30 18:59:00+00:00,0.468,0.67,0.4112,-0.082
937,"-1*get_CCI(df['p5_to0'],df['p5_to0'],ts_Cov2(ts_Argmax(df['p3_mf9'],39),df['p4_ms6'],40),39)","951_-1*get_CCI(df['p5_to0'],df['p5_to0'],ts_Cov2(ts_Argmax(df['p3_mf9'],39),df['p4_ms6'],40),39)",11.9572,0.0047,1.0605,3.3519,0.5680279999565645,2024-01-01 20:59:00+00:00,2025-01-30 18:59:00+00:00,0.642,0.506,0.5592,-0.107
938,"get_CCI(ts_Argmin(df['p6_tn13'],3),df['di'],ts_CovChg(get_HT_DCPHASE(df['p2_et8']),ts_Rank(df['p2_et7'],50),40),14)","952_get_CCI(ts_Argmin(df['p6_tn13'],3),df['di'],ts_CovChg(get_HT_DCPHASE(df['p2_et8']),ts_Rank(df['p2_et7'],50),40),14)",10.6549,0.0042,0.9323,3.3716,0.4827515442556229,2024-01-01 20:59:00+00:00,2025-01-30 18:59:00+00:00,0.354,0.655,0.3508,-0.209
939,"-1*get_CCI(ts_Mean(df['ultosc'],7),df['p6_tn2'],ts_Max(Power(df['p2_et14'],13),29),11)","953_-1*get_CCI(ts_Mean(df['ultosc'],7),df['p6_tn2'],ts_Max(Power(df['p2_et14'],13),29),11)",13.9065,0.0042,1.2529,3.5068,0.5158275355822379,2024-01-01 20:59:00+00:00,2025-01-30 18:59:00+00:00,0.57,0.786,0.4204,-0.27
940,"get_CCI(Max(ts_Max(df['p2_et5'],33),Log(df['p5_to6'])),ts_Argmin(ts_CorrChg(df['p3_mf4'],df['p6_tn13'],15),23),df['di'],15)","954_get_CCI(Max(ts_Max(df['p2_et5'],33),Log(df['p5_to6'])),ts_Argmin(ts_CorrChg(df['p3_mf4'],df['p6_tn13'],15),23),df['di'],15)",12.7394,0.0035,1.1492,3.2104,0.39709945988446255,2024-01-01 20:59:00+00:00,2025-01-30 18:59:00+00:00,0.344,0.899,0.2767,-0.148
941,"-1*get_CCI(df['cci'],ts_Argmin(df['p2_et3'],23),get_LINEARREG_SLOPE(pn_FillMax(df['p2_et15']),14),14)","955_-1*get_CCI(df['cci'],ts_Argmin(df['p2_et3'],23),get_LINEARREG_SLOPE(pn_FillMax(df['p2_et15']),14),14)",21.5347,0.0061,2.0207,3.0653,0.47226955219273514,2024-01-01 20:59:00+00:00,2025-01-30 18:59:00+00:00,0.932,1.255,0.4262,-0.271
942,"-1*get_CCI(df['p2_et7'],pn_GroupNorm(df['p3_mf4'],df['p2_et0']),df['cci'],14)","956_-1*get_CCI(df['p2_et7'],pn_GroupNorm(df['p3_mf4'],df['p2_et0']),df['cci'],14)",22.8426,0.0065,2.138,3.4164,0.5714102487085105,2024-01-01 20:59:00+00:00,2025-01-30 18:59:00+00:00,0.838,1.476,0.3621,-0.282
943,"-1*get_CCI(Min(ts_Decay(df['p2_et14'],18),df['p2_et14']),Abs(df['p3_mf4']),ts_Max(get_LINEARREG_ANGLE(df['p2_et10'],28),49),8)","956_-1*get_CCI(Min(ts_Decay(df['p2_et14'],18),df['p2_et14']),Abs(df['p3_mf4']),ts_Max(get_LINEARREG_ANGLE(df['p2_et10'],28),49),8)",13.0146,0.0041,1.1409,4.2077,0.49835296963749337,2024-01-01 20:59:00+00:00,2025-01-30 18:59:00+00:00,0.57,0.665,0.4615,-0.184
944,"-1*get_CCI(MEthan(df['p2_et7'],df['di']),ts_Rank(df['p2_et7'],50),ts_Rank(df['p6_tn13'],50),48)","955_-1*get_CCI(MEthan(df['p2_et7'],df['di']),ts_Rank(df['p2_et7'],50),ts_Rank(df['p6_tn13'],50),48)",16.9635,0.0076,1.5231,4.052,0.5871361057757843,2024-01-01 20:59:00+00:00,2025-01-30 18:59:00+00:00,0.777,0.871,0.4715,-0.492
945,"-1*Min(pn_GroupNorm(df['p2_et7'],df['p3_mf7']),ts_MeanChg(df['p6_tn1'],34))","956_-1*Min(pn_GroupNorm(df['p2_et7'],df['p3_mf7']),ts_MeanChg(df['p6_tn1'],34))",10.2997,0.0044,0.8962,3.3524,0.5554942175669022,2024-01-01 20:59:00+00:00,2025-01-30 18:59:00+00:00,0.833,0.137,0.8588,-0.483
946,"-1*Multiply(get_MINUS_DM(get_LINEARREG_ANGLE(df['p4_ms5'],48),Reverse(df['p6_tn9']),12),ts_Regression(df['p3_mf11'],df['p4_ms3'],9,'C'))","958_-1*Multiply(get_MINUS_DM(get_LINEARREG_ANGLE(df['p4_ms5'],48),Reverse(df['p6_tn9']),12),ts_Regression(df['p3_mf11'],df['p4_ms3'],9,'C'))",14.3019,0.0068,1.2879,3.2422,0.589975448239714,2024-01-01 20:59:00+00:00,2025-01-30 18:59:00+00:00,0.52,0.874,0.373,-0.291
947,"Multiply(ts_Corr2(df['p2_et0'],df['di'],5),df['p4_ms1'])","959_Multiply(ts_Corr2(df['p2_et0'],df['di'],5),df['p4_ms1'])",11.6252,0.0055,1.0311,3.1183,0.47699587034938606,2024-01-01 20:59:00+00:00,2025-01-30 18:59:00+00:00,0.356,0.76,0.319,-0.099
948,"-1*Min(ts_Sum(get_KAMA(df['p4_ms5'],34),12),pn_GroupNorm(df['p2_et7'],ts_Delay(df['p6_tn4'],46)))","959_-1*Min(ts_Sum(get_KAMA(df['p4_ms5'],34),12),pn_GroupNorm(df['p2_et7'],ts_Delay(df['p6_tn4'],46)))",18.3237,0.0078,1.6693,3.7193,0.5937453021310075,2024-01-01 20:59:00+00:00,2025-01-30 18:59:00+00:00,1.012,0.795,0.56,-0.603
949,"-1*Min(pn_GroupNorm(pn_Winsor(df['p4_ms1'],47),pn_GroupNeutral(df['p4_ms2'],df['p4_ms5'])),FilterInf(df['p3_mf2']))","958_-1*Min(pn_GroupNorm(pn_Winsor(df['p4_ms1'],47),pn_GroupNeutral(df['p4_ms2'],df['p4_ms5'])),FilterInf(df['p3_mf2']))",12.8962,0.0049,1.1421,3.6836,0.5782596248362666,2024-01-01 20:59:00+00:00,2025-01-30 18:59:00+00:00,0.743,0.493,0.6011,-0.137
950,"-1*Multiply(ts_TransNorm(df['p2_et9'],12),get_KAMA(df['p3_mf11'],26))","959_-1*Multiply(ts_TransNorm(df['p2_et9'],12),get_KAMA(df['p3_mf11'],26))",11.0662,0.003,0.9914,3.0134,0.3920903295223245,2024-01-01 20:59:00+00:00,2025-01-30 18:59:00+00:00,0.24,0.833,0.2237,-0.067
951,"Min(pn_Winsor(Reverse(df['p2_et0']),27),get_KAMA(get_LINEARREG_ANGLE(df['p4_ms2'],2),7))","960_Min(pn_Winsor(Reverse(df['p2_et0']),27),get_KAMA(get_LINEARREG_ANGLE(df['p4_ms2'],2),7))",12.0457,0.004,1.064,3.613,0.47430888811287053,2024-01-01 20:59:00+00:00,2025-01-30 18:59:00+00:00,0.879,0.273,0.763,-0.087
952,"-1*Min(ts_Corr(df['p2_et8'],df['p3_mf4'],46),pn_TransNorm(SignedPower(df['p2_et9'],30)))","960_-1*Min(ts_Corr(df['p2_et8'],df['p3_mf4'],46),pn_TransNorm(SignedPower(df['p2_et9'],30)))",15.6863,0.0065,1.394,4.2682,0.5947996093870233,2024-01-01 20:59:00+00:00,2025-01-30 18:59:00+00:00,0.513,0.996,0.34,-0.271
953,"-1*Min(ts_Cov(df['p2_et3'],ts_Scale(df['p6_tn12'],5),6),df['p4_ms5'])","961_-1*Min(ts_Cov(df['p2_et3'],ts_Scale(df['p6_tn12'],5),6),df['p4_ms5'])",15.4929,0.0061,1.3864,3.9685,0.5582232153738381,2024-01-01 20:59:00+00:00,2025-01-30 18:59:00+00:00,0.494,1.006,0.3293,-0.226
954,"-1*Min(pn_CrossFit(ts_Stdev2(df['p4_ms1'],3),Min(df['p4_ms5'],0.88)),ts_Product(df['di'],3))","962_-1*Min(pn_CrossFit(ts_Stdev2(df['p4_ms1'],3),Min(df['p4_ms5'],0.88)),ts_Product(df['di'],3))",16.4183,0.0065,1.5061,3.0985,0.5633079353807697,2024-01-01 20:59:00+00:00,2025-01-30 18:59:00+00:00,0.766,0.863,0.4702,-0.224
955,"-1*Min(get_CCI(df['p2_et9'],df['cmo'],df['dcperiod'],9),get_KAMA(df['p2_et3'],3))","963_-1*Min(get_CCI(df['p2_et9'],df['cmo'],df['dcperiod'],9),get_KAMA(df['p2_et3'],3))",16.4376,0.0071,1.5077,3.0106,0.5765772587352627,2024-01-01 20:59:00+00:00,2025-01-30 18:59:00+00:00,0.427,1.204,0.2618,-0.364
956,"-1*get_CMO(ts_Delta(df['p2_et0'],47),38)","964_-1*get_CMO(ts_Delta(df['p2_et0'],47),38)",10.4691,0.0049,0.9121,3.3087,0.5099555740913574,2024-01-01 20:59:00+00:00,2025-01-30 18:59:00+00:00,0.301,0.686,0.305,-0.127
957,"-1*ts_Rank(ts_Delta(FilterInf(df['p6_tn5']),26),27)","965_-1*ts_Rank(ts_Delta(FilterInf(df['p6_tn5']),26),27)",14.4967,0.0046,1.3252,3.0479,0.5618710105669084,2024-01-01 20:59:00+00:00,2025-01-30 18:59:00+00:00,0.626,0.808,0.4365,-0.157
958,"-1*get_LINEARREG_ANGLE(pn_GroupRank(pn_TransNorm(df['p2_et0']),ts_Skewness(df['di'],25)),10)","966_-1*get_LINEARREG_ANGLE(pn_GroupRank(pn_TransNorm(df['p2_et0']),ts_Skewness(df['di'],25)),10)",9.6387,0.0044,0.8411,3.0229,0.5605416679694684,2024-01-01 20:59:00+00:00,2025-01-30 18:59:00+00:00,0.429,0.481,0.4714,-0.23
959,"-1*ts_Rank(ts_Delta(FilterInf(df['p6_tn5']),50),26)","967_-1*ts_Rank(ts_Delta(FilterInf(df['p6_tn5']),50),26)",14.8609,0.0055,1.3494,3.2691,0.49063375816033067,2024-01-01 20:59:00+00:00,2025-01-30 18:59:00+00:00,0.501,0.959,0.3432,-0.157
960,"-1*pn_GroupRank(ts_Delta(FilterInf(df['p6_tn5']),47),df['p6_tn2'])","967_-1*pn_GroupRank(ts_Delta(FilterInf(df['p6_tn5']),47),df['p6_tn2'])",13.1326,0.0059,1.1562,3.8277,0.5813652107887988,2024-01-01 20:59:00+00:00,2025-01-30 18:59:00+00:00,0.473,0.779,0.3778,-0.178
961,"pn_Stand(ts_Divide(df['p4_ms2'],10))","968_pn_Stand(ts_Divide(df['p4_ms2'],10))",12.4064,0.0059,1.0796,3.9505,0.5680171814913393,2024-01-01 20:59:00+00:00,2025-01-30 18:59:00+00:00,0.452,0.716,0.387,-0.142
962,"-1*pn_GroupRank(ts_Delta(pn_TransNorm(df['p2_et1']),47),MEthan(Add(df['p1_corrs2'],df['p3_mf0']),get_MINUS_DM(df['p6_tn4'],df['p5_to2'],15)))","969_-1*pn_GroupRank(ts_Delta(pn_TransNorm(df['p2_et1']),47),MEthan(Add(df['p1_corrs2'],df['p3_mf0']),get_MINUS_DM(df['p6_tn4'],df['p5_to2'],15)))",10.9058,0.0053,0.9616,3.0721,0.5770698445724797,2024-01-01 20:59:00+00:00,2025-01-30 18:59:00+00:00,0.263,0.777,0.2529,-0.153
963,"-1*pn_GroupRank(get_CMO(df['p6_tn1'],45),pn_TransNorm(df['dcphase']))","970_-1*pn_GroupRank(get_CMO(df['p6_tn1'],45),pn_TransNorm(df['dcphase']))",10.892,0.0064,0.9541,3.0994,0.586647712398837,2024-01-01 20:59:00+00:00,2025-01-30 18:59:00+00:00,0.377,0.655,0.3653,-0.189
964,"-1*pn_GroupRank(pn_Stand(Max(df['p3_mf11'],df['cmo'])),ts_TransNorm(IfThen(df['p2_et7'],37,36),47))","971_-1*pn_GroupRank(pn_Stand(Max(df['p3_mf11'],df['cmo'])),ts_TransNorm(IfThen(df['p2_et7'],37,36),47))",11.3865,0.005,1.0104,3.103,0.5337216488689318,2024-01-01 20:59:00+00:00,2025-01-30 18:59:00+00:00,0.439,0.654,0.4016,-0.232
965,"-1*pn_GroupRank(pn_TransStd(df['p6_tn1']),ts_Divide(df['p3_mf3'],34))","971_-1*pn_GroupRank(pn_TransStd(df['p6_tn1']),ts_Divide(df['p3_mf3'],34))",10.5342,0.0048,0.9056,3.7125,0.5672330351548314,2024-01-01 20:59:00+00:00,2025-01-30 18:59:00+00:00,0.322,0.658,0.3286,-0.156
966,"-1*pn_GroupRank(df['p5_to2'],get_MINUS_DI(df['p3_mf6'],And(df['p2_et17'],df['p2_et1']),ts_StdevChg(df['dcphase'],28),39))","971_-1*pn_GroupRank(df['p5_to2'],get_MINUS_DI(df['p3_mf6'],And(df['p2_et17'],df['p2_et1']),ts_StdevChg(df['dcphase'],28),39))",10.3015,0.0035,0.8983,3.4259,0.4652315574089755,2024-01-01 20:59:00+00:00,2025-01-30 18:59:00+00:00,0.23,0.743,0.2364,-0.095
967,"-1*pn_GroupRank(ts_Median(df['dcphase'],9),pn_Winsor(ts_Mean(df['p1_corrs2'],18),34))","972_-1*pn_GroupRank(ts_Median(df['dcphase'],9),pn_Winsor(ts_Mean(df['p1_corrs2'],18),34))",9.62,0.0032,0.8396,3.1859,0.49508972949036717,2024-01-01 20:59:00+00:00,2025-01-30 18:59:00+00:00,0.386,0.523,0.4246,-0.213
968,"-1*pn_GroupRank(pn_TransStd(get_LINEARREG_ANGLE(df['dcphase'],12)),ts_Sum(ts_Cov2(df['cmo'],df['p3_mf9'],50),50))","972_-1*pn_GroupRank(pn_TransStd(get_LINEARREG_ANGLE(df['dcphase'],12)),ts_Sum(ts_Cov2(df['cmo'],df['p3_mf9'],50),50))",12.1544,0.0059,1.0452,4.2259,0.545757829275506,2024-01-01 20:59:00+00:00,2025-01-30 18:59:00+00:00,0.605,0.526,0.5349,-0.223
969,"-1*pn_GroupRank(Mthan(df['p2_et4'],ts_Mean(df['p2_et6'],14)),ts_Stdev2(ts_Stdev2(df['p6_tn11'],5),42))","973_-1*pn_GroupRank(Mthan(df['p2_et4'],ts_Mean(df['p2_et6'],14)),ts_Stdev2(ts_Stdev2(df['p6_tn11'],5),42))",10.7327,0.0042,0.9402,3.3625,0.37897568615318705,2024-01-01 20:59:00+00:00,2025-01-30 18:59:00+00:00,0.386,0.631,0.3795,-0.101
970,"-1*pn_GroupRank(Minus(df['p6_tn10'],df['p4_ms2']),Mthan(ts_TransNorm(df['p5_to2'],18),ts_Cov(df['adosc'],df['p6_tn11'],34)))","974_-1*pn_GroupRank(Minus(df['p6_tn10'],df['p4_ms2']),Mthan(ts_TransNorm(df['p5_to2'],18),ts_Cov(df['adosc'],df['p6_tn11'],34)))",16.9863,0.007,1.5332,3.9162,0.5640011221618405,2024-01-01 20:59:00+00:00,2025-01-30 18:59:00+00:00,0.763,0.897,0.4596,-0.227
971,"pn_GroupRank(Max(df['p6_tn1'],Reverse(df['cmo'])),pn_TransNorm(UnEqual(df['liangle'],df['p5_to5'])))","975_pn_GroupRank(Max(df['p6_tn1'],Reverse(df['cmo'])),pn_TransNorm(UnEqual(df['liangle'],df['p5_to5'])))",10.8,0.005,0.9521,3.0931,0.5898021133914462,2024-01-01 20:59:00+00:00,2025-01-30 18:59:00+00:00,0.536,0.495,0.5199,-0.422
972,"-1*ts_Regression(df['p1_corrs9'],df['lislope'],20,'C')","976_-1*ts_Regression(df['p1_corrs9'],df['lislope'],20,'C')",11.2668,0.0056,0.9947,3.1176,0.5963158387451643,2024-01-01 20:59:00+00:00,2025-01-30 18:59:00+00:00,0.297,0.779,0.276,-0.439
973,"ts_Delta(ts_Scale(pn_GroupNeutral(df['p5_to4'],df['p5_to0']),42),37)","977_ts_Delta(ts_Scale(pn_GroupNeutral(df['p5_to4'],df['p5_to0']),42),37)",9.9377,0.0026,0.8591,3.6507,0.1367408545392844,2024-01-01 20:59:00+00:00,2025-01-30 18:59:00+00:00,0.461,0.468,0.4962,0.004
974,"-1*pn_GroupNorm(get_CCI(SignedPower(df['p1_corrs1'],0.366),df['p3_mf8'],df['p2_et14'],10),df['p1_corrs7'])","978_-1*pn_GroupNorm(get_CCI(SignedPower(df['p1_corrs1'],0.366),df['p3_mf8'],df['p2_et14'],10),df['p1_corrs7'])",10.804,0.0049,0.9301,3.7705,0.5820371770266145,2024-01-01 20:59:00+00:00,2025-01-30 18:59:00+00:00,0.336,0.67,0.334,-0.206
975,"ts_MeanChg(get_CMO(IfThen(df['p6_tn3'],42,16),28),13)","978_ts_MeanChg(get_CMO(IfThen(df['p6_tn3'],42,16),28),13)",14.1695,0.0056,1.2572,3.9481,0.5176014363893138,2024-01-01 20:59:00+00:00,2025-01-30 18:59:00+00:00,0.804,0.557,0.5907,-0.209
976,"-1*pn_GroupNeutral(ts_Divide(df['p6_tn5'],10),ts_Stdev(df['p2_et9'],20))","979_-1*pn_GroupNeutral(ts_Divide(df['p6_tn5'],10),ts_Stdev(df['p2_et9'],20))",15.4021,0.0073,1.3527,4.5361,0.5464326707771385,2024-01-01 20:59:00+00:00,2025-01-30 18:59:00+00:00,0.709,0.755,0.4843,-0.206
977,"ts_MeanChg(pn_GroupRank(df['p4_ms5'],df['p1_corrs3']),10)","979_ts_MeanChg(pn_GroupRank(df['p4_ms5'],df['p1_corrs3']),10)",17.5022,0.0068,1.5662,4.5004,0.5798274414748064,2024-01-01 20:59:00+00:00,2025-01-30 18:59:00+00:00,0.76,0.935,0.4484,-0.284
978,"-1*pn_CrossFit(ts_Argmin(get_CCI(df['p3_mf1'],df['p3_mf7'],df['p4_ms2'],45),26),df['p6_tn13'])","981_-1*pn_CrossFit(ts_Argmin(get_CCI(df['p3_mf1'],df['p3_mf7'],df['p4_ms2'],45),26),df['p6_tn13'])",14.7631,0.0052,1.3439,3.1973,0.4515465605848071,2024-01-01 20:59:00+00:00,2025-01-30 18:59:00+00:00,0.681,0.773,0.4684,-0.124
979,"-1*pn_GroupNeutral(get_CCI(pn_GroupNeutral(df['p3_mf6'],df['p2_et5']),df['p4_ms2'],df['p2_et14'],10),Sign(df['p4_ms2']))","982_-1*pn_GroupNeutral(get_CCI(pn_GroupNeutral(df['p3_mf6'],df['p2_et5']),df['p4_ms2'],df['p2_et14'],10),Sign(df['p4_ms2']))",9.2568,0.0035,0.8071,3.0398,0.12402539347339452,2024-01-01 20:59:00+00:00,2025-01-30 18:59:00+00:00,0.487,0.386,0.5578,0.041
980,"-1*ts_MeanChg(Add(df['p2_et14'],df['p4_ms2']),13)","981_-1*ts_MeanChg(Add(df['p2_et14'],df['p4_ms2']),13)",15.0654,0.0075,1.3471,3.6602,0.5794329464895511,2024-01-01 20:59:00+00:00,2025-01-30 18:59:00+00:00,0.688,0.769,0.4722,-0.287
