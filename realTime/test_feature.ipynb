{"cells": [{"cell_type": "code", "execution_count": 9, "id": "d20002e9", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["成功打开共享内存，包含 515 个合约 最近更新时间:1747294501203530770\n"]}], "source": ["from read_demo import KlineReader\n", "path = \"/home/<USER>/git/fast_trader_elite_pkg/main/kline_data.shm\"\n", "reader = <PERSON>lineReader(path)\n", "data = reader.read_kline_data()\n", "reader.close()"]}, {"cell_type": "code", "execution_count": 10, "id": "2ac744d9", "metadata": {}, "outputs": [{"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>RAREUSDT</th>\n", "      <th>HOOKUSDT</th>\n", "      <th>HPOS10IUSDT</th>\n", "      <th>SWEATUSDT</th>\n", "      <th>TAOUSDT</th>\n", "      <th>PENGUUSDT</th>\n", "      <th>SLFUSDT</th>\n", "      <th>BTCUSDT</th>\n", "      <th>PIPPINUSDT</th>\n", "      <th>SXTUSDT</th>\n", "      <th>...</th>\n", "      <th>HIGHUSDT</th>\n", "      <th>SDUSDT</th>\n", "      <th>GOATUSDT</th>\n", "      <th>MILKUSDT</th>\n", "      <th>SAFEUSDT</th>\n", "      <th>JSTUSDT</th>\n", "      <th>SSVUSDT</th>\n", "      <th>HAEDALUSDT</th>\n", "      <th>GLMRUSDT</th>\n", "      <th>ENSUSDT</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>1747003140000</th>\n", "      <td>0.07378</td>\n", "      <td>0.1623</td>\n", "      <td>0.08659</td>\n", "      <td>0.00478</td>\n", "      <td>450.25</td>\n", "      <td>0.014092</td>\n", "      <td>0.2129</td>\n", "      <td>103750.1</td>\n", "      <td>0.02839</td>\n", "      <td>0.1426</td>\n", "      <td>...</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1747003200000</th>\n", "      <td>0.07383</td>\n", "      <td>0.1625</td>\n", "      <td>0.08659</td>\n", "      <td>0.00478</td>\n", "      <td>450.28</td>\n", "      <td>0.014074</td>\n", "      <td>0.2129</td>\n", "      <td>103757.5</td>\n", "      <td>0.02830</td>\n", "      <td>0.1426</td>\n", "      <td>...</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1747003260000</th>\n", "      <td>0.07376</td>\n", "      <td>0.1623</td>\n", "      <td>0.08656</td>\n", "      <td>0.00478</td>\n", "      <td>449.96</td>\n", "      <td>0.014082</td>\n", "      <td>0.2129</td>\n", "      <td>103734.0</td>\n", "      <td>0.02834</td>\n", "      <td>0.1426</td>\n", "      <td>...</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1747003320000</th>\n", "      <td>0.07370</td>\n", "      <td>0.1623</td>\n", "      <td>0.08656</td>\n", "      <td>0.00478</td>\n", "      <td>449.68</td>\n", "      <td>0.014091</td>\n", "      <td>0.2129</td>\n", "      <td>103717.2</td>\n", "      <td>0.02837</td>\n", "      <td>0.1424</td>\n", "      <td>...</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1747003380000</th>\n", "      <td>0.07369</td>\n", "      <td>0.1627</td>\n", "      <td>0.08656</td>\n", "      <td>0.00478</td>\n", "      <td>449.99</td>\n", "      <td>0.014110</td>\n", "      <td>0.2129</td>\n", "      <td>103716.0</td>\n", "      <td>0.02845</td>\n", "      <td>0.1422</td>\n", "      <td>...</td>\n", "      <td>0.7788</td>\n", "      <td>0.5479</td>\n", "      <td>0.20532</td>\n", "      <td>0.1121</td>\n", "      <td>0.5512</td>\n", "      <td>0.03729</td>\n", "      <td>9.724</td>\n", "      <td>0.1313</td>\n", "      <td>0.09853</td>\n", "      <td>NaN</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "<p>5 rows × 515 columns</p>\n", "</div>"], "text/plain": ["               RAREUSDT  HOOKUSDT  HPOS10IUSDT  SWEATUSDT  TAOUSDT  PENGUUSDT  \\\n", "1747003140000   0.07378    0.1623      0.08659    0.00478   450.25   0.014092   \n", "1747003200000   0.07383    0.1625      0.08659    0.00478   450.28   0.014074   \n", "1747003260000   0.07376    0.1623      0.08656    0.00478   449.96   0.014082   \n", "1747003320000   0.07370    0.1623      0.08656    0.00478   449.68   0.014091   \n", "1747003380000   0.07369    0.1627      0.08656    0.00478   449.99   0.014110   \n", "\n", "               SLFUSDT   BTCUSDT  PIPPINUSDT  SXTUSDT  ...  HIGHUSDT  SDUSDT  \\\n", "1747003140000   0.2129  103750.1     0.02839   0.1426  ...       NaN     NaN   \n", "1747003200000   0.2129  103757.5     0.02830   0.1426  ...       NaN     NaN   \n", "1747003260000   0.2129  103734.0     0.02834   0.1426  ...       NaN     NaN   \n", "1747003320000   0.2129  103717.2     0.02837   0.1424  ...       NaN     NaN   \n", "1747003380000   0.2129  103716.0     0.02845   0.1422  ...    0.7788  0.5479   \n", "\n", "               GOATUSDT  MILKUSDT  SAFEUSDT  JSTUSDT  SSVUSDT  HAEDALUSDT  \\\n", "1747003140000       NaN       NaN       NaN      NaN      NaN         NaN   \n", "1747003200000       NaN       NaN       NaN      NaN      NaN         NaN   \n", "1747003260000       NaN       NaN       NaN      NaN      NaN         NaN   \n", "1747003320000       NaN       NaN       NaN      NaN      NaN         NaN   \n", "1747003380000   0.20532    0.1121    0.5512  0.03729    9.724      0.1313   \n", "\n", "               GLMRUSDT  ENSUSDT  \n", "1747003140000       NaN      NaN  \n", "1747003200000       NaN      NaN  \n", "1747003260000       NaN      NaN  \n", "1747003320000       NaN      NaN  \n", "1747003380000   0.09853      NaN  \n", "\n", "[5 rows x 515 columns]"]}, "execution_count": 10, "metadata": {}, "output_type": "execute_result"}], "source": ["data[\"close\"].head()"]}, {"cell_type": "code", "execution_count": 11, "id": "20845f90", "metadata": {}, "outputs": [], "source": ["import pandas as pd\n"]}, {"cell_type": "code", "execution_count": 12, "id": "e5c9a913", "metadata": {}, "outputs": [], "source": ["home_dir = \"/home/<USER>/git/\"\n", "opt_config =  {'factorNum': 100, 'lb': 3 }\n", "expr = pd.read_csv(home_dir + \"/realTime/Expr/result_1m10m2h_0508.csv\")\n", "expr = list(expr.sort_values(by = 'results_is',ascending=False).head(opt_config['factorNum'])['expr'])\n", "expr.append(\"pn_GroupNeutral(ts_Delta(df['p6_tn6'],9),<PERSON>(df['p5_to2'],ts_<PERSON>(df['p2_et7'],11)))\")"]}, {"cell_type": "code", "execution_count": 13, "id": "102420d3", "metadata": {}, "outputs": [], "source": ["def get_minFactorForms3():\n", "    Forms = pd.DataFrame(columns = ['type','forms'])\n", "    forms = pd.DataFrame(columns = ['type','forms'])\n", "    p1_corrs = [\n", "                'ts_<PERSON>rr(Close,Volume,60)',\n", "                'ts_<PERSON><PERSON>(Close/ts_Delay(Close,1)-1,Volume,60)',\n", "                'ts_<PERSON><PERSON>(ts_<PERSON>ay(Close,1),Volume,60)',\n", "                'ts_<PERSON><PERSON>(Close,ts_<PERSON>ay(Volume,1),60)',\n", "                'ts_<PERSON><PERSON>(ts_Delay(Close/ts_Delay(Close,1)-1,1),Close/ts_Delay(Close,1)-1,60)',\n", "                'ts_<PERSON><PERSON>(ts_Delay(Close/ts_Delay(Close,1)-1,1),Close,60)',\n", "                'ts_<PERSON><PERSON>(Volume,Volume-ts_Delay(Volume,1),60)',\n", "                'ts_<PERSON><PERSON>(Volume-ts_Delay(Volume,1), Volume-ts_Delay(Volume,1) - ts_Delay(Volume-ts_Delay(Volume,1),1),60)',\n", "                'ts_<PERSON>rr(VWAP,Volume,60)',\n", "                'ts_Corr(VWAP/ts_Delay(VWAP,1)-1,Volume,60)',\n", "                'ts_<PERSON>rr(ts_Delay(VWAP,1),Volume,60)',\n", "                'ts_<PERSON><PERSON>(VWAP,ts_Delay(Volume,1),60)',\n", "                'ts_<PERSON>rr(ts_Delay(VWAP/ts_Delay(VWAP,1)-1,1),VWAP/ts_Delay(VWAP,1)-1,60)',\n", "                'ts_<PERSON>rr(ts_Delay(VWAP/ts_Delay(VWAP,1)-1,1),VWA<PERSON>,60)',\n", "                'ts_<PERSON><PERSON>(Volume,Volume-ts_Delay(Volume,1),60)',\n", "                'ts_<PERSON><PERSON>(Volume-ts_Delay(Volume,1), Volume-ts_Delay(Volume,1) - ts_Delay(Volume-ts_Delay(Volume,1),1),60)',\n", "                ]\n", "    names = 'p1_corrs'\n", "    forms['forms'] = p1_corrs\n", "    forms['type'] = names\n", "    z = []\n", "    for v in range(len(p1_corrs)):\n", "        z.append(names + str(int(v)))\n", "    forms['fname'] = z\n", "    Forms = pd.concat([Forms,forms],axis = 0)\n", "    \n", "    forms = pd.DataFrame(columns = ['type','forms'])\n", "    p2_et = [\n", "            'Tot_Mean(IfThen(IfThen(Volume-ts_Delay(Volume,1)-Tot_<PERSON>(Volume-ts_Delay(Volume,1))-Tot_Stdev(Volume-ts_Delay(Volume,1)),1,0),Close/ts_Delay(Close,1)-1,get<PERSON><PERSON>(Close/ts_Delay(Close,1)-1)))',\n", "            'Tot_<PERSON>(IfThen(ts_Sum(IfThen(Tot_Rank(Volume-ts_Delay(Volume,1))-0.9,1,0),10),Close/ts_Delay(Close,1)-1,get<PERSON><PERSON>(Close/ts_Delay(Close,1)-1)))',\n", "            'Tot_Stdev(IfThen(ts_Sum(IfThen(Tot_Rank(Close/ts_Delay(Close,1)-1)-0.9,1,0),10),Close/ts_Delay(Close,1)-1,get<PERSON><PERSON>(Close/ts_Delay(Close,1)-1)))',\n", "            'Tot_<PERSON>(IfThen(IfThen(Close/ts_Delay(Close,1)-1-<PERSON><PERSON>_<PERSON>(Close/ts_Delay(Close,1)-1)-To<PERSON>_Stdev(Close/ts_Delay(Close,1)-1),1,0),Close/ts_Delay(Close,1)-1,get<PERSON><PERSON>(Close/ts_Delay(Close,1)-1)))',\n", "            'Tot_<PERSON>(IfThen(ts_Sum(IfThen(Tot_Rank(Close/ts_Delay(Close,1)-1)-0.9,1,0),10),Close/ts_Delay(Close,1)-1,get<PERSON><PERSON>(Close/ts_Delay(Close,1)-1)))',\n", "            'ts_Stdev(Close/ts_Delay(Close,1)-1,60)-pn_Mean(ts_Stdev(Close/ts_Delay(Close,1)-1,60))',\n", "            'Abs(ts_Stdev(Close/ts_Delay(Close,1)-1,60)-pn_Mean(ts_Stdev(Close/ts_Delay(Close,1)-1,60)))',\n", "            'ts_Mean(Close/ts_Delay(Close,1)-1,60)-pn_Mean(ts_Mean(Close/ts_Delay(Close,1)-1,60))',\n", "            'Abs(ts_Mean(Close/ts_Delay(Close,1)-1,60)-pn_Mean(ts_Mean(Close/ts_Delay(Close,1)-1,60)))',\n", "            'ts_Mean((Close/ts_Delay(Close,1)-1)*IfThen(Volume-ts_Mean(Volume,30)-1.210*ts_Stdev(Volume,30),1,0),30)',\n", "            'ts_Mean(IfThen((Close/ts_Delay(Close,1)-1),(Close/ts_Delay(Close,1)-1),0)*IfThen(Volume-ts_Mean(Volume,30)-0.10*ts_Stdev(Volume,30),1,0),30)',\n", "            'To<PERSON>_<PERSON><PERSON>(IfThen(ts_<PERSON>ay(ts_<PERSON>(Low,30),1)-Low,1,0))',\n", "            'Tot_Stdev(Abs(Close/ts_Delay(Close,1)-1-pn_Mean(Close/ts_Delay(Close,1)-1)))',\n", "            'Tot_Stdev(pn_Rank(Close/ts_Delay(Close,1)-1))',\n", "            'Tot_Stdev(pn_Rank(Volume))',\n", "            'Tot_Mean(Abs(Close/ts_Delay(Close,1)-1-pn_Mean(Close/ts_Delay(Close,1)-1))/(Abs(pn_Mean(Close/ts_Delay(Close,1)-1))+Abs(Close/ts_Delay(Close,1)-1)+0.1))',\n", "            'Tot_Mean(IfThen(-(Close/ts_Delay(Close,1)-1)+(Tot_Mean(Close/ts_Delay(Close,1)-1)-Tot_Stdev(Close/ts_Delay(Close,1)-1)),Close/ts_Delay(Close,1)-1,get<PERSON><PERSON>(Close)))',\n", "            'Tot_<PERSON>(IfThen(Tot_Rank(Close/ts_Delay(Close,1)-1)-0.93,Close/ts_Delay(Close,1)-1,get<PERSON><PERSON>(Close)))',\n", "            'Tot_<PERSON>(IfThen(0.07-Tot_Rank(Close/ts_Delay(Close,1)-1),Close/ts_Delay(Close,1)-1,get<PERSON><PERSON>(Close)))',\n", "            'To<PERSON>_<PERSON><PERSON>(IfThen(Equal(Close/ts_Delay(Close,1)-1,0),1,get<PERSON><PERSON>(Close)))',\n", "        ]\n", "    names = 'p2_et'\n", "    forms['forms'] = p2_et\n", "    forms['type'] = names  \n", "    z = []\n", "    for v in range(len(p2_et)):\n", "        z.append(names + str(int(v)))\n", "    forms['fname'] = z\n", "    Forms = pd.concat([Forms,forms],axis = 0)\n", "    \n", "    forms = pd.DataFrame(columns = ['type','forms'])\n", "    p3_mf = [\n", "                'Tot_Mean(Abs(Close/Open-1)/Sqrt(Volume))',\n", "                'Tot_Stdev(Abs(Close/Open-1)/Sqrt(Volume))',\n", "                'Tot_Mean(IfThen(Tot_Rank(Abs(Close/Open-1)/Sqrt(Volume))-0.8,Close,get<PERSON><PERSON>(Close)))',\n", "                'Tot_Mean(IfThen(Tot_Rank(Abs(Close/Open-1)+(Volume))-0.8,Close,get<PERSON><PERSON>(Close)))',\n", "                'Tot_Mean(IfThen(Tot_Rank(Abs(Close/Open-1)/Log(Volume))-0.8,Close,get<PERSON><PERSON>(Close)))',\n", "                'To<PERSON>_<PERSON><PERSON>(If<PERSON>hen(Equal(Abs(Close-Open),A<PERSON>(High-Low)),Amount,get<PERSON><PERSON>(Close)))',\n", "                'Tot_Sum(IfThen(Tot_Rank(Abs(Close/Open-1)/Sqrt(Volume))-0.8,Volume,get<PERSON>an(Close)))',\n", "                'Tot_Mean(ts_Stdev(Close/ts_Delay(Close,1)-1,10))',\n", "                'Tot_<PERSON>(ts_Stdev(ts_Stdev(Close/ts_Delay(Close,1)-1,10),10))',\n", "                'ts_<PERSON><PERSON>(ts_Stdev(ts_Stdev(Close/ts_Delay(Close,1)-1,10),10),Volume*Close,60)',\n", "                'To<PERSON>_<PERSON><PERSON>(IfThen(ts_Stdev(ts_Stdev(Close/ts_Delay(Close,1)-1,10),10)-<PERSON><PERSON>_<PERSON>(ts_Stdev(ts_Stdev(Close/ts_Delay(Close,1)-1,10),10)),Volume*Close,get<PERSON><PERSON>(Close)))',\n", "                'Tot_Sum(IfThen(Tot_Rank(Volume)-0.8,(Close-Open)/Open,get<PERSON>an(Close)))',\n", "                '(<PERSON><PERSON>_<PERSON>m(IfThen(0.2-Tot_Rank(Volume),(Close-Open)/Open,get<PERSON>an(Close))))',\n", "            ]\n", "    names = 'p3_mf'\n", "    forms['forms'] = p3_mf\n", "    forms['type'] = names\n", "    z = []\n", "    for v in range(len(p3_mf)):\n", "        z.append(names + str(int(v)))\n", "    forms['fname'] = z\n", "    Forms = pd.concat([Forms,forms],axis = 0)\n", "    \n", "    forms = pd.DataFrame(columns = ['type','forms'])\n", "    p4_ms = [\n", "            'Tot_<PERSON>(Close/ts_Delay(Close,1)-1-Log(Close/ts_Delay(Close,1)))',\n", "            'Tot_Mean(Close/ts_Delay(Close,1)-1-Log(Close/ts_Delay(Close,1))-(Log(Close/ts_Delay(Close,1)))**2/2)',\n", "            'Tot_ArgMax(Close)',\n", "            '<PERSON><PERSON>_<PERSON><PERSON><PERSON><PERSON>(Close)',\n", "            'Tot_Sum(((Close-ts_Delay(Close,1))/Close)**2)',\n", "            'Tot_Sum(((Close-ts_Delay(Close,1))/Close)**3)',\n", "            'To<PERSON>_<PERSON><PERSON>(IfThen(Close/ts_Delay(Close,1)-1,Volume,get<PERSON><PERSON>(Close)))',\n", "            ]\n", "    names = 'p4_ms'\n", "    forms['forms'] = p4_ms\n", "    forms['type'] = names \n", "    z = []\n", "    for v in range(len(p4_ms)):\n", "        z.append(names + str(int(v)))\n", "    forms['fname'] = z\n", "    Forms = pd.concat([Forms,forms],axis = 0)\n", "    \n", "    forms = pd.DataFrame(columns = ['type','forms'])\n", "    p5_to = [\n", "            'To<PERSON>_<PERSON><PERSON>(If<PERSON>hen(Close-ts_<PERSON>ay(Close,1),Amount,-Amount))',\n", "            'Tot_ArgMax(Volume)',\n", "            'ts_<PERSON><PERSON>(Amount,ts_<PERSON><PERSON>(Amount,1),60)',\n", "            'Tot_Sum(Amount)/(Tot_Sum(Abs(High-Low)/Close+Abs(Open-ts_Delay(Close,1)))*(1+Tot_Stdev(Close)/Tot_Mean(Close)))',\n", "            'Tot_Mean(Abs(Close/ts_Delay(Close,1)-1)/Amount)',\n", "            'Tot_Sum((High-Low)/Close)/Tot_Sum(Amount)',\n", "            'Tot_Sum((2*(High-Low)-Abs(Open-Close))/Close)/Tot_Sum(Amount)',\n", "            'Tot_Sum(Abs(Close/ts_Delay(Close,1)-1)/(Close*Volume))',\n", "            ]\n", "    names = 'p5_to'\n", "    forms['forms'] = p5_to\n", "    forms['type'] = names\n", "    z = []\n", "    for v in range(len(p5_to)):\n", "        z.append(names + str(int(v)))\n", "    forms['fname'] = z\n", "    Forms = pd.concat([Forms,forms],axis = 0)\n", "    \n", "    forms = pd.DataFrame(columns = ['type','forms'])\n", "    p6_tn = [\n", "            'Tot_<PERSON>m(IfThen(Close-(ts_Mean(Close,30)+ts_Stdev(Close,30)),-1,IfThen((ts_Mean(Close,30)-ts_Stdev(Close,30))-Close,1,0)))',\n", "            '<PERSON><PERSON>_<PERSON><PERSON>(High-Open)-<PERSON><PERSON>_<PERSON><PERSON>(Open-Low)',\n", "            \"ts_Regression(High,Low,60,'D')\",\n", "            'Tot_Mean(((High+Low)-ts_Delay(High+Low,1))*(High-Low)/2/Amount)',\n", "            'Tot_<PERSON>m(IfThen(Close-ts_Delay(Close,1),1,0))',\n", "            'Tot_<PERSON>(High-ts_Delay(Close,1))/Tot_Sum(ts_Delay(Close,1)-Low)',\n", "            'Tot_Mean((ts_<PERSON>(High,30)-Close)/(ts_<PERSON>(High,30)-ts_<PERSON>(Low,30)))',\n", "            'Tot_Mean((Close-ts_Min(Low,30))/(ts_<PERSON>(High,30)-ts_<PERSON>(Low,30)))',\n", "            'Tot_Mean((High-Low)/Close)',\n", "            'Tot_Mean(IfThen(Tot_Rank((Close-ts_Delay(Close,1))/Close)-0.910,(Close-ts_Delay(Close,1))/Close,get<PERSON><PERSON>(Close)))',\n", "            'Tot_Mean(IfThen(Tot_Rank(0.010-(Close-ts_Delay(Close,1))/Close),(Close-ts_Delay(Close,1))/Close,get<PERSON><PERSON>(Close)))',\n", "            'ts_<PERSON><PERSON>(Close-ts_Delay(Close,1),pn_<PERSON>(Close-ts_Delay(Close,1)),60)',\n", "            'Tot_Sum(IfThen(Tot_Rank(High-Low)-0.10,Volume,0))-Tot_Sum(IfThen(Tot_Rank(High-Low)-0.10,0,Volume))',\n", "            'To<PERSON>_<PERSON><PERSON>(If<PERSON><PERSON>(Close-ts_Delay(Close,1),Volume,-Volume))',\n", "            ]\n", "    names = 'p6_tn'\n", "    forms['forms'] = p6_tn\n", "    forms['type'] = names  \n", "    z = []\n", "    for v in range(len(p6_tn)):\n", "        z.append(names + str(int(v)))\n", "    forms['fname'] = z\n", "    Forms = pd.concat([Forms,forms],axis = 0)\n", "    return Forms"]}, {"cell_type": "code", "execution_count": 14, "id": "26cc8f4f", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["feature need: len 71\n"]}], "source": ["features_names1 = get_minFactorForms3()\n", "features_names1 = list(features_names1['fname'])\n", "features_names2 = [  'kama','adosc','dcperiod','dcphase','cci',  'cmo','dx','di','dm','ultosc','liangle','lislope']\n", "features_names = features_names1 + features_names2\n", "\n", "# 判断需要哪些featrue\n", "need = []\n", "for v in features_names:\n", "    for vv in expr:\n", "        if v in vv:\n", "            need.append(v) \n", "            break\n", "print('feature need: len',len(need))"]}, {"cell_type": "code", "execution_count": 16, "id": "b6c4d371", "metadata": {}, "outputs": [], "source": ["import numpy as np\n", "def ts_Corr(s1, s2, n):\n", "    n = int(n)\n", "    if n <= 1:\n", "        n = 1\n", "    tem1 = s1.copy()\n", "    tem2 = s2.copy()\n", "    tem1[tem2.isna()] = np.nan\n", "    tem2[tem1.isna()] = np.nan\n", "    tem1_m = tem1.rolling(n, axis=0, min_periods=1).mean()\n", "    tem2_m = tem2.rolling(n, axis=0, min_periods=1).mean()\n", "    tem_prod_m = (tem1 * tem2).rolling(n, axis=0, min_periods=1).mean()\n", "    tem1_std = tem1.rolling(n, axis=0, min_periods=1).std(ddof=0)\n", "    tem2_std = tem2.rolling(n, axis=0, min_periods=1).std(ddof=0)\n", "    res = (tem_prod_m - tem1_m * tem2_m) / (tem1_std * tem2_std)\n", "    return res.replace([-np.inf, np.inf], np.nan)"]}, {"cell_type": "code", "execution_count": 19, "id": "b8a99139", "metadata": {}, "outputs": [{"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>RAREUSDT</th>\n", "      <th>HOOKUSDT</th>\n", "      <th>HPOS10IUSDT</th>\n", "      <th>SWEATUSDT</th>\n", "      <th>TAOUSDT</th>\n", "      <th>PENGUUSDT</th>\n", "      <th>SLFUSDT</th>\n", "      <th>BTCUSDT</th>\n", "      <th>PIPPINUSDT</th>\n", "      <th>SXTUSDT</th>\n", "      <th>...</th>\n", "      <th>HIGHUSDT</th>\n", "      <th>SDUSDT</th>\n", "      <th>GOATUSDT</th>\n", "      <th>MILKUSDT</th>\n", "      <th>SAFEUSDT</th>\n", "      <th>JSTUSDT</th>\n", "      <th>SSVUSDT</th>\n", "      <th>HAEDALUSDT</th>\n", "      <th>GLMRUSDT</th>\n", "      <th>ENSUSDT</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>1747003140000</th>\n", "      <td>0.07378</td>\n", "      <td>0.1623</td>\n", "      <td>0.08659</td>\n", "      <td>0.00478</td>\n", "      <td>450.25</td>\n", "      <td>0.014092</td>\n", "      <td>0.2129</td>\n", "      <td>103750.1</td>\n", "      <td>0.02839</td>\n", "      <td>0.1426</td>\n", "      <td>...</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1747003200000</th>\n", "      <td>0.07383</td>\n", "      <td>0.1625</td>\n", "      <td>0.08659</td>\n", "      <td>0.00478</td>\n", "      <td>450.28</td>\n", "      <td>0.014074</td>\n", "      <td>0.2129</td>\n", "      <td>103757.5</td>\n", "      <td>0.02830</td>\n", "      <td>0.1426</td>\n", "      <td>...</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1747003260000</th>\n", "      <td>0.07376</td>\n", "      <td>0.1623</td>\n", "      <td>0.08656</td>\n", "      <td>0.00478</td>\n", "      <td>449.96</td>\n", "      <td>0.014082</td>\n", "      <td>0.2129</td>\n", "      <td>103734.0</td>\n", "      <td>0.02834</td>\n", "      <td>0.1426</td>\n", "      <td>...</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1747003320000</th>\n", "      <td>0.07370</td>\n", "      <td>0.1623</td>\n", "      <td>0.08656</td>\n", "      <td>0.00478</td>\n", "      <td>449.68</td>\n", "      <td>0.014091</td>\n", "      <td>0.2129</td>\n", "      <td>103717.2</td>\n", "      <td>0.02837</td>\n", "      <td>0.1424</td>\n", "      <td>...</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1747003380000</th>\n", "      <td>0.07369</td>\n", "      <td>0.1627</td>\n", "      <td>0.08656</td>\n", "      <td>0.00478</td>\n", "      <td>449.99</td>\n", "      <td>0.014110</td>\n", "      <td>0.2129</td>\n", "      <td>103716.0</td>\n", "      <td>0.02845</td>\n", "      <td>0.1422</td>\n", "      <td>...</td>\n", "      <td>0.7788</td>\n", "      <td>0.5479</td>\n", "      <td>0.20532</td>\n", "      <td>0.1121</td>\n", "      <td>0.5512</td>\n", "      <td>0.03729</td>\n", "      <td>9.724</td>\n", "      <td>0.1313</td>\n", "      <td>0.09853</td>\n", "      <td>NaN</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "<p>5 rows × 515 columns</p>\n", "</div>"], "text/plain": ["               RAREUSDT  HOOKUSDT  HPOS10IUSDT  SWEATUSDT  TAOUSDT  PENGUUSDT  \\\n", "1747003140000   0.07378    0.1623      0.08659    0.00478   450.25   0.014092   \n", "1747003200000   0.07383    0.1625      0.08659    0.00478   450.28   0.014074   \n", "1747003260000   0.07376    0.1623      0.08656    0.00478   449.96   0.014082   \n", "1747003320000   0.07370    0.1623      0.08656    0.00478   449.68   0.014091   \n", "1747003380000   0.07369    0.1627      0.08656    0.00478   449.99   0.014110   \n", "\n", "               SLFUSDT   BTCUSDT  PIPPINUSDT  SXTUSDT  ...  HIGHUSDT  SDUSDT  \\\n", "1747003140000   0.2129  103750.1     0.02839   0.1426  ...       NaN     NaN   \n", "1747003200000   0.2129  103757.5     0.02830   0.1426  ...       NaN     NaN   \n", "1747003260000   0.2129  103734.0     0.02834   0.1426  ...       NaN     NaN   \n", "1747003320000   0.2129  103717.2     0.02837   0.1424  ...       NaN     NaN   \n", "1747003380000   0.2129  103716.0     0.02845   0.1422  ...    0.7788  0.5479   \n", "\n", "               GOATUSDT  MILKUSDT  SAFEUSDT  JSTUSDT  SSVUSDT  HAEDALUSDT  \\\n", "1747003140000       NaN       NaN       NaN      NaN      NaN         NaN   \n", "1747003200000       NaN       NaN       NaN      NaN      NaN         NaN   \n", "1747003260000       NaN       NaN       NaN      NaN      NaN         NaN   \n", "1747003320000       NaN       NaN       NaN      NaN      NaN         NaN   \n", "1747003380000   0.20532    0.1121    0.5512  0.03729    9.724      0.1313   \n", "\n", "               GLMRUSDT  ENSUSDT  \n", "1747003140000       NaN      NaN  \n", "1747003200000       NaN      NaN  \n", "1747003260000       NaN      NaN  \n", "1747003320000       NaN      NaN  \n", "1747003380000   0.09853      NaN  \n", "\n", "[5 rows x 515 columns]"]}, "execution_count": 19, "metadata": {}, "output_type": "execute_result"}], "source": ["data['close'].head()"]}, {"cell_type": "code", "execution_count": 17, "id": "3bafdbb0", "metadata": {}, "outputs": [], "source": ["features1 = {}\n", "Forms = get_minFactorForms3()  # 获取特征序列公式\n", "for v in range(len(Forms)):\n", "    # start_time = time.time()\n", "    expr = Forms['forms'].values[v]\n", "    fm = Forms['fname'].values[v]\n", "    if fm in need and fm == \"p1_corrs0\":\n", "        tmp = ts_Corr(data['close'],data['volume'],60)\n", "        features1[fm] = tmp"]}, {"cell_type": "code", "execution_count": null, "id": "4f9bf68e", "metadata": {}, "outputs": [{"data": {"text/plain": ["{'p1_corrs0':                RAREUSDT  HOOKUSDT  HPOS10IUSDT  SWEATUSDT   TAOUSDT  \\\n", " 1747003140000       NaN       NaN          NaN        NaN       NaN   \n", " 1747003200000 -1.000000  1.000000          NaN        NaN  1.000000   \n", " 1747003260000 -0.932209 -0.437151    -0.974778        NaN  0.994133   \n", " 1747003320000 -0.333429 -0.182395    -0.232673        NaN  0.875471   \n", " 1747003380000 -0.035456  0.857002    -0.006826        NaN  0.851453   \n", " ...                 ...       ...          ...        ...       ...   \n", " 1747294200000 -0.131986 -0.060600    -0.386367  -0.146161 -0.402957   \n", " 1747294260000 -0.149858 -0.194843    -0.373588  -0.132211 -0.388040   \n", " 1747294320000 -0.161631 -0.165041    -0.361549  -0.141680 -0.367575   \n", " 1747294380000 -0.283542 -0.146519    -0.356536  -0.124332 -0.355133   \n", " 1747294440000 -0.267578 -0.130033    -0.347909  -0.118968 -0.332982   \n", " \n", "                PENGUUSDT   SLFUSDT   BTCUSDT  PIPPINUSDT   SXTUSDT  ...  \\\n", " 1747003140000        NaN       NaN       NaN         NaN       NaN  ...   \n", " 1747003200000   1.000000       NaN -1.000000   -1.000000       NaN  ...   \n", " 1747003260000  -0.001082       NaN  0.682145   -0.826065       NaN  ...   \n", " 1747003320000  -0.143094       NaN -0.182962   -0.498582 -0.120552  ...   \n", " 1747003380000  -0.249560       NaN  0.131188   -0.576143  0.333969  ...   \n", " ...                  ...       ...       ...         ...       ...  ...   \n", " 1747294200000  -0.441499 -0.094159 -0.391704   -0.502619 -0.257434  ...   \n", " 1747294260000  -0.538520 -0.075857 -0.385317   -0.463789 -0.247988  ...   \n", " 1747294320000  -0.532528 -0.063144 -0.366280   -0.367889 -0.402965  ...   \n", " 1747294380000  -0.611960 -0.043193 -0.369060   -0.328141 -0.397201  ...   \n", " 1747294440000  -0.656503 -0.015086 -0.352818   -0.319010 -0.387288  ...   \n", " \n", "                HIGHUSDT    SDUSDT  GOATUSDT  MILKUSDT  SAFEUSDT   JSTUSDT  \\\n", " 1747003140000       NaN       NaN       NaN       NaN       NaN       NaN   \n", " 1747003200000       NaN       NaN       NaN       NaN       NaN       NaN   \n", " 1747003260000       NaN       NaN       NaN       NaN       NaN       NaN   \n", " 1747003320000       NaN       NaN       NaN       NaN       NaN       NaN   \n", " 1747003380000       NaN       NaN       NaN       NaN       NaN       NaN   \n", " ...                 ...       ...       ...       ...       ...       ...   \n", " 1747294200000 -0.079736 -0.726242 -0.166767 -0.304370 -0.133918 -0.049137   \n", " 1747294260000 -0.154059 -0.700003 -0.012149 -0.427785 -0.311568 -0.039998   \n", " 1747294320000 -0.145875 -0.673667  0.050535 -0.439101 -0.293585 -0.044932   \n", " 1747294380000 -0.201597 -0.639890  0.008479 -0.426439 -0.272365 -0.037119   \n", " 1747294440000 -0.383100 -0.610738 -0.000316 -0.409898 -0.240484 -0.026827   \n", " \n", "                 SSVUSDT  HAEDALUSDT  GLMRUSDT   ENSUSDT  \n", " 1747003140000       NaN         NaN       NaN       NaN  \n", " 1747003200000       NaN         NaN       NaN       NaN  \n", " 1747003260000       NaN         NaN       NaN       NaN  \n", " 1747003320000       NaN         NaN       NaN       NaN  \n", " 1747003380000       NaN         NaN       NaN       NaN  \n", " ...                 ...         ...       ...       ...  \n", " 1747294200000  0.114208   -0.440541 -0.662129 -0.547403  \n", " 1747294260000  0.110193   -0.432815 -0.607930 -0.566166  \n", " 1747294320000  0.116220   -0.424012 -0.564793 -0.555517  \n", " 1747294380000  0.109835   -0.396508 -0.622087 -0.604398  \n", " 1747294440000  0.089337   -0.385320 -0.619046 -0.598160  \n", " \n", " [4856 rows x 515 columns]}"]}, "execution_count": 18, "metadata": {}, "output_type": "execute_result"}], "source": ["features1"]}], "metadata": {"kernelspec": {"display_name": "Python 3", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.10.13"}}, "nbformat": 4, "nbformat_minor": 5}