{"cells": [{"cell_type": "code", "execution_count": 1, "id": "ecd25bcb", "metadata": {}, "outputs": [], "source": ["import pandas as pd\n", "import numpy as np"]}, {"cell_type": "code", "execution_count": 2, "id": "c545565c", "metadata": {}, "outputs": [], "source": ["home_dir = \"/home/<USER>/git/\"\n", "opt_config =  {'factorNum': 100, 'lb': 3 }\n", "expr = pd.read_csv(home_dir + \"/realTime/Expr/result_1m10m2h_0508.csv\")\n", "expr = list(expr.sort_values(by = 'results_is',ascending=False).head(opt_config['factorNum'])['expr'])\n", "expr.append(\"pn_GroupNeutral(ts_Delta(df['p6_tn6'],9),<PERSON>(df['p5_to2'],ts_<PERSON>(df['p2_et7'],11)))\")"]}, {"cell_type": "code", "execution_count": 6, "id": "34384d67", "metadata": {}, "outputs": [{"data": {"text/plain": ["[\"Minus(<PERSON><PERSON>than(df['p6_tn4'],df['p4_ms5']),ts_Delta(get_LINEARREG_ANGLE(df['ultosc'],11),8))\",\n", " \"-1*get_LINEARREG_ANGLE(pn_GroupRank(df['p6_tn10'],Exp(df['p3_mf4'])),12)\",\n", " \"-1*Add(ts_Mean(pn_GroupRank(df['p5_to2'],df['p4_ms0']),39),Min(df['p5_to2'],get_LINEARREG_ANGLE(df['ultosc'],11)))\",\n", " \"-1*ts_Scale(ts_Divide(df['ultosc'],9),9)\",\n", " \"get_<PERSON><PERSON>(df['p6_tn6'],<PERSON>(df['p2_et18'],0.514),ts_<PERSON><PERSON>(df['p6_tn6'],10),40)\",\n", " \"-1*get_<PERSON><PERSON>(pn_<PERSON>(df['p2_et1']),<PERSON><PERSON>(df['p6_tn6'],ts_MeanChg(df['p5_to0'],13)),<PERSON>(df['p6_tn4'],pn_<PERSON>(df['p2_et4'])),9)\",\n", " \"-1*pn_GroupNorm(pn_GroupNeutral(ts_Delta(df['p2_et1'],10),ts_<PERSON>rgmin(df['p2_et16'],43)),UnEqual(ts_Entropy(df['p5_to6'],10),pn_Rank(df['p5_to0'])))\",\n", " \"-1*ts_<PERSON>(ts_Rank(df['liangle'],9),9)\",\n", " \"-1*pn_GroupNeutral(pn_Stand(Exp(df['p2_et7'])),ts_CovChg(ts_Stdev(df['p3_mf5'],11),ts_CorrChg(df['cci'],df['p3_mf9'],38),11))\",\n", " \"-1*Min(ts_Scale(df['dcphase'],21),ts_Delta(df['p6_tn5'],10))\",\n", " \"-1*get_CCI(df['p6_tn0'],ts_<PERSON>(get_CMO(df['p2_et7'],8),26),df['p2_et14'],36)\",\n", " \"-1*pn_GroupNeutral(get_LINEARREG_ANGLE(df['p2_et1'],12),ts_Product(pn_GroupNorm(df['p6_tn1'],df['p3_mf0']),7))\",\n", " \"-1*pn_GroupNeutral(ts_Scale(df['p2_et7'],9),ts_Delay(pn_TransStd(df['p2_et7']),19))\",\n", " \"-1*ts_Regression(ts_Cov2(df['ultosc'],pn_Stand(df['p4_ms0']),3),ts_TransNorm(df['p2_et1'],18),46,'C')\",\n", " \"ts_MeanChg(get_CCI(ts_Delay(df['p5_to0'],48),ts_Cov2(df['p2_et13'],df['p3_mf4'],43),df['p5_to0'],15),10)\",\n", " \"-1*ts_MeanChg(FilterInf(ts_Scale(df['p6_tn6'],33)),10)\",\n", " \"-1*get_CCI(pn_GroupNorm(df['ultosc'],df['p3_mf9']),ts_Decay2(pn_FillMax(df['p6_tn4']),3),ts_<PERSON>(df['p5_to0'],40),10)\",\n", " \"-1*pn_GroupNeutral(ts_Delta(df['p2_et0'],45),ts_StdevChg(get_CMO(df['p2_et17'],6),43))\",\n", " \"-1*get_CCI(ts_<PERSON><PERSON>(Equal(df['p2_et16'],df['p1_corrs3']),42),pn_FillMin(ts_Divide(df['p6_tn8'],27)),ts_<PERSON>(df['p6_tn1'],11),23)\",\n", " \"-1*get_CCI(ts_StdevChg(df['p5_to5'],14),<PERSON>(df['p1_corrs2'],36),<PERSON>us(df['p2_et10'],df['dm']),14)\",\n", " \"-1*Power(get_CCI(Log(df['p1_corrs8']),ts_ChgRate(df['p6_tn7'],26),ts_TransNorm(df['p6_tn7'],6),12),23)\",\n", " \"-1*pn_GroupRank(ts_TransNorm(ts_Delta(df['p6_tn13'],10),31),ts_TransNorm(ts_Delta(df['p4_ms1'],37),10))\",\n", " \"-1*get_LINEARREG_ANGLE(Min(df['p6_tn10'],Min(df['p4_ms0'],df['p6_tn4'])),15)\",\n", " \"-1*pn_GroupNeutral(df['p4_ms1'],ts_Delay(df['p4_ms1'],7))\",\n", " \"ts_Divide(ts_Scale(df['di'],8),11)\",\n", " \"pn_GroupRank(ts_<PERSON>(Reverse(df['p6_tn10']),27),ts_Decay2(Abs(df['p6_tn4']),24))\",\n", " \"Add(df['p2_et8'],ts_<PERSON><PERSON>min(pn_<PERSON>sor(df['di'],8),5))\",\n", " \"Add(Exp(df['p6_tn6']),pn_GroupRank(ts_Divide(df['p6_tn12'],35),df['p2_et10']))\",\n", " \"-1*ts_Scale(ts_Regression(FilterInf(df['cmo']),df['p2_et2'],10,'D'),12)\",\n", " \"-1*<PERSON>(ts_<PERSON>(Min(df['p4_ms5'],0.399),7),get_KAMA(Mthan(df['p3_mf5'],df['p3_mf12']),7))\",\n", " \"-1*Minus(ts_TransNorm(df['p6_tn13'],1),ts_Delta(pn_TransStd(df['p5_to0']),24))\",\n", " \"-1*ts_Regression(ts_Regression(FilterInf(df['cmo']),get_HT_DCPHASE(df['p2_et2']),10,'D'),get_MINUS_DM(df['p5_to3'],ts_Stdev(df['liangle'],31),21),17,'D')\",\n", " \"-1*Add(Min(Sign(df['p5_to0']),pn_TransStd(df['p4_ms5'])),df['p5_to2'])\",\n", " \"Minus(ts_Mean(ts_Skewness(df['p3_mf4'],47),8),ts_TransNorm(get_CCI(df['p3_mf9'],df['p5_to2'],df['p6_tn13'],14),45))\",\n", " \"-1*pn_GroupRank(ts_Delta(df['p2_et9'],4),ts_TransNorm(ts_Max(df['p2_et12'],39),13))\",\n", " \"ts_MeanChg(Minus(df['p3_mf11'],df['p2_et7']),6)\",\n", " \"-1*Add(ts_Skewness(Exp(df['p3_mf3']),11),pn_Stand(df['p4_ms5']))\",\n", " \"Power(get_CCI(df['p6_tn6'],ts_Decay(df['dcperiod'],34),get_CMO(df['p6_tn6'],24),12),23)\",\n", " \"-1*pn_GroupNorm(get_LINEARREG_SLOPE(df['p3_mf11'],7),Add(ts_Corr(df['p3_mf5'],df['p2_et10'],44),df['dcperiod']))\",\n", " \"-1*ts_Scale(ts_Regression(FilterInf(df['cmo']),get_CMO(df['p4_ms2'],39),7,'D'),12)\",\n", " \"-1*pn_GroupNeutral(Power(Abs(df['p1_corrs1']),2),df['p2_et7'])\",\n", " \"-1*Multiply(Multiply(pn_FillMax(df['p3_mf7']),get_HT_DCPHASE(df['p6_tn2'])),pn_GroupRank(df['p5_to0'],ts_Quantile(df['p6_tn13'],3,'A')))\",\n", " \"get_MINUS_DM(get_MINUS_DM(df['liangle'],df['p5_to7'],49),pn_CrossFit(ts_Median(df['p2_et17'],49),get_MINUS_DM(df['p2_et17'],df['p5_to7'],12)),37)\",\n", " \"-1*Min(<PERSON><PERSON>(pn_TransStd(df['p2_et8'])),ts_Decay(df['p2_et15'],42))\",\n", " \"-1*Min(ts_<PERSON>(Min(df['p4_ms5'],36),50),get_KAMA(Mthan(df['p3_mf5'],df['p3_mf12']),7))\",\n", " \"pn_GroupNeutral(ts_Product(pn_GroupNorm(df['p2_et15'],df['p5_to4']),5),ts_Sum(df['p3_mf7'],22))\",\n", " \"-1*Add(Min(ts_Median(df['p6_tn2'],25),pn_GroupNorm(df['p5_to0'],df['p5_to3'])),df['p2_et14'])\",\n", " \"-1*Min(df['p4_ms1'],ts_Cov(get_LINEARREG_ANGLE(df['p5_to0'],6),df['p3_mf8'],27))\",\n", " \"-1*Add(IfThen(df['p2_et12'],44,19),Multiply(df['p2_et14'],df['p6_tn10']))\",\n", " \"pn_GroupNeutral(<PERSON>erse(ts_<PERSON>(df['p3_mf3'],43)),get_CMO(df['dm'],45))\",\n", " \"-1*pn_GroupNeutral(df['p6_tn10'],ts_StdevChg(ts_Corr2(df['p1_corrs1'],df['p3_mf5'],13),22))\",\n", " \"-1*Min(pn_TransNorm(df['p4_ms5']),pn_Stand(ts_Stdev(df['p6_tn11'],36)))\",\n", " \"-1*pn_GroupRank(df['p5_to0'],ts_<PERSON>(Reverse(df['p3_mf5']),10))\",\n", " \"-1*ts_Regression(df['p4_ms1'],ts_Rank(ts_TransNorm(df['p2_et7'],49),24),42,'C')\",\n", " \"-1*Min(pn_GroupNorm(df['p3_mf11'],ts_<PERSON>(df['p5_to0'],14)),pn_Stand(df['p2_et14']))\",\n", " \"ts_MeanChg(get_CCI(df['p4_ms0'],UnEqual(df['p6_tn5'],df['p1_corrs1']),pn_TransNorm(df['ultosc']),15),10)\",\n", " \"-1*get_CCI(get_LINEARREG_ANGLE(df['p3_mf1'],34),<PERSON>or(Sign(df['p3_mf9']),Sign(df['p6_tn11'])),ts_Median(ts_Scale(df['p6_tn10'],10),5),32)\",\n", " \"Minus(get_LINEARREG_SLOPE(df['di'],4),Min(Exp(df['p3_mf11']),get_KAMA(df['p4_ms4'],11)))\",\n", " \"-1*ts_Regression(Divide(Power(df['p2_et15'],31),df['p4_ms0']),ts_Sum(df['p3_mf7'],32),13,'D')\",\n", " \"ts_MeanChg(get_CCI(df['p5_to0'],pn_RankCentered(df['p3_mf8']),<PERSON>than(df['p6_tn10'],df['dx']),10),15)\",\n", " \"-1*ts_Divide(df['p4_ms3'],10)\",\n", " \"pn_GroupNeutral(ts_<PERSON>vide(df['dm'],4),<PERSON><PERSON>(ts_<PERSON>(df['p6_tn5'],21),ts_Corr2(df['p2_et7'],df['di'],39)))\",\n", " \"-1*get_CCI(ts_Delta(df['p6_tn13'],26),df['p2_et12'],df['p4_ms5'],14)\",\n", " \"-1*pn_GroupNeutral(ts_Rank(df['p3_mf11'],16),ts_Min(df['p2_et15'],6))\",\n", " \"-1*Min(get_LINEARREG_ANGLE(df['p2_et1'],10),ts_Cov2(df['p2_et7'],df['p4_ms6'],36))\",\n", " \"-1*pn_CrossFit(ts_Cov2(ts_<PERSON>(df['p6_tn9'],32),ts_<PERSON>(df['p3_mf5'],13),21),<PERSON>(ts_Scale(df['p2_et0'],1),get_KAMA(df['p2_et0'],4)))\",\n", " \"Minus(pn_GroupNeutral(<PERSON><PERSON><PERSON>(df['p2_et12'],df['di']),pn_TransStd(df['p3_mf3'])),ts_Delay(FilterInf(df['p4_ms5']),36))\",\n", " \"-1*get_CCI(df['cmo'],pn_Rank(Log(df['p1_corrs5'])),ts_Argmin(pn_Rank(df['p6_tn2']),48),7)\",\n", " \"-1*Add(ts_Decay2(df['p1_corrs0'],42),get_LINEARREG_SLOPE(df['p6_tn4'],13))\",\n", " \"-1*Min(ts_Decay(Multiply(df['p3_mf9'],df['p1_corrs3']),44),pn_GroupNorm(df['ultosc'],ts_Sum(df['p3_mf7'],6)))\",\n", " \"-1*Multiply(df['p5_to4'],pn_TransStd(df['p3_mf11']))\",\n", " \"-1*pn_GroupRank(ts_Delay(df['p2_et10'],35),pn_TransStd(df['ultosc']))\",\n", " \"SignedPower(ts_Rank(df['dm'],8),Add(Sqrt(df['p1_corrs5']),df['p1_corrs1']))\",\n", " \"-1*pn_GroupRank(ts_MeanChg(df['p4_ms2'],11),ts_Median(pn_Winsor(df['p1_corrs1'],40),29))\",\n", " \"ts_MeanChg(pn_TransNorm(pn_TransNorm(df['p5_to0'])),10)\",\n", " \"Multiply(df['p6_tn10'],pn_CrossFit(ts_Mean(df['p2_et13'],46),df['p4_ms6']))\",\n", " \"-1*ts_ChgRate(pn_Rank(df['p2_et7']),42)\",\n", " \"-1*pn_GroupNeutral(pn_Winsor(ts_Delta(df['p2_et0'],40),32),Power(df['p5_to4'],35))\",\n", " \"-1*Min(ts_Median(df['p1_corrs5'],3),get_LINEARREG_SLOPE(Add(df['p4_ms3'],df['p2_et12']),13))\",\n", " \"-1*Signed<PERSON>ower(FilterInf(ts_Divide(df['p6_tn8'],43)),df['p2_et9'])\",\n", " \"-1*pn_GroupNeutral(pn_TransNorm(ts_Cov(df['p4_ms5'],df['p2_et14'],16)),And(pn_Cut(df['p5_to7']),ts_Rank(df['p2_et10'],24)))\",\n", " \"-1*Multiply(ts_Corr2(df['p3_mf3'],df['p2_et4'],18),Power(pn_TransNorm(df['p5_to0']),45))\",\n", " \"-1*Min(get_LINEARREG_ANGLE(df['p6_tn7'],13),ts_Rank(ts_Median(df['p2_et15'],25),12))\",\n", " \"-1*pn_CrossFit(Minus(ts_Partial_corr(df['p4_ms0'],df['p4_ms4'],df['p1_corrs7'],45),Sign(df['p2_et5'])),Minus(df['p6_tn10'],df['p3_mf7']))\",\n", " \"get_<PERSON><PERSON>(ts_<PERSON><PERSON>(df['p6_tn8'],8),<PERSON><PERSON>(df['p2_et4']),pn_FillMin(ts_Delay(df['p6_tn8'],37)),42)\",\n", " \"-1*Add(get_CCI(df['p6_tn4'],ts_<PERSON><PERSON><PERSON>(df['p2_et9'],6),ts_<PERSON><PERSON><PERSON>(df['p4_ms1'],5),34),29)\",\n", " \"-1*pn_GroupNeutral(pn_<PERSON><PERSON>(ts_Delta(df['p2_et9'],40),3),Log(df['p5_to4']))\",\n", " \"-1*Min(Add(Sign(df['p2_et2']),df['p3_mf3']),ts_Scale(ts_Scale(df['p5_to0'],0.217),16))\",\n", " \"-1*pn_GroupRank(pn_TransStd(ts_Delta(df['p2_et9'],26)),pn_GroupRank(df['p2_et4'],df['p5_to3']))\",\n", " \"-1*get_CCI(inv(df['p5_to0']),ts_<PERSON>rr(Not(df['p6_tn6']),Add(df['p2_et18'],0.542),20),ts_Decay(ts_Scale(df['p6_tn4'],12),49),10)\",\n", " \"-1*ts_Rank(get_CCI(df['p2_et16'],df['dm'],ts_Rank(df['cmo'],31),22),11)\",\n", " \"pn_GroupNorm(ts_Rank(df['dm'],36),get_MINUS_DM(df['p1_corrs5'],pn_GroupNeutral(df['p2_et11'],df['p2_et13']),49))\",\n", " \"-1*Min(get_LINEARREG_ANGLE(df['cmo'],6),ts_Rank(ts_Mean(df['p2_et14'],24),33))\",\n", " \"-1*ts_Scale(get_CCI(inv(df['dm']),pn_CrossFit(df['dcphase'],df['p2_et14']),df['p6_tn7'],36),15)\",\n", " \"-1*get_CCI(pn_GroupRank(df['p2_et1'],ts_Entropy(df['dm'],19)),df['p3_mf8'],get_MINUS_DM(df['p3_mf7'],ts_Scale(df['dm'],6),33),35)\",\n", " \"-1*get_CCI(ts_<PERSON>(df['p5_to1'],25),df['ultosc'],ts_Delay(df['ultosc'],32),45)\",\n", " \"-1*pn_GroupNeutral(ts_<PERSON><PERSON>(Log(df['p6_tn5']),36),ts_CovChg(df['p5_to1'],df['p6_tn9'],5))\",\n", " \"-1*SignedPower(get_HT_DCPHASE(df['p6_tn13']),df['p2_et0'])\",\n", " \"-1*pn_GroupNorm(get_LINEARREG_SLOPE(Max(df['p2_et7'],df['p2_et4']),7),ts_Entropy(ts_Entropy(df['p5_to0'],7),46))\",\n", " \"pn_GroupNorm(get_CMO(ts_Delta(df['dm'],10),12),inv(get_LINEARREG_SLOPE(df['p2_et7'],9)))\",\n", " \"pn_GroupNeutral(ts_<PERSON>(df['p6_tn6'],9),<PERSON>(df['p5_to2'],ts_<PERSON>(df['p2_et7'],11)))\"]"]}, "execution_count": 6, "metadata": {}, "output_type": "execute_result"}], "source": ["expr"]}, {"cell_type": "code", "execution_count": 3, "id": "10c657dd", "metadata": {}, "outputs": [], "source": ["feature_folder = \"/home/<USER>/git/realTime/Code/strategy/feature.csv\""]}, {"cell_type": "code", "execution_count": 4, "id": "34189368", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["feature need: len 71 for 101 factor.\n"]}], "source": ["features_table = pd.read_csv(feature_folder,index_col = 0)\n", "features_names = list(features_table['fname'])\n", "need = []\n", "for v in features_names:\n", "    for vv in expr:\n", "        if v in vv:\n", "            need.append(v) \n", "            break\n", "print('feature need: len',len(need),'for' , str(len(expr)),'factor.')"]}, {"cell_type": "code", "execution_count": 5, "id": "021e2bd3", "metadata": {}, "outputs": [{"data": {"text/plain": ["['p1_corrs0',\n", " 'p1_corrs1',\n", " 'p1_corrs2',\n", " 'p1_corrs3',\n", " 'p1_corrs5',\n", " 'p1_corrs7',\n", " 'p1_corrs8',\n", " 'p2_et0',\n", " 'p2_et1',\n", " 'p2_et2',\n", " 'p2_et4',\n", " 'p2_et5',\n", " 'p2_et7',\n", " 'p2_et8',\n", " 'p2_et9',\n", " 'p2_et10',\n", " 'p2_et11',\n", " 'p2_et12',\n", " 'p2_et13',\n", " 'p2_et14',\n", " 'p2_et15',\n", " 'p2_et16',\n", " 'p2_et17',\n", " 'p2_et18',\n", " 'p3_mf0',\n", " 'p3_mf1',\n", " 'p3_mf3',\n", " 'p3_mf4',\n", " 'p3_mf5',\n", " 'p3_mf7',\n", " 'p3_mf8',\n", " 'p3_mf9',\n", " 'p3_mf11',\n", " 'p3_mf12',\n", " 'p4_ms0',\n", " 'p4_ms1',\n", " 'p4_ms2',\n", " 'p4_ms3',\n", " 'p4_ms4',\n", " 'p4_ms5',\n", " 'p4_ms6',\n", " 'p5_to0',\n", " 'p5_to1',\n", " 'p5_to2',\n", " 'p5_to3',\n", " 'p5_to4',\n", " 'p5_to5',\n", " 'p5_to6',\n", " 'p5_to7',\n", " 'p6_tn0',\n", " 'p6_tn1',\n", " 'p6_tn2',\n", " 'p6_tn4',\n", " 'p6_tn5',\n", " 'p6_tn6',\n", " 'p6_tn7',\n", " 'p6_tn8',\n", " 'p6_tn9',\n", " 'p6_tn10',\n", " 'p6_tn11',\n", " 'p6_tn12',\n", " 'p6_tn13',\n", " 'dcperiod',\n", " 'dcphase',\n", " 'cci',\n", " 'cmo',\n", " 'dx',\n", " 'di',\n", " 'dm',\n", " 'ultosc',\n", " 'liangle']"]}, "execution_count": 5, "metadata": {}, "output_type": "execute_result"}], "source": ["need"]}], "metadata": {"kernelspec": {"display_name": "Python 3", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.10.13"}}, "nbformat": 4, "nbformat_minor": 5}