{"unoptimized": ["Abs", "Add", "And", "Ceil", "Divide", "Equal", "Exp", "FilterInf", "Floor", "IfThen", "Inv", "<PERSON><PERSON><PERSON>", "Log", "<PERSON><PERSON>", "<PERSON><PERSON>", "Max", "Min", "Minus", "Mthan", "Multiply", "Not", "Or", "Power", "Reverse", "Round", "Sign", "SignedPower", "Softsign", "Sqrt", "Tot_ArgMax", "Tot_Arg<PERSON>in", "Tot_ChgRate", "Tot_Delta", "Tot_Divide", "Tot_Max", "Tot_Mean", "To<PERSON>_<PERSON>", "Tot_Rank", "Tot_Stdev", "Tot_Sum", "UnEqual", "<PERSON><PERSON>", "fillNaN", "getInf", "get<PERSON><PERSON>", "pn_CrossFit", "pn_Cut", "pn_FillMax", "pn_FillMin", "pn_GroupNeutral", "pn_GroupNorm", "pn_GroupRank", "pn_Mean", "pn_Rank", "pn_Rank2", "pn_RankCentered", "pn_Stand", "pn_TransNorm", "pn_TransStd", "pn_Winsor", "replaceInf", "ts_ArgMax", "ts_<PERSON><PERSON><PERSON>in", "ts_ChgRate", "ts_<PERSON>rr", "ts_Cov", "ts_Decay", "ts_Decay2", "ts_<PERSON><PERSON>", "ts_<PERSON>", "ts_Divide", "ts_Entropy", "ts_<PERSON><PERSON>", "ts_<PERSON>", "ts_MaxDD", "ts_Mean", "ts_MeanChg", "ts_Median", "ts_<PERSON>", "ts_<PERSON><PERSON>_corr", "ts_Product", "ts_Quantile", "ts_Rank", "ts_Regression", "ts_Scale", "ts_Skewness", "ts_Stdev", "ts_Sum", "ts_TransNorm"], "optimized": ["Abs", "Add", "And", "Ceil", "Divide", "Equal", "Exp", "FilterInf", "Floor", "IfThen", "Inv", "<PERSON><PERSON><PERSON>", "Log", "<PERSON><PERSON>", "<PERSON><PERSON>", "Max", "Min", "Minus", "Mthan", "Multiply", "Not", "Or", "Power", "Reverse", "Round", "Sign", "SignedPower", "Softsign", "Sqrt", "Tot_ArgMax", "Tot_Arg<PERSON>in", "Tot_ChgRate", "Tot_Delta", "Tot_Divide", "Tot_Max", "Tot_Mean", "To<PERSON>_<PERSON>", "Tot_Rank", "Tot_Stdev", "Tot_Sum", "UnEqual", "<PERSON><PERSON>", "fillNaN", "getInf", "get<PERSON><PERSON>", "pn_CrossFit", "pn_Cut", "pn_FillMax", "pn_FillMin", "pn_GroupNeutral", "pn_GroupNorm", "pn_GroupRank", "pn_Mean", "pn_Rank", "pn_Rank2", "pn_RankCentered", "pn_Stand", "pn_TransNorm", "pn_TransStd", "pn_Winsor", "replaceInf", "ts_ArgMax", "ts_<PERSON><PERSON><PERSON>in", "ts_ChgRate", "ts_<PERSON>rr", "ts_Cov", "ts_Decay", "ts_Decay2", "ts_<PERSON><PERSON>", "ts_<PERSON>", "ts_Divide", "ts_Entropy", "ts_<PERSON><PERSON>", "ts_<PERSON>", "ts_MaxDD", "ts_Mean", "ts_MeanChg", "ts_Median", "ts_<PERSON>", "ts_<PERSON><PERSON>_corr", "ts_Product", "ts_Quantile", "ts_Rank", "ts_Regression", "ts_Scale", "ts_Skewness", "ts_Stdev", "ts_Sum", "ts_TransNorm"], "python": ["Abs", "Add", "And", "Ceil", "Divide", "Equal", "Exp", "<PERSON>ll<PERSON><PERSON>", "FilterInf", "Floor", "GetSingleBar", "IfThen", "<PERSON><PERSON><PERSON>", "Log", "<PERSON><PERSON>", "<PERSON><PERSON>", "Max", "Min", "Minus", "Mthan", "Multiply", "Not", "Or", "Power", "Repmat", "Reverse", "Round", "Sign", "SignedPower", "Softsign", "Sqrt", "Tot_ArgMax", "Tot_Arg<PERSON>in", "Tot_ChgRate", "Tot_Delta", "Tot_Divide", "Tot_Max", "Tot_Mean", "To<PERSON>_<PERSON>", "Tot_Rank", "Tot_Stdev", "Tot_Sum", "UnEqual", "Util_AdjForTS", "<PERSON><PERSON>", "__general_fun", "__grpTransNorm_fun", "__maxDD_fun", "_pn_Rank", "_pn_TransNorm", "_pn_ranks", "_pn_ranks2", "_repmat", "_ts_Fun", "_ts_entropy_v", "calc", "calc2", "entropy", "getInf", "get<PERSON><PERSON>", "get_dfs", "inv", "is_number", "log", "parse_expression", "pn_CrossFit", "pn_Cut", "pn_FillMax", "pn_FillMin", "pn_GroupNeutral", "pn_GroupNorm", "pn_GroupRank", "pn_Mean", "pn_Rank", "pn_Rank2", "pn_RankCentered", "pn_Stand", "pn_TransNorm", "pn_TransStd", "pn_Winsor", "ts_<PERSON><PERSON><PERSON>", "ts_<PERSON><PERSON><PERSON>", "ts_ChgRate", "ts_<PERSON>rr", "ts_Cov", "ts_Decay", "ts_Decay2", "ts_<PERSON><PERSON>", "ts_<PERSON>", "ts_Divide", "ts_Entropy", "ts_<PERSON><PERSON>", "ts_<PERSON>", "ts_MaxDD", "ts_Mean", "ts_MeanChg", "ts_Median", "ts_<PERSON>", "ts_<PERSON><PERSON>_corr", "ts_Product", "ts_Quantile", "ts_Rank", "ts_Regression", "ts_Scale", "ts_Skewness", "ts_Stdev", "ts_Sum", "ts_TransNorm"], "common": ["And", "pn_Winsor", "ts_Rank", "ts_<PERSON><PERSON>", "pn_Stand", "FilterInf", "Exp", "SignedPower", "ts_Quantile", "Add", "Reverse", "ts_<PERSON>", "ts_MaxDD", "pn_FillMax", "ts_Mean", "ts_Divide", "Sign", "UnEqual", "Minus", "ts_Entropy", "pn_Rank2", "ts_Median", "ts_ChgRate", "ts_MeanChg", "To<PERSON>_<PERSON>", "ts_<PERSON>", "<PERSON><PERSON>", "IfThen", "Equal", "get<PERSON><PERSON>", "ts_Scale", "Tot_Rank", "Multiply", "Not", "Softsign", "Tot_ArgMax", "Tot_Delta", "<PERSON><PERSON>", "<PERSON><PERSON>", "Max", "ts_<PERSON><PERSON>_corr", "ts_TransNorm", "Or", "Tot_Mean", "Tot_Max", "ts_Sum", "Log", "ts_Product", "pn_TransNorm", "pn_CrossFit", "ts_<PERSON>", "Abs", "pn_GroupNeutral", "ts_Decay", "Round", "Tot_Divide", "Sqrt", "pn_RankCentered", "Power", "Tot_ChgRate", "ts_Stdev", "pn_GroupRank", "Min", "Divide", "ts_Skewness", "ts_<PERSON><PERSON>", "Tot_Arg<PERSON>in", "Tot_Sum", "ts_Regression", "pn_Cut", "ts_Decay2", "pn_Mean", "<PERSON><PERSON><PERSON>", "getInf", "pn_FillMin", "ts_Cov", "Tot_Stdev", "pn_GroupNorm", "ts_<PERSON>rr", "Ceil", "Mthan", "pn_TransStd", "pn_Rank", "Floor"]}