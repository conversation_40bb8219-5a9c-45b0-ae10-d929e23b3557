#include "factor_calculator.h"
#include <iostream>
#include <iomanip>
#include <vector>
#include <string>
#include <cmath>
#include <random>

using namespace feature_operators;
using namespace factor_calculator;

// 打印矩阵
void printMatrix(const std::string& name, const DataFrame& matrix) {
    std::cout << name << " (" << matrix.rows() << "x" << matrix.cols() << "):" << std::endl;
    for (int i = 0; i < std::min(10, static_cast<int>(matrix.rows())); ++i) {
        for (int j = 0; j < std::min(5, static_cast<int>(matrix.cols())); ++j) {
            if (isNaN(matrix(i, j))) {
                std::cout << std::setw(10) << "NaN";
            } else {
                std::cout << std::setw(10) << std::fixed << std::setprecision(4) << matrix(i, j);
            }
        }
        std::cout << std::endl;
    }
    std::cout << std::endl;
}

// 生成模拟的K线数据
void generateKlineData(int rows, int cols,
                      DataFrame& open,
                      DataFrame& high,
                      DataFrame& low,
                      DataFrame& close,
                      DataFrame& volume,
                      DataFrame& amount,
                      DataFrame& vwap) {
    std::random_device rd;
    std::mt19937 gen(rd());
    std::uniform_real_distribution<> price_dis(100.0, 200.0);
    std::uniform_real_distribution<> vol_dis(1000.0, 10000.0);

    open = DataFrame(rows, cols);
    high = DataFrame(rows, cols);
    low = DataFrame(rows, cols);
    close = DataFrame(rows, cols);
    volume = DataFrame(rows, cols);
    amount = DataFrame(rows, cols);
    vwap = DataFrame(rows, cols);

    // 初始价格
    for (int j = 0; j < cols; ++j) {
        double base_price = price_dis(gen);
        open(0, j) = base_price;
        double h = base_price * (1.0 + 0.01 * (gen() % 100) / 100.0);
        double l = base_price * (1.0 - 0.01 * (gen() % 100) / 100.0);
        high(0, j) = h;
        low(0, j) = l;
        close(0, j) = l + (h - l) * (gen() % 100) / 100.0;
        volume(0, j) = vol_dis(gen);
        amount(0, j) = volume(0, j) * (open(0, j) + close(0, j)) / 2.0;
        vwap(0, j) = amount(0, j) / volume(0, j);
    }

    // 生成后续K线
    for (int i = 1; i < rows; ++i) {
        for (int j = 0; j < cols; ++j) {
            // 基于前一个收盘价生成开盘价
            double prev_close = close(i-1, j);
            double change_pct = 0.01 * ((gen() % 200) - 100) / 100.0; // -1% 到 1% 的变化
            open(i, j) = prev_close * (1.0 + change_pct);

            // 生成高低价
            double h = open(i, j) * (1.0 + 0.01 * (gen() % 100) / 100.0);
            double l = open(i, j) * (1.0 - 0.01 * (gen() % 100) / 100.0);
            high(i, j) = h;
            low(i, j) = l;

            // 生成收盘价
            close(i, j) = l + (h - l) * (gen() % 100) / 100.0;

            // 生成成交量和成交额
            volume(i, j) = vol_dis(gen);
            amount(i, j) = volume(i, j) * (open(i, j) + close(i, j)) / 2.0;
            vwap(i, j) = amount(i, j) / volume(i, j);
        }
    }
}

// 测试p1_corrs因子组
void testP1CorrsFactors(const DataFrame& open, const DataFrame& high, const DataFrame& low,
                       const DataFrame& close, const DataFrame& volume, const DataFrame& amount,
                       const DataFrame& vwap) {
    std::cout << "=== 测试 p1_corrs 因子组 ===" << std::endl;

    auto group = p1_corrs::get_factors();
    for (size_t i = 0; i < group.names.size(); ++i) {
        std::cout << "计算因子: " << group.names[i] << std::endl;
        DataFrame result(open.rows(), open.cols());
        group.functions[i](open, high, low, close, volume, amount, vwap, result);
        printMatrix(group.names[i], result);
    }
}

// 测试p3_mf因子组
void testP3MfFactors(const DataFrame& open, const DataFrame& high, const DataFrame& low,
                    const DataFrame& close, const DataFrame& volume, const DataFrame& amount,
                    const DataFrame& vwap) {
    std::cout << "=== 测试 p3_mf 因子组 ===" << std::endl;

    auto group = p3_mf::get_factors();
    for (size_t i = 0; i < group.names.size(); ++i) {
        std::cout << "计算因子: " << group.names[i] << std::endl;
        DataFrame result(open.rows(), open.cols());
        group.functions[i](open, high, low, close, volume, amount, vwap, result);
        printMatrix(group.names[i], result);
    }
}

// 测试p4_ms因子组
void testP4MsFactors(const DataFrame& open, const DataFrame& high, const DataFrame& low,
                    const DataFrame& close, const DataFrame& volume, const DataFrame& amount,
                    const DataFrame& vwap) {
    std::cout << "=== 测试 p4_ms 因子组 ===" << std::endl;

    auto group = p4_ms::get_factors();
    for (size_t i = 0; i < group.names.size(); ++i) {
        std::cout << "计算因子: " << group.names[i] << std::endl;
        DataFrame result(open.rows(), open.cols());
        group.functions[i](open, high, low, close, volume, amount, vwap, result);
        printMatrix(group.names[i], result);
    }
}

// 测试p2_et因子组
void testP2EtFactors(const DataFrame& open, const DataFrame& high, const DataFrame& low,
                    const DataFrame& close, const DataFrame& volume, const DataFrame& amount,
                    const DataFrame& vwap) {
    std::cout << "=== 测试 p2_et 因子组 ===" << std::endl;

    auto group = p2_et::get_factors();
    for (size_t i = 0; i < group.names.size(); ++i) {
        std::cout << "计算因子: " << group.names[i] << std::endl;
        DataFrame result(open.rows(), open.cols());
        group.functions[i](open, high, low, close, volume, amount, vwap, result);
        printMatrix(group.names[i], result);
    }
}

// 测试p5_to因子组
void testP5ToFactors(const DataFrame& open, const DataFrame& high, const DataFrame& low,
                    const DataFrame& close, const DataFrame& volume, const DataFrame& amount,
                    const DataFrame& vwap) {
    std::cout << "=== 测试 p5_to 因子组 ===" << std::endl;

    auto group = p5_to::get_factors();
    for (size_t i = 0; i < group.names.size(); ++i) {
        std::cout << "计算因子: " << group.names[i] << std::endl;
        DataFrame result(open.rows(), open.cols());
        group.functions[i](open, high, low, close, volume, amount, vwap, result);
        printMatrix(group.names[i], result);
    }
}

// 测试p6_tn因子组
void testP6TnFactors(const DataFrame& open, const DataFrame& high, const DataFrame& low,
                    const DataFrame& close, const DataFrame& volume, const DataFrame& amount,
                    const DataFrame& vwap) {
    std::cout << "=== 测试 p6_tn 因子组 ===" << std::endl;

    auto group = p6_tn::get_factors();
    for (size_t i = 0; i < group.names.size(); ++i) {
        std::cout << "计算因子: " << group.names[i] << std::endl;
        DataFrame result(open.rows(), open.cols());
        group.functions[i](open, high, low, close, volume, amount, vwap, result);
        printMatrix(group.names[i], result);
    }
}

// 测试因子计算函数
void testCalculateFactors(const DataFrame& open, const DataFrame& high, const DataFrame& low,
                         const DataFrame& close, const DataFrame& volume, const DataFrame& amount,
                         const DataFrame& vwap) {
    std::cout << "=== 测试 calculate_factors 函数 ===" << std::endl;

    std::vector<std::string> factor_names = {
        "p1_corrs0", "p3_mf0", "p4_ms0", "p5_to0", "p6_tn0"
    };

    std::map<std::string, DataFrame> results;
    calculate_factors(factor_names, open, high, low, close, volume, amount, vwap, results);

    for (const auto& pair : results) {
        printMatrix(pair.first, pair.second);
    }
}

int main() {
    std::cout << "开始测试Eigen实现的因子计算..." << std::endl;

    // 生成模拟数据
    int rows = 100; // 100个K线
    int cols = 5;   // 5个合约

    DataFrame open, high, low, close, volume, amount, vwap;
    generateKlineData(rows, cols, open, high, low, close, volume, amount, vwap);

    // 打印生成的数据
    std::cout << "生成的模拟K线数据:" << std::endl;
    printMatrix("Open", open);
    printMatrix("High", high);
    printMatrix("Low", low);
    printMatrix("Close", close);
    printMatrix("Volume", volume);
    printMatrix("Amount", amount);
    printMatrix("VWAP", vwap);

    // 测试各个因子组
    testP1CorrsFactors(open, high, low, close, volume, amount, vwap);
    testP3MfFactors(open, high, low, close, volume, amount, vwap);
    testP4MsFactors(open, high, low, close, volume, amount, vwap);
    testP2EtFactors(open, high, low, close, volume, amount, vwap);
    testP5ToFactors(open, high, low, close, volume, amount, vwap);
    testP6TnFactors(open, high, low, close, volume, amount, vwap);

    // 测试因子计算函数
    testCalculateFactors(open, high, low, close, volume, amount, vwap);

    std::cout << "测试完成！" << std::endl;
    return 0;
}
