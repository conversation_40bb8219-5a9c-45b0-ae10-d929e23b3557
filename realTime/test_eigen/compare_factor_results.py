#!/usr/bin/env python3
import os
import pandas as pd
import numpy as np
import argparse
import glob

def load_csv(filepath):
    """Load CSV file into pandas DataFrame."""
    try:
        df = pd.read_csv(filepath, index_col=0)
        return df
    except Exception as e:
        print(f"Error loading {filepath}: {e}")
        return None

def compare_dataframes(df1, df2, tolerance=1e-10):
    """
    比较两个 DataFrame 并返回它们是否在容差范围内相等。

    参数:
        df1: 第一个 DataFrame
        df2: 第二个 DataFrame
        tolerance: 浮点比较的容差

    返回:
        is_equal: 布尔值，表示 DataFrame 是否在容差范围内相等
        max_diff: DataFrame 之间的最大绝对差异
        mean_diff: DataFrame 之间的平均绝对差异
        nan_diff: 一个 DataFrame 有 NaN 而另一个没有的位置数量
    """
    if df1 is None or df2 is None:
        return False, float('inf'), float('inf'), float('inf')

    # 确保两个 DataFrame 具有相同的形状
    if df1.shape != df2.shape:
        print(f"形状不匹配: {df1.shape} vs {df2.shape}")
        return False, float('inf'), float('inf'), float('inf')

    # 转换为 numpy 数组以避免索引/列标签问题
    arr1 = df1.values
    arr2 = df2.values

    # 检查 NaN 差异
    nan_mask1 = np.isnan(arr1)
    nan_mask2 = np.isnan(arr2)
    nan_diff = np.sum(nan_mask1 != nan_mask2)

    # 检查 Inf 差异
    inf_mask1 = np.isinf(arr1)
    inf_mask2 = np.isinf(arr2)
    inf_diff = np.sum(inf_mask1 != inf_mask2)

    # 比较非 NaN、非 Inf 值
    valid_mask = ~(nan_mask1 | nan_mask2 | inf_mask1 | inf_mask2)
    if np.sum(valid_mask) == 0:
        return nan_diff == 0 and inf_diff == 0, 0, 0, nan_diff

    # 仅计算有效值的差异
    diff = np.zeros_like(arr1)
    diff[valid_mask] = np.abs(arr1[valid_mask] - arr2[valid_mask])

    max_diff = np.max(diff[valid_mask]) if np.sum(valid_mask) > 0 else 0
    mean_diff = np.mean(diff[valid_mask]) if np.sum(valid_mask) > 0 else 0

    is_equal = max_diff <= tolerance and nan_diff == 0 and inf_diff == 0

    return is_equal, max_diff, mean_diff, nan_diff

def compare_factor_results(unopt_dir, opt_dir, py_dir, factor_name, tolerance=1e-6):
    """
    Compare the results of a specific factor across all three implementations.

    Args:
        unopt_dir: Directory containing unoptimized C++ results
        opt_dir: Directory containing optimized C++ results
        py_dir: Directory containing Python results
        factor_name: Name of the factor to compare
        tolerance: Tolerance for floating point comparison

    Returns:
        dict: Comparison results
    """
    unopt_path = os.path.join(unopt_dir, f"{factor_name}.csv")
    opt_path = os.path.join(opt_dir, f"{factor_name}.csv")
    py_path = os.path.join(py_dir, f"{factor_name}.csv")

    unopt_df = load_csv(unopt_path) if os.path.exists(unopt_path) else None
    opt_df = load_csv(opt_path) if os.path.exists(opt_path) else None
    py_df = load_csv(py_path) if os.path.exists(py_path) else None

    # Check which implementations are available
    has_unopt = unopt_df is not None
    has_opt = opt_df is not None
    has_py = py_df is not None

    result = {
        "factor": factor_name,
        "has_unopt": has_unopt,
        "has_opt": has_opt,
        "has_py": has_py,
        "unopt_vs_opt": None,
        "unopt_vs_py": None,
        "opt_vs_py": None
    }

    # Compare unoptimized C++ vs optimized C++
    if has_unopt and has_opt:
        is_equal, max_diff, mean_diff, nan_diff = compare_dataframes(unopt_df, opt_df, tolerance)
        result["unopt_vs_opt"] = {
            "is_equal": is_equal,
            "max_diff": max_diff,
            "mean_diff": mean_diff,
            "nan_diff": nan_diff
        }

    # Compare unoptimized C++ vs Python
    if has_unopt and has_py:
        is_equal, max_diff, mean_diff, nan_diff = compare_dataframes(unopt_df, py_df, tolerance)
        result["unopt_vs_py"] = {
            "is_equal": is_equal,
            "max_diff": max_diff,
            "mean_diff": mean_diff,
            "nan_diff": nan_diff
        }

    # Compare optimized C++ vs Python
    if has_opt and has_py:
        is_equal, max_diff, mean_diff, nan_diff = compare_dataframes(opt_df, py_df, tolerance)
        result["opt_vs_py"] = {
            "is_equal": is_equal,
            "max_diff": max_diff,
            "mean_diff": mean_diff,
            "nan_diff": nan_diff
        }

    return result

def main():
    parser = argparse.ArgumentParser(description='比较不同实现的因子结果')
    parser.add_argument('--tolerance', type=float, default=1e-10, help='浮点比较的容差')
    parser.add_argument('--results-dir', type=str, default="/home/<USER>/git/realTime/test_eigen/test_results",
                        help='包含测试结果的目录')
    args = parser.parse_args()

    # 定义路径
    base_dir = args.results_dir
    unoptimized_dir = os.path.join(base_dir, "unoptimized")
    optimized_dir = os.path.join(base_dir, "optimized")
    python_dir = os.path.join(base_dir, "python")

    # 获取所有目录中的所有可用因子
    unopt_factors = [os.path.splitext(os.path.basename(f))[0] for f in glob.glob(os.path.join(unoptimized_dir, "*.csv"))]
    opt_factors = [os.path.splitext(os.path.basename(f))[0] for f in glob.glob(os.path.join(optimized_dir, "*.csv"))]
    py_factors = [os.path.splitext(os.path.basename(f))[0] for f in glob.glob(os.path.join(python_dir, "*.csv"))]

    # 合并所有因子
    all_factors = sorted(set(unopt_factors + opt_factors + py_factors))

    print(f"在所有实现中找到 {len(all_factors)} 个唯一因子")
    print(f"未优化 C++: {len(unopt_factors)} 个因子")
    print(f"优化 C++: {len(opt_factors)} 个因子")
    print(f"Python: {len(py_factors)} 个因子")

    # 比较每个因子
    results = []
    for factor in all_factors:
        result = compare_factor_results(unoptimized_dir, optimized_dir, python_dir, factor, args.tolerance)
        results.append(result)

    # 准备结果表
    table_data = []
    for result in results:
        factor = result["factor"]
        has_unopt = "✓" if result["has_unopt"] else "✗"
        has_opt = "✓" if result["has_opt"] else "✗"
        has_py = "✓" if result["has_py"] else "✗"

        unopt_vs_opt = "N/A"
        if result["unopt_vs_opt"]:
            if result["unopt_vs_opt"]["is_equal"]:
                unopt_vs_opt = "✓"
            else:
                unopt_vs_opt = f"✗ (最大差异: {result['unopt_vs_opt']['max_diff']:.10e})"

        unopt_vs_py = "N/A"
        if result["unopt_vs_py"]:
            if result["unopt_vs_py"]["is_equal"]:
                unopt_vs_py = "✓"
            else:
                unopt_vs_py = f"✗ (最大差异: {result['unopt_vs_py']['max_diff']:.10e})"

        opt_vs_py = "N/A"
        if result["opt_vs_py"]:
            if result["opt_vs_py"]["is_equal"]:
                opt_vs_py = "✓"
            else:
                opt_vs_py = f"✗ (最大差异: {result['opt_vs_py']['max_diff']:.10e})"

        table_data.append([
            factor, has_unopt, has_opt, has_py, unopt_vs_opt, unopt_vs_py, opt_vs_py
        ])

    # 打印结果表
    try:
        from tabulate import tabulate
        headers = ["因子", "未优化 C++", "优化 C++", "Python", "未优化 vs 优化", "未优化 vs Python", "优化 vs Python"]
        print("\n结果:")
        print(tabulate(table_data, headers=headers, tablefmt="grid"))
    except ImportError:
        headers = ["因子", "未优化 C++", "优化 C++", "Python", "未优化 vs 优化", "未优化 vs Python", "优化 vs Python"]
        print("\n结果:")
        print(" | ".join(headers))
        print("-" * 100)
        for row in table_data:
            print(" | ".join(str(cell) for cell in row))

    # 计算匹配和不匹配
    unopt_vs_opt_matches = sum(1 for r in results if r["unopt_vs_opt"] and r["unopt_vs_opt"]["is_equal"])
    unopt_vs_py_matches = sum(1 for r in results if r["unopt_vs_py"] and r["unopt_vs_py"]["is_equal"])
    opt_vs_py_matches = sum(1 for r in results if r["opt_vs_py"] and r["opt_vs_py"]["is_equal"])

    unopt_vs_opt_total = sum(1 for r in results if r["unopt_vs_opt"])
    unopt_vs_py_total = sum(1 for r in results if r["unopt_vs_py"])
    opt_vs_py_total = sum(1 for r in results if r["opt_vs_py"])

    print("\n摘要:")
    if unopt_vs_opt_total > 0:
        print(f"未优化 C++ vs 优化 C++: {unopt_vs_opt_matches}/{unopt_vs_opt_total} 个因子匹配 ({unopt_vs_opt_matches/unopt_vs_opt_total*100:.1f}%)")
    if unopt_vs_py_total > 0:
        print(f"未优化 C++ vs Python: {unopt_vs_py_matches}/{unopt_vs_py_total} 个因子匹配 ({unopt_vs_py_matches/unopt_vs_py_total*100:.1f}%)")
    if opt_vs_py_total > 0:
        print(f"优化 C++ vs Python: {opt_vs_py_matches}/{opt_vs_py_total} 个因子匹配 ({opt_vs_py_matches/opt_vs_py_total*100:.1f}%)")

    # 保存结果到文件
    with open(os.path.join(base_dir, "comparison_results.txt"), "w") as f:
        try:
            from tabulate import tabulate
            f.write(tabulate(table_data, headers=headers, tablefmt="grid"))
        except ImportError:
            f.write(" | ".join(headers) + "\n")
            f.write("-" * 100 + "\n")
            for row in table_data:
                f.write(" | ".join(str(cell) for cell in row) + "\n")

        f.write("\n\n摘要:\n")
        if unopt_vs_opt_total > 0:
            f.write(f"未优化 C++ vs 优化 C++: {unopt_vs_opt_matches}/{unopt_vs_opt_total} 个因子匹配 ({unopt_vs_opt_matches/unopt_vs_opt_total*100:.1f}%)\n")
        if unopt_vs_py_total > 0:
            f.write(f"未优化 C++ vs Python: {unopt_vs_py_matches}/{unopt_vs_py_total} 个因子匹配 ({unopt_vs_py_matches/unopt_vs_py_total*100:.1f}%)\n")
        if opt_vs_py_total > 0:
            f.write(f"优化 C++ vs Python: {opt_vs_py_matches}/{opt_vs_py_total} 个因子匹配 ({opt_vs_py_matches/opt_vs_py_total*100:.1f}%)\n")

    print(f"\n结果已保存到 {os.path.join(base_dir, 'comparison_results.txt')}")

if __name__ == "__main__":
    main()
