#include "feature_operators.h"
#include <iostream>
#include <iomanip>
#include <vector>
#include <string>
#include <cmath>
#include <random>

using namespace feature_operators;

// 打印矩阵
void printMatrix(const std::string& name, const DataFrame& matrix) {
    std::cout << name << " (" << matrix.rows() << "x" << matrix.cols() << "):" << std::endl;
    for (int i = 0; i < std::min(10, static_cast<int>(matrix.rows())); ++i) {
        for (int j = 0; j < std::min(5, static_cast<int>(matrix.cols())); ++j) {
            if (isNaN(matrix(i, j))) {
                std::cout << std::setw(10) << "NaN";
            } else {
                std::cout << std::setw(10) << std::fixed << std::setprecision(4) << matrix(i, j);
            }
        }
        std::cout << std::endl;
    }
    std::cout << std::endl;
}

// 生成随机数据
DataFrame generateRandomData(int rows, int cols) {
    std::random_device rd;
    std::mt19937 gen(rd());
    std::uniform_real_distribution<> dis(-10.0, 10.0);
    
    DataFrame data(rows, cols);
    for (int i = 0; i < rows; ++i) {
        for (int j = 0; j < cols; ++j) {
            data(i, j) = dis(gen);
        }
    }
    return data;
}

// 测试基本算术运算
void testBasicOperations() {
    std::cout << "=== 测试基本算术运算 ===" << std::endl;
    
    DataFrame a = generateRandomData(10, 5);
    DataFrame b = generateRandomData(10, 5);
    
    printMatrix("A", a);
    printMatrix("B", b);
    
    printMatrix("A + B", Add(a, b));
    printMatrix("A - B", Minus(a, b));
    printMatrix("A * B", Multiply(a, b));
    printMatrix("A / B", Divide(a, b));
    printMatrix("Sqrt(A)", Sqrt(Abs(a)));
    printMatrix("Log(|A|)", Log(Abs(a)));
    printMatrix("Abs(A)", Abs(a));
    printMatrix("Sign(A)", Sign(a));
}

// 测试条件运算
void testConditionalOperations() {
    std::cout << "=== 测试条件运算 ===" << std::endl;
    
    DataFrame a = generateRandomData(10, 5);
    DataFrame b = generateRandomData(10, 5);
    
    printMatrix("A", a);
    printMatrix("B", b);
    
    printMatrix("IfThen(A > 0, A, B)", IfThen(a, a, b));
    printMatrix("IfThen(A > 0, 1, 0)", IfThen(a, 1.0, 0.0));
    printMatrix("A > B", MEthan(a, b));
    printMatrix("A < B", Lthan(a, b));
    printMatrix("A == B", Equal(a, b));
}

// 测试时间序列运算
void testTimeSeriesOperations() {
    std::cout << "=== 测试时间序列运算 ===" << std::endl;
    
    int rows = 20;
    int cols = 5;
    
    // 创建一个简单的时间序列数据
    DataFrame data(rows, cols);
    for (int i = 0; i < rows; ++i) {
        for (int j = 0; j < cols; ++j) {
            // 简单的正弦波 + 线性趋势
            data(i, j) = 10.0 * std::sin(i * 0.5) + i * 0.5 + j;
        }
    }
    
    printMatrix("原始数据", data);
    printMatrix("ts_Delay(data, 2)", ts_Delay(data, 2));
    printMatrix("ts_Mean(data, 5)", ts_Mean(data, 5));
    printMatrix("ts_Sum(data, 5)", ts_Sum(data, 5));
    printMatrix("ts_Stdev(data, 5)", ts_Stdev(data, 5));
    
    // 创建另一个时间序列进行相关性测试
    DataFrame data2(rows, cols);
    for (int i = 0; i < rows; ++i) {
        for (int j = 0; j < cols; ++j) {
            // 余弦波 + 线性趋势
            data2(i, j) = 10.0 * std::cos(i * 0.5) + i * 0.5 + j;
        }
    }
    
    printMatrix("另一个数据", data2);
    printMatrix("ts_Corr(data, data2, 10)", ts_Corr(data, data2, 10));
    printMatrix("ts_Cov(data, data2, 10)", ts_Cov(data, data2, 10));
    printMatrix("ts_Delta(data, 1)", ts_Delta(data, 1));
}

// 测试横截面运算
void testCrossSectionalOperations() {
    std::cout << "=== 测试横截面运算 ===" << std::endl;
    
    DataFrame data = generateRandomData(10, 5);
    printMatrix("原始数据", data);
    printMatrix("pn_Mean(data)", pn_Mean(data));
    printMatrix("pn_Rank(data)", pn_Rank(data));
    printMatrix("pn_TransNorm(data)", pn_TransNorm(data));
}

// 测试Tot系列函数
void testTotFunctions() {
    std::cout << "=== 测试Tot系列函数 ===" << std::endl;
    
    int rows = 20;
    int cols = 5;
    
    // 创建一个简单的时间序列数据
    DataFrame data(rows, cols);
    for (int i = 0; i < rows; ++i) {
        for (int j = 0; j < cols; ++j) {
            // 简单的正弦波 + 线性趋势
            data(i, j) = 10.0 * std::sin(i * 0.5) + i * 0.5 + j;
        }
    }
    
    printMatrix("原始数据", data);
    printMatrix("Tot_Mean(data)", Tot_Mean(data));
    printMatrix("Tot_Sum(data)", Tot_Sum(data));
    printMatrix("Tot_Stdev(data)", Tot_Stdev(data));
    printMatrix("Tot_Rank(data)", Tot_Rank(data));
    printMatrix("Tot_ArgMax(data)", Tot_ArgMax(data));
    printMatrix("Tot_ArgMin(data)", Tot_ArgMin(data));
}

int main() {
    std::cout << "开始测试Eigen实现的因子计算算子..." << std::endl;
    
    testBasicOperations();
    testConditionalOperations();
    testTimeSeriesOperations();
    testCrossSectionalOperations();
    testTotFunctions();
    
    std::cout << "测试完成！" << std::endl;
    return 0;
}
