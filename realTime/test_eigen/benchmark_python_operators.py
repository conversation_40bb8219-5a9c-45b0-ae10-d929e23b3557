#!/usr/bin/env python3
# -*- coding: utf-8 -*-

import sys
import os
import numpy as np
import pandas as pd
import time
import json
import tempfile
from datetime import datetime

# 添加Python因子计算路径
sys.path.append('/home/<USER>/git/realTime/Code/strategy')

# 导入Python因子计算模块
import feature_operator_funcs as fof

def generate_test_data(rows=500, cols=5, seed=42):
    """生成测试数据"""
    np.random.seed(seed)

    # 初始价格
    base_prices = np.random.uniform(100, 200, cols)

    # 创建数据框
    open_data = np.zeros((rows, cols))
    high_data = np.zeros((rows, cols))
    low_data = np.zeros((rows, cols))
    close_data = np.zeros((rows, cols))
    volume_data = np.zeros((rows, cols))
    amount_data = np.zeros((rows, cols))
    vwap_data = np.zeros((rows, cols))

    # 设置初始值
    for j in range(cols):
        open_data[0, j] = base_prices[j]
        h = base_prices[j] * (1.0 + 0.01 * np.random.rand())
        l = base_prices[j] * (1.0 - 0.01 * np.random.rand())
        high_data[0, j] = h
        low_data[0, j] = l
        close_data[0, j] = l + (h - l) * np.random.rand()
        volume_data[0, j] = np.random.uniform(1000, 10000)
        amount_data[0, j] = volume_data[0, j] * (open_data[0, j] + close_data[0, j]) / 2.0
        vwap_data[0, j] = amount_data[0, j] / volume_data[0, j]

    # 生成后续K线
    for i in range(1, rows):
        for j in range(cols):
            prev_close = close_data[i-1, j]
            change_pct = 0.01 * (np.random.rand() * 2 - 1)  # -1% 到 1% 的变化
            open_data[i, j] = prev_close * (1.0 + change_pct)

            h = open_data[i, j] * (1.0 + 0.01 * np.random.rand())
            l = open_data[i, j] * (1.0 - 0.01 * np.random.rand())
            high_data[i, j] = h
            low_data[i, j] = l
            close_data[i, j] = l + (h - l) * np.random.rand()

            volume_data[i, j] = np.random.uniform(1000, 10000)
            amount_data[i, j] = volume_data[i, j] * (open_data[i, j] + close_data[i, j]) / 2.0
            vwap_data[i, j] = amount_data[i, j] / volume_data[i, j]

    # 创建时间索引
    dates = pd.date_range(start='2023-01-01', periods=rows, freq='15min')

    # 创建合约列名
    contracts = [f'contract_{i}' for i in range(cols)]

    # 创建DataFrame
    open_df = pd.DataFrame(open_data, index=dates, columns=contracts)
    high_df = pd.DataFrame(high_data, index=dates, columns=contracts)
    low_df = pd.DataFrame(low_data, index=dates, columns=contracts)
    close_df = pd.DataFrame(close_data, index=dates, columns=contracts)
    volume_df = pd.DataFrame(volume_data, index=dates, columns=contracts)
    amount_df = pd.DataFrame(amount_data, index=dates, columns=contracts)
    vwap_df = pd.DataFrame(vwap_data, index=dates, columns=contracts)

    return open_df, high_df, low_df, close_df, volume_df, amount_df, vwap_df

def save_data_to_csv(data_dict, output_dir):
    """保存数据到CSV文件"""
    os.makedirs(output_dir, exist_ok=True)

    for name, df in data_dict.items():
        df.to_csv(os.path.join(output_dir, f"{name}.csv"))

    print(f"数据已保存到 {output_dir}")

def measure_python_operator_time(open_df, high_df, low_df, close_df, volume_df, amount_df, vwap_df, output_file, iterations=1000):
    """测量Python算子的执行时间"""
    # 创建数据字典
    data_dict = {
        'open': open_df,
        'high': high_df,
        'low': low_df,
        'close': close_df,
        'volume': volume_df,
        'amount': amount_df,
        'vwap': vwap_df
    }

    # 定义要测试的算子
    operators = {
        # 基本算术运算
        'add': lambda: fof.Add(data_dict['close'], data_dict['open']),
        'minus': lambda: fof.Minus(data_dict['close'], data_dict['open']),
        'multiply': lambda: fof.Multiply(data_dict['close'], data_dict['volume']),
        'divide': lambda: fof.Divide(data_dict['close'], data_dict['open']),
        'sqrt': lambda: fof.Sqrt(data_dict['volume']),
        'log': lambda: fof.Log(data_dict['volume']),
        'abs': lambda: fof.Abs(fof.Minus(data_dict['close'], data_dict['open'])),
        'max': lambda: fof.Max(data_dict['close'], data_dict['open']),
        'min': lambda: fof.Min(data_dict['close'], data_dict['open']),

        # 逻辑运算
        'and': lambda: fof.And(data_dict['close'], data_dict['open']),
        'or': lambda: fof.Or(data_dict['close'], data_dict['open']),
        'not': lambda: fof.Not(data_dict['close']),
        'xor': lambda: fof.Xor(data_dict['close'], data_dict['open']),

        # 时间序列运算
        'ts_delay': lambda: fof.ts_Delay(data_dict['close'], 1),
        'ts_mean': lambda: fof.ts_Mean(data_dict['close'], 10),
        'ts_sum': lambda: fof.ts_Sum(data_dict['volume'], 10),
        'ts_stdev': lambda: fof.ts_Stdev(data_dict['close'], 10),
        'ts_corr': lambda: fof.ts_Corr(data_dict['close'], data_dict['volume'], 10),
        'ts_min': lambda: fof.ts_Min(data_dict['close'], 10),
        'ts_max': lambda: fof.ts_Max(data_dict['close'], 10),
        'ts_delta': lambda: fof.ts_Delta(data_dict['close'], 10),
        'ts_divide': lambda: fof.ts_Divide(data_dict['close'], 10),
        'ts_chgrate': lambda: fof.ts_ChgRate(data_dict['close'], 10),
        'ts_median': lambda: fof.ts_Median(data_dict['close'], 10),
        'ts_skewness': lambda: fof.ts_Skewness(data_dict['close'], 10),
        'ts_kurtosis': lambda: fof.ts_Kurtosis(data_dict['close'], 10),
        'ts_scale': lambda: fof.ts_Scale(data_dict['close'], 10),
        'ts_product': lambda: fof.ts_Product(data_dict['close'], 10),
        'ts_transnorm': lambda: fof.ts_TransNorm(data_dict['close'], 10),
        'ts_decay': lambda: fof.ts_Decay(data_dict['close'], 10),
        'ts_decay2': lambda: fof.ts_Decay2(data_dict['close'], 10),
        'ts_partial_corr': lambda: fof.ts_Partial_corr(data_dict['close'], data_dict['open'], data_dict['high'], 10),
    }

    # 创建分组标签
    group_labels = pd.DataFrame(np.zeros(data_dict['close'].shape, dtype=int),
                               index=data_dict['close'].index,
                               columns=data_dict['close'].columns)
    # 按列索引模3分组
    for j in range(group_labels.shape[1]):
        group_labels.iloc[:, j] = j % 3

    # 添加面板运算
    operators.update({
        'pn_mean': lambda: fof.pn_Mean(data_dict['close']),
        'pn_rank': lambda: fof.pn_Rank(data_dict['close']),
        'pn_transnorm': lambda: fof.pn_TransNorm(data_dict['close']),
        'pn_stand': lambda: fof.pn_Stand(data_dict['close']),
        'pn_winsor': lambda: fof.pn_Winsor(data_dict['close'], 3),
        'pn_cut': lambda: fof.pn_Cut(data_dict['close']),
        'pn_grouprank': lambda: fof.pn_GroupRank(data_dict['close'], group_labels),
        'pn_groupnorm': lambda: fof.pn_GroupNorm(data_dict['close'], group_labels),
        'pn_groupneutral': lambda: fof.pn_GroupNeutral(data_dict['close'], group_labels),
    })

    # 测量每个算子的执行时间
    python_times = {}

    print("Python算子执行时间 (微秒):")
    print("=" * 50)
    print(f"{'算子名称':<20} {'执行时间(微秒)':<15}")
    print("-" * 50)

    for op_name, op_func in operators.items():
        # 预热
        result = op_func()
        # 确保结果被计算但不被优化掉
        if isinstance(result, pd.DataFrame):
            # 使用结果但不存储，防止编译器优化
            dummy = result.iloc[0, 0] if not result.empty else 0

        # 测量总时间
        start_time = time.time()
        for _ in range(iterations):
            result = op_func()  # 计算结果但不存储
            # 确保结果被计算但不被优化掉
            if isinstance(result, pd.DataFrame):
                # 使用结果但不存储，防止编译器优化
                dummy = result.iloc[0, 0] if not result.empty else 0
        end_time = time.time()

        # 计算平均时间
        total_time = (end_time - start_time) * 1e6  # 转换为微秒
        avg_time = total_time / iterations
        python_times[op_name] = avg_time

        # 打印结果
        print(f"{op_name:<20} {avg_time:.3f}")

    # 保存结果到JSON文件
    os.makedirs(os.path.dirname(output_file), exist_ok=True)
    with open(output_file, 'w') as f:
        json.dump(python_times, f)

    print(f"\n结果已保存到: {output_file}")

    return python_times

def main():
    # 解析命令行参数
    import argparse
    parser = argparse.ArgumentParser(description='测量Python算子的执行时间')
    parser.add_argument('--data-dir', type=str, default='/tmp/tmpdir/data', help='数据目录')
    parser.add_argument('--output-file', type=str, default='/tmp/tmpdir/python_results.json', help='输出文件')
    parser.add_argument('--iterations', type=int, default=1000, help='迭代次数')
    args = parser.parse_args()

    # 确保数据目录存在
    os.makedirs(args.data_dir, exist_ok=True)

    # 生成测试数据
    print("生成测试数据...")
    open_df, high_df, low_df, close_df, volume_df, amount_df, vwap_df = generate_test_data(rows=500, cols=518)

    # 保存数据到CSV
    data_dict = {
        'open': open_df,
        'high': high_df,
        'low': low_df,
        'close': close_df,
        'volume': volume_df,
        'amount': amount_df,
        'vwap': vwap_df
    }
    save_data_to_csv(data_dict, args.data_dir)

    # 测量Python算子执行时间
    print(f"\n测量Python算子执行时间 (迭代次数: {args.iterations})...")
    measure_python_operator_time(
        open_df, high_df, low_df, close_df, volume_df, amount_df, vwap_df,
        args.output_file, iterations=args.iterations
    )

if __name__ == "__main__":
    main()
