#!/bin/bash

# Set the working directory
cd /home/<USER>/git/realTime/test_eigen

# Create output directories
mkdir -p test_results/unoptimized
mkdir -p test_results/optimized
mkdir -p test_results/python

echo "Compiling unoptimized operator test..."
g++ -std=c++17 -O3 test_unoptimized_operators.cpp feature_operators.cpp -o test_unoptimized_operators -I/usr/local/include/eigen3 -I/usr/include/eigen3

echo "Compiling optimized operator test..."
g++ -std=c++17 -O3 test_optimized_operators_results.cpp optimized_operators.cpp -o test_optimized_operators_results -I/usr/local/include/eigen3 -I/usr/include/eigen3

echo "Running unoptimized operator test..."
./test_unoptimized_operators > test_results/unoptimized_log.txt 2>&1

echo "Running optimized operator test..."
./test_optimized_operators_results > test_results/optimized_log.txt 2>&1

echo "Running Python operator test..."
# Activate the virtual environment
source /home/<USER>/freqtrade/.venv/bin/activate
# Install tabulate if needed
# pip install tabulate
python3 test_python_operators.py > test_results/python_log.txt 2>&1
deactivate

echo "Extracting operators from header files..."
python3 extract_operators.py > test_results/operators_log.txt 2>&1

echo "比较结果..."
source /home/<USER>/freqtrade/.venv/bin/activate
python3 simple_compare.py > test_results/comparison_log.txt 2>&1
echo "比较结果已保存到 test_results/comparison_log.txt"
echo "检查 test_results/comparison_results.txt 获取详细比较结果。"

# 打印 NaN 不一致的因子
echo "NaN 不一致的因子:"
grep -A 30 "NaN 不一致的因子" test_results/comparison_results.txt | grep -v "^$" | head -n 10

deactivate

echo "All tests completed. Results are in the test_results directory."
echo "Check test_results/comparison_results.txt for detailed comparison."

# Print a summary of the comparison
cat test_results/comparison_results.txt | tail -4
