cmake_minimum_required(VERSION 3.10)
project(feature_operators)

# 设置C++标准
set(CMAKE_CXX_STANDARD 17)
set(CMAKE_CXX_STANDARD_REQUIRED ON)
set(CMAKE_EXPORT_COMPILE_COMMANDS ON)

# 查找Eigen库
find_package(Eigen3 REQUIRED)
include_directories(${EIGEN3_INCLUDE_DIR})

# 添加库文件
add_library(feature_operators SHARED feature_operators.cpp)
add_library(optimized_operators SHARED optimized_operators.cpp)
add_library(factor_calculator SHARED factor_calculator.cpp)

# 添加可执行文件
add_executable(test_operators test_operators.cpp feature_operators.cpp)
add_executable(test_factors test_factors.cpp factor_calculator.cpp feature_operators.cpp)
add_executable(test_factors_csv test_factors_csv.cpp factor_calculator.cpp feature_operators.cpp)
add_executable(test_operators_csv test_operators_csv.cpp feature_operators.cpp)
add_executable(test_optimized_operators test_optimized_operators.cpp optimized_operators.cpp)
add_executable(benchmark_operators benchmark_operators.cpp)
add_executable(benchmark_optimized_operators benchmark_optimized_operators.cpp)

# 链接库
target_link_libraries(test_factors feature_operators factor_calculator)
target_link_libraries(test_factors_csv feature_operators factor_calculator)
target_link_libraries(test_operators_csv feature_operators)
target_link_libraries(test_optimized_operators optimized_operators)
target_link_libraries(benchmark_operators feature_operators)
target_link_libraries(benchmark_optimized_operators optimized_operators)

# 添加C++17支持
set_target_properties(test_factors_csv PROPERTIES
    CXX_STANDARD 17
    CXX_STANDARD_REQUIRED ON
)
set_target_properties(test_operators_csv PROPERTIES
    CXX_STANDARD 17
    CXX_STANDARD_REQUIRED ON
)
set_target_properties(test_optimized_operators PROPERTIES
    CXX_STANDARD 17
    CXX_STANDARD_REQUIRED ON
)
set_target_properties(benchmark_operators PROPERTIES
    CXX_STANDARD 17
    CXX_STANDARD_REQUIRED ON
)
set_target_properties(benchmark_optimized_operators PROPERTIES
    CXX_STANDARD 17
    CXX_STANDARD_REQUIRED ON
)

# 设置优化选项
set(CMAKE_CXX_FLAGS "${CMAKE_CXX_FLAGS} -O3")

# 安装目标
install(TARGETS feature_operators optimized_operators factor_calculator DESTINATION lib)
install(FILES feature_operators.h optimized_operators.h factor_calculator.h DESTINATION include)
