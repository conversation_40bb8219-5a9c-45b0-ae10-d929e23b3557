#!/bin/bash

# 设置工作目录
WORK_DIR="/tmp/operator_benchmark"
DATA_DIR="$WORK_DIR/data"
RESULTS_DIR="$WORK_DIR/results"
LOG_FILE="$WORK_DIR/performance_comparison.log"

# 创建目录
mkdir -p "$DATA_DIR"
mkdir -p "$RESULTS_DIR"

# 清空日志文件
echo "性能对比测试 - $(date)" > "$LOG_FILE"
echo "=======================================" >> "$LOG_FILE"

# 编译C++基准测试程序
echo "编译C++基准测试程序..." | tee -a "$LOG_FILE"
cd /home/<USER>/git/realTime/test_eigen/build
cmake .. && make -j4

if [ $? -ne 0 ]; then
    echo "编译失败，退出测试" | tee -a "$LOG_FILE"
    exit 1
fi

# 运行Python基准测试
echo "运行Python基准测试..." | tee -a "$LOG_FILE"
cd /home/<USER>/git/realTime/test_eigen
source /home/<USER>/freqtrade/.venv/bin/activate
python benchmark_python_operators.py --data-dir "$DATA_DIR" --output-file "$RESULTS_DIR/python_results.json" --iterations 100 2>&1 | tee -a "$LOG_FILE"

# 运行原始C++基准测试
echo "运行原始C++基准测试..." | tee -a "$LOG_FILE"
cd /home/<USER>/git/realTime/test_eigen/build
./benchmark_operators "$DATA_DIR" "$RESULTS_DIR/cpp_results.json" 100 2>&1 | tee -a "$LOG_FILE"

# 运行优化C++基准测试
echo "运行优化C++基准测试..." | tee -a "$LOG_FILE"
cd /home/<USER>/git/realTime/test_eigen/build
./benchmark_optimized_operators "$DATA_DIR" "$RESULTS_DIR/optimized_results.json" 100 2>&1 | tee -a "$LOG_FILE"

# 分析结果
echo "分析结果..." | tee -a "$LOG_FILE"
cd /home/<USER>/git/realTime/test_eigen
python - <<EOF >> "$LOG_FILE"
import json
import numpy as np
import os

# 加载结果
results_dir = "$RESULTS_DIR"
python_file = os.path.join(results_dir, "python_results.json")
cpp_file = os.path.join(results_dir, "cpp_results.json")
optimized_file = os.path.join(results_dir, "optimized_results.json")

try:
    with open(python_file, 'r') as f:
        python_times = json.load(f)
except Exception as e:
    print(f"无法加载Python结果: {e}")
    python_times = {}

try:
    with open(cpp_file, 'r') as f:
        cpp_times = json.load(f)
except Exception as e:
    print(f"无法加载C++结果: {e}")
    cpp_times = {}

try:
    with open(optimized_file, 'r') as f:
        optimized_times = json.load(f)
except Exception as e:
    print(f"无法加载优化C++结果: {e}")
    optimized_times = {}

# 计算总时间
python_total = sum(python_times.values())
cpp_total = sum(cpp_times.values())
optimized_total = sum(optimized_times.values())

print("\n性能对比总结:")
print("=" * 50)
print(f"Python总耗时: {python_total:.3f} 微秒")
print(f"原始C++总耗时: {cpp_total:.3f} 微秒")
print(f"优化C++总耗时: {optimized_total:.3f} 微秒")

if cpp_total > 0 and optimized_total > 0:
    python_to_cpp = python_total / cpp_total
    python_to_optimized = python_total / optimized_total
    cpp_to_optimized = cpp_total / optimized_total
    
    print(f"\nPython vs 原始C++: Python慢 {python_to_cpp:.2f}x")
    print(f"Python vs 优化C++: Python慢 {python_to_optimized:.2f}x")
    print(f"原始C++ vs 优化C++: 优化版快 {cpp_to_optimized:.2f}x")

# 对比每个算子的性能
print("\n各算子性能对比 (微秒):")
print("=" * 100)
print(f"{'算子名称':<20} {'Python':<15} {'原始C++':<15} {'优化C++':<15} {'Python/原始':<10} {'Python/优化':<10} {'原始/优化':<10}")
print("-" * 100)

# 获取所有算子名称
all_ops = set(python_times.keys()) | set(cpp_times.keys()) | set(optimized_times.keys())

for op in sorted(all_ops):
    py_time = python_times.get(op, float('nan'))
    cpp_time = cpp_times.get(op, float('nan'))
    opt_time = optimized_times.get(op, float('nan'))
    
    py_cpp_ratio = py_time / cpp_time if not np.isnan(py_time) and not np.isnan(cpp_time) and cpp_time > 0 else float('nan')
    py_opt_ratio = py_time / opt_time if not np.isnan(py_time) and not np.isnan(opt_time) and opt_time > 0 else float('nan')
    cpp_opt_ratio = cpp_time / opt_time if not np.isnan(cpp_time) and not np.isnan(opt_time) and opt_time > 0 else float('nan')
    
    print(f"{op:<20} {py_time:<15.3f} {cpp_time:<15.3f} {opt_time:<15.3f} {py_cpp_ratio:<10.2f} {py_opt_ratio:<10.2f} {cpp_opt_ratio:<10.2f}")
EOF

echo "性能测试完成，结果已保存到 $LOG_FILE"
echo "查看结果: cat $LOG_FILE"
