#include "optimized_operators.h"
#include <cmath>
#include <algorithm>
#include <numeric>
#include <limits>
#include <iostream>
#include <deque>
#include <vector>
#include <Eigen/Core>
#include <Eigen/Dense>

namespace optimized_operators {

// 辅助函数
bool isNaN(double value) {
    return std::isnan(value);
}

bool isInf(double value) {
    return std::isinf(value);
}

// 使用Eigen的向量化操作填充NaN值
DataFrame fillNaN(const DataFrame& data, double value) {
    DataFrame result = data;
    // 使用Eigen的数组操作进行条件替换
    result = (result.array().isNaN()).select(value, result);
    return result;
}

// 使用Eigen的向量化操作替换无穷大值
DataFrame replaceInf(const DataFrame& data, double value) {
    DataFrame result = data;
    // 使用Eigen的数组操作进行条件替换
    result = (result.array() == std::numeric_limits<double>::infinity() ||
              result.array() == -std::numeric_limits<double>::infinity()).select(value, result);
    return result;
}

// 基本算术运算
DataFrame Add(const DataFrame& s1, const DataFrame& s2) {
    // 使用延迟求值，避免创建临时矩阵
    return (s1 + s2).eval();
}

DataFrame Minus(const DataFrame& s1, const DataFrame& s2) {
    // 使用延迟求值，避免创建临时矩阵
    return (s1 - s2).eval();
}

DataFrame Multiply(const DataFrame& s1, const DataFrame& s2) {
    // 使用延迟求值，避免创建临时矩阵
    return s1.cwiseProduct(s2).eval();
}

// 优化的除法操作，使用Eigen的数组操作处理除零情况
DataFrame Divide(const DataFrame& s1, const DataFrame& s2) {
    const int rows = s1.rows();
    const int cols = s1.cols();

    // 创建一个掩码，标记除数接近零或为NaN的位置
    Eigen::Array<bool, Eigen::Dynamic, Eigen::Dynamic> mask =
        (s2.array().abs() < 1e-10) || s2.array().isNaN();

    // 使用表达式模板和延迟求值
    // 创建结果矩阵
    DataFrame result(rows, cols);

    // 使用noalias()避免临时拷贝
    result.noalias() = mask.select(
        DataFrame::Constant(rows, cols, std::numeric_limits<double>::quiet_NaN()),
        s1.cwiseQuotient(s2)
    );

    return result;
}

DataFrame Sqrt(const DataFrame& s1) {
    const int rows = s1.rows();
    const int cols = s1.cols();

    // 创建掩码，标记负数或NaN的位置
    Eigen::Array<bool, Eigen::Dynamic, Eigen::Dynamic> mask =
        (s1.array() < 0) || s1.array().isNaN();

    // 使用表达式模板和延迟求值
    DataFrame result(rows, cols);

    // 使用noalias()避免临时拷贝
    result.noalias() = mask.select(
        DataFrame::Constant(rows, cols, std::numeric_limits<double>::quiet_NaN()),
        s1.cwiseSqrt()
    );

    return result;
}

DataFrame Log(const DataFrame& s1) {
    const int rows = s1.rows();
    const int cols = s1.cols();

    // 创建掩码，标记小于等于0或NaN的位置
    Eigen::Array<bool, Eigen::Dynamic, Eigen::Dynamic> mask =
        (s1.array() <= 0) || s1.array().isNaN();

    // 使用表达式模板和延迟求值
    DataFrame result(rows, cols);

    // 使用noalias()避免临时拷贝
    result.noalias() = mask.select(
        DataFrame::Constant(rows, cols, std::numeric_limits<double>::quiet_NaN()),
        s1.array().log().matrix()
    );

    return result;
}

DataFrame Inv(const DataFrame& s1) {
    const int rows = s1.rows();
    const int cols = s1.cols();

    // 创建掩码，标记接近零或NaN的位置
    Eigen::Array<bool, Eigen::Dynamic, Eigen::Dynamic> mask =
        (s1.array().abs() < 1e-10) || s1.array().isNaN();

    // 使用表达式模板和延迟求值
    DataFrame result(rows, cols);

    // 使用noalias()避免临时拷贝
    result.noalias() = mask.select(
        DataFrame::Constant(rows, cols, std::numeric_limits<double>::quiet_NaN()),
        s1.array().inverse().matrix()
    );

    return result;
}

DataFrame Power(const DataFrame& s1, int n) {
    // 使用延迟求值，避免创建临时矩阵
    return s1.array().pow(n).matrix().eval();
}

DataFrame Power(const DataFrame& s1, double n) {
    const int rows = s1.rows();
    const int cols = s1.cols();

    // 检查是否为整数幂
    bool isIntegerPower = std::abs(n - std::round(n)) < 1e-10;

    if (isIntegerPower) {
        // 整数幂可以直接使用Eigen的pow函数
        return s1.array().pow(n).matrix().eval();
    } else {
        // 非整数幂需要处理负数底数的情况
        // 创建掩码，标记负数或NaN的位置
        Eigen::Array<bool, Eigen::Dynamic, Eigen::Dynamic> mask =
            (s1.array() < 0) || s1.array().isNaN();

        // 使用表达式模板和延迟求值
        DataFrame result(rows, cols);

        // 对于非负数，使用Eigen的pow函数
        // 对于负数和NaN，使用NaN
        result.noalias() = mask.select(
            DataFrame::Constant(rows, cols, std::numeric_limits<double>::quiet_NaN()),
            s1.array().pow(n).matrix()
        );

        return result;
    }
}

DataFrame Abs(const DataFrame& s1) {
    // 使用延迟求值，避免创建临时矩阵
    return s1.cwiseAbs().eval();
}

DataFrame Sign(const DataFrame& s1) {
    // 使用延迟求值，避免创建临时矩阵
    return s1.cwiseSign().eval();
}

DataFrame Exp(const DataFrame& s1) {
    // 使用延迟求值，避免创建临时矩阵
    return s1.array().exp().matrix().eval();
}

DataFrame Reverse(const DataFrame& s1) {
    const int rows = s1.rows();
    const int cols = s1.cols();

    // 创建掩码，标记接近零或NaN的位置
    Eigen::Array<bool, Eigen::Dynamic, Eigen::Dynamic> mask =
        (s1.array().abs() < 1e-10) || s1.array().isNaN();

    // 使用表达式模板和延迟求值
    DataFrame result(rows, cols);

    // 使用noalias()避免临时拷贝
    result.noalias() = mask.select(
        DataFrame::Constant(rows, cols, std::numeric_limits<double>::quiet_NaN()),
        -s1
    );

    return result;
}

DataFrame Ceil(const DataFrame& s1) {
    // 使用延迟求值，避免创建临时矩阵
    return s1.array().ceil().matrix().eval();
}

DataFrame Floor(const DataFrame& s1) {
    // 使用延迟求值，避免创建临时矩阵
    return s1.array().floor().matrix().eval();
}

DataFrame Round(const DataFrame& s1) {
    // 使用延迟求值，避免创建临时矩阵
    return s1.array().round().matrix().eval();
}

DataFrame SignedPower(const DataFrame& s1, double n) {
    const int rows = s1.rows();
    const int cols = s1.cols();

    // 使用表达式模板和延迟求值
    DataFrame result(rows, cols);

    // 创建掩码，标记NaN的位置
    Eigen::Array<bool, Eigen::Dynamic, Eigen::Dynamic> nan_mask = s1.array().isNaN();

    // 创建符号数组
    Eigen::Array<double, Eigen::Dynamic, Eigen::Dynamic> sign_array(rows, cols);
    for (int i = 0; i < rows; ++i) {
        for (int j = 0; j < cols; ++j) {
            sign_array(i, j) = (s1(i, j) >= 0) ? 1.0 : -1.0;
        }
    }

    // 使用noalias()避免临时拷贝
    result.noalias() = nan_mask.select(
        DataFrame::Constant(rows, cols, std::numeric_limits<double>::quiet_NaN()),
        (sign_array * s1.array().abs().pow(n)).matrix()
    );

    return result;
}

DataFrame Softsign(const DataFrame& s1) {
    // x / (1 + |x|)
    // 使用延迟求值，避免创建临时矩阵
    return (s1.array() / (1.0 + s1.array().abs())).matrix().eval();
}

// 打印函数
void printDataFrame(const DataFrame &df, int precision, int width,
                    bool scientific, bool showSize,
                    const std::string &name) {
    // 保存原始格式状态
    std::ios::fmtflags old_flags = std::cout.flags();
    std::streamsize old_precision = std::cout.precision();

    // 设置输出格式
    std::cout << std::setprecision(precision);
    std::cout << std::setw(width);

    if (scientific) {
        std::cout << std::scientific;
    } else {
        std::cout << std::fixed;
    }

    // 打印名称和尺寸（如果启用）
    if (!name.empty()) {
        std::cout << name << " = ";
    }

    if (showSize) {
        std::cout << "[" << df.rows() << "x" << df.cols() << "]" << std::endl;
    }

    // 打印矩阵内容
    std::cout << df << std::endl;

    // 恢复原始格式状态
    std::cout.flags(old_flags);
    std::cout.precision(old_precision);
}

// 条件运算
DataFrame IfThen(const DataFrame& condition, const DataFrame& value_true, const DataFrame& value_false) {
    const int rows = condition.rows();
    const int cols = condition.cols();

    // 创建掩码，标记条件为真和NaN的位置
    Eigen::Array<bool, Eigen::Dynamic, Eigen::Dynamic> true_mask = (condition.array() > 0);
    Eigen::Array<bool, Eigen::Dynamic, Eigen::Dynamic> nan_mask = condition.array().isNaN();

    // 使用表达式模板和延迟求值
    DataFrame result(rows, cols);

    // 使用noalias()避免临时拷贝
    // 先根据条件选择结果，再处理NaN情况
    result.noalias() = nan_mask.select(
        DataFrame::Constant(rows, cols, std::numeric_limits<double>::quiet_NaN()),
        true_mask.select(value_true, value_false)
    );

    return result;
}

DataFrame IfThen(const DataFrame& condition, double value_true, const DataFrame& value_false) {
    const int rows = condition.rows();
    const int cols = condition.cols();

    // 创建掩码，标记条件为真和NaN的位置
    Eigen::Array<bool, Eigen::Dynamic, Eigen::Dynamic> true_mask = (condition.array() > 0);
    Eigen::Array<bool, Eigen::Dynamic, Eigen::Dynamic> nan_mask = condition.array().isNaN();

    // 使用表达式模板和延迟求值
    DataFrame result(rows, cols);

    // 使用noalias()避免临时拷贝
    // 先根据条件选择结果，再处理NaN情况
    result.noalias() = nan_mask.select(
        DataFrame::Constant(rows, cols, std::numeric_limits<double>::quiet_NaN()),
        true_mask.select(
            DataFrame::Constant(rows, cols, value_true),
            value_false
        )
    );

    return result;
}

DataFrame IfThen(const DataFrame& condition, const DataFrame& value_true, double value_false) {
    const int rows = condition.rows();
    const int cols = condition.cols();

    // 创建掩码，标记条件为真和NaN的位置
    Eigen::Array<bool, Eigen::Dynamic, Eigen::Dynamic> true_mask = (condition.array() > 0);
    Eigen::Array<bool, Eigen::Dynamic, Eigen::Dynamic> nan_mask = condition.array().isNaN();

    // 使用表达式模板和延迟求值
    DataFrame result(rows, cols);

    // 使用noalias()避免临时拷贝
    // 先根据条件选择结果，再处理NaN情况
    result.noalias() = nan_mask.select(
        DataFrame::Constant(rows, cols, std::numeric_limits<double>::quiet_NaN()),
        true_mask.select(
            value_true,
            DataFrame::Constant(rows, cols, value_false)
        )
    );

    return result;
}

DataFrame IfThen(const DataFrame& condition, double value_true, double value_false) {
    const int rows = condition.rows();
    const int cols = condition.cols();

    // 创建掩码，标记条件为真和NaN的位置
    Eigen::Array<bool, Eigen::Dynamic, Eigen::Dynamic> true_mask = (condition.array() > 0);
    Eigen::Array<bool, Eigen::Dynamic, Eigen::Dynamic> nan_mask = condition.array().isNaN();

    // 使用表达式模板和延迟求值
    DataFrame result(rows, cols);

    // 使用noalias()避免临时拷贝
    // 先根据条件选择结果，再处理NaN情况
    result.noalias() = nan_mask.select(
        DataFrame::Constant(rows, cols, std::numeric_limits<double>::quiet_NaN()),
        true_mask.select(
            DataFrame::Constant(rows, cols, value_true),
            DataFrame::Constant(rows, cols, value_false)
        )
    );

    return result;
}

// 逻辑运算
DataFrame And(const DataFrame& s1, const DataFrame& s2) {
    const int rows = s1.rows();
    const int cols = s1.cols();

    // 创建掩码，标记NaN的位置
    Eigen::Array<bool, Eigen::Dynamic, Eigen::Dynamic> nan_mask =
        s1.array().isNaN() || s2.array().isNaN();

    // 计算逻辑与
    Eigen::Array<bool, Eigen::Dynamic, Eigen::Dynamic> result_mask =
        (s1.array() != 0) && (s2.array() != 0);

    // 使用表达式模板和延迟求值
    DataFrame result(rows, cols);

    // 使用noalias()避免临时拷贝
    result.noalias() = nan_mask.select(
        DataFrame::Constant(rows, cols, std::numeric_limits<double>::quiet_NaN()),
        result_mask.cast<double>()
    );

    return result;
}

DataFrame Or(const DataFrame& s1, const DataFrame& s2) {
    const int rows = s1.rows();
    const int cols = s1.cols();

    // 创建掩码，标记NaN的位置
    Eigen::Array<bool, Eigen::Dynamic, Eigen::Dynamic> nan_mask =
        s1.array().isNaN() || s2.array().isNaN();

    // 计算逻辑或
    Eigen::Array<bool, Eigen::Dynamic, Eigen::Dynamic> result_mask =
        (s1.array() != 0) || (s2.array() != 0);

    // 使用表达式模板和延迟求值
    DataFrame result(rows, cols);

    // 使用noalias()避免临时拷贝
    result.noalias() = nan_mask.select(
        DataFrame::Constant(rows, cols, std::numeric_limits<double>::quiet_NaN()),
        result_mask.cast<double>()
    );

    return result;
}

DataFrame Not(const DataFrame& s1) {
    const int rows = s1.rows();
    const int cols = s1.cols();

    // 创建掩码，标记NaN的位置
    Eigen::Array<bool, Eigen::Dynamic, Eigen::Dynamic> nan_mask = s1.array().isNaN();

    // 计算逻辑非
    Eigen::Array<bool, Eigen::Dynamic, Eigen::Dynamic> result_mask = (s1.array() == 0);

    // 使用表达式模板和延迟求值
    DataFrame result(rows, cols);

    // 使用noalias()避免临时拷贝
    result.noalias() = nan_mask.select(
        DataFrame::Constant(rows, cols, std::numeric_limits<double>::quiet_NaN()),
        result_mask.cast<double>()
    );

    return result;
}

DataFrame Xor(const DataFrame& s1, const DataFrame& s2) {
    const int rows = s1.rows();
    const int cols = s1.cols();

    // 创建掩码，标记NaN的位置
    Eigen::Array<bool, Eigen::Dynamic, Eigen::Dynamic> nan_mask =
        s1.array().isNaN() || s2.array().isNaN();

    // 计算逻辑异或
    Eigen::Array<bool, Eigen::Dynamic, Eigen::Dynamic> result_mask =
        ((s1.array() != 0) && (s2.array() == 0)) || ((s1.array() == 0) && (s2.array() != 0));

    // 使用表达式模板和延迟求值
    DataFrame result(rows, cols);

    // 使用noalias()避免临时拷贝
    result.noalias() = nan_mask.select(
        DataFrame::Constant(rows, cols, std::numeric_limits<double>::quiet_NaN()),
        result_mask.cast<double>()
    );

    return result;
}

DataFrame Equal(const DataFrame& s1, const DataFrame& s2) {
    const int rows = s1.rows();
    const int cols = s1.cols();

    // 创建掩码，标记NaN的位置
    Eigen::Array<bool, Eigen::Dynamic, Eigen::Dynamic> nan_mask =
        s1.array().isNaN() || s2.array().isNaN();

    // 计算相等关系，考虑浮点数精度
    Eigen::Array<bool, Eigen::Dynamic, Eigen::Dynamic> result_mask =
        (s1.array() - s2.array()).abs() < 1e-10;

    // 使用表达式模板和延迟求值
    DataFrame result(rows, cols);

    // 使用noalias()避免临时拷贝
    result.noalias() = nan_mask.select(
        DataFrame::Constant(rows, cols, std::numeric_limits<double>::quiet_NaN()),
        result_mask.cast<double>()
    );

    return result;
}

DataFrame UnEqual(const DataFrame& s1, const DataFrame& s2) {
    const int rows = s1.rows();
    const int cols = s1.cols();

    // 创建掩码，标记NaN的位置
    Eigen::Array<bool, Eigen::Dynamic, Eigen::Dynamic> nan_mask =
        s1.array().isNaN() || s2.array().isNaN();

    // 计算不等关系，考虑浮点数精度
    Eigen::Array<bool, Eigen::Dynamic, Eigen::Dynamic> result_mask =
        (s1.array() - s2.array()).abs() >= 1e-10;

    // 使用表达式模板和延迟求值
    DataFrame result(rows, cols);

    // 使用noalias()避免临时拷贝
    result.noalias() = nan_mask.select(
        DataFrame::Constant(rows, cols, std::numeric_limits<double>::quiet_NaN()),
        result_mask.cast<double>()
    );

    return result;
}

DataFrame Mthan(const DataFrame& s1, const DataFrame& s2) {
    const int rows = s1.rows();
    const int cols = s1.cols();

    // 创建掩码，标记NaN的位置
    Eigen::Array<bool, Eigen::Dynamic, Eigen::Dynamic> nan_mask =
        s1.array().isNaN() || s2.array().isNaN();

    // 计算大于关系
    Eigen::Array<bool, Eigen::Dynamic, Eigen::Dynamic> result_mask =
        s1.array() > s2.array();

    // 使用表达式模板和延迟求值
    DataFrame result(rows, cols);

    // 使用noalias()避免临时拷贝
    result.noalias() = nan_mask.select(
        DataFrame::Constant(rows, cols, std::numeric_limits<double>::quiet_NaN()),
        result_mask.cast<double>()
    );

    return result;
}

DataFrame MEthan(const DataFrame& s1, const DataFrame& s2) {
    const int rows = s1.rows();
    const int cols = s1.cols();

    // 创建掩码，标记NaN的位置
    Eigen::Array<bool, Eigen::Dynamic, Eigen::Dynamic> nan_mask =
        s1.array().isNaN() || s2.array().isNaN();

    // 计算大于等于关系
    Eigen::Array<bool, Eigen::Dynamic, Eigen::Dynamic> result_mask =
        s1.array() >= s2.array();

    // 使用表达式模板和延迟求值
    DataFrame result(rows, cols);

    // 使用noalias()避免临时拷贝
    result.noalias() = nan_mask.select(
        DataFrame::Constant(rows, cols, std::numeric_limits<double>::quiet_NaN()),
        result_mask.cast<double>()
    );

    return result;
}

DataFrame Lthan(const DataFrame& s1, const DataFrame& s2) {
    const int rows = s1.rows();
    const int cols = s1.cols();

    // 创建掩码，标记NaN的位置
    Eigen::Array<bool, Eigen::Dynamic, Eigen::Dynamic> nan_mask =
        s1.array().isNaN() || s2.array().isNaN();

    // 计算小于关系
    Eigen::Array<bool, Eigen::Dynamic, Eigen::Dynamic> result_mask =
        s1.array() < s2.array();

    // 使用表达式模板和延迟求值
    DataFrame result(rows, cols);

    // 使用noalias()避免临时拷贝
    result.noalias() = nan_mask.select(
        DataFrame::Constant(rows, cols, std::numeric_limits<double>::quiet_NaN()),
        result_mask.cast<double>()
    );

    return result;
}

DataFrame LEthan(const DataFrame& s1, const DataFrame& s2) {
    const int rows = s1.rows();
    const int cols = s1.cols();

    // 创建掩码，标记NaN的位置
    Eigen::Array<bool, Eigen::Dynamic, Eigen::Dynamic> nan_mask =
        s1.array().isNaN() || s2.array().isNaN();

    // 计算小于等于关系
    Eigen::Array<bool, Eigen::Dynamic, Eigen::Dynamic> result_mask =
        s1.array() <= s2.array();

    // 使用表达式模板和延迟求值
    DataFrame result(rows, cols);

    // 使用noalias()避免临时拷贝
    result.noalias() = nan_mask.select(
        DataFrame::Constant(rows, cols, std::numeric_limits<double>::quiet_NaN()),
        result_mask.cast<double>()
    );

    return result;
}

// 特殊函数
DataFrame getNan(const DataFrame& s1) {
    // 使用延迟求值，避免创建临时矩阵
    return DataFrame::Constant(s1.rows(), s1.cols(), std::numeric_limits<double>::quiet_NaN()).eval();
}

DataFrame getInf(const DataFrame& s1) {
    // 使用延迟求值，避免创建临时矩阵
    return DataFrame::Constant(s1.rows(), s1.cols(), std::numeric_limits<double>::infinity()).eval();
}

DataFrame FilterInf(const DataFrame& s1) {
    // 使用延迟求值，避免创建临时矩阵
    return replaceInf(s1, std::numeric_limits<double>::quiet_NaN());
}

DataFrame Max(const DataFrame& s1, const DataFrame& s2) {
    // 使用延迟求值，避免创建临时矩阵
    return s1.cwiseMax(s2).eval();
}

DataFrame Min(const DataFrame& s1, const DataFrame& s2) {
    // 使用延迟求值，避免创建临时矩阵
    return s1.cwiseMin(s2).eval();
}

// 时间序列运算
DataFrame ts_Delay(const DataFrame& s1, int n) {
    if (n <= 0) return s1;

    DataFrame result = DataFrame::Constant(s1.rows(), s1.cols(), std::numeric_limits<double>::quiet_NaN());

    // 从n行开始复制数据
    if (s1.rows() > n) {
        result.bottomRows(s1.rows() - n) = s1.topRows(s1.rows() - n);
    }

    return result;
}

DataFrame ts_Mean(const DataFrame& s1, int n) {
    if (n <= 0) return s1;

    const int rows = s1.rows();
    const int cols = s1.cols();

    // 使用延迟求值创建结果矩阵
    DataFrame result = DataFrame::Constant(rows, cols, std::numeric_limits<double>::quiet_NaN());

    // 预分配内存以避免重新分配
    Eigen::Array<double, Eigen::Dynamic, 1> cumsum(rows + 1);
    Eigen::Array<int, Eigen::Dynamic, 1> count(rows + 1);

    // 对每一列计算滚动平均值
    for (int j = 0; j < cols; ++j) {
        // 初始化累积和和计数数组
        cumsum.setZero();
        count.setZero();

        // 计算累积和和有效值计数 - 使用向量化操作
        for (int i = 0; i < rows; ++i) {
            cumsum(i + 1) = cumsum(i);
            count(i + 1) = count(i);

            if (!isNaN(s1(i, j))) {
                cumsum(i + 1) += s1(i, j);
                count(i + 1)++;
            }
        }

        // 计算滚动平均值 - 使用向量化操作
        for (int i = 0; i < rows; ++i) {
            int start = std::max(0, i - n + 1);
            int window_count = count(i + 1) - count(start);

            if (window_count > 0) {
                double window_sum = cumsum(i + 1) - cumsum(start);
                result(i, j) = window_sum / window_count;
            }
        }
    }

    return result;
}

DataFrame ts_Sum(const DataFrame& s1, int n) {
    if (n <= 0) return s1;

    const int rows = s1.rows();
    const int cols = s1.cols();

    // 使用延迟求值创建结果矩阵
    DataFrame result = DataFrame::Constant(rows, cols, std::numeric_limits<double>::quiet_NaN());

    // 预分配内存以避免重新分配
    Eigen::Array<double, Eigen::Dynamic, 1> cumsum(rows + 1);
    Eigen::Array<bool, Eigen::Dynamic, 1> has_valid(rows + 1);

    // 对每一列计算滚动和
    for (int j = 0; j < cols; ++j) {
        // 初始化累积和和有效标记数组
        cumsum.setZero();
        has_valid.setConstant(false);

        // 计算累积和和有效标记 - 使用Eigen数组操作
        for (int i = 0; i < rows; ++i) {
            cumsum(i + 1) = cumsum(i);
            has_valid(i + 1) = has_valid(i);

            if (!isNaN(s1(i, j))) {
                cumsum(i + 1) += s1(i, j);
                has_valid(i + 1) = true;
            }
        }

        // 计算滚动和 - 使用Eigen数组操作
        for (int i = 0; i < rows; ++i) {
            int start = std::max(0, i - n + 1);

            if (has_valid(i + 1)) {
                double window_sum = cumsum(i + 1) - cumsum(start);
                result(i, j) = window_sum;
            }
        }
    }

    return result;
}

DataFrame ts_Stdev(const DataFrame& s1, int n) {
    if (n <= 1) {
        // 标准差至少需要2个样本
        return DataFrame::Constant(s1.rows(), s1.cols(), std::numeric_limits<double>::quiet_NaN());
    }

    const int rows = s1.rows();
    const int cols = s1.cols();

    // 使用延迟求值创建结果矩阵
    DataFrame result = DataFrame::Constant(rows, cols, std::numeric_limits<double>::quiet_NaN());

    // 计算滚动均值
    DataFrame means = ts_Mean(s1, n);

    // 对每一列计算滚动标准差
    for (int j = 0; j < cols; ++j) {
        // 使用Eigen数组操作优化计算
        for (int i = 0; i < rows; ++i) {
            if (isNaN(means(i, j))) continue;

            int start = std::max(0, i - n + 1);
            int count = 0;
            double sum_sq_diff = 0.0;

            // 计算平方差之和
            for (int k = start; k <= i; ++k) {
                if (!isNaN(s1(k, j))) {
                    // 使用noalias()避免临时变量
                    double diff = s1(k, j) - means(i, j);
                    sum_sq_diff += diff * diff;
                    count++;
                }
            }

            if (count > 1) {
                // 使用无偏估计 (ddof=1)，与pandas一致
                result(i, j) = std::sqrt(sum_sq_diff / (count - 1));
            }
        }
    }

    return result;
}

DataFrame ts_Delta(const DataFrame& s1, int n) {
    if (n <= 0) return DataFrame::Zero(s1.rows(), s1.cols());

    DataFrame delayed = ts_Delay(s1, n);
    return Minus(s1, delayed);
}

DataFrame ts_Divide(const DataFrame& s1, int n) {
    if (n < 5) n = 5; // 最小窗口大小

    DataFrame delayed = ts_Delay(s1, n);
    return Divide(s1, delayed);
}

DataFrame ts_ChgRate(const DataFrame& s1, int n) {
    if (n < 1) n = 1; // 最小窗口大小

    DataFrame ratio = ts_Divide(s1, n);
    return Minus(ratio, DataFrame::Ones(s1.rows(), s1.cols()));
}

DataFrame ts_Min(const DataFrame& s1, int n) {
    if (n <= 0) return s1;

    const int rows = s1.rows();
    const int cols = s1.cols();

    // 使用延迟求值创建结果矩阵
    DataFrame result = DataFrame::Constant(rows, cols, std::numeric_limits<double>::quiet_NaN());

    // 对每一列计算滚动最小值
    for (int j = 0; j < cols; ++j) {
        // 使用Eigen数组操作优化计算
        Eigen::Array<double, Eigen::Dynamic, 1> window_values(n);

        for (int i = 0; i < rows; ++i) {
            int start = std::max(0, i - n + 1);
            int window_size = i - start + 1;
            int valid_count = 0;

            // 收集窗口内的有效值
            for (int k = start; k <= i; ++k) {
                if (!isNaN(s1(k, j))) {
                    window_values(valid_count++) = s1(k, j);
                }
            }

            // 如果有有效值，计算最小值
            if (valid_count > 0) {
                // 使用Eigen的minCoeff()函数
                result(i, j) = window_values.head(valid_count).minCoeff();
            }
        }
    }

    return result;
}

DataFrame ts_Max(const DataFrame& s1, int n) {
    if (n <= 0) return s1;

    const int rows = s1.rows();
    const int cols = s1.cols();

    // 使用延迟求值创建结果矩阵
    DataFrame result = DataFrame::Constant(rows, cols, std::numeric_limits<double>::quiet_NaN());

    // 对每一列计算滚动最大值
    for (int j = 0; j < cols; ++j) {
        // 使用Eigen数组操作优化计算
        Eigen::Array<double, Eigen::Dynamic, 1> window_values(n);

        for (int i = 0; i < rows; ++i) {
            int start = std::max(0, i - n + 1);
            int window_size = i - start + 1;
            int valid_count = 0;

            // 收集窗口内的有效值
            for (int k = start; k <= i; ++k) {
                if (!isNaN(s1(k, j))) {
                    window_values(valid_count++) = s1(k, j);
                }
            }

            // 如果有有效值，计算最大值
            if (valid_count > 0) {
                // 使用Eigen的maxCoeff()函数
                result(i, j) = window_values.head(valid_count).maxCoeff();
            }
        }
    }

    return result;
}

// Tot系列函数（包装函数）
DataFrame Tot_Mean(const DataFrame& s1) {
    // 使用延迟求值，避免创建临时矩阵
    return ts_Mean(s1, 15);
}

DataFrame Tot_Sum(const DataFrame& s1) {
    // 使用延迟求值，避免创建临时矩阵
    return ts_Sum(s1, 15);
}

DataFrame Tot_Stdev(const DataFrame& s1) {
    // 使用延迟求值，避免创建临时矩阵
    return ts_Stdev(s1, 15);
}

DataFrame Tot_Delta(const DataFrame& s1) {
    // 使用延迟求值，避免创建临时矩阵
    return ts_Delta(s1, 15);
}

DataFrame Tot_Divide(const DataFrame& s1) {
    // 使用延迟求值，避免创建临时矩阵
    return ts_Divide(s1, 15);
}

DataFrame Tot_ChgRate(const DataFrame& s1) {
    // 使用延迟求值，避免创建临时矩阵
    return ts_ChgRate(s1, 15);
}

DataFrame Tot_Max(const DataFrame& s1) {
    // 使用延迟求值，避免创建临时矩阵
    return ts_Max(s1, 15);
}

DataFrame Tot_Min(const DataFrame& s1) {
    // 使用延迟求值，避免创建临时矩阵
    return ts_Min(s1, 15);
}

DataFrame ts_Rank(const DataFrame& s1, int n) {
    if (n < 5) {
        n = 5; // 最小窗口大小，与Python保持一致
    }

    const int rows = s1.rows();
    const int cols = s1.cols();

    // 使用延迟求值创建结果矩阵
    DataFrame result = DataFrame::Constant(rows, cols, std::numeric_limits<double>::quiet_NaN());

    // 对每一列计算滚动排名
    for (int j = 0; j < cols; ++j) {
        // 使用Eigen数组操作优化计算
        Eigen::Array<double, Eigen::Dynamic, 1> window_values(n);
        Eigen::Array<int, Eigen::Dynamic, 1> indices(n);

        for (int i = n - 1; i < rows; ++i) {
            int start = i - n + 1;
            int valid_count = 0;

            // 收集窗口内的非NaN值及其索引
            for (int k = start; k <= i; ++k) {
                if (!isNaN(s1(k, j))) {
                    window_values(valid_count) = s1(k, j);
                    indices(valid_count) = k - start;
                    valid_count++;
                }
            }

            if (valid_count <= 1) continue;

            // 创建排序索引
            std::vector<std::pair<double, int>> value_index_pairs(valid_count);
            for (int k = 0; k < valid_count; ++k) {
                value_index_pairs[k] = {window_values(k), indices(k)};
            }

            // 排序（升序）
            std::sort(value_index_pairs.begin(), value_index_pairs.end());

            // 处理相等值的情况（平均排名）
            std::vector<double> ranks(n, std::numeric_limits<double>::quiet_NaN());

            size_t k = 0;
            while (k < value_index_pairs.size()) {
                double val = value_index_pairs[k].first;
                size_t l = k + 1;
                while (l < value_index_pairs.size() && std::abs(value_index_pairs[l].first - val) < 1e-10) {
                    l++;
                }

                // 计算平均排名（从1开始）
                double rank_avg = 0.0;
                for (size_t m = k; m < l; m++) {
                    rank_avg += static_cast<double>(m + 1);  // 从1开始计数
                }
                rank_avg /= (l - k);

                // 分配排名（百分比排名）
                for (size_t m = k; m < l; m++) {
                    ranks[value_index_pairs[m].second] = rank_avg / valid_count;  // 转换为0到1
                }

                k = l;
            }

            // 填充结果，减去 1/n/2
            if (!isNaN(ranks[n - 1])) {
                result(i, j) = ranks[n - 1] - 1.0 / static_cast<double>(n) / 2.0;
            }
        }
    }

    return result;
}

DataFrame Tot_Rank(const DataFrame& s1) {
    // 调用ts_Rank函数，窗口大小为15
    // 使用延迟求值，避免创建临时矩阵
    return ts_Rank(s1, 15);
}

DataFrame ts_ArgMax(const DataFrame& s1, int n) {
    if (n < 3) {
        n = 3; // 最小窗口大小，与Python保持一致
    }

    const int rows = s1.rows();
    const int cols = s1.cols();

    // 使用延迟求值创建结果矩阵
    DataFrame result = DataFrame::Constant(rows, cols, std::numeric_limits<double>::quiet_NaN());

    // 对每一列计算滚动最大值的位置
    for (int j = 0; j < cols; ++j) {
        // 使用Eigen数组操作优化计算
        Eigen::Array<double, Eigen::Dynamic, 1> window_values(n);
        Eigen::Array<int, Eigen::Dynamic, 1> indices(n);

        for (int i = n - 1; i < rows; ++i) {
            int start = i - n + 1;
            int valid_count = 0;
            int maxIndex = -1;
            double maxValue = -std::numeric_limits<double>::infinity();

            // 在窗口内找到最大值的位置
            for (int k = start; k <= i; ++k) {
                if (!isNaN(s1(k, j))) {
                    if (s1(k, j) > maxValue) {
                        maxValue = s1(k, j);
                        maxIndex = k - start;
                    }
                }
            }

            // 如果找到了最大值，计算其相对于当前位置的偏移
            // 注意：Python版本的索引从1开始，所以这里加1
            if (maxIndex >= 0) {
                result(i, j) = static_cast<double>(n - maxIndex - 1 + 1);
            }
        }
    }

    return result;
}

DataFrame Tot_ArgMax(const DataFrame& s1) {
    // 调用ts_ArgMax函数，窗口大小为15
    // 使用延迟求值，避免创建临时矩阵
    return ts_ArgMax(s1, 15);
}

DataFrame ts_ArgMin(const DataFrame& s1, int n) {
    if (n < 3) {
        n = 3; // 最小窗口大小，与Python保持一致
    }

    const int rows = s1.rows();
    const int cols = s1.cols();

    // 使用延迟求值创建结果矩阵
    DataFrame result = DataFrame::Constant(rows, cols, std::numeric_limits<double>::quiet_NaN());

    // 对每一列计算滚动最小值的位置
    for (int j = 0; j < cols; ++j) {
        // 使用Eigen数组操作优化计算
        Eigen::Array<double, Eigen::Dynamic, 1> window_values(n);
        Eigen::Array<int, Eigen::Dynamic, 1> indices(n);

        for (int i = n - 1; i < rows; ++i) {
            int start = i - n + 1;
            int valid_count = 0;
            int minIndex = -1;
            double minValue = std::numeric_limits<double>::infinity();

            // 在窗口内找到最小值的位置
            for (int k = start; k <= i; ++k) {
                if (!isNaN(s1(k, j))) {
                    if (s1(k, j) < minValue) {
                        minValue = s1(k, j);
                        minIndex = k - start;
                    }
                }
            }

            // 如果找到了最小值，计算其相对于当前位置的偏移
            // 注意：Python版本的索引从1开始，所以这里加1
            if (minIndex >= 0) {
                result(i, j) = static_cast<double>(n - minIndex - 1 + 1);
            }
        }
    }

    return result;
}

DataFrame Tot_ArgMin(const DataFrame& s1) {
    // 调用ts_ArgMin函数，窗口大小为15
    // 使用延迟求值，避免创建临时矩阵
    return ts_ArgMin(s1, 15);
}

// 正态分布的累积分布函数的近似逆函数
double normInv(double p) {
    // 处理边界情况
    if (p <= 0.0) return -std::numeric_limits<double>::infinity();
    if (p >= 1.0) return std::numeric_limits<double>::infinity();

    // 使用与scipy.stats.norm.ppf完全相同的算法
    static const double a[] = {
        -3.969683028665376e+01,
        2.209460984245205e+02,
        -2.759285104469687e+02,
        1.383577518672690e+02,
        -3.066479806614716e+01,
        2.506628277459239e+00
    };

    static const double b[] = {
        -5.447609879822406e+01,
        1.615858368580409e+02,
        -1.556989798598866e+02,
        6.680131188771972e+01,
        -1.328068155288572e+01
    };

    static const double c[] = {
        -7.784894002430293e-03,
        -3.223964580411365e-01,
        -2.400758277161838e+00,
        -2.549732539343734e+00,
        4.374664141464968e+00,
        2.938163982698783e+00
    };

    static const double d[] = {
        7.784695709041462e-03,
        3.224671290700398e-01,
        2.445134137142996e+00,
        3.754408661907416e+00
    };

    double q, r;

    if (p < 0.5) {
        q = p;
    } else {
        q = 1.0 - p;
    }

    if (q == 0.0) {
        return (p < 0.5) ? -std::numeric_limits<double>::infinity() : std::numeric_limits<double>::infinity();
    }

    r = std::sqrt(-2.0 * std::log(q));

    double x;
    if (r <= 5.0) {
        r -= 1.6;
        x = (((((a[0] * r + a[1]) * r + a[2]) * r + a[3]) * r + a[4]) * r + a[5]) /
            (((((b[0] * r + b[1]) * r + b[2]) * r + b[3]) * r + b[4]) * r + 1.0);
    } else {
        r -= 5.0;
        x = (((((c[0] * r + c[1]) * r + c[2]) * r + c[3]) * r + c[4]) * r + c[5]) /
            ((((d[0] * r + d[1]) * r + d[2]) * r + d[3]) * r + 1.0);
    }

    return (p < 0.5) ? -x : x;
}

// 横截面运算
DataFrame pn_Mean(const DataFrame& s1) {
    const int rows = s1.rows();
    const int cols = s1.cols();

    // 使用延迟求值创建结果矩阵
    DataFrame result(rows, cols);

    // 对每一行计算均值
    for (int i = 0; i < rows; ++i) {
        // 创建掩码，标记非NaN的位置
        Eigen::Array<bool, 1, Eigen::Dynamic> valid_mask = !s1.row(i).array().isNaN();

        // 计算有效元素数量
        int valid_count = valid_mask.count();

        if (valid_count > 0) {
            // 计算均值 - 使用Eigen的向量化操作
            double mean = (s1.row(i).array() * valid_mask.cast<double>()).sum() / valid_count;

            // 使用Eigen的向量化操作设置结果
            // 将均值赋给该行的所有非NaN元素，NaN保持不变
            result.row(i) = valid_mask.select(
                Eigen::RowVectorXd::Constant(1, cols, mean),
                s1.row(i)
            );
        } else {
            // 如果没有有效元素，保持原始值
            result.row(i) = s1.row(i);
        }
    }

    return result;
}

DataFrame pn_Rank(const DataFrame& s1) {
    DataFrame result = DataFrame::Constant(s1.rows(), s1.cols(), std::numeric_limits<double>::quiet_NaN());

    // 对每一行进行排名
    for (int i = 0; i < s1.rows(); ++i) {
        std::vector<std::pair<double, int>> values;

        // 收集非NaN值
        for (int j = 0; j < s1.cols(); ++j) {
            if (!isNaN(s1(i, j))) {
                values.push_back({s1(i, j), j});
            }
        }

        if (values.empty()) continue;

        // 排序（升序）
        std::sort(values.begin(), values.end());

        // 计算排名（百分比排名）
        std::vector<double> ranks(s1.cols(), std::numeric_limits<double>::quiet_NaN());

        // 处理相等值的情况（平均排名）
        size_t k = 0;
        while (k < values.size()) {
            double val = values[k].first;
            size_t l = k + 1;
            while (l < values.size() && std::abs(values[l].first - val) < 1e-10) {
                l++;
            }

            // 计算平均排名（从1开始）
            double rank_avg = 0.0;
            for (size_t m = k; m < l; m++) {
                rank_avg += static_cast<double>(m + 1);
            }
            rank_avg /= (l - k);

            // 分配排名（百分比排名）
            for (size_t m = k; m < l; m++) {
                int j = values[m].second;
                ranks[j] = rank_avg / values.size();
            }

            k = l;
        }

        // 填充结果
        for (int j = 0; j < s1.cols(); ++j) {
            if (!isNaN(ranks[j])) {
                // 减去最小值的一半，与Python版本保持一致
                double min_rank = 1.0 / (2.0 * values.size());
                result(i, j) = ranks[j] - min_rank;
            }
        }
    }

    return result;
}

DataFrame pn_TransNorm(const DataFrame& s1) {
    // 复制输入数据
    DataFrame dfCleaned = s1;

    // 计算每行的排名（百分比形式）
    DataFrame rank_ = DataFrame::Constant(s1.rows(), s1.cols(), std::numeric_limits<double>::quiet_NaN());
    for (int i = 0; i < s1.rows(); ++i) {
        std::vector<std::pair<double, int>> values;

        // 收集当前行的非NaN值及其索引
        for (int j = 0; j < s1.cols(); ++j) {
            if (!isNaN(s1(i, j))) {
                values.push_back({s1(i, j), j});
            }
        }

        if (!values.empty()) {
            // 排序
            std::sort(values.begin(), values.end());

            // 计算排名（百分比形式）
            for (size_t k = 0; k < values.size(); ++k) {
                int j = values[k].second;
                rank_(i, j) = (k + 1.0) / values.size();
            }
        }
    }

    // 计算每行的最小值
    std::vector<double> min_vals(s1.rows(), std::numeric_limits<double>::infinity());
    for (int i = 0; i < rank_.rows(); ++i) {
        for (int j = 0; j < rank_.cols(); ++j) {
            if (!isNaN(rank_(i, j)) && rank_(i, j) < min_vals[i]) {
                min_vals[i] = rank_(i, j);
            }
        }
    }

    // 计算cut值
    std::vector<double> cut(s1.rows());
    for (int i = 0; i < s1.rows(); ++i) {
        if (min_vals[i] != std::numeric_limits<double>::infinity()) {
            cut[i] = min_vals[i] / 2.0;
        } else {
            cut[i] = 0.0;
        }
    }

    // 减去cut值
    for (int i = 0; i < rank_.rows(); ++i) {
        for (int j = 0; j < rank_.cols(); ++j) {
            if (!isNaN(rank_(i, j))) {
                rank_(i, j) -= cut[i];
            }
        }
    }

    // 应用正态分布的逆函数
    DataFrame result = DataFrame::Constant(s1.rows(), s1.cols(), std::numeric_limits<double>::quiet_NaN());
    for (int i = 0; i < rank_.rows(); ++i) {
        for (int j = 0; j < rank_.cols(); ++j) {
            if (!isNaN(rank_(i, j))) {
                // 确保排名在有效范围内
                double adjusted_rank = rank_(i, j);
                if (adjusted_rank <= 0.0) adjusted_rank = 0.0001;
                if (adjusted_rank >= 1.0) adjusted_rank = 0.9999;

                // 使用normInv函数，与Python的norm.ppf完全一致
                result(i, j) = normInv(adjusted_rank);
            }
        }
    }

    return result;
}

DataFrame pn_Rank2(const DataFrame& s1) {
    DataFrame result = DataFrame::Constant(s1.rows(), s1.cols(), std::numeric_limits<double>::quiet_NaN());

    // 对每一行进行排名
    for (int i = 0; i < s1.rows(); ++i) {
        std::vector<std::pair<double, int>> values;

        // 收集当前行的非NaN值
        for (int j = 0; j < s1.cols(); ++j) {
            if (!isNaN(s1(i, j))) {
                values.push_back({s1(i, j), j});
            }
        }

        if (values.empty()) continue;

        // 排序（升序）
        std::sort(values.begin(), values.end());

        // 处理相等值的情况（平均排名）
        size_t k = 0;
        while (k < values.size()) {
            double val = values[k].first;
            size_t l = k + 1;
            while (l < values.size() && std::abs(values[l].first - val) < 1e-10) {
                l++;
            }

            // 计算平均排名（从1开始）
            double rank_avg = 0.0;
            for (size_t m = k; m < l; m++) {
                rank_avg += static_cast<double>(m + 1);
            }
            rank_avg /= (l - k);

            // 分配排名
            for (size_t m = k; m < l; m++) {
                result(i, values[m].second) = rank_avg;
            }

            k = l;
        }
    }

    return result;
}

DataFrame pn_RankCentered(const DataFrame& s1) {
    // 使用pn_Rank而不是pn_Rank2，与Python版本保持一致
    DataFrame ranks = pn_Rank(s1);

    // 将排名缩放到[-1, 1]区间
    DataFrame result = ranks.array() * 2.0 - 1.0;

    return result;
}

DataFrame pn_FillMax(const DataFrame& s1) {
    DataFrame result = DataFrame::Constant(s1.rows(), s1.cols(), std::numeric_limits<double>::quiet_NaN());

    // 对每一行计算最大值
    for (int i = 0; i < s1.rows(); ++i) {
        // 创建掩码，标记非NaN的位置
        Eigen::Array<bool, 1, Eigen::Dynamic> valid_mask = !s1.row(i).array().isNaN();

        if (valid_mask.any()) {
            // 计算最大值
            double max_val = (s1.row(i).array() * valid_mask.cast<double>() +
                             (-std::numeric_limits<double>::infinity()) * (!valid_mask).cast<double>()).maxCoeff();

            // 用最大值填充每一列
            for (int j = 0; j < s1.cols(); ++j) {
                result(i, j) = max_val;
            }
        }
    }

    return result;
}

DataFrame pn_FillMin(const DataFrame& s1) {
    DataFrame result = DataFrame::Constant(s1.rows(), s1.cols(), std::numeric_limits<double>::quiet_NaN());

    // 对每一行计算最小值
    for (int i = 0; i < s1.rows(); ++i) {
        // 创建掩码，标记非NaN的位置
        Eigen::Array<bool, 1, Eigen::Dynamic> valid_mask = !s1.row(i).array().isNaN();

        if (valid_mask.any()) {
            // 计算最小值
            double min_val = (s1.row(i).array() * valid_mask.cast<double>() +
                             (std::numeric_limits<double>::infinity()) * (!valid_mask).cast<double>()).minCoeff();

            // 用最小值填充每一列
            for (int j = 0; j < s1.cols(); ++j) {
                result(i, j) = min_val;
            }
        }
    }

    return result;
}

DataFrame pn_TransStd(const DataFrame& s1) {
    const int rows = s1.rows();
    const int cols = s1.cols();

    // 使用延迟求值创建结果矩阵
    DataFrame result = DataFrame::Constant(rows, cols, std::numeric_limits<double>::quiet_NaN());

    // 对每一行进行标准化
    for (int i = 0; i < rows; ++i) {
        // 创建掩码，标记非NaN的位置
        Eigen::Array<bool, 1, Eigen::Dynamic> valid_mask = !s1.row(i).array().isNaN();
        int valid_count = valid_mask.count();

        if (valid_count > 1) {
            // 提取有效值到临时数组
            Eigen::Array<double, 1, Eigen::Dynamic> valid_values(1, valid_count);
            int idx = 0;
            for (int j = 0; j < cols; ++j) {
                if (valid_mask(j)) {
                    valid_values(0, idx++) = s1(i, j);
                }
            }

            // 使用Eigen计算均值
            double mean = valid_values.mean();

            // 使用Eigen计算标准差
            double var = ((valid_values - mean).square()).sum() / (valid_count - 1);  // 使用无偏估计
            double stddev = std::sqrt(var);

            if (stddev > 1e-10) {
                // 使用Eigen进行标准化
                for (int j = 0; j < cols; ++j) {
                    if (valid_mask(j)) {
                        // 使用noalias()避免临时变量
                        result(i, j) = (s1(i, j) - mean) / stddev;
                    }
                }
            }
        }
    }

    return result;
}


DataFrame pn_Winsor(const DataFrame& s1, double Multiplier) {
    const int rows = s1.rows();
    const int cols = s1.cols();

    // 使用延迟求值创建结果矩阵
    DataFrame result = s1;

    // 对每一行进行Winsorization
    for (int i = 0; i < rows; ++i) {
        // 创建掩码，标记非NaN的位置
        Eigen::Array<bool, 1, Eigen::Dynamic> valid_mask = !s1.row(i).array().isNaN();
        int valid_count = valid_mask.count();

        if (valid_count > 1) {
            // 提取有效值到临时数组
            Eigen::Array<double, 1, Eigen::Dynamic> valid_values(1, valid_count);
            int idx = 0;
            for (int j = 0; j < cols; ++j) {
                if (valid_mask(j)) {
                    valid_values(0, idx++) = s1(i, j);
                }
            }

            // 使用Eigen计算均值和标准差
            double mean = valid_values.mean();
            double var = ((valid_values - mean).square()).sum() / (valid_count - 1);  // 使用无偏估计
            double stddev = std::sqrt(var);

            // 计算上下限
            double upper_bound = mean + Multiplier * stddev;
            double lower_bound = mean - Multiplier * stddev;

            // 应用Winsorization
            for (int j = 0; j < cols; ++j) {
                if (valid_mask(j)) {
                    if (s1(i, j) > upper_bound) {
                        result(i, j) = upper_bound;
                    } else if (s1(i, j) < lower_bound) {
                        result(i, j) = lower_bound;
                    }
                }
            }
        }
    }

    return result;
}

DataFrame pn_Cut(const DataFrame& s1) {
    const int rows = s1.rows();
    const int cols = s1.cols();

    // 使用延迟求值创建结果矩阵
    DataFrame result = s1;

    // 对每一行进行Cut操作（类似于Winsorization，但使用3倍标准差）
    for (int i = 0; i < rows; ++i) {
        // 创建掩码，标记非NaN的位置
        Eigen::Array<bool, 1, Eigen::Dynamic> valid_mask = !s1.row(i).array().isNaN();
        int valid_count = valid_mask.count();

        if (valid_count > 1) {
            // 提取有效值到临时数组
            Eigen::Array<double, 1, Eigen::Dynamic> valid_values(1, valid_count);
            int idx = 0;
            for (int j = 0; j < cols; ++j) {
                if (valid_mask(j)) {
                    valid_values(0, idx++) = s1(i, j);
                }
            }

            // 使用Eigen计算均值和标准差
            double mean = valid_values.mean();
            double var = ((valid_values - mean).square()).sum() / (valid_count - 1);  // 使用无偏估计
            double stddev = std::sqrt(var);

            // 计算上下限（3倍标准差）
            double upper_bound = mean + 3.0 * stddev;
            double lower_bound = mean - 3.0 * stddev;

            // 应用Cut操作
            for (int j = 0; j < cols; ++j) {
                if (valid_mask(j)) {
                    if (s1(i, j) > upper_bound) {
                        result(i, j) = upper_bound;
                    } else if (s1(i, j) < lower_bound) {
                        result(i, j) = lower_bound;
                    }
                }
            }
        }
    }

    return result;
}

DataFrame pn_Stand(const DataFrame& s1) {
    const int rows = s1.rows();
    const int cols = s1.cols();

    // 使用延迟求值创建结果矩阵
    DataFrame result = DataFrame::Constant(rows, cols, std::numeric_limits<double>::quiet_NaN());

    // 对每一行进行标准化
    for (int i = 0; i < rows; ++i) {
        // 创建掩码，标记非NaN的位置
        Eigen::Array<bool, 1, Eigen::Dynamic> valid_mask = !s1.row(i).array().isNaN();
        int valid_count = valid_mask.count();

        if (valid_count > 1) {
            // 提取有效值到临时数组
            Eigen::Array<double, 1, Eigen::Dynamic> valid_values(1, valid_count);
            int idx = 0;
            for (int j = 0; j < cols; ++j) {
                if (valid_mask(j)) {
                    valid_values(0, idx++) = s1(i, j);
                }
            }

            // 使用Eigen计算中位数
            std::vector<double> values;
            values.reserve(valid_count);
            for (int k = 0; k < valid_count; ++k) {
                values.push_back(valid_values(0, k));
            }
            std::sort(values.begin(), values.end());
            double median;
            if (values.size() % 2 == 0) {
                median = (values[values.size() / 2 - 1] + values[values.size() / 2]) / 2.0;
            } else {
                median = values[values.size() / 2];
            }

            // 使用Eigen计算标准差
            double var = ((valid_values - valid_values.mean()).square()).sum() / valid_count;  // 使用有偏估计
            double stddev = std::sqrt(var);

            if (stddev > 1e-10) {
                // 使用Eigen进行标准化
                for (int j = 0; j < cols; ++j) {
                    if (valid_mask(j)) {
                        // 使用noalias()避免临时变量
                        result(i, j) = (s1(i, j) - median) / stddev;
                    }
                }
            }
        }
    }

    return result;
}

DataFrame pn_GroupRank(const DataFrame& s1, const DataFrame& group) {
    const int rows = s1.rows();
    const int cols = s1.cols();

    // 使用延迟求值创建结果矩阵
    DataFrame result = DataFrame::Constant(rows, cols, std::numeric_limits<double>::quiet_NaN());

    // 对每一行进行分组排名
    for (int i = 0; i < rows; ++i) {
        // 收集每个分组的值
        std::map<int, std::vector<std::pair<double, int>>> group_values;

        for (int j = 0; j < cols; ++j) {
            if (!isNaN(s1(i, j)) && !isNaN(group(i, j))) {
                int g = static_cast<int>(group(i, j));
                group_values[g].push_back({s1(i, j), j});
            }
        }

        // 对每个分组进行排名
        for (auto& kv : group_values) {
            auto& values = kv.second;

            if (values.size() > 0) {
                // 排序（升序）
                std::sort(values.begin(), values.end());

                // 计算排名（百分比形式）
                for (size_t k = 0; k < values.size(); ++k) {
                    int col = values[k].second;
                    double rank = (k + 1.0) / values.size();

                    // 减去最小值的一半，与Python版本保持一致
                    double min_rank = 1.0 / (2.0 * values.size());
                    result(i, col) = rank - min_rank;
                }
            }
        }
    }

    return result;
}

DataFrame pn_GroupNorm(const DataFrame& s1, const DataFrame& group) {
    const int rows = s1.rows();
    const int cols = s1.cols();

    // 使用延迟求值创建结果矩阵
    DataFrame result = DataFrame::Constant(rows, cols, 0.0);  // 初始化为0，而不是NaN

    // 对每一行进行分组标准化
    for (int i = 0; i < rows; ++i) {
        // 收集每个分组的值和位置
        std::map<int, std::vector<std::pair<double, int>>> group_values;

        for (int j = 0; j < cols; ++j) {
            if (!isNaN(s1(i, j)) && !isNaN(group(i, j))) {
                int g = static_cast<int>(group(i, j));
                group_values[g].push_back({s1(i, j), j});
            }
        }

        // 对每个分组进行标准化
        for (auto& kv : group_values) {
            auto& values = kv.second;

            if (values.size() > 0) {
                // 排序（升序）
                std::sort(values.begin(), values.end());

                // 计算排名（百分比形式）
                std::vector<double> ranks(values.size());
                for (size_t k = 0; k < values.size(); ++k) {
                    ranks[k] = (k + 1.0) / values.size();
                }

                // 计算最小值
                double min_rank = 1.0 / (2.0 * values.size());

                // 调整排名
                for (size_t k = 0; k < values.size(); ++k) {
                    ranks[k] -= min_rank;

                    // 确保排名在有效范围内
                    if (ranks[k] <= 0.0) ranks[k] = 0.0001;
                    if (ranks[k] >= 1.0) ranks[k] = 0.9999;

                    // 应用正态分布的逆函数
                    int col = values[k].second;
                    result(i, col) = normInv(ranks[k]);
                }
            }
        }

        // 将NaN值填回结果
        for (int j = 0; j < cols; ++j) {
            if (isNaN(s1(i, j)) || isNaN(group(i, j))) {
                result(i, j) = std::numeric_limits<double>::quiet_NaN();
            }
        }
    }

    return result;
}

DataFrame pn_GroupNeutral(const DataFrame& s1, const DataFrame& group) {
    const int rows = s1.rows();
    const int cols = s1.cols();

    // 使用延迟求值创建结果矩阵
    DataFrame result = DataFrame::Constant(rows, cols, std::numeric_limits<double>::quiet_NaN());

    // 对每一行进行分组中性化
    for (int i = 0; i < rows; ++i) {
        // 收集每个分组的值
        std::map<int, std::vector<std::pair<double, int>>> group_values;

        for (int j = 0; j < cols; ++j) {
            if (!isNaN(s1(i, j)) && !isNaN(group(i, j))) {
                int g = static_cast<int>(group(i, j));
                group_values[g].push_back({s1(i, j), j});
            }
        }

        // 对每个分组进行中性化
        for (auto& kv : group_values) {
            auto& values = kv.second;

            if (values.size() > 0) {
                // 计算均值
                double sum = 0.0;
                for (const auto& p : values) {
                    sum += p.first;
                }
                double mean = sum / values.size();

                // 应用中性化
                for (const auto& p : values) {
                    int col = p.second;
                    result(i, col) = s1(i, col) - mean;
                }
            }
        }
    }

    return result;
}

DataFrame pn_CrossFit(const DataFrame& y, const DataFrame& x) {
    const int rows = y.rows();
    const int cols = y.cols();

    // 使用延迟求值创建结果矩阵
    DataFrame result = DataFrame::Constant(rows, cols, std::numeric_limits<double>::quiet_NaN());

    // 对每一行进行交叉拟合
    for (int i = 0; i < rows; ++i) {
        // 创建掩码，标记非NaN的位置
        Eigen::Array<bool, 1, Eigen::Dynamic> valid_mask_x = !x.row(i).array().isNaN();
        Eigen::Array<bool, 1, Eigen::Dynamic> valid_mask_y = !y.row(i).array().isNaN();
        Eigen::Array<bool, 1, Eigen::Dynamic> valid_mask = valid_mask_x && valid_mask_y;
        int valid_count = valid_mask.count();

        if (valid_count > 1) {
            // 提取有效值到临时数组
            Eigen::Array<double, 1, Eigen::Dynamic> valid_x(1, valid_count);
            Eigen::Array<double, 1, Eigen::Dynamic> valid_y(1, valid_count);
            int idx = 0;
            for (int j = 0; j < cols; ++j) {
                if (valid_mask(j)) {
                    valid_x(0, idx) = x(i, j);
                    valid_y(0, idx) = y(i, j);
                    idx++;
                }
            }

            // 计算均值
            double mean_x = valid_x.mean();
            double mean_y = valid_y.mean();

            // 计算中心化数据
            Eigen::Array<double, 1, Eigen::Dynamic> centered_x = valid_x - mean_x;
            Eigen::Array<double, 1, Eigen::Dynamic> centered_y = valid_y - mean_y;

            // 计算回归系数
            double numerator = (centered_x * centered_y).sum();
            double denominator = (centered_x * centered_x).sum();

            if (std::abs(denominator) > 1e-10) {
                double b = numerator / denominator;
                double a = mean_y - b * mean_x;

                // 计算残差
                for (int j = 0; j < cols; ++j) {
                    if (!isNaN(x(i, j)) && !isNaN(y(i, j))) {
                        result(i, j) = y(i, j) - (a + b * x(i, j));
                    }
                }
            }
        }
    }

    return result;
}

DataFrame ts_Corr(const DataFrame& s1, const DataFrame& s2, int n) {
    if (n <= 1) {
        // 相关系数至少需要2个样本
        return DataFrame::Constant(s1.rows(), s1.cols(), std::numeric_limits<double>::quiet_NaN());
    }

    const int rows = s1.rows();
    const int cols = s1.cols();

    // 使用延迟求值创建结果矩阵
    DataFrame result = DataFrame::Constant(rows, cols, std::numeric_limits<double>::quiet_NaN());

    // 计算滚动均值
    DataFrame means1 = ts_Mean(s1, n);
    DataFrame means2 = ts_Mean(s2, n);

    // 对每一列计算滚动相关系数
    #pragma omp parallel for collapse(2) if(rows * cols > 1000)
    for (int j = 0; j < cols; ++j) {
        for (int i = 0; i < rows; ++i) {
            if (isNaN(means1(i, j)) || isNaN(means2(i, j))) continue;

            int start = std::max(0, i - n + 1);

            // 使用Eigen向量化操作
            Eigen::VectorXd x_values(n);
            Eigen::VectorXd y_values(n);
            int valid_count = 0;

            // 收集窗口内的有效值
            for (int k = start; k <= i; ++k) {
                if (!isNaN(s1(k, j)) && !isNaN(s2(k, j))) {
                    x_values(valid_count) = s1(k, j) - means1(i, j);
                    y_values(valid_count) = s2(k, j) - means2(i, j);
                    valid_count++;
                }
            }

            if (valid_count > 1) {
                // 使用Eigen计算点积和范数
                double sum_xy = x_values.head(valid_count).dot(y_values.head(valid_count));
                double sum_x2 = x_values.head(valid_count).squaredNorm();
                double sum_y2 = y_values.head(valid_count).squaredNorm();

                if (sum_x2 > 1e-10 && sum_y2 > 1e-10) {
                    // 计算相关系数
                    result(i, j) = sum_xy / std::sqrt(sum_x2 * sum_y2);
                }
            }
        }
    }

    return result;
}

DataFrame ts_Cov(const DataFrame& s1, const DataFrame& s2, int n) {
    if (n <= 1) {
        // 协方差至少需要2个样本
        return DataFrame::Constant(s1.rows(), s1.cols(), std::numeric_limits<double>::quiet_NaN());
    }

    const int rows = s1.rows();
    const int cols = s1.cols();

    // 使用延迟求值创建结果矩阵
    DataFrame result = DataFrame::Constant(rows, cols, std::numeric_limits<double>::quiet_NaN());

    // 计算滚动均值
    DataFrame means1 = ts_Mean(s1, n);
    DataFrame means2 = ts_Mean(s2, n);

    // 对每一列计算滚动协方差
    #pragma omp parallel for collapse(2) if(rows * cols > 1000)
    for (int j = 0; j < cols; ++j) {
        for (int i = 0; i < rows; ++i) {
            if (isNaN(means1(i, j)) || isNaN(means2(i, j))) continue;

            int start = std::max(0, i - n + 1);

            // 使用Eigen向量化操作
            Eigen::VectorXd x_values(n);
            Eigen::VectorXd y_values(n);
            int valid_count = 0;

            // 收集窗口内的有效值
            for (int k = start; k <= i; ++k) {
                if (!isNaN(s1(k, j)) && !isNaN(s2(k, j))) {
                    x_values(valid_count) = s1(k, j) - means1(i, j);
                    y_values(valid_count) = s2(k, j) - means2(i, j);
                    valid_count++;
                }
            }

            if (valid_count > 1) {
                // 使用Eigen计算点积
                double sum_xy = x_values.head(valid_count).dot(y_values.head(valid_count));

                // 计算协方差（使用无偏估计）
                result(i, j) = sum_xy / (valid_count - 1);
            }
        }
    }

    return result;
}

DataFrame ts_Median(const DataFrame& s1, int n) {
    if (n <= 0) return s1;

    const int rows = s1.rows();
    const int cols = s1.cols();

    // 使用延迟求值创建结果矩阵
    DataFrame result = DataFrame::Constant(rows, cols, std::numeric_limits<double>::quiet_NaN());

    // 对每一列计算滚动中位数
    for (int j = 0; j < cols; ++j) {
        // 使用Eigen数组操作优化计算
        std::vector<double> window_values;
        window_values.reserve(n);

        for (int i = 0; i < rows; ++i) {
            int start = std::max(0, i - n + 1);
            window_values.clear();

            // 收集窗口内的有效值
            for (int k = start; k <= i; ++k) {
                if (!isNaN(s1(k, j))) {
                    window_values.push_back(s1(k, j));
                }
            }

            // 如果有有效值，计算中位数
            if (!window_values.empty()) {
                size_t size = window_values.size();
                if (size % 2 == 0) {
                    // 偶数个元素，取中间两个的平均值
                    std::nth_element(window_values.begin(), window_values.begin() + size/2 - 1, window_values.end());
                    double median1 = window_values[size/2 - 1];
                    std::nth_element(window_values.begin(), window_values.begin() + size/2, window_values.end());
                    double median2 = window_values[size/2];
                    result(i, j) = (median1 + median2) / 2.0;
                } else {
                    // 奇数个元素，取中间值
                    std::nth_element(window_values.begin(), window_values.begin() + size/2, window_values.end());
                    result(i, j) = window_values[size/2];
                }
            }
        }
    }

    return result;
}

DataFrame ts_Skewness(const DataFrame& s1, int n) {
    if (n <= 2) {
        n = 3; // 最小窗口大小为3
    }

    const int rows = s1.rows();
    const int cols = s1.cols();

    // 使用延迟求值创建结果矩阵
    DataFrame result = DataFrame::Constant(rows, cols, std::numeric_limits<double>::quiet_NaN());

    // 对每一列计算滚动偏度
    for (int j = 0; j < cols; ++j) {
        // 使用Eigen数组操作优化计算
        Eigen::Array<double, Eigen::Dynamic, 1> window_values(n);

        for (int i = 0; i < rows; ++i) {
            int start = std::max(0, i - n + 1);
            int valid_count = 0;

            // 收集窗口内的有效值
            for (int k = start; k <= i; ++k) {
                if (!isNaN(s1(k, j))) {
                    window_values(valid_count++) = s1(k, j);
                }
            }

            if (valid_count > 2) {
                // 使用Eigen计算均值
                double mean = window_values.head(valid_count).mean();

                // 计算中心矩
                double m2 = 0.0; // 二阶中心矩
                double m3 = 0.0; // 三阶中心矩

                for (int k = 0; k < valid_count; ++k) {
                    double dev = window_values(k) - mean;
                    double dev2 = dev * dev;
                    m2 += dev2;
                    m3 += dev2 * dev;
                }

                // 计算样本方差（无偏估计）
                double var = m2 / (valid_count - 1);
                double stddev = std::sqrt(var);

                if (stddev > 1e-10) {
                    // 计算偏度（Fisher's moment coefficient of skewness）
                    double skewness = (valid_count * m3) / ((valid_count - 1) * (valid_count - 2) * std::pow(stddev, 3));
                    result(i, j) = skewness;
                }
            }
        }
    }

    return result;
}

DataFrame ts_Kurtosis(const DataFrame& s1, int n) {
    if (n <= 3) {
        n = 4; // 最小窗口大小为4
    }

    const int rows = s1.rows();
    const int cols = s1.cols();

    // 使用延迟求值创建结果矩阵
    DataFrame result = DataFrame::Constant(rows, cols, std::numeric_limits<double>::quiet_NaN());

    // 对每一列计算滚动峰度
    for (int j = 0; j < cols; ++j) {
        // 使用Eigen数组操作优化计算
        Eigen::Array<double, Eigen::Dynamic, 1> window_values(n);

        for (int i = 0; i < rows; ++i) {
            int start = std::max(0, i - n + 1);
            int valid_count = 0;

            // 收集窗口内的有效值
            for (int k = start; k <= i; ++k) {
                if (!isNaN(s1(k, j))) {
                    window_values(valid_count++) = s1(k, j);
                }
            }

            if (valid_count > 3) {
                // 使用Eigen计算均值
                double mean = window_values.head(valid_count).mean();

                // 计算中心矩
                double m2 = 0.0; // 二阶中心矩
                double m4 = 0.0; // 四阶中心矩

                for (int k = 0; k < valid_count; ++k) {
                    double dev = window_values(k) - mean;
                    double dev2 = dev * dev;
                    m2 += dev2;
                    m4 += dev2 * dev2;
                }

                // 计算样本方差（无偏估计）
                double var = m2 / (valid_count - 1);

                if (var > 1e-10) {
                    // 计算峰度（与scipy.stats.kurtosis一致，使用Fisher定义）
                    double n = valid_count;
                    double k = (n * (n + 1) * (n - 1) * m4) / ((n - 2) * (n - 3) * m2 * m2) - 3 * (n - 1) * (n - 1) / ((n - 2) * (n - 3));

                    result(i, j) = k;
                }
            }
        }
    }

    return result;
}

DataFrame ts_Scale(const DataFrame& s1, int n) {
    if (n <= 1) {
        return s1;
    }

    const int rows = s1.rows();
    const int cols = s1.cols();

    // 使用延迟求值创建结果矩阵
    DataFrame result = DataFrame::Constant(rows, cols, std::numeric_limits<double>::quiet_NaN());

    // 计算滚动均值和标准差
    DataFrame means = ts_Mean(s1, n);
    DataFrame stdevs = ts_Stdev(s1, n);

    // 对每一列进行缩放
    for (int j = 0; j < cols; ++j) {
        for (int i = 0; i < rows; ++i) {
            if (!isNaN(means(i, j)) && !isNaN(stdevs(i, j)) && !isNaN(s1(i, j))) {
                if (stdevs(i, j) > 1e-10) {
                    // 标准化
                    result(i, j) = (s1(i, j) - means(i, j)) / stdevs(i, j);
                } else {
                    // 如果标准差接近零，则结果为0
                    result(i, j) = 0.0;
                }
            }
        }
    }

    return result;
}

DataFrame ts_Product(const DataFrame& s1, int n) {
    if (n < 3) {
        n = 3; // 最小窗口大小为3
    }

    const int rows = s1.rows();
    const int cols = s1.cols();

    // 使用延迟求值创建结果矩阵
    DataFrame result = DataFrame::Constant(rows, cols, std::numeric_limits<double>::quiet_NaN());

    // 对每一列计算滚动乘积
    for (int j = 0; j < cols; ++j) {
        for (int i = 0; i < rows; ++i) {
            int start = std::max(0, i - n + 1);
            double product = 1.0;
            bool hasValidValue = false;

            // 计算窗口内的乘积
            for (int k = start; k <= i; ++k) {
                if (!isNaN(s1(k, j))) {
                    product *= s1(k, j);
                    hasValidValue = true;
                }
            }

            if (hasValidValue) {
                result(i, j) = product;
            }
        }
    }

    return result;
}

DataFrame ts_TransNorm(const DataFrame& s1, int n) {
    if (n < 3) {
        n = 3; // 最小窗口大小为3
    }

    const int rows = s1.rows();
    const int cols = s1.cols();

    // 使用延迟求值创建结果矩阵
    DataFrame result = DataFrame::Constant(rows, cols, std::numeric_limits<double>::quiet_NaN());

    // 对每一列计算滚动排名并转换为正态分布
    for (int j = 0; j < cols; ++j) {
        for (int i = n - 1; i < rows; ++i) {
            int start = i - n + 1;
            std::vector<std::pair<double, int>> values;

            // 收集窗口内的非NaN值及其索引
            for (int k = start; k <= i; ++k) {
                if (!isNaN(s1(k, j))) {
                    values.push_back({s1(k, j), k - start});
                }
            }

            if (values.size() <= 1) continue;

            // 排序（升序）
            std::sort(values.begin(), values.end());

            // 计算排名（百分比形式）
            std::vector<double> ranks(n, std::numeric_limits<double>::quiet_NaN());

            for (size_t k = 0; k < values.size(); ++k) {
                int idx = values[k].second;
                ranks[idx] = (k + 1.0) / values.size();
            }

            // 调整排名，减去 1/n/2
            if (!isNaN(ranks[n - 1])) {
                double adjusted_rank = ranks[n - 1] - 1.0 / static_cast<double>(n) / 2.0;

                // 确保排名在有效范围内
                if (adjusted_rank <= 0.0) adjusted_rank = 0.0001;
                if (adjusted_rank >= 1.0) adjusted_rank = 0.9999;

                // 使用normInv函数，与Python的norm.ppf完全一致
                result(i, j) = normInv(adjusted_rank);
            }
        }
    }

    return result;
}

DataFrame ts_Decay(const DataFrame& s1, int n) {
    if (n <= 0) return s1;

    const int rows = s1.rows();
    const int cols = s1.cols();

    // 创建权重数组（与Python版本一致）
    Eigen::Array<double, Eigen::Dynamic, 1> weights(n);
    for (int i = 0; i < n; ++i) {
        weights(i) = 1.0 - 1.0 / n * i;
    }

    // 归一化权重
    double weightSum = weights.sum();
    weights /= weightSum;

    // 使用延迟求值创建结果矩阵
    DataFrame result = DataFrame::Constant(rows, cols, std::numeric_limits<double>::quiet_NaN());

    // 对每一列应用衰减加权
    for (int j = 0; j < cols; ++j) {
        for (int i = n - 1; i < rows; ++i) {
            double weightedSum = 0.0;
            double weightUsed = 0.0;
            bool hasValidValue = false;

            // 应用权重
            for (int k = 0; k < n; ++k) {
                int idx = i - k;
                if (idx >= 0 && !isNaN(s1(idx, j))) {
                    weightedSum += s1(idx, j) * weights(k);
                    weightUsed += weights(k);
                    hasValidValue = true;
                }
            }

            // 如果有有效值，计算加权平均
            if (hasValidValue && weightUsed > 1e-10) {
                result(i, j) = weightedSum / weightUsed;
            }
        }
    }

    return result;
}

DataFrame ts_Decay2(const DataFrame& s1, int n) {
    if (n <= 0) return s1;

    const int rows = s1.rows();
    const int cols = s1.cols();

    // 计算alpha参数（与Python版本一致）
    double alpha = 1.0 - 2.0 / (n + 1);

    // 创建权重数组
    Eigen::Array<double, Eigen::Dynamic, 1> weights(n);
    for (int i = 0; i < n; ++i) {
        weights(i) = std::pow(alpha, i);
    }

    // 归一化权重
    double weightSum = weights.sum();
    weights /= weightSum;

    // 使用延迟求值创建结果矩阵
    DataFrame result = DataFrame::Constant(rows, cols, std::numeric_limits<double>::quiet_NaN());

    // 对每一列应用衰减加权
    for (int j = 0; j < cols; ++j) {
        for (int i = n - 1; i < rows; ++i) {
            double weightedSum = 0.0;
            double weightUsed = 0.0;
            bool hasValidValue = false;

            // 应用权重
            for (int k = 0; k < n; ++k) {
                int idx = i - k;
                if (idx >= 0 && !isNaN(s1(idx, j))) {
                    weightedSum += s1(idx, j) * weights(k);
                    weightUsed += weights(k);
                    hasValidValue = true;
                }
            }

            // 如果有有效值，计算加权平均
            if (hasValidValue && weightUsed > 1e-10) {
                result(i, j) = weightedSum / weightUsed;
            }
        }
    }

    return result;
}

DataFrame ts_Partial_corr(const DataFrame& s1, const DataFrame& s2, const DataFrame& s3, int n) {
    if (n <= 2) {
        n = 3; // 最小窗口大小为3
    }

    // 计算相关系数
    DataFrame corr12 = ts_Corr(s1, s2, n);
    DataFrame corr13 = ts_Corr(s1, s3, n);
    DataFrame corr23 = ts_Corr(s2, s3, n);

    const int rows = s1.rows();
    const int cols = s1.cols();

    // 使用延迟求值创建结果矩阵
    DataFrame result = DataFrame::Constant(rows, cols, std::numeric_limits<double>::quiet_NaN());

    // 计算偏相关系数
    for (int i = 0; i < rows; ++i) {
        for (int j = 0; j < cols; ++j) {
            if (!isNaN(corr12(i, j)) && !isNaN(corr13(i, j)) && !isNaN(corr23(i, j))) {
                double denom1 = 1.0 - corr13(i, j) * corr13(i, j);
                double denom2 = 1.0 - corr23(i, j) * corr23(i, j);

                if (denom1 > 1e-10 && denom2 > 1e-10) {
                    double partial_corr = (corr12(i, j) - corr13(i, j) * corr23(i, j)) /
                                         (std::sqrt(denom1) * std::sqrt(denom2));

                    // 处理无穷大值
                    if (std::isinf(partial_corr)) {
                        result(i, j) = std::numeric_limits<double>::quiet_NaN();
                    } else {
                        result(i, j) = partial_corr;
                    }
                }
            }
        }
    }

    return result;
}

DataFrame ts_Regression(const DataFrame& s1, const DataFrame& s2, int n, char rettype) {
    if (n <= 3) {
        n = 3; // 最小窗口大小为3
    }

    const int rows = s1.rows();
    const int cols = s1.cols();

    // 创建临时数据，处理NaN值
    DataFrame tem1 = s1;
    DataFrame tem2 = s2;

    // 对每一列处理NaN值
    for (int i = 0; i < rows; ++i) {
        for (int j = 0; j < cols; ++j) {
            if (isNaN(s1(i, j))) {
                tem2(i, j) = std::numeric_limits<double>::quiet_NaN();
            }
            if (isNaN(s2(i, j))) {
                tem1(i, j) = std::numeric_limits<double>::quiet_NaN();
            }
        }
    }

    // 计算滚动均值
    DataFrame tem1_m = ts_Mean(tem1, n);
    DataFrame tem2_m = ts_Mean(tem2, n);

    // 计算协方差
    DataFrame cov = ts_Cov(tem1, tem2, n);

    // 计算方差
    DataFrame tem2_var = DataFrame::Constant(rows, cols, std::numeric_limits<double>::quiet_NaN());

    // 对每一列计算滚动方差
    for (int j = 0; j < cols; ++j) {
        for (int i = 0; i < rows; ++i) {
            int start = std::max(0, i - n + 1);
            int count = 0;
            double sum_sq_diff = 0.0;

            // 计算平方差之和
            for (int k = start; k <= i; ++k) {
                if (!isNaN(tem2(k, j)) && !isNaN(tem2_m(i, j))) {
                    double diff = tem2(k, j) - tem2_m(i, j);
                    sum_sq_diff += diff * diff;
                    count++;
                }
            }

            if (count > 0) {
                tem2_var(i, j) = sum_sq_diff / count;
            }
        }
    }

    // 计算beta
    DataFrame beta = DataFrame::Constant(rows, cols, std::numeric_limits<double>::quiet_NaN());
    for (int i = 0; i < rows; ++i) {
        for (int j = 0; j < cols; ++j) {
            if (!isNaN(cov(i, j)) && !isNaN(tem2_var(i, j)) && tem2_var(i, j) > 1e-10) {
                beta(i, j) = cov(i, j) / tem2_var(i, j);
            }
        }
    }

    // 如果返回beta
    if (rettype == 'a' || rettype == 'A') {
        return beta;
    }

    // 计算alpha (const)
    DataFrame alpha = DataFrame::Constant(rows, cols, std::numeric_limits<double>::quiet_NaN());
    for (int i = 0; i < rows; ++i) {
        for (int j = 0; j < cols; ++j) {
            if (!isNaN(tem1_m(i, j)) && !isNaN(tem2_m(i, j)) && !isNaN(beta(i, j))) {
                alpha(i, j) = tem1_m(i, j) - beta(i, j) * tem2_m(i, j);
            }
        }
    }

    // 如果返回alpha
    if (rettype == 'b' || rettype == 'B') {
        return alpha;
    }

    // 计算拟合值
    DataFrame y_est = DataFrame::Constant(rows, cols, std::numeric_limits<double>::quiet_NaN());
    for (int i = 0; i < rows; ++i) {
        for (int j = 0; j < cols; ++j) {
            if (!isNaN(alpha(i, j)) && !isNaN(beta(i, j)) && !isNaN(tem2(i, j))) {
                y_est(i, j) = alpha(i, j) + beta(i, j) * tem2(i, j);
            }
        }
    }

    // 如果返回拟合值
    if (rettype == 'c' || rettype == 'C') {
        return y_est;
    }

    // 计算残差
    DataFrame resid = DataFrame::Constant(rows, cols, std::numeric_limits<double>::quiet_NaN());
    for (int i = 0; i < rows; ++i) {
        for (int j = 0; j < cols; ++j) {
            if (!isNaN(tem1(i, j)) && !isNaN(y_est(i, j))) {
                resid(i, j) = tem1(i, j) - y_est(i, j);
            }
        }
    }

    // 如果返回残差
    if (rettype == 'd' || rettype == 'D') {
        return resid;
    }

    // 计算相关系数
    if (rettype == 'r' || rettype == 'R') {
        return ts_Corr(tem1, tem2, n);
    }

    // 默认返回beta
    return beta;
}

DataFrame ts_Entropy(const DataFrame& s1, int n) {
    if (n <= 1) {
        return DataFrame::Constant(s1.rows(), s1.cols(), std::numeric_limits<double>::quiet_NaN());
    }

    const int rows = s1.rows();
    const int cols = s1.cols();
    const int bucket = 10; // 与Python版本一致

    // 使用延迟求值创建结果矩阵
    DataFrame result = DataFrame::Constant(rows, cols, std::numeric_limits<double>::quiet_NaN());

    // 对每一列计算滚动熵
    for (int j = 0; j < cols; ++j) {
        for (int i = 0; i < rows; ++i) {
            int start = std::max(0, i - n + 1);
            std::vector<double> values;

            // 收集窗口内的非NaN值
            for (int k = start; k <= i; ++k) {
                if (!isNaN(s1(k, j))) {
                    values.push_back(s1(k, j));
                }
            }

            if (values.size() > 2) {
                // 计算直方图
                std::vector<double> hist(bucket, 0.0);
                double min_val = *std::min_element(values.begin(), values.end());
                double max_val = *std::max_element(values.begin(), values.end());
                double range = max_val - min_val;

                if (range < 1e-10) {
                    // 如果所有值都相同，熵为0
                    result(i, j) = 0.0;
                    continue;
                }

                // 计算直方图
                for (double val : values) {
                    int bin = std::min(bucket - 1, static_cast<int>((val - min_val) / range * bucket));
                    hist[bin] += 1.0;
                }

                // 归一化直方图
                for (int b = 0; b < bucket; ++b) {
                    hist[b] /= values.size();
                }

                // 计算熵
                double entropy = std::log(bucket);
                for (int b = 0; b < bucket; ++b) {
                    if (hist[b] > 0.0) {
                        entropy -= hist[b] * std::log(hist[b]);
                    }
                }

                result(i, j) = entropy;
            }
        }
    }

    return result;
}

DataFrame ts_MaxDD(const DataFrame& s1, int n) {
    if (n <= 1) {
        return DataFrame::Constant(s1.rows(), s1.cols(), std::numeric_limits<double>::quiet_NaN());
    }

    const int rows = s1.rows();
    const int cols = s1.cols();

    // 使用延迟求值创建结果矩阵
    DataFrame result = DataFrame::Constant(rows, cols, std::numeric_limits<double>::quiet_NaN());

    // 对每一列计算滚动最大回撤
    for (int j = 0; j < cols; ++j) {
        for (int i = 0; i < rows; ++i) {
            int start = std::max(0, i - n + 1);
            std::vector<double> values;

            // 收集窗口内的非NaN值
            for (int k = start; k <= i; ++k) {
                if (!isNaN(s1(k, j))) {
                    values.push_back(s1(k, j));
                }
            }

            if (values.size() > 1) {
                // 计算最大回撤
                double max_dd = 0.0;
                double peak = values[0];

                for (size_t k = 1; k < values.size(); ++k) {
                    if (values[k] > peak) {
                        peak = values[k];
                    } else {
                        double dd = (peak - values[k]) / peak;
                        if (dd > max_dd) {
                            max_dd = dd;
                        }
                    }
                }

                result(i, j) = max_dd;
            }
        }
    }

    return result;
}

DataFrame ts_MeanChg(const DataFrame& s1, int n) {
    if (n < 3) n = 3;

    const int rows = s1.rows();
    const int cols = s1.cols();

    // 使用延迟求值创建结果矩阵
    DataFrame result = DataFrame::Constant(rows, cols, std::numeric_limits<double>::quiet_NaN());

    // 计算当前均值
    DataFrame mean_current = ts_Mean(s1, n);

    // 计算衰减均值
    DataFrame decay = ts_Decay(s1, n);

    // 计算均值变化
    for (int i = 0; i < rows; ++i) {
        for (int j = 0; j < cols; ++j) {
            if (!isNaN(mean_current(i, j)) && !isNaN(decay(i, j))) {
                result(i, j) = mean_current(i, j) - decay(i, j);
            }
        }
    }

    return result;
}

DataFrame ts_Quantile(const DataFrame& s1, int n, double q) {
    if (n < 5) {
        n = 5; // 最小窗口大小为5
    }

    const int rows = s1.rows();
    const int cols = s1.cols();

    // 使用延迟求值创建结果矩阵
    DataFrame result = DataFrame::Constant(rows, cols, std::numeric_limits<double>::quiet_NaN());

    // 确定分位数
    double quantile = 0.5; // 默认为中位数
    if (q == 'A' || q == 'a' || q == 0.2) {
        quantile = 0.2;
    } else if (q == 'B' || q == 'b' || q == 0.4) {
        quantile = 0.4;
    } else if (q == 'C' || q == 'c' || q == 0.6) {
        quantile = 0.6;
    } else if (q == 'D' || q == 'd' || q == 0.8) {
        quantile = 0.8;
    }

    // 对每一列计算滚动分位数
    for (int j = 0; j < cols; ++j) {
        for (int i = 0; i < rows; ++i) {
            int start = std::max(0, i - n + 1);
            std::vector<double> values;

            // 收集窗口内的非NaN值
            for (int k = start; k <= i; ++k) {
                if (!isNaN(s1(k, j))) {
                    values.push_back(s1(k, j));
                }
            }

            if (!values.empty()) {
                // 计算分位数
                size_t idx = static_cast<size_t>(quantile * values.size());
                if (idx >= values.size()) {
                    idx = values.size() - 1;
                }
                std::nth_element(values.begin(), values.begin() + idx, values.end());
                result(i, j) = values[idx];
            }
        }
    }

    return result;
}

} // namespace optimized_operators
