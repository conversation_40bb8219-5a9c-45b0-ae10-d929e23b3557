#include "optimized_operators.h"
#include <iostream>
#include <iomanip>
#include <vector>
#include <string>
#include <cmath>
#include <random>
#include <fstream>
#include <sstream>
#include <filesystem>
#include <chrono>

using namespace optimized_operators;
namespace fs = std::filesystem;

// 从CSV文件加载数据
DataFrame loadCsv(const std::string& filepath) {
    std::ifstream file(filepath);
    if (!file.is_open()) {
        std::cerr << "无法打开文件: " << filepath << std::endl;
        return DataFrame();
    }

    std::vector<std::vector<double>> data;
    std::string line;

    // 跳过标题行
    std::getline(file, line);

    // 读取数据行
    while (std::getline(file, line)) {
        std::vector<double> row;
        std::stringstream ss(line);
        std::string cell;

        // 跳过第一列（日期）
        std::getline(ss, cell, ',');

        // 读取数据列
        while (std::getline(ss, cell, ',')) {
            try {
                double value = std::stod(cell);
                row.push_back(value);
            } catch (const std::exception& e) {
                row.push_back(std::numeric_limits<double>::quiet_NaN());
            }
        }

        if (!row.empty()) {
            data.push_back(row);
        }
    }

    // 创建Eigen矩阵
    int rows = data.size();
    int cols = rows > 0 ? data[0].size() : 0;

    DataFrame matrix(rows, cols);
    for (int i = 0; i < rows; ++i) {
        for (int j = 0; j < cols; ++j) {
            matrix(i, j) = data[i][j];
        }
    }

    return matrix;
}

// 将矩阵转换为JSON数组
std::string matrixToJson(const DataFrame& matrix) {
    std::stringstream ss;
    ss << "[";

    for (int i = 0; i < matrix.rows(); ++i) {
        ss << "[";
        for (int j = 0; j < matrix.cols(); ++j) {
            if (isNaN(matrix(i, j)) || std::isinf(matrix(i, j))) {
                ss << "null";
            } else {
                // 使用固定精度格式化浮点数，避免过长的数字导致JSON解析错误
                ss << std::fixed << std::setprecision(10) << matrix(i, j);
            }

            if (j < matrix.cols() - 1) {
                ss << ",";
            }
        }
        ss << "]";

        if (i < matrix.rows() - 1) {
            ss << ",";
        }
    }

    ss << "]";
    return ss.str();
}

// 将结果保存为JSON文件
void saveResultsToJson(const std::map<std::string, DataFrame>& results, const std::string& filepath) {
    std::ofstream file(filepath);
    if (!file.is_open()) {
        std::cerr << "无法创建文件: " << filepath << std::endl;
        return;
    }

    file << "{";

    bool first = true;
    for (const auto& pair : results) {
        if (!first) {
            file << ",";
        }
        first = false;

        file << "\"" << pair.first << "\":" << matrixToJson(pair.second);
    }

    file << "}";
    file.close();

    std::cout << "结果已保存到: " << filepath << std::endl;
}

// 将结果和时间保存为JSON文件
void saveResultsToJson(const std::map<std::string, DataFrame>& results,
                       const std::map<std::string, double>& timings,
                       const std::string& filepath) {
    std::ofstream file(filepath);
    if (!file.is_open()) {
        std::cerr << "无法创建文件: " << filepath << std::endl;
        return;
    }

    file << "{";

    bool first = true;
    for (const auto& pair : results) {
        if (!first) {
            file << ",";
        }
        first = false;

        file << "\"" << pair.first << "\":{";
        file << "\"data\":" << matrixToJson(pair.second);

        // 添加时间信息
        if (timings.find(pair.first) != timings.end()) {
            file << ",\"time\":" << timings.at(pair.first);
        }

        file << "}";
    }

    file << "}";
    file.close();

    std::cout << "结果已保存到: " << filepath << std::endl;
}

// 测量函数执行时间的辅助函数
template<typename Func>
double measureExecutionTime(Func func) {
    auto start = std::chrono::high_resolution_clock::now();
    func();
    auto end = std::chrono::high_resolution_clock::now();
    std::chrono::duration<double, std::milli> duration = end - start;
    return duration.count();
}

int main(int argc, char* argv[]) {
    std::string dataDir;
    std::string outputFile;

    if (argc < 3) {
        std::cerr << "用法: " << argv[0] << " <数据目录> <输出文件>" << std::endl;
        std::cerr << "使用默认值..." << std::endl;
        dataDir = "/tmp/tmpdir/data";
        outputFile = "/tmp/tmpdir/optimized_operator_results.json";
    } else {
        dataDir = argv[1];
        outputFile = argv[2];
    }

    // 确保输出目录存在
    fs::path outputPath(outputFile);
    fs::create_directories(outputPath.parent_path());

    std::cout << "从目录加载数据: " << dataDir << std::endl;

    // 加载数据
    DataFrame open = loadCsv(dataDir + "/open.csv");
    DataFrame high = loadCsv(dataDir + "/high.csv");
    DataFrame low = loadCsv(dataDir + "/low.csv");
    DataFrame close = loadCsv(dataDir + "/close.csv");
    DataFrame volume = loadCsv(dataDir + "/volume.csv");
    DataFrame amount = loadCsv(dataDir + "/amount.csv");
    DataFrame vwap = loadCsv(dataDir + "/vwap.csv");

    if (open.rows() == 0 || high.rows() == 0 || low.rows() == 0 || close.rows() == 0 ||
        volume.rows() == 0 || amount.rows() == 0 || vwap.rows() == 0) {
        std::cerr << "加载数据失败!" << std::endl;
        return 1;
    }

    std::cout << "数据加载成功: " << open.rows() << " 行, " << open.cols() << " 列" << std::endl;

    // 计算各种算子
    std::map<std::string, DataFrame> results;
    std::map<std::string, double> timings;

    // 基本算术运算
    timings["add"] = measureExecutionTime([&]() { results["add"] = Add(close, open); });
    timings["minus"] = measureExecutionTime([&]() { results["minus"] = Minus(close, open); });
    timings["multiply"] = measureExecutionTime([&]() { results["multiply"] = Multiply(close, volume); });
    timings["divide"] = measureExecutionTime([&]() { results["divide"] = Divide(close, open); });
    timings["sqrt"] = measureExecutionTime([&]() { results["sqrt"] = Sqrt(volume); });
    timings["log"] = measureExecutionTime([&]() { results["log"] = Log(volume); });
    timings["abs"] = measureExecutionTime([&]() { results["abs"] = Abs(Minus(close, open)); });
    timings["max"] = measureExecutionTime([&]() { results["max"] = Max(close, open); });
    timings["min"] = measureExecutionTime([&]() { results["min"] = Min(close, open); });
    timings["equal"] = measureExecutionTime([&]() { results["equal"] = Equal(close, open); });
    timings["power"] = measureExecutionTime([&]() { results["power"] = Power(volume, 2.0); });
    timings["sign"] = measureExecutionTime([&]() { results["sign"] = Sign(Minus(close, open)); });
    timings["reverse"] = measureExecutionTime([&]() { results["reverse"] = Reverse(close); });
    timings["exp"] = measureExecutionTime([&]() { results["exp"] = Exp(Log(volume)); });

    // 逻辑运算
    timings["and"] = measureExecutionTime([&]() { results["and"] = And(close, open); });
    timings["or"] = measureExecutionTime([&]() { results["or"] = Or(close, open); });
    timings["not"] = measureExecutionTime([&]() { results["not"] = Not(close); });
    timings["xor"] = measureExecutionTime([&]() { results["xor"] = Xor(close, open); });
    timings["mthan"] = measureExecutionTime([&]() { results["mthan"] = Mthan(close, open); });
    timings["methan"] = measureExecutionTime([&]() { results["methan"] = MEthan(close, open); });
    timings["lthan"] = measureExecutionTime([&]() { results["lthan"] = Lthan(close, open); });
    timings["lethan"] = measureExecutionTime([&]() { results["lethan"] = LEthan(close, open); });
    timings["unequal"] = measureExecutionTime([&]() { results["unequal"] = UnEqual(close, open); });

    // 时间序列运算
    timings["ts_delay"] = measureExecutionTime([&]() { results["ts_delay"] = ts_Delay(close, 1); });
    timings["ts_mean"] = measureExecutionTime([&]() { results["ts_mean"] = ts_Mean(close, 10); });
    timings["ts_sum"] = measureExecutionTime([&]() { results["ts_sum"] = ts_Sum(volume, 10); });
    timings["ts_min"] = measureExecutionTime([&]() { results["ts_min"] = ts_Min(close, 10); });
    timings["ts_max"] = measureExecutionTime([&]() { results["ts_max"] = ts_Max(close, 10); });
    timings["ts_delta"] = measureExecutionTime([&]() { results["ts_delta"] = ts_Delta(close, 10); });
    timings["ts_divide"] = measureExecutionTime([&]() { results["ts_divide"] = ts_Divide(close, 10); });
    timings["ts_chgrate"] = measureExecutionTime([&]() { results["ts_chgrate"] = ts_ChgRate(close, 10); });
    timings["ts_rank"] = measureExecutionTime([&]() { results["ts_rank"] = ts_Rank(close, 10); });
    timings["ts_argmax"] = measureExecutionTime([&]() { results["ts_argmax"] = ts_ArgMax(close, 10); });
    timings["ts_argmin"] = measureExecutionTime([&]() { results["ts_argmin"] = ts_ArgMin(close, 10); });
    timings["ts_corr"] = measureExecutionTime([&]() { results["ts_corr"] = ts_Corr(close, open, 10); });
    timings["ts_cov"] = measureExecutionTime([&]() { results["ts_cov"] = ts_Cov(close, open, 10); });
    timings["ts_median"] = measureExecutionTime([&]() { results["ts_median"] = ts_Median(close, 10); });
    timings["ts_skewness"] = measureExecutionTime([&]() { results["ts_skewness"] = ts_Skewness(close, 10); });
    timings["ts_kurtosis"] = measureExecutionTime([&]() { results["ts_kurtosis"] = ts_Kurtosis(close, 10); });
    timings["ts_scale"] = measureExecutionTime([&]() { results["ts_scale"] = ts_Scale(close, 10); });
    timings["ts_product"] = measureExecutionTime([&]() { results["ts_product"] = ts_Product(close, 10); });
    timings["ts_transnorm"] = measureExecutionTime([&]() { results["ts_transnorm"] = ts_TransNorm(close, 10); });
    timings["ts_decay"] = measureExecutionTime([&]() { results["ts_decay"] = ts_Decay(close, 10); });
    timings["ts_decay2"] = measureExecutionTime([&]() { results["ts_decay2"] = ts_Decay2(close, 10); });
    timings["ts_partial_corr"] = measureExecutionTime([&]() { results["ts_partial_corr"] = ts_Partial_corr(close, open, high, 10); });
    timings["ts_regression"] = measureExecutionTime([&]() { results["ts_regression"] = ts_Regression(close, open, 10, 'a'); });
    timings["ts_entropy"] = measureExecutionTime([&]() { results["ts_entropy"] = ts_Entropy(close, 10); });
    timings["ts_maxdd"] = measureExecutionTime([&]() { results["ts_maxdd"] = ts_MaxDD(close, 10); });
    timings["ts_meanchg"] = measureExecutionTime([&]() { results["ts_meanchg"] = ts_MeanChg(close, 10); });
    timings["ts_quantile"] = measureExecutionTime([&]() { results["ts_quantile"] = ts_Quantile(close, 10, 0.5); });

    // Tot系列函数
    timings["tot_mean"] = measureExecutionTime([&]() { results["tot_mean"] = Tot_Mean(close); });
    timings["tot_sum"] = measureExecutionTime([&]() { results["tot_sum"] = Tot_Sum(volume); });
    timings["tot_min"] = measureExecutionTime([&]() { results["tot_min"] = Tot_Min(close); });
    timings["tot_max"] = measureExecutionTime([&]() { results["tot_max"] = Tot_Max(close); });
    timings["tot_delta"] = measureExecutionTime([&]() { results["tot_delta"] = Tot_Delta(close); });
    timings["tot_divide"] = measureExecutionTime([&]() { results["tot_divide"] = Tot_Divide(close); });
    timings["tot_chgrate"] = measureExecutionTime([&]() { results["tot_chgrate"] = Tot_ChgRate(close); });
    timings["tot_rank"] = measureExecutionTime([&]() { results["tot_rank"] = Tot_Rank(volume); });
    timings["tot_argmax"] = measureExecutionTime([&]() { results["tot_argmax"] = Tot_ArgMax(close); });
    timings["tot_argmin"] = measureExecutionTime([&]() { results["tot_argmin"] = Tot_ArgMin(close); });

    // 面板运算
    timings["pn_mean"] = measureExecutionTime([&]() { results["pn_mean"] = pn_Mean(close); });
    timings["pn_rank"] = measureExecutionTime([&]() { results["pn_rank"] = pn_Rank(close); });
    timings["pn_transnorm"] = measureExecutionTime([&]() { results["pn_transnorm"] = pn_TransNorm(close); });
    timings["pn_rank2"] = measureExecutionTime([&]() { results["pn_rank2"] = pn_Rank2(close); });
    timings["pn_rankcentered"] = measureExecutionTime([&]() { results["pn_rankcentered"] = pn_RankCentered(close); });
    timings["pn_fillmax"] = measureExecutionTime([&]() { results["pn_fillmax"] = pn_FillMax(close); });
    timings["pn_fillmin"] = measureExecutionTime([&]() { results["pn_fillmin"] = pn_FillMin(close); });
    timings["pn_transstd"] = measureExecutionTime([&]() { results["pn_transstd"] = pn_TransStd(close); });
    timings["pn_stand"] = measureExecutionTime([&]() { results["pn_stand"] = pn_Stand(close); });
    timings["pn_winsor"] = measureExecutionTime([&]() { results["pn_winsor"] = pn_Winsor(close, 3.0); });
    timings["pn_cut"] = measureExecutionTime([&]() { results["pn_cut"] = pn_Cut(close); });

    // 创建一个简单的分组矩阵用于测试
    DataFrame group = DataFrame::Zero(close.rows(), close.cols());
    for (int j = 0; j < close.cols(); ++j) {
        group.col(j).setConstant(j % 5); // 将列分为5组
    }

    timings["pn_grouprank"] = measureExecutionTime([&]() { results["pn_grouprank"] = pn_GroupRank(close, group); });
    timings["pn_groupnorm"] = measureExecutionTime([&]() { results["pn_groupnorm"] = pn_GroupNorm(close, group); });
    timings["pn_groupneutral"] = measureExecutionTime([&]() { results["pn_groupneutral"] = pn_GroupNeutral(close, group); });
    timings["pn_crossfit"] = measureExecutionTime([&]() { results["pn_crossfit"] = pn_CrossFit(close, open); });

    // 保存结果和时间
    saveResultsToJson(results, timings, outputFile);

    // 打印执行时间
    std::cout << "算子执行时间统计 (毫秒):" << std::endl;
    for (const auto& pair : timings) {
        std::cout << std::setw(20) << pair.first << ": " << std::fixed << std::setprecision(3) << pair.second << " ms" << std::endl;
    }

    std::cout << "计算完成!" << std::endl;
    return 0;
}
