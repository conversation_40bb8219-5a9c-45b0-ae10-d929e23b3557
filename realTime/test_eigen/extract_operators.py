#!/usr/bin/env python3
import re
import os
import json

def extract_operators_from_header(header_file):
    """Extract operator function names from a C++ header file."""
    operators = []
    
    with open(header_file, 'r') as f:
        content = f.read()
    
    # Regular expression to match function declarations
    # This pattern looks for return type, function name, and parameters
    pattern = r'DataFrame\s+([A-Za-z0-9_]+)\s*\('
    
    matches = re.findall(pattern, content)
    operators.extend(matches)
    
    return sorted(set(operators))

def extract_operators_from_python(python_file):
    """Extract operator function names from a Python file."""
    operators = []
    
    with open(python_file, 'r') as f:
        content = f.read()
    
    # Regular expression to match function definitions
    # This pattern looks for 'def' followed by function name
    pattern = r'def\s+([A-Za-z0-9_]+)\s*\('
    
    matches = re.findall(pattern, content)
    operators.extend(matches)
    
    return sorted(set(operators))

def main():
    # Define paths to header and Python files
    unoptimized_header = "/home/<USER>/git/realTime/test_eigen/feature_operators.h"
    optimized_header = "/home/<USER>/git/realTime/test_eigen/optimized_operators.h"
    python_file = "/home/<USER>/git/realTime/Code/strategy/feature_operator_funcs.py"
    
    # Extract operators
    unoptimized_operators = extract_operators_from_header(unoptimized_header)
    optimized_operators = extract_operators_from_header(optimized_header)
    python_operators = extract_operators_from_python(python_file)
    
    # Find common operators
    common_operators = set(unoptimized_operators) & set(optimized_operators) & set(python_operators)
    
    # Find operators missing in each implementation
    missing_in_optimized = set(unoptimized_operators) - set(optimized_operators)
    missing_in_python = set(unoptimized_operators) - set(python_operators)
    
    # Print results
    print(f"Total operators in unoptimized C++: {len(unoptimized_operators)}")
    print(f"Total operators in optimized C++: {len(optimized_operators)}")
    print(f"Total operators in Python: {len(python_operators)}")
    print(f"Common operators across all implementations: {len(common_operators)}")
    
    print("\nOperators missing in optimized C++:")
    for op in sorted(missing_in_optimized):
        print(f"  - {op}")
    
    print("\nOperators missing in Python:")
    for op in sorted(missing_in_python):
        print(f"  - {op}")
    
    # Save the list of common operators to a file
    with open("/home/<USER>/git/realTime/test_eigen/common_operators.json", "w") as f:
        json.dump(list(common_operators), f, indent=2)
    
    # Save the list of all operators to a file
    all_operators = {
        "unoptimized": unoptimized_operators,
        "optimized": optimized_operators,
        "python": python_operators,
        "common": list(common_operators)
    }
    with open("/home/<USER>/git/realTime/test_eigen/all_operators.json", "w") as f:
        json.dump(all_operators, f, indent=2)
    
    print(f"\nOperator lists saved to common_operators.json and all_operators.json")

if __name__ == "__main__":
    main()
