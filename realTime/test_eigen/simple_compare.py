#!/usr/bin/env python3
import os
import pandas as pd
import numpy as np
import glob

def load_csv(filepath):
    """加载 CSV 文件到 pandas DataFrame。"""
    try:
        # 首先尝试直接加载，不使用索引和标题，强制所有列为浮点数
        df = pd.read_csv(filepath, header=None, dtype=float)
        return df
    except Exception as e:
        try:
            # 如果失败，尝试使用索引加载
            df = pd.read_csv(filepath, index_col=0)
            # 尝试将所有列转换为浮点数
            df = df.astype(float)
            return df
        except Exception as e2:
            try:
                # 如果仍然失败，尝试先加载然后手动处理
                df = pd.read_csv(filepath)
                # 如果第一列看起来像索引，则将其设置为索引
                if df.columns[0] == 'Unnamed: 0' or df.columns[0] == 'index':
                    df = df.set_index(df.columns[0])
                # 尝试将所有列转换为浮点数
                df = df.astype(float)
                return df
            except Exception as e3:
                print(f"加载 {filepath} 出错: {e} -> {e2} -> {e3}")
                return None

def compare_files(file1, file2, tolerance=1e-10):
    """比较两个 CSV 文件，检查它们是否在容差范围内相等。"""
    df1 = load_csv(file1)
    df2 = load_csv(file2)

    if df1 is None or df2 is None:
        return False, "文件加载失败", {"nan_count": 0, "inf_count": 0, "valid_count": 0, "total_count": 0, "max_diff": float('inf'), "mean_diff": float('inf')}

    # 处理形状不匹配的问题
    if df1.shape != df2.shape:
        # 尝试找到共同的行和列
        if df1.shape[0] > 0 and df2.shape[0] > 0 and df1.shape[1] > 0 and df2.shape[1] > 0:
            # 取最小的行数和列数
            min_rows = min(df1.shape[0], df2.shape[0])
            min_cols = min(df1.shape[1], df2.shape[1])

            # 裁剪两个 DataFrame 到相同的大小
            df1 = df1.iloc[:min_rows, :min_cols]
            df2 = df2.iloc[:min_rows, :min_cols]

            print(f"裁剪 DataFrame 到相同大小: {df1.shape}")
        else:
            return False, f"形状不匹配且无法裁剪: {df1.shape} vs {df2.shape}", {"nan_count": 0, "inf_count": 0, "valid_count": 0, "total_count": 0, "max_diff": float('inf'), "mean_diff": float('inf')}

    # 再次检查形状
    if df1.shape != df2.shape:
        return False, f"形状不匹配: {df1.shape} vs {df2.shape}", {"nan_count": 0, "inf_count": 0, "valid_count": 0, "total_count": 0, "max_diff": float('inf'), "mean_diff": float('inf')}

    # 转换为 numpy 数组
    arr1 = df1.values
    arr2 = df2.values
    total_count = arr1.size

    # 检查 NaN 和 Inf
    nan_mask1 = np.isnan(arr1)
    nan_mask2 = np.isnan(arr2)
    inf_mask1 = np.isinf(arr1)
    inf_mask2 = np.isinf(arr2)

    # 检查 NaN 和 Inf 是否一致
    nan_diff_mask = nan_mask1 != nan_mask2
    inf_diff_mask = inf_mask1 != inf_mask2
    nan_diff = np.sum(nan_diff_mask)
    inf_diff = np.sum(inf_diff_mask)

    # 统计 NaN 和 Inf 的数量
    nan_count = np.sum(nan_mask1 | nan_mask2)
    inf_count = np.sum(inf_mask1 | inf_mask2)

    # 比较有效值
    valid_mask = ~(nan_mask1 | nan_mask2 | inf_mask1 | inf_mask2)
    valid_count = np.sum(valid_mask)

    # 计算详细统计信息
    stats = {
        "nan_count": int(nan_count),
        "inf_count": int(inf_count),
        "valid_count": int(valid_count),
        "total_count": int(total_count),
        "nan_diff": int(nan_diff),
        "inf_diff": int(inf_diff)
    }

    if nan_diff > 0:
        stats["max_diff"] = float('inf')
        stats["mean_diff"] = float('inf')
        return False, f"NaN 不一致: {nan_diff} 个位置", stats

    if inf_diff > 0:
        stats["max_diff"] = float('inf')
        stats["mean_diff"] = float('inf')
        return False, f"Inf 不一致: {inf_diff} 个位置", stats

    if valid_count == 0:
        stats["max_diff"] = 0
        stats["mean_diff"] = 0
        return True, "所有值都是 NaN 或 Inf，且一致", stats

    # 计算差异
    diff = np.abs(arr1[valid_mask] - arr2[valid_mask])
    max_diff = np.max(diff)
    mean_diff = np.mean(diff)

    stats["max_diff"] = float(max_diff)
    stats["mean_diff"] = float(mean_diff)

    if max_diff <= tolerance:
        return True, f"最大差异: {max_diff:.10e}", stats
    else:
        return False, f"最大差异: {max_diff:.10e} > 容差 {tolerance:.10e}", stats

def main():
    # 定义路径
    base_dir = "/home/<USER>/git/realTime/test_eigen/test_results"
    unoptimized_dir = os.path.join(base_dir, "unoptimized")
    optimized_dir = os.path.join(base_dir, "optimized")
    python_dir = os.path.join(base_dir, "python")

    # 获取所有目录中的所有可用因子
    unopt_files = glob.glob(os.path.join(unoptimized_dir, "*.csv"))
    opt_files = glob.glob(os.path.join(optimized_dir, "*.csv"))
    py_files = glob.glob(os.path.join(python_dir, "*.csv"))

    # 提取因子名称
    unopt_factors = [os.path.splitext(os.path.basename(f))[0] for f in unopt_files]
    opt_factors = [os.path.splitext(os.path.basename(f))[0] for f in opt_files]
    py_factors = [os.path.splitext(os.path.basename(f))[0] for f in py_files]

    # 合并所有因子
    all_factors = sorted(set(unopt_factors + opt_factors + py_factors))

    print(f"在所有实现中找到 {len(all_factors)} 个唯一因子")
    print(f"未优化 C++: {len(unopt_factors)} 个因子")
    print(f"优化 C++: {len(opt_factors)} 个因子")
    print(f"Python: {len(py_factors)} 个因子")

    # 比较结果
    print("\n比较结果:")
    print("=" * 120)
    print(f"{'因子':<20} {'未优化 C++':<10} {'优化 C++':<10} {'Python':<10} {'未优化 vs 优化':<25} {'未优化 vs Python':<25} {'优化 vs Python':<25}")
    print("-" * 120)

    # 统计
    unopt_vs_opt_matches = 0
    unopt_vs_py_matches = 0
    opt_vs_py_matches = 0
    unopt_vs_opt_total = 0
    unopt_vs_py_total = 0
    opt_vs_py_total = 0

    # 详细统计
    nan_diff_factors = []  # NaN 不一致的因子
    inf_diff_factors = []  # Inf 不一致的因子
    all_nan_inf_factors = []  # 全是 NaN 或 Inf 的因子
    value_diff_factors = []  # 值不一致的因子

    # 保存结果到文件
    with open(os.path.join(base_dir, "comparison_results.txt"), "w") as f:
        f.write(f"在所有实现中找到 {len(all_factors)} 个唯一因子\n")
        f.write(f"未优化 C++: {len(unopt_factors)} 个因子\n")
        f.write(f"优化 C++: {len(opt_factors)} 个因子\n")
        f.write(f"Python: {len(py_factors)} 个因子\n\n")

        f.write("比较结果:\n")
        f.write("=" * 120 + "\n")
        f.write(f"{'因子':<20} {'未优化 C++':<10} {'优化 C++':<10} {'Python':<10} {'未优化 vs 优化':<25} {'未优化 vs Python':<25} {'优化 vs Python':<25}\n")
        f.write("-" * 120 + "\n")

    # 收集所有比较结果
    comparison_results = []

    for factor in all_factors:
        unopt_file = os.path.join(unoptimized_dir, f"{factor}.csv")
        opt_file = os.path.join(optimized_dir, f"{factor}.csv")
        py_file = os.path.join(python_dir, f"{factor}.csv")

        has_unopt = os.path.exists(unopt_file) and os.path.getsize(unopt_file) > 0
        has_opt = os.path.exists(opt_file) and os.path.getsize(opt_file) > 0
        has_py = os.path.exists(py_file) and os.path.getsize(py_file) > 0

        unopt_status = "✓" if has_unopt else "✗"
        opt_status = "✓" if has_opt else "✗"
        py_status = "✓" if has_py else "✗"

        factor_result = {
            "factor": factor,
            "has_unopt": has_unopt,
            "has_opt": has_opt,
            "has_py": has_py,
            "unopt_vs_opt": None,
            "unopt_vs_py": None,
            "opt_vs_py": None
        }

        # 比较未优化 C++ vs 优化 C++
        unopt_vs_opt = "N/A"
        if has_unopt and has_opt:
            try:
                is_equal, message, stats = compare_files(unopt_file, opt_file)
                unopt_vs_opt = "✓" if is_equal else f"✗ ({message})"
                unopt_vs_opt_total += 1
                if is_equal:
                    unopt_vs_opt_matches += 1

                factor_result["unopt_vs_opt"] = {
                    "is_equal": is_equal,
                    "message": message,
                    "stats": stats
                }

                # 收集统计信息
                if not is_equal:
                    if "NaN 不一致" in message:
                        nan_diff_factors.append((factor, "unopt_vs_opt", stats))
                    elif "Inf 不一致" in message:
                        inf_diff_factors.append((factor, "unopt_vs_opt", stats))
                    elif "所有值都是 NaN 或 Inf" in message:
                        all_nan_inf_factors.append((factor, "unopt_vs_opt", stats))
                    else:
                        value_diff_factors.append((factor, "unopt_vs_opt", stats))
            except Exception as e:
                unopt_vs_opt = f"错误: {e}"
                factor_result["unopt_vs_opt"] = {
                    "is_equal": False,
                    "message": str(e),
                    "stats": None
                }

        # 比较未优化 C++ vs Python
        unopt_vs_py = "N/A"
        if has_unopt and has_py:
            try:
                is_equal, message, stats = compare_files(unopt_file, py_file)
                unopt_vs_py = "✓" if is_equal else f"✗ ({message})"
                unopt_vs_py_total += 1
                if is_equal:
                    unopt_vs_py_matches += 1

                factor_result["unopt_vs_py"] = {
                    "is_equal": is_equal,
                    "message": message,
                    "stats": stats
                }

                # 收集统计信息
                if not is_equal:
                    if "NaN 不一致" in message:
                        nan_diff_factors.append((factor, "unopt_vs_py", stats))
                    elif "Inf 不一致" in message:
                        inf_diff_factors.append((factor, "unopt_vs_py", stats))
                    elif "所有值都是 NaN 或 Inf" in message:
                        all_nan_inf_factors.append((factor, "unopt_vs_py", stats))
                    else:
                        value_diff_factors.append((factor, "unopt_vs_py", stats))
            except Exception as e:
                unopt_vs_py = f"错误: {e}"
                factor_result["unopt_vs_py"] = {
                    "is_equal": False,
                    "message": str(e),
                    "stats": None
                }

        # 比较优化 C++ vs Python
        opt_vs_py = "N/A"
        if has_opt and has_py:
            try:
                is_equal, message, stats = compare_files(opt_file, py_file)
                opt_vs_py = "✓" if is_equal else f"✗ ({message})"
                opt_vs_py_total += 1
                if is_equal:
                    opt_vs_py_matches += 1

                factor_result["opt_vs_py"] = {
                    "is_equal": is_equal,
                    "message": message,
                    "stats": stats
                }

                # 收集统计信息
                if not is_equal:
                    if "NaN 不一致" in message:
                        nan_diff_factors.append((factor, "opt_vs_py", stats))
                    elif "Inf 不一致" in message:
                        inf_diff_factors.append((factor, "opt_vs_py", stats))
                    elif "所有值都是 NaN 或 Inf" in message:
                        all_nan_inf_factors.append((factor, "opt_vs_py", stats))
                    else:
                        value_diff_factors.append((factor, "opt_vs_py", stats))
            except Exception as e:
                opt_vs_py = f"错误: {e}"
                factor_result["opt_vs_py"] = {
                    "is_equal": False,
                    "message": str(e),
                    "stats": None
                }

        comparison_results.append(factor_result)

        result_line = f"{factor:<20} {unopt_status:<10} {opt_status:<10} {py_status:<10} {unopt_vs_opt:<25} {unopt_vs_py:<25} {opt_vs_py:<25}"
        print(result_line)

        # 保存到文件
        with open(os.path.join(base_dir, "comparison_results.txt"), "a") as f:
            f.write(result_line + "\n")

    print("-" * 120)

    # 打印摘要
    print("\n摘要:")
    summary = []
    if unopt_vs_opt_total > 0:
        summary.append(f"未优化 C++ vs 优化 C++: {unopt_vs_opt_matches}/{unopt_vs_opt_total} 个因子匹配 ({unopt_vs_opt_matches/unopt_vs_opt_total*100:.1f}%)")
    if unopt_vs_py_total > 0:
        summary.append(f"未优化 C++ vs Python: {unopt_vs_py_matches}/{unopt_vs_py_total} 个因子匹配 ({unopt_vs_py_matches/unopt_vs_py_total*100:.1f}%)")
    if opt_vs_py_total > 0:
        summary.append(f"优化 C++ vs Python: {opt_vs_py_matches}/{opt_vs_py_total} 个因子匹配 ({opt_vs_py_matches/opt_vs_py_total*100:.1f}%)")

    for line in summary:
        print(line)

    # 打印详细统计信息
    print("\n详细统计信息:")

    # NaN 不一致的因子
    if nan_diff_factors:
        print("\nNaN 不一致的因子:")
        for factor, comparison, stats in nan_diff_factors:
            print(f"  {factor} ({comparison}): NaN 不一致 {stats['nan_diff']} 个位置，共 {stats['total_count']} 个元素")

    # Inf 不一致的因子
    if inf_diff_factors:
        print("\nInf 不一致的因子:")
        for factor, comparison, stats in inf_diff_factors:
            print(f"  {factor} ({comparison}): Inf 不一致 {stats['inf_diff']} 个位置，共 {stats['total_count']} 个元素")

    # 全是 NaN 或 Inf 的因子
    if all_nan_inf_factors:
        print("\n全是 NaN 或 Inf 的因子:")
        for factor, comparison, stats in all_nan_inf_factors:
            print(f"  {factor} ({comparison}): NaN {stats['nan_count']} 个，Inf {stats['inf_count']} 个，共 {stats['total_count']} 个元素")

    # 值不一致的因子
    if value_diff_factors:
        print("\n值不一致的因子:")
        for factor, comparison, stats in value_diff_factors:
            print(f"  {factor} ({comparison}): 最大差异 {stats['max_diff']:.10e}，平均差异 {stats['mean_diff']:.10e}，有效值 {stats['valid_count']} 个，共 {stats['total_count']} 个元素")

    # 保存摘要到文件
    with open(os.path.join(base_dir, "comparison_results.txt"), "a") as f:
        f.write("\n\n摘要:\n")
        for line in summary:
            f.write(line + "\n")

        # 保存详细统计信息
        f.write("\n详细统计信息:\n")

        # NaN 不一致的因子
        if nan_diff_factors:
            f.write("\nNaN 不一致的因子:\n")
            for factor, comparison, stats in nan_diff_factors:
                f.write(f"  {factor} ({comparison}): NaN 不一致 {stats['nan_diff']} 个位置，共 {stats['total_count']} 个元素\n")

        # Inf 不一致的因子
        if inf_diff_factors:
            f.write("\nInf 不一致的因子:\n")
            for factor, comparison, stats in inf_diff_factors:
                f.write(f"  {factor} ({comparison}): Inf 不一致 {stats['inf_diff']} 个位置，共 {stats['total_count']} 个元素\n")

        # 全是 NaN 或 Inf 的因子
        if all_nan_inf_factors:
            f.write("\n全是 NaN 或 Inf 的因子:\n")
            for factor, comparison, stats in all_nan_inf_factors:
                f.write(f"  {factor} ({comparison}): NaN {stats['nan_count']} 个，Inf {stats['inf_count']} 个，共 {stats['total_count']} 个元素\n")

        # 值不一致的因子
        if value_diff_factors:
            f.write("\n值不一致的因子:\n")
            for factor, comparison, stats in value_diff_factors:
                f.write(f"  {factor} ({comparison}): 最大差异 {stats['max_diff']:.10e}，平均差异 {stats['mean_diff']:.10e}，有效值 {stats['valid_count']} 个，共 {stats['total_count']} 个元素\n")

if __name__ == "__main__":
    main()
