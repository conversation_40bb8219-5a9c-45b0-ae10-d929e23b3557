[{"directory": "/home/<USER>/git/realTime/test_eigen/build", "command": "/opt/rh/gcc-toolset-11/root/usr/bin/c++ -Dfeature_operators_EXPORTS -I/usr/local/include/eigen3  -O3 -std=gnu++17 -fPIC -o CMakeFiles/feature_operators.dir/feature_operators.cpp.o -c /home/<USER>/git/realTime/test_eigen/feature_operators.cpp", "file": "/home/<USER>/git/realTime/test_eigen/feature_operators.cpp", "output": "CMakeFiles/feature_operators.dir/feature_operators.cpp.o"}, {"directory": "/home/<USER>/git/realTime/test_eigen/build", "command": "/opt/rh/gcc-toolset-11/root/usr/bin/c++ -Doptimized_operators_EXPORTS -I/usr/local/include/eigen3  -O3 -std=gnu++17 -fPIC -o CMakeFiles/optimized_operators.dir/optimized_operators.cpp.o -c /home/<USER>/git/realTime/test_eigen/optimized_operators.cpp", "file": "/home/<USER>/git/realTime/test_eigen/optimized_operators.cpp", "output": "CMakeFiles/optimized_operators.dir/optimized_operators.cpp.o"}, {"directory": "/home/<USER>/git/realTime/test_eigen/build", "command": "/opt/rh/gcc-toolset-11/root/usr/bin/c++ -Dfactor_calculator_EXPORTS -I/usr/local/include/eigen3  -O3 -std=gnu++17 -fPIC -o CMakeFiles/factor_calculator.dir/factor_calculator.cpp.o -c /home/<USER>/git/realTime/test_eigen/factor_calculator.cpp", "file": "/home/<USER>/git/realTime/test_eigen/factor_calculator.cpp", "output": "CMakeFiles/factor_calculator.dir/factor_calculator.cpp.o"}, {"directory": "/home/<USER>/git/realTime/test_eigen/build", "command": "/opt/rh/gcc-toolset-11/root/usr/bin/c++  -I/usr/local/include/eigen3  -O3 -std=gnu++17 -o CMakeFiles/test_operators.dir/test_operators.cpp.o -c /home/<USER>/git/realTime/test_eigen/test_operators.cpp", "file": "/home/<USER>/git/realTime/test_eigen/test_operators.cpp", "output": "CMakeFiles/test_operators.dir/test_operators.cpp.o"}, {"directory": "/home/<USER>/git/realTime/test_eigen/build", "command": "/opt/rh/gcc-toolset-11/root/usr/bin/c++  -I/usr/local/include/eigen3  -O3 -std=gnu++17 -o CMakeFiles/test_operators.dir/feature_operators.cpp.o -c /home/<USER>/git/realTime/test_eigen/feature_operators.cpp", "file": "/home/<USER>/git/realTime/test_eigen/feature_operators.cpp", "output": "CMakeFiles/test_operators.dir/feature_operators.cpp.o"}, {"directory": "/home/<USER>/git/realTime/test_eigen/build", "command": "/opt/rh/gcc-toolset-11/root/usr/bin/c++  -I/usr/local/include/eigen3  -O3 -std=gnu++17 -o CMakeFiles/test_factors.dir/test_factors.cpp.o -c /home/<USER>/git/realTime/test_eigen/test_factors.cpp", "file": "/home/<USER>/git/realTime/test_eigen/test_factors.cpp", "output": "CMakeFiles/test_factors.dir/test_factors.cpp.o"}, {"directory": "/home/<USER>/git/realTime/test_eigen/build", "command": "/opt/rh/gcc-toolset-11/root/usr/bin/c++  -I/usr/local/include/eigen3  -O3 -std=gnu++17 -o CMakeFiles/test_factors.dir/factor_calculator.cpp.o -c /home/<USER>/git/realTime/test_eigen/factor_calculator.cpp", "file": "/home/<USER>/git/realTime/test_eigen/factor_calculator.cpp", "output": "CMakeFiles/test_factors.dir/factor_calculator.cpp.o"}, {"directory": "/home/<USER>/git/realTime/test_eigen/build", "command": "/opt/rh/gcc-toolset-11/root/usr/bin/c++  -I/usr/local/include/eigen3  -O3 -std=gnu++17 -o CMakeFiles/test_factors.dir/feature_operators.cpp.o -c /home/<USER>/git/realTime/test_eigen/feature_operators.cpp", "file": "/home/<USER>/git/realTime/test_eigen/feature_operators.cpp", "output": "CMakeFiles/test_factors.dir/feature_operators.cpp.o"}, {"directory": "/home/<USER>/git/realTime/test_eigen/build", "command": "/opt/rh/gcc-toolset-11/root/usr/bin/c++  -I/usr/local/include/eigen3  -O3 -std=gnu++17 -o CMakeFiles/test_factors_csv.dir/test_factors_csv.cpp.o -c /home/<USER>/git/realTime/test_eigen/test_factors_csv.cpp", "file": "/home/<USER>/git/realTime/test_eigen/test_factors_csv.cpp", "output": "CMakeFiles/test_factors_csv.dir/test_factors_csv.cpp.o"}, {"directory": "/home/<USER>/git/realTime/test_eigen/build", "command": "/opt/rh/gcc-toolset-11/root/usr/bin/c++  -I/usr/local/include/eigen3  -O3 -std=gnu++17 -o CMakeFiles/test_factors_csv.dir/factor_calculator.cpp.o -c /home/<USER>/git/realTime/test_eigen/factor_calculator.cpp", "file": "/home/<USER>/git/realTime/test_eigen/factor_calculator.cpp", "output": "CMakeFiles/test_factors_csv.dir/factor_calculator.cpp.o"}, {"directory": "/home/<USER>/git/realTime/test_eigen/build", "command": "/opt/rh/gcc-toolset-11/root/usr/bin/c++  -I/usr/local/include/eigen3  -O3 -std=gnu++17 -o CMakeFiles/test_factors_csv.dir/feature_operators.cpp.o -c /home/<USER>/git/realTime/test_eigen/feature_operators.cpp", "file": "/home/<USER>/git/realTime/test_eigen/feature_operators.cpp", "output": "CMakeFiles/test_factors_csv.dir/feature_operators.cpp.o"}, {"directory": "/home/<USER>/git/realTime/test_eigen/build", "command": "/opt/rh/gcc-toolset-11/root/usr/bin/c++  -I/usr/local/include/eigen3  -O3 -std=gnu++17 -o CMakeFiles/test_operators_csv.dir/test_operators_csv.cpp.o -c /home/<USER>/git/realTime/test_eigen/test_operators_csv.cpp", "file": "/home/<USER>/git/realTime/test_eigen/test_operators_csv.cpp", "output": "CMakeFiles/test_operators_csv.dir/test_operators_csv.cpp.o"}, {"directory": "/home/<USER>/git/realTime/test_eigen/build", "command": "/opt/rh/gcc-toolset-11/root/usr/bin/c++  -I/usr/local/include/eigen3  -O3 -std=gnu++17 -o CMakeFiles/test_operators_csv.dir/feature_operators.cpp.o -c /home/<USER>/git/realTime/test_eigen/feature_operators.cpp", "file": "/home/<USER>/git/realTime/test_eigen/feature_operators.cpp", "output": "CMakeFiles/test_operators_csv.dir/feature_operators.cpp.o"}, {"directory": "/home/<USER>/git/realTime/test_eigen/build", "command": "/opt/rh/gcc-toolset-11/root/usr/bin/c++  -I/usr/local/include/eigen3  -O3 -std=gnu++17 -o CMakeFiles/test_optimized_operators.dir/test_optimized_operators.cpp.o -c /home/<USER>/git/realTime/test_eigen/test_optimized_operators.cpp", "file": "/home/<USER>/git/realTime/test_eigen/test_optimized_operators.cpp", "output": "CMakeFiles/test_optimized_operators.dir/test_optimized_operators.cpp.o"}, {"directory": "/home/<USER>/git/realTime/test_eigen/build", "command": "/opt/rh/gcc-toolset-11/root/usr/bin/c++  -I/usr/local/include/eigen3  -O3 -std=gnu++17 -o CMakeFiles/test_optimized_operators.dir/optimized_operators.cpp.o -c /home/<USER>/git/realTime/test_eigen/optimized_operators.cpp", "file": "/home/<USER>/git/realTime/test_eigen/optimized_operators.cpp", "output": "CMakeFiles/test_optimized_operators.dir/optimized_operators.cpp.o"}, {"directory": "/home/<USER>/git/realTime/test_eigen/build", "command": "/opt/rh/gcc-toolset-11/root/usr/bin/c++  -I/usr/local/include/eigen3  -O3 -std=gnu++17 -o CMakeFiles/benchmark_operators.dir/benchmark_operators.cpp.o -c /home/<USER>/git/realTime/test_eigen/benchmark_operators.cpp", "file": "/home/<USER>/git/realTime/test_eigen/benchmark_operators.cpp", "output": "CMakeFiles/benchmark_operators.dir/benchmark_operators.cpp.o"}, {"directory": "/home/<USER>/git/realTime/test_eigen/build", "command": "/opt/rh/gcc-toolset-11/root/usr/bin/c++  -I/usr/local/include/eigen3  -O3 -std=gnu++17 -o CMakeFiles/benchmark_optimized_operators.dir/benchmark_optimized_operators.cpp.o -c /home/<USER>/git/realTime/test_eigen/benchmark_optimized_operators.cpp", "file": "/home/<USER>/git/realTime/test_eigen/benchmark_optimized_operators.cpp", "output": "CMakeFiles/benchmark_optimized_operators.dir/benchmark_optimized_operators.cpp.o"}]