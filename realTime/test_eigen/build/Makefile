# CMAKE generated file: DO NOT EDIT!
# Generated by "Unix Makefiles" Generator, CMake Version 3.26

# Default target executed when no arguments are given to make.
default_target: all
.PHONY : default_target

# Allow only one "make -f Makefile2" at a time, but pass parallelism.
.NOTPARALLEL:

#=============================================================================
# Special targets provided by cmake.

# Disable implicit rules so canonical targets will work.
.SUFFIXES:

# Disable VCS-based implicit rules.
% : %,v

# Disable VCS-based implicit rules.
% : RCS/%

# Disable VCS-based implicit rules.
% : RCS/%,v

# Disable VCS-based implicit rules.
% : SCCS/s.%

# Disable VCS-based implicit rules.
% : s.%

.SUFFIXES: .hpux_make_needs_suffix_list

# Command-line flag to silence nested $(MAKE).
$(VERBOSE)MAKESILENT = -s

#Suppress display of executed commands.
$(VERBOSE).SILENT:

# A target that is always out of date.
cmake_force:
.PHONY : cmake_force

#=============================================================================
# Set environment variables for the build.

# The shell in which to execute make rules.
SHELL = /bin/sh

# The CMake executable.
CMAKE_COMMAND = /usr/bin/cmake

# The command to remove a file.
RM = /usr/bin/cmake -E rm -f

# Escaping for special characters.
EQUALS = =

# The top-level source directory on which CMake was run.
CMAKE_SOURCE_DIR = /home/<USER>/git/realTime/test_eigen

# The top-level build directory on which CMake was run.
CMAKE_BINARY_DIR = /home/<USER>/git/realTime/test_eigen/build

#=============================================================================
# Targets provided globally by CMake.

# Special rule for the target edit_cache
edit_cache:
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --cyan "Running CMake cache editor..."
	/usr/bin/ccmake -S$(CMAKE_SOURCE_DIR) -B$(CMAKE_BINARY_DIR)
.PHONY : edit_cache

# Special rule for the target edit_cache
edit_cache/fast: edit_cache
.PHONY : edit_cache/fast

# Special rule for the target rebuild_cache
rebuild_cache:
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --cyan "Running CMake to regenerate build system..."
	/usr/bin/cmake --regenerate-during-build -S$(CMAKE_SOURCE_DIR) -B$(CMAKE_BINARY_DIR)
.PHONY : rebuild_cache

# Special rule for the target rebuild_cache
rebuild_cache/fast: rebuild_cache
.PHONY : rebuild_cache/fast

# Special rule for the target list_install_components
list_install_components:
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --cyan "Available install components are: \"Unspecified\""
.PHONY : list_install_components

# Special rule for the target list_install_components
list_install_components/fast: list_install_components
.PHONY : list_install_components/fast

# Special rule for the target install
install: preinstall
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --cyan "Install the project..."
	/usr/bin/cmake -P cmake_install.cmake
.PHONY : install

# Special rule for the target install
install/fast: preinstall/fast
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --cyan "Install the project..."
	/usr/bin/cmake -P cmake_install.cmake
.PHONY : install/fast

# Special rule for the target install/local
install/local: preinstall
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --cyan "Installing only the local directory..."
	/usr/bin/cmake -DCMAKE_INSTALL_LOCAL_ONLY=1 -P cmake_install.cmake
.PHONY : install/local

# Special rule for the target install/local
install/local/fast: preinstall/fast
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --cyan "Installing only the local directory..."
	/usr/bin/cmake -DCMAKE_INSTALL_LOCAL_ONLY=1 -P cmake_install.cmake
.PHONY : install/local/fast

# Special rule for the target install/strip
install/strip: preinstall
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --cyan "Installing the project stripped..."
	/usr/bin/cmake -DCMAKE_INSTALL_DO_STRIP=1 -P cmake_install.cmake
.PHONY : install/strip

# Special rule for the target install/strip
install/strip/fast: preinstall/fast
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --cyan "Installing the project stripped..."
	/usr/bin/cmake -DCMAKE_INSTALL_DO_STRIP=1 -P cmake_install.cmake
.PHONY : install/strip/fast

# The main all target
all: cmake_check_build_system
	$(CMAKE_COMMAND) -E cmake_progress_start /home/<USER>/git/realTime/test_eigen/build/CMakeFiles /home/<USER>/git/realTime/test_eigen/build//CMakeFiles/progress.marks
	$(MAKE) $(MAKESILENT) -f CMakeFiles/Makefile2 all
	$(CMAKE_COMMAND) -E cmake_progress_start /home/<USER>/git/realTime/test_eigen/build/CMakeFiles 0
.PHONY : all

# The main clean target
clean:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/Makefile2 clean
.PHONY : clean

# The main clean target
clean/fast: clean
.PHONY : clean/fast

# Prepare targets for installation.
preinstall: all
	$(MAKE) $(MAKESILENT) -f CMakeFiles/Makefile2 preinstall
.PHONY : preinstall

# Prepare targets for installation.
preinstall/fast:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/Makefile2 preinstall
.PHONY : preinstall/fast

# clear depends
depend:
	$(CMAKE_COMMAND) -S$(CMAKE_SOURCE_DIR) -B$(CMAKE_BINARY_DIR) --check-build-system CMakeFiles/Makefile.cmake 1
.PHONY : depend

#=============================================================================
# Target rules for targets named feature_operators

# Build rule for target.
feature_operators: cmake_check_build_system
	$(MAKE) $(MAKESILENT) -f CMakeFiles/Makefile2 feature_operators
.PHONY : feature_operators

# fast build rule for target.
feature_operators/fast:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/feature_operators.dir/build.make CMakeFiles/feature_operators.dir/build
.PHONY : feature_operators/fast

#=============================================================================
# Target rules for targets named optimized_operators

# Build rule for target.
optimized_operators: cmake_check_build_system
	$(MAKE) $(MAKESILENT) -f CMakeFiles/Makefile2 optimized_operators
.PHONY : optimized_operators

# fast build rule for target.
optimized_operators/fast:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/optimized_operators.dir/build.make CMakeFiles/optimized_operators.dir/build
.PHONY : optimized_operators/fast

#=============================================================================
# Target rules for targets named factor_calculator

# Build rule for target.
factor_calculator: cmake_check_build_system
	$(MAKE) $(MAKESILENT) -f CMakeFiles/Makefile2 factor_calculator
.PHONY : factor_calculator

# fast build rule for target.
factor_calculator/fast:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/factor_calculator.dir/build.make CMakeFiles/factor_calculator.dir/build
.PHONY : factor_calculator/fast

#=============================================================================
# Target rules for targets named test_operators

# Build rule for target.
test_operators: cmake_check_build_system
	$(MAKE) $(MAKESILENT) -f CMakeFiles/Makefile2 test_operators
.PHONY : test_operators

# fast build rule for target.
test_operators/fast:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/test_operators.dir/build.make CMakeFiles/test_operators.dir/build
.PHONY : test_operators/fast

#=============================================================================
# Target rules for targets named test_factors

# Build rule for target.
test_factors: cmake_check_build_system
	$(MAKE) $(MAKESILENT) -f CMakeFiles/Makefile2 test_factors
.PHONY : test_factors

# fast build rule for target.
test_factors/fast:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/test_factors.dir/build.make CMakeFiles/test_factors.dir/build
.PHONY : test_factors/fast

#=============================================================================
# Target rules for targets named test_factors_csv

# Build rule for target.
test_factors_csv: cmake_check_build_system
	$(MAKE) $(MAKESILENT) -f CMakeFiles/Makefile2 test_factors_csv
.PHONY : test_factors_csv

# fast build rule for target.
test_factors_csv/fast:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/test_factors_csv.dir/build.make CMakeFiles/test_factors_csv.dir/build
.PHONY : test_factors_csv/fast

#=============================================================================
# Target rules for targets named test_operators_csv

# Build rule for target.
test_operators_csv: cmake_check_build_system
	$(MAKE) $(MAKESILENT) -f CMakeFiles/Makefile2 test_operators_csv
.PHONY : test_operators_csv

# fast build rule for target.
test_operators_csv/fast:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/test_operators_csv.dir/build.make CMakeFiles/test_operators_csv.dir/build
.PHONY : test_operators_csv/fast

#=============================================================================
# Target rules for targets named test_optimized_operators

# Build rule for target.
test_optimized_operators: cmake_check_build_system
	$(MAKE) $(MAKESILENT) -f CMakeFiles/Makefile2 test_optimized_operators
.PHONY : test_optimized_operators

# fast build rule for target.
test_optimized_operators/fast:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/test_optimized_operators.dir/build.make CMakeFiles/test_optimized_operators.dir/build
.PHONY : test_optimized_operators/fast

#=============================================================================
# Target rules for targets named benchmark_operators

# Build rule for target.
benchmark_operators: cmake_check_build_system
	$(MAKE) $(MAKESILENT) -f CMakeFiles/Makefile2 benchmark_operators
.PHONY : benchmark_operators

# fast build rule for target.
benchmark_operators/fast:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/benchmark_operators.dir/build.make CMakeFiles/benchmark_operators.dir/build
.PHONY : benchmark_operators/fast

#=============================================================================
# Target rules for targets named benchmark_optimized_operators

# Build rule for target.
benchmark_optimized_operators: cmake_check_build_system
	$(MAKE) $(MAKESILENT) -f CMakeFiles/Makefile2 benchmark_optimized_operators
.PHONY : benchmark_optimized_operators

# fast build rule for target.
benchmark_optimized_operators/fast:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/benchmark_optimized_operators.dir/build.make CMakeFiles/benchmark_optimized_operators.dir/build
.PHONY : benchmark_optimized_operators/fast

benchmark_operators.o: benchmark_operators.cpp.o
.PHONY : benchmark_operators.o

# target to build an object file
benchmark_operators.cpp.o:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/benchmark_operators.dir/build.make CMakeFiles/benchmark_operators.dir/benchmark_operators.cpp.o
.PHONY : benchmark_operators.cpp.o

benchmark_operators.i: benchmark_operators.cpp.i
.PHONY : benchmark_operators.i

# target to preprocess a source file
benchmark_operators.cpp.i:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/benchmark_operators.dir/build.make CMakeFiles/benchmark_operators.dir/benchmark_operators.cpp.i
.PHONY : benchmark_operators.cpp.i

benchmark_operators.s: benchmark_operators.cpp.s
.PHONY : benchmark_operators.s

# target to generate assembly for a file
benchmark_operators.cpp.s:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/benchmark_operators.dir/build.make CMakeFiles/benchmark_operators.dir/benchmark_operators.cpp.s
.PHONY : benchmark_operators.cpp.s

benchmark_optimized_operators.o: benchmark_optimized_operators.cpp.o
.PHONY : benchmark_optimized_operators.o

# target to build an object file
benchmark_optimized_operators.cpp.o:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/benchmark_optimized_operators.dir/build.make CMakeFiles/benchmark_optimized_operators.dir/benchmark_optimized_operators.cpp.o
.PHONY : benchmark_optimized_operators.cpp.o

benchmark_optimized_operators.i: benchmark_optimized_operators.cpp.i
.PHONY : benchmark_optimized_operators.i

# target to preprocess a source file
benchmark_optimized_operators.cpp.i:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/benchmark_optimized_operators.dir/build.make CMakeFiles/benchmark_optimized_operators.dir/benchmark_optimized_operators.cpp.i
.PHONY : benchmark_optimized_operators.cpp.i

benchmark_optimized_operators.s: benchmark_optimized_operators.cpp.s
.PHONY : benchmark_optimized_operators.s

# target to generate assembly for a file
benchmark_optimized_operators.cpp.s:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/benchmark_optimized_operators.dir/build.make CMakeFiles/benchmark_optimized_operators.dir/benchmark_optimized_operators.cpp.s
.PHONY : benchmark_optimized_operators.cpp.s

factor_calculator.o: factor_calculator.cpp.o
.PHONY : factor_calculator.o

# target to build an object file
factor_calculator.cpp.o:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/factor_calculator.dir/build.make CMakeFiles/factor_calculator.dir/factor_calculator.cpp.o
	$(MAKE) $(MAKESILENT) -f CMakeFiles/test_factors.dir/build.make CMakeFiles/test_factors.dir/factor_calculator.cpp.o
	$(MAKE) $(MAKESILENT) -f CMakeFiles/test_factors_csv.dir/build.make CMakeFiles/test_factors_csv.dir/factor_calculator.cpp.o
.PHONY : factor_calculator.cpp.o

factor_calculator.i: factor_calculator.cpp.i
.PHONY : factor_calculator.i

# target to preprocess a source file
factor_calculator.cpp.i:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/factor_calculator.dir/build.make CMakeFiles/factor_calculator.dir/factor_calculator.cpp.i
	$(MAKE) $(MAKESILENT) -f CMakeFiles/test_factors.dir/build.make CMakeFiles/test_factors.dir/factor_calculator.cpp.i
	$(MAKE) $(MAKESILENT) -f CMakeFiles/test_factors_csv.dir/build.make CMakeFiles/test_factors_csv.dir/factor_calculator.cpp.i
.PHONY : factor_calculator.cpp.i

factor_calculator.s: factor_calculator.cpp.s
.PHONY : factor_calculator.s

# target to generate assembly for a file
factor_calculator.cpp.s:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/factor_calculator.dir/build.make CMakeFiles/factor_calculator.dir/factor_calculator.cpp.s
	$(MAKE) $(MAKESILENT) -f CMakeFiles/test_factors.dir/build.make CMakeFiles/test_factors.dir/factor_calculator.cpp.s
	$(MAKE) $(MAKESILENT) -f CMakeFiles/test_factors_csv.dir/build.make CMakeFiles/test_factors_csv.dir/factor_calculator.cpp.s
.PHONY : factor_calculator.cpp.s

feature_operators.o: feature_operators.cpp.o
.PHONY : feature_operators.o

# target to build an object file
feature_operators.cpp.o:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/feature_operators.dir/build.make CMakeFiles/feature_operators.dir/feature_operators.cpp.o
	$(MAKE) $(MAKESILENT) -f CMakeFiles/test_operators.dir/build.make CMakeFiles/test_operators.dir/feature_operators.cpp.o
	$(MAKE) $(MAKESILENT) -f CMakeFiles/test_factors.dir/build.make CMakeFiles/test_factors.dir/feature_operators.cpp.o
	$(MAKE) $(MAKESILENT) -f CMakeFiles/test_factors_csv.dir/build.make CMakeFiles/test_factors_csv.dir/feature_operators.cpp.o
	$(MAKE) $(MAKESILENT) -f CMakeFiles/test_operators_csv.dir/build.make CMakeFiles/test_operators_csv.dir/feature_operators.cpp.o
.PHONY : feature_operators.cpp.o

feature_operators.i: feature_operators.cpp.i
.PHONY : feature_operators.i

# target to preprocess a source file
feature_operators.cpp.i:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/feature_operators.dir/build.make CMakeFiles/feature_operators.dir/feature_operators.cpp.i
	$(MAKE) $(MAKESILENT) -f CMakeFiles/test_operators.dir/build.make CMakeFiles/test_operators.dir/feature_operators.cpp.i
	$(MAKE) $(MAKESILENT) -f CMakeFiles/test_factors.dir/build.make CMakeFiles/test_factors.dir/feature_operators.cpp.i
	$(MAKE) $(MAKESILENT) -f CMakeFiles/test_factors_csv.dir/build.make CMakeFiles/test_factors_csv.dir/feature_operators.cpp.i
	$(MAKE) $(MAKESILENT) -f CMakeFiles/test_operators_csv.dir/build.make CMakeFiles/test_operators_csv.dir/feature_operators.cpp.i
.PHONY : feature_operators.cpp.i

feature_operators.s: feature_operators.cpp.s
.PHONY : feature_operators.s

# target to generate assembly for a file
feature_operators.cpp.s:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/feature_operators.dir/build.make CMakeFiles/feature_operators.dir/feature_operators.cpp.s
	$(MAKE) $(MAKESILENT) -f CMakeFiles/test_operators.dir/build.make CMakeFiles/test_operators.dir/feature_operators.cpp.s
	$(MAKE) $(MAKESILENT) -f CMakeFiles/test_factors.dir/build.make CMakeFiles/test_factors.dir/feature_operators.cpp.s
	$(MAKE) $(MAKESILENT) -f CMakeFiles/test_factors_csv.dir/build.make CMakeFiles/test_factors_csv.dir/feature_operators.cpp.s
	$(MAKE) $(MAKESILENT) -f CMakeFiles/test_operators_csv.dir/build.make CMakeFiles/test_operators_csv.dir/feature_operators.cpp.s
.PHONY : feature_operators.cpp.s

optimized_operators.o: optimized_operators.cpp.o
.PHONY : optimized_operators.o

# target to build an object file
optimized_operators.cpp.o:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/optimized_operators.dir/build.make CMakeFiles/optimized_operators.dir/optimized_operators.cpp.o
	$(MAKE) $(MAKESILENT) -f CMakeFiles/test_optimized_operators.dir/build.make CMakeFiles/test_optimized_operators.dir/optimized_operators.cpp.o
.PHONY : optimized_operators.cpp.o

optimized_operators.i: optimized_operators.cpp.i
.PHONY : optimized_operators.i

# target to preprocess a source file
optimized_operators.cpp.i:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/optimized_operators.dir/build.make CMakeFiles/optimized_operators.dir/optimized_operators.cpp.i
	$(MAKE) $(MAKESILENT) -f CMakeFiles/test_optimized_operators.dir/build.make CMakeFiles/test_optimized_operators.dir/optimized_operators.cpp.i
.PHONY : optimized_operators.cpp.i

optimized_operators.s: optimized_operators.cpp.s
.PHONY : optimized_operators.s

# target to generate assembly for a file
optimized_operators.cpp.s:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/optimized_operators.dir/build.make CMakeFiles/optimized_operators.dir/optimized_operators.cpp.s
	$(MAKE) $(MAKESILENT) -f CMakeFiles/test_optimized_operators.dir/build.make CMakeFiles/test_optimized_operators.dir/optimized_operators.cpp.s
.PHONY : optimized_operators.cpp.s

test_factors.o: test_factors.cpp.o
.PHONY : test_factors.o

# target to build an object file
test_factors.cpp.o:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/test_factors.dir/build.make CMakeFiles/test_factors.dir/test_factors.cpp.o
.PHONY : test_factors.cpp.o

test_factors.i: test_factors.cpp.i
.PHONY : test_factors.i

# target to preprocess a source file
test_factors.cpp.i:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/test_factors.dir/build.make CMakeFiles/test_factors.dir/test_factors.cpp.i
.PHONY : test_factors.cpp.i

test_factors.s: test_factors.cpp.s
.PHONY : test_factors.s

# target to generate assembly for a file
test_factors.cpp.s:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/test_factors.dir/build.make CMakeFiles/test_factors.dir/test_factors.cpp.s
.PHONY : test_factors.cpp.s

test_factors_csv.o: test_factors_csv.cpp.o
.PHONY : test_factors_csv.o

# target to build an object file
test_factors_csv.cpp.o:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/test_factors_csv.dir/build.make CMakeFiles/test_factors_csv.dir/test_factors_csv.cpp.o
.PHONY : test_factors_csv.cpp.o

test_factors_csv.i: test_factors_csv.cpp.i
.PHONY : test_factors_csv.i

# target to preprocess a source file
test_factors_csv.cpp.i:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/test_factors_csv.dir/build.make CMakeFiles/test_factors_csv.dir/test_factors_csv.cpp.i
.PHONY : test_factors_csv.cpp.i

test_factors_csv.s: test_factors_csv.cpp.s
.PHONY : test_factors_csv.s

# target to generate assembly for a file
test_factors_csv.cpp.s:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/test_factors_csv.dir/build.make CMakeFiles/test_factors_csv.dir/test_factors_csv.cpp.s
.PHONY : test_factors_csv.cpp.s

test_operators.o: test_operators.cpp.o
.PHONY : test_operators.o

# target to build an object file
test_operators.cpp.o:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/test_operators.dir/build.make CMakeFiles/test_operators.dir/test_operators.cpp.o
.PHONY : test_operators.cpp.o

test_operators.i: test_operators.cpp.i
.PHONY : test_operators.i

# target to preprocess a source file
test_operators.cpp.i:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/test_operators.dir/build.make CMakeFiles/test_operators.dir/test_operators.cpp.i
.PHONY : test_operators.cpp.i

test_operators.s: test_operators.cpp.s
.PHONY : test_operators.s

# target to generate assembly for a file
test_operators.cpp.s:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/test_operators.dir/build.make CMakeFiles/test_operators.dir/test_operators.cpp.s
.PHONY : test_operators.cpp.s

test_operators_csv.o: test_operators_csv.cpp.o
.PHONY : test_operators_csv.o

# target to build an object file
test_operators_csv.cpp.o:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/test_operators_csv.dir/build.make CMakeFiles/test_operators_csv.dir/test_operators_csv.cpp.o
.PHONY : test_operators_csv.cpp.o

test_operators_csv.i: test_operators_csv.cpp.i
.PHONY : test_operators_csv.i

# target to preprocess a source file
test_operators_csv.cpp.i:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/test_operators_csv.dir/build.make CMakeFiles/test_operators_csv.dir/test_operators_csv.cpp.i
.PHONY : test_operators_csv.cpp.i

test_operators_csv.s: test_operators_csv.cpp.s
.PHONY : test_operators_csv.s

# target to generate assembly for a file
test_operators_csv.cpp.s:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/test_operators_csv.dir/build.make CMakeFiles/test_operators_csv.dir/test_operators_csv.cpp.s
.PHONY : test_operators_csv.cpp.s

test_optimized_operators.o: test_optimized_operators.cpp.o
.PHONY : test_optimized_operators.o

# target to build an object file
test_optimized_operators.cpp.o:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/test_optimized_operators.dir/build.make CMakeFiles/test_optimized_operators.dir/test_optimized_operators.cpp.o
.PHONY : test_optimized_operators.cpp.o

test_optimized_operators.i: test_optimized_operators.cpp.i
.PHONY : test_optimized_operators.i

# target to preprocess a source file
test_optimized_operators.cpp.i:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/test_optimized_operators.dir/build.make CMakeFiles/test_optimized_operators.dir/test_optimized_operators.cpp.i
.PHONY : test_optimized_operators.cpp.i

test_optimized_operators.s: test_optimized_operators.cpp.s
.PHONY : test_optimized_operators.s

# target to generate assembly for a file
test_optimized_operators.cpp.s:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/test_optimized_operators.dir/build.make CMakeFiles/test_optimized_operators.dir/test_optimized_operators.cpp.s
.PHONY : test_optimized_operators.cpp.s

# Help Target
help:
	@echo "The following are some of the valid targets for this Makefile:"
	@echo "... all (the default if no target is provided)"
	@echo "... clean"
	@echo "... depend"
	@echo "... edit_cache"
	@echo "... install"
	@echo "... install/local"
	@echo "... install/strip"
	@echo "... list_install_components"
	@echo "... rebuild_cache"
	@echo "... benchmark_operators"
	@echo "... benchmark_optimized_operators"
	@echo "... factor_calculator"
	@echo "... feature_operators"
	@echo "... optimized_operators"
	@echo "... test_factors"
	@echo "... test_factors_csv"
	@echo "... test_operators"
	@echo "... test_operators_csv"
	@echo "... test_optimized_operators"
	@echo "... benchmark_operators.o"
	@echo "... benchmark_operators.i"
	@echo "... benchmark_operators.s"
	@echo "... benchmark_optimized_operators.o"
	@echo "... benchmark_optimized_operators.i"
	@echo "... benchmark_optimized_operators.s"
	@echo "... factor_calculator.o"
	@echo "... factor_calculator.i"
	@echo "... factor_calculator.s"
	@echo "... feature_operators.o"
	@echo "... feature_operators.i"
	@echo "... feature_operators.s"
	@echo "... optimized_operators.o"
	@echo "... optimized_operators.i"
	@echo "... optimized_operators.s"
	@echo "... test_factors.o"
	@echo "... test_factors.i"
	@echo "... test_factors.s"
	@echo "... test_factors_csv.o"
	@echo "... test_factors_csv.i"
	@echo "... test_factors_csv.s"
	@echo "... test_operators.o"
	@echo "... test_operators.i"
	@echo "... test_operators.s"
	@echo "... test_operators_csv.o"
	@echo "... test_operators_csv.i"
	@echo "... test_operators_csv.s"
	@echo "... test_optimized_operators.o"
	@echo "... test_optimized_operators.i"
	@echo "... test_optimized_operators.s"
.PHONY : help



#=============================================================================
# Special targets to cleanup operation of make.

# Special rule to run CMake to check the build system integrity.
# No rule that depends on this can have commands that come from listfiles
# because they might be regenerated.
cmake_check_build_system:
	$(CMAKE_COMMAND) -S$(CMAKE_SOURCE_DIR) -B$(CMAKE_BINARY_DIR) --check-build-system CMakeFiles/Makefile.cmake 0
.PHONY : cmake_check_build_system

