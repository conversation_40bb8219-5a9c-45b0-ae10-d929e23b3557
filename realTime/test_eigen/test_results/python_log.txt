加载测试数据...
数据已加载。形状: (500, 518)
测试基本算术运算符...
已保存到 /home/<USER>/git/realTime/test_eigen/test_results/python/Add.csv
已保存到 /home/<USER>/git/realTime/test_eigen/test_results/python/Minus.csv
已保存到 /home/<USER>/git/realTime/test_eigen/test_results/python/Multiply.csv
已保存到 /home/<USER>/git/realTime/test_eigen/test_results/python/Divide.csv
已保存到 /home/<USER>/git/realTime/test_eigen/test_results/python/Sqrt.csv
已保存到 /home/<USER>/git/realTime/test_eigen/test_results/python/Log.csv
已保存到 /home/<USER>/git/realTime/test_eigen/test_results/python/Inv.csv
已保存到 /home/<USER>/git/realTime/test_eigen/test_results/python/Power.csv
已保存到 /home/<USER>/git/realTime/test_eigen/test_results/python/Abs.csv
已保存到 /home/<USER>/git/realTime/test_eigen/test_results/python/Sign.csv
已保存到 /home/<USER>/git/realTime/test_eigen/test_results/python/Exp.csv
已保存到 /home/<USER>/git/realTime/test_eigen/test_results/python/Reverse.csv
已保存到 /home/<USER>/git/realTime/test_eigen/test_results/python/Ceil.csv
已保存到 /home/<USER>/git/realTime/test_eigen/test_results/python/Floor.csv
已保存到 /home/<USER>/git/realTime/test_eigen/test_results/python/Round.csv
已保存到 /home/<USER>/git/realTime/test_eigen/test_results/python/SignedPower.csv
测试逻辑运算符...
已保存到 /home/<USER>/git/realTime/test_eigen/test_results/python/And.csv
已保存到 /home/<USER>/git/realTime/test_eigen/test_results/python/Or.csv
已保存到 /home/<USER>/git/realTime/test_eigen/test_results/python/Not.csv
已保存到 /home/<USER>/git/realTime/test_eigen/test_results/python/Xor.csv
已保存到 /home/<USER>/git/realTime/test_eigen/test_results/python/Equal.csv
已保存到 /home/<USER>/git/realTime/test_eigen/test_results/python/UnEqual.csv
已保存到 /home/<USER>/git/realTime/test_eigen/test_results/python/Mthan.csv
已保存到 /home/<USER>/git/realTime/test_eigen/test_results/python/MEthan.csv
已保存到 /home/<USER>/git/realTime/test_eigen/test_results/python/Lthan.csv
已保存到 /home/<USER>/git/realTime/test_eigen/test_results/python/LEthan.csv
测试时间序列运算符...
已保存到 /home/<USER>/git/realTime/test_eigen/test_results/python/ts_Delay.csv
已保存到 /home/<USER>/git/realTime/test_eigen/test_results/python/ts_Mean.csv
已保存到 /home/<USER>/git/realTime/test_eigen/test_results/python/ts_Sum.csv
已保存到 /home/<USER>/git/realTime/test_eigen/test_results/python/ts_Stdev.csv
已保存到 /home/<USER>/git/realTime/test_eigen/test_results/python/ts_Min.csv
已保存到 /home/<USER>/git/realTime/test_eigen/test_results/python/ts_Max.csv
已保存到 /home/<USER>/git/realTime/test_eigen/test_results/python/ts_Delta.csv
已保存到 /home/<USER>/git/realTime/test_eigen/test_results/python/ts_Divide.csv
已保存到 /home/<USER>/git/realTime/test_eigen/test_results/python/ts_ChgRate.csv
高级时间序列运算符错误: name 'ts_ArgMax' is not defined
测试横截面运算符...
已保存到 /home/<USER>/git/realTime/test_eigen/test_results/python/pn_Mean.csv
已保存到 /home/<USER>/git/realTime/test_eigen/test_results/python/pn_Rank.csv
已保存到 /home/<USER>/git/realTime/test_eigen/test_results/python/pn_Stand.csv
已保存到 /home/<USER>/git/realTime/test_eigen/test_results/python/pn_TransNorm.csv
已保存到 /home/<USER>/git/realTime/test_eigen/test_results/python/pn_Rank2.csv
已保存到 /home/<USER>/git/realTime/test_eigen/test_results/python/pn_RankCentered.csv
已保存到 /home/<USER>/git/realTime/test_eigen/test_results/python/pn_FillMax.csv
已保存到 /home/<USER>/git/realTime/test_eigen/test_results/python/pn_FillMin.csv
已保存到 /home/<USER>/git/realTime/test_eigen/test_results/python/pn_TransStd.csv
已保存到 /home/<USER>/git/realTime/test_eigen/test_results/python/pn_Winsor.csv
已保存到 /home/<USER>/git/realTime/test_eigen/test_results/python/pn_Cut.csv
已保存到 /home/<USER>/git/realTime/test_eigen/test_results/python/pn_GroupRank.csv
已保存到 /home/<USER>/git/realTime/test_eigen/test_results/python/pn_GroupNorm.csv
已保存到 /home/<USER>/git/realTime/test_eigen/test_results/python/pn_GroupNeutral.csv
已保存到 /home/<USER>/git/realTime/test_eigen/test_results/python/pn_CrossFit.csv
测试特殊函数...
已保存到 /home/<USER>/git/realTime/test_eigen/test_results/python/getNan.csv
已保存到 /home/<USER>/git/realTime/test_eigen/test_results/python/getInf.csv
已保存到 /home/<USER>/git/realTime/test_eigen/test_results/python/FilterInf.csv
已保存到 /home/<USER>/git/realTime/test_eigen/test_results/python/Max.csv
已保存到 /home/<USER>/git/realTime/test_eigen/test_results/python/Min.csv
测试 Tot 系列函数...
已保存到 /home/<USER>/git/realTime/test_eigen/test_results/python/Tot_Mean.csv
已保存到 /home/<USER>/git/realTime/test_eigen/test_results/python/Tot_Sum.csv
已保存到 /home/<USER>/git/realTime/test_eigen/test_results/python/Tot_Stdev.csv
已保存到 /home/<USER>/git/realTime/test_eigen/test_results/python/Tot_Delta.csv
已保存到 /home/<USER>/git/realTime/test_eigen/test_results/python/Tot_Divide.csv
已保存到 /home/<USER>/git/realTime/test_eigen/test_results/python/Tot_ChgRate.csv
已保存到 /home/<USER>/git/realTime/test_eigen/test_results/python/Tot_Rank.csv
已保存到 /home/<USER>/git/realTime/test_eigen/test_results/python/Tot_ArgMax.csv
已保存到 /home/<USER>/git/realTime/test_eigen/test_results/python/Tot_ArgMin.csv
已保存到 /home/<USER>/git/realTime/test_eigen/test_results/python/Tot_Max.csv
已保存到 /home/<USER>/git/realTime/test_eigen/test_results/python/Tot_Min.csv
Python 算子测试完成。
