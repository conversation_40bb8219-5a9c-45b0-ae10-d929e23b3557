["And", "pn_Winsor", "ts_Rank", "ts_<PERSON><PERSON>", "pn_Stand", "FilterInf", "Exp", "SignedPower", "ts_Quantile", "Add", "Reverse", "ts_<PERSON>", "ts_MaxDD", "pn_FillMax", "ts_Mean", "ts_Divide", "Sign", "UnEqual", "Minus", "ts_Entropy", "pn_Rank2", "ts_Median", "ts_ChgRate", "ts_MeanChg", "To<PERSON>_<PERSON>", "ts_<PERSON>", "<PERSON><PERSON>", "IfThen", "Equal", "get<PERSON><PERSON>", "ts_Scale", "Tot_Rank", "Multiply", "Not", "Softsign", "Tot_ArgMax", "Tot_Delta", "<PERSON><PERSON>", "<PERSON><PERSON>", "Max", "ts_<PERSON><PERSON>_corr", "ts_TransNorm", "Or", "Tot_Mean", "Tot_Max", "ts_Sum", "Log", "ts_Product", "pn_TransNorm", "pn_CrossFit", "ts_<PERSON>", "Abs", "pn_GroupNeutral", "ts_Decay", "Round", "Tot_Divide", "Sqrt", "pn_RankCentered", "Power", "Tot_ChgRate", "ts_Stdev", "pn_GroupRank", "Min", "Divide", "ts_Skewness", "ts_<PERSON><PERSON>", "Tot_Arg<PERSON>in", "Tot_Sum", "ts_Regression", "pn_Cut", "ts_Decay2", "pn_Mean", "<PERSON><PERSON><PERSON>", "getInf", "pn_FillMin", "ts_Cov", "Tot_Stdev", "pn_GroupNorm", "ts_<PERSON>rr", "Ceil", "Mthan", "pn_TransStd", "pn_Rank", "Floor"]