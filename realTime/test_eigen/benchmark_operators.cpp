#include "feature_operators.h"
#include <iostream>
#include <iomanip>
#include <vector>
#include <string>
#include <cmath>
#include <fstream>
#include <sstream>
#include <filesystem>
#include <chrono>
#include <map>

using namespace feature_operators;
namespace fs = std::filesystem;

// 从CSV文件加载数据
DataFrame loadCsv(const std::string& filepath) {
    std::ifstream file(filepath);
    if (!file.is_open()) {
        std::cerr << "无法打开文件: " << filepath << std::endl;
        return DataFrame();
    }

    std::vector<std::vector<double>> data;
    std::string line;

    // 跳过标题行
    std::getline(file, line);

    // 读取数据行
    while (std::getline(file, line)) {
        std::vector<double> row;
        std::stringstream ss(line);
        std::string cell;

        // 跳过第一列（日期）
        std::getline(ss, cell, ',');

        // 读取数据列
        while (std::getline(ss, cell, ',')) {
            try {
                double value = std::stod(cell);
                row.push_back(value);
            } catch (const std::exception& e) {
                row.push_back(std::numeric_limits<double>::quiet_NaN());
            }
        }

        if (!row.empty()) {
            data.push_back(row);
        }
    }

    // 创建Eigen矩阵
    int rows = data.size();
    int cols = rows > 0 ? data[0].size() : 0;

    DataFrame matrix(rows, cols);
    for (int i = 0; i < rows; ++i) {
        for (int j = 0; j < cols; ++j) {
            matrix(i, j) = data[i][j];
        }
    }

    return matrix;
}

// 将结果保存为JSON文件
void saveResultsToJson(const std::map<std::string, double>& timings, const std::string& filepath) {
    std::ofstream file(filepath);
    if (!file.is_open()) {
        std::cerr << "无法创建文件: " << filepath << std::endl;
        return;
    }

    file << "{";

    bool first = true;
    for (const auto& pair : timings) {
        if (!first) {
            file << ",";
        }
        first = false;

        file << "\"" << pair.first << "\":" << pair.second;
    }

    file << "}";
    file.close();

    std::cout << "结果已保存到: " << filepath << std::endl;
}

// 测量函数执行时间的辅助函数（微秒）
template<typename Func>
double measureExecutionTime(Func func, int iterations) {
    // 预热
    func();

    auto start = std::chrono::high_resolution_clock::now();

    // 多次执行以获得更准确的时间
    for (int i = 0; i < iterations; ++i) {
        func();
    }

    auto end = std::chrono::high_resolution_clock::now();
    std::chrono::duration<double, std::micro> duration = end - start;

    // 返回平均执行时间（微秒）
    return duration.count() / iterations;
}

int main(int argc, char* argv[]) {
    std::string dataDir;
    std::string outputFile;
    int iterations = 1000; // 默认迭代次数

    if (argc < 3) {
        std::cerr << "用法: " << argv[0] << " <数据目录> <输出文件> [迭代次数]" << std::endl;
        std::cerr << "使用默认值..." << std::endl;
        dataDir = "/tmp/tmpdir/data";
        outputFile = "/tmp/tmpdir/benchmark_results.json";
    } else {
        dataDir = argv[1];
        outputFile = argv[2];
        if (argc > 3) {
            iterations = std::stoi(argv[3]);
        }
    }

    // 确保输出目录存在
    fs::path outputPath(outputFile);
    fs::create_directories(outputPath.parent_path());

    std::cout << "从目录加载数据: " << dataDir << std::endl;
    std::cout << "迭代次数: " << iterations << std::endl;

    // 加载数据
    DataFrame open = loadCsv(dataDir + "/open.csv");
    DataFrame high = loadCsv(dataDir + "/high.csv");
    DataFrame low = loadCsv(dataDir + "/low.csv");
    DataFrame close = loadCsv(dataDir + "/close.csv");
    DataFrame volume = loadCsv(dataDir + "/volume.csv");
    DataFrame amount = loadCsv(dataDir + "/amount.csv");
    DataFrame vwap = loadCsv(dataDir + "/vwap.csv");

    if (open.rows() == 0 || high.rows() == 0 || low.rows() == 0 || close.rows() == 0 ||
        volume.rows() == 0 || amount.rows() == 0 || vwap.rows() == 0) {
        std::cerr << "加载数据失败!" << std::endl;
        return 1;
    }

    std::cout << "数据加载成功: " << open.rows() << " 行, " << open.cols() << " 列" << std::endl;

    // 创建分组标签
    DataFrame group_labels = DataFrame::Constant(close.rows(), close.cols(), 0);
    for (int i = 0; i < close.rows(); ++i) {
        for (int j = 0; j < close.cols(); ++j) {
            group_labels(i, j) = j % 3;  // 简单地按列索引模3分组
        }
    }

    // 确保数据没有NaN值
    DataFrame test_data_group = close;
    // 填充一些固定的值，确保每个分组都有数据
    for (int i = 0; i < test_data_group.rows(); ++i) {
        for (int j = 0; j < test_data_group.cols(); ++j) {
            test_data_group(i, j) = i * 10 + j;
        }
    }

    // 测量各种算子的执行时间
    std::map<std::string, double> timings;

    // 基本算术运算
    timings["add"] = measureExecutionTime([&]() {
        DataFrame result = Add(close, open);
        volatile double dummy = result(0, 0);  // 使用结果防止编译器优化
    }, iterations);
    timings["minus"] = measureExecutionTime([&]() {
        DataFrame result = Minus(close, open);
        volatile double dummy = result(0, 0);
    }, iterations);
    timings["multiply"] = measureExecutionTime([&]() {
        DataFrame result = Multiply(close, volume);
        volatile double dummy = result(0, 0);
    }, iterations);
    timings["divide"] = measureExecutionTime([&]() {
        DataFrame result = Divide(close, open);
        volatile double dummy = result(0, 0);
    }, iterations);
    timings["sqrt"] = measureExecutionTime([&]() {
        DataFrame result = Sqrt(volume);
        volatile double dummy = result(0, 0);
    }, iterations);
    timings["log"] = measureExecutionTime([&]() {
        DataFrame result = Log(volume);
        volatile double dummy = result(0, 0);
    }, iterations);
    timings["abs"] = measureExecutionTime([&]() {
        DataFrame result = Abs(Minus(close, open));
        volatile double dummy = result(0, 0);
    }, iterations);
    timings["max"] = measureExecutionTime([&]() {
        DataFrame result = Max(close, open);
        volatile double dummy = result(0, 0);
    }, iterations);
    timings["min"] = measureExecutionTime([&]() {
        DataFrame result = Min(close, open);
        volatile double dummy = result(0, 0);
    }, iterations);

    // 逻辑运算
    timings["and"] = measureExecutionTime([&]() {
        DataFrame result = And(close, open);
        volatile double dummy = result(0, 0);
    }, iterations);
    timings["or"] = measureExecutionTime([&]() {
        DataFrame result = Or(close, open);
        volatile double dummy = result(0, 0);
    }, iterations);
    timings["not"] = measureExecutionTime([&]() {
        DataFrame result = Not(close);
        volatile double dummy = result(0, 0);
    }, iterations);
    timings["xor"] = measureExecutionTime([&]() {
        DataFrame result = Xor(close, open);
        volatile double dummy = result(0, 0);
    }, iterations);

    // 时间序列运算
    timings["ts_delay"] = measureExecutionTime([&]() {
        DataFrame result = ts_Delay(close, 1);
        volatile double dummy = result(0, 0);
    }, iterations);
    timings["ts_mean"] = measureExecutionTime([&]() {
        DataFrame result = ts_Mean(close, 10);
        volatile double dummy = result(0, 0);
    }, iterations);
    timings["ts_sum"] = measureExecutionTime([&]() {
        DataFrame result = ts_Sum(volume, 10);
        volatile double dummy = result(0, 0);
    }, iterations);
    timings["ts_stdev"] = measureExecutionTime([&]() {
        DataFrame result = ts_Stdev(close, 10);
        volatile double dummy = result(0, 0);
    }, iterations);
    timings["ts_corr"] = measureExecutionTime([&]() {
        DataFrame result = ts_Corr(close, volume, 10);
        volatile double dummy = result(0, 0);
    }, iterations);
    timings["ts_min"] = measureExecutionTime([&]() {
        DataFrame result = ts_Min(close, 10);
        volatile double dummy = result(0, 0);
    }, iterations);
    timings["ts_max"] = measureExecutionTime([&]() {
        DataFrame result = ts_Max(close, 10);
        volatile double dummy = result(0, 0);
    }, iterations);
    timings["ts_delta"] = measureExecutionTime([&]() {
        DataFrame result = ts_Delta(close, 10);
        volatile double dummy = result(0, 0);
    }, iterations);
    timings["ts_divide"] = measureExecutionTime([&]() {
        DataFrame result = ts_Divide(close, 10);
        volatile double dummy = result(0, 0);
    }, iterations);
    timings["ts_chgrate"] = measureExecutionTime([&]() {
        DataFrame result = ts_ChgRate(close, 10);
        volatile double dummy = result(0, 0);
    }, iterations);
    timings["ts_median"] = measureExecutionTime([&]() {
        DataFrame result = ts_Median(close, 10);
        volatile double dummy = result(0, 0);
    }, iterations);
    timings["ts_skewness"] = measureExecutionTime([&]() {
        DataFrame result = ts_Skewness(close, 10);
        volatile double dummy = result(0, 0);
    }, iterations);
    timings["ts_kurtosis"] = measureExecutionTime([&]() {
        DataFrame result = ts_Kurtosis(close, 10);
        volatile double dummy = result(0, 0);
    }, iterations);
    timings["ts_scale"] = measureExecutionTime([&]() {
        DataFrame result = ts_Scale(close, 10);
        volatile double dummy = result(0, 0);
    }, iterations);
    timings["ts_product"] = measureExecutionTime([&]() {
        DataFrame result = ts_Product(close, 10);
        volatile double dummy = result(0, 0);
    }, iterations);
    timings["ts_transnorm"] = measureExecutionTime([&]() {
        DataFrame result = ts_TransNorm(close, 10);
        volatile double dummy = result(0, 0);
    }, iterations);
    timings["ts_decay"] = measureExecutionTime([&]() {
        DataFrame result = ts_Decay(close, 10);
        volatile double dummy = result(0, 0);
    }, iterations);
    timings["ts_decay2"] = measureExecutionTime([&]() {
        DataFrame result = ts_Decay2(close, 10);
        volatile double dummy = result(0, 0);
    }, iterations);
    timings["ts_partial_corr"] = measureExecutionTime([&]() {
        DataFrame result = ts_Partial_corr(close, open, volume, 10);
        volatile double dummy = result(0, 0);
    }, iterations);

    // 面板运算
    timings["pn_mean"] = measureExecutionTime([&]() {
        DataFrame result = pn_Mean(close);
        volatile double dummy = result(0, 0);
    }, iterations);
    timings["pn_rank"] = measureExecutionTime([&]() {
        DataFrame result = pn_Rank(close);
        volatile double dummy = result(0, 0);
    }, iterations);
    timings["pn_transnorm"] = measureExecutionTime([&]() {
        DataFrame result = pn_TransNorm(close);
        volatile double dummy = result(0, 0);
    }, iterations);
    timings["pn_stand"] = measureExecutionTime([&]() {
        DataFrame result = pn_Stand(close);
        volatile double dummy = result(0, 0);
    }, iterations);
    timings["pn_winsor"] = measureExecutionTime([&]() {
        DataFrame result = pn_Winsor(close, 2.0);
        volatile double dummy = result(0, 0);
    }, iterations);
    timings["pn_cut"] = measureExecutionTime([&]() {
        DataFrame result = pn_Cut(close);
        volatile double dummy = result(0, 0);
    }, iterations);
    timings["pn_groupneutral"] = measureExecutionTime([&]() {
        DataFrame result = pn_GroupNeutral(test_data_group, group_labels);
        volatile double dummy = result(0, 0);
    }, iterations);
    timings["pn_groupnorm"] = measureExecutionTime([&]() {
        DataFrame result = pn_GroupNorm(test_data_group, group_labels);
        volatile double dummy = result(0, 0);
    }, iterations);
    timings["pn_grouprank"] = measureExecutionTime([&]() {
        DataFrame result = pn_GroupRank(test_data_group, group_labels);
        volatile double dummy = result(0, 0);
    }, iterations);

    // 保存结果
    saveResultsToJson(timings, outputFile);

    // 打印执行时间
    std::cout << "算子执行时间统计 (微秒):" << std::endl;
    for (const auto& pair : timings) {
        std::cout << std::setw(20) << pair.first << ": " << std::fixed << std::setprecision(3) << pair.second << " μs" << std::endl;
    }

    std::cout << "测试完成!" << std::endl;
    return 0;
}
