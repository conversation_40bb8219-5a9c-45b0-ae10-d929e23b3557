#pragma once

#include <Eigen/Dense>
#include <algorithm>
#include <cmath>
#include <iostream>
#include <limits>
#include <map>
#include <numeric>
#include <string>
#include <vector>
#include <iomanip>

namespace optimized_operators {

// 定义数据类型
using DataFrame = Eigen::MatrixXd;
using Series = Eigen::VectorXd;
using Index = std::vector<int64_t>;       // 时间戳索引
using Columns = std::vector<std::string>; // 列名（合约名）

// 数据框架结构，包含数据、索引和列名
struct DataFrameWithIndex {
  DataFrame data;
  Index index;
  Columns columns;

  // 构造函数
  DataFrameWithIndex() = default;

  DataFrameWithIndex(const DataFrame &data, const Index &index,
                     const Columns &columns)
      : data(data), index(index), columns(columns) {}

  // 获取行数和列数
  int rows() const { return data.rows(); }
  int cols() const { return data.cols(); }

  // 检查是否为空
  bool empty() const { return data.size() == 0; }
};

// 辅助函数
bool isNaN(double value);
bool isInf(double value);
DataFrame fillNaN(const DataFrame &data, double value);
DataFrame replaceInf(const DataFrame &data, double value);
double normInv(double p); // 正态分布逆函数

// 基本算术运算
DataFrame Add(const DataFrame &s1, const DataFrame &s2);
DataFrame Minus(const DataFrame &s1, const DataFrame &s2);
DataFrame Multiply(const DataFrame &s1, const DataFrame &s2);
DataFrame Divide(const DataFrame &s1, const DataFrame &s2);
DataFrame Sqrt(const DataFrame &s1);
DataFrame Log(const DataFrame &s1);
DataFrame Inv(const DataFrame &s1);
DataFrame Power(const DataFrame &s1, int n);
DataFrame Power(const DataFrame &s1, double n);
DataFrame Abs(const DataFrame &s1);
DataFrame Sign(const DataFrame &s1);
DataFrame Exp(const DataFrame &s1);
DataFrame Reverse(const DataFrame &s1);
DataFrame Ceil(const DataFrame &s1);
DataFrame Floor(const DataFrame &s1);
DataFrame Round(const DataFrame &s1);
DataFrame SignedPower(const DataFrame &s1, double n);
DataFrame Softsign(const DataFrame &s1);

// 条件运算
DataFrame IfThen(const DataFrame &condition, const DataFrame &value_true,
                 const DataFrame &value_false);
DataFrame IfThen(const DataFrame &condition, double value_true,
                 const DataFrame &value_false);
DataFrame IfThen(const DataFrame &condition, const DataFrame &value_true,
                 double value_false);
DataFrame IfThen(const DataFrame &condition, double value_true,
                 double value_false);

// 逻辑运算
DataFrame And(const DataFrame &s1, const DataFrame &s2);
DataFrame Or(const DataFrame &s1, const DataFrame &s2);
DataFrame Not(const DataFrame &s1);
DataFrame Xor(const DataFrame &s1, const DataFrame &s2);
DataFrame Equal(const DataFrame &s1, const DataFrame &s2);
DataFrame UnEqual(const DataFrame &s1, const DataFrame &s2);
DataFrame Mthan(const DataFrame &s1, const DataFrame &s2);
DataFrame MEthan(const DataFrame &s1, const DataFrame &s2);
DataFrame Lthan(const DataFrame &s1, const DataFrame &s2);
DataFrame LEthan(const DataFrame &s1, const DataFrame &s2);

// 时间序列运算
DataFrame ts_Delay(const DataFrame &s1, int n);
DataFrame ts_Mean(const DataFrame &s1, int n);
DataFrame ts_Sum(const DataFrame &s1, int n);
DataFrame ts_Stdev(const DataFrame &s1, int n);
DataFrame ts_Corr(const DataFrame &s1, const DataFrame &s2, int n);
DataFrame ts_Cov(const DataFrame &s1, const DataFrame &s2, int n);
DataFrame ts_Delta(const DataFrame &s1, int n);
DataFrame ts_Divide(const DataFrame &s1, int n);
DataFrame ts_ChgRate(const DataFrame &s1, int n);
DataFrame ts_ArgMax(const DataFrame &s1, int n);
DataFrame ts_ArgMin(const DataFrame &s1, int n);
DataFrame ts_Rank(const DataFrame &s1, int n);
DataFrame ts_Min(const DataFrame &s1, int n);
DataFrame ts_Max(const DataFrame &s1, int n);
DataFrame ts_Median(const DataFrame &s1, int n);
DataFrame ts_Skewness(const DataFrame &s1, int n);
DataFrame ts_Kurtosis(const DataFrame &s1, int n);
DataFrame ts_Scale(const DataFrame &s1, int n);
DataFrame ts_Product(const DataFrame &s1, int n);
DataFrame ts_TransNorm(const DataFrame &s1, int n);
DataFrame ts_Decay(const DataFrame &s1, int n);
DataFrame ts_Decay2(const DataFrame &s1, int n);
DataFrame ts_Partial_corr(const DataFrame &s1, const DataFrame &s2, const DataFrame &s3, int n);
DataFrame ts_Regression(const DataFrame &s1, const DataFrame &s2, int n, char rettype);
DataFrame ts_Entropy(const DataFrame &s1, int n);
DataFrame ts_MaxDD(const DataFrame &s1, int n);
DataFrame ts_MeanChg(const DataFrame &s1, int n);
DataFrame ts_Quantile(const DataFrame &s1, int n, double rettype);

// 横截面运算
DataFrame pn_Mean(const DataFrame &s1);
DataFrame pn_Rank(const DataFrame &s1);
DataFrame pn_TransNorm(const DataFrame &s1);
DataFrame pn_Rank2(const DataFrame &s1);
DataFrame pn_RankCentered(const DataFrame &s1);
DataFrame pn_FillMax(const DataFrame &s1);
DataFrame pn_FillMin(const DataFrame &s1);
DataFrame pn_TransStd(const DataFrame &s1);
DataFrame pn_Stand(const DataFrame &s1);
DataFrame pn_Winsor(const DataFrame &s1, double Multiplier);
DataFrame pn_Cut(const DataFrame &s1);
DataFrame pn_GroupRank(const DataFrame &s1, const DataFrame &group);
DataFrame pn_GroupNorm(const DataFrame &s1, const DataFrame &group);
DataFrame pn_GroupNeutral(const DataFrame &s1, const DataFrame &group);
DataFrame pn_CrossFit(const DataFrame &y, const DataFrame &x);

// 特殊函数
DataFrame getNan(const DataFrame &s1);
DataFrame getInf(const DataFrame &s1);
DataFrame FilterInf(const DataFrame &s1);
DataFrame Max(const DataFrame &s1, const DataFrame &s2);
DataFrame Min(const DataFrame &s1, const DataFrame &s2);
DataFrame Tot_Max(const DataFrame &s1);
DataFrame Tot_Min(const DataFrame &s1);

// Tot系列函数（包装函数）
DataFrame Tot_Mean(const DataFrame &s1);
DataFrame Tot_Sum(const DataFrame &s1);
DataFrame Tot_Stdev(const DataFrame &s1);
DataFrame Tot_Delta(const DataFrame &s1);
DataFrame Tot_Divide(const DataFrame &s1);
DataFrame Tot_ChgRate(const DataFrame &s1);
DataFrame Tot_Rank(const DataFrame &s1);
DataFrame Tot_ArgMax(const DataFrame &s1);
DataFrame Tot_ArgMin(const DataFrame &s1);

// 打印函数
static void printDataFrame(const DataFrame &df, int precision = 4, int width = 10,
                    bool scientific = false, bool showSize = true,
                    const std::string &name = "");

} // namespace optimized_operators
