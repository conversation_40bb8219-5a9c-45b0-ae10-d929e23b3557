#include "feature_operators.h"
#include <fstream>
#include <iomanip>
#include <iostream>
#include <sstream>
#include <string>
#include <vector>

using namespace feature_operators;

// Function to read CSV file into DataFrame
DataFrame readCSV(const std::string &filename) {
  std::ifstream file(filename);
  if (!file.is_open()) {
    std::cerr << "Error opening file: " << filename << std::endl;
    return DataFrame();
  }

  std::string line;
  std::vector<std::vector<double>> data;
  std::vector<std::string> headers;

  // Read header
  if (std::getline(file, line)) {
    std::stringstream ss(line);
    std::string cell;

    // Skip the first column (timestamp)
    std::getline(ss, cell, ',');

    while (std::getline(ss, cell, ',')) {
      headers.push_back(cell);
    }
  }

  // Read data
  while (std::getline(file, line)) {
    std::stringstream ss(line);
    std::string cell;
    std::vector<double> row;

    // Read timestamp (first column) but don't store it
    std::getline(ss, cell, ',');

    // Read the rest of the columns
    while (std::getline(ss, cell, ',')) {
      if (cell.empty()) {
        row.push_back(std::numeric_limits<double>::quiet_NaN());
      } else {
        try {
          row.push_back(std::stod(cell));
        } catch (const std::exception &e) {
          row.push_back(std::numeric_limits<double>::quiet_NaN());
        }
      }
    }

    if (!row.empty()) {
      data.push_back(row);
    }
  }

  // Convert to Eigen DataFrame
  int rows = data.size();
  int cols = (rows > 0) ? data[0].size() : 0;

  DataFrame df(rows, cols);
  for (int i = 0; i < rows; ++i) {
    for (int j = 0; j < cols; ++j) {
      df(i, j) = data[i][j];
    }
  }

  return df;
}

// Function to write DataFrame to CSV file
void writeCSV(const DataFrame &df, const std::string &filename) {
  std::ofstream file(filename);
  if (!file.is_open()) {
    std::cerr << "Error opening file for writing: " << filename << std::endl;
    return;
  }

  // Use scientific notation with 10 decimal places for high precision
  file << std::scientific << std::setprecision(10);

  for (int i = 0; i < df.rows(); ++i) {
    for (int j = 0; j < df.cols(); ++j) {
      if (j > 0)
        file << ",";
      if (std::isnan(df(i, j))) {
        file << "NaN";
      } else if (std::isinf(df(i, j))) {
        file << (df(i, j) > 0 ? "Inf" : "-Inf");
      } else {
        file << df(i, j);
      }
    }
    file << "\n";
  }
}

int main() {
  std::cout << "Loading test data..." << std::endl;
  std::string data_path =
      "/home/<USER>/git/realTime/test_eigen/test_data/";
  std::string res_path = "/home/<USER>/git/realTime/test_eigen/"
                         "test_results/unoptimized/";

  // Load test data
  DataFrame close = readCSV(data_path + "close.csv");
  DataFrame open = readCSV(data_path + "open.csv");
  DataFrame high = readCSV(data_path + "high.csv");
  DataFrame low = readCSV(data_path + "low.csv");
  DataFrame volume = readCSV(data_path + "volume.csv");

  std::cout << "Data loaded. Rows: " << close.rows()
            << ", Cols: " << close.cols() << std::endl;

  // Create output directory
  system("mkdir -p "
         "/home/<USER>/git/realTime/test_eigen/test_results/"
         "unoptimized");

  // 测试基本算术运算符
  std::cout << "Testing basic arithmetic operators..." << std::endl;
  writeCSV(Add(close, open), res_path + "Add.csv");
  writeCSV(Minus(close, open), res_path + "Minus.csv");
  writeCSV(Multiply(close, open), res_path + "Multiply.csv");
  writeCSV(Divide(close, open), res_path + "Divide.csv");
  writeCSV(Sqrt(close), res_path + "Sqrt.csv");
  writeCSV(Log(close), res_path + "Log.csv");
  writeCSV(Inv(close), res_path + "Inv.csv");
  writeCSV(Power(close, 2), res_path + "Power.csv");
  writeCSV(Abs(Minus(close, open)), res_path + "Abs.csv");
  writeCSV(Sign(Minus(close, open)), res_path + "Sign.csv");
  writeCSV(Exp(Log(close)), res_path + "Exp.csv");
  writeCSV(Reverse(close), res_path + "Reverse.csv");
  writeCSV(Ceil(close), res_path + "Ceil.csv");
  writeCSV(Floor(close), res_path + "Floor.csv");
  writeCSV(Round(close), res_path + "Round.csv");
  writeCSV(SignedPower(close, 2.0), res_path + "SignedPower.csv");

  // 测试逻辑运算符
  std::cout << "Testing logical operators..." << std::endl;
  try {
    writeCSV(And(close, open), res_path + "And.csv");
    writeCSV(Or(close, open), res_path + "Or.csv");
    writeCSV(Not(close), res_path + "Not.csv");
    writeCSV(Xor(close, open), res_path + "Xor.csv");
    writeCSV(Equal(close, open), res_path + "Equal.csv");
    writeCSV(UnEqual(close, open), res_path + "UnEqual.csv");
    writeCSV(Mthan(close, open), res_path + "Mthan.csv");
    writeCSV(MEthan(close, open), res_path + "MEthan.csv");
    writeCSV(Lthan(close, open), res_path + "Lthan.csv");
    writeCSV(LEthan(close, open), res_path + "LEthan.csv");
  } catch (const std::exception &e) {
    std::cerr << "Error in logical operators: " << e.what() << std::endl;
  }

  // 测试时间序列运算符
  std::cout << "Testing time series operators..." << std::endl;
  writeCSV(ts_Delay(close, 5), res_path + "ts_Delay.csv");
  writeCSV(ts_Mean(close, 10), res_path + "ts_Mean.csv");
  writeCSV(ts_Sum(close, 10), res_path + "ts_Sum.csv");
  writeCSV(ts_Stdev(close, 10), res_path + "ts_Stdev.csv");
  writeCSV(ts_Min(close, 10), res_path + "ts_Min.csv");
  writeCSV(ts_Max(close, 10), res_path + "ts_Max.csv");
  writeCSV(ts_Delta(close, 5), res_path + "ts_Delta.csv");
  writeCSV(ts_Divide(close, 5), res_path + "ts_Divide.csv");
  writeCSV(ts_ChgRate(close, 5), res_path + "ts_ChgRate.csv");
  writeCSV(ts_ArgMax(close, 10), res_path + "ts_ArgMax.csv");
  writeCSV(ts_ArgMin(close, 10), res_path + "ts_ArgMin.csv");
  writeCSV(ts_Rank(close, 10), res_path + "ts_Rank.csv");
  writeCSV(ts_Median(close, 10), res_path + "ts_Median.csv");

  try {
    writeCSV(ts_Corr(close, volume, 10), res_path + "ts_Corr.csv");
    writeCSV(ts_Cov(close, volume, 10), res_path + "ts_Cov.csv");
    writeCSV(ts_Skewness(close, 10), res_path + "ts_Skewness.csv");
    writeCSV(ts_Kurtosis(close, 10), res_path + "ts_Kurtosis.csv");
    writeCSV(ts_Scale(close, 10), res_path + "ts_Scale.csv");
    writeCSV(ts_Product(close, 10), res_path + "ts_Product.csv");
    writeCSV(ts_TransNorm(close, 10), res_path + "ts_TransNorm.csv");
    writeCSV(ts_Decay(close, 10), res_path + "ts_Decay.csv");
    writeCSV(ts_Decay2(close, 10), res_path + "ts_Decay2.csv");
    writeCSV(ts_Partial_corr(close, open, high, 10),
             res_path + "ts_Partial_corr.csv");
    writeCSV(ts_Regression(close, open, 10, 'a'),
             res_path + "ts_Regression.csv");
    writeCSV(ts_Entropy(close, 10), res_path + "ts_Entropy.csv");
    writeCSV(ts_MaxDD(close, 10), res_path + "ts_MaxDD.csv");
    writeCSV(ts_MeanChg(close, 10), res_path + "ts_MeanChg.csv");
    writeCSV(ts_Quantile(close, 10, 0.5), res_path + "ts_Quantile.csv");
  } catch (const std::exception &e) {
    std::cerr << "Error in advanced time series operators: " << e.what()
              << std::endl;
  }

  // 测试横截面运算符
  std::cout << "Testing cross-sectional operators..." << std::endl;
  writeCSV(pn_Mean(close), res_path + "pn_Mean.csv");
  writeCSV(pn_Rank(close), res_path + "pn_Rank.csv");
  writeCSV(pn_Stand(close), res_path + "pn_Stand.csv");

  try {
    writeCSV(pn_TransNorm(close), res_path + "pn_TransNorm.csv");
    writeCSV(pn_Rank2(close), res_path + "pn_Rank2.csv");
    writeCSV(pn_RankCentered(close), res_path + "pn_RankCentered.csv");
    writeCSV(pn_FillMax(close), res_path + "pn_FillMax.csv");
    writeCSV(pn_FillMin(close), res_path + "pn_FillMin.csv");
    writeCSV(pn_TransStd(close), res_path + "pn_TransStd.csv");
    writeCSV(pn_Winsor(close, 3.0), res_path + "pn_Winsor.csv");
    writeCSV(pn_Cut(close), res_path + "pn_Cut.csv");

    // 创建一个简单的分组矩阵用于分组操作
    DataFrame group = DataFrame::Zero(close.rows(), close.cols());
    for (int j = 0; j < close.cols(); ++j) {
      group.col(j).setConstant(j % 5); // 将列分成5组
    }

    writeCSV(pn_GroupRank(close, group), res_path + "pn_GroupRank.csv");
    writeCSV(pn_GroupNorm(close, group), res_path + "pn_GroupNorm.csv");
    writeCSV(pn_GroupNeutral(close, group), res_path + "pn_GroupNeutral.csv");
    writeCSV(pn_CrossFit(close, open), res_path + "pn_CrossFit.csv");
  } catch (const std::exception &e) {
    std::cerr << "Error in advanced cross-sectional operators: " << e.what()
              << std::endl;
  }

  // 测试特殊函数
  std::cout << "Testing special functions..." << std::endl;
  writeCSV(getNan(close), res_path + "getNan.csv");
  writeCSV(getInf(close), res_path + "getInf.csv");
  writeCSV(FilterInf(close), res_path + "FilterInf.csv");
  writeCSV(Max(high, low), res_path + "Max.csv");
  writeCSV(Min(high, low), res_path + "Min.csv");

  // 测试 Tot 系列函数
  std::cout << "Testing Tot series functions..." << std::endl;
  writeCSV(Tot_Mean(close), res_path + "Tot_Mean.csv");
  writeCSV(Tot_Sum(close), res_path + "Tot_Sum.csv");
  writeCSV(Tot_Stdev(close), res_path + "Tot_Stdev.csv");
  writeCSV(Tot_Delta(close), res_path + "Tot_Delta.csv");
  writeCSV(Tot_Divide(close), res_path + "Tot_Divide.csv");
  writeCSV(Tot_ChgRate(close), res_path + "Tot_ChgRate.csv");
  writeCSV(Tot_Rank(close), res_path + "Tot_Rank.csv");
  writeCSV(Tot_ArgMax(close), res_path + "Tot_ArgMax.csv");
  writeCSV(Tot_ArgMin(close), res_path + "Tot_ArgMin.csv");
  writeCSV(Tot_Max(close), res_path + "Tot_Max.csv");
  writeCSV(Tot_Min(close), res_path + "Tot_Min.csv");

  std::cout << "Unoptimized operator tests completed." << std::endl;
  return 0;
}
