#!/usr/bin/env python3
import os
import sys
import pandas as pd
import numpy as np

# 添加 feature_operator_funcs.py 的路径
sys.path.append('/home/<USER>/git/realTime/Code/strategy')
from feature_operator_funcs import *

def load_csv(filepath):
    """加载 CSV 文件到 pandas DataFrame。"""
    try:
        df = pd.read_csv(filepath, index_col=0)
        return df
    except Exception as e:
        print(f"加载 {filepath} 出错: {e}")
        return None

def save_csv(df, filepath):
    """将 DataFrame 保存为高精度 CSV 文件。"""
    try:
        os.makedirs(os.path.dirname(filepath), exist_ok=True)
        # 使用科学计数法，保留 10 位小数以获得高精度
        df.to_csv(filepath, float_format='%.10e')
        print(f"已保存到 {filepath}")
    except Exception as e:
        print(f"保存到 {filepath} 出错: {e}")

def main():
    print("加载测试数据...")

    # 定义数据目录和输出目录
    data_dir = "/home/<USER>/git/realTime/test_eigen/test_data"
    output_dir = "/home/<USER>/git/realTime/test_eigen/test_results/python"

    # 创建输出目录
    os.makedirs(output_dir, exist_ok=True)

    # 加载测试数据
    close = load_csv(os.path.join(data_dir, "close.csv"))
    open_price = load_csv(os.path.join(data_dir, "open.csv"))
    high = load_csv(os.path.join(data_dir, "high.csv"))
    low = load_csv(os.path.join(data_dir, "low.csv"))
    volume = load_csv(os.path.join(data_dir, "volume.csv"))

    if close is None or open_price is None or high is None or low is None or volume is None:
        print("加载数据文件失败。")
        return

    print(f"数据已加载。形状: {close.shape}")

    # 测试基本算术运算符
    print("测试基本算术运算符...")
    save_csv(Add(close, open_price), os.path.join(output_dir, "Add.csv"))
    save_csv(Minus(close, open_price), os.path.join(output_dir, "Minus.csv"))
    save_csv(Multiply(close, open_price), os.path.join(output_dir, "Multiply.csv"))
    save_csv(Divide(close, open_price), os.path.join(output_dir, "Divide.csv"))
    save_csv(Sqrt(close), os.path.join(output_dir, "Sqrt.csv"))
    save_csv(Log(close), os.path.join(output_dir, "Log.csv"))
    save_csv(inv(close), os.path.join(output_dir, "Inv.csv"))
    save_csv(Power(close, 2), os.path.join(output_dir, "Power.csv"))
    save_csv(Abs(Minus(close, open_price)), os.path.join(output_dir, "Abs.csv"))
    save_csv(Sign(Minus(close, open_price)), os.path.join(output_dir, "Sign.csv"))
    save_csv(Exp(Log(close)), os.path.join(output_dir, "Exp.csv"))
    save_csv(Reverse(close), os.path.join(output_dir, "Reverse.csv"))
    save_csv(Ceil(close), os.path.join(output_dir, "Ceil.csv"))
    save_csv(Floor(close), os.path.join(output_dir, "Floor.csv"))
    save_csv(Round(close), os.path.join(output_dir, "Round.csv"))
    save_csv(SignedPower(close, 2.0), os.path.join(output_dir, "SignedPower.csv"))

    # 测试逻辑运算符
    print("测试逻辑运算符...")
    try:
        save_csv(And(close, open_price), os.path.join(output_dir, "And.csv"))
        save_csv(Or(close, open_price), os.path.join(output_dir, "Or.csv"))
        save_csv(Not(close), os.path.join(output_dir, "Not.csv"))
        save_csv(Xor(close, open_price), os.path.join(output_dir, "Xor.csv"))
        save_csv(Equal(close, open_price), os.path.join(output_dir, "Equal.csv"))
        save_csv(UnEqual(close, open_price), os.path.join(output_dir, "UnEqual.csv"))
        save_csv(Mthan(close, open_price), os.path.join(output_dir, "Mthan.csv"))
        save_csv(MEthan(close, open_price), os.path.join(output_dir, "MEthan.csv"))
        save_csv(Lthan(close, open_price), os.path.join(output_dir, "Lthan.csv"))
        save_csv(LEthan(close, open_price), os.path.join(output_dir, "LEthan.csv"))
    except Exception as e:
        print(f"逻辑运算符错误: {e}")

    # 测试时间序列运算符
    print("测试时间序列运算符...")
    save_csv(ts_Delay(close, 5), os.path.join(output_dir, "ts_Delay.csv"))
    save_csv(ts_Mean(close, 10), os.path.join(output_dir, "ts_Mean.csv"))
    save_csv(ts_Sum(close, 10), os.path.join(output_dir, "ts_Sum.csv"))

    # Python 没有直接的 ts_Stdev，但我们可以使用 pandas 的 std
    ts_stdev = close.rolling(10, min_periods=1).std(ddof=0)
    save_csv(ts_stdev, os.path.join(output_dir, "ts_Stdev.csv"))

    save_csv(ts_Min(close, 10), os.path.join(output_dir, "ts_Min.csv"))
    save_csv(ts_Max(close, 10), os.path.join(output_dir, "ts_Max.csv"))
    save_csv(ts_Delta(close, 5), os.path.join(output_dir, "ts_Delta.csv"))
    save_csv(ts_Divide(close, 5), os.path.join(output_dir, "ts_Divide.csv"))
    save_csv(ts_ChgRate(close, 5), os.path.join(output_dir, "ts_ChgRate.csv"))

    try:
        save_csv(ts_ArgMax(close, 10), os.path.join(output_dir, "ts_ArgMax.csv"))
        save_csv(ts_ArgMin(close, 10), os.path.join(output_dir, "ts_ArgMin.csv"))
        save_csv(ts_Rank(close, 10), os.path.join(output_dir, "ts_Rank.csv"))
        save_csv(ts_Median(close, 10), os.path.join(output_dir, "ts_Median.csv"))
        save_csv(ts_Corr(close, volume, 10), os.path.join(output_dir, "ts_Corr.csv"))
        save_csv(ts_Cov(close, volume, 10), os.path.join(output_dir, "ts_Cov.csv"))
        save_csv(ts_Skewness(close, 10), os.path.join(output_dir, "ts_Skewness.csv"))
        save_csv(ts_Kurtosis(close, 10), os.path.join(output_dir, "ts_Kurtosis.csv"))
        save_csv(ts_Scale(close, 10), os.path.join(output_dir, "ts_Scale.csv"))
        save_csv(ts_Product(close, 10), os.path.join(output_dir, "ts_Product.csv"))
        save_csv(ts_TransNorm(close, 10), os.path.join(output_dir, "ts_TransNorm.csv"))
        save_csv(ts_Decay(close, 10), os.path.join(output_dir, "ts_Decay.csv"))
        save_csv(ts_Decay2(close, 10), os.path.join(output_dir, "ts_Decay2.csv"))
        save_csv(ts_Partial_corr(close, open_price, high, 10), os.path.join(output_dir, "ts_Partial_corr.csv"))
        save_csv(ts_Regression(close, open_price, 10, 'a'), os.path.join(output_dir, "ts_Regression.csv"))
        save_csv(ts_Entropy(close, 10), os.path.join(output_dir, "ts_Entropy.csv"))
        save_csv(ts_MaxDD(close, 10), os.path.join(output_dir, "ts_MaxDD.csv"))
        save_csv(ts_MeanChg(close, 10), os.path.join(output_dir, "ts_MeanChg.csv"))
        save_csv(ts_Quantile(close, 10, 0.5), os.path.join(output_dir, "ts_Quantile.csv"))
    except Exception as e:
        print(f"高级时间序列运算符错误: {e}")

    # 测试横截面运算符
    print("测试横截面运算符...")
    # 对于 pn_Mean，我们需要使其与 C++ 版本匹配
    # C++ 版本返回的是一个多列的 DataFrame，每列都是相同的均值
    # 而不是一个单列的 DataFrame
    # 同时，我们需要确保 NaN 的处理方式与 C++ 一致
    # 在 C++ 中，如果一行全是 NaN，则结果也是 NaN
    # 如果一行有一些 NaN，则忽略这些 NaN 计算均值

    # 首先，创建一个与 close 相同形状的 DataFrame
    pn_mean = pd.DataFrame(np.zeros(close.shape), index=close.index, columns=close.columns)

    # 对每一行计算均值，忽略 NaN
    for i in range(close.shape[0]):
        row = close.iloc[i, :]
        if row.isna().all():
            # 如果一行全是 NaN，则结果也是 NaN
            pn_mean.iloc[i, :] = np.nan
        else:
            # 否则，计算均值，忽略 NaN
            mean_value = row.mean(skipna=True)
            pn_mean.iloc[i, :] = mean_value

    save_csv(pn_mean, os.path.join(output_dir, "pn_Mean.csv"))
    save_csv(pn_Rank(close), os.path.join(output_dir, "pn_Rank.csv"))
    save_csv(pn_Stand(close), os.path.join(output_dir, "pn_Stand.csv"))

    try:
        save_csv(pn_TransNorm(close), os.path.join(output_dir, "pn_TransNorm.csv"))
        save_csv(pn_Rank2(close), os.path.join(output_dir, "pn_Rank2.csv"))
        save_csv(pn_RankCentered(close), os.path.join(output_dir, "pn_RankCentered.csv"))
        save_csv(pn_FillMax(close), os.path.join(output_dir, "pn_FillMax.csv"))
        save_csv(pn_FillMin(close), os.path.join(output_dir, "pn_FillMin.csv"))
        save_csv(pn_TransStd(close), os.path.join(output_dir, "pn_TransStd.csv"))
        save_csv(pn_Winsor(close, 3.0), os.path.join(output_dir, "pn_Winsor.csv"))
        save_csv(pn_Cut(close), os.path.join(output_dir, "pn_Cut.csv"))

        # 创建一个简单的分组矩阵用于分组操作
        group = pd.DataFrame(np.zeros(close.shape), index=close.index, columns=close.columns)
        for j, col in enumerate(group.columns):
            group[col] = j % 5  # 将列分成5组

        save_csv(pn_GroupRank(close, group), os.path.join(output_dir, "pn_GroupRank.csv"))
        save_csv(pn_GroupNorm(close, group), os.path.join(output_dir, "pn_GroupNorm.csv"))
        save_csv(pn_GroupNeutral(close, group), os.path.join(output_dir, "pn_GroupNeutral.csv"))
        save_csv(pn_CrossFit(close, open_price), os.path.join(output_dir, "pn_CrossFit.csv"))
    except Exception as e:
        print(f"高级横截面运算符错误: {e}")

    # 测试特殊函数
    print("测试特殊函数...")
    save_csv(getNan(close), os.path.join(output_dir, "getNan.csv"))
    save_csv(getInf(close), os.path.join(output_dir, "getInf.csv"))
    save_csv(FilterInf(close), os.path.join(output_dir, "FilterInf.csv"))
    save_csv(Max(high, low), os.path.join(output_dir, "Max.csv"))
    save_csv(Min(high, low), os.path.join(output_dir, "Min.csv"))

    # 测试 Tot 系列函数
    print("测试 Tot 系列函数...")
    save_csv(Tot_Mean(close), os.path.join(output_dir, "Tot_Mean.csv"))
    save_csv(Tot_Sum(close), os.path.join(output_dir, "Tot_Sum.csv"))
    save_csv(Tot_Stdev(close), os.path.join(output_dir, "Tot_Stdev.csv"))
    save_csv(Tot_Delta(close), os.path.join(output_dir, "Tot_Delta.csv"))
    save_csv(Tot_Divide(close), os.path.join(output_dir, "Tot_Divide.csv"))
    save_csv(Tot_ChgRate(close), os.path.join(output_dir, "Tot_ChgRate.csv"))
    save_csv(Tot_Rank(close), os.path.join(output_dir, "Tot_Rank.csv"))
    save_csv(Tot_ArgMax(close), os.path.join(output_dir, "Tot_ArgMax.csv"))
    save_csv(Tot_ArgMin(close), os.path.join(output_dir, "Tot_ArgMin.csv"))
    save_csv(Tot_Max(close), os.path.join(output_dir, "Tot_Max.csv"))
    save_csv(Tot_Min(close), os.path.join(output_dir, "Tot_Min.csv"))

    print("Python 算子测试完成。")

if __name__ == "__main__":
    main()
