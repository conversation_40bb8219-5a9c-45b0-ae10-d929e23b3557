#pragma once

#include "feature_operators.h"
#include <Eigen/Dense>
#include <string>
#include <map>
#include <functional>
#include <vector>

namespace factor_calculator {

using namespace feature_operators;

// 定义因子计算函数类型
using FactorFunction = std::function<void(const DataFrame&, const DataFrame&, const DataFrame&,
                                         const DataFrame&, const DataFrame&, const DataFrame&,
                                         const DataFrame&, DataFrame&)>;

// 因子组类型
struct FactorGroup {
    std::string type;
    std::vector<std::string> names;
    std::vector<FactorFunction> functions;
};

// 获取所有因子组
std::vector<FactorGroup> get_all_factor_groups();

// 计算单个因子
void calculate_factor(const std::string& factor_name,
                     const DataFrame& open,
                     const DataFrame& high,
                     const DataFrame& low,
                     const DataFrame& close,
                     const DataFrame& volume,
                     const DataFrame& amount,
                     const DataFrame& vwap,
                     DataFrame& result);

// 计算多个因子
void calculate_factors(const std::vector<std::string>& factor_names,
                      const DataFrame& open,
                      const DataFrame& high,
                      const DataFrame& low,
                      const DataFrame& close,
                      const DataFrame& volume,
                      const DataFrame& amount,
                      const DataFrame& vwap,
                      std::map<std::string, DataFrame>& results);

// 各个因子组的实现
namespace p1_corrs {
    // 相关性因子组
    void factor0(const DataFrame& open, const DataFrame& high, const DataFrame& low,
                const DataFrame& close, const DataFrame& volume, const DataFrame& amount,
                const DataFrame& vwap, DataFrame& result);

    void factor1(const DataFrame& open, const DataFrame& high, const DataFrame& low,
                const DataFrame& close, const DataFrame& volume, const DataFrame& amount,
                const DataFrame& vwap, DataFrame& result);

    // 其他因子...

    // 获取所有p1_corrs因子
    FactorGroup get_factors();
}

namespace p2_et {
    // 事件因子组
    void factor0(const DataFrame& open, const DataFrame& high, const DataFrame& low,
                const DataFrame& close, const DataFrame& volume, const DataFrame& amount,
                const DataFrame& vwap, DataFrame& result);

    void factor1(const DataFrame& open, const DataFrame& high, const DataFrame& low,
                const DataFrame& close, const DataFrame& volume, const DataFrame& amount,
                const DataFrame& vwap, DataFrame& result);

    // 其他因子...

    // 获取所有p2_et因子
    FactorGroup get_factors();
}

namespace p3_mf {
    // 动量因子组
    void factor0(const DataFrame& open, const DataFrame& high, const DataFrame& low,
                const DataFrame& close, const DataFrame& volume, const DataFrame& amount,
                const DataFrame& vwap, DataFrame& result);

    void factor1(const DataFrame& open, const DataFrame& high, const DataFrame& low,
                const DataFrame& close, const DataFrame& volume, const DataFrame& amount,
                const DataFrame& vwap, DataFrame& result);

    // 其他因子...

    // 获取所有p3_mf因子
    FactorGroup get_factors();
}

namespace p4_ms {
    // 市场结构因子组
    void factor0(const DataFrame& open, const DataFrame& high, const DataFrame& low,
                const DataFrame& close, const DataFrame& volume, const DataFrame& amount,
                const DataFrame& vwap, DataFrame& result);

    void factor1(const DataFrame& open, const DataFrame& high, const DataFrame& low,
                const DataFrame& close, const DataFrame& volume, const DataFrame& amount,
                const DataFrame& vwap, DataFrame& result);

    // 其他因子...

    // 获取所有p4_ms因子
    FactorGroup get_factors();
}

namespace p5_to {
    // 交易订单因子组
    void factor0(const DataFrame& open, const DataFrame& high, const DataFrame& low,
                const DataFrame& close, const DataFrame& volume, const DataFrame& amount,
                const DataFrame& vwap, DataFrame& result);

    void factor1(const DataFrame& open, const DataFrame& high, const DataFrame& low,
                const DataFrame& close, const DataFrame& volume, const DataFrame& amount,
                const DataFrame& vwap, DataFrame& result);

    // 其他因子...

    // 获取所有p5_to因子
    FactorGroup get_factors();
}

namespace p6_tn {
    // 技术指标因子组
    void factor0(const DataFrame& open, const DataFrame& high, const DataFrame& low,
                const DataFrame& close, const DataFrame& volume, const DataFrame& amount,
                const DataFrame& vwap, DataFrame& result);

    void factor1(const DataFrame& open, const DataFrame& high, const DataFrame& low,
                const DataFrame& close, const DataFrame& volume, const DataFrame& amount,
                const DataFrame& vwap, DataFrame& result);

    // 其他因子...

    // 获取所有p6_tn因子
    FactorGroup get_factors();
}

} // namespace factor_calculator
