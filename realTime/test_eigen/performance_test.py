#!/usr/bin/env python3
# -*- coding: utf-8 -*-

import sys
import os
import numpy as np
import pandas as pd
import time
import json
import subprocess
import tempfile
from datetime import datetime

# 添加Python因子计算路径
sys.path.append('/home/<USER>/git/realTime/Code/strategy')

# 导入Python因子计算模块
import feature_operator_funcs as fof

def generate_test_data(rows=500, cols=5, seed=42):
    """生成测试数据"""
    np.random.seed(seed)

    # 初始价格
    base_prices = np.random.uniform(100, 200, cols)

    # 创建数据框
    open_data = np.zeros((rows, cols))
    high_data = np.zeros((rows, cols))
    low_data = np.zeros((rows, cols))
    close_data = np.zeros((rows, cols))
    volume_data = np.zeros((rows, cols))
    amount_data = np.zeros((rows, cols))
    vwap_data = np.zeros((rows, cols))

    # 设置初始值
    for j in range(cols):
        open_data[0, j] = base_prices[j]
        h = base_prices[j] * (1.0 + 0.01 * np.random.rand())
        l = base_prices[j] * (1.0 - 0.01 * np.random.rand())
        high_data[0, j] = h
        low_data[0, j] = l
        close_data[0, j] = l + (h - l) * np.random.rand()
        volume_data[0, j] = np.random.uniform(1000, 10000)
        amount_data[0, j] = volume_data[0, j] * (open_data[0, j] + close_data[0, j]) / 2.0
        vwap_data[0, j] = amount_data[0, j] / volume_data[0, j]

    # 生成后续K线
    for i in range(1, rows):
        for j in range(cols):
            prev_close = close_data[i-1, j]
            change_pct = 0.01 * (np.random.rand() * 2 - 1)  # -1% 到 1% 的变化
            open_data[i, j] = prev_close * (1.0 + change_pct)

            h = open_data[i, j] * (1.0 + 0.01 * np.random.rand())
            l = open_data[i, j] * (1.0 - 0.01 * np.random.rand())
            high_data[i, j] = h
            low_data[i, j] = l
            close_data[i, j] = l + (h - l) * np.random.rand()

            volume_data[i, j] = np.random.uniform(1000, 10000)
            amount_data[i, j] = volume_data[i, j] * (open_data[i, j] + close_data[i, j]) / 2.0
            vwap_data[i, j] = amount_data[i, j] / volume_data[i, j]

    # 创建时间索引
    dates = pd.date_range(start='2023-01-01', periods=rows, freq='15min')

    # 创建合约列名
    contracts = [f'contract_{i}' for i in range(cols)]

    # 创建DataFrame
    open_df = pd.DataFrame(open_data, index=dates, columns=contracts)
    high_df = pd.DataFrame(high_data, index=dates, columns=contracts)
    low_df = pd.DataFrame(low_data, index=dates, columns=contracts)
    close_df = pd.DataFrame(close_data, index=dates, columns=contracts)
    volume_df = pd.DataFrame(volume_data, index=dates, columns=contracts)
    amount_df = pd.DataFrame(amount_data, index=dates, columns=contracts)
    vwap_df = pd.DataFrame(vwap_data, index=dates, columns=contracts)

    return open_df, high_df, low_df, close_df, volume_df, amount_df, vwap_df

def save_data_to_csv(data_dict, output_dir):
    """保存数据到CSV文件"""
    os.makedirs(output_dir, exist_ok=True)

    for name, df in data_dict.items():
        df.to_csv(os.path.join(output_dir, f"{name}.csv"))

    print(f"数据已保存到 {output_dir}")

def run_cpp_operator_test(data_dir, output_file, log_file, is_optimized=False):
    """运行C++算子测试程序"""
    # 编译C++测试程序
    subprocess.run(["cd /home/<USER>/git/realTime/test_eigen/build && cmake .. && make"], shell=True, check=True)

    # 确定要运行的程序
    if is_optimized:
        program = "/home/<USER>/git/realTime/test_eigen/build/test_optimized_operators"
    else:
        program = "/home/<USER>/git/realTime/test_eigen/build/test_operators_csv"

    # 运行C++测试程序
    cmd = f"{program} {data_dir} {output_file}"
    
    # 记录开始时间
    start_time = time.time()
    
    # 运行程序
    subprocess.run(cmd, shell=True, check=True)
    
    # 记录结束时间
    end_time = time.time()
    elapsed_time = end_time - start_time
    
    # 记录到日志
    with open(log_file, 'a') as f:
        f.write(f"{'优化版' if is_optimized else '原始版'} C++ 总耗时: {elapsed_time:.6f} 秒\n")
    
    # 加载C++结果
    with open(output_file, 'r') as f:
        cpp_results = json.load(f)
    
    return cpp_results, elapsed_time

def measure_python_operator_time(open_df, high_df, low_df, close_df, volume_df, amount_df, vwap_df, log_file):
    """测量Python算子的执行时间"""
    # 创建数据字典
    data_dict = {
        'open': open_df,
        'high': high_df,
        'low': low_df,
        'close': close_df,
        'volume': volume_df,
        'amount': amount_df,
        'vwap': vwap_df
    }
    
    # 定义要测试的算子
    operators = {
        'ts_corr': lambda: fof.ts_Corr(data_dict['close'], data_dict['open'], 10),
        'ts_cov': lambda: fof.ts_Cov(data_dict['close'], data_dict['open'], 10),
        'ts_median': lambda: fof.ts_Median(data_dict['close'], 10),
        'ts_skewness': lambda: fof.ts_Skewness(data_dict['close'], 10),
        'ts_kurtosis': lambda: fof.ts_Kurtosis(data_dict['close'], 10),
        'ts_scale': lambda: fof.ts_Scale(data_dict['close'], 10),
        'ts_product': lambda: fof.ts_Product(data_dict['close'], 10),
        'ts_transnorm': lambda: fof.ts_TransNorm(data_dict['close'], 10),
        'ts_decay': lambda: fof.ts_Decay(data_dict['close'], 10),
        'ts_decay2': lambda: fof.ts_Decay2(data_dict['close'], 10),
        'ts_partial_corr': lambda: fof.ts_Partial_corr(data_dict['close'], data_dict['open'], data_dict['high'], 10),
        'ts_regression': lambda: fof.ts_Regression(data_dict['close'], data_dict['open'], 10, 'a'),
        'ts_entropy': lambda: fof.ts_Entropy(data_dict['close'], 10),
        'ts_meanchg': lambda: fof.ts_MeanChg(data_dict['close'], 10),
        'ts_quantile': lambda: fof.ts_Quantile(data_dict['close'], 10, 0.5),
        'pn_stand': lambda: fof.pn_Stand(data_dict['close']),
        'pn_winsor': lambda: fof.pn_Winsor(data_dict['close'], 3),
        'pn_cut': lambda: fof.pn_Cut(data_dict['close']),
    }
    
    # 创建分组标签
    group_labels = pd.DataFrame(np.zeros(data_dict['close'].shape, dtype=int),
                               index=data_dict['close'].index,
                               columns=data_dict['close'].columns)
    # 按列索引模3分组
    for j in range(group_labels.shape[1]):
        group_labels.iloc[:, j] = j % 3
    
    # 添加分组相关的算子
    operators.update({
        'pn_grouprank': lambda: fof.pn_GroupRank(data_dict['close'], group_labels),
        'pn_groupnorm': lambda: fof.pn_GroupNorm(data_dict['close'], group_labels),
        'pn_groupneutral': lambda: fof.pn_GroupNeutral(data_dict['close'], group_labels),
        'pn_crossfit': lambda: fof.pn_CrossFit(data_dict['close'], data_dict['open']),
    })
    
    # 测量每个算子的执行时间
    python_times = {}
    total_time = 0
    
    with open(log_file, 'a') as f:
        f.write("\nPython算子执行时间:\n")
        f.write("=" * 50 + "\n")
        f.write(f"{'算子名称':<20} {'执行时间(秒)':<15}\n")
        f.write("-" * 50 + "\n")
    
    for op_name, op_func in operators.items():
        # 预热
        op_func()
        
        # 测量时间
        start_time = time.time()
        result = op_func()
        end_time = time.time()
        
        elapsed_time = end_time - start_time
        python_times[op_name] = elapsed_time
        total_time += elapsed_time
        
        # 记录到日志
        with open(log_file, 'a') as f:
            f.write(f"{op_name:<20} {elapsed_time:.6f}\n")
    
    # 记录总时间
    with open(log_file, 'a') as f:
        f.write("-" * 50 + "\n")
        f.write(f"{'总计':<20} {total_time:.6f}\n\n")
    
    return python_times, total_time

def extract_operator_times_from_json(json_file, log_file, prefix=""):
    """从JSON结果文件中提取每个算子的执行时间"""
    try:
        with open(json_file, 'r') as f:
            results = json.load(f)
        
        # 提取时间信息
        times = {}
        total_time = 0
        
        with open(log_file, 'a') as f:
            f.write(f"\n{prefix}算子执行时间:\n")
            f.write("=" * 50 + "\n")
            f.write(f"{'算子名称':<20} {'执行时间(秒)':<15}\n")
            f.write("-" * 50 + "\n")
        
        for op_name, op_data in results.items():
            if isinstance(op_data, dict) and 'time' in op_data:
                time_value = op_data['time']
                times[op_name] = time_value
                total_time += time_value
                
                # 记录到日志
                with open(log_file, 'a') as f:
                    f.write(f"{op_name:<20} {time_value:.6f}\n")
        
        # 记录总时间
        with open(log_file, 'a') as f:
            f.write("-" * 50 + "\n")
            f.write(f"{'总计':<20} {total_time:.6f}\n\n")
        
        return times, total_time
    except Exception as e:
        print(f"提取时间信息时出错: {e}")
        return {}, 0

def main():
    # 创建日志文件
    log_dir = "/tmp/operator_performance_logs"
    os.makedirs(log_dir, exist_ok=True)
    log_file = os.path.join(log_dir, f"performance_test_{datetime.now().strftime('%Y%m%d_%H%M%S')}.log")
    
    # 写入日志头
    with open(log_file, 'w') as f:
        f.write("算子性能测试日志\n")
        f.write("=" * 50 + "\n")
        f.write(f"测试时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}\n\n")
    
    # 生成测试数据
    print("生成测试数据...")
    open_df, high_df, low_df, close_df, volume_df, amount_df, vwap_df = generate_test_data(rows=1000, cols=10)
    
    # 创建临时目录
    temp_dir = tempfile.mkdtemp()
    data_dir = os.path.join(temp_dir, "data")
    
    # 保存数据到CSV
    data_dict = {
        'open': open_df,
        'high': high_df,
        'low': low_df,
        'close': close_df,
        'volume': volume_df,
        'amount': amount_df,
        'vwap': vwap_df
    }
    save_data_to_csv(data_dict, data_dir)
    
    # 测量Python算子执行时间
    print("测量Python算子执行时间...")
    python_times, python_total_time = measure_python_operator_time(
        open_df, high_df, low_df, close_df, volume_df, amount_df, vwap_df, log_file
    )
    
    # 运行原始C++测试
    print("运行原始C++算子测试...")
    cpp_output_file = os.path.join(temp_dir, "cpp_operator_results.json")
    cpp_results, cpp_total_time = run_cpp_operator_test(data_dir, cpp_output_file, log_file, is_optimized=False)
    
    # 运行优化版C++测试
    print("运行优化版C++算子测试...")
    optimized_output_file = os.path.join(temp_dir, "optimized_operator_results.json")
    optimized_results, optimized_total_time = run_cpp_operator_test(data_dir, optimized_output_file, log_file, is_optimized=True)
    
    # 提取每个算子的执行时间
    cpp_op_times, _ = extract_operator_times_from_json(cpp_output_file, log_file, "原始C++")
    optimized_op_times, _ = extract_operator_times_from_json(optimized_output_file, log_file, "优化C++")
    
    # 计算性能提升
    with open(log_file, 'a') as f:
        f.write("\n性能对比:\n")
        f.write("=" * 50 + "\n")
        f.write(f"Python总耗时: {python_total_time:.6f} 秒\n")
        f.write(f"原始C++总耗时: {cpp_total_time:.6f} 秒\n")
        f.write(f"优化C++总耗时: {optimized_total_time:.6f} 秒\n\n")
        
        python_to_cpp = python_total_time / cpp_total_time if cpp_total_time > 0 else float('inf')
        python_to_optimized = python_total_time / optimized_total_time if optimized_total_time > 0 else float('inf')
        cpp_to_optimized = cpp_total_time / optimized_total_time if optimized_total_time > 0 else float('inf')
        
        f.write(f"Python vs 原始C++: Python慢 {python_to_cpp:.2f}x\n")
        f.write(f"Python vs 优化C++: Python慢 {python_to_optimized:.2f}x\n")
        f.write(f"原始C++ vs 优化C++: 优化版快 {cpp_to_optimized:.2f}x\n\n")
        
        # 对比每个算子的性能
        f.write("\n各算子性能对比:\n")
        f.write("=" * 80 + "\n")
        f.write(f"{'算子名称':<20} {'Python(秒)':<15} {'原始C++(秒)':<15} {'优化C++(秒)':<15} {'Python/原始':<10} {'Python/优化':<10} {'原始/优化':<10}\n")
        f.write("-" * 80 + "\n")
        
        # 获取所有算子名称
        all_ops = set(python_times.keys()) | set(cpp_op_times.keys()) | set(optimized_op_times.keys())
        
        for op in sorted(all_ops):
            py_time = python_times.get(op, float('nan'))
            cpp_time = cpp_op_times.get(op, float('nan'))
            opt_time = optimized_op_times.get(op, float('nan'))
            
            py_cpp_ratio = py_time / cpp_time if not np.isnan(py_time) and not np.isnan(cpp_time) and cpp_time > 0 else float('nan')
            py_opt_ratio = py_time / opt_time if not np.isnan(py_time) and not np.isnan(opt_time) and opt_time > 0 else float('nan')
            cpp_opt_ratio = cpp_time / opt_time if not np.isnan(cpp_time) and not np.isnan(opt_time) and opt_time > 0 else float('nan')
            
            f.write(f"{op:<20} {py_time:<15.6f} {cpp_time:<15.6f} {opt_time:<15.6f} {py_cpp_ratio:<10.2f} {py_opt_ratio:<10.2f} {cpp_opt_ratio:<10.2f}\n")
    
    print(f"性能测试完成，结果已保存到 {log_file}")
    
    # 打印日志文件路径
    print(f"\n日志文件路径: {log_file}")
    
    # 打印日志内容
    with open(log_file, 'r') as f:
        print("\n日志内容:")
        print("=" * 50)
        print(f.read())

if __name__ == "__main__":
    main()
