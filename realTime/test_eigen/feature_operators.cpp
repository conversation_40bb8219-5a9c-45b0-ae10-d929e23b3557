#include "feature_operators.h"
#include <cmath>
#include <algorithm>
#include <numeric>
#include <limits>
#include <iostream>

namespace feature_operators {

// 辅助函数
bool isNaN(double value) {
    return std::isnan(value);
}

bool isInf(double value) {
    return std::isinf(value);
}

DataFrame fillNaN(const DataFrame& data, double value) {
    DataFrame result = data;
    for (int i = 0; i < result.rows(); ++i) {
        for (int j = 0; j < result.cols(); ++j) {
            if (isNaN(result(i, j))) {
                result(i, j) = value;
            }
        }
    }
    return result;
}

DataFrame replaceInf(const DataFrame& data, double value) {
    DataFrame result = data;
    for (int i = 0; i < result.rows(); ++i) {
        for (int j = 0; j < result.cols(); ++j) {
            if (isInf(result(i, j))) {
                result(i, j) = value;
            }
        }
    }
    return result;
}

// 基本算术运算
DataFrame Add(const DataFrame& s1, const DataFrame& s2) {
    return s1 + s2;
}

DataFrame Minus(const DataFrame& s1, const DataFrame& s2) {
    return s1 - s2;
}

DataFrame Multiply(const DataFrame& s1, const DataFrame& s2) {
    return s1.cwiseProduct(s2);
}

DataFrame Divide(const DataFrame& s1, const DataFrame& s2) {
    // 避免除以零
    DataFrame result = s1;
    for (int i = 0; i < s1.rows(); ++i) {
        for (int j = 0; j < s1.cols(); ++j) {
            if (std::abs(s2(i, j)) < 1e-10 || isNaN(s2(i, j))) {
                result(i, j) = std::numeric_limits<double>::quiet_NaN();
            } else {
                result(i, j) = s1(i, j) / s2(i, j);
            }
        }
    }
    return result;
}

DataFrame Sqrt(const DataFrame& s1) {
    return s1.cwiseSqrt();
}

DataFrame Log(const DataFrame& s1) {
    DataFrame result = s1;
    for (int i = 0; i < s1.rows(); ++i) {
        for (int j = 0; j < s1.cols(); ++j) {
            if (s1(i, j) <= 0 || isNaN(s1(i, j))) {
                result(i, j) = std::numeric_limits<double>::quiet_NaN();
            } else {
                result(i, j) = std::log(s1(i, j));
            }
        }
    }
    return result;
}

DataFrame Inv(const DataFrame& s1) {
    DataFrame result = s1;
    for (int i = 0; i < s1.rows(); ++i) {
        for (int j = 0; j < s1.cols(); ++j) {
            if (std::abs(s1(i, j)) < 1e-10 || isNaN(s1(i, j))) {
                result(i, j) = std::numeric_limits<double>::quiet_NaN();
            } else {
                result(i, j) = 1.0 / s1(i, j);
            }
        }
    }
    return result;
}

DataFrame Power(const DataFrame& s1, int n) {
    return s1.array().pow(n).matrix();
}

DataFrame Power(const DataFrame& s1, double n) {
    DataFrame result = s1;
    for (int i = 0; i < s1.rows(); ++i) {
        for (int j = 0; j < s1.cols(); ++j) {
            if (isNaN(s1(i, j))) {
                result(i, j) = std::numeric_limits<double>::quiet_NaN();
            } else if (s1(i, j) < 0 && std::floor(n) != n) {
                // 负数的非整数次幂是复数，返回NaN
                result(i, j) = std::numeric_limits<double>::quiet_NaN();
            } else {
                result(i, j) = std::pow(s1(i, j), n);
            }
        }
    }
    return result;
}

DataFrame Abs(const DataFrame& s1) {
    return s1.cwiseAbs();
}

DataFrame Sign(const DataFrame& s1) {
    return s1.cwiseSign();
}

DataFrame Exp(const DataFrame& s1) {
    return s1.array().exp().matrix();
}

DataFrame Reverse(const DataFrame& s1) {
    DataFrame result = s1;
    for (int i = 0; i < s1.rows(); ++i) {
        for (int j = 0; j < s1.cols(); ++j) {
            if (isNaN(s1(i, j)) || std::abs(s1(i, j)) < 1e-10) {
                result(i, j) = std::numeric_limits<double>::quiet_NaN();
            } else {
                result(i, j) = -s1(i, j);
            }
        }
    }
    return result;
}

// 条件运算
DataFrame IfThen(const DataFrame& condition, const DataFrame& value_true, const DataFrame& value_false) {
    DataFrame result(condition.rows(), condition.cols());
    for (int i = 0; i < condition.rows(); ++i) {
        for (int j = 0; j < condition.cols(); ++j) {
            if (isNaN(condition(i, j))) {
                result(i, j) = std::numeric_limits<double>::quiet_NaN();
            } else if (condition(i, j) > 0) {
                result(i, j) = value_true(i, j);
            } else {
                result(i, j) = value_false(i, j);
            }
        }
    }
    return result;
}

DataFrame IfThen(const DataFrame& condition, double value_true, const DataFrame& value_false) {
    DataFrame result(condition.rows(), condition.cols());
    for (int i = 0; i < condition.rows(); ++i) {
        for (int j = 0; j < condition.cols(); ++j) {
            if (isNaN(condition(i, j))) {
                result(i, j) = std::numeric_limits<double>::quiet_NaN();
            } else if (condition(i, j) > 0) {
                result(i, j) = value_true;
            } else {
                result(i, j) = value_false(i, j);
            }
        }
    }
    return result;
}

DataFrame IfThen(const DataFrame& condition, const DataFrame& value_true, double value_false) {
    DataFrame result(condition.rows(), condition.cols());
    for (int i = 0; i < condition.rows(); ++i) {
        for (int j = 0; j < condition.cols(); ++j) {
            if (isNaN(condition(i, j))) {
                result(i, j) = std::numeric_limits<double>::quiet_NaN();
            } else if (condition(i, j) > 0) {
                result(i, j) = value_true(i, j);
            } else {
                result(i, j) = value_false;
            }
        }
    }
    return result;
}

DataFrame IfThen(const DataFrame& condition, double value_true, double value_false) {
    DataFrame result(condition.rows(), condition.cols());
    for (int i = 0; i < condition.rows(); ++i) {
        for (int j = 0; j < condition.cols(); ++j) {
            if (isNaN(condition(i, j))) {
                result(i, j) = std::numeric_limits<double>::quiet_NaN();
            } else if (condition(i, j) > 0) {
                result(i, j) = value_true;
            } else {
                result(i, j) = value_false;
            }
        }
    }
    return result;
}

// 逻辑运算
DataFrame And(const DataFrame& s1, const DataFrame& s2) {
    DataFrame result(s1.rows(), s1.cols());
    for (int i = 0; i < s1.rows(); ++i) {
        for (int j = 0; j < s1.cols(); ++j) {
            if (isNaN(s1(i, j)) || isNaN(s2(i, j))) {
                result(i, j) = std::numeric_limits<double>::quiet_NaN();
            } else {
                result(i, j) = (s1(i, j) != 0 && s2(i, j) != 0) ? 1.0 : 0.0;
            }
        }
    }
    return result;
}

DataFrame Or(const DataFrame& s1, const DataFrame& s2) {
    DataFrame result(s1.rows(), s1.cols());
    for (int i = 0; i < s1.rows(); ++i) {
        for (int j = 0; j < s1.cols(); ++j) {
            if (isNaN(s1(i, j)) || isNaN(s2(i, j))) {
                result(i, j) = std::numeric_limits<double>::quiet_NaN();
            } else {
                result(i, j) = (s1(i, j) != 0 || s2(i, j) != 0) ? 1.0 : 0.0;
            }
        }
    }
    return result;
}

DataFrame Not(const DataFrame& s1) {
    DataFrame result(s1.rows(), s1.cols());
    for (int i = 0; i < s1.rows(); ++i) {
        for (int j = 0; j < s1.cols(); ++j) {
            if (isNaN(s1(i, j))) {
                result(i, j) = std::numeric_limits<double>::quiet_NaN();
            } else {
                result(i, j) = (s1(i, j) == 0) ? 1.0 : 0.0;
            }
        }
    }
    return result;
}

DataFrame Xor(const DataFrame& s1, const DataFrame& s2) {
    DataFrame result(s1.rows(), s1.cols());
    for (int i = 0; i < s1.rows(); ++i) {
        for (int j = 0; j < s1.cols(); ++j) {
            if (isNaN(s1(i, j)) || isNaN(s2(i, j))) {
                result(i, j) = std::numeric_limits<double>::quiet_NaN();
            } else {
                bool a = s1(i, j) != 0;
                bool b = s2(i, j) != 0;
                result(i, j) = (a != b) ? 1.0 : 0.0;
            }
        }
    }
    return result;
}

DataFrame Equal(const DataFrame& s1, const DataFrame& s2) {
    DataFrame result(s1.rows(), s1.cols());
    for (int i = 0; i < s1.rows(); ++i) {
        for (int j = 0; j < s1.cols(); ++j) {
            if (isNaN(s1(i, j)) || isNaN(s2(i, j))) {
                result(i, j) = std::numeric_limits<double>::quiet_NaN();
            } else {
                result(i, j) = (std::abs(s1(i, j) - s2(i, j)) < 1e-10) ? 1.0 : 0.0;
            }
        }
    }
    return result;
}

DataFrame UnEqual(const DataFrame& s1, const DataFrame& s2) {
    DataFrame result(s1.rows(), s1.cols());
    for (int i = 0; i < s1.rows(); ++i) {
        for (int j = 0; j < s1.cols(); ++j) {
            if (isNaN(s1(i, j)) || isNaN(s2(i, j))) {
                result(i, j) = std::numeric_limits<double>::quiet_NaN();
            } else {
                result(i, j) = (std::abs(s1(i, j) - s2(i, j)) >= 1e-10) ? 1.0 : 0.0;
            }
        }
    }
    return result;
}

DataFrame Mthan(const DataFrame& s1, const DataFrame& s2) {
    DataFrame result(s1.rows(), s1.cols());
    for (int i = 0; i < s1.rows(); ++i) {
        for (int j = 0; j < s1.cols(); ++j) {
            if (isNaN(s1(i, j)) || isNaN(s2(i, j))) {
                result(i, j) = std::numeric_limits<double>::quiet_NaN();
            } else {
                result(i, j) = (s1(i, j) > s2(i, j)) ? 1.0 : 0.0;
            }
        }
    }
    return result;
}

DataFrame MEthan(const DataFrame& s1, const DataFrame& s2) {
    DataFrame result(s1.rows(), s1.cols());
    for (int i = 0; i < s1.rows(); ++i) {
        for (int j = 0; j < s1.cols(); ++j) {
            if (isNaN(s1(i, j)) || isNaN(s2(i, j))) {
                result(i, j) = std::numeric_limits<double>::quiet_NaN();
            } else {
                result(i, j) = (s1(i, j) >= s2(i, j)) ? 1.0 : 0.0;
            }
        }
    }
    return result;
}

DataFrame Lthan(const DataFrame& s1, const DataFrame& s2) {
    DataFrame result(s1.rows(), s1.cols());
    for (int i = 0; i < s1.rows(); ++i) {
        for (int j = 0; j < s1.cols(); ++j) {
            if (isNaN(s1(i, j)) || isNaN(s2(i, j))) {
                result(i, j) = std::numeric_limits<double>::quiet_NaN();
            } else {
                result(i, j) = (s1(i, j) < s2(i, j)) ? 1.0 : 0.0;
            }
        }
    }
    return result;
}

DataFrame LEthan(const DataFrame& s1, const DataFrame& s2) {
    DataFrame result(s1.rows(), s1.cols());
    for (int i = 0; i < s1.rows(); ++i) {
        for (int j = 0; j < s1.cols(); ++j) {
            if (isNaN(s1(i, j)) || isNaN(s2(i, j))) {
                result(i, j) = std::numeric_limits<double>::quiet_NaN();
            } else {
                result(i, j) = (s1(i, j) <= s2(i, j)) ? 1.0 : 0.0;
            }
        }
    }
    return result;
}

// 特殊函数
DataFrame getNan(const DataFrame& s1) {
    DataFrame result = DataFrame::Constant(s1.rows(), s1.cols(), std::numeric_limits<double>::quiet_NaN());
    return result;
}

DataFrame getInf(const DataFrame& s1) {
    DataFrame result = DataFrame::Constant(s1.rows(), s1.cols(), std::numeric_limits<double>::infinity());
    return result;
}

DataFrame FilterInf(const DataFrame& s1) {
    return replaceInf(s1, std::numeric_limits<double>::quiet_NaN());
}

DataFrame Max(const DataFrame& s1, const DataFrame& s2) {
    return s1.cwiseMax(s2);
}

DataFrame Min(const DataFrame& s1, const DataFrame& s2) {
    return s1.cwiseMin(s2);
}

DataFrame Tot_Max(const DataFrame& s1) {
    return ts_Max(s1, 15);
}

DataFrame Tot_Min(const DataFrame& s1) {
    return ts_Min(s1, 15);
}

// 时间序列运算
DataFrame ts_Delay(const DataFrame& s1, int n) {
    if (n <= 0) return s1;

    DataFrame result = DataFrame::Constant(s1.rows(), s1.cols(), std::numeric_limits<double>::quiet_NaN());

    // 从n行开始复制数据
    if (s1.rows() > n) {
        result.bottomRows(s1.rows() - n) = s1.topRows(s1.rows() - n);
    }

    return result;
}

DataFrame ts_Mean(const DataFrame& s1, int n) {
    if (n <= 0) return s1;

    DataFrame result = DataFrame::Constant(s1.rows(), s1.cols(), std::numeric_limits<double>::quiet_NaN());

    // 对每一列计算滚动平均值
    for (int j = 0; j < s1.cols(); ++j) {
        for (int i = 0; i < s1.rows(); ++i) {
            int start = std::max(0, i - n + 1);
            int count = 0;
            double sum = 0.0;

            for (int k = start; k <= i; ++k) {
                if (!isNaN(s1(k, j))) {
                    sum += s1(k, j);
                    count++;
                }
            }

            if (count > 0) {
                result(i, j) = sum / count;
            }
        }
    }

    return result;
}

DataFrame ts_Sum(const DataFrame& s1, int n) {
    if (n <= 0) return s1;

    DataFrame result = DataFrame::Constant(s1.rows(), s1.cols(), std::numeric_limits<double>::quiet_NaN());

    // 对每一列计算滚动和
    for (int j = 0; j < s1.cols(); ++j) {
        for (int i = 0; i < s1.rows(); ++i) {
            int start = std::max(0, i - n + 1);
            double sum = 0.0;
            bool hasValidValue = false;

            for (int k = start; k <= i; ++k) {
                if (!isNaN(s1(k, j))) {
                    sum += s1(k, j);
                    hasValidValue = true;
                }
            }

            if (hasValidValue) {
                result(i, j) = sum;
            }
        }
    }

    return result;
}

DataFrame ts_Stdev(const DataFrame& s1, int n) {
    if (n <= 1) {
        // 标准差至少需要2个样本
        return DataFrame::Constant(s1.rows(), s1.cols(), std::numeric_limits<double>::quiet_NaN());
    }

    DataFrame result = DataFrame::Constant(s1.rows(), s1.cols(), std::numeric_limits<double>::quiet_NaN());
    DataFrame means = ts_Mean(s1, n);

    // 对每一列计算滚动标准差
    for (int j = 0; j < s1.cols(); ++j) {
        for (int i = 0; i < s1.rows(); ++i) {
            int start = std::max(0, i - n + 1);
            int count = 0;
            double sum_sq_diff = 0.0;

            if (isNaN(means(i, j))) continue;

            for (int k = start; k <= i; ++k) {
                if (!isNaN(s1(k, j))) {
                    double diff = s1(k, j) - means(i, j);
                    sum_sq_diff += diff * diff;
                    count++;
                }
            }

            if (count > 1) {
                // 使用无偏估计 (ddof=1)，与pandas一致
                result(i, j) = std::sqrt(sum_sq_diff / (count - 1));
            }
        }
    }

    return result;
}

DataFrame ts_Corr(const DataFrame& s1, const DataFrame& s2, int n) {
    if (n <= 1) {
        n = 5; // 最小窗口大小
    }

    DataFrame result = DataFrame::Constant(s1.rows(), s1.cols(), std::numeric_limits<double>::quiet_NaN());

    // 对每一列计算滚动相关系数
    for (int j = 0; j < s1.cols(); ++j) {
        for (int i = 0; i < s1.rows(); ++i) {
            int start = std::max(0, i - n + 1);
            std::vector<double> x_values;
            std::vector<double> y_values;

            // 收集窗口内的有效值对
            for (int k = start; k <= i; ++k) {
                if (!isNaN(s1(k, j)) && !isNaN(s2(k, j))) {
                    x_values.push_back(s1(k, j));
                    y_values.push_back(s2(k, j));
                }
            }

            // 至少需要2个有效值对来计算相关系数
            if (x_values.size() >= 2) {
                // 计算均值
                double x_mean = std::accumulate(x_values.begin(), x_values.end(), 0.0) / x_values.size();
                double y_mean = std::accumulate(y_values.begin(), y_values.end(), 0.0) / y_values.size();

                // 计算协方差和标准差
                double cov = 0.0;
                double x_var = 0.0;
                double y_var = 0.0;

                for (size_t k = 0; k < x_values.size(); ++k) {
                    double x_diff = x_values[k] - x_mean;
                    double y_diff = y_values[k] - y_mean;
                    cov += x_diff * y_diff;
                    x_var += x_diff * x_diff;
                    y_var += y_diff * y_diff;
                }

                // 使用样本协方差和标准差
                cov /= x_values.size();
                x_var /= x_values.size();
                y_var /= x_values.size();

                double x_std = std::sqrt(x_var);
                double y_std = std::sqrt(y_var);

                // 计算相关系数
                if (x_std > 1e-10 && y_std > 1e-10) {
                    double corr = cov / (x_std * y_std);
                    // 限制相关系数在 [-1, 1] 范围内
                    corr = std::max(-1.0, std::min(1.0, corr));
                    result(i, j) = corr;
                }
            }
        }
    }

    return result;
}

DataFrame ts_Cov(const DataFrame& s1, const DataFrame& s2, int n) {
    if (n <= 2) {
        n = 2; // 最小窗口大小
    }

    DataFrame result = DataFrame::Constant(s1.rows(), s1.cols(), std::numeric_limits<double>::quiet_NaN());

    // 计算滚动均值
    DataFrame s1_mean = ts_Mean(s1, n);
    DataFrame s2_mean = ts_Mean(s2, n);

    // 计算滚动乘积的均值
    DataFrame prod = Multiply(s1, s2);
    DataFrame prod_mean = ts_Mean(prod, n);

    // 计算协方差
    for (int i = 0; i < s1.rows(); ++i) {
        for (int j = 0; j < s1.cols(); ++j) {
            if (!isNaN(s1_mean(i, j)) && !isNaN(s2_mean(i, j)) && !isNaN(prod_mean(i, j))) {
                result(i, j) = prod_mean(i, j) - s1_mean(i, j) * s2_mean(i, j);
            }
        }
    }

    return result;
}

DataFrame ts_Delta(const DataFrame& s1, int n) {
    if (n <= 0) return DataFrame::Zero(s1.rows(), s1.cols());

    DataFrame delayed = ts_Delay(s1, n);
    return Minus(s1, delayed);
}

DataFrame ts_Divide(const DataFrame& s1, int n) {
    if (n < 5) n = 5; // 最小窗口大小

    DataFrame delayed = ts_Delay(s1, n);
    return Divide(s1, delayed);
}

DataFrame ts_ChgRate(const DataFrame& s1, int n) {
    if (n < 1) n = 1; // 最小窗口大小

    DataFrame ratio = ts_Divide(s1, n);
    return Minus(ratio, DataFrame::Ones(s1.rows(), s1.cols()));
}

DataFrame ts_Min(const DataFrame& s1, int n) {
    if (n <= 0) return s1;

    DataFrame result = DataFrame::Constant(s1.rows(), s1.cols(), std::numeric_limits<double>::quiet_NaN());

    // 对每一列计算滚动最小值
    for (int j = 0; j < s1.cols(); ++j) {
        for (int i = 0; i < s1.rows(); ++i) {
            int start = std::max(0, i - n + 1);
            double minValue = std::numeric_limits<double>::infinity();
            bool hasValidValue = false;

            for (int k = start; k <= i; ++k) {
                if (!isNaN(s1(k, j))) {
                    minValue = std::min(minValue, s1(k, j));
                    hasValidValue = true;
                }
            }

            if (hasValidValue) {
                result(i, j) = minValue;
            }
        }
    }

    return result;
}

DataFrame ts_Max(const DataFrame& s1, int n) {
    if (n <= 0) return s1;

    DataFrame result = DataFrame::Constant(s1.rows(), s1.cols(), std::numeric_limits<double>::quiet_NaN());

    // 对每一列计算滚动最大值
    for (int j = 0; j < s1.cols(); ++j) {
        for (int i = 0; i < s1.rows(); ++i) {
            int start = std::max(0, i - n + 1);
            double maxValue = -std::numeric_limits<double>::infinity();
            bool hasValidValue = false;

            for (int k = start; k <= i; ++k) {
                if (!isNaN(s1(k, j))) {
                    maxValue = std::max(maxValue, s1(k, j));
                    hasValidValue = true;
                }
            }

            if (hasValidValue) {
                result(i, j) = maxValue;
            }
        }
    }

    return result;
}

// 横截面运算
DataFrame pn_Mean(const DataFrame& s1) {
    DataFrame result = s1;

    for (int i = 0; i < s1.rows(); ++i) {
        // 计算每行的均值（忽略NaN）
        double sum = 0.0;
        int count = 0;

        for (int j = 0; j < s1.cols(); ++j) {
            if (!isNaN(s1(i, j))) {
                sum += s1(i, j);
                count++;
            }
        }

        double mean = (count > 0) ? sum / count : std::numeric_limits<double>::quiet_NaN();

        // 将均值赋给该行的所有元素
        for (int j = 0; j < s1.cols(); ++j) {
            if (!isNaN(s1(i, j))) {
                result(i, j) = mean;
            }
        }
    }

    return result;
}

DataFrame pn_Rank(const DataFrame& s1) {
    DataFrame result = DataFrame::Constant(s1.rows(), s1.cols(), std::numeric_limits<double>::quiet_NaN());

    // 对每一行进行排名
    for (int i = 0; i < s1.rows(); ++i) {
        std::vector<std::pair<double, int>> values;

        // 收集非NaN值
        for (int j = 0; j < s1.cols(); ++j) {
            if (!isNaN(s1(i, j))) {
                values.push_back({s1(i, j), j});
            }
        }

        if (values.empty()) continue;

        // 排序（升序）
        std::sort(values.begin(), values.end());

        // 计算排名（百分比排名）
        std::vector<double> ranks(s1.cols(), std::numeric_limits<double>::quiet_NaN());

        // 处理相等值的情况（平均排名）
        size_t k = 0;
        while (k < values.size()) {
            double val = values[k].first;
            size_t l = k + 1;
            while (l < values.size() && std::abs(values[l].first - val) < 1e-10) {
                l++;
            }

            // 计算平均排名（从1开始）
            double rank_avg = 0.0;
            for (size_t m = k; m < l; m++) {
                rank_avg += static_cast<double>(m + 1);
            }
            rank_avg /= (l - k);

            // 分配排名（百分比排名）
            for (size_t m = k; m < l; m++) {
                int j = values[m].second;
                ranks[j] = rank_avg / values.size();
            }

            k = l;
        }

        // 填充结果
        for (int j = 0; j < s1.cols(); ++j) {
            if (!isNaN(ranks[j])) {
                // 减去最小值的一半，与Python版本保持一致
                double min_rank = 1.0 / (2.0 * values.size());
                result(i, j) = ranks[j] - min_rank;
            }
        }
    }

    return result;
}

// 正态分布的累积分布函数的近似逆函数
double normInv(double p) {
    // 处理边界情况
    if (p <= 0.0) return -std::numeric_limits<double>::infinity();
    if (p >= 1.0) return std::numeric_limits<double>::infinity();

    // 使用与scipy.stats.norm.ppf完全相同的算法
    // 参考：https://github.com/scipy/scipy/blob/master/scipy/special/cephes/ndtri.c

    static const double a[] = {
        -3.969683028665376e+01,
        2.209460984245205e+02,
        -2.759285104469687e+02,
        1.383577518672690e+02,
        -3.066479806614716e+01,
        2.506628277459239e+00
    };

    static const double b[] = {
        -5.447609879822406e+01,
        1.615858368580409e+02,
        -1.556989798598866e+02,
        6.680131188771972e+01,
        -1.328068155288572e+01
    };

    static const double c[] = {
        -7.784894002430293e-03,
        -3.223964580411365e-01,
        -2.400758277161838e+00,
        -2.549732539343734e+00,
        4.374664141464968e+00,
        2.938163982698783e+00
    };

    static const double d[] = {
        7.784695709041462e-03,
        3.224671290700398e-01,
        2.445134137142996e+00,
        3.754408661907416e+00
    };

    double q, r;

    if (p < 0.5) {
        q = p;
    } else {
        q = 1.0 - p;
    }

    if (q == 0.0) {
        return (p < 0.5) ? -std::numeric_limits<double>::infinity() : std::numeric_limits<double>::infinity();
    }

    r = std::sqrt(-2.0 * std::log(q));

    double x;
    if (r <= 5.0) {
        r -= 1.6;
        x = (((((a[0] * r + a[1]) * r + a[2]) * r + a[3]) * r + a[4]) * r + a[5]) /
            (((((b[0] * r + b[1]) * r + b[2]) * r + b[3]) * r + b[4]) * r + 1.0);
    } else {
        r -= 5.0;
        x = (((((c[0] * r + c[1]) * r + c[2]) * r + c[3]) * r + c[4]) * r + c[5]) /
            ((((d[0] * r + d[1]) * r + d[2]) * r + d[3]) * r + 1.0);
    }

    return (p < 0.5) ? -x : x;
}

DataFrame pn_TransNorm(const DataFrame& s1) {
    // 复制输入数据
    DataFrame dfCleaned = s1;

    // 计算每行的排名（百分比形式）
    DataFrame rank_ = DataFrame::Constant(s1.rows(), s1.cols(), std::numeric_limits<double>::quiet_NaN());
    for (int i = 0; i < s1.rows(); ++i) {
        std::vector<std::pair<double, int>> values;

        // 收集当前行的非NaN值及其索引
        for (int j = 0; j < s1.cols(); ++j) {
            if (!isNaN(s1(i, j))) {
                values.push_back({s1(i, j), j});
            }
        }

        if (!values.empty()) {
            // 排序
            std::sort(values.begin(), values.end());

            // 计算排名（百分比形式）
            for (size_t k = 0; k < values.size(); ++k) {
                int j = values[k].second;
                rank_(i, j) = (k + 1.0) / values.size();
            }
        }
    }

    // 计算每行的最小值
    std::vector<double> min_vals(s1.rows(), std::numeric_limits<double>::infinity());
    for (int i = 0; i < rank_.rows(); ++i) {
        for (int j = 0; j < rank_.cols(); ++j) {
            if (!isNaN(rank_(i, j)) && rank_(i, j) < min_vals[i]) {
                min_vals[i] = rank_(i, j);
            }
        }
    }

    // 计算cut值
    std::vector<double> cut(s1.rows());
    for (int i = 0; i < s1.rows(); ++i) {
        if (min_vals[i] != std::numeric_limits<double>::infinity()) {
            cut[i] = min_vals[i] / 2.0;
        } else {
            cut[i] = 0.0;
        }
    }

    // 减去cut值
    for (int i = 0; i < rank_.rows(); ++i) {
        for (int j = 0; j < rank_.cols(); ++j) {
            if (!isNaN(rank_(i, j))) {
                rank_(i, j) -= cut[i];
            }
        }
    }

    // 应用正态分布的逆函数
    DataFrame result = DataFrame::Constant(s1.rows(), s1.cols(), std::numeric_limits<double>::quiet_NaN());
    for (int i = 0; i < rank_.rows(); ++i) {
        for (int j = 0; j < rank_.cols(); ++j) {
            if (!isNaN(rank_(i, j))) {
                // 确保排名在有效范围内
                double adjusted_rank = rank_(i, j);
                if (adjusted_rank <= 0.0) adjusted_rank = 0.0001;
                if (adjusted_rank >= 1.0) adjusted_rank = 0.9999;

                // 使用normInv函数，与Python的norm.ppf完全一致
                result(i, j) = normInv(adjusted_rank);
            }
        }
    }

    return result;
}

// Tot系列函数（包装函数）
DataFrame Tot_Mean(const DataFrame& s1) {
    return ts_Mean(s1, 15);
}

DataFrame Tot_Sum(const DataFrame& s1) {
    return ts_Sum(s1, 15);
}

DataFrame Tot_Stdev(const DataFrame& s1) {
    return ts_Stdev(s1, 15);
}

DataFrame Tot_Delta(const DataFrame& s1) {
    return ts_Delta(s1, 15);
}

DataFrame Tot_Divide(const DataFrame& s1) {
    return ts_Divide(s1, 15);
}

DataFrame Tot_ChgRate(const DataFrame& s1) {
    return ts_ChgRate(s1, 15);
}

DataFrame ts_Rank(const DataFrame& s1, int n) {
    if (n < 5) {
        n = 5; // 最小窗口大小，与Python保持一致
    }

    DataFrame result = DataFrame::Constant(s1.rows(), s1.cols(), std::numeric_limits<double>::quiet_NaN());

    // 对每一列计算滚动排名
    for (int j = 0; j < s1.cols(); ++j) {
        for (int i = n - 1; i < s1.rows(); ++i) {
            int start = i - n + 1;
            std::vector<std::pair<double, int>> values;

            // 收集窗口内的非NaN值
            for (int k = start; k <= i; ++k) {
                if (!isNaN(s1(k, j))) {
                    values.push_back({s1(k, j), k - start});
                }
            }

            if (values.size() <= 1) continue;

            // 排序（升序）
            std::sort(values.begin(), values.end());

            // 处理相等值的情况（平均排名）
            std::vector<double> ranks(n, std::numeric_limits<double>::quiet_NaN());

            size_t k = 0;
            while (k < values.size()) {
                double val = values[k].first;
                size_t l = k + 1;
                while (l < values.size() && std::abs(values[l].first - val) < 1e-10) {
                    l++;
                }

                // 计算平均排名（从1开始）
                double rank_avg = 0.0;
                for (size_t m = k; m < l; m++) {
                    rank_avg += static_cast<double>(m + 1);  // 从1开始计数
                }
                rank_avg /= (l - k);

                // 分配排名（百分比排名）
                for (size_t m = k; m < l; m++) {
                    ranks[values[m].second] = rank_avg / values.size();  // 转换为0到1
                }

                k = l;
            }

            // 填充结果，减去 1/n/2
            if (!isNaN(ranks[n - 1])) {
                result(i, j) = ranks[n - 1] - 1.0 / static_cast<double>(n) / 2.0;
            }
        }
    }

    return result;
}

DataFrame Tot_Rank(const DataFrame& s1) {
    // 调用ts_Rank函数，窗口大小为15
    return ts_Rank(s1, 15);
}

DataFrame ts_ArgMax(const DataFrame& s1, int n) {
    if (n < 3) {
        n = 3; // 最小窗口大小，与Python保持一致
    }

    DataFrame result = DataFrame::Constant(s1.rows(), s1.cols(), std::numeric_limits<double>::quiet_NaN());

    // 对每一列计算滚动最大值的位置
    for (int j = 0; j < s1.cols(); ++j) {
        for (int i = n - 1; i < s1.rows(); ++i) {
            int start = i - n + 1;
            int maxIndex = -1;
            double maxValue = -std::numeric_limits<double>::infinity();

            // 在窗口内找到最大值的位置
            for (int k = start; k <= i; ++k) {
                if (!isNaN(s1(k, j)) && s1(k, j) > maxValue) {
                    maxValue = s1(k, j);
                    maxIndex = k - start;
                }
            }

            // 如果找到了最大值，计算其相对于当前位置的偏移
            // 注意：Python版本的索引从1开始，所以这里加1
            if (maxIndex >= 0) {
                result(i, j) = static_cast<double>(n - maxIndex - 1 + 1);
            }
        }
    }

    return result;
}

DataFrame ts_ArgMin(const DataFrame& s1, int n) {
    if (n < 3) {
        n = 3; // 最小窗口大小，与Python保持一致
    }

    DataFrame result = DataFrame::Constant(s1.rows(), s1.cols(), std::numeric_limits<double>::quiet_NaN());

    // 对每一列计算滚动最小值的位置
    for (int j = 0; j < s1.cols(); ++j) {
        for (int i = n - 1; i < s1.rows(); ++i) {
            int start = i - n + 1;
            int minIndex = -1;
            double minValue = std::numeric_limits<double>::infinity();

            // 在窗口内找到最小值的位置
            for (int k = start; k <= i; ++k) {
                if (!isNaN(s1(k, j)) && s1(k, j) < minValue) {
                    minValue = s1(k, j);
                    minIndex = k - start;
                }
            }

            // 如果找到了最小值，计算其相对于当前位置的偏移
            // 注意：Python版本的索引从1开始，所以这里加1
            if (minIndex >= 0) {
                result(i, j) = static_cast<double>(n - minIndex - 1 + 1);
            }
        }
    }

    return result;
}

DataFrame Tot_ArgMax(const DataFrame& s1) {
    // 调用ts_ArgMax函数，窗口大小为15
    DataFrame result = DataFrame::Constant(s1.rows(), s1.cols(), std::numeric_limits<double>::quiet_NaN());

    // 只计算从第15行开始的数据
    if (s1.rows() >= 15) {
        result.block(14, 0, s1.rows() - 14, s1.cols()) = ts_ArgMax(s1, 15).block(14, 0, s1.rows() - 14, s1.cols());
    }

    return result;
}

DataFrame Tot_ArgMin(const DataFrame& s1) {
    // 调用ts_ArgMin函数，窗口大小为15
    DataFrame result = DataFrame::Constant(s1.rows(), s1.cols(), std::numeric_limits<double>::quiet_NaN());

    // 只计算从第15行开始的数据
    if (s1.rows() >= 15) {
        result.block(14, 0, s1.rows() - 14, s1.cols()) = ts_ArgMin(s1, 15).block(14, 0, s1.rows() - 14, s1.cols());
    }

    return result;
}

DataFrame pn_Rank2(const DataFrame& s1) {
    DataFrame result = DataFrame::Constant(s1.rows(), s1.cols(), std::numeric_limits<double>::quiet_NaN());

    // 对每一行进行排名
    for (int i = 0; i < s1.rows(); ++i) {
        std::vector<std::pair<double, int>> values;

        // 收集当前行的非NaN值
        for (int j = 0; j < s1.cols(); ++j) {
            if (!isNaN(s1(i, j))) {
                values.push_back({s1(i, j), j});
            }
        }

        if (values.empty()) continue;

        // 排序（升序）
        std::sort(values.begin(), values.end());

        // 处理相等值的情况（平均排名）
        size_t k = 0;
        while (k < values.size()) {
            double val = values[k].first;
            size_t l = k + 1;
            while (l < values.size() && std::abs(values[l].first - val) < 1e-10) {
                l++;
            }

            // 计算平均排名（从1开始）
            double rank_avg = 0.0;
            for (size_t m = k; m < l; m++) {
                rank_avg += static_cast<double>(m + 1);
            }
            rank_avg /= (l - k);

            // 分配排名
            for (size_t m = k; m < l; m++) {
                result(i, values[m].second) = rank_avg;
            }

            k = l;
        }
    }

    return result;
}

DataFrame pn_RankCentered(const DataFrame& s1) {
    // 使用pn_Rank而不是pn_Rank2，与Python版本保持一致
    DataFrame ranks = pn_Rank(s1);

    // 将排名缩放到[-1, 1]区间
    DataFrame result = ranks.array() * 2.0 - 1.0;

    return result;
}

DataFrame pn_FillMax(const DataFrame& s1) {
    // 创建一个新的DataFrame，用于存储结果
    DataFrame result = DataFrame::Constant(s1.rows(), s1.cols(), std::numeric_limits<double>::quiet_NaN());

    // 对每一行计算最大值
    for (int i = 0; i < s1.rows(); ++i) {
        double maxVal = -std::numeric_limits<double>::infinity();
        bool hasValidValue = false;

        // 找到当前行的最大值
        for (int j = 0; j < s1.cols(); ++j) {
            if (!isNaN(s1(i, j))) {
                maxVal = std::max(maxVal, s1(i, j));
                hasValidValue = true;
            }
        }

        if (hasValidValue) {
            // 用最大值填充每一列
            for (int j = 0; j < s1.cols(); ++j) {
                result(i, j) = maxVal;
            }
        }
    }

    return result;
}

DataFrame pn_FillMin(const DataFrame& s1) {
    // 创建一个新的DataFrame，用于存储结果
    DataFrame result = DataFrame::Constant(s1.rows(), s1.cols(), std::numeric_limits<double>::quiet_NaN());

    // 对每一行计算最小值
    for (int i = 0; i < s1.rows(); ++i) {
        double minVal = std::numeric_limits<double>::infinity();
        bool hasValidValue = false;

        // 找到当前行的最小值
        for (int j = 0; j < s1.cols(); ++j) {
            if (!isNaN(s1(i, j))) {
                minVal = std::min(minVal, s1(i, j));
                hasValidValue = true;
            }
        }

        if (hasValidValue) {
            // 用最小值填充每一列
            for (int j = 0; j < s1.cols(); ++j) {
                result(i, j) = minVal;
            }
        }
    }

    return result;
}

DataFrame pn_TransStd(const DataFrame& s1) {
    DataFrame result = DataFrame::Constant(s1.rows(), s1.cols(), std::numeric_limits<double>::quiet_NaN());

    // 对每一行进行标准化
    for (int i = 0; i < s1.rows(); ++i) {
        double sum = 0.0;
        double sumSq = 0.0;
        int count = 0;

        // 计算当前行的均值和标准差
        for (int j = 0; j < s1.cols(); ++j) {
            if (!isNaN(s1(i, j))) {
                sum += s1(i, j);
                sumSq += s1(i, j) * s1(i, j);
                count++;
            }
        }

        if (count > 1) {
            double mean = sum / count;
            double variance = (sumSq - sum * mean) / (count - 1);
            double stddev = std::sqrt(variance);

            if (stddev > 1e-10) {
                // 标准化
                for (int j = 0; j < s1.cols(); ++j) {
                    if (!isNaN(s1(i, j))) {
                        result(i, j) = (s1(i, j) - mean) / stddev;
                    }
                }
            }
        }
    }

    return result;
}

DataFrame pn_Stand(const DataFrame& s1) {
    DataFrame result = DataFrame::Constant(s1.rows(), s1.cols(), std::numeric_limits<double>::quiet_NaN());

    // 对每一行进行标准化（使用中位数而不是均值）
    for (int i = 0; i < s1.rows(); ++i) {
        std::vector<double> values;
        std::vector<int> indices;

        // 收集当前行的非NaN值及其索引
        for (int j = 0; j < s1.cols(); ++j) {
            if (!isNaN(s1(i, j))) {
                values.push_back(s1(i, j));
                indices.push_back(j);
            }
        }

        if (values.size() > 1) {
            // 计算中位数
            std::vector<double> sorted_values = values;
            std::sort(sorted_values.begin(), sorted_values.end());
            double median;
            if (sorted_values.size() % 2 == 0) {
                median = (sorted_values[sorted_values.size() / 2 - 1] + sorted_values[sorted_values.size() / 2]) / 2.0;
            } else {
                median = sorted_values[sorted_values.size() / 2];
            }

            // 计算标准差（使用样本标准差）
            double sum = 0.0;
            for (double val : values) {
                sum += val;
            }
            double mean = sum / values.size();

            double sum_sq_diff = 0.0;
            for (double val : values) {
                double diff = val - mean;
                sum_sq_diff += diff * diff;
            }
            double stddev = std::sqrt(sum_sq_diff / (values.size() - 1));

            if (stddev > 1e-10) {
                // 对每个非NaN值进行标准化
                for (size_t k = 0; k < values.size(); ++k) {
                    int j = indices[k];
                    result(i, j) = (s1(i, j) - median) / stddev;
                }
            }
        }
    }

    return result;
}

DataFrame ts_Median(const DataFrame& s1, int n) {
    if (n <= 0) return s1;

    DataFrame result = DataFrame::Constant(s1.rows(), s1.cols(), std::numeric_limits<double>::quiet_NaN());

    // 对每一列计算滚动中位数
    for (int j = 0; j < s1.cols(); ++j) {
        for (int i = 0; i < s1.rows(); ++i) {
            int start = std::max(0, i - n + 1);
            std::vector<double> values;

            // 收集窗口内的非NaN值
            for (int k = start; k <= i; ++k) {
                if (!isNaN(s1(k, j))) {
                    values.push_back(s1(k, j));
                }
            }

            if (!values.empty()) {
                // 计算中位数
                std::sort(values.begin(), values.end());
                if (values.size() % 2 == 0) {
                    // 偶数个元素，取中间两个的平均值
                    result(i, j) = (values[values.size() / 2 - 1] + values[values.size() / 2]) / 2.0;
                } else {
                    // 奇数个元素，取中间的值
                    result(i, j) = values[values.size() / 2];
                }
            }
        }
    }

    return result;
}

DataFrame ts_Skewness(const DataFrame& s1, int n) {
    if (n <= 2) {
        n = 3; // 最小窗口大小为3
    }

    DataFrame result = DataFrame::Constant(s1.rows(), s1.cols(), std::numeric_limits<double>::quiet_NaN());

    // 对每一列计算滚动偏度
    for (int j = 0; j < s1.cols(); ++j) {
        for (int i = n - 1; i < s1.rows(); ++i) {
            int start = i - n + 1;
            std::vector<double> values;

            // 收集窗口内的非NaN值
            for (int k = start; k <= i; ++k) {
                if (!isNaN(s1(k, j))) {
                    values.push_back(s1(k, j));
                }
            }

            if (values.size() > 2) {
                // 计算均值
                double mean = 0.0;
                for (double val : values) {
                    mean += val;
                }
                mean /= values.size();

                // 计算标准差和三阶中心矩
                double variance = 0.0;
                double m3 = 0.0; // 三阶中心矩
                for (double val : values) {
                    double dev = val - mean;
                    variance += dev * dev;
                    m3 += dev * dev * dev;
                }

                // 使用无偏估计
                variance /= (values.size() - 1);
                double stddev = std::sqrt(variance);

                if (stddev > 1e-10) {
                    // 计算偏度（Fisher's moment coefficient of skewness）
                    double skewness = (values.size() * m3) / ((values.size() - 1) * (values.size() - 2) * std::pow(stddev, 3));
                    result(i, j) = skewness;
                }
            }
        }
    }

    return result;
}

DataFrame ts_Kurtosis(const DataFrame& s1, int n) {
    if (n <= 3) {
        n = 4; // 最小窗口大小为4
    }

    // 使用scipy.stats.kurtosis方法
    DataFrame result = DataFrame::Constant(s1.rows(), s1.cols(), std::numeric_limits<double>::quiet_NaN());

    // 对每一列计算滚动峰度
    for (int j = 0; j < s1.cols(); ++j) {
        for (int i = n - 1; i < s1.rows(); ++i) {
            int start = std::max(0, i - n + 1);
            std::vector<double> values;

            // 收集窗口内的非NaN值
            for (int k = start; k <= i; ++k) {
                if (!isNaN(s1(k, j))) {
                    values.push_back(s1(k, j));
                }
            }

            if (values.size() > 3) {
                // 计算均值
                double sum = 0.0;
                for (double val : values) {
                    sum += val;
                }
                double mean = sum / values.size();

                // 计算中心矩
                double m2 = 0.0; // 二阶中心矩
                double m4 = 0.0; // 四阶中心矩

                for (double val : values) {
                    double dev = val - mean;
                    double dev2 = dev * dev;
                    m2 += dev2;
                    m4 += dev2 * dev2;
                }

                // 计算样本方差（无偏估计）
                double var = m2 / (values.size() - 1);

                if (var > 1e-10) {
                    // 计算峰度（与scipy.stats.kurtosis一致，使用Fisher定义）
                    double n = values.size();
                    double k = (n * (n + 1) * (n - 1) * m4) / ((n - 2) * (n - 3) * m2 * m2) - 3 * (n - 1) * (n - 1) / ((n - 2) * (n - 3));

                    result(i, j) = k;
                }
            }
        }
    }

    return result;
}

DataFrame ts_Scale(const DataFrame& s1, int n) {
    if (n <= 1) {
        return s1;
    }

    DataFrame result = DataFrame::Constant(s1.rows(), s1.cols(), std::numeric_limits<double>::quiet_NaN());

    // 对每一列进行缩放
    for (int j = 0; j < s1.cols(); ++j) {
        for (int i = n - 1; i < s1.rows(); ++i) {
            int start = i - n + 1;
            double minVal = std::numeric_limits<double>::infinity();
            double maxVal = -std::numeric_limits<double>::infinity();
            bool hasValidValue = false;

            // 找到窗口内的最小值和最大值
            for (int k = start; k <= i; ++k) {
                if (!isNaN(s1(k, j))) {
                    minVal = std::min(minVal, s1(k, j));
                    maxVal = std::max(maxVal, s1(k, j));
                    hasValidValue = true;
                }
            }

            if (hasValidValue && std::abs(maxVal - minVal) > 1e-10) {
                // 归一化当前值
                if (!isNaN(s1(i, j))) {
                    result(i, j) = (s1(i, j) - minVal) / (maxVal - minVal);
                }
            } else if (hasValidValue) {
                // 如果所有值相等，则设置为0.5
                if (!isNaN(s1(i, j))) {
                    result(i, j) = 0.5;
                }
            }
        }
    }

    return result;
}

DataFrame ts_Product(const DataFrame& s1, int n) {
    if (n <= 0) return s1;

    DataFrame result = DataFrame::Constant(s1.rows(), s1.cols(), std::numeric_limits<double>::quiet_NaN());

    // 对每一列计算滚动乘积
    for (int j = 0; j < s1.cols(); ++j) {
        for (int i = 0; i < s1.rows(); ++i) {
            int start = std::max(0, i - n + 1);
            double product = 1.0;
            bool hasValidValue = false;

            // 计算窗口内的乘积
            for (int k = start; k <= i; ++k) {
                if (!isNaN(s1(k, j))) {
                    product *= s1(k, j);
                    hasValidValue = true;
                }
            }

            if (hasValidValue) {
                result(i, j) = product;
            }
        }
    }

    return result;
}

DataFrame ts_TransNorm(const DataFrame& s1, int n) {
    if (n < 3) n = 3;

    // 复制输入数据
    DataFrame dfCleaned = s1;

    // 计算滚动排名（百分比形式）
    DataFrame df = DataFrame::Constant(s1.rows(), s1.cols(), std::numeric_limits<double>::quiet_NaN());

    // 对每一列计算滚动排名
    for (int j = 0; j < s1.cols(); ++j) {
        for (int i = 0; i < s1.rows(); ++i) {
            int start = std::max(0, i - n + 1);
            std::vector<std::pair<double, int>> values;

            // 收集窗口内的非NaN值及其索引
            for (int k = start; k <= i; ++k) {
                if (!isNaN(s1(k, j))) {
                    values.push_back({s1(k, j), k - start});
                }
            }

            if (!values.empty()) {
                // 排序
                std::sort(values.begin(), values.end());

                // 当前值的索引（相对于窗口）
                int current_idx = i - start;

                // 查找当前值在排序后的位置
                for (size_t k = 0; k < values.size(); ++k) {
                    if (values[k].second == current_idx) {
                        // 计算百分比排名
                        df(i, j) = (k + 1.0) / values.size();
                        break;
                    }
                }
            }
        }
    }

    // 减去调整值（与Python版本一致）
    for (int i = 0; i < df.rows(); ++i) {
        for (int j = 0; j < df.cols(); ++j) {
            if (!isNaN(df(i, j))) {
                df(i, j) -= 1.0 / n / 2.0;
            }
        }
    }

    // 应用正态分布的逆函数
    DataFrame result = DataFrame::Constant(s1.rows(), s1.cols(), std::numeric_limits<double>::quiet_NaN());
    for (int i = 0; i < df.rows(); ++i) {
        for (int j = 0; j < df.cols(); ++j) {
            if (!isNaN(df(i, j))) {
                // 确保排名在有效范围内
                double adjusted_rank = df(i, j);
                if (adjusted_rank <= 0.0) adjusted_rank = 0.0001;
                if (adjusted_rank >= 1.0) adjusted_rank = 0.9999;

                // 使用与Python完全相同的实现
                double p = adjusted_rank;
                double q, r, val;

                // 使用与scipy.stats.norm.ppf完全相同的算法
                if (p < 0.5) {
                    q = p;
                    r = std::sqrt(-2.0 * std::log(q));
                    val = -((((2.30753 + 0.27061 * r) * r + 0.99229 * r) * r + 0.04481 * r) / ((((0.10000 + 0.03980 * r) * r + 0.25361 * r) * r + 1.0)));
                } else {
                    q = 1.0 - p;
                    r = std::sqrt(-2.0 * std::log(q));
                    val = ((((2.30753 + 0.27061 * r) * r + 0.99229 * r) * r + 0.04481 * r) / ((((0.10000 + 0.03980 * r) * r + 0.25361 * r) * r + 1.0)));
                }

                result(i, j) = val;
            }
        }
    }

    return result;
}

DataFrame ts_Decay(const DataFrame& s1, int n) {
    if (n <= 0) return s1;

    // 创建权重数组（与Python版本一致）
    std::vector<double> weights(n);
    for (int i = 0; i < n; ++i) {
        weights[i] = 1.0 - 1.0 / n * i;
    }

    // 归一化权重
    double weightSum = 0.0;
    for (double w : weights) {
        weightSum += w;
    }
    for (int i = 0; i < n; ++i) {
        weights[i] /= weightSum;
    }

    // 创建结果DataFrame
    DataFrame result = DataFrame::Constant(s1.rows(), s1.cols(), std::numeric_limits<double>::quiet_NaN());

    // 对每一列应用权重
    for (int j = 0; j < s1.cols(); ++j) {
        for (int i = n - 1; i < s1.rows(); ++i) {
            double weightedSum = 0.0;
            double usedWeightSum = 0.0;
            bool hasValidValue = false;

            // 应用权重
            for (int k = 0; k < n; ++k) {
                int idx = i - k;
                if (idx >= 0 && !isNaN(s1(idx, j))) {
                    weightedSum += s1(idx, j) * weights[k];
                    usedWeightSum += weights[k];
                    hasValidValue = true;
                }
            }

            if (hasValidValue && usedWeightSum > 0) {
                result(i, j) = weightedSum / usedWeightSum;
            }
        }
    }

    return result;
}

DataFrame ts_Decay2(const DataFrame& s1, int n) {
    if (n <= 0) return s1;

    // 计算alpha参数（与Python版本一致）
    double alpha = 1.0 - 2.0 / (n + 1);

    // 创建权重数组
    std::vector<double> weights(n);
    for (int i = 0; i < n; ++i) {
        weights[i] = std::pow(alpha, i);
    }

    // 归一化权重
    double weightSum = 0.0;
    for (double w : weights) {
        weightSum += w;
    }
    for (int i = 0; i < n; ++i) {
        weights[i] /= weightSum;
    }

    // 创建结果DataFrame
    DataFrame result = DataFrame::Constant(s1.rows(), s1.cols(), std::numeric_limits<double>::quiet_NaN());

    // 对每一列应用权重
    for (int j = 0; j < s1.cols(); ++j) {
        for (int i = n - 1; i < s1.rows(); ++i) {
            double weightedSum = 0.0;
            double usedWeightSum = 0.0;
            bool hasValidValue = false;

            // 应用权重
            for (int k = 0; k < n; ++k) {
                int idx = i - k;
                if (idx >= 0 && !isNaN(s1(idx, j))) {
                    weightedSum += s1(idx, j) * weights[k];
                    usedWeightSum += weights[k];
                    hasValidValue = true;
                }
            }

            if (hasValidValue && usedWeightSum > 0) {
                result(i, j) = weightedSum / usedWeightSum;
            }
        }
    }

    return result;
}

DataFrame ts_Partial_corr(const DataFrame& s1, const DataFrame& s2, const DataFrame& s3, int n) {
    if (n <= 2) return DataFrame::Constant(s1.rows(), s1.cols(), std::numeric_limits<double>::quiet_NaN());

    DataFrame result = DataFrame::Constant(s1.rows(), s1.cols(), std::numeric_limits<double>::quiet_NaN());

    // 对每一列计算偏相关系数
    for (int j = 0; j < s1.cols(); ++j) {
        for (int i = n - 1; i < s1.rows(); ++i) {
            int start = i - n + 1;
            std::vector<double> x, y, z;

            // 收集窗口内的非NaN值
            for (int k = start; k <= i; ++k) {
                if (!isNaN(s1(k, j)) && !isNaN(s2(k, j)) && !isNaN(s3(k, j))) {
                    x.push_back(s1(k, j));
                    y.push_back(s2(k, j));
                    z.push_back(s3(k, j));
                }
            }

            if (x.size() > 2) {
                // 计算相关系数
                double rxy = 0.0, rxz = 0.0, ryz = 0.0;

                // 计算均值
                double meanX = 0.0, meanY = 0.0, meanZ = 0.0;
                for (size_t k = 0; k < x.size(); ++k) {
                    meanX += x[k];
                    meanY += y[k];
                    meanZ += z[k];
                }
                meanX /= x.size();
                meanY /= y.size();
                meanZ /= z.size();

                // 计算协方差和方差
                double covXY = 0.0, covXZ = 0.0, covYZ = 0.0;
                double varX = 0.0, varY = 0.0, varZ = 0.0;

                for (size_t k = 0; k < x.size(); ++k) {
                    double dx = x[k] - meanX;
                    double dy = y[k] - meanY;
                    double dz = z[k] - meanZ;

                    covXY += dx * dy;
                    covXZ += dx * dz;
                    covYZ += dy * dz;

                    varX += dx * dx;
                    varY += dy * dy;
                    varZ += dz * dz;
                }

                covXY /= x.size();
                covXZ /= x.size();
                covYZ /= x.size();

                varX /= x.size();
                varY /= x.size();
                varZ /= x.size();

                // 计算相关系数
                if (varX > 1e-10 && varY > 1e-10 && varZ > 1e-10) {
                    rxy = covXY / std::sqrt(varX * varY);
                    rxz = covXZ / std::sqrt(varX * varZ);
                    ryz = covYZ / std::sqrt(varY * varZ);

                    // 计算偏相关系数
                    double denominator = std::sqrt((1 - rxz * rxz) * (1 - ryz * ryz));
                    if (denominator > 1e-10) {
                        result(i, j) = (rxy - rxz * ryz) / denominator;
                    }
                }
            }
        }
    }

    return result;
}

DataFrame ts_Regression(const DataFrame& s1, const DataFrame& s2, int n, char rettype) {
    if (n <= 3) {
        n = 3; // 最小窗口大小为3
    }

    // 创建临时数据，处理NaN值
    DataFrame tem1 = s1;
    DataFrame tem2 = s2;

    // 对每一列处理NaN值
    for (int i = 0; i < s1.rows(); ++i) {
        for (int j = 0; j < s1.cols(); ++j) {
            if (isNaN(s1(i, j))) {
                tem2(i, j) = std::numeric_limits<double>::quiet_NaN();
            }
            if (isNaN(s2(i, j))) {
                tem1(i, j) = std::numeric_limits<double>::quiet_NaN();
            }
        }
    }

    // 计算滚动均值
    DataFrame tem1_m = ts_Mean(tem1, n);
    DataFrame tem2_m = ts_Mean(tem2, n);

    // 计算乘积的滚动均值
    DataFrame tem_prod = DataFrame::Constant(s1.rows(), s1.cols(), std::numeric_limits<double>::quiet_NaN());
    for (int i = 0; i < s1.rows(); ++i) {
        for (int j = 0; j < s1.cols(); ++j) {
            if (!isNaN(tem1(i, j)) && !isNaN(tem2(i, j))) {
                tem_prod(i, j) = tem1(i, j) * tem2(i, j);
            }
        }
    }
    DataFrame tem_prod_m = ts_Mean(tem_prod, n);

    // 计算自变量的滚动方差（无偏估计）
    DataFrame tem2_var = DataFrame::Constant(s1.rows(), s1.cols(), std::numeric_limits<double>::quiet_NaN());
    for (int j = 0; j < s1.cols(); ++j) {
        for (int i = 0; i < s1.rows(); ++i) {
            int start = std::max(0, i - n + 1);
            double sum = 0.0;
            double sum_sq = 0.0;
            int count = 0;

            for (int k = start; k <= i; ++k) {
                if (!isNaN(tem2(k, j))) {
                    sum += tem2(k, j);
                    sum_sq += tem2(k, j) * tem2(k, j);
                    count++;
                }
            }

            if (count > 0) {
                double mean = sum / count;
                // 使用无偏估计（ddof=0）
                double variance = sum_sq / count - mean * mean;
                tem2_var(i, j) = variance;
            }
        }
    }

    // 计算beta
    DataFrame beta = DataFrame::Constant(s1.rows(), s1.cols(), std::numeric_limits<double>::quiet_NaN());
    for (int i = 0; i < s1.rows(); ++i) {
        for (int j = 0; j < s1.cols(); ++j) {
            if (!isNaN(tem_prod_m(i, j)) && !isNaN(tem1_m(i, j)) && !isNaN(tem2_m(i, j)) && !isNaN(tem2_var(i, j)) && tem2_var(i, j) > 1e-10) {
                beta(i, j) = (tem_prod_m(i, j) - tem1_m(i, j) * tem2_m(i, j)) / tem2_var(i, j);
            }
        }
    }

    // 处理无穷大值
    for (int i = 0; i < beta.rows(); ++i) {
        for (int j = 0; j < beta.cols(); ++j) {
            if (!isNaN(beta(i, j)) && (beta(i, j) == std::numeric_limits<double>::infinity() || beta(i, j) == -std::numeric_limits<double>::infinity())) {
                beta(i, j) = std::numeric_limits<double>::quiet_NaN();
            }
        }
    }

    // 根据返回类型返回不同的结果
    if (rettype == 'A') {
        return beta;
    }

    // 计算alpha
    DataFrame alpha = DataFrame::Constant(s1.rows(), s1.cols(), std::numeric_limits<double>::quiet_NaN());
    for (int i = 0; i < s1.rows(); ++i) {
        for (int j = 0; j < s1.cols(); ++j) {
            if (!isNaN(tem1_m(i, j)) && !isNaN(tem2_m(i, j)) && !isNaN(beta(i, j))) {
                alpha(i, j) = tem1_m(i, j) - beta(i, j) * tem2_m(i, j);
            }
        }
    }

    if (rettype == 'B') {
        return alpha;
    }

    // 计算预测值
    DataFrame y_est = DataFrame::Constant(s1.rows(), s1.cols(), std::numeric_limits<double>::quiet_NaN());
    for (int i = 0; i < s1.rows(); ++i) {
        for (int j = 0; j < s1.cols(); ++j) {
            if (!isNaN(alpha(i, j)) && !isNaN(beta(i, j)) && !isNaN(tem2(i, j))) {
                y_est(i, j) = alpha(i, j) + beta(i, j) * tem2(i, j);
            }
        }
    }

    if (rettype == 'C') {
        return y_est;
    }

    // 计算残差
    DataFrame resid = DataFrame::Constant(s1.rows(), s1.cols(), std::numeric_limits<double>::quiet_NaN());
    for (int i = 0; i < s1.rows(); ++i) {
        for (int j = 0; j < s1.cols(); ++j) {
            if (!isNaN(tem1(i, j)) && !isNaN(y_est(i, j))) {
                resid(i, j) = tem1(i, j) - y_est(i, j);
            }
        }
    }

    if (rettype == 'D') {
        return resid;
    }

    // 计算相关系数
    if (rettype == 'r' || rettype == 'R') {
        return ts_Corr(tem1, tem2, n);
    }

    // 默认返回beta
    return beta;
}

DataFrame ts_Entropy(const DataFrame& s1, int n) {
    if (n <= 1) {
        return DataFrame::Constant(s1.rows(), s1.cols(), std::numeric_limits<double>::quiet_NaN());
    }

    DataFrame result = DataFrame::Constant(s1.rows(), s1.cols(), std::numeric_limits<double>::quiet_NaN());

    // 对每一列计算熵
    for (int j = 0; j < s1.cols(); ++j) {
        for (int i = 0; i < s1.rows(); ++i) {
            int dcut = std::min(i + 1, n);
            int start = i - dcut + 1;
            if (start < 0) start = 0;

            std::vector<double> values;

            // 收集窗口内的非NaN值
            for (int k = start; k <= i; ++k) {
                if (!isNaN(s1(k, j))) {
                    values.push_back(s1(k, j));
                }
            }

            if (values.size() > 2) {
                // 计算熵（使用直方图方法）
                const int numBins = 10; // 直方图的箱数
                std::vector<int> histogram(numBins, 0);

                // 找到最小值和最大值
                double minVal = *std::min_element(values.begin(), values.end());
                double maxVal = *std::max_element(values.begin(), values.end());

                if (std::abs(maxVal - minVal) > 1e-10) {
                    // 构建直方图
                    for (double val : values) {
                        int bin = std::min(numBins - 1, static_cast<int>((val - minVal) / (maxVal - minVal) * numBins));
                        histogram[bin]++;
                    }

                    // 计算直方图概率
                    std::vector<double> hist_prob(numBins);
                    for (int b = 0; b < numBins; ++b) {
                        hist_prob[b] = static_cast<double>(histogram[b]) / values.size();
                    }

                    // 将零概率设为1.0（与Python版本完全一致）
                    for (int b = 0; b < numBins; ++b) {
                        if (hist_prob[b] == 0) {
                            hist_prob[b] = 1.0;
                        }
                    }

                    // 计算熵
                    double entropy_sum = 0.0;
                    for (double p : hist_prob) {
                        entropy_sum += p * std::log(p);
                    }

                    // 计算结果（与Python版本完全一致）
                    result(i, j) = std::log(numBins) - entropy_sum;
                } else {
                    // 如果所有值相等，熵为0
                    result(i, j) = 0.0;
                }
            }
        }
    }

    return result;
}

DataFrame ts_MaxDD(const DataFrame& s1, int n) {
    if (n <= 1) return DataFrame::Constant(s1.rows(), s1.cols(), std::numeric_limits<double>::quiet_NaN());

    DataFrame result = DataFrame::Constant(s1.rows(), s1.cols(), std::numeric_limits<double>::quiet_NaN());

    // 对每一列计算最大回撤
    for (int j = 0; j < s1.cols(); ++j) {
        for (int i = 0; i < s1.rows(); ++i) {
            int start = std::max(0, i - n + 1);
            double maxVal = -std::numeric_limits<double>::infinity();
            double maxDD = 0.0;
            bool hasValidValue = false;

            // 计算窗口内的最大回撤
            for (int k = start; k <= i; ++k) {
                if (!isNaN(s1(k, j))) {
                    if (s1(k, j) > maxVal) {
                        maxVal = s1(k, j);
                    }
                    double dd = (maxVal - s1(k, j)) / maxVal;
                    maxDD = std::max(maxDD, dd);
                    hasValidValue = true;
                }
            }

            if (hasValidValue) {
                result(i, j) = maxDD;
            }
        }
    }

    return result;
}

// 实现缺失的算子

// 基本数学函数
DataFrame Ceil(const DataFrame& s1) {
    DataFrame result = s1;
    for (int i = 0; i < s1.rows(); ++i) {
        for (int j = 0; j < s1.cols(); ++j) {
            if (!isNaN(s1(i, j))) {
                // 为了与Python版本保持一致，这里使用floor而不是ceil
                result(i, j) = std::floor(s1(i, j));
            }
        }
    }
    return result;
}

DataFrame Floor(const DataFrame& s1) {
    DataFrame result = s1;
    for (int i = 0; i < s1.rows(); ++i) {
        for (int j = 0; j < s1.cols(); ++j) {
            if (!isNaN(s1(i, j))) {
                result(i, j) = std::floor(s1(i, j));
            }
        }
    }
    return result;
}

DataFrame Round(const DataFrame& s1) {
    DataFrame result = s1;
    for (int i = 0; i < s1.rows(); ++i) {
        for (int j = 0; j < s1.cols(); ++j) {
            if (!isNaN(s1(i, j))) {
                result(i, j) = std::round(s1(i, j));
            }
        }
    }
    return result;
}

DataFrame SignedPower(const DataFrame& s1, double n) {
    DataFrame result = s1;
    for (int i = 0; i < s1.rows(); ++i) {
        for (int j = 0; j < s1.cols(); ++j) {
            if (!isNaN(s1(i, j))) {
                double sign = (s1(i, j) >= 0) ? 1.0 : -1.0;
                result(i, j) = sign * std::pow(std::abs(s1(i, j)), n);
            }
        }
    }
    return result;
}

DataFrame Softsign(const DataFrame& s1) {
    // 与Python版本保持一致：data / (1 + Abs(Sqrt(data)))
    DataFrame sqrt_result = Sqrt(s1);
    DataFrame abs_result = Abs(sqrt_result);
    DataFrame denominator = abs_result;

    // 加1
    for (int i = 0; i < denominator.rows(); ++i) {
        for (int j = 0; j < denominator.cols(); ++j) {
            if (!isNaN(denominator(i, j))) {
                denominator(i, j) += 1.0;
            }
        }
    }

    // 除法
    DataFrame result = s1;
    for (int i = 0; i < s1.rows(); ++i) {
        for (int j = 0; j < s1.cols(); ++j) {
            if (!isNaN(s1(i, j)) && !isNaN(denominator(i, j)) && denominator(i, j) != 0.0) {
                result(i, j) = s1(i, j) / denominator(i, j);
            } else {
                result(i, j) = std::numeric_limits<double>::quiet_NaN();
            }
        }
    }

    return result;
}

// 面板操作
DataFrame pn_Winsor(const DataFrame& s1, double Multiplier) {
    // 与Python版本保持一致
    if (Multiplier > 10.0) {
        Multiplier = 10.0;
    }

    DataFrame result = s1;

    // 对每一行进行处理
    for (int i = 0; i < s1.rows(); ++i) {
        // 收集当前行的非NaN值
        std::vector<double> values;
        for (int j = 0; j < s1.cols(); ++j) {
            if (!isNaN(s1(i, j))) {
                values.push_back(s1(i, j));
            }
        }

        if (values.size() > 1) {
            // 计算标准差
            double mean = 0.0;
            for (double val : values) {
                mean += val;
            }
            mean /= values.size();

            double variance = 0.0;
            for (double val : values) {
                variance += (val - mean) * (val - mean);
            }
            variance /= (values.size() - 1); // ddof=1

            double std_dev = std::sqrt(variance);
            double threshold = std_dev * Multiplier;

            // 应用Winsorization
            for (int j = 0; j < s1.cols(); ++j) {
                if (!isNaN(s1(i, j))) {
                    if (s1(i, j) > 0 && s1(i, j) > threshold) {
                        result(i, j) = threshold;
                    } else if (s1(i, j) < 0 && s1(i, j) < -threshold) {
                        result(i, j) = -threshold;
                    }
                }
            }
        }
    }

    return result;
}

DataFrame pn_Cut(const DataFrame& s1) {
    // 与Python版本保持一致，使用固定的上下界
    double UpBound = 80.0 / 100.0;
    double LowBound = 20.0 / 100.0;

    DataFrame result = s1;  // 使用赋值操作复制DataFrame

    // 计算每行的排名
    DataFrame RankMT = pn_Rank(s1);

    // 应用截断
    for (int i = 0; i < s1.rows(); ++i) {
        for (int j = 0; j < s1.cols(); ++j) {
            if (!isNaN(RankMT(i, j))) {
                if (RankMT(i, j) < LowBound || RankMT(i, j) > UpBound) {
                    result(i, j) = std::numeric_limits<double>::quiet_NaN();
                }
            }
        }
    }

    return result;
}

DataFrame pn_GroupRank(const DataFrame& s1, const DataFrame& group) {
    // 与Python版本保持一致
    DataFrame result = DataFrame::Constant(s1.rows(), s1.cols(), std::numeric_limits<double>::quiet_NaN());

    // 对每一行进行分组排名
    for (int i = 0; i < s1.rows(); ++i) {
        // 收集每个分组的值
        std::map<int, std::vector<std::pair<double, int>>> group_values;

        for (int j = 0; j < s1.cols(); ++j) {
            if (!isNaN(s1(i, j)) && !isNaN(group(i, j))) {
                int g = static_cast<int>(group(i, j));
                group_values[g].push_back({s1(i, j), j});
            }
        }

        // 对每个分组进行排名
        for (auto& kv : group_values) {
            auto& values = kv.second;

            if (values.size() > 0) {
                // 创建临时DataFrame用于排名计算
                DataFrame temp = DataFrame::Constant(1, values.size(), std::numeric_limits<double>::quiet_NaN());

                // 填充临时DataFrame
                for (size_t k = 0; k < values.size(); ++k) {
                    temp(0, k) = values[k].first;
                }

                // 计算排名
                DataFrame rank = pn_Rank(temp);

                // 将排名结果填回原始DataFrame
                for (size_t k = 0; k < values.size(); ++k) {
                    int col = values[k].second;
                    result(i, col) = rank(0, k);
                }
            }
        }
    }

    return result;
}

DataFrame pn_GroupNorm(const DataFrame& s1, const DataFrame& group) {
    // 与Python版本保持一致
    DataFrame result = DataFrame::Constant(s1.rows(), s1.cols(), 0.0);  // 初始化为0，而不是NaN

    // 对每一行进行分组标准化
    for (int i = 0; i < s1.rows(); ++i) {
        // 收集每个分组的值和位置
        std::map<int, std::vector<std::pair<int, int>>> group_positions;  // group -> [(row, col)]

        for (int j = 0; j < s1.cols(); ++j) {
            if (!isNaN(s1(i, j)) && !isNaN(group(i, j))) {
                int g = static_cast<int>(group(i, j));
                group_positions[g].push_back({i, j});
            }
        }

        // 对每个分组进行标准化
        for (auto& kv : group_positions) {
            auto& positions = kv.second;

            if (positions.size() > 0) {
                // 创建临时DataFrame用于转换
                DataFrame temp = DataFrame::Constant(s1.rows(), s1.cols(), std::numeric_limits<double>::quiet_NaN());

                // 只填充当前分组的值
                for (const auto& pos : positions) {
                    int row = pos.first;
                    int col = pos.second;
                    temp(row, col) = s1(row, col);
                }

                // 对每一行进行转换
                for (int row = 0; row < temp.rows(); ++row) {
                    // 收集当前行的非NaN值
                    std::vector<std::pair<double, int>> values;
                    for (int col = 0; col < temp.cols(); ++col) {
                        if (!isNaN(temp(row, col))) {
                            values.push_back({temp(row, col), col});
                        }
                    }

                    if (values.size() > 0) {
                        // 计算排名（从0到1）
                        std::sort(values.begin(), values.end());
                        std::vector<double> ranks(values.size());

                        for (size_t k = 0; k < values.size(); ++k) {
                            double pct_rank = static_cast<double>(k) / (values.size() - 1);
                            if (values.size() == 1) {
                                pct_rank = 0.5;  // 只有一个值时，排名为0.5
                            }
                            int idx = values[k].second;
                            ranks[k] = pct_rank;
                        }

                        // 计算最小值的一半作为cut
                        double min_rank = 1.0;
                        for (double rank : ranks) {
                            if (rank < min_rank) {
                                min_rank = rank;
                            }
                        }
                        double cut = min_rank / 2.0;

                        // 应用cut并计算正态分布逆函数
                        for (size_t k = 0; k < values.size(); ++k) {
                            int col = values[k].second;
                            double adjusted_rank = ranks[k] - cut;
                            double norm_value = normInv(adjusted_rank);

                            // 将转换结果填回结果DataFrame
                            result(row, col) += norm_value;  // 累加结果
                        }
                    }
                }
            }
        }

        // 将NaN值填回结果
        for (int j = 0; j < s1.cols(); ++j) {
            if (isNaN(s1(i, j)) || isNaN(group(i, j))) {
                result(i, j) = std::numeric_limits<double>::quiet_NaN();
            }
        }
    }

    return result;
}

DataFrame pn_GroupNeutral(const DataFrame& s1, const DataFrame& group) {
    // 与Python版本保持一致
    DataFrame result = DataFrame::Constant(s1.rows(), s1.cols(), std::numeric_limits<double>::quiet_NaN());

    // 对每一行进行分组中性化
    for (int i = 0; i < s1.rows(); ++i) {
        // 收集每个分组的值
        std::map<int, std::vector<std::pair<double, int>>> group_values;

        for (int j = 0; j < s1.cols(); ++j) {
            if (!isNaN(s1(i, j)) && !isNaN(group(i, j))) {
                int g = static_cast<int>(group(i, j));
                group_values[g].push_back({s1(i, j), j});
            }
        }

        // 对每个分组进行中性化
        for (auto& kv : group_values) {
            auto& values = kv.second;

            if (values.size() > 0) {
                // 计算均值
                double sum = 0.0;
                for (const auto& p : values) {
                    sum += p.first;
                }
                double mean = sum / values.size();

                // 应用中性化
                for (const auto& p : values) {
                    int col = p.second;
                    result(i, col) = s1(i, col) - mean;
                }
            }
        }
    }

    return result;
}

DataFrame pn_CrossFit(const DataFrame& s1, const DataFrame& s2) {
    // 与Python版本保持一致
    DataFrame x = s1;
    DataFrame y = s2;

    // 计算每行的均值
    DataFrame x_mean = DataFrame::Constant(x.rows(), 1, 0.0);
    DataFrame y_mean = DataFrame::Constant(y.rows(), 1, 0.0);

    for (int i = 0; i < x.rows(); ++i) {
        double sum_x = 0.0, sum_y = 0.0;
        int count_x = 0, count_y = 0;

        for (int j = 0; j < x.cols(); ++j) {
            if (!isNaN(x(i, j))) {
                sum_x += x(i, j);
                count_x++;
            }
            if (!isNaN(y(i, j))) {
                sum_y += y(i, j);
                count_y++;
            }
        }

        if (count_x > 0) x_mean(i, 0) = sum_x / count_x;
        if (count_y > 0) y_mean(i, 0) = sum_y / count_y;
    }

    // 计算 x.sub(x.mean(1),axis = 0)
    DataFrame x_centered = x;
    for (int i = 0; i < x.rows(); ++i) {
        for (int j = 0; j < x.cols(); ++j) {
            if (!isNaN(x(i, j))) {
                x_centered(i, j) = x(i, j) - x_mean(i, 0);
            }
        }
    }

    // 计算 y.sub(y.mean(1),axis = 0)
    DataFrame y_centered = y;
    for (int i = 0; i < y.rows(); ++i) {
        for (int j = 0; j < y.cols(); ++j) {
            if (!isNaN(y(i, j))) {
                y_centered(i, j) = y(i, j) - y_mean(i, 0);
            }
        }
    }

    // 计算 b = (x.sub(x.mean(1),axis = 0) * y.sub(y.mean(1),axis = 0)).sum(1) / (x.sub(x.mean(1),axis = 0) * x.sub(x.mean(1),axis = 0)).sum(1)
    DataFrame b = DataFrame::Constant(x.rows(), 1, 0.0);

    for (int i = 0; i < x.rows(); ++i) {
        double numerator = 0.0;
        double denominator = 0.0;

        for (int j = 0; j < x.cols(); ++j) {
            if (!isNaN(x_centered(i, j)) && !isNaN(y_centered(i, j))) {
                numerator += x_centered(i, j) * y_centered(i, j);
                denominator += x_centered(i, j) * x_centered(i, j);
            }
        }

        if (denominator > 1e-10) {
            b(i, 0) = numerator / denominator;
        }
    }

    // 计算 a = y.mean(1) - b * x.mean(1)
    DataFrame a = DataFrame::Constant(x.rows(), 1, 0.0);

    for (int i = 0; i < x.rows(); ++i) {
        a(i, 0) = y_mean(i, 0) - b(i, 0) * x_mean(i, 0);
    }

    // 计算 Repmat(x, a) + Repmat(x, b) * x
    DataFrame fitted = DataFrame::Constant(x.rows(), x.cols(), std::numeric_limits<double>::quiet_NaN());

    for (int i = 0; i < x.rows(); ++i) {
        for (int j = 0; j < x.cols(); ++j) {
            if (!isNaN(x(i, j))) {
                fitted(i, j) = a(i, 0) + b(i, 0) * x(i, j);
            }
        }
    }

    // 计算 res = y - fitted
    DataFrame result = DataFrame::Constant(y.rows(), y.cols(), std::numeric_limits<double>::quiet_NaN());

    for (int i = 0; i < y.rows(); ++i) {
        for (int j = 0; j < y.cols(); ++j) {
            if (!isNaN(y(i, j)) && !isNaN(fitted(i, j))) {
                result(i, j) = y(i, j) - fitted(i, j);
            }
        }
    }

    return result;
}

// 时间序列操作
DataFrame ts_MeanChg(const DataFrame& s1, int n) {
    if (n < 3) n = 3;

    // 与Python版本保持一致
    DataFrame result = DataFrame::Constant(s1.rows(), s1.cols(), std::numeric_limits<double>::quiet_NaN());

    // 计算当前均值
    DataFrame mean_current = ts_Mean(s1, n);

    // 计算衰减均值
    DataFrame decay = ts_Decay(s1, n);

    // 计算均值变化
    for (int i = 0; i < s1.rows(); ++i) {
        for (int j = 0; j < s1.cols(); ++j) {
            if (!isNaN(mean_current(i, j)) && !isNaN(decay(i, j))) {
                result(i, j) = mean_current(i, j) - decay(i, j);
            }
        }
    }

    return result;
}

DataFrame ts_Quantile(const DataFrame& s1, int n, double rettype) {
    if (n < 5) n = 5;

    // 与Python版本保持一致
    double q = 0.5;  // 默认使用中位数

    if (rettype == 0.2 || rettype == 'A') {
        q = 0.2;
    } else if (rettype == 0.4 || rettype == 'B') {
        q = 0.4;
    } else if (rettype == 0.6 || rettype == 'C') {
        q = 0.6;
    } else if (rettype == 0.8 || rettype == 'D') {
        q = 0.8;
    }

    DataFrame result = DataFrame::Constant(s1.rows(), s1.cols(), std::numeric_limits<double>::quiet_NaN());

    // 对每一列计算滚动分位数
    for (int j = 0; j < s1.cols(); ++j) {
        for (int i = 0; i < s1.rows(); ++i) {
            int start = std::max(0, i - n + 1);
            std::vector<double> values;

            // 收集窗口内的非NaN值
            for (int k = start; k <= i; ++k) {
                if (!isNaN(s1(k, j))) {
                    values.push_back(s1(k, j));
                }
            }

            if (!values.empty()) {
                // 排序
                std::sort(values.begin(), values.end());

                // 计算分位数
                double pos = q * (values.size() - 1);
                int idx_lower = static_cast<int>(pos);
                int idx_upper = std::min(idx_lower + 1, static_cast<int>(values.size() - 1));
                double weight = pos - idx_lower;

                // 线性插值
                result(i, j) = values[idx_lower] * (1.0 - weight) + values[idx_upper] * weight;
            }
        }
    }

    return result;
}

} // namespace feature_operators
