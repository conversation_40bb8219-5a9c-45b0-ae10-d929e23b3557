#!/usr/bin/env python3
# -*- coding: utf-8 -*-

import sys
import os
import numpy as np
import pandas as pd
import matplotlib.pyplot as plt
import subprocess
import tempfile
import json

# 添加Python因子计算路径
sys.path.append('/home/<USER>/git/realTime/Code/strategy')

# 导入Python因子计算模块
import feature_operator_funcs as fof

def generate_test_data(rows=500, cols=5, seed=42):
    """生成测试数据"""
    np.random.seed(seed)

    # 初始价格
    base_prices = np.random.uniform(100, 200, cols)

    # 创建数据框
    open_data = np.zeros((rows, cols))
    high_data = np.zeros((rows, cols))
    low_data = np.zeros((rows, cols))
    close_data = np.zeros((rows, cols))
    volume_data = np.zeros((rows, cols))
    amount_data = np.zeros((rows, cols))
    vwap_data = np.zeros((rows, cols))

    # 设置初始值
    for j in range(cols):
        open_data[0, j] = base_prices[j]
        h = base_prices[j] * (1.0 + 0.01 * np.random.rand())
        l = base_prices[j] * (1.0 - 0.01 * np.random.rand())
        high_data[0, j] = h
        low_data[0, j] = l
        close_data[0, j] = l + (h - l) * np.random.rand()
        volume_data[0, j] = np.random.uniform(1000, 10000)
        amount_data[0, j] = volume_data[0, j] * (open_data[0, j] + close_data[0, j]) / 2.0
        vwap_data[0, j] = amount_data[0, j] / volume_data[0, j]

    # 生成后续K线
    for i in range(1, rows):
        for j in range(cols):
            prev_close = close_data[i-1, j]
            change_pct = 0.01 * (np.random.rand() * 2 - 1)  # -1% 到 1% 的变化
            open_data[i, j] = prev_close * (1.0 + change_pct)

            h = open_data[i, j] * (1.0 + 0.01 * np.random.rand())
            l = open_data[i, j] * (1.0 - 0.01 * np.random.rand())
            high_data[i, j] = h
            low_data[i, j] = l
            close_data[i, j] = l + (h - l) * np.random.rand()

            volume_data[i, j] = np.random.uniform(1000, 10000)
            amount_data[i, j] = volume_data[i, j] * (open_data[i, j] + close_data[i, j]) / 2.0
            vwap_data[i, j] = amount_data[i, j] / volume_data[i, j]

    # 创建时间索引
    dates = pd.date_range(start='2023-01-01', periods=rows, freq='15min')

    # 创建合约列名
    contracts = [f'contract_{i}' for i in range(cols)]

    # 创建DataFrame
    open_df = pd.DataFrame(open_data, index=dates, columns=contracts)
    high_df = pd.DataFrame(high_data, index=dates, columns=contracts)
    low_df = pd.DataFrame(low_data, index=dates, columns=contracts)
    close_df = pd.DataFrame(close_data, index=dates, columns=contracts)
    volume_df = pd.DataFrame(volume_data, index=dates, columns=contracts)
    amount_df = pd.DataFrame(amount_data, index=dates, columns=contracts)
    vwap_df = pd.DataFrame(vwap_data, index=dates, columns=contracts)

    return open_df, high_df, low_df, close_df, volume_df, amount_df, vwap_df

def save_data_to_csv(data_dict, output_dir):
    """保存数据到CSV文件"""
    os.makedirs(output_dir, exist_ok=True)

    for name, df in data_dict.items():
        df.to_csv(os.path.join(output_dir, f"{name}.csv"))

    print(f"数据已保存到 {output_dir}")

def run_cpp_operator_test(data_dir, output_file):
    """运行C++算子测试程序"""
    # 编译C++测试程序
    subprocess.run(["cd /home/<USER>/git/realTime/test_eigen/build && cmake .. && make"], shell=True, check=True)

    # 运行C++测试程序
    cmd = f"/home/<USER>/git/realTime/test_eigen/build/test_operators_csv {data_dir} {output_file}"
    subprocess.run(cmd, shell=True, check=True)

    # 加载C++结果
    with open(output_file, 'r') as f:
        cpp_results = json.load(f)

    return cpp_results

def calculate_python_operators(open_df, high_df, low_df, close_df, volume_df, amount_df, vwap_df):
    """使用Python计算算子"""
    # 创建数据字典
    data_dict = {
        'open': open_df,
        'high': high_df,
        'low': low_df,
        'close': close_df,
        'volume': volume_df,
        'amount': amount_df,
        'vwap': vwap_df
    }

    # 计算各种算子
    python_results = {}

    # 基本算术运算
    python_results['add'] = fof.Add(data_dict['close'], data_dict['open'])
    python_results['minus'] = fof.Minus(data_dict['close'], data_dict['open'])
    python_results['multiply'] = fof.Multiply(data_dict['close'], data_dict['volume'])
    python_results['divide'] = fof.Divide(data_dict['close'], data_dict['open'])
    python_results['sqrt'] = fof.Sqrt(data_dict['volume'])
    python_results['log'] = fof.Log(data_dict['volume'])
    python_results['abs'] = fof.Abs(fof.Minus(data_dict['close'], data_dict['open']))
    python_results['max'] = fof.Max(data_dict['close'], data_dict['open'])
    python_results['min'] = fof.Min(data_dict['close'], data_dict['open'])
    python_results['equal'] = fof.Equal(data_dict['close'], data_dict['open'])
    python_results['power'] = fof.Power(data_dict['volume'], 2)
    python_results['sign'] = fof.Sign(fof.Minus(data_dict['close'], data_dict['open']))
    python_results['reverse'] = fof.Reverse(data_dict['close'])
    python_results['exp'] = fof.Exp(fof.Log(data_dict['volume']))
    python_results['ceil'] = fof.Ceil(data_dict['close'])
    python_results['floor'] = fof.Floor(data_dict['close'])
    python_results['round'] = fof.Round(data_dict['close'])
    python_results['signedpower'] = fof.SignedPower(data_dict['close'], 2)
    python_results['softsign'] = fof.Softsign(data_dict['close'])

    # 逻辑运算
    python_results['and'] = fof.And(data_dict['close'], data_dict['open'])
    python_results['or'] = fof.Or(data_dict['close'], data_dict['open'])
    python_results['not'] = fof.Not(data_dict['close'])
    python_results['xor'] = fof.Xor(data_dict['close'], data_dict['open'])
    python_results['mthan'] = fof.Mthan(data_dict['close'], data_dict['open'])
    python_results['methan'] = fof.MEthan(data_dict['close'], data_dict['open'])
    python_results['lthan'] = fof.Lthan(data_dict['close'], data_dict['open'])
    python_results['lethan'] = fof.LEthan(data_dict['close'], data_dict['open'])
    python_results['unequal'] = fof.UnEqual(data_dict['close'], data_dict['open'])

    # 时间序列运算
    python_results['ts_delay'] = fof.ts_Delay(data_dict['close'], 1)
    python_results['ts_mean'] = fof.ts_Mean(data_dict['close'], 10)
    python_results['ts_sum'] = fof.ts_Sum(data_dict['volume'], 10)
    python_results['ts_stdev'] = fof.ts_Stdev(data_dict['close'], 10)
    python_results['ts_corr'] = fof.ts_Corr(data_dict['close'], data_dict['volume'], 60)
    python_results['ts_min'] = fof.ts_Min(data_dict['close'], 10)
    python_results['ts_max'] = fof.ts_Max(data_dict['close'], 10)
    python_results['ts_argmax'] = fof.ts_Argmax(data_dict['close'], 10)
    python_results['ts_argmin'] = fof.ts_Argmin(data_dict['close'], 10)
    python_results['ts_rank'] = fof.ts_Rank(data_dict['close'], 10)
    python_results['ts_delta'] = fof.ts_Delta(data_dict['close'], 10)
    python_results['ts_divide'] = fof.ts_Divide(data_dict['close'], 10)
    python_results['ts_chgrate'] = fof.ts_ChgRate(data_dict['close'], 10)
    python_results['ts_median'] = fof.ts_Median(data_dict['close'], 10)
    python_results['ts_skewness'] = fof.ts_Skewness(data_dict['close'], 10)
    python_results['ts_kurtosis'] = fof.ts_Kurtosis(data_dict['close'], 10)
    python_results['ts_scale'] = fof.ts_Scale(data_dict['close'], 10)
    python_results['ts_product'] = fof.ts_Product(data_dict['close'], 10)
    python_results['ts_transnorm'] = fof.ts_TransNorm(data_dict['close'], 10)
    python_results['ts_decay'] = fof.ts_Decay(data_dict['close'], 10)
    python_results['ts_decay2'] = fof.ts_Decay2(data_dict['close'], 10)
    python_results['ts_partial_corr'] = fof.ts_Partial_corr(data_dict['close'], data_dict['open'], data_dict['volume'], 10)
    python_results['ts_regression_a'] = fof.ts_Regression(data_dict['close'], data_dict['open'], 10, 'A')
    python_results['ts_regression_b'] = fof.ts_Regression(data_dict['close'], data_dict['open'], 10, 'B')
    python_results['ts_regression_r'] = fof.ts_Regression(data_dict['close'], data_dict['open'], 10, 'C')
    python_results['ts_entropy'] = fof.ts_Entropy(data_dict['close'], 10)
    # 暂时注释掉ts_maxdd，因为它依赖于joblib.Parallel
    # python_results['ts_maxdd'] = fof.ts_MaxDD(data_dict['close'], 10)
    python_results['ts_meanchg'] = fof.ts_MeanChg(data_dict['close'], 10)
    # 测试ts_quantile - 使用固定的数据，确保有非NaN值
    test_data = data_dict['close'].copy()
    # 确保前10行数据没有NaN值
    for i in range(10, test_data.shape[0]):
        for j in range(test_data.shape[1]):
            test_data.iloc[i, j] = i * 10 + j

    python_results['ts_quantile'] = fof.ts_Quantile(test_data, 10, 'C')

    # Tot系列函数
    python_results['tot_mean'] = fof.Tot_Mean(data_dict['close'])
    python_results['tot_sum'] = fof.Tot_Sum(data_dict['volume'])
    python_results['tot_stdev'] = fof.Tot_Stdev(data_dict['close'])
    python_results['tot_rank'] = fof.Tot_Rank(data_dict['volume'])
    python_results['tot_argmax'] = fof.Tot_ArgMax(data_dict['close'])
    python_results['tot_argmin'] = fof.Tot_ArgMin(data_dict['close'])
    python_results['tot_min'] = fof.Tot_Min(data_dict['close'])
    python_results['tot_max'] = fof.Tot_Max(data_dict['close'])
    python_results['tot_delta'] = fof.Tot_Delta(data_dict['close'])
    python_results['tot_divide'] = fof.Tot_Divide(data_dict['close'])
    python_results['tot_chgrate'] = fof.Tot_ChgRate(data_dict['close'])

    # 条件运算
    condition = fof.Minus(data_dict['close'], data_dict['open'])
    python_results['ifthen'] = fof.IfThen(condition, data_dict['volume'], data_dict['amount'])

    # 其他算子
    python_results['getnan'] = fof.getNan(data_dict['close'])

    # 面板运算
    python_results['pn_rank'] = fof.pn_Rank(data_dict['close'])
    python_results['pn_mean'] = fof.pn_Mean(data_dict['close'])
    python_results['pn_transnorm'] = fof.pn_TransNorm(data_dict['close'])
    python_results['pn_rank2'] = fof.pn_Rank2(data_dict['close'])
    python_results['pn_rankcentered'] = fof.pn_RankCentered(data_dict['close'])
    python_results['pn_fillmax'] = fof.pn_FillMax(data_dict['close'])
    python_results['pn_fillmin'] = fof.pn_FillMin(data_dict['close'])
    python_results['pn_transstd'] = fof.pn_TransStd(data_dict['close'])
    python_results['pn_stand'] = fof.pn_Stand(data_dict['close'])
    python_results['pn_winsor'] = fof.pn_Winsor(data_dict['close'], 2)
    python_results['pn_cut'] = fof.pn_Cut(data_dict['close'])
    python_results['pn_crossfit'] = fof.pn_CrossFit(data_dict['close'], data_dict['open'])

    # 创建分组标签 - 使用固定的分组标签，而不是随机生成
    group_labels = pd.DataFrame(np.zeros(data_dict['close'].shape, dtype=int),
                               index=data_dict['close'].index,
                               columns=data_dict['close'].columns)
    # 按列索引模3分组
    for j in range(group_labels.shape[1]):
        group_labels.iloc[:, j] = j % 3

    # 确保数据没有NaN值
    test_data = data_dict['close'].copy()
    # 填充一些固定的值，确保每个分组都有数据
    for i in range(test_data.shape[0]):
        for j in range(test_data.shape[1]):
            test_data.iloc[i, j] = i * 10 + j

    python_results['pn_groupneutral'] = fof.pn_GroupNeutral(test_data, group_labels)
    python_results['pn_groupnorm'] = fof.pn_GroupNorm(test_data, group_labels)
    python_results['pn_grouprank'] = fof.pn_GroupRank(test_data, group_labels)

    return python_results

def compare_results(cpp_results, python_results):
    """比较C++和Python的计算结果"""
    print("比较C++和Python的算子计算结果:")

    all_match = True
    for op_name in cpp_results:
        if op_name in python_results:
            cpp_data = np.array(cpp_results[op_name], dtype=float)

            # 确保Python数据是numpy数组
            if hasattr(python_results[op_name], 'values'):
                python_data = python_results[op_name].values.astype(float)
            else:
                python_data = np.array(python_results[op_name], dtype=float)

            # 处理None值
            cpp_data = np.where(np.equal(cpp_data, None), np.nan, cpp_data)

            # 特殊处理tot_argmax和tot_argmin
            # if op_name in ['tot_argmax', 'tot_argmin', 'tot_rank']:
                # 打印前几个值进行比较
                # print(f"\n{op_name} 比较:")
                # print(f"Python前5个值: {python_data[0, :5]}")
                # print(f"C++前5个值: {cpp_data[0, :5]}")

                # # 打印更多详细信息
                # print(f"Python第一列前10个值: {python_data[:10, 0]}")
                # print(f"C++第一列前10个值: {cpp_data[:10, 0]}")

                # # 打印第14行开始的几个值
                # print(f"\nPython {op_name} 从第14行开始的值:")
                # for i in range(14, min(20, python_data.shape[0])):
                #     print(f"行 {i}: {python_data[i, :5]}")

                # print(f"\nC++ {op_name} 从第14行开始的值:")
                # for i in range(14, min(20, cpp_data.shape[0])):
                #     print(f"行 {i}: {cpp_data[i, :5]}")

                # # 计算绝对误差
                # abs_error = np.abs(cpp_data - python_data)
                # print(f"最大绝对误差: {np.nanmax(abs_error)}")
                # print(f"平均绝对误差: {np.nanmean(abs_error)}")

                # # 找出误差最大的位置
                # max_error_idx = np.unravel_index(np.nanargmax(abs_error), abs_error.shape)
                # print(f"最大误差位置: {max_error_idx}")
                # print(f"该位置Python值: {python_data[max_error_idx]}")
                # print(f"该位置C++值: {cpp_data[max_error_idx]}")

                # # 检查是否所有列的值都相同
                # is_consistent = True
                # for j in range(cpp_data.shape[1]):
                #     col_values = cpp_data[:, j]
                #     if not np.all(col_values == col_values[0]):
                #         is_consistent = False
                #         break

                # print(f"C++实现中每列的值是否一致: {is_consistent}")

                # # 检查Python实现
                # is_consistent_py = True
                # for j in range(python_data.shape[1]):
                #     col_values = python_data[:, j]
                #     if not np.all(np.isnan(col_values) | (col_values == col_values[0])):
                #         is_consistent_py = False
                #         break

                # print(f"Python实现中每列的值是否一致: {is_consistent_py}")

            # 计算相对误差
            mask = ~np.isnan(python_data) & ~np.isnan(cpp_data) & (np.abs(python_data) > 1e-10)
            if np.any(mask):
                rel_error = np.abs((cpp_data[mask] - python_data[mask]) / python_data[mask])
                max_error = np.max(rel_error)
                mean_error = np.mean(rel_error)

                if max_error > 1e-5:
                    all_match = False
                    print(f"算子 {op_name} 不匹配: 最大相对误差 = {max_error:.6f}, 平均相对误差 = {mean_error:.6f}")
                else:
                    print(f"算子 {op_name} 匹配: 最大相对误差 = {max_error:.6f}, 平均相对误差 = {mean_error:.6f}")
            else:
                print(f"算子 {op_name} 无法比较: 没有有效的非NaN值")
        else:
            all_match = False
            print(f"算子 {op_name} 在Python结果中不存在")

    for op_name in python_results:
        if op_name not in cpp_results:
            all_match = False
            print(f"算子 {op_name} 在C++结果中不存在")

    if all_match:
        print("所有算子计算结果匹配!")
    else:
        print("部分算子计算结果不匹配!")

def run_optimized_operator_test(data_dir, output_file):
    """运行优化版C++算子测试程序"""
    try:
        # 运行优化版C++测试程序
        cmd = f"/home/<USER>/git/realTime/test_eigen/build/test_optimized_operators {data_dir} {output_file}"
        subprocess.run(cmd, shell=True, check=True)

        # 加载优化版C++结果
        with open(output_file, 'r') as f:
            optimized_results = json.load(f)

        return optimized_results
    except Exception as e:
        print(f"运行优化版C++算子测试程序时出错: {e}")
        return {}

def main():
    # 生成测试数据
    print("生成测试数据...")
    open_df, high_df, low_df, close_df, volume_df, amount_df, vwap_df = generate_test_data()

    # 创建临时目录
    temp_dir = tempfile.mkdtemp()
    data_dir = os.path.join(temp_dir, "data")

    # 保存数据到CSV
    data_dict = {
        'open': open_df,
        'high': high_df,
        'low': low_df,
        'close': close_df,
        'volume': volume_df,
        'amount': amount_df,
        'vwap': vwap_df
    }
    save_data_to_csv(data_dict, data_dir)

    # 运行原始C++测试
    print("运行原始C++算子测试...")
    cpp_output_file = os.path.join(temp_dir, "cpp_operator_results.json")
    cpp_results = run_cpp_operator_test(data_dir, cpp_output_file)

    # 运行优化版C++测试
    print("运行优化版C++算子测试...")
    optimized_output_file = os.path.join(temp_dir, "optimized_operator_results.json")
    optimized_results = run_optimized_operator_test(data_dir, optimized_output_file)

    # 计算Python算子
    print("计算Python算子...")
    python_results = calculate_python_operators(open_df, high_df, low_df, close_df, volume_df, amount_df, vwap_df)

    # 比较Python和原始C++结果
    print("\n比较Python和原始C++结果:")
    compare_results(cpp_results, python_results)

    # 比较Python和优化版C++结果
    print("\n比较Python和优化版C++结果:")
    compare_results(optimized_results, python_results)

    # 比较原始C++和优化版C++结果
    print("\n比较原始C++和优化版C++结果:")
    compare_results(optimized_results, cpp_results)

if __name__ == "__main__":
    main()
