#!/usr/bin/env python3
# -*- coding: utf-8 -*-

import sys
import os
import numpy as np
import pandas as pd
import matplotlib.pyplot as plt
import subprocess
import tempfile
import json

# 添加Python因子计算路径
sys.path.append('/home/<USER>/git/realTime/Code/strategy')

# 导入Python因子计算模块
import feature_operator_funcs as fof
from getMinFactor_fast_15min_sele2 import get_minFactorForms3

def generate_test_data(rows=1000, cols=5, seed=42):
    """生成测试数据"""
    np.random.seed(seed)

    # 初始价格
    base_prices = np.random.uniform(100, 200, cols)

    # 创建数据框
    open_data = np.zeros((rows, cols))
    high_data = np.zeros((rows, cols))
    low_data = np.zeros((rows, cols))
    close_data = np.zeros((rows, cols))
    volume_data = np.zeros((rows, cols))
    amount_data = np.zeros((rows, cols))
    vwap_data = np.zeros((rows, cols))

    # 设置初始值
    for j in range(cols):
        open_data[0, j] = base_prices[j]
        h = base_prices[j] * (1.0 + 0.01 * np.random.rand())
        l = base_prices[j] * (1.0 - 0.01 * np.random.rand())
        high_data[0, j] = h
        low_data[0, j] = l
        close_data[0, j] = l + (h - l) * np.random.rand()
        volume_data[0, j] = np.random.uniform(1000, 10000)
        amount_data[0, j] = volume_data[0, j] * (open_data[0, j] + close_data[0, j]) / 2.0
        vwap_data[0, j] = amount_data[0, j] / volume_data[0, j]

    # 生成后续K线
    for i in range(1, rows):
        for j in range(cols):
            prev_close = close_data[i-1, j]
            change_pct = 0.01 * (np.random.rand() * 2 - 1)  # -1% 到 1% 的变化
            open_data[i, j] = prev_close * (1.0 + change_pct)

            h = open_data[i, j] * (1.0 + 0.01 * np.random.rand())
            l = open_data[i, j] * (1.0 - 0.01 * np.random.rand())
            high_data[i, j] = h
            low_data[i, j] = l
            close_data[i, j] = l + (h - l) * np.random.rand()

            volume_data[i, j] = np.random.uniform(1000, 10000)
            amount_data[i, j] = volume_data[i, j] * (open_data[i, j] + close_data[i, j]) / 2.0
            vwap_data[i, j] = amount_data[i, j] / volume_data[i, j]

    # 创建时间索引
    dates = pd.date_range(start='2023-01-01', periods=rows, freq='15min')

    # 创建合约列名
    contracts = [f'contract_{i}' for i in range(cols)]

    # 创建DataFrame
    open_df = pd.DataFrame(open_data, index=dates, columns=contracts)
    high_df = pd.DataFrame(high_data, index=dates, columns=contracts)
    low_df = pd.DataFrame(low_data, index=dates, columns=contracts)
    close_df = pd.DataFrame(close_data, index=dates, columns=contracts)
    volume_df = pd.DataFrame(volume_data, index=dates, columns=contracts)
    amount_df = pd.DataFrame(amount_data, index=dates, columns=contracts)
    vwap_df = pd.DataFrame(vwap_data, index=dates, columns=contracts)

    return open_df, high_df, low_df, close_df, volume_df, amount_df, vwap_df

def save_data_to_csv(data_dict, output_dir):
    """保存数据到CSV文件"""
    os.makedirs(output_dir, exist_ok=True)

    for name, df in data_dict.items():
        df.to_csv(os.path.join(output_dir, f"{name}.csv"))

    print(f"数据已保存到 {output_dir}")

def run_cpp_test(data_dir, output_file):
    """运行C++测试程序"""
    # 编译C++测试程序
    subprocess.run(["cd /home/<USER>/git/realTime/test_eigen/build && cmake .. && make"], shell=True, check=True)

    # 运行C++测试程序
    cmd = f"/home/<USER>/git/realTime/test_eigen/build/test_factors_csv {data_dir} {output_file}"
    subprocess.run(cmd, shell=True, check=True)

    # 加载C++结果
    with open(output_file, 'r') as f:
        cpp_results = json.load(f)

    return cpp_results

def calculate_python_factors(open_df, high_df, low_df, close_df, volume_df, amount_df, vwap_df):
    """使用Python计算因子"""
    # 获取因子公式
    factor_forms_df = get_minFactorForms3()

    # 创建数据字典
    data_dict = {
        'Open': open_df,
        'High': high_df,
        'Low': low_df,
        'Close': close_df,
        'Volume': volume_df,
        'Amount': amount_df,
        'VWAP': vwap_df
    }

    # 添加算子函数到全局命名空间
    globals_dict = globals().copy()
    for name in dir(fof):
        if not name.startswith('_'):
            globals_dict[name] = getattr(fof, name)

    # 计算因子
    python_results = {}

    # 遍历DataFrame中的每一行
    for i in range(len(factor_forms_df)):
        factor_name = factor_forms_df['fname'].iloc[i]
        factor_form = factor_forms_df['forms'].iloc[i]

        try:
            # 使用eval计算因子
            result = eval(factor_form, globals_dict, data_dict)
            python_results[factor_name] = result
        except Exception as e:
            print(f"计算因子 {factor_name} 时出错: {e}")

    return python_results

def compare_results(cpp_results, python_results):
    """比较C++和Python的计算结果"""
    print("比较C++和Python的计算结果:")

    all_match = True
    for factor_name in cpp_results:
        if factor_name in python_results:
            cpp_data = np.array(cpp_results[factor_name], dtype=float)

            # 确保Python数据是numpy数组
            if hasattr(python_results[factor_name], 'values'):
                python_data = python_results[factor_name].values.astype(float)
            else:
                python_data = np.array(python_results[factor_name], dtype=float)

            # 处理None值
            cpp_data = np.where(np.equal(cpp_data, None), np.nan, cpp_data)

            # 计算相对误差
            mask = ~np.isnan(python_data) & ~np.isnan(cpp_data) & (np.abs(python_data) > 1e-10)
            if np.any(mask):
                rel_error = np.abs((cpp_data[mask] - python_data[mask]) / python_data[mask])
                max_error = np.max(rel_error)
                mean_error = np.mean(rel_error)

                if max_error > 1e-5:
                    all_match = False
                    print(f"因子 {factor_name} 不匹配: 最大相对误差 = {max_error:.6f}, 平均相对误差 = {mean_error:.6f}")
                else:
                    print(f"因子 {factor_name} 匹配: 最大相对误差 = {max_error:.6f}, 平均相对误差 = {mean_error:.6f}")
            else:
                print(f"因子 {factor_name} 无法比较: 没有有效的非NaN值")
        else:
            all_match = False
            print(f"因子 {factor_name} 在Python结果中不存在")

    for factor_name in python_results:
        if factor_name not in cpp_results:
            all_match = False
            print(f"因子 {factor_name} 在C++结果中不存在")

    if all_match:
        print("所有因子计算结果匹配!")
    else:
        print("部分因子计算结果不匹配!")

def main():
    # 生成测试数据
    print("生成测试数据...")
    open_df, high_df, low_df, close_df, volume_df, amount_df, vwap_df = generate_test_data()

    # 创建临时目录
    temp_dir = tempfile.mkdtemp()
    data_dir = os.path.join(temp_dir, "data")

    # 保存数据到CSV
    data_dict = {
        'open': open_df,
        'high': high_df,
        'low': low_df,
        'close': close_df,
        'volume': volume_df,
        'amount': amount_df,
        'vwap': vwap_df
    }
    save_data_to_csv(data_dict, data_dir)

    # 运行C++测试
    cpp_output_file = os.path.join(temp_dir, "cpp_results.json")
    cpp_results = run_cpp_test(data_dir, cpp_output_file)

    # 计算Python因子
    python_results = calculate_python_factors(open_df, high_df, low_df, close_df, volume_df, amount_df, vwap_df)

    # 比较结果
    compare_results(cpp_results, python_results)

if __name__ == "__main__":
    main()
