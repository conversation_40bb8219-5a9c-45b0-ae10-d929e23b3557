#include "factor_calculator.h"
#include <iostream>
#include <algorithm>
#include <unordered_map>

namespace factor_calculator {

// 获取所有因子组
std::vector<FactorGroup> get_all_factor_groups() {
    std::vector<FactorGroup> groups;

    groups.push_back(p1_corrs::get_factors());
    groups.push_back(p2_et::get_factors());
    groups.push_back(p3_mf::get_factors());
    groups.push_back(p4_ms::get_factors());
    groups.push_back(p5_to::get_factors());
    groups.push_back(p6_tn::get_factors());

    return groups;
}

// 计算单个因子
void calculate_factor(const std::string& factor_name,
                     const DataFrame& open,
                     const DataFrame& high,
                     const DataFrame& low,
                     const DataFrame& close,
                     const DataFrame& volume,
                     const DataFrame& amount,
                     const DataFrame& vwap,
                     DataFrame& result) {

    // 获取所有因子组
    std::vector<FactorGroup> groups = get_all_factor_groups();

    // 查找因子
    for (const auto& group : groups) {
        for (size_t i = 0; i < group.names.size(); ++i) {
            if (group.names[i] == factor_name) {
                group.functions[i](open, high, low, close, volume, amount, vwap, result);
                return;
            }
        }
    }

    // 如果找不到因子，设置为NaN
    std::cerr << "Factor not found: " << factor_name << std::endl;
    result = DataFrame::Constant(open.rows(), open.cols(), std::numeric_limits<double>::quiet_NaN());
}

// 计算多个因子
void calculate_factors(const std::vector<std::string>& factor_names,
                      const DataFrame& open,
                      const DataFrame& high,
                      const DataFrame& low,
                      const DataFrame& close,
                      const DataFrame& volume,
                      const DataFrame& amount,
                      const DataFrame& vwap,
                      std::map<std::string, DataFrame>& results) {

    for (const auto& factor_name : factor_names) {
        DataFrame result(open.rows(), open.cols());
        calculate_factor(factor_name, open, high, low, close, volume, amount, vwap, result);
        results[factor_name] = result;
    }
}

// p1_corrs 因子组实现
namespace p1_corrs {

void factor0(const DataFrame& open, const DataFrame& high, const DataFrame& low,
            const DataFrame& close, const DataFrame& volume, const DataFrame& amount,
            const DataFrame& vwap, DataFrame& result) {
    // ts_Corr(Close,Volume,60)
    result = ts_Corr(close, volume, 60);
}

void factor1(const DataFrame& open, const DataFrame& high, const DataFrame& low,
            const DataFrame& close, const DataFrame& volume, const DataFrame& amount,
            const DataFrame& vwap, DataFrame& result) {
    // ts_Corr(Close/ts_Delay(Close,1)-1,Volume,60)
    DataFrame close_delay = ts_Delay(close, 1);
    DataFrame close_return = Divide(close, close_delay);
    close_return = Minus(close_return, DataFrame::Ones(close.rows(), close.cols()));
    result = ts_Corr(close_return, volume, 60);
}

void factor2(const DataFrame& open, const DataFrame& high, const DataFrame& low,
            const DataFrame& close, const DataFrame& volume, const DataFrame& amount,
            const DataFrame& vwap, DataFrame& result) {
    // ts_Corr(ts_Delay(Close,1),Volume,60)
    DataFrame close_delay = ts_Delay(close, 1);
    result = ts_Corr(close_delay, volume, 60);
}

void factor3(const DataFrame& open, const DataFrame& high, const DataFrame& low,
            const DataFrame& close, const DataFrame& volume, const DataFrame& amount,
            const DataFrame& vwap, DataFrame& result) {
    // ts_Corr(Close,ts_Delay(Volume,1),60)
    DataFrame volume_delay = ts_Delay(volume, 1);
    result = ts_Corr(close, volume_delay, 60);
}

void factor4(const DataFrame& open, const DataFrame& high, const DataFrame& low,
            const DataFrame& close, const DataFrame& volume, const DataFrame& amount,
            const DataFrame& vwap, DataFrame& result) {
    // ts_Corr(ts_Delay(Close/ts_Delay(Close,1)-1,1),Close/ts_Delay(Close,1)-1,60)
    DataFrame close_delay = ts_Delay(close, 1);
    DataFrame close_return = Divide(close, close_delay);
    close_return = Minus(close_return, DataFrame::Ones(close.rows(), close.cols()));
    DataFrame delayed_return = ts_Delay(close_return, 1);
    result = ts_Corr(delayed_return, close_return, 60);
}

void factor5(const DataFrame& open, const DataFrame& high, const DataFrame& low,
            const DataFrame& close, const DataFrame& volume, const DataFrame& amount,
            const DataFrame& vwap, DataFrame& result) {
    // ts_Corr(ts_Delay(Close/ts_Delay(Close,1)-1,1),Close,60)
    DataFrame close_delay = ts_Delay(close, 1);
    DataFrame close_return = Divide(close, close_delay);
    close_return = Minus(close_return, DataFrame::Ones(close.rows(), close.cols()));
    DataFrame delayed_return = ts_Delay(close_return, 1);
    result = ts_Corr(delayed_return, close, 60);
}

void factor6(const DataFrame& open, const DataFrame& high, const DataFrame& low,
            const DataFrame& close, const DataFrame& volume, const DataFrame& amount,
            const DataFrame& vwap, DataFrame& result) {
    // ts_Corr(Volume,Volume-ts_Delay(Volume,1),60)
    DataFrame volume_delay = ts_Delay(volume, 1);
    DataFrame volume_change = Minus(volume, volume_delay);
    result = ts_Corr(volume, volume_change, 60);
}

void factor7(const DataFrame& open, const DataFrame& high, const DataFrame& low,
            const DataFrame& close, const DataFrame& volume, const DataFrame& amount,
            const DataFrame& vwap, DataFrame& result) {
    // ts_Corr(Volume-ts_Delay(Volume,1), Volume-ts_Delay(Volume,1) - ts_Delay(Volume-ts_Delay(Volume,1),1),60)
    DataFrame volume_delay = ts_Delay(volume, 1);
    DataFrame volume_change = Minus(volume, volume_delay);
    DataFrame delayed_volume_change = ts_Delay(volume_change, 1);
    DataFrame volume_change_change = Minus(volume_change, delayed_volume_change);
    result = ts_Corr(volume_change, volume_change_change, 60);
}

void factor8(const DataFrame& open, const DataFrame& high, const DataFrame& low,
            const DataFrame& close, const DataFrame& volume, const DataFrame& amount,
            const DataFrame& vwap, DataFrame& result) {
    // ts_Corr(VWAP,Volume,60)
    result = ts_Corr(vwap, volume, 60);
}

void factor9(const DataFrame& open, const DataFrame& high, const DataFrame& low,
            const DataFrame& close, const DataFrame& volume, const DataFrame& amount,
            const DataFrame& vwap, DataFrame& result) {
    // ts_Corr(VWAP/ts_Delay(VWAP,1)-1,Volume,60)
    DataFrame vwap_delay = ts_Delay(vwap, 1);
    DataFrame vwap_return = Divide(vwap, vwap_delay);
    vwap_return = Minus(vwap_return, DataFrame::Ones(vwap.rows(), vwap.cols()));
    result = ts_Corr(vwap_return, volume, 60);
}

void factor10(const DataFrame& open, const DataFrame& high, const DataFrame& low,
             const DataFrame& close, const DataFrame& volume, const DataFrame& amount,
             const DataFrame& vwap, DataFrame& result) {
    // ts_Corr(ts_Delay(VWAP,1),Volume,60)
    DataFrame vwap_delay = ts_Delay(vwap, 1);
    result = ts_Corr(vwap_delay, volume, 60);
}

void factor11(const DataFrame& open, const DataFrame& high, const DataFrame& low,
             const DataFrame& close, const DataFrame& volume, const DataFrame& amount,
             const DataFrame& vwap, DataFrame& result) {
    // ts_Corr(VWAP,ts_Delay(Volume,1),60)
    DataFrame volume_delay = ts_Delay(volume, 1);
    result = ts_Corr(vwap, volume_delay, 60);
}

FactorGroup get_factors() {
    FactorGroup group;
    group.type = "p1_corrs";

    // 添加因子名称
    for (int i = 0; i < 12; ++i) {
        group.names.push_back("p1_corrs" + std::to_string(i));
    }

    // 添加因子函数
    group.functions.push_back(factor0);
    group.functions.push_back(factor1);
    group.functions.push_back(factor2);
    group.functions.push_back(factor3);
    group.functions.push_back(factor4);
    group.functions.push_back(factor5);
    group.functions.push_back(factor6);
    group.functions.push_back(factor7);
    group.functions.push_back(factor8);
    group.functions.push_back(factor9);
    group.functions.push_back(factor10);
    group.functions.push_back(factor11);

    return group;
}

} // namespace p1_corrs

// p3_mf 因子组实现
namespace p3_mf {

void factor0(const DataFrame& open, const DataFrame& high, const DataFrame& low,
            const DataFrame& close, const DataFrame& volume, const DataFrame& amount,
            const DataFrame& vwap, DataFrame& result) {
    // Tot_Mean(Abs(Close/Open-1)/Sqrt(Volume))
    DataFrame close_open_ratio = Divide(close, open);
    close_open_ratio = Minus(close_open_ratio, DataFrame::Ones(close.rows(), close.cols()));
    DataFrame abs_ratio = Abs(close_open_ratio);
    DataFrame sqrt_volume = Sqrt(volume);
    DataFrame ratio_div_sqrt = Divide(abs_ratio, sqrt_volume);
    result = Tot_Mean(ratio_div_sqrt);
}

void factor1(const DataFrame& open, const DataFrame& high, const DataFrame& low,
            const DataFrame& close, const DataFrame& volume, const DataFrame& amount,
            const DataFrame& vwap, DataFrame& result) {
    // Tot_Stdev(Abs(Close/Open-1)/Sqrt(Volume))
    DataFrame close_open_ratio = Divide(close, open);
    close_open_ratio = Minus(close_open_ratio, DataFrame::Ones(close.rows(), close.cols()));
    DataFrame abs_ratio = Abs(close_open_ratio);
    DataFrame sqrt_volume = Sqrt(volume);
    DataFrame ratio_div_sqrt = Divide(abs_ratio, sqrt_volume);
    result = Tot_Stdev(ratio_div_sqrt);
}

void factor2(const DataFrame& open, const DataFrame& high, const DataFrame& low,
            const DataFrame& close, const DataFrame& volume, const DataFrame& amount,
            const DataFrame& vwap, DataFrame& result) {
    // Tot_Mean(IfThen(Tot_Rank(Abs(Close/Open-1)/Sqrt(Volume))-0.8,Close,getNan(Close)))
    DataFrame close_open_ratio = Divide(close, open);
    close_open_ratio = Minus(close_open_ratio, DataFrame::Ones(close.rows(), close.cols()));
    DataFrame abs_ratio = Abs(close_open_ratio);
    DataFrame sqrt_volume = Sqrt(volume);
    DataFrame ratio_div_sqrt = Divide(abs_ratio, sqrt_volume);
    DataFrame rank = Tot_Rank(ratio_div_sqrt);
    DataFrame threshold = DataFrame::Constant(rank.rows(), rank.cols(), 0.8);
    DataFrame condition = Minus(rank, threshold);
    DataFrame nan_close = getNan(close);
    DataFrame selected = IfThen(condition, close, nan_close);
    result = Tot_Mean(selected);
}

void factor3(const DataFrame& open, const DataFrame& high, const DataFrame& low,
            const DataFrame& close, const DataFrame& volume, const DataFrame& amount,
            const DataFrame& vwap, DataFrame& result) {
    // Tot_Mean(IfThen(Tot_Rank(Abs(Close/Open-1)+(Volume))-0.8,Close,getNan(Close)))
    DataFrame close_open_ratio = Divide(close, open);
    close_open_ratio = Minus(close_open_ratio, DataFrame::Ones(close.rows(), close.cols()));
    DataFrame abs_ratio = Abs(close_open_ratio);
    DataFrame sum_term = Add(abs_ratio, volume);
    DataFrame rank = Tot_Rank(sum_term);
    DataFrame threshold = DataFrame::Constant(rank.rows(), rank.cols(), 0.8);
    DataFrame condition = Minus(rank, threshold);
    DataFrame nan_close = getNan(close);
    DataFrame selected = IfThen(condition, close, nan_close);
    result = Tot_Mean(selected);
}

void factor4(const DataFrame& open, const DataFrame& high, const DataFrame& low,
            const DataFrame& close, const DataFrame& volume, const DataFrame& amount,
            const DataFrame& vwap, DataFrame& result) {
    // Tot_Mean(IfThen(Tot_Rank(Abs(Close/Open-1)/Log(Volume))-0.8,Close,getNan(Close)))
    DataFrame close_open_ratio = Divide(close, open);
    close_open_ratio = Minus(close_open_ratio, DataFrame::Ones(close.rows(), close.cols()));
    DataFrame abs_ratio = Abs(close_open_ratio);
    DataFrame log_volume = Log(volume);
    DataFrame ratio_div_log = Divide(abs_ratio, log_volume);
    DataFrame rank = Tot_Rank(ratio_div_log);
    DataFrame threshold = DataFrame::Constant(rank.rows(), rank.cols(), 0.8);
    DataFrame condition = Minus(rank, threshold);
    DataFrame nan_close = getNan(close);
    DataFrame selected = IfThen(condition, close, nan_close);
    result = Tot_Mean(selected);
}

void factor5(const DataFrame& open, const DataFrame& high, const DataFrame& low,
            const DataFrame& close, const DataFrame& volume, const DataFrame& amount,
            const DataFrame& vwap, DataFrame& result) {
    // Tot_Sum(IfThen(Equal(Abs(Close-Open),Abs(High-Low)),Amount,getNan(Close)))
    DataFrame close_open_diff = Minus(close, open);
    DataFrame abs_close_open_diff = Abs(close_open_diff);
    DataFrame high_low_diff = Minus(high, low);
    DataFrame abs_high_low_diff = Abs(high_low_diff);
    DataFrame condition = Equal(abs_close_open_diff, abs_high_low_diff);
    DataFrame nan_close = getNan(close);
    DataFrame selected = IfThen(condition, amount, nan_close);
    result = Tot_Sum(selected);
}

void factor6(const DataFrame& open, const DataFrame& high, const DataFrame& low,
            const DataFrame& close, const DataFrame& volume, const DataFrame& amount,
            const DataFrame& vwap, DataFrame& result) {
    // Tot_Sum(IfThen(Tot_Rank(Abs(Close/Open-1)/Sqrt(Volume))-0.8,Volume,getNan(Close)))
    DataFrame close_open_ratio = Divide(close, open);
    close_open_ratio = Minus(close_open_ratio, DataFrame::Ones(close.rows(), close.cols()));
    DataFrame abs_ratio = Abs(close_open_ratio);
    DataFrame sqrt_volume = Sqrt(volume);
    DataFrame ratio_div_sqrt = Divide(abs_ratio, sqrt_volume);
    DataFrame rank = Tot_Rank(ratio_div_sqrt);
    DataFrame threshold = DataFrame::Constant(rank.rows(), rank.cols(), 0.8);
    DataFrame condition = Minus(rank, threshold);
    DataFrame nan_close = getNan(close);
    DataFrame selected = IfThen(condition, volume, nan_close);
    result = Tot_Sum(selected);
}

void factor7(const DataFrame& open, const DataFrame& high, const DataFrame& low,
            const DataFrame& close, const DataFrame& volume, const DataFrame& amount,
            const DataFrame& vwap, DataFrame& result) {
    // Tot_Mean(ts_Stdev(Close/ts_Delay(Close,1)-1,10))
    DataFrame close_delay = ts_Delay(close, 1);
    DataFrame close_return = Divide(close, close_delay);
    close_return = Minus(close_return, DataFrame::Ones(close.rows(), close.cols()));
    DataFrame stdev = ts_Stdev(close_return, 10);
    result = Tot_Mean(stdev);
}

void factor8(const DataFrame& open, const DataFrame& high, const DataFrame& low,
            const DataFrame& close, const DataFrame& volume, const DataFrame& amount,
            const DataFrame& vwap, DataFrame& result) {
    // Tot_Mean(ts_Stdev(ts_Stdev(Close/ts_Delay(Close,1)-1,10),10))
    DataFrame close_delay = ts_Delay(close, 1);
    DataFrame close_return = Divide(close, close_delay);
    close_return = Minus(close_return, DataFrame::Ones(close.rows(), close.cols()));
    DataFrame stdev1 = ts_Stdev(close_return, 10);
    DataFrame stdev2 = ts_Stdev(stdev1, 10);
    result = Tot_Mean(stdev2);
}

void factor9(const DataFrame& open, const DataFrame& high, const DataFrame& low,
            const DataFrame& close, const DataFrame& volume, const DataFrame& amount,
            const DataFrame& vwap, DataFrame& result) {
    // ts_Corr(ts_Stdev(ts_Stdev(Close/ts_Delay(Close,1)-1,10),10),Volume*Close,60)
    DataFrame close_delay = ts_Delay(close, 1);
    DataFrame close_return = Divide(close, close_delay);
    close_return = Minus(close_return, DataFrame::Ones(close.rows(), close.cols()));
    DataFrame stdev1 = ts_Stdev(close_return, 10);
    DataFrame stdev2 = ts_Stdev(stdev1, 10);
    DataFrame volume_close = Multiply(volume, close);
    result = ts_Corr(stdev2, volume_close, 60);
}

void factor10(const DataFrame& open, const DataFrame& high, const DataFrame& low,
             const DataFrame& close, const DataFrame& volume, const DataFrame& amount,
             const DataFrame& vwap, DataFrame& result) {
    // Tot_Sum(IfThen(ts_Stdev(ts_Stdev(Close/ts_Delay(Close,1)-1,10),10)-Tot_Mean(ts_Stdev(ts_Stdev(Close/ts_Delay(Close,1)-1,10),10)),Volume*Close,getNan(Close)))
    DataFrame close_delay = ts_Delay(close, 1);
    DataFrame close_return = Divide(close, close_delay);
    close_return = Minus(close_return, DataFrame::Ones(close.rows(), close.cols()));
    DataFrame stdev1 = ts_Stdev(close_return, 10);
    DataFrame stdev2 = ts_Stdev(stdev1, 10);
    DataFrame mean_stdev2 = Tot_Mean(stdev2);
    DataFrame condition = Minus(stdev2, mean_stdev2);
    DataFrame volume_close = Multiply(volume, close);
    DataFrame nan_close = getNan(close);
    DataFrame selected = IfThen(condition, volume_close, nan_close);
    result = Tot_Sum(selected);
}

void factor11(const DataFrame& open, const DataFrame& high, const DataFrame& low,
             const DataFrame& close, const DataFrame& volume, const DataFrame& amount,
             const DataFrame& vwap, DataFrame& result) {
    // Tot_Sum(IfThen(Tot_Rank(Volume)-0.8,(Close-Open)/Open,getNan(Close)))
    DataFrame rank_volume = Tot_Rank(volume);
    DataFrame threshold = DataFrame::Constant(rank_volume.rows(), rank_volume.cols(), 0.8);
    DataFrame condition = Minus(rank_volume, threshold);
    DataFrame close_open_diff = Minus(close, open);
    DataFrame close_open_ratio = Divide(close_open_diff, open);
    DataFrame nan_close = getNan(close);
    DataFrame selected = IfThen(condition, close_open_ratio, nan_close);
    result = Tot_Sum(selected);
}

void factor12(const DataFrame& open, const DataFrame& high, const DataFrame& low,
             const DataFrame& close, const DataFrame& volume, const DataFrame& amount,
             const DataFrame& vwap, DataFrame& result) {
    // (Tot_Sum(IfThen(0.2-Tot_Rank(Volume),(Close-Open)/Open,getNan(Close))))
    DataFrame rank_volume = Tot_Rank(volume);
    DataFrame threshold = DataFrame::Constant(rank_volume.rows(), rank_volume.cols(), 0.2);
    DataFrame condition = Minus(threshold, rank_volume);
    DataFrame close_open_diff = Minus(close, open);
    DataFrame close_open_ratio = Divide(close_open_diff, open);
    DataFrame nan_close = getNan(close);
    DataFrame selected = IfThen(condition, close_open_ratio, nan_close);
    result = Tot_Sum(selected);
}

FactorGroup get_factors() {
    FactorGroup group;
    group.type = "p3_mf";

    // 添加因子名称
    for (int i = 0; i < 13; ++i) {
        group.names.push_back("p3_mf" + std::to_string(i));
    }

    // 添加因子函数
    group.functions.push_back(factor0);
    group.functions.push_back(factor1);
    group.functions.push_back(factor2);
    group.functions.push_back(factor3);
    group.functions.push_back(factor4);
    group.functions.push_back(factor5);
    group.functions.push_back(factor6);
    group.functions.push_back(factor7);
    group.functions.push_back(factor8);
    group.functions.push_back(factor9);
    group.functions.push_back(factor10);
    group.functions.push_back(factor11);
    group.functions.push_back(factor12);

    return group;
}

} // namespace p3_mf

// p4_ms 因子组实现
namespace p4_ms {

void factor0(const DataFrame& open, const DataFrame& high, const DataFrame& low,
            const DataFrame& close, const DataFrame& volume, const DataFrame& amount,
            const DataFrame& vwap, DataFrame& result) {
    // Tot_Mean(Close/ts_Delay(Close,1)-1-Log(Close/ts_Delay(Close,1)))
    DataFrame close_delay = ts_Delay(close, 1);
    DataFrame close_ratio = Divide(close, close_delay);
    DataFrame close_return = Minus(close_ratio, DataFrame::Ones(close.rows(), close.cols()));
    DataFrame log_ratio = Log(close_ratio);
    DataFrame diff = Minus(close_return, log_ratio);
    result = Tot_Mean(diff);
}

void factor1(const DataFrame& open, const DataFrame& high, const DataFrame& low,
            const DataFrame& close, const DataFrame& volume, const DataFrame& amount,
            const DataFrame& vwap, DataFrame& result) {
    // Tot_Mean(Close/ts_Delay(Close,1)-1-Log(Close/ts_Delay(Close,1))-(Log(Close/ts_Delay(Close,1)))**2/2)
    DataFrame close_delay = ts_Delay(close, 1);
    DataFrame close_ratio = Divide(close, close_delay);
    DataFrame close_return = Minus(close_ratio, DataFrame::Ones(close.rows(), close.cols()));
    DataFrame log_ratio = Log(close_ratio);
    DataFrame log_ratio_sq = Multiply(log_ratio, log_ratio);
    DataFrame log_ratio_sq_half = Multiply(log_ratio_sq, DataFrame::Constant(log_ratio.rows(), log_ratio.cols(), 0.5));
    DataFrame diff1 = Minus(close_return, log_ratio);
    DataFrame diff2 = Minus(diff1, log_ratio_sq_half);
    result = Tot_Mean(diff2);
}

void factor2(const DataFrame& open, const DataFrame& high, const DataFrame& low,
            const DataFrame& close, const DataFrame& volume, const DataFrame& amount,
            const DataFrame& vwap, DataFrame& result) {
    // Tot_ArgMax(Close)
    result = Tot_ArgMax(close);
}

void factor3(const DataFrame& open, const DataFrame& high, const DataFrame& low,
            const DataFrame& close, const DataFrame& volume, const DataFrame& amount,
            const DataFrame& vwap, DataFrame& result) {
    // Tot_ArgMin(Close)
    result = Tot_ArgMin(close);
}

void factor4(const DataFrame& open, const DataFrame& high, const DataFrame& low,
            const DataFrame& close, const DataFrame& volume, const DataFrame& amount,
            const DataFrame& vwap, DataFrame& result) {
    // Tot_Sum(((Close-ts_Delay(Close,1))/Close)**2)
    DataFrame close_delay = ts_Delay(close, 1);
    DataFrame close_diff = Minus(close, close_delay);
    DataFrame close_diff_ratio = Divide(close_diff, close);
    DataFrame close_diff_ratio_sq = Multiply(close_diff_ratio, close_diff_ratio);
    result = Tot_Sum(close_diff_ratio_sq);
}

void factor5(const DataFrame& open, const DataFrame& high, const DataFrame& low,
            const DataFrame& close, const DataFrame& volume, const DataFrame& amount,
            const DataFrame& vwap, DataFrame& result) {
    // Tot_Sum(((Close-ts_Delay(Close,1))/Close)**3)
    DataFrame close_delay = ts_Delay(close, 1);
    DataFrame close_diff = Minus(close, close_delay);
    DataFrame close_diff_ratio = Divide(close_diff, close);
    DataFrame close_diff_ratio_sq = Multiply(close_diff_ratio, close_diff_ratio);
    DataFrame close_diff_ratio_cube = Multiply(close_diff_ratio_sq, close_diff_ratio);
    result = Tot_Sum(close_diff_ratio_cube);
}

void factor6(const DataFrame& open, const DataFrame& high, const DataFrame& low,
            const DataFrame& close, const DataFrame& volume, const DataFrame& amount,
            const DataFrame& vwap, DataFrame& result) {
    // Tot_Sum(IfThen(Close/ts_Delay(Close,1)-1,Volume,getNan(Close)))
    DataFrame close_delay = ts_Delay(close, 1);
    DataFrame close_ratio = Divide(close, close_delay);
    DataFrame close_return = Minus(close_ratio, DataFrame::Ones(close.rows(), close.cols()));
    DataFrame nan_close = getNan(close);
    DataFrame selected = IfThen(close_return, volume, nan_close);
    result = Tot_Sum(selected);
}

FactorGroup get_factors() {
    FactorGroup group;
    group.type = "p4_ms";

    // 添加因子名称
    for (int i = 0; i < 7; ++i) {
        group.names.push_back("p4_ms" + std::to_string(i));
    }

    // 添加因子函数
    group.functions.push_back(factor0);
    group.functions.push_back(factor1);
    group.functions.push_back(factor2);
    group.functions.push_back(factor3);
    group.functions.push_back(factor4);
    group.functions.push_back(factor5);
    group.functions.push_back(factor6);

    return group;
}

} // namespace p4_ms

// p2_et 因子组实现
namespace p2_et {

void factor0(const DataFrame& open, const DataFrame& high, const DataFrame& low,
            const DataFrame& close, const DataFrame& volume, const DataFrame& amount,
            const DataFrame& vwap, DataFrame& result) {
    // Tot_Mean(IfThen(IfThen(Volume-ts_Delay(Volume,1)-Tot_Mean(Volume-ts_Delay(Volume,1))-Tot_Stdev(Volume-ts_Delay(Volume,1)),1,0),Close/ts_Delay(Close,1)-1,getNan(Close/ts_Delay(Close,1)-1)))
    DataFrame volume_delay = ts_Delay(volume, 1);
    DataFrame volume_change = Minus(volume, volume_delay);
    DataFrame mean_volume_change = Tot_Mean(volume_change);
    DataFrame stdev_volume_change = Tot_Stdev(volume_change);
    DataFrame volume_change_diff = Minus(volume_change, mean_volume_change);
    volume_change_diff = Minus(volume_change_diff, stdev_volume_change);

    DataFrame condition1 = IfThen(volume_change_diff, 1.0, 0.0);

    DataFrame close_delay = ts_Delay(close, 1);
    DataFrame close_return = Divide(close, close_delay);
    close_return = Minus(close_return, DataFrame::Ones(close.rows(), close.cols()));
    DataFrame nan_close_return = getNan(close_return);

    DataFrame selected = IfThen(condition1, close_return, nan_close_return);
    result = Tot_Mean(selected);
}

void factor1(const DataFrame& open, const DataFrame& high, const DataFrame& low,
            const DataFrame& close, const DataFrame& volume, const DataFrame& amount,
            const DataFrame& vwap, DataFrame& result) {
    // Tot_Mean(IfThen(ts_Sum(IfThen(Tot_Rank(Volume-ts_Delay(Volume,1))-0.9,1,0),10),Close/ts_Delay(Close,1)-1,getNan(Close/ts_Delay(Close,1)-1)))
    DataFrame volume_delay = ts_Delay(volume, 1);
    DataFrame volume_change = Minus(volume, volume_delay);
    DataFrame rank_volume_change = Tot_Rank(volume_change);
    DataFrame threshold = DataFrame::Constant(rank_volume_change.rows(), rank_volume_change.cols(), 0.9);
    DataFrame condition1 = Minus(rank_volume_change, threshold);
    DataFrame selected1 = IfThen(condition1, 1.0, 0.0);
    DataFrame sum_selected1 = ts_Sum(selected1, 10);

    DataFrame close_delay = ts_Delay(close, 1);
    DataFrame close_return = Divide(close, close_delay);
    close_return = Minus(close_return, DataFrame::Ones(close.rows(), close.cols()));
    DataFrame nan_close_return = getNan(close_return);

    DataFrame selected2 = IfThen(sum_selected1, close_return, nan_close_return);
    result = Tot_Mean(selected2);
}

void factor2(const DataFrame& open, const DataFrame& high, const DataFrame& low,
            const DataFrame& close, const DataFrame& volume, const DataFrame& amount,
            const DataFrame& vwap, DataFrame& result) {
    // Tot_Mean(IfThen(ts_Sum(IfThen(Tot_Rank(Volume-ts_Delay(Volume,1))-0.8,1,0),10),Close/ts_Delay(Close,1)-1,getNan(Close/ts_Delay(Close,1)-1)))
    DataFrame volume_delay = ts_Delay(volume, 1);
    DataFrame volume_change = Minus(volume, volume_delay);
    DataFrame rank_volume_change = Tot_Rank(volume_change);
    DataFrame threshold = DataFrame::Constant(rank_volume_change.rows(), rank_volume_change.cols(), 0.8);
    DataFrame condition1 = Minus(rank_volume_change, threshold);
    DataFrame selected1 = IfThen(condition1, 1.0, 0.0);
    DataFrame sum_selected1 = ts_Sum(selected1, 10);

    DataFrame close_delay = ts_Delay(close, 1);
    DataFrame close_return = Divide(close, close_delay);
    close_return = Minus(close_return, DataFrame::Ones(close.rows(), close.cols()));
    DataFrame nan_close_return = getNan(close_return);

    DataFrame selected2 = IfThen(sum_selected1, close_return, nan_close_return);
    result = Tot_Mean(selected2);
}

void factor3(const DataFrame& open, const DataFrame& high, const DataFrame& low,
            const DataFrame& close, const DataFrame& volume, const DataFrame& amount,
            const DataFrame& vwap, DataFrame& result) {
    // Tot_Mean(IfThen(ts_Sum(IfThen(Tot_Rank(Volume-ts_Delay(Volume,1))-0.7,1,0),10),Close/ts_Delay(Close,1)-1,getNan(Close/ts_Delay(Close,1)-1)))
    DataFrame volume_delay = ts_Delay(volume, 1);
    DataFrame volume_change = Minus(volume, volume_delay);
    DataFrame rank_volume_change = Tot_Rank(volume_change);
    DataFrame threshold = DataFrame::Constant(rank_volume_change.rows(), rank_volume_change.cols(), 0.7);
    DataFrame condition1 = Minus(rank_volume_change, threshold);
    DataFrame selected1 = IfThen(condition1, 1.0, 0.0);
    DataFrame sum_selected1 = ts_Sum(selected1, 10);

    DataFrame close_delay = ts_Delay(close, 1);
    DataFrame close_return = Divide(close, close_delay);
    close_return = Minus(close_return, DataFrame::Ones(close.rows(), close.cols()));
    DataFrame nan_close_return = getNan(close_return);

    DataFrame selected2 = IfThen(sum_selected1, close_return, nan_close_return);
    result = Tot_Mean(selected2);
}

void factor4(const DataFrame& open, const DataFrame& high, const DataFrame& low,
            const DataFrame& close, const DataFrame& volume, const DataFrame& amount,
            const DataFrame& vwap, DataFrame& result) {
    // Tot_Mean(IfThen(ts_Sum(IfThen(0.3-Tot_Rank(Volume-ts_Delay(Volume,1)),1,0),10),Close/ts_Delay(Close,1)-1,getNan(Close/ts_Delay(Close,1)-1)))
    DataFrame volume_delay = ts_Delay(volume, 1);
    DataFrame volume_change = Minus(volume, volume_delay);
    DataFrame rank_volume_change = Tot_Rank(volume_change);
    DataFrame threshold = DataFrame::Constant(rank_volume_change.rows(), rank_volume_change.cols(), 0.3);
    DataFrame condition1 = Minus(threshold, rank_volume_change);
    DataFrame selected1 = IfThen(condition1, 1.0, 0.0);
    DataFrame sum_selected1 = ts_Sum(selected1, 10);

    DataFrame close_delay = ts_Delay(close, 1);
    DataFrame close_return = Divide(close, close_delay);
    close_return = Minus(close_return, DataFrame::Ones(close.rows(), close.cols()));
    DataFrame nan_close_return = getNan(close_return);

    DataFrame selected2 = IfThen(sum_selected1, close_return, nan_close_return);
    result = Tot_Mean(selected2);
}

void factor5(const DataFrame& open, const DataFrame& high, const DataFrame& low,
            const DataFrame& close, const DataFrame& volume, const DataFrame& amount,
            const DataFrame& vwap, DataFrame& result) {
    // Tot_Mean(IfThen(ts_Sum(IfThen(0.2-Tot_Rank(Volume-ts_Delay(Volume,1)),1,0),10),Close/ts_Delay(Close,1)-1,getNan(Close/ts_Delay(Close,1)-1)))
    DataFrame volume_delay = ts_Delay(volume, 1);
    DataFrame volume_change = Minus(volume, volume_delay);
    DataFrame rank_volume_change = Tot_Rank(volume_change);
    DataFrame threshold = DataFrame::Constant(rank_volume_change.rows(), rank_volume_change.cols(), 0.2);
    DataFrame condition1 = Minus(threshold, rank_volume_change);
    DataFrame selected1 = IfThen(condition1, 1.0, 0.0);
    DataFrame sum_selected1 = ts_Sum(selected1, 10);

    DataFrame close_delay = ts_Delay(close, 1);
    DataFrame close_return = Divide(close, close_delay);
    close_return = Minus(close_return, DataFrame::Ones(close.rows(), close.cols()));
    DataFrame nan_close_return = getNan(close_return);

    DataFrame selected2 = IfThen(sum_selected1, close_return, nan_close_return);
    result = Tot_Mean(selected2);
}

FactorGroup get_factors() {
    FactorGroup group;
    group.type = "p2_et";

    // 添加因子名称
    for (int i = 0; i < 6; ++i) {
        group.names.push_back("p2_et" + std::to_string(i));
    }

    // 添加因子函数
    group.functions.push_back(factor0);
    group.functions.push_back(factor1);
    group.functions.push_back(factor2);
    group.functions.push_back(factor3);
    group.functions.push_back(factor4);
    group.functions.push_back(factor5);

    return group;
}

} // namespace p2_et

// p5_to 因子组实现
namespace p5_to {

void factor0(const DataFrame& open, const DataFrame& high, const DataFrame& low,
            const DataFrame& close, const DataFrame& volume, const DataFrame& amount,
            const DataFrame& vwap, DataFrame& result) {
    // Tot_Sum(IfThen(Close-ts_Delay(Close,1),Amount,-Amount))
    DataFrame close_delay = ts_Delay(close, 1);
    DataFrame close_change = Minus(close, close_delay);
    DataFrame neg_amount = Multiply(amount, DataFrame::Constant(amount.rows(), amount.cols(), -1.0));
    DataFrame selected = IfThen(close_change, amount, neg_amount);
    result = Tot_Sum(selected);
}

void factor1(const DataFrame& open, const DataFrame& high, const DataFrame& low,
            const DataFrame& close, const DataFrame& volume, const DataFrame& amount,
            const DataFrame& vwap, DataFrame& result) {
    // Tot_ArgMax(Volume)
    result = Tot_ArgMax(volume);
}

void factor2(const DataFrame& open, const DataFrame& high, const DataFrame& low,
            const DataFrame& close, const DataFrame& volume, const DataFrame& amount,
            const DataFrame& vwap, DataFrame& result) {
    // ts_Corr(Amount,ts_Delay(Amount,1),60)
    DataFrame amount_delay = ts_Delay(amount, 1);
    result = ts_Corr(amount, amount_delay, 60);
}

void factor3(const DataFrame& open, const DataFrame& high, const DataFrame& low,
            const DataFrame& close, const DataFrame& volume, const DataFrame& amount,
            const DataFrame& vwap, DataFrame& result) {
    // Tot_Sum(Amount)/(Tot_Sum(Abs(High-Low)/Close+Abs(Open-ts_Delay(Close,1)))*(1+Tot_Stdev(Close)/Tot_Mean(Close)))
    DataFrame sum_amount = Tot_Sum(amount);

    DataFrame high_low_diff = Minus(high, low);
    DataFrame abs_high_low_diff = Abs(high_low_diff);
    DataFrame abs_high_low_diff_div_close = Divide(abs_high_low_diff, close);

    DataFrame close_delay = ts_Delay(close, 1);
    DataFrame open_prev_close_diff = Minus(open, close_delay);
    DataFrame abs_open_prev_close_diff = Abs(open_prev_close_diff);

    DataFrame sum_term = Add(abs_high_low_diff_div_close, abs_open_prev_close_diff);
    DataFrame sum_sum_term = Tot_Sum(sum_term);

    DataFrame stdev_close = Tot_Stdev(close);
    DataFrame mean_close = Tot_Mean(close);
    DataFrame stdev_div_mean = Divide(stdev_close, mean_close);
    DataFrame one_plus_stdev_div_mean = Add(DataFrame::Ones(stdev_div_mean.rows(), stdev_div_mean.cols()), stdev_div_mean);

    DataFrame denominator = Multiply(sum_sum_term, one_plus_stdev_div_mean);
    result = Divide(sum_amount, denominator);
}

void factor4(const DataFrame& open, const DataFrame& high, const DataFrame& low,
            const DataFrame& close, const DataFrame& volume, const DataFrame& amount,
            const DataFrame& vwap, DataFrame& result) {
    // Tot_Mean(Abs(Close/ts_Delay(Close,1)-1)/Amount)
    DataFrame close_delay = ts_Delay(close, 1);
    DataFrame close_ratio = Divide(close, close_delay);
    DataFrame close_return = Minus(close_ratio, DataFrame::Ones(close.rows(), close.cols()));
    DataFrame abs_close_return = Abs(close_return);
    DataFrame abs_close_return_div_amount = Divide(abs_close_return, amount);
    result = Tot_Mean(abs_close_return_div_amount);
}

void factor5(const DataFrame& open, const DataFrame& high, const DataFrame& low,
            const DataFrame& close, const DataFrame& volume, const DataFrame& amount,
            const DataFrame& vwap, DataFrame& result) {
    // Tot_Sum((High-Low)/Close)/Tot_Sum(Amount)
    DataFrame high_low_diff = Minus(high, low);
    DataFrame high_low_diff_div_close = Divide(high_low_diff, close);
    DataFrame sum_high_low_diff_div_close = Tot_Sum(high_low_diff_div_close);
    DataFrame sum_amount = Tot_Sum(amount);
    result = Divide(sum_high_low_diff_div_close, sum_amount);
}

void factor6(const DataFrame& open, const DataFrame& high, const DataFrame& low,
            const DataFrame& close, const DataFrame& volume, const DataFrame& amount,
            const DataFrame& vwap, DataFrame& result) {
    // Tot_Sum((2*(High-Low)-Abs(Open-Close))/Close)/Tot_Sum(Amount)
    DataFrame high_low_diff = Minus(high, low);
    DataFrame high_low_diff_2x = Multiply(high_low_diff, DataFrame::Constant(high_low_diff.rows(), high_low_diff.cols(), 2.0));
    DataFrame open_close_diff = Minus(open, close);
    DataFrame abs_open_close_diff = Abs(open_close_diff);
    DataFrame numerator_term = Minus(high_low_diff_2x, abs_open_close_diff);
    DataFrame numerator_term_div_close = Divide(numerator_term, close);
    DataFrame sum_numerator_term_div_close = Tot_Sum(numerator_term_div_close);
    DataFrame sum_amount = Tot_Sum(amount);
    result = Divide(sum_numerator_term_div_close, sum_amount);
}

void factor7(const DataFrame& open, const DataFrame& high, const DataFrame& low,
            const DataFrame& close, const DataFrame& volume, const DataFrame& amount,
            const DataFrame& vwap, DataFrame& result) {
    // Tot_Sum(Abs(Close/ts_Delay(Close,1)-1)/(Close*Volume))
    DataFrame close_delay = ts_Delay(close, 1);
    DataFrame close_ratio = Divide(close, close_delay);
    DataFrame close_return = Minus(close_ratio, DataFrame::Ones(close.rows(), close.cols()));
    DataFrame abs_close_return = Abs(close_return);
    DataFrame close_volume = Multiply(close, volume);
    DataFrame abs_close_return_div_close_volume = Divide(abs_close_return, close_volume);
    result = Tot_Sum(abs_close_return_div_close_volume);
}

FactorGroup get_factors() {
    FactorGroup group;
    group.type = "p5_to";

    // 添加因子名称
    for (int i = 0; i < 8; ++i) {
        group.names.push_back("p5_to" + std::to_string(i));
    }

    // 添加因子函数
    group.functions.push_back(factor0);
    group.functions.push_back(factor1);
    group.functions.push_back(factor2);
    group.functions.push_back(factor3);
    group.functions.push_back(factor4);
    group.functions.push_back(factor5);
    group.functions.push_back(factor6);
    group.functions.push_back(factor7);

    return group;
}

} // namespace p5_to

// p6_tn 因子组实现
namespace p6_tn {

void factor0(const DataFrame& open, const DataFrame& high, const DataFrame& low,
            const DataFrame& close, const DataFrame& volume, const DataFrame& amount,
            const DataFrame& vwap, DataFrame& result) {
    // ts_Corr(Close-Open,Volume,60)
    DataFrame close_open_diff = Minus(close, open);
    result = ts_Corr(close_open_diff, volume, 60);
}

void factor1(const DataFrame& open, const DataFrame& high, const DataFrame& low,
            const DataFrame& close, const DataFrame& volume, const DataFrame& amount,
            const DataFrame& vwap, DataFrame& result) {
    // ts_Corr(Abs(Close-Open),Volume,60)
    DataFrame close_open_diff = Minus(close, open);
    DataFrame abs_close_open_diff = Abs(close_open_diff);
    result = ts_Corr(abs_close_open_diff, volume, 60);
}

void factor2(const DataFrame& open, const DataFrame& high, const DataFrame& low,
            const DataFrame& close, const DataFrame& volume, const DataFrame& amount,
            const DataFrame& vwap, DataFrame& result) {
    // ts_Corr(High-Low,Volume,60)
    DataFrame high_low_diff = Minus(high, low);
    result = ts_Corr(high_low_diff, volume, 60);
}

void factor3(const DataFrame& open, const DataFrame& high, const DataFrame& low,
            const DataFrame& close, const DataFrame& volume, const DataFrame& amount,
            const DataFrame& vwap, DataFrame& result) {
    // ts_Corr(High-Low,Amount,60)
    DataFrame high_low_diff = Minus(high, low);
    result = ts_Corr(high_low_diff, amount, 60);
}

void factor4(const DataFrame& open, const DataFrame& high, const DataFrame& low,
            const DataFrame& close, const DataFrame& volume, const DataFrame& amount,
            const DataFrame& vwap, DataFrame& result) {
    // ts_Corr(High-Low,Close,60)
    DataFrame high_low_diff = Minus(high, low);
    result = ts_Corr(high_low_diff, close, 60);
}

void factor5(const DataFrame& open, const DataFrame& high, const DataFrame& low,
            const DataFrame& close, const DataFrame& volume, const DataFrame& amount,
            const DataFrame& vwap, DataFrame& result) {
    // ts_Corr(High-Low,Open,60)
    DataFrame high_low_diff = Minus(high, low);
    result = ts_Corr(high_low_diff, open, 60);
}

void factor6(const DataFrame& open, const DataFrame& high, const DataFrame& low,
            const DataFrame& close, const DataFrame& volume, const DataFrame& amount,
            const DataFrame& vwap, DataFrame& result) {
    // ts_Corr(High-Low,VWAP,60)
    DataFrame high_low_diff = Minus(high, low);
    result = ts_Corr(high_low_diff, vwap, 60);
}

void factor7(const DataFrame& open, const DataFrame& high, const DataFrame& low,
            const DataFrame& close, const DataFrame& volume, const DataFrame& amount,
            const DataFrame& vwap, DataFrame& result) {
    // ts_Corr(High-Low,High,60)
    DataFrame high_low_diff = Minus(high, low);
    result = ts_Corr(high_low_diff, high, 60);
}

void factor8(const DataFrame& open, const DataFrame& high, const DataFrame& low,
            const DataFrame& close, const DataFrame& volume, const DataFrame& amount,
            const DataFrame& vwap, DataFrame& result) {
    // ts_Corr(High-Low,Low,60)
    DataFrame high_low_diff = Minus(high, low);
    result = ts_Corr(high_low_diff, low, 60);
}

void factor9(const DataFrame& open, const DataFrame& high, const DataFrame& low,
            const DataFrame& close, const DataFrame& volume, const DataFrame& amount,
            const DataFrame& vwap, DataFrame& result) {
    // Tot_Mean(ts_Stdev(High-Low,10))
    DataFrame high_low_diff = Minus(high, low);
    DataFrame stdev = ts_Stdev(high_low_diff, 10);
    result = Tot_Mean(stdev);
}

FactorGroup get_factors() {
    FactorGroup group;
    group.type = "p6_tn";

    // 添加因子名称
    for (int i = 0; i < 10; ++i) {
        group.names.push_back("p6_tn" + std::to_string(i));
    }

    // 添加因子函数
    group.functions.push_back(factor0);
    group.functions.push_back(factor1);
    group.functions.push_back(factor2);
    group.functions.push_back(factor3);
    group.functions.push_back(factor4);
    group.functions.push_back(factor5);
    group.functions.push_back(factor6);
    group.functions.push_back(factor7);
    group.functions.push_back(factor8);
    group.functions.push_back(factor9);

    return group;
}

} // namespace p6_tn

} // namespace factor_calculator
