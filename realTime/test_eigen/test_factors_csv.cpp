#include "factor_calculator.h"
#include <iostream>
#include <iomanip>
#include <vector>
#include <string>
#include <cmath>
#include <random>
#include <fstream>
#include <sstream>
#include <filesystem>

using namespace feature_operators;
using namespace factor_calculator;
namespace fs = std::filesystem;

// 从CSV文件加载数据
DataFrame loadCsv(const std::string& filepath) {
    std::ifstream file(filepath);
    if (!file.is_open()) {
        std::cerr << "无法打开文件: " << filepath << std::endl;
        return DataFrame();
    }

    std::vector<std::vector<double>> data;
    std::string line;

    // 跳过标题行
    std::getline(file, line);

    // 读取数据行
    while (std::getline(file, line)) {
        std::vector<double> row;
        std::stringstream ss(line);
        std::string cell;

        // 跳过第一列（日期）
        std::getline(ss, cell, ',');

        // 读取数据列
        while (std::getline(ss, cell, ',')) {
            try {
                double value = std::stod(cell);
                row.push_back(value);
            } catch (const std::exception& e) {
                row.push_back(std::numeric_limits<double>::quiet_NaN());
            }
        }

        if (!row.empty()) {
            data.push_back(row);
        }
    }

    // 创建Eigen矩阵
    int rows = data.size();
    int cols = rows > 0 ? data[0].size() : 0;

    DataFrame matrix(rows, cols);
    for (int i = 0; i < rows; ++i) {
        for (int j = 0; j < cols; ++j) {
            matrix(i, j) = data[i][j];
        }
    }

    return matrix;
}

// 将矩阵转换为JSON数组
std::string matrixToJson(const DataFrame& matrix) {
    std::stringstream ss;
    ss << "[";

    for (int i = 0; i < matrix.rows(); ++i) {
        ss << "[";
        for (int j = 0; j < matrix.cols(); ++j) {
            if (isNaN(matrix(i, j))) {
                ss << "null";
            } else {
                ss << matrix(i, j);
            }

            if (j < matrix.cols() - 1) {
                ss << ",";
            }
        }
        ss << "]";

        if (i < matrix.rows() - 1) {
            ss << ",";
        }
    }

    ss << "]";
    return ss.str();
}

// 将结果保存为JSON文件
void saveResultsToJson(const std::map<std::string, DataFrame>& results, const std::string& filepath) {
    std::ofstream file(filepath);
    if (!file.is_open()) {
        std::cerr << "无法创建文件: " << filepath << std::endl;
        return;
    }

    file << "{";

    bool first = true;
    for (const auto& pair : results) {
        if (!first) {
            file << ",";
        }
        first = false;

        file << "\"" << pair.first << "\":" << matrixToJson(pair.second);
    }

    file << "}";
    file.close();

    std::cout << "结果已保存到: " << filepath << std::endl;
}

int main(int argc, char* argv[]) {
    std::string dataDir;
    std::string outputFile;

    if (argc < 3) {
        std::cerr << "用法: " << argv[0] << " <数据目录> <输出文件>" << std::endl;
        std::cerr << "使用默认值..." << std::endl;
        dataDir = "/tmp/tmpdir/data";
        outputFile = "/tmp/tmpdir/cpp_results.json";
    } else {
        dataDir = argv[1];
        outputFile = argv[2];
    }

    // 确保输出目录存在
    fs::path outputPath(outputFile);
    fs::create_directories(outputPath.parent_path());

    std::cout << "从目录加载数据: " << dataDir << std::endl;

    // 加载数据
    DataFrame open = loadCsv(dataDir + "/open.csv");
    DataFrame high = loadCsv(dataDir + "/high.csv");
    DataFrame low = loadCsv(dataDir + "/low.csv");
    DataFrame close = loadCsv(dataDir + "/close.csv");
    DataFrame volume = loadCsv(dataDir + "/volume.csv");
    DataFrame amount = loadCsv(dataDir + "/amount.csv");
    DataFrame vwap = loadCsv(dataDir + "/vwap.csv");

    if (open.rows() == 0 || high.rows() == 0 || low.rows() == 0 || close.rows() == 0 ||
        volume.rows() == 0 || amount.rows() == 0 || vwap.rows() == 0) {
        std::cerr << "加载数据失败!" << std::endl;
        return 1;
    }

    std::cout << "数据加载成功: " << open.rows() << " 行, " << open.cols() << " 列" << std::endl;

    // 获取所有因子组
    std::vector<FactorGroup> groups = get_all_factor_groups();

    // 计算所有因子
    std::map<std::string, DataFrame> results;

    for (const auto& group : groups) {
        for (size_t i = 0; i < group.names.size(); ++i) {
            std::cout << "计算因子: " << group.names[i] << std::endl;
            DataFrame result(open.rows(), open.cols());
            group.functions[i](open, high, low, close, volume, amount, vwap, result);
            results[group.names[i]] = result;
        }
    }

    // 保存结果
    saveResultsToJson(results, outputFile);

    std::cout << "计算完成!" << std::endl;
    return 0;
}
