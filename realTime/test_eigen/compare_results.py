#!/usr/bin/env python3
import os
import pandas as pd
import numpy as np
import argparse
import json
try:
    from tabulate import tabulate
except ImportError:
    # Fallback if tabulate is not installed
    def tabulate(data, headers, tablefmt):
        result = ""
        # Add headers
        result += " | ".join(headers) + "\n"
        result += "-" * (sum(len(h) for h in headers) + 3 * (len(headers) - 1)) + "\n"
        # Add data
        for row in data:
            result += " | ".join(str(cell) for cell in row) + "\n"
        return result

def load_csv(filepath):
    """Load CSV file into pandas DataFrame."""
    try:
        df = pd.read_csv(filepath, index_col=0)
        return df
    except Exception as e:
        print(f"Error loading {filepath}: {e}")
        return None

def compare_dataframes(df1, df2, tolerance=1e-6):
    """
    Compare two DataFrames and return if they are equal within tolerance.

    Args:
        df1: First DataFrame
        df2: Second DataFrame
        tolerance: Tolerance for floating point comparison

    Returns:
        is_equal: Boolean indicating if DataFrames are equal within tolerance
        max_diff: Maximum absolute difference between DataFrames
        mean_diff: Mean absolute difference between DataFrames
        nan_diff: Number of positions where one DataFrame has NaN and the other doesn't
    """
    if df1.shape != df2.shape:
        print(f"Shape mismatch: {df1.shape} vs {df2.shape}")
        return False, float('inf'), float('inf'), float('inf')

    # Convert to numpy arrays to avoid index/column label issues
    arr1 = df1.values
    arr2 = df2.values

    # Check for NaN differences
    nan_mask1 = np.isnan(arr1)
    nan_mask2 = np.isnan(arr2)
    nan_diff = np.sum(nan_mask1 != nan_mask2)

    # Compare non-NaN values
    valid_mask = ~(nan_mask1 | nan_mask2)
    if np.sum(valid_mask) == 0:
        return nan_diff == 0, 0, 0, nan_diff

    # Calculate differences only for valid values
    diff = np.zeros_like(arr1)
    diff[valid_mask] = np.abs(arr1[valid_mask] - arr2[valid_mask])

    max_diff = np.max(diff[valid_mask]) if np.sum(valid_mask) > 0 else 0
    mean_diff = np.mean(diff[valid_mask]) if np.sum(valid_mask) > 0 else 0

    is_equal = max_diff <= tolerance and nan_diff == 0

    return is_equal, max_diff, mean_diff, nan_diff

def main():
    parser = argparse.ArgumentParser(description='Compare operator results from different implementations')
    parser.add_argument('--tolerance', type=float, default=1e-6, help='Tolerance for floating point comparison')
    parser.add_argument('--operators-file', type=str, default="/home/<USER>/git/realTime/test_eigen/all_operators.json",
                        help='JSON file containing operator lists')
    args = parser.parse_args()

    # Define paths
    base_dir = "/home/<USER>/git/realTime/test_eigen/test_results"
    unoptimized_dir = os.path.join(base_dir, "unoptimized")
    optimized_dir = os.path.join(base_dir, "optimized")
    python_dir = os.path.join(base_dir, "python")

    # Load operator lists from JSON file if it exists
    try:
        with open(args.operators_file, 'r') as f:
            operator_lists = json.load(f)
            all_operators = set(operator_lists["unoptimized"] +
                               operator_lists["optimized"] +
                               operator_lists["python"])
    except (FileNotFoundError, json.JSONDecodeError):
        # Fallback to a default list of operators
        all_operators = [
            "Add", "Minus", "Multiply", "Divide", "Sqrt", "Log", "Abs", "Sign", "Power",
            "ts_Delay", "ts_Mean", "ts_Sum", "ts_Stdev", "ts_Min", "ts_Max", "ts_Delta", "ts_Corr",
            "pn_Mean", "pn_Rank", "pn_Stand",
            "Max", "Min", "FilterInf",
            "Tot_Mean", "Tot_Sum", "Tot_Max", "Tot_Min"
        ]

    # Check which operators have result files
    operators = []
    missing_operators = {"unoptimized": [], "optimized": [], "python": []}

    for op in all_operators:
        unopt_path = os.path.join(unoptimized_dir, f"{op}.csv")
        opt_path = os.path.join(optimized_dir, f"{op}.csv")
        py_path = os.path.join(python_dir, f"{op}.csv")

        has_unopt = os.path.exists(unopt_path)
        has_opt = os.path.exists(opt_path)
        has_py = os.path.exists(py_path)

        if not has_unopt:
            missing_operators["unoptimized"].append(op)
        if not has_opt:
            missing_operators["optimized"].append(op)
        if not has_py:
            missing_operators["python"].append(op)

        if has_unopt and has_opt and has_py:
            operators.append(op)

    # Print missing operators
    print("Missing operator implementations:")
    print(f"  Unoptimized C++: {len(missing_operators['unoptimized'])} operators missing")
    if missing_operators["unoptimized"]:
        print("    " + ", ".join(missing_operators["unoptimized"][:10]) +
              (", ..." if len(missing_operators["unoptimized"]) > 10 else ""))

    print(f"  Optimized C++: {len(missing_operators['optimized'])} operators missing")
    if missing_operators["optimized"]:
        print("    " + ", ".join(missing_operators["optimized"][:10]) +
              (", ..." if len(missing_operators["optimized"]) > 10 else ""))

    print(f"  Python: {len(missing_operators['python'])} operators missing")
    if missing_operators["python"]:
        print("    " + ", ".join(missing_operators["python"][:10]) +
              (", ..." if len(missing_operators["python"]) > 10 else ""))

    print(f"\nComparing {len(operators)} operators with results from all implementations")

    # Prepare results table
    results = []

    print(f"Comparing results with tolerance: {args.tolerance}")

    for op in operators:
        unopt_path = os.path.join(unoptimized_dir, f"{op}.csv")
        opt_path = os.path.join(optimized_dir, f"{op}.csv")
        py_path = os.path.join(python_dir, f"{op}.csv")

        unopt_df = load_csv(unopt_path)
        opt_df = load_csv(opt_path)
        py_df = load_csv(py_path)

        if unopt_df is None or opt_df is None or py_df is None:
            results.append([op, "Error loading files", "Error loading files", "Error loading files"])
            continue

        # Compare unoptimized C++ vs optimized C++
        unopt_vs_opt_equal, unopt_vs_opt_max_diff, unopt_vs_opt_mean_diff, unopt_vs_opt_nan_diff = compare_dataframes(unopt_df, opt_df, args.tolerance)

        # Compare unoptimized C++ vs Python
        unopt_vs_py_equal, unopt_vs_py_max_diff, unopt_vs_py_mean_diff, unopt_vs_py_nan_diff = compare_dataframes(unopt_df, py_df, args.tolerance)

        # Compare optimized C++ vs Python
        opt_vs_py_equal, opt_vs_py_max_diff, opt_vs_py_mean_diff, opt_vs_py_nan_diff = compare_dataframes(opt_df, py_df, args.tolerance)

        # Add results to table
        results.append([
            op,
            "✓" if unopt_vs_opt_equal else f"✗ (max diff: {unopt_vs_opt_max_diff:.8f}, nan diff: {unopt_vs_opt_nan_diff})",
            "✓" if unopt_vs_py_equal else f"✗ (max diff: {unopt_vs_py_max_diff:.8f}, nan diff: {unopt_vs_py_nan_diff})",
            "✓" if opt_vs_py_equal else f"✗ (max diff: {opt_vs_py_max_diff:.8f}, nan diff: {opt_vs_py_nan_diff})"
        ])

    # Print results table
    headers = ["Operator", "Unopt C++ vs Opt C++", "Unopt C++ vs Python", "Opt C++ vs Python"]
    print(tabulate(results, headers=headers, tablefmt="grid"))

    # Count total matches and mismatches
    total_operators = len(operators)
    unopt_vs_opt_matches = sum(1 for row in results if row[1].startswith("✓"))
    unopt_vs_py_matches = sum(1 for row in results if row[2].startswith("✓"))
    opt_vs_py_matches = sum(1 for row in results if row[3].startswith("✓"))

    print("\nSummary:")
    print(f"Unoptimized C++ vs Optimized C++: {unopt_vs_opt_matches}/{total_operators} operators match ({unopt_vs_opt_matches/total_operators*100:.1f}%)")
    print(f"Unoptimized C++ vs Python: {unopt_vs_py_matches}/{total_operators} operators match ({unopt_vs_py_matches/total_operators*100:.1f}%)")
    print(f"Optimized C++ vs Python: {opt_vs_py_matches}/{total_operators} operators match ({opt_vs_py_matches/total_operators*100:.1f}%)")

    # Save results to file
    with open(os.path.join(base_dir, "comparison_results.txt"), "w") as f:
        f.write(tabulate(results, headers=headers, tablefmt="grid"))
        f.write("\n\nSummary:\n")
        f.write(f"Unoptimized C++ vs Optimized C++: {unopt_vs_opt_matches}/{total_operators} operators match ({unopt_vs_opt_matches/total_operators*100:.1f}%)\n")
        f.write(f"Unoptimized C++ vs Python: {unopt_vs_py_matches}/{total_operators} operators match ({unopt_vs_py_matches/total_operators*100:.1f}%)\n")
        f.write(f"Optimized C++ vs Python: {opt_vs_py_matches}/{total_operators} operators match ({opt_vs_py_matches/total_operators*100:.1f}%)\n")

if __name__ == "__main__":
    main()
