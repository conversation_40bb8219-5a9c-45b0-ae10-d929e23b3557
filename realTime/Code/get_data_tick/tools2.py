from datetime import datetime, UTC, timedelta
import os
import pandas as pd
import numpy as np

from pathlib import Path
home_dir = str(Path.home())
# 普通时间转换会iso时间

def timeConvert1(timestamp_str):
    # timestamp_str = '2025-03-12 07:05:00'
    if not type(timestamp_str) == str:
        timestamp_str = str(timestamp_str)
    dt = datetime.strptime(timestamp_str, '%Y-%m-%d %H:%M:%S')
    iso_format = dt.strftime('%Y-%m-%dT%H:%M:%SZ')
    # iso_format = datetime.strptime(iso_format, '%Y-%m-%dT%H:%M:%SZ')
    return iso_format


# 获取当前的 ISO 8601 时间
def get_nowISO():
    now_iso = datetime.now(UTC).strftime('%Y-%m-%dT%H:%M:%SZ')
    # now = datetime.strptime(now_iso, '%Y-%m-%dT%H:%M:%SZ')
    return now_iso


def get_timedelta(now,iso_format):
    iso_format = datetime.strptime(iso_format, '%Y-%m-%dT%H:%M:%SZ')
    now = datetime.strptime(now, '%Y-%m-%dT%H:%M:%SZ')
    time_difference = (now - iso_format ).total_seconds()
    # 计算 5分钟 的间隔数
    five_minute_intervals = int(time_difference // (5 * 60))
    return five_minute_intervals

def get_startTime(de):
    now = datetime.now(UTC)
    now_iso = now.strftime('%Y-%m-%dT%H:%M:%SZ')

    # 计算 1000 个 5 分钟的总时间差
    time_difference = timedelta(minutes=5 * de)

    # 减去时间差
    result_time = now - time_difference

    # 转换为 ISO 8601 格式
    result_iso = result_time.strftime('%Y-%m-%dT%H:%M:%SZ')
    return result_iso

def get_contracts():
    return ['IP/USDT:USDT', 'REX/USDT:USDT', 'AVL/USDT:USDT', 'BAN/USDT:USDT', 'KAITO/USDT:USDT', 'MON/USDT:USDT', 'VR/USDT:USDT', 'NS/USDT:USDT', 'DUCK/USDT:USDT', 'F/USDT:USDT', 'SHELL/USDT:USDT', 'J/USDT:USDT', 'COOK/USDT:USDT', 'FLOCK/USDT:USDT', 'STPT/USDT:USDT', 'BNX/USDT:USDT', 'CLOUD/USDT:USDT', '1000CATS/USDT:USDT', 'BROCCOLI/USDT:USDT', 'FB/USDT:USDT', '10000QUBIC/USDT:USDT', 'NC/USDT:USDT', 'VANA/USDT:USDT', 'GPS/USDT:USDT', 'REQ/USDT:USDT', 'GEMS/USDT:USDT', 'GOMINING/USDT:USDT', 'PAXG/USDT:USDT', 'SLF/USDT:USDT', 'ALCH/USDT:USDT', 'COW/USDT:USDT', 'ZBCN/USDT:USDT', 'AUCTION/USDT:USDT', 'AVAIL/USDT:USDT', 'SD/USDT:USDT', 'SWARMS/USDT:USDT', 'ZKJ/USDT:USDT', 'MAX/USDT:USDT', 'REN/USDT:USDT', 'JAILSTOOL/USDT:USDT', 'SUNDOG/USDT:USDT', 'SNT/USDT:USDT', 'SEND/USDT:USDT', '1000MUMU/USDT:USDT', 'ETHBTC/USDT:USDT', 'OM/USDT:USDT', 'XDC/USDT:USDT', 'XION/USDT:USDT', 'JELLYJELLY/USDT:USDT', 'GNO/USDT:USDT', 'L3/USDT:USDT', 'TSTBSC/USDT:USDT', 'AVAAI/USDT:USDT', 'VINE/USDT:USDT', 'CARV/USDT:USDT', 'PEAQ/USDT:USDT', '1000RATS/USDT:USDT', 'KOMA/USDT:USDT', 'PNUT/USDT:USDT', 'NEIROETH/USDT:USDT', 'OL/USDT:USDT', 'FUEL/USDT:USDT', 'SCA/USDT:USDT', 'XCN/USDT:USDT', 'PIRATE/USDT:USDT', 'SOLAYER/USDT:USDT', 'LUCE/USDT:USDT', 'ALEO/USDT:USDT', 'ZEREBRO/USDT:USDT', 'SWEAT/USDT:USDT', 'BEL/USDT:USDT', 'ACT/USDT:USDT', 'URO/USDT:USDT', 'MASA/USDT:USDT', 'XRD/USDT:USDT', 'XCH/USDT:USDT', 'GLM/USDT:USDT', 'A8/USDT:USDT', 'ARK/USDT:USDT', 'ACH/USDT:USDT', 'RIFSOL/USDT:USDT', 'DBR/USDT:USDT', '10000WHY/USDT:USDT', 'MEMEFI/USDT:USDT', 'GODS/USDT:USDT', 'B3/USDT:USDT', 'BOBA/USDT:USDT', 'BERA/USDT:USDT', 'PIPPIN/USDT:USDT', 'FIRE/USDT:USDT', 'REZ/USDT:USDT', 'SAFE/USDT:USDT', 'ARC/USDT:USDT', 'XMR/USDT:USDT', 'DEXE/USDT:USDT', 'MELANIA/USDT:USDT', 'ATH/USDT:USDT', 'COOKIE/USDT:USDT', 'GRASS/USDT:USDT', 'OG/USDT:USDT', '10000COQ/USDT:USDT', 'SWELL/USDT:USDT', 'LISTA/USDT:USDT', 'CAKE/USDT:USDT', 'FWOG/USDT:USDT', 'CVC/USDT:USDT', 'PLUME/USDT:USDT', 'ORBS/USDT:USDT', 'SOLV/USDT:USDT', 'MICHI/USDT:USDT', 'CHILLGUY/USDT:USDT', 'MKR/USDT:USDT', 'DGB/USDT:USDT', 'LAI/USDT:USDT', '1000000CHEEMS/USDT:USDT', 'AGLD/USDT:USDT', 'ORDI/USDT:USDT', 'HEI/USDT:USDT', 'ALU/USDT:USDT', 'MOTHER/USDT:USDT', 'MDT/USDT:USDT', '10000ELON/USDT:USDT', 'FOXY/USDT:USDT', 'PROS/USDT:USDT', 'RDNT/USDT:USDT', 'WAVES/USDT:USDT', 'ALPACA/USDT:USDT', 'RSS3/USDT:USDT', 'FIO/USDT:USDT', 'OSMO/USDT:USDT', 'ANIME/USDT:USDT', 'PRCL/USDT:USDT', 'PROM/USDT:USDT', 'OMG/USDT:USDT', 'MYRIA/USDT:USDT', 'RAD/USDT:USDT', 'XVS/USDT:USDT', 'IDEX/USDT:USDT', 'FLR/USDT:USDT', 'KMNO/USDT:USDT', 'BLUE/USDT:USDT', 'XEM/USDT:USDT', 'LOOKS/USDT:USDT', 'TAI/USDT:USDT', 'VVV/USDT:USDT', 'GRIFFAIN/USDT:USDT', 'CKB/USDT:USDT', 'MYRO/USDT:USDT', 'MERL/USDT:USDT', 'RUNE/USDT:USDT', 'TNSR/USDT:USDT', 'MOBILE/USDT:USDT', 'HIPPO/USDT:USDT', 'MAJOR/USDT:USDT', 'TIA/USDT:USDT', 'PONKE/USDT:USDT', '10000SATS/USDT:USDT', 'ZENT/USDT:USDT', 'XNO/USDT:USDT', '1000NEIROCTO/USDT:USDT', 'MBL/USDT:USDT', 'HNT/USDT:USDT', 'ZEC/USDT:USDT', 'SONIC/USDT:USDT', 'SOLO/USDT:USDT', 'BLAST/USDT:USDT', '10000LADYS/USDT:USDT', 'GIGA/USDT:USDT', 'NOT/USDT:USDT', 'MOODENG/USDT:USDT', 'GME/USDT:USDT', 'BUZZ/USDT:USDT', 'JTO/USDT:USDT', 'STORJ/USDT:USDT', 'ORDER/USDT:USDT', 'MVL/USDT:USDT', 'FARTCOIN/USDT:USDT', 'FORTH/USDT:USDT', 'CATI/USDT:USDT', 'TROY/USDT:USDT', '10000000AIDOGE/USDT:USDT', 'HMSTR/USDT:USDT', 'MNT/USDT:USDT', 'DATA/USDT:USDT', 'S/USDT:USDT', 'THE/USDT:USDT', 'BIO/USDT:USDT', 'CTC/USDT:USDT', 'VELO/USDT:USDT', 'HYPE/USDT:USDT', 'USUAL/USDT:USDT', 'LUMIA/USDT:USDT', 'AIXBT/USDT:USDT', 'VIDT/USDT:USDT', 'MAVIA/USDT:USDT', 'BAKE/USDT:USDT', 'POPCAT/USDT:USDT', 'PRIME/USDT:USDT', '1000APU/USDT:USDT', 'AI16Z/USDT:USDT', 'RAYDIUM/USDT:USDT', 'DOG/USDT:USDT', 'USTC/USDT:USDT', 'MOCA/USDT:USDT', 'PUFFER/USDT:USDT', 'PYR/USDT:USDT', 'BIGTIME/USDT:USDT', 'DRIFT/USDT:USDT', 'MASK/USDT:USDT', 'PENGU/USDT:USDT', 'DEEP/USDT:USDT', 'MORPHO/USDT:USDT', '1000TOSHI/USDT:USDT', 'FXS/USDT:USDT', '1000000PEIPEI/USDT:USDT', 'PEOPLE/USDT:USDT', 'DEGEN/USDT:USDT', 'AVA/USDT:USDT', 'G/USDT:USDT', 'RARE/USDT:USDT', 'TRB/USDT:USDT', 'HPOS10I/USDT:USDT', 'IOTX/USDT:USDT', 'GOAT/USDT:USDT', 'ENA/USDT:USDT', 'TRX/USDT:USDT', 'AGI/USDT:USDT', 'BSW/USDT:USDT', 'QNT/USDT:USDT', 'MANEKI/USDT:USDT', 'YGG/USDT:USDT', 'CRO/USDT:USDT', 'API3/USDT:USDT', 'APT/USDT:USDT', 'WIF/USDT:USDT', 'KAVA/USDT:USDT', 'CGPT/USDT:USDT', '10000WEN/USDT:USDT', 'COMBO/USDT:USDT', 'LINA/USDT:USDT', 'TWT/USDT:USDT', 'ZRC/USDT:USDT', 'ETHW/USDT:USDT', 'CTK/USDT:USDT', 'AIOZ/USDT:USDT', 'LTC/USDT:USDT', 'ACE/USDT:USDT', 'TOKEN/USDT:USDT', 'ZEUS/USDT:USDT', 'ME/USDT:USDT', 'ZEN/USDT:USDT', 'AUDIO/USDT:USDT', 'AXL/USDT:USDT', 'CVX/USDT:USDT', 'HOOK/USDT:USDT', 'IOTA/USDT:USDT', '1000BTT/USDT:USDT', 'NULS/USDT:USDT', '1000X/USDT:USDT', 'UXLINK/USDT:USDT', 'LPT/USDT:USDT', 'NMR/USDT:USDT', '1000CAT/USDT:USDT', 'ACX/USDT:USDT', 'SC/USDT:USDT', 'EIGEN/USDT:USDT', 'SXP/USDT:USDT', 'HIVE/USDT:USDT', 'ORCA/USDT:USDT', 'CORE/USDT:USDT', 'ASTR/USDT:USDT', 'CYBER/USDT:USDT', 'TRU/USDT:USDT', '1000TURBO/USDT:USDT', 'SPELL/USDT:USDT', 'MOVE/USDT:USDT', 'MBOX/USDT:USDT', 'QUICK/USDT:USDT', 'UMA/USDT:USDT', 'JST/USDT:USDT', 'BB/USDT:USDT', 'EDU/USDT:USDT', 'METIS/USDT:USDT', 'FIDA/USDT:USDT', 'TAIKO/USDT:USDT', 'BADGER/USDT:USDT', 'SEI/USDT:USDT', 'LEVER/USDT:USDT', 'ARPA/USDT:USDT', 'ID/USDT:USDT', 'BOME/USDT:USDT', 'MOVR/USDT:USDT', 'NKN/USDT:USDT', 'SCRT/USDT:USDT', 'CHESS/USDT:USDT', 'TAO/USDT:USDT', 'JUP/USDT:USDT', 'RLC/USDT:USDT', 'AERGO/USDT:USDT', '1000000MOG/USDT:USDT', 'SUN/USDT:USDT', 'VIRTUAL/USDT:USDT', 'BAND/USDT:USDT', 'ARKM/USDT:USDT', 'HFT/USDT:USDT', 'BRETT/USDT:USDT', 'DOGS/USDT:USDT', 'VOXEL/USDT:USDT', 'TRUMP/USDT:USDT', 'SCR/USDT:USDT', 'T/USDT:USDT', 'PYTH/USDT:USDT', 'GTC/USDT:USDT', 'POLYX/USDT:USDT', 'HIGH/USDT:USDT', 'SFP/USDT:USDT', 'COTI/USDT:USDT', 'CFX/USDT:USDT', 'SYN/USDT:USDT', 'XVG/USDT:USDT', 'DODO/USDT:USDT', 'RPL/USDT:USDT', 'SPX/USDT:USDT', 'HIFI/USDT:USDT', 'OXT/USDT:USDT', 'VRA/USDT:USDT', 'NTRN/USDT:USDT', 'HBAR/USDT:USDT', 'ALPHA/USDT:USDT', 'OMNI/USDT:USDT', 'CELR/USDT:USDT', 'CETUS/USDT:USDT', 'ZETA/USDT:USDT', 'POWR/USDT:USDT', 'SPEC/USDT:USDT', 'SSV/USDT:USDT', 'NFP/USDT:USDT', 'OP/USDT:USDT', 'FLUX/USDT:USDT', 'PORTAL/USDT:USDT', 'OGN/USDT:USDT', 'MTL/USDT:USDT', 'SAGA/USDT:USDT', 'DYM/USDT:USDT', '1000FLOKI/USDT:USDT', 'DENT/USDT:USDT', 'TLM/USDT:USDT', 'MAV/USDT:USDT', 'VANRY/USDT:USDT', 'MEME/USDT:USDT', 'ATA/USDT:USDT', 'LDO/USDT:USDT', 'QI/USDT:USDT', 'AEVO/USDT:USDT', 'KAS/USDT:USDT', 'PERP/USDT:USDT', 'DUSK/USDT:USDT', 'KAIA/USDT:USDT', 'BNT/USDT:USDT', 'ADA/USDT:USDT', 'VTHO/USDT:USDT', 'FLM/USDT:USDT', 'PHA/USDT:USDT', 'SLP/USDT:USDT', 'AKT/USDT:USDT', 'BANANA/USDT:USDT', 'PHB/USDT:USDT', 'LSK/USDT:USDT', 'JOE/USDT:USDT', '1000LUNC/USDT:USDT', 'C98/USDT:USDT', 'WLD/USDT:USDT', 'ONG/USDT:USDT', 'CHR/USDT:USDT', 'COS/USDT:USDT', 'STG/USDT:USDT', 'MINA/USDT:USDT', 'ALT/USDT:USDT', '1000XEC/USDT:USDT', 'SNX/USDT:USDT', 'RONIN/USDT:USDT', 'VELODROME/USDT:USDT', 'XAI/USDT:USDT', 'GLMR/USDT:USDT', 'BAL/USDT:USDT', 'PIXEL/USDT:USDT', 'SKL/USDT:USDT', 'WAXP/USDT:USDT', 'MANTA/USDT:USDT', 'ILV/USDT:USDT', 'LRC/USDT:USDT', 'HOT/USDT:USDT', 'BEAM/USDT:USDT', 'SYS/USDT:USDT', 'LQTY/USDT:USDT', 'AI/USDT:USDT', 'BICO/USDT:USDT', 'BLUR/USDT:USDT', 'CTSI/USDT:USDT', 'STEEM/USDT:USDT', 'UNI/USDT:USDT', 'ALICE/USDT:USDT', 'RIF/USDT:USDT', 'AR/USDT:USDT', 'IMX/USDT:USDT', 'ANKR/USDT:USDT', 'GMX/USDT:USDT', 'IO/USDT:USDT', 'SLERF/USDT:USDT', 'MEW/USDT:USDT', 'EGLD/USDT:USDT', 'YFI/USDT:USDT', 'DYDX/USDT:USDT', 'BCH/USDT:USDT', 'APE/USDT:USDT', 'KSM/USDT:USDT', 'QTUM/USDT:USDT', 'BAT/USDT:USDT', 'BSV/USDT:USDT', 'WOO/USDT:USDT', 'INJ/USDT:USDT', 'XLM/USDT:USDT', 'KDA/USDT:USDT', 'ENJ/USDT:USDT', 'W/USDT:USDT', '1000PEPE/USDT:USDT', 'ROSE/USDT:USDT', 'KNC/USDT:USDT', 'ONE/USDT:USDT', 'DASH/USDT:USDT', 'STRK/USDT:USDT', 'ETHFI/USDT:USDT', 'SUI/USDT:USDT', 'GAS/USDT:USDT', 'SUSHI/USDT:USDT', 'LUNA2/USDT:USDT', 'IOST/USDT:USDT', 'ICX/USDT:USDT', '1000BONK/USDT:USDT', 'CHZ/USDT:USDT', 'MAGIC/USDT:USDT', 'ZRX/USDT:USDT', 'SUPER/USDT:USDT', 'SOL/USDT:USDT', 'ZK/USDT:USDT', 'COMP/USDT:USDT', 'ONT/USDT:USDT', 'AERO/USDT:USDT', 'NEAR/USDT:USDT', '1000000BABYDOGE/USDT:USDT', 'ZRO/USDT:USDT', 'ALGO/USDT:USDT', 'CRV/USDT:USDT', 'TON/USDT:USDT', 'BNB/USDT:USDT', 'RVN/USDT:USDT', 'GMT/USDT:USDT', 'STX/USDT:USDT', 'PENDLE/USDT:USDT', 'CELO/USDT:USDT', 'XRP/USDT:USDT', 'ZIL/USDT:USDT', 'THETA/USDT:USDT', 'ENS/USDT:USDT', 'ETC/USDT:USDT', 'ATOM/USDT:USDT', 'DOT/USDT:USDT', 'GALA/USDT:USDT', 'AAVE/USDT:USDT', 'ARB/USDT:USDT', 'MANA/USDT:USDT', 'JASMY/USDT:USDT', 'ONDO/USDT:USDT', 'AXS/USDT:USDT', 'SAND/USDT:USDT', 'SHIB1000/USDT:USDT', 'XTZ/USDT:USDT', 'VET/USDT:USDT', 'FIL/USDT:USDT', '1INCH/USDT:USDT', 'RSR/USDT:USDT', 'GRT/USDT:USDT', 'POL/USDT:USDT', 'NEO/USDT:USDT', 'EOS/USDT:USDT', 'FLOW/USDT:USDT', 'ICP/USDT:USDT', 'DOGE/USDT:USDT', 'RENDER/USDT:USDT', 'LINK/USDT:USDT', 'AVAX/USDT:USDT', 'ETH/USDT:USDT', 'BTC/USDT:USDT']


def get_initialData(path):
    if os.path.exists(path + 'open' + '.pkl'):
        Open = pd.read_pickle(path + 'open' + '.pkl').replace({None: np.nan}).iloc[:-6]
        High = pd.read_pickle(path + 'high' + '.pkl').replace({None: np.nan}).iloc[:-6]
        Low = pd.read_pickle(path + 'low' + '.pkl').replace({None: np.nan}).iloc[:-6]
        Close = pd.read_pickle(path + 'close' + '.pkl').replace({None: np.nan}).iloc[:-6]
        Volume = pd.read_pickle(path + 'volume' + '.pkl').replace({None: np.nan}).iloc[:-6]
        usdt_contract = list(Volume)
        timeToBeUpdate = get_timedelta(get_nowISO(),timeConvert1(Open.index[-1]))
    else:
        usdt_contracts = get_contracts()  
        Open = pd.DataFrame(columns = usdt_contracts )
        High = pd.DataFrame(columns = usdt_contracts )
        Low = pd.DataFrame(columns = usdt_contracts )
        Close = pd.DataFrame(columns = usdt_contracts )
        Volume = pd.DataFrame(columns = usdt_contracts )
        timeToBeUpdate = 3999
    return  Open, High, Low, Close, Volume, timeToBeUpdate


def fetch_all_ohlcv(exchange, symbol, timeframe, since, limit=1000):
    """
    分页获取 Bybit 的历史 K 线数据
    :param symbol: 交易对，例如 'BTC/USDT'
    :param timeframe: 时间周期，例如 '1m', '5m', '1h', '1d'
    :param since: 起始时间，Unix 时间戳（毫秒）
    :param limit: 每次获取的最大数据量，默认为 1000
    :return: 所有历史 K 线数据（列表形式）
    """
    all_data = []
    while True:
        # 获取 K 线数据
        ohlcv = exchange.fetch_ohlcv(symbol, timeframe, since=since, limit=limit)
        if not ohlcv:
            break  # 如果没有数据，退出循环

        all_data.extend(ohlcv)  # 添加数据到结果列表
        since = ohlcv[-1][0]    # 更新起始时间为最后一条数据的时间戳 + 1 毫秒

        # 如果获取的数据量少于 limit，说明已经获取完所有数据
        if len(ohlcv) < limit:
            break

    return all_data

def fetch_candles2(bybit, symbol,since ,timeframe, limit=1000):
    """
    获取 Bybit 4 小时合约数据，并转换为 Pandas DataFrame
    :param bybit: 初始化后的 Bybit 实例
    :param symbol: 交易对，例如 'BTC/USDT'
    :param limit: 返回的K线数量
    :return: Pandas DataFrame 格式的 K 线数据
    """
    try:
        candles = fetch_all_ohlcv(bybit, symbol, timeframe, since)
        # candles = bybit.fetch_ohlcv(symbol, timeframe, limit=limit)
        # 转换为 Pandas DataFrame
        df = pd.DataFrame(candles, columns=['timestamp', 'open', 'high', 'low', 'close', 'volume'])
        # 将时间戳转换为可读时间格式，并设置为索引
        df['timestamp'] = pd.to_datetime(df['timestamp'], unit='ms')  # 转换为 datetime
        df.set_index('timestamp', inplace=True)  # 设置为索引
        df = df[~df.index.duplicated(keep='first')]
        return df
    except Exception as e:
        print(f"获取 K 线数据时出错: {e}")
        return None
    
    
def download_dt(bybit,since,timeframe,Open, High, Low, Close, Volume):
    candles = fetch_candles2(bybit, 'BTC/USDT:USDT',since ,timeframe, 1000)
    Open_up = pd.DataFrame(0, columns = Open.columns , index = candles.index)
    High_up = pd.DataFrame(0, columns = Open.columns , index = candles.index)
    Low_up = pd.DataFrame(0, columns = Open.columns , index = candles.index)
    Close_up = pd.DataFrame(0, columns = Open.columns  , index = candles.index)
    Volume_up = pd.DataFrame(0, columns = Open.columns  , index = candles.index)
    
    a = 0
    for symbol in Open.columns:
        a = a + 1
        try:
            candles = fetch_candles2(bybit, symbol,since ,timeframe, 1000)
            # print(a , symbol )
            Open_up[symbol] = candles['open']
            High_up[symbol] = candles['high']
            Low_up[symbol] = candles['low']
            Close_up[symbol] = candles['close']
            Volume_up[symbol] = candles['volume']
        except:
            print(symbol,'no exist!')
            
    Open_up = Open_up.replace({None: np.nan})
    High_up = High_up.replace({None: np.nan})
    Low_up = Low_up.replace({None: np.nan})
    Close_up = Close_up.replace({None: np.nan})
    Volume_up = Volume_up.replace({None: np.nan})
    
    Open = pd.concat([Open, Open_up], axis = 0)
    High = pd.concat([High, High_up], axis = 0)
    Low = pd.concat([Low, Low_up], axis = 0)
    Close = pd.concat([Close, Close_up], axis = 0)
    Volume = pd.concat([Volume, Volume_up], axis = 0)
    
    Open = Open[~Open.index.duplicated()]
    High = High[~High.index.duplicated()]
    Low = Low[~Low.index.duplicated()]
    Close = Close[~Close.index.duplicated()]
    Volume = Volume[~Volume.index.duplicated()]
    return Open, High, Low, Close, Volume

def download_dt_par(bybit,since,timeframe,Open, High, Low, Close, Volume):
    candles = fetch_candles2(bybit, 'BTC/USDT:USDT',since ,timeframe, 1000)
    Open_up = pd.DataFrame(0, columns = Open.columns , index = candles.index)
    High_up = pd.DataFrame(0, columns = Open.columns , index = candles.index)
    Low_up = pd.DataFrame(0, columns = Open.columns , index = candles.index)
    Close_up = pd.DataFrame(0, columns = Open.columns  , index = candles.index)
    Volume_up = pd.DataFrame(0, columns = Open.columns  , index = candles.index)
    
    a = 0
    for symbol in Open.columns:
        a = a + 1
        try:
            candles = fetch_candles2(bybit, symbol,since ,timeframe, 1000)
            # print(a , symbol )
            Open_up[symbol] = candles['open']
            High_up[symbol] = candles['high']
            Low_up[symbol] = candles['low']
            Close_up[symbol] = candles['close']
            Volume_up[symbol] = candles['volume']
        except:
            print(symbol,'no exist!')
            
    import joblib
    temp_folder = r"/home/<USER>/realTime/Tmp"
    results = joblib.Parallel(n_jobs= 3, temp_folder=temp_folder )(
        joblib.delayed(fetch_candles2)(bybit, symbol,since ,timeframe, 1000) for symbol in Open.columns  )
    

            
    Open_up = Open_up.replace({None: np.nan})
    High_up = High_up.replace({None: np.nan})
    Low_up = Low_up.replace({None: np.nan})
    Close_up = Close_up.replace({None: np.nan})
    Volume_up = Volume_up.replace({None: np.nan})
    
    Open = pd.concat([Open, Open_up], axis = 0)
    High = pd.concat([High, High_up], axis = 0)
    Low = pd.concat([Low, Low_up], axis = 0)
    Close = pd.concat([Close, Close_up], axis = 0)
    Volume = pd.concat([Volume, Volume_up], axis = 0)
    
    Open = Open[~Open.index.duplicated()]
    High = High[~High.index.duplicated()]
    Low = Low[~Low.index.duplicated()]
    Close = Close[~Close.index.duplicated()]
    Volume = Volume[~Volume.index.duplicated()]
    return Open, High, Low, Close, Volume


def clear_data(Open, High, Low, Close, Volume, sd0, end0):
    Open = Open[Open.index >= sd0]
    High = High[High.index >= sd0]
    Low = Low[Low.index>= sd0]
    Close = Close[Close.index>= sd0]
    Volume = Volume[Volume.index>= sd0]

    Open = Open[Open.index<= end0]
    High = High[High.index<= end0]
    Low = Low[Low.index<= end0]
    Close = Close[Close.index<= end0]
    Volume = Volume[Volume.index<= end0]
    
    VWAP = (Open + High+Low+Close) * 4
    Amount = VWAP * Volume
    df = {}
    df['open'],df['high'],df['low'],df['close'],df['volume'], df['amount'], df['vwap']= Open ,High,Low,Close,Volume, Amount, VWAP
    df['totalRet'] = df['close'] / df['close'].shift(1) - 1
    df['totalRet'][df['close'].fillna(0) == 0] = np.nan
    df['totalRet'] = df['totalRet'].replace([np.inf, -np.inf], np.nan) 
    
    for v in df.keys():
        df[v].index = df[v].index.strftime('%Y-%m-%d %H:%M:%S')

    return df


