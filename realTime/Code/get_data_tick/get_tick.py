import ccxt
import os
import pandas as pd
import numpy as np

import warnings
warnings.filterwarnings('ignore')
import time
from datetime import datetime, timezone

import matplotlib.pyplot as plt
import tools2 as tl2

from pathlib import Path
home_dir = str(Path.home())

# 初始化 Bybit 交易所
def initialize_bybit(api_key, secret):
    """
    初始化 Bybit 实例
    :param api_key: Bybit API 密钥
    :param secret: Bybit API 密钥
    :return: 初始化后的 Bybit 实例
    """
    bybit = ccxt.bybit({
        'apiKey': api_key,
        'secret': secret,
        'options': {
            'defaultType': 'future',  # 如果是合约交易，设置为 'future'
        },
    })
    return bybit


# 市价下单函数
def place_market_order(bybit, symbol, side, amount):
    """
    市价下单
    :param bybit: 初始化后的 Bybit 实例
    :param symbol: 交易对，例如 'BTC/USDT'
    :param side: 买入或卖出，'buy' 或 'sell'
    :param amount: 下单数量
    :return: 下单结果
    """
    try:
        order = bybit.create_order(
            symbol=symbol,
            type='market',
            side=side,
            amount=amount
        )
        print(f"市价下单成功: {order}")
        return order
    except Exception as e:
        print(f"市价下单失败: {e}")
        return None


# 获取持仓信息函数
def fetch_positions(bybit):
    """
    获取用户当前的持仓信息
    :param bybit: 初始化后的 Bybit 实例
    :return: 持仓信息列表
    """
    try:
        # 调用 Bybit 的合约账户持仓接口
        positions = bybit.get_positions()['result']
        # 过滤出有仓位的合约（未平仓的持仓）
        active_positions = [
            position for position in positions if float(position['size']) != 0
        ]
        # 格式化持仓信息
        formatted_positions = []
        for position in active_positions:
            formatted_positions.append({
                'symbol': position['symbol'],                  # 交易对（如 BTCUSDT）
                'positionAmt': float(position['size']),        # 持仓数量（正数为多仓，负数为空仓）
                'entryPrice': float(position['entry_price']),  # 开仓价格
                'unrealizedProfit': float(position['unrealised_pnl']),  # 未实现盈亏
                'leverage': int(position['leverage']),         # 杠杆倍数
                'marginType': position['position_margin_mode'], # 保证金类型（cross 或 isolated）
                'liquidationPrice': float(position['liq_price']),  # 强平价格
            })
        return formatted_positions
    except Exception as e:
        print(f"获取持仓信息失败: {e}")
        return None


def get_usdt_contracts_by_ccxt():
    # 初始化 Bybit 交易所
    exchange = ccxt.bybit()

    # 获取所有市场信息
    markets = exchange.load_markets()

    # 筛选以 USDT 为计价单位的合约
    usdt_contracts = [symbol for symbol in markets if symbol.endswith("USDT")]

    return usdt_contracts


def calculate_fitness9_neut(_expr3, df ,rate_ave, rate, delayNum, universe, barraFactor):
    _expr = _expr3
    f = eval(_expr) 
    f = f.replace([np.inf, -np.inf], np.nan)
    f[universe == 0] = np.nan
    f = of.pn_TransNorm(f)
    f_up,f_dn = get_portFromFactor_both(f, 50 )       # 获取多空组合 待优化（）减少换手
    f = f_up + f_dn

    rets = (f.shift(delayNum) * rate).sum(1)
    # rets[:100] = np.nan
    rets_up = (f_up.shift(delayNum) * rate).sum(1) - rate_ave
    rets_dn = (f_dn.shift(delayNum) * rate).sum(1) + rate_ave
    rets[:100] = 0
    rets_up[:100] = 0
    rets_dn[:100] = 0
    # print( to,_expr3)
    return (f, f_up, f_dn, rets, rets_up, rets_dn)

def get_ccf_1m_p1_neut(df, toTest1, rate, delayNum, barraFactor, sd, universe,ispic):
    # 算每一个因子值， 计算他们的多空收益
    vals = {}
    vals_up = {}
    vals_dn = {}
    returns = pd.DataFrame()
    returns_up = pd.DataFrame()
    returns_dn = pd.DataFrame()
    rate2 = rate.copy()
    rate2[universe == 0] = np.nan
    rate_ave = rate2.mean(1)
    rate_ave[:10] = np.nan
    population = toTest1
    for v in range(len(population)):
        _expr = population[v]
        # print(_expr)
        vals[_expr], vals_up[_expr] , vals_dn[_expr] , returns[_expr] , returns_up[_expr] , returns_dn[_expr]  = calculate_fitness9_neut(_expr, df, rate_ave, rate, delayNum, universe, barraFactor )
        # '''
        if ispic:
            r = returns[_expr] .copy()
            r = r[r.index >  sd]
            r.index = pd.to_datetime(r.index)
            r2 = returns_up[_expr] .copy()
            r2 = r2[r2.index > sd]
            r2.index = pd.to_datetime(r2.index)
            r3 = returns_dn[_expr] .copy()
            r3 = r3[r3.index > sd]
            r3.index = pd.to_datetime(r3.index)
            
            r.cumsum().plot()
            r2.cumsum().plot()
            r3.cumsum().plot()
            # print(v,'OTS:', round(r2.sum()/(r2.sum() + r3.sum()),3))
            to = (vals[_expr]- vals[_expr].shift(1)).abs().sum(1)
            to = to[to.index >  sd ]
            to = round(to.mean(),3)
            sr = round(r.mean() / r.std() * (365*24*2)**0.5 ,4)
            plt.title(str(v)+'__' + str(sr) +'__'+ str(to))
            plt.show()
            savefigpath = home_dir + '/realTime/Pic/factor/'
            plt.savefig(savefigpath + 'FR_' + str(v)+'_8_' + str(sr) +'__'+ str(to)+ '.png' )
            plt.close()
    return  vals, vals_up, vals_dn, returns, returns_up, returns_dn, universe


def get_ccf_1m_p2_opt(vals, returns,rate, universe, delayNum, is_real_trading,sd,opt1,opt2):
    # 因子组合参数
    config = {}
    config['EDate'] = 5000000000
    if is_real_trading:
        config['SDate'] = len(rate) - 10
    else:
        z = returns[returns.index < sd]
        config['SDate'] = len(z)
    config['cov_method'] = 'ret_cov'
    config['FactorNAmes'] = returns.columns
    config['exRet'] = opt1
    config['TotalRet'] = rate
    config['delayNum'] = delayNum
    config['cost'] = 0.000
    config['fb'] = rate.fillna(0) * 0
    config['get_dire'] = 0
    config['maxWgt'] = 1/len(returns.columns) * 3   # max(1/len(returns.columns) * 3,0.1)
    config['universe'] = universe
    config['num'] = 10
    config['base'] = rate.mean(1)
    config['Nrd']  = 5
    
    config['rolling_window'] = opt2   #合成因子阶段，回看长度1   120 
    config['rolling_window_cov']= opt2  #合成因子阶段，回看长度2  120
    cf_ts , wgt = get_cf6(vals,returns ,config)   # (wgt - wgt.shift(1)).abs().sum(1)

    # cf10  = pn_TransNorm(ts_Decay3(cf_ts,nrd,'exp'))
    cf10  = pn_TransNorm(cf_ts)
    cf10, f_up, f_dn = get_ls_post2(cf10)

    return cf10


def get_lsRet(ls2,ret, costR, delayNum,frq,sd,name = '0'):
    tos2 = (ls2 - ls2.shift(1)).abs().sum(1)
    ls_ret = (ls2.shift(delayNum) * ret).sum(1) - tos2 * costR
    ls_ret = ls_ret[ls_ret.index > sd ]
    # tos2 = (ls2 - ls2.shift(1)).abs().sum(1)
    tos2 = tos2[tos2.index > sd ]
    to = round(tos2.mean(),3)
    sr = round( (ls_ret.mean() / ls_ret.std()) * (365*24*60/frq) ** 0.5,3)
    ar = round( ls_ret.mean() * (365*24*60/frq),3)
    ls_ret.index = pd.to_datetime(ls_ret.index)
    ls_ret.cumsum().plot()
    print(name, str(sr) +'  ' +   str(ar) +'  ' +   str(to))
    plt.title(str(sr) +'  ' +   str(ar) +'  ' +   str(to)) 
    # plt.show()
    savefigpath = home_dir + '/realTime/Pic/'
    plt.savefig(savefigpath  + name + '.png' )
    plt.close()
    return ls_ret


def get_portFromFactor_both(f, num):
    f_ = f.copy()
    f_2 = f_.rank(axis=1,ascending=False,method = 'first')
    hold = f_.fillna(0) * 0
    hold[f_2 <= num] = 1
    hold = hold / of.Repmat(hold,hold.sum(1))
    hold2 = f_.fillna(0) * 0
    hold2[f_2 > of.Repmat(f_2,f_2.max(1)) - num] = 1
    hold2 = hold2 / of.Repmat(hold2,hold2.sum(1)) *-1
    return hold,hold2


def get_snap(df_all_future, frq):
    df_all_future['open'] = df_all_future['open'].shift(frq-1)
    df_all_future['high'] = df_all_future['high'].rolling(frq).max()
    df_all_future['low'] = df_all_future['low'].rolling(frq).min()
    df_all_future['amount'] = df_all_future['amount'].rolling(frq).sum()
    df_all_future['volume'] = df_all_future['volume'].rolling(frq).sum()
    for v in df_all_future.keys():
        df_all_future[v] = df_all_future[v][::frq] 
    df_all_future['vwap'] = df_all_future['amount'] / df_all_future['volume']
    df_all_future['totalRet'] = df_all_future['close'] / df_all_future['close'].shift(1) - 1
    df_all_future['totalRet'][df_all_future['close'].fillna(0) == 0] = np.nan
    df_all_future['totalRet'] = df_all_future['totalRet'].replace([np.inf, -np.inf], np.nan)
    return df_all_future


def get_factorRet(cf_1m,frets,delayNum,sd, name):
    rate2 = frets.copy()
    rate_ave = rate2.mean(1)

    f = cf_1m.copy()
    f, f_up, f_dn = get_ls_post2(f)
    ret = (f.shift(delayNum) * frets).sum(1)
    rets_up = (f_up.shift(delayNum) * frets).sum(1) - rate_ave
    rets_dn = (f_dn.shift(delayNum) * frets).sum(1) + rate_ave
    rs = pd.DataFrame()
    rs['ls'] = ret .copy()
    rs['l'] = rets_up .copy()
    rs['s'] = rets_dn .copy()
    rs = rs[rs.index >  sd ]
    rs.index = pd.to_datetime(rs.index)
    rs.cumsum().plot()
    to = (f - f.shift(1)).abs().sum(1)
    to = to[to.index >  sd ]
    to = to.mean()
    plt.title(str(round(to,3)) + '_' + str(round(rs['ls'].mean()/rs['ls'].std()*(365*24*2)**0.5,2)) )
    plt.show()
    savefigpath = home_dir + '/realTime/Pic/'
    plt.savefig(savefigpath + name + '.png' )
    plt.close()
    print(name, str(round(to,3)) + '_' + str(round(rs['ls'].mean()/rs['ls'].std()*(365*24*2)**0.5,2)))
    return rs
   

def plot_save(rs,name):
    rs.plot()
    plt.title(name)
    plt.show()
    savefigpath = home_dir + '/realTime/Pic/'
    plt.savefig(savefigpath + name + '.png' )
    plt.close()

def tic():
    """记录开始时间"""
    global start_time0
    start_time0 = time.time()

def toc():
    """计算经过的时间并打印结果"""
    if 'start_time' in globals():
        elapsed_time = time.time() - start_time0
        print(f"Elapsed time: {elapsed_time:.4f} seconds")
    else:
        print("Call tic() first to start the timer.")

def ts_Regression2(s1, s2, n, rettype):
    n = int(n)
    if n <= 2:
        n = 3
    tem1 = s1.copy()
    tem2 = s2.copy()
    tem1[tem2.isna()] = np.nan
    tem2[tem1.isna()] = np.nan
    tem1_m = tem1.rolling(n, axis=0, min_periods=1).mean()
    tem2_m = tem2.rolling(n, axis=0, min_periods=1).mean()
    tem_prod_m = (tem1 * tem2).rolling(n, axis=0, min_periods=1).mean()
    tem2_var = tem2.rolling(n, axis=0, min_periods=1).var(ddof=0)
    beta = (tem_prod_m - tem1_m * tem2_m) / tem2_var
    beta = beta.replace([np.inf, -np.inf], np.nan)
    beta[beta < 1] = 1
    beta[beta > 3 ] = 3
    if rettype == 'A':
        return beta
    const = tem1_m - beta * tem2_m
    if rettype == 'B':
        return const
    y_est = const + beta * tem2
    if rettype == 'C':
        return y_est
    resid = tem1 - y_est
    if rettype == 'D':
        return resid

def prepareDt(df,expr):
    
    df_D1 = {}
    for v in df.keys():
        df_D1[v] = df[v].copy().shift(1)  # testing
        
    dff = get_data_feature_fast_fromExpr(df_D1, expr)

    df = get_snap(df, frq)
    df_D1 = get_snap(df_D1, frq)
    
    universe = (df_D1['amount'].rolling(2*24).sum()>3000000) * 1
    universe = universe * (df_D1['close'] > 0)
    for v in df_D1.keys():
        df_D1[v][universe == 0 ] = np.nan
        
    universe2 = df['close'] > 0
    for v in df.keys():
        df[v][universe2 == 0 ] = np.nan    

    base = of.Repmat(df['totalRet'],df['totalRet']['BTC/USDT:USDT'])
    # beta[beta < 0.3] = 0.3
    # beta[beta < 1] = 1
    # beta[beta > 3] = 3
    # IdioRet = df['totalRet'] - beta * base
    # IdioRet = of.ts_Regression(df['totalRet'], base, 48*7, 'D')
    # IdioRet = df['totalRet'] - beta * base - alpha
    IdioRet = ts_Regression2(df['totalRet'], base, 48*7, 'D')
    # alpha = ts_Regression2(df['totalRet'], base, 48*7, 'B')
    # beta = ts_Regression2(df['totalRet'], base, 48*7, 'A')
    
    baseD = of.Repmat(df_D1['totalRet'],df_D1['totalRet']['BTC/USDT:USDT'])
    betaD = of.ts_Regression(df_D1['totalRet'], baseD,  48*7, 'A')
    # betaD[betaD < 0.3] = 0.3
    betaD[betaD < 1] = 1
    # betaD[betaD < 0.5] = 0.5
    betaD[betaD > 3] = 3
    IdioRetD1 = df_D1['totalRet'] - betaD  * baseD
    # IdioRetD1 = ts_Regression2(df_D1['totalRet'], baseD, 48*7, 'D')
            
    for v in dff.keys():
        dff[v] =  dff[v][::frq]
    for v in dff.keys():
        df_D1[v] = dff[v]
    df_D1['IdioRet'] = IdioRetD1
    return df_D1,  df, IdioRet, universe, betaD

def get_total_ccf(df, expr, sd):
    df_D1,  df, IdioRet, universe, betaD = prepareDt(df,expr)
    
    vals, vals_up, vals_dn, returns, returns_up, returns_dn, universe = get_ccf_1m_p1_neut(df_D1, expr, IdioRet, 1, {}, sd, universe,1)
    
    costr = 0.0001
    equal = IdioRet.copy().fillna(0) * 0
    for v in vals.keys():
        tmp = vals[v].copy()
        tmp[universe == 0] = np.nan
        tmp = pn_TransNorm(tmp).fillna(0)
        # tmp = pn_Rank(tmp).fillna(0)
        equal = equal + tmp
    equal[universe == 0] = np.nan
    equal = pn_TransNorm(equal)
    # equal = pn_Rank(equal)
    r3 = get_factorRet(equal,df['totalRet'],1,sd,'0Factor_equal_TT' )
    ls_port_up,ls_port_dn = get_portFromFactor_both(equal, 10)       # 获取多空组合 待优化（）减少换手
    ls_port = ls_port_up + ls_port_dn
    r1 = get_lsRet(ls_port,df['totalRet'], costr,1,frq1*frq,sd,'0_eql_ls_cost')
    ls_port_dn2 = ls_port_dn.copy()
    ls_port_dn2['BTC/USDT:USDT'] = 1
    r1 = get_lsRet(ls_port_dn2,df['totalRet'],costr,1,frq1*frq,sd,'0_eql_lsB_COST')
    ls_port_beta = ls_port / betaD
    r1 = get_lsRet(ls_port_beta,df['totalRet'], costr,1,frq1*frq,sd,'0_eql_ls_cost_Beta')
    s_port_beta = (ls_port_dn / betaD).fillna(0)
    s_port_beta['BTC/USDT:USDT'] = 1
    r1 = get_lsRet(s_port_beta,df['totalRet'], costr,1,frq1*frq,sd,'0_eql_lsB_cost_Beta')
    
    
    cf_1 = get_ccf_1m_p2_opt(vals, returns, IdioRet, universe, 1, 0 ,sd,3,48*7)
    ls_port_up,ls_port_dn = get_portFromFactor_both(cf_1, 10 )       # 获取多空组合 待优化（）减少换手
    ls_port = ls_port_up + ls_port_dn
    r3 = get_factorRet(cf_1,IdioRet,1,sd,'1Factor_mar' )
    r3 = get_factorRet(cf_1,df['totalRet'],1,sd,'1Factor_mar3' )
    r1 = get_lsRet(ls_port,df['totalRet'], costr,1,frq1*frq,sd,'1_MAR_ls_cost')
    ls_port_dn2 = ls_port_dn.copy()
    ls_port_dn2['BTC/USDT:USDT'] = 1
    r1 = get_lsRet(ls_port_dn2,df['totalRet'], costr,1,frq1*frq,sd,'1_MAR_lsB_cost')
    ls_port_beta = ls_port / betaD
    r1 = get_lsRet(ls_port_beta,df['totalRet'], costr,1,frq1*frq,sd,'1_MAR_ls_cost_Beta')
    s_port_beta = ls_port_dn / betaD
    s_port_beta['BTC/USDT:USDT'] = 1
    r1 = get_lsRet(s_port_beta,df['totalRet'], costr,1,frq1*frq,sd,'1_MAR_lsB_cost_Beta')
    return equal, cf_1


def delete_temp_files(temp_folder):
    import os
    try:
        # 遍历目录树
        for root, dirs, files in os.walk(temp_folder, topdown=False):
            for file in files:
                file_path = os.path.join(root, file)
                os.remove(file_path)
                # print(f"Deleted file: {file_path}")
            for dir in dirs:
                dir_path = os.path.join(root, dir)
                os.rmdir(dir_path)
                # print(f"Deleted directory: {dir_path}")
        # print(f"All temporary files and directories in {temp_folder} deleted successfully.")
    except Exception as e:
        print(f"Error deleting temporary files: {e}")
        
def get_last_ccf(df_tmp, expr, sd):
    df_D1,  df, IdioRet, universe, betaD = prepareDt(df_tmp, expr)
    print('get data:')
    toc()
    
    vals, vals_up, vals_dn, returns, returns_up, returns_dn, universe = get_ccf_1m_p1_neut(df_D1, expr, IdioRet, 1, {}, sd, universe, 0)
    print('get factors1:')
    toc()
    cf_1 = get_ccf_1m_p2_opt(vals, returns, IdioRet, universe, 1, 1 ,sd,3,48*7)
    print('get c-mark-factor1:')
    toc()
    
    # rate2 = IdioRet.copy()
    # rate2[universe == 0] = np.nan
    # rate_ave = rate2.mean(1)
    # rate_ave[:10] = np.nan
    # temp_folder = r"/home/<USER>/realTime/Tmp"
    # results = joblib.Parallel(n_jobs= 4, temp_folder=temp_folder )(
    #     joblib.delayed(of.calculate_fitness9_neut_par)(v, df ,rate_ave, IdioRet, 1, universe, expr) for v in expr  )
    # f, f_up, f_dn, rets, rets_up, rets_dn = zip(*results)
    # vals2 = {}
    # returns2 = pd.DataFrame()
    # for v in range(len(expr)):
    #     vals2[expr[v]] = f[v]
    #     returns2[expr[v]] = rets[v]
    # delete_temp_files(temp_folder)
    # print('get factors2:')
    # toc()
    # cf_2 = get_ccf_1m_p2_opt(vals2, returns2, IdioRet, universe, 1, 1 ,sd,3,48*7)
    # print('get c-mark-factor2:')
    # toc()
    

    # equal = IdioRet.copy().fillna(0) * 0
    # for v in vals.keys():
    #     tmp = vals[v].copy()
    #     tmp[universe == 0] = np.nan
    #     tmp = pn_TransNorm(tmp).fillna(0)
    #     equal = equal + tmp
    # equal[universe == 0] = np.nan
    # equal = pn_TransNorm(equal)
    # print('get c-equal-factor:')
    # toc()
    
    return cf_1 


def fetch_trades(bybit,symbol, since , limit=1000):
    """
    获取Bybit逐笔交易数据
    :param symbol: 交易对，如 'BTC/USDT'
    :param limit: 每次请求的最大数量（默认1000）
    :param since: 起始时间戳（毫秒）
    :return: DataFrame格式的交易数据
    """
    try:
        trades = bybit.fetch_trades(symbol, limit=limit, since=since)
        
        # 转换为DataFrame
        df = pd.DataFrame(trades, columns=['timestamp', 'datetime', 'symbol', 'id', 'order', 'price', 'amount', 'side', 'info'])
        
        # 转换时间戳为可读格式
        df['datetime'] = pd.to_datetime(df['datetime'])
        df['timestamp'] = pd.to_datetime(df['timestamp'], unit='ms')
        
        # 添加买卖方向（buy/sell）
        df['side'] = df['side'].apply(lambda x: 'buy' if x == 'buy' else 'sell')
        
        return df
    
    except Exception as e:
        print(f"Error fetching trades: {e}")
        return pd.DataFrame()


def fetch_historical_trades(bybit,symbol, start_time, end_time=None):
    all_trades = []
    since = start_time
    
    while True:
        print(since)
        trades = bybit.fetch_trades(symbol, since=since, limit=1000)
        if not trades:
            break
            
        last_trade = trades[-1]
        all_trades.extend(trades)
        
        # 如果最后一条数据的时间超过 end_time，则停止
        if end_time and last_trade['timestamp'] >= end_time:
            break
            
        # 更新 since 为最后一条数据的时间戳 +1 毫秒
        since = last_trade['timestamp'] + 1
        
        # 避免频繁请求（遵守API速率限制）
        # time.sleep(0.1)
            # 转换为DataFrame
            
    df = pd.DataFrame(all_trades, columns=['timestamp', 'datetime', 'symbol', 'id', 'order', 'price', 'amount', 'side', 'info'])
    
    # 转换时间戳为可读格式
    df['datetime'] = pd.to_datetime(df['datetime'])
    df['timestamp'] = pd.to_datetime(df['timestamp'], unit='ms')
    
    # 添加买卖方向（buy/sell）
    df['side'] = df['side'].apply(lambda x: 'buy' if x == 'buy' else 'sell')

    return df


def fetch_all_tick(exchange, symbol, since, limit=1000):
    print(since)
    all_data = []
    while True:
        # 获取 K 线数据
        ohlcv = exchange.fetch_trades(symbol, since=since, limit=limit)
        if not ohlcv:
            break  # 如果没有数据，退出循环

        all_data.extend(ohlcv)  # 添加数据到结果列表
        since = ohlcv[0]['timestamp']    # 更新起始时间为最后一条数据的时间戳
        
        print(since, len(ohlcv))
        # 如果获取的数据量少于 limit，说明已经获取完所有数据
        if len(ohlcv) < limit:
            break

    return all_data

def fetch_ticks(bybit, symbol,since ,end_time, limit=1000):
    try:
        all_trades = fetch_historical_trades(bybit, symbol, since, end_time)
        # 转换为 Pandas DataFrame
        df = pd.DataFrame(all_trades, columns=['timestamp', 'datetime', 'symbol', 'id', 'order', 'price', 'amount', 'side', 'info'])
    
        # 转换时间戳为可读格式
        df['datetime'] = pd.to_datetime(df['datetime'])
        df['timestamp'] = pd.to_datetime(df['timestamp'], unit='ms')
        # 添加买卖方向（buy/sell）
        df['side'] = df['side'].apply(lambda x: 'buy' if x == 'buy' else 'sell')
        df = df[~df.index.duplicated(keep='first')]
        return df
    except Exception as e:
        print(f"获取 tick 数据时出错: {e}")
        return None


def fetch_historical_trades(bybit, symbol, start_time, end_time):
    """
    获取 Bybit 历史逐笔交易数据（按时间倒序）
    :param symbol: 交易对，如 'BTC/USDT'
    :param start_time: 开始时间（毫秒级时间戳）
    :param end_time: 结束时间（毫秒级时间戳，可选）
    :return: 按时间升序排列的交易数据（从旧到新）
    """
    all_trades = []
    current_end_time = end_time  # 初始 end_time
    
    while True:
        try:
            # 获取数据（默认返回最新数据，但可以用 end_time 限制）
            trades = bybit.fetch_trades(
                symbol,
                since=start_time,  # 起始时间
                limit=1000,        # 每次最多1000条
                params={'end': current_end_time} 
            )
            print
            if not trades:
                break  # 没有更多数据
            
            # 按时间升序存储（因为 Bybit 返回的是倒序）
            all_trades.extend(trades)  # 反转列表
            
            # 更新 end_time 为最早一条数据的时间 -1 毫秒（往前翻页）
            earliest_trade = trades[0]
            current_end_time = earliest_trade['timestamp'] - 1
            
            print(current_end_time,trades[0]['datetime'],trades[-1]['datetime'],len(trades))
            
            # 如果已经爬取到 start_time 之前，停止
            if current_end_time < start_time:
                break
            
            # 避免触发 API 限流
            # time.sleep(0.1)
            
        except Exception as e:
            print(f"Error fetching trades: {e}")
            break
    
    return all_trades
    

# 示例用法
if __name__ == '__main__':
    # Bybit API 密钥
    api_key = '1Vo3MyCK4SJdjRlUay'
    secret = 'n1W9eMAsigOkG04yLXdc3PTPO5BqyQkTjhBc'
    frq = 4
    frq1 = 5
    tic()
    path = home_dir + '/realTime/Data/rt/'
    
    # 初始化 Bybit
    bybit = initialize_bybit(api_key, secret)
    
    
    symbol = 'BTC/USDT:USDT'
    utc_time1 = datetime(2025, 3, 24, 9, 50, 15, tzinfo=timezone.utc)
    since = int(utc_time1.timestamp() * 1000)
    trades_df = fetch_trades(bybit,symbol, since , 1000)
    print(trades_df)
    
    
    tic()
    utc_time1 = datetime(2025, 3, 24, 9, 50, 15, tzinfo=timezone.utc)
    since = int(utc_time1.timestamp() * 1000)
    
    utc_time2 = datetime(2025, 3, 24, 10, 20, 15, tzinfo=timezone.utc)
    end_time = int(utc_time2.timestamp() * 1000)
    print(since)
    # # end_time = int(datetime(2023, 10, 2).timestamp() * 1000
    historical_trades = fetch_ticks(bybit,symbol, since,end_time, 1000)
    toc()

    # # 获取并打印 USDT 合约名称
    # usdt_contracts_current = get_usdt_contracts_by_ccxt()
    
    Open, High, Low, Close, Volume, timeToBeUpdate = tl2.get_initialData(path)

    # since = bybit.parse8601('2025-01-01T00:00:00Z') 
    since = bybit.parse8601(tl2.get_startTime(timeToBeUpdate + 1))
    timeframe = '5m'   
    
    Open, High, Low, Close, Volume = tl2.download_dt(bybit,since,timeframe,Open, High, Low, Close, Volume)
    print('finish raw data')
    toc()

    sd0 = '2025-03-01 00:00:00'
    sd = '2025-03-05 00:00:00'
    end0 = '2025-04-04 00:05:30'
    
    sd0 = '2025-03-01 00:00:00'
    sd = '2025-03-05 00:00:00'
    end0 = '2025-04-04 00:05:30'
    
    df = tl2.clear_data(Open, High, Low, Close, Volume, sd0, end0)
    

    expr =  pd.read_csv(home_dir + '/realTime/Expr/result_pncut.csv')
    # expr =  pd.read_csv(home_dir + '/realTime/Expr/result_pn_regResi.csv')
    expr = list(expr.sort_values(by = 'result',ascending=False).head(50)['expr'])
    
    df_tt = df.copy()
    equal,mark = get_total_ccf(df_tt, expr, sd)
    a = 1
    
    # df_rt = df.copy()    
    # sd_s = '2025-03-06 00:00:00'
    # lookback = 2997 
    # dateline = mark.index
    # dat = dateline[-100]
    # for dat in dateline:
    #     if dat < sd_s:
    #         continue
    #     df_tmp = {}
    #     for ll in df_rt.keys():
    #         df_tmp[ll] = df[ll][df[ll].index <= dat].tail(lookback)
    #         # df_tmp[ll] = df_rt[ll].copy().tail(lookback)
    #     tic()
    #     ccf_rt = get_last_ccf(df_tmp, expr, sd)
    #     toc()
    #     corr1 = round(np.corrcoef(mark.loc[dat] ,ccf_rt.loc[dat] )[0,1],4)
    #     # corr2 = round(np.corrcoef(equal.loc[dat].fillna(0)  ,equal_rt.loc[dat].fillna(0)  )[0,1],4)
    #     print(dat, corr1)


    #     if corr1 < 0.98 :
    #         # print(dat,)
    #         break
    
    # a = 1