import pandas as pd
import numpy as np
import time
import os


def get_minFactor(Open, High, Low, Close, Volume, Amount, VWAP,form):
    # from bn_mf_11.Feature.code.feature_operator_funcs import ts_Corr, ts_Delay, Abs, pn_<PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON>, <PERSON>,ts_<PERSON>, ts_Stdev, ts_Sum, get<PERSON><PERSON>, Sqrt, ts_<PERSON>, ts_<PERSON>, pn_Rank, Equal,Max ,Min, Log, ts_Regression
    # from bn_mf_11.Feature.code.feature_operator_funcs import Tot_Mean, Tot_Sum, Tot_Stdev, Tot_Delta, Tot_Divide, Tot_ChgRate, Tot_Rank, Tot_Min, Tot_Max, Tot_ArgMax, Tot_ArgMin , GetSingleBar
    from feature_operator_funcs import ts_Corr, ts_Delay, Abs, pn_Mean, IfThen, Mthan, And,ts_Mean, ts_Stdev, ts_<PERSON>m, get<PERSON><PERSON>, <PERSON>q<PERSON>, ts_<PERSON>, ts_<PERSON>, pn_<PERSON>, <PERSON>,<PERSON> ,<PERSON>, Log, ts_Regression
    from feature_operator_funcs import Tot_Mean, Tot_Sum, Tot_Stdev, Tot_Delta, Tot_Divide, Tot_ChgRate, Tot_Rank, Tot_Min, Tot_Max, Tot_ArgMax, Tot_ArgMin , GetSingleBar
    tmp = eval(form)
    return tmp


def get_KAMA(close,n):
    # 函数名：KAMA
    # 名称： 考夫曼的自适应移动平均线
    # 简介：短期均线贴近价格走势，灵敏度高，但会有很多噪声，产生虚假信号；长期均线在判断趋势上一般比较准确 ，但是长期均线有着严重滞后的问题。我们想得到这样的均线，当价格沿一个方向快速移动时，短期的移动 平均线是最合适的；当价格在横盘的过程中，长期移动平均线是合适的。
    # NOTE: The KAMA function has an unstable period.
    # df["KAMA"] = talib.KAMA(close_p, timeperiod=30)
    # open = df_all['open']
    from  talib import KAMA
    tmp2 = pd.DataFrame(np.nan, index = close.index, columns = close.columns)
    for v in tmp2.columns:  
        try:
            _tmp = KAMA(close[v],timeperiod=n)
            tmp2[v] = _tmp
        except:
            c = 1
    return tmp2

def get_ADOSC(high, low, close,volume,n1,n2):
    # 函数名：ADOSC
    # 名称：Chaikin A/D Oscillator Chaikin震荡指标
    # 简介：将资金流动情况与价格行为相对比，检测市场中资金流入和流出的情况
    # 计算公式：fastperiod A/D - slowperiod A/D
    # 研判：
    # 1、交易信号是背离：看涨背离做多，看跌背离做空
    # 2、股价与90天移动平均结合，与其他指标结合
    # 3、由正变负卖出，由负变正买进
    # real = ADOSC(high, low, close, volume, fastperiod=3, slowperiod=10)
    # df["ADOSC"] = talib.ADOSC(high, low, close, volume, fastperiod=3, slowperiod=10)
    from  talib import ADOSC
    tmp2 = pd.DataFrame(np.nan, index = close.index, columns = close.columns)
    for v in tmp2.columns:  
        try:
            _tmp = ADOSC(high[v],low[v],close[v], volume[v],  fastperiod=n1, slowperiod=n2)
            tmp2[v] = _tmp
        except:
            c = 1
    return tmp2

def get_HT_DCPERIOD(close):
    # 名称： 希尔伯特变换-主导周期
    # 简介：将价格作为信息信号，计算价格处在的周期的位置，作为择时的依据。
    # NOTE: The HT_DCPERIOD function has an unstable period.
    # real = HT_DCPERIOD(close)
    # df["HT_DCPERIOD"] = talib.HT_DCPERIOD(close)
    from  talib import HT_DCPERIOD
    tmp2 = pd.DataFrame(np.nan, index = close.index, columns = close.columns)
    for v in tmp2.columns:  
        try:
            _tmp = HT_DCPERIOD(close[v])
            tmp2[v] = _tmp
        except:
            c = 1
    return tmp2

def get_HT_DCPHASE(close):
    # 名称： 希尔伯特变换-主导循环阶段
    # NOTE: The HT_DCPHASE function has an unstable period.
    # real = HT_DCPHASE(close)
    # df["HT_DCPHASE"] = talib.HT_DCPHASE(close)
    from  talib import HT_DCPHASE
    tmp2 = pd.DataFrame(np.nan, index = close.index, columns = close.columns)
    for v in tmp2.columns:  
        try:
            _tmp = HT_DCPHASE(close[v])
            tmp2[v] = _tmp
        except:
            c = 1
    return tmp2

def get_CCI(high, low, close,n):
    # CCI - Commodity Channel Index
    # 函数名：CCI
    # 名称：顺势指标
    # 简介：CCI指标专门测量股价是否已超出常态分布范围
    # real = CCI(high, low, close, timeperiod=14)
    # df["CCI"] = talib.CCI(high, low, close, timeperiod=14)
    from  talib import CCI
    tmp2 = pd.DataFrame(np.nan, index = close.index, columns = close.columns)
    for v in tmp2.columns:  
        try:
            _tmp = CCI(high[v], low[v], close[v], timeperiod=n)
            tmp2[v] = _tmp
        except:
            c = 1
    return tmp2

def get_CMO(close,n):
    # 名称：钱德动量摆动指标
    # 简介：与其他动量指标摆动指标如相对强弱指标（RSI）和随机指标（KDJ）不同，钱德动量指标在计算公式的分子中采用上涨日和下跌日的数据。 计算公式：CMO=（Su－Sd）*100/（Su+Sd）
    # 其中：Su是今日收盘价与昨日收盘价（上涨日）差值加总。若当日下跌，则增加值为0；Sd是今日收盘价与做日收盘价（下跌日）差值的绝对值加总。若当日上涨，则增加值为0；
    # 指标应用
    # 本指标类似RSI指标。
    # 当本指标下穿-50水平时是买入信号，上穿+50水平是卖出信号。
    # 钱德动量摆动指标的取值介于-100和100之间。
    # 本指标也能给出良好的背离信号。
    # 当股票价格创出新低而本指标未能创出新低时，出现牛市背离；
    # 当股票价格创出新高而本指标未能创出新高时，当出现熊市背离时。
    # 我们可以用移动均值对该指标进行平滑。
    # NOTE: The CMO function has an unstable period.
    # real = CMO(close, timeperiod=14)
    # df["CMO"] = talib.CMO(close, timeperiod=14)
    from  talib import CMO
    tmp2 = pd.DataFrame(np.nan, index = close.index, columns = close.columns)
    for v in tmp2.columns:  
        try:
            _tmp = CMO(close[v], timeperiod=n)
            tmp2[v] = _tmp
        except:
            c = 1
    return tmp2

def get_DX(high, low, close,n):
    # DX - Directional Movement Index DMI指标又叫动向指标或趋向指标
    # 函数名：DX
    # 名称：动向指标或趋向指标
    # 简介：通过分析股票价格在涨跌过程中买卖双方力量均衡点的变化情况，即多空双方的力量的变化受价格波动的影响而发生由均衡到失衡的循环过程，从而提供对趋势判断依据的一种技术指标。
    # 分析和应用：百度百科 维基百科 同花顺学院
    # NOTE: The DX function has an unstable period.
    # real = DX(high, low, close, timeperiod=14)
    # df["DX"] = talib.DX(high, low, close, timeperiod=14)
    from  talib import DX
    tmp2 = pd.DataFrame(np.nan, index = close.index, columns = close.columns)
    for v in tmp2.columns:  
        try:
            _tmp = DX(high[v], low[v], close[v], timeperiod=n)
            tmp2[v] = _tmp
        except:
            c = 1
    return tmp2

def get_MINUS_DI(high, low, close,n):
    # MINUS_DI - Minus Directional Indicator
    # 函数名：DMI 中的DI指标 负方向指标
    # 名称：下升动向值
    # 简介：通过分析股票价格在涨跌过程中买卖双方力量均衡点的变化情况，即多空双方的力量的变化受价格波动的影响而发生由均衡到失衡的循环过程，从而提供对趋势判断依据的一种技术指标。
    # real = MINUS_DI(high, low, close, timeperiod=14)
    # df["MINUS_DI"] = talib.MINUS_DI(high, low, close, timeperiod=14)
    from  talib import MINUS_DI
    tmp2 = pd.DataFrame(np.nan, index = close.index, columns = close.columns)
    for v in tmp2.columns:  
        try:
            _tmp = MINUS_DI(high[v], low[v], close[v], timeperiod=n)
            tmp2[v] = _tmp
        except:
            c = 1
    return tmp2

def get_MINUS_DM(high, low, close,n):
    # MINUS_DM - Minus Directional Movement
    # 函数名：MINUS_DM
    # 名称： 上升动向值 DMI中的DM代表正趋向变动值即上升动向值
    # 简介：通过分析股票价格在涨跌过程中买卖双方力量均衡点的变化情况，即多空双方的力量的变化受价格波动的影响而发生由均衡到失衡的循环过程，从而提供对趋势判断依据的一种技术指标。
    # 分析和应用：百度百科 维基百科 同花顺学院
    # NOTE: The MINUS_DM function has an unstable period.
    # real = MINUS_DM(high, low, timeperiod=14)
    # df["MINUS_DM"] = talib.MINUS_DM(high, low, timeperiod=14)
    from  talib import MINUS_DM
    tmp2 = pd.DataFrame(np.nan, index = close.index, columns = close.columns)
    for v in tmp2.columns:  
        try:
            _tmp = MINUS_DM(high[v], low[v], timeperiod=n)
            tmp2[v] = _tmp
        except:
            c = 1
    return tmp2

def get_ULTOSC(high, low, close,n1,n2,n3):
    # ULTOSC - Ultimate Oscillator 终极波动指标
    # 函数名：ULTOSC
    # 名称：终极波动指标
    # 简介：UOS是一种多方位功能的指标，除了趋势确认及超买超卖方面的作用之外，它的“突破”讯号不仅可以提供最适当的交易时机之外，更可以进一步加强指标的可靠度。
    # 分析和应用：百度百科 同花顺学院
    # real = ULTOSC(high, low, close, timeperiod1=7, timeperiod2=14, timeperiod3=28)
    # df["ULTOSC"] = talib.ULTOSC(high, low, close, timeperiod1=7, timeperiod2=14, timeperiod3=28)
    from  talib import ULTOSC
    tmp2 = pd.DataFrame(np.nan, index = close.index, columns = close.columns)
    for v in tmp2.columns:  
        try:
            _tmp = ULTOSC(high[v], low[v], close[v], timeperiod1=n1, timeperiod2=n2, timeperiod3=n3)
            tmp2[v] = _tmp
        except:
            c = 1
    return tmp2

def get_LINEARREG_ANGLE(close,n):
    # LINEARREG_ANGLE - Linear Regression Angle
    # 函数名：LINEARREG_ANGLE
    # 名称：线性回归的角度
    # 简介：来确定价格的角度变化. 参考
    # real = LINEARREG_ANGLE(close, timeperiod=14)
    # df["LINEARREG_ANGLE"] = talib.LINEARREG_ANGLE(close, timeperiod=14)
    from  talib import LINEARREG_ANGLE
    tmp2 = pd.DataFrame(np.nan, index = close.index, columns = close.columns)
    for v in tmp2.columns:  
        try:
            _tmp = LINEARREG_ANGLE(close[v], timeperiod=n)
            tmp2[v] = _tmp
        except:
            c = 1
    return tmp2

def get_LINEARREG_SLOPE(close,n):
    # LINEARREG_SLOPE - Linear Regression Slope
    # 函数名：LINEARREG_SLOPE
    # 名称：线性回归斜率指标
    # real = LINEARREG_SLOPE(close, timeperiod=14)
    # df["LINEARREG_SLOPE"] = talib.LINEARREG_SLOPE(close, timeperiod=14)
    from talib import LINEARREG_SLOPE
    tmp2 = pd.DataFrame(np.nan, index = close.index, columns = close.columns)
    for v in tmp2.columns:  
        try:
            _tmp = LINEARREG_SLOPE(close[v], timeperiod=n)
            tmp2[v] = _tmp
        except:
            c = 1
    return tmp2




def get_Feature_par(df_all,features_table, dataPath_future_feature , temp_folder,isSave, isOut):
    # features1 = {}
    # for v in range(len(Forms)):
    #     start_time = time.time()
    #     expr = Forms['forms'].values[v]
    #     fm = Forms['fname'].values[v]
    #     if fm in need:
    #         tmp = get_minFactor(df_all['open'], df_all['high'],df_all['low'],df_all['close'], df_all['volume'], df_all['amount'], df_all['vwap'], expr)
    #         print( v , "toc: {:.2f} s".format(time.time() -  start_time), expr)
    #         # features1[fm] = tmp
    #         pd.to_pickle(tmp,dataPath_future_feature + fm + '.pkl')
    def funs(v, df_all ,features_table,isSave, isOut ):
        start_time = time.time()
        expr = features_table['forms'].values[v]
        fm = features_table['fname'].values[v]

        tmp = get_minFactor(df_all['open'], df_all['high'],df_all['low'],df_all['close'], df_all['volume'], df_all['amount'], df_all['vwap'], expr)
        # print( v , "toc: {:.2f} s".format(time.time() -  start_time), expr)

        if isSave:
            pd.to_pickle(tmp,dataPath_future_feature + fm + '.pkl')
        if isOut:
            return tmp
        else:
            return 0
    import joblib
    # results = joblib.Parallel(n_jobs= os.cpu_count() - 1, temp_folder=temp_folder )(  
    #             joblib.delayed(funs)(v,df_all, features_table ,isSave, isOut) for v in range(len(features_table)) )
    results = joblib.Parallel(n_jobs= os.cpu_count() , temp_folder=temp_folder )(
                 joblib.delayed(funs)(v,df_all, features_table ,isSave, isOut) for v in range(len(features_table)) )
    if isOut:
        vals2 = {}
        for v in range(len(features_table)):
            vals2[features_table['fname'].values[v]] = results[v]
        return vals2
    else:
        return {}


def get_data_feature(df_all_future, need, dataPath_future_feature, temp_folder, feature_folder, isSave, isOut): 
    # 原始的feature
    features_table = pd.read_csv(feature_folder,index_col = 0)
    features_table = features_table[features_table['fname'].isin(need)]
    F_feature = get_Feature_par(df_all_future,features_table, dataPath_future_feature , temp_folder,isSave, isOut)
    return F_feature


def get_data_feature_fromExpr(df_all_future, expr, dataPath_future_feature, temp_folder, feature_folder, isSave, isOut): 
    features_table = pd.read_csv(feature_folder,index_col = 0)
    features_names = list(features_table['fname'])
    need = []
    for v in features_names:
        for vv in expr:
            if v in vv:
               need.append(v) 
               break
    print('feature need: len',len(need),'for' , str(len(expr)),'factor.')
    F_feature = get_Feature_par(df_all_future,features_table, dataPath_future_feature , temp_folder,isSave, isOut)
    return F_feature



if __name__=="__main__":
    get_data_feature()

