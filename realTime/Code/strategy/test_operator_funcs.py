
"""
算子的具体函数实现

"""

import pandas as pd
import numpy as np
from numba import njit, prange
from scipy.stats import norm
# import rolling_quantiles as rq
from joblib import Parallel, delayed
import os

import warnings
warnings.filterwarnings("ignore")


def parse_expression(formula, expr=''):
    if len(formula) == 5:
        expr += '{0}({1},{2},{3},{4})'.format(formula[0], parse_expression(formula[1], expr), parse_expression(formula[2], expr), parse_expression(formula[3], expr), parse_expression(formula[4], expr))
    elif len(formula) == 4:
        expr += '{0}({1},{2},{3})'.format(formula[0], parse_expression(formula[1], expr), parse_expression(formula[2], expr), parse_expression(formula[3], expr))
    elif len(formula) == 3:
        expr += '{0}({1},{2})'.format(formula[0], parse_expression(formula[1], expr), parse_expression(formula[2], expr))
    elif len(formula) == 2:
        expr += '{0}({1})'.format(formula[0], parse_expression(formula[1], expr))
    elif len(formula) == 1:
        if is_number(formula[0]):
            expr += '{0}'.format(formula[0])
        elif formula[0] in ['A', 'B', 'C', 'D']:
            expr += "'{0}'".format(formula[0])
        else:
            expr += "df['{0}']".format(formula[0])
    return expr


def get_dfs(expr):
    es = []
    for v in expr.split("df['"):
        if "']" in v:
            es.append(v.split("']")[0])
    return es

def calc(df, expr_list):
    expr = parse_expression(expr_list)
    # print(expr)
    try:
        res = pd.eval(expr, engine='python', target=df)
    except:
        print('计算失败: [', expr, ']')
        # es = get_dfs(expr)
        res = df[list(df.keys())[0]].copy().fillna(0) * 0
    return res

def calc2(df, expr):
    try:
        res = pd.eval(expr, engine='python', target=df)
    except:
        print('计算失败: [', expr, ']')
        # es = get_dfs(expr)
        res = df[list(df.keys())[0]].copy().fillna(0) * 0
    return res




# 判断你是不是数字
def is_number(s):
    try:
        float(s)
        return True
    except ValueError:
        pass
    try:
        import unicodedata
        unicodedata.numeric(s)
        return True
    except (TypeError, ValueError):
        pass
    return False


def Add(s1, s2):
    return s1+s2

def Minus(s1, s2):
    return s1-s2

def Multiply(s1, s2):
    return s1*s2

def Divide(s1, s2):
    return s1/s2

def Sqrt(s1):
    return s1**0.5

def log(s1):
    s2 = s1.copy()
    s2[s2 == 0] = np.nan
    return np.log(s1)

def inv(s1):
    return 1/s1

def Floor( data: any) -> any:
    '''
    取矩陣或數的無條件捨去值。
    input:
        data: int、float、pd.DataFrame、np.array, numeric 1-d or 2-d(M * N)

    output:
        newData: int、float、pd.DataFrame、np.array(depend on dtype of input), numeric 1-d or 2-d(M * N)
    '''
    return np.floor(data)

def Ceil( data: any) -> any:
    '''
    取矩陣或數的無條件進位值。
    input:
        data: int、float、pd.DataFrame、np.array, numeric 1-d or 2-d(M * N)

    output:
        newData: int、float、pd.DataFrame、np.array(depend on dtype of input), numeric 1-d or 2-d(M * N)
    '''
    return np.floor(data)

def Round( data: any) -> any:
    '''
    取矩陣或數的四捨五入值。
    input:
        data: int、float、pd.DataFrame、np.array, numeric 1-d or 2-d(M * N)

    output:
        newData: int、float、pd.DataFrame、np.array(depend on dtype of input), numeric 1-d or 2-d(M * N)
    '''
    return np.round(data)

def FilterInf( s1: any) -> any:
    '''
    將矩陣中Inf改為nan。
    input:
        data: pd.DataFrame、np.array, numeric 2-d, M * N

    output:
        newData: pd.DataFrame、np.array(depend on dtype of input), numeric 2-d, M * N
    '''
    data = s1.copy()
    data.replace(np.inf, np.nan, inplace=True)
    return data

def FillNan(data: any, fillData_: any) -> any:
    '''
    將data矩陣中的nan值替換為fillData對應位置的值。
    input:
        data: pd.DataFrame、np.array, numeric 2-d, M * N
        fillData: int、float、pd.DataFrame、np.array, numeric 1-d or 2-d(M * N)
    output:
        newData: pd.DataFrame、np.array(depend on dtype of data), numeric 2-d, M * N
    '''
    newData = data.copy()
    fillData = fillData_.copy()
    if (type(fillData) == int) | (type(fillData) == float):
        newData[np.isnan(newData)] = fillData
    else:
        assert data.shape == fillData.shape, 'Shape Mismatch!'
        isNpArray_1 = isinstance(data, np.ndarray)
        isNpArray_2 = isinstance(fillData, np.ndarray)
        if (isNpArray_1) & (not isNpArray_2):
            fillData = fillData.values
        elif (not isNpArray_1) & (isNpArray_2):
            fillData = pd.DataFrame(fillData, index=data.index, columns=data.columns)
        newData[np.isnan(newData)] = fillData[np.isnan(newData)]
    return newData

def getNan(s1: any) -> any:
    '''
    取得與data矩陣同大小的nan矩陣。
    input:
        data: pd.DataFrame、np.array, numeric 2-d, M * N
    output:
        newData: pd.DataFrame、np.array(depend on dtype of input), numeric 2-d, M * N
    '''
    data = s1.copy()
    isNpArray = isinstance(data, np.ndarray)
    if isNpArray:
        return np.full(data.shape, np.nan)
    else:
        return pd.DataFrame(np.full(data.shape, np.nan), index=data.index, columns=data.columns)

def getInf(s1: any) -> any:
    '''
    取得與data矩陣同大小的inf矩陣。
    input:
        data: pd.DataFrame、np.array, numeric 2-d, M * N
    output:
        newData: pd.DataFrame、np.array(depend on dtype of input), numeric 2-d, M * N
    '''
    data = s1.copy()
    isNpArray = isinstance(data, np.ndarray)
    if isNpArray:
        return np.full(data.shape, np.inf)
    else:
        return pd.DataFrame(np.full(data.shape, np.inf), index=data.index, columns=data.columns)

def SignedPower(s1 , Power) :
    '''
    取得帶正負號的次方值。
    input:
        data: pd.DataFrame、np.array, numeric 2-d, M * N
        Power: int、float, numeric 1-d
    output:
        newData: pd.DataFrame、np.array(depend on dtype of input), numeric 2-d, M * N
    '''
    data = s1.copy()
    negFilter = data < 0
    newData = abs(data) ** Power
    newData[negFilter] = -newData[negFilter]
    return newData

def Abs(data: any) -> any:
    '''
    取得data的絕對值。
    input:
        data: int、float、pd.DataFrame、np.array, numeric 1-d or 2-d(M * N)
    output:
        newData: int、float、pd.DataFrame、np.array(depend on dtype of input), numeric 1-d or 2-d(M * N)
    '''
    return abs(data)

def Log(data: any) -> any:
    '''
    取得data矩陣的log值。
    input:
        data: pd.DataFrame、np.array, numeric 2-d, M * N
    output:
        newData: pd.DataFrame、np.array(depend on dtype of input), numeric 2-d, M * N
    '''
    newData = data.copy()
    newData[newData <= 0] = np.nan
    return np.log(newData)

def Sign(data: any) -> any:
    '''
    取得data矩陣的正負號，正號 = 1，負號 = -1。
    input:
        data: int、float、pd.DataFrame、np.array, numeric 1-d or 2-d(M * N)
    output:
        newData: int、float、pd.DataFrame、np.array(depend on dtype of input), numeric 1-d or 2-d(M * N)
    '''
    return np.sign(data*1)

def Reverse(data: any) -> any:
    '''
    將data矩陣數值取負。
    input:
        data: int、float、pd.DataFrame、np.array, numeric 1-d or 2-d(M * N)
    output:
        newData: int、float、pd.DataFrame、np.array(depend on dtype of input), numeric 1-d or 2-d(M * N)
    '''
    return data * -1

def Power(s1, n):
    n = int(n)
    return np.power(s1, n)

def Exp(data: any) -> any:
    '''
    取得data的自然指數值。
    input:
        data: int、float、pd.DataFrame、np.array, numeric 1-d or 2-d(M * N)
    output:
        newData: int、float、pd.DataFrame、np.array(depend on dtype of data), numeric 1-d or 2-d(M * N)
    '''
    return np.exp(data)

def Softsign(data: any) -> any:
    '''
    data矩陣除以 (1 + data開根號後的絕對值)。
    input:
        data: pd.DataFrame、np.array, numeric 2-d, M * N
    output:
        newData: pd.DataFrame、np.array(depend on dtype of data), numeric 2-d, M * N
    '''
    return Divide(data, (1 + Abs(Sqrt(data))))


def IfThen(s1: any, fillData1: any, fillData2: any) -> any:
    '''
    以data元素值大於0為判斷，若True則取fillData1值，若False則取fillData2值。
    input:
        data: pd.DataFrame、np.array, numeric 2-d, M * N
        fillData1: int、float、pd.DataFrame、np.array, numeric 1-d or 2-d(M * N)
        fillData2: int、float、pd.DataFrame、np.array, numeric 1-d or 2-d(M * N)
    output:
        newData: pd.DataFrame、np.array(depend on dtype of data), numeric 2-d, M * N
    '''
    data = s1.copy()
    isPdFrame = isinstance(data, pd.DataFrame)
    newData = np.full(data.shape, np.nan)
    flag = (data > 0)

    if (type(fillData1) == int) | (type(fillData1) == float):
        newData[flag] = fillData1
    else:
        assert newData.shape == fillData1.shape, 'Shape Mismatch!'
        isPdFrame_1 = isinstance(fillData1, pd.DataFrame)
        if isPdFrame_1:
            fillData1 = fillData1.values

        newData[flag] = fillData1[flag]

    if (type(fillData2) == int) | (type(fillData2) == float):
        newData[~flag] = fillData2
    else:
        assert newData.shape == fillData2.shape, 'Shape Mismatch!'
        isPdFrame_2 = isinstance(fillData2, pd.DataFrame)
        if isPdFrame_2:
            fillData2 = fillData2.values

        newData[~flag] = fillData2[~flag]

    newData[np.isnan(data)] = np.nan

    if isPdFrame:
        newData = pd.DataFrame(newData, index=data.index, columns=data.columns)

    return newData

def And(s1: any, s2: any) -> any:
    '''
    若data1元素值與data2元素值皆不為0，則為True；其他情況都為False。
    input:
        data1: pd.DataFrame、np.array, numeric 2-d, M * N
        data2: pd.DataFrame、np.array, numeric 2-d, M * N
    output:
        newData: pd.DataFrame、np.array(depend on dtype of data1), boolean 2-d, M * N
    '''
    data1 = s1.copy()
    if (type(s2) == int) | (type(s2) == float):
        data2 = pd.DataFrame(s2, index=s1.index, columns=s1.columns)
    else:
        data2 = s2.copy()

    assert not ((type(data1) == int) | (type(data1) == float)), 'first input is numeric!'
    assert not ((type(data2) == int) | (type(data2) == float)), 'second input is numeric!'
    assert data1.shape == data2.shape, 'Shape Mismatch!'

    isPdFrame_1 = isinstance(data1, pd.DataFrame)
    isPdFrame_2 = isinstance(data2, pd.DataFrame)

    if isPdFrame_1:
        index = data1.index
        columns = data1.columns
        data1 = data1.values

    if isPdFrame_2:
        data2 = data2.values

    Filter = np.isnan(data1) | np.isnan(data2)
    newData = np.full(data1.shape, False)
    newData[~Filter] = (data1[~Filter] != 0) & (data2[~Filter] != 0)

    if isPdFrame_1:
        newData = pd.DataFrame(newData, index=index, columns=columns)

    return newData * 1

def Or(s1: any, s2: any) -> any:
    '''
    若data1元素值與data2元素值至少一個不為0，則為True；其他情況都為False。
    input:
        data1: pd.DataFrame、np.array, numeric 2-d, M * N
        data2: pd.DataFrame、np.array, numeric 2-d, M * N
    output:
        newData: pd.DataFrame、np.array(depend on dtype of data1), boolean 2-d, M * N
    '''
    data1 = s1.copy()
    if (type(s2) == int) | (type(s2) == float):
        data2 = pd.DataFrame(s2, index=s1.index, columns=s1.columns)
    else:
        data2 = s2.copy()

    assert not ((type(data1) == int) | (type(data1) == float)), 'first input is numeric!'
    assert not ((type(data2) == int) | (type(data2) == float)), 'second input is numeric!'
    assert data1.shape == data2.shape, 'Shape Mismatch!'

    isPdFrame_1 = isinstance(data1, pd.DataFrame)
    isPdFrame_2 = isinstance(data2, pd.DataFrame)

    if isPdFrame_1:
        index = data1.index
        columns = data1.columns
        data1 = data1.values

    if isPdFrame_2:
        data2 = data2.values

    Filter = np.isnan(data1) | np.isnan(data2)
    newData = np.full(data1.shape, False)
    newData[~Filter] = (data1[~Filter] != 0) | (data2[~Filter] != 0)

    if isPdFrame_1:
        newData = pd.DataFrame(newData, index=index, columns=columns)

    return newData * 1

def Not(s1: any) -> any:
    '''
    若data元素值為0，則為True；其他情況都為False。
    input:
        data: pd.DataFrame、np.array, numeric 2-d, M * N
    output:
        newData: pd.DataFrame、np.array(depend on dtype of data), boolean 2-d, M * N
    '''
    data = s1.copy()
    isPdFrame = isinstance(data, pd.DataFrame)
    if isPdFrame:
        index = data.index
        columns = data.columns
        data = data.values

    Filter = np.isnan(data)
    newData = np.full(data.shape, False)
    newData[~Filter] = (data[~Filter] == 0)
    newData[Filter] = False

    if isPdFrame:
        newData = pd.DataFrame(newData, index=index, columns=columns)

    return newData * 1

def Xor(s1: any, s2: any) -> any:
    '''
    若data1元素值與data2元素值只有一個不為0，則為True；其他情況都為False。
    input:
        data1: pd.DataFrame、np.array, numeric 2-d, M * N
        data2: pd.DataFrame、np.array, numeric 2-d, M * N
    output:
        newData: pd.DataFrame、np.array(depend on dtype of data1), boolean 2-d, M * N
    '''
    data1 = s1.copy()
    if (type(s2) == int) | (type(s2) == float):
        data2 = pd.DataFrame(s2, index=s1.index, columns=s1.columns)
    else:
        data2 = s2.copy()
    assert not ((type(data1) == int) | (type(data1) == float)), 'first input is numeric!'
    assert not ((type(data2) == int) | (type(data2) == float)), 'second input is numeric!'
    assert data1.shape == data2.shape, 'Shape Mismatch!'

    isPdFrame_1 = isinstance(data1, pd.DataFrame)
    isPdFrame_2 = isinstance(data2, pd.DataFrame)

    if isPdFrame_1:
        index = data1.index
        columns = data1.columns
        data1 = data1.values

    if isPdFrame_2:
        data2 = data2.values

    Filter = np.isnan(data1) | np.isnan(data2)
    newData = np.full(data1.shape, False)
    newData[~Filter] = ((data1[~Filter] != 0) & ~(data2[~Filter] != 0)) | (
                ~(data1[~Filter] != 0) & (data2[~Filter] != 0))

    if isPdFrame_1:
        newData = pd.DataFrame(newData, index=index, columns=columns)

    return newData * 1

def Mthan(s1: any, s2: any) -> any:
    '''
    若data1元素值 > data2元素值，則為True，否則為False。
    input:
        data1: pd.DataFrame、np.array, numeric 2-d, M * N
        data2: pd.DataFrame、np.array, numeric 2-d, M * N
    output:
        newData: pd.DataFrame、np.array(depend on dtype of data1), boolean 2-d, M * N
    '''
    data1 = s1.copy()
    if (type(s2) == int) | (type(s2) == float):
        data2 = pd.DataFrame(s2, index=s1.index, columns=s1.columns)
    else:
        data2 = s2.copy()
    assert not ((type(data1) == int) | (type(data1) == float)), 'first input is numeric!'
    assert not ((type(data2) == int) | (type(data2) == float)), 'second input is numeric!'
    assert data1.shape == data2.shape, 'Shape Mismatch!'

    isPdFrame_1 = isinstance(data1, pd.DataFrame)
    isPdFrame_2 = isinstance(data2, pd.DataFrame)

    if isPdFrame_1:
        index = data1.index
        columns = data1.columns
        data1 = data1.values

    if isPdFrame_2:
        data2 = data2.values

    newData = data1 > data2

    if isPdFrame_1:
        newData = pd.DataFrame(newData, index=index, columns=columns)

    return newData * 1

def MEthan(s1: any, s2: any) -> any:
    '''
    若data1元素值 >= data2元素值，則為True，否則為False。
    input:
        data1: pd.DataFrame、np.array, numeric 2-d, M * N
        data2: pd.DataFrame、np.array, numeric 2-d, M * N
    output:
        newData: pd.DataFrame、np.array(depend on dtype of data1), boolean 2-d, M * N
    '''
    data1 = s1.copy()
    if (type(s2) == int) | (type(s2) == float):
        data2 = pd.DataFrame(s2, index=s1.index, columns=s1.columns)
    else:
        data2 = s2.copy()
    assert not ((type(data1) == int) | (type(data1) == float)), 'first input is numeric!'
    assert not ((type(data2) == int) | (type(data2) == float)), 'second input is numeric!'
    assert data1.shape == data2.shape, 'Shape Mismatch!'

    isPdFrame_1 = isinstance(data1, pd.DataFrame)
    isPdFrame_2 = isinstance(data2, pd.DataFrame)

    if isPdFrame_1:
        index = data1.index
        columns = data1.columns
        data1 = data1.values

    if isPdFrame_2:
        data2 = data2.values

    newData = data1 >= data2

    if isPdFrame_1:
        newData = pd.DataFrame(newData, index=index, columns=columns)

    return newData * 1

def Lthan(s1: any, s2: any) -> any:
    '''
    若data1元素值 < data2元素值，則為True，否則為False。
    input:
        data1: pd.DataFrame、np.array, numeric 2-d, M * N
        data2: pd.DataFrame、np.array, numeric 2-d, M * N
    output:
        newData: pd.DataFrame、np.array(depend on dtype of data1), boolean 2-d, M * N
    '''
    data1 = s1.copy()
    if (type(s2) == int) | (type(s2) == float):
        data2 = pd.DataFrame(s2, index=s1.index, columns=s1.columns)
    else:
        data2 = s2.copy()
    assert not ((type(data1) == int) | (type(data1) == float)), 'first input is numeric!'
    assert not ((type(data2) == int) | (type(data2) == float)), 'second input is numeric!'
    assert data1.shape == data2.shape, 'Shape Mismatch!'

    isPdFrame_1 = isinstance(data1, pd.DataFrame)
    isPdFrame_2 = isinstance(data2, pd.DataFrame)

    if isPdFrame_1:
        index = data1.index
        columns = data1.columns
        data1 = data1.values

    if isPdFrame_2:
        data2 = data2.values

    newData = data1 < data2

    if isPdFrame_1:
        newData = pd.DataFrame(newData, index=index, columns=columns)

    return newData * 1

def LEthan(s1: any, s2: any) -> any:
    '''
    若data1元素值 <= data2元素值，則為True，否則為False。
    input:
        data1: pd.DataFrame、np.array, numeric 2-d, M * N
        data2: pd.DataFrame、np.array, numeric 2-d, M * N
    output:
        newData: pd.DataFrame、np.array(depend on dtype of data1), boolean 2-d, M * N
    '''
    data1 = s1.copy()
    if (type(s2) == int) | (type(s2) == float):
        data2 = pd.DataFrame(s2, index=s1.index, columns=s1.columns)
    else:
        data2 = s2.copy()
    assert not ((type(data1) == int) | (type(data1) == float)), 'first input is numeric!'
    assert not ((type(data2) == int) | (type(data2) == float)), 'second input is numeric!'
    assert data1.shape == data2.shape, 'Shape Mismatch!'

    isPdFrame_1 = isinstance(data1, pd.DataFrame)
    isPdFrame_2 = isinstance(data2, pd.DataFrame)

    if isPdFrame_1:
        index = data1.index
        columns = data1.columns
        data1 = data1.values

    if isPdFrame_2:
        data2 = data2.values

    newData = data1 <= data2

    if isPdFrame_1:
        newData = pd.DataFrame(newData, index=index, columns=columns)

    return newData * 1

def Equal(s1: any, s2: any) -> any:
    '''
    若data1元素值 == data2元素值，則為True，否則為False。
    input:
        data1: pd.DataFrame、np.array, numeric 2-d, M * N
        data2: pd.DataFrame、np.array, numeric 2-d, M * N
    output:
        newData: pd.DataFrame、np.array(depend on dtype of data1), boolean 2-d, M * N
    '''
    data1 = s1.copy()
    if (type(s2) == int) | (type(s2) == float):
        data2 = pd.DataFrame(s2,index = s1.index, columns = s1.columns)
    else:
        data2 = s2.copy()
    assert not ((type(data1) == int) | (type(data1) == float)), 'first input is numeric!'
    assert not ((type(data2) == int) | (type(data2) == float)), 'second input is numeric!'
    assert data1.shape == data2.shape, 'Shape Mismatch!'
    isPdFrame_1 = isinstance(data1, pd.DataFrame)
    isPdFrame_2 = isinstance(data2, pd.DataFrame)
    if isPdFrame_1:
        index = data1.index
        columns = data1.columns
        data1 = data1.values
    if isPdFrame_2:
        data2 = data2.values
    newData = data1 == data2
    if isPdFrame_1:
        newData = pd.DataFrame(newData, index=index, columns=columns)
    return newData * 1

def UnEqual(s1: any, s2: any) -> any:
    '''
    若data1元素值 != data2元素值，則為True，否則為False。
    input:
        data1: pd.DataFrame、np.array, numeric 2-d, M * N
        data2: pd.DataFrame、np.array, numeric 2-d, M * N
    output:
        newData: pd.DataFrame、np.array(depend on dtype of data1), boolean 2-d, M * N
    '''
    data1 = s1.copy()
    if (type(s2) == int) | (type(s2) == float):
        data2 = pd.DataFrame(s2, index=s1.index, columns=s1.columns)
    else:
        data2 = s2.copy()
    assert not ((type(data1) == int) | (type(data1) == float)), 'first input is numeric!'
    assert not ((type(data2) == int) | (type(data2) == float)), 'second input is numeric!'
    assert data1.shape == data2.shape, 'Shape Mismatch!'

    isPdFrame_1 = isinstance(data1, pd.DataFrame)
    isPdFrame_2 = isinstance(data2, pd.DataFrame)

    if isPdFrame_1:
        index = data1.index
        columns = data1.columns
        data1 = data1.values

    if isPdFrame_2:
        data2 = data2.values

    newData = data1 != data2

    if isPdFrame_1:
        newData = pd.DataFrame(newData, index=index, columns=columns)

    return newData * 1

def Min(data1: any, data2: any) -> any:
    '''
    取兩矩陣或數的最小值。
    input:
        data1: int、float、pd.DataFrame、np.array, numeric 1-d or 2-d(M * N)
        data2: int、float、pd.DataFrame、np.array, numeric 1-d or 2-d(M * N)
        至少有1個input是矩陣。
    output:
        newData: pd.DataFrame、np.array(depend on dtype of input), numeric 2-d, M * N
    '''
    assert ((isinstance(data1, np.ndarray)) | (isinstance(data1, pd.DataFrame))) | ((isinstance(data2, np.ndarray)) | (
        isinstance(data2, pd.DataFrame))), 'At least one of the input should be Matrix!'
    flag = data1 > data2
    # 若data1為數值，則以data2為基礎
    if (type(data1) == int) | (type(data1) == float):
        newData = data2.copy()
        newData[~flag] = data1
    # 若data2為數值，則以data1為基礎
    elif (type(data2) == int) | (type(data2) == float):
        newData = data1.copy()
        newData[flag] = data2
    else:
        assert data1.shape == data2.shape, 'Shape Mismatch!'
        isNpArray_1 = isinstance(data1, np.ndarray)
        isNpArray_2 = isinstance(data2, np.ndarray)
        if (isNpArray_1) & (not isNpArray_2):
            data2 = data2.values
        elif (not isNpArray_1) & (isNpArray_2):
            data2 = pd.DataFrame(data2, index=data1.index, columns=data1.columns)
        newData = data1.copy()
        newData[flag] = data2[flag]
    return newData

def Max(data1: any, data2: any) -> any:
    '''
    取兩矩陣或數的最大值。
    input:
        data1: int、float、pd.DataFrame、np.array, numeric 1-d or 2-d(M * N)
        data2: int、float、pd.DataFrame、np.array, numeric 1-d or 2-d(M * N)
        至少有1個input是矩陣。
    output:
        newData: pd.DataFrame、np.array(depend on dtype of input), numeric 2-d, M * N
    '''
    assert ((isinstance(data1, np.ndarray)) | (isinstance(data1, pd.DataFrame))) | ((isinstance(data2, np.ndarray)) | (
        isinstance(data2, pd.DataFrame))), 'At least one of the input should be Matrix!'
    flag = data1 < data2
    # 若data1為數值，則以data2為基礎
    if (type(data1) == int) | (type(data1) == float):
        newData = data2.copy()
        newData[~flag] = data1
    # 若data2為數值，則以data1為基礎
    elif (type(data2) == int) | (type(data2) == float):
        newData = data1.copy()
        newData[flag] = data2
    else:
        assert data1.shape == data2.shape, 'Shape Mismatch!'
        isNpArray_1 = isinstance(data1, np.ndarray)
        isNpArray_2 = isinstance(data2, np.ndarray)
        if (isNpArray_1) & (not isNpArray_2):
            data2 = data2.values
        elif (not isNpArray_1) & (isNpArray_2):
            data2 = pd.DataFrame(data2, index=data1.index, columns=data1.columns)
        newData = data1.copy()
        newData[flag] = data2[flag]
    return newData


##################################################################################################################
# pn

## PNs

# def pn_Cut(s1: any, UpBound: 'int or float >= 0 & <= 100', LowBound: 'int or float >= 0 & <= 100') -> any:
#     '''
#     將data中pn_rank值超過UpBound及LowBound的值取代為nan。
#     input:
#         data: pd.DataFrame、np.array, numeric 2-d, M * N
#         UpBound: int、float, numeric 1-d, value= [0, 100](should larger than LowBound)
#         LowBound: int、float, numeric 1-d, value= [0, 100](should lower than LowBound)
#     output:
#         newData: pd.DataFrame、np.array(depend on dtype of data1), numeric 2-d, M * N
#     '''
#     assert UpBound < LowBound , 'UpBound < LowBound!'
#     data = s1.copy()
#     UpBound = UpBound / 100
#     LowBound = LowBound / 100
#     RankMT = pn_Rank(data)
#     ValidFilter = (RankMT >= LowBound) & (RankMT <= UpBound)
#     newData = data.copy()
#     newData[~ValidFilter] = np.nan
#     return newData

def pn_Cut(s1):
    '''
    將data中pn_rank值超過UpBound及LowBound的值取代為nan。
    input:
        data: pd.DataFrame、np.array, numeric 2-d, M * N
        UpBound: int、float, numeric 1-d, value= [0, 100](should larger than LowBound)
        LowBound: int、float, numeric 1-d, value= [0, 100](should lower than LowBound)
    output:
        newData: pd.DataFrame、np.array(depend on dtype of data1), numeric 2-d, M * N
    '''
    UpBound = 80
    LowBound = 20
    data = s1.copy()
    UpBound = UpBound / 100
    LowBound = LowBound / 100
    RankMT = pn_Rank(data)
    ValidFilter = (RankMT >= LowBound) & (RankMT <= UpBound)
    newData = data.copy()
    newData[~ValidFilter] = np.nan
    return newData

def pn_Rank(s2):  # normalization
    # dfCleaned = pd.DataFrame({'a':[np.nan,-1.7,5,3],'b':[np.nan,2.9,-3.1,8],'c':[4,np.nan,-6.11,8.1],'d':[7,22,-3.21,81],'e':[9,12,-1.21,11]},index=['one','two','three','four'])
    s1 = s2.copy()
    rank_ = s1.rank(pct=True, axis=1)
    cut = rank_.min(axis=1) / 2
    rank_ = rank_.sub(cut, axis=0)
    return rank_

def pn_Mean(s1: any) -> any:
    '''
    取data橫截面的平均值。
    input:
        data: pd.DataFrame、np.array, numeric 2-d, M * N
    output:
        newData: pd.DataFrame、np.array(depend on dtype of data1), numeric 2-d, M * N
    '''
    data = s1.copy()
    isPdFrame = isinstance(data, pd.DataFrame)
    newData = _repmat(np.nanmean(data, axis=1), data.shape[1], axis=1)
    if isPdFrame:
        newData = pd.DataFrame(newData, index=data.index, columns=data.columns)
    return newData


def pn_TransNorm(s1):  # normalization
    # print(s1)
    dfCleaned = s1.copy()
    rank_ = dfCleaned.rank(pct=True, axis=1)
    cut = rank_.min(axis=1) / 2
    rank_ = rank_.sub(cut, axis=0)
    out = pd.DataFrame(norm.ppf(np.array(rank_)), index=rank_.index, columns=rank_.columns)
    return out

# for some mid use
def Repmat(f_,lowBan):
    a = np.array([lowBan.values]).T
    c = np.tile(a,len(f_.columns))
    try:
        z = pd.DataFrame(c, index = f_.index, columns = f_.columns)
    except:
        c = np.reshape(c,(np.shape(c)[1],np.shape(c)[2]))
        z = pd.DataFrame(c, index = f_.index, columns = f_.columns)
    return z

def pn_CrossFit(s1,s2):
    x = s1.copy()
    y = s2.copy()
    b =( x.sub(x.mean(1),axis = 0) * y.sub(y.mean(1),axis = 0)).sum(1) / (x.sub(x.mean(1),axis = 0) * x.sub(x.mean(1),axis = 0)).sum(1)
    a = y.mean(1) - b * x.mean(1)
    res = y - (Repmat(x, a) + Repmat(x, b) * x)
    return res

def pn_Rank2(s1: any) -> any:
    '''
    取data橫截面的排序值。
    input:
        data: pd.DataFrame、np.array, numeric 2-d, M * N
    output:
        newData: pd.DataFrame、np.array(depend on dtype of data), numeric 2-d, M * N
    '''
    data = s1.copy()
    newData = data.rank(axis=1)
    return newData

def pn_RankCentered(s1: any) -> any:
    '''
    取data橫截面的排序值並縮放至(-1, 1)區間。
    input:
        data: pd.DataFrame、np.array, numeric 2-d, M * N
    output:
        newData: pd.DataFrame、np.array(depend on dtype of data), numeric 2-d, M * N
    '''
    data = s1.copy()
    newData = _pn_Rank(data)
    newData = newData * 2 - 1
    return newData

def pn_FillMax(s1: any) -> any:
    '''
    取data橫截面的最大值。
    input:
        data: pd.DataFrame、np.array, numeric 2-d, M * N
    output:
        newData: pd.DataFrame、np.array(depend on dtype of data), numeric 2-d, M * N
    '''
    data = s1.copy()
    isPdFrame = isinstance(data, pd.DataFrame)
    if isPdFrame:
        index = data.index
        columns = data.columns
        data = data.values

    newData = _repmat(np.nanmax(data, axis=1), data.shape[1], axis=1)

    if isPdFrame:
        newData = pd.DataFrame(newData, index=index, columns=columns)

    return newData


def pn_FillMin(s1: any) -> any:
    '''
    取data橫截面的最小值。
    input:
        data: pd.DataFrame、np.array, numeric 2-d, M * N
    output:
        newData: pd.DataFrame、np.array(depend on dtype of data), numeric 2-d, M * N
    '''
    data = s1.copy()
    isPdFrame = isinstance(data, pd.DataFrame)
    if isPdFrame:
        index = data.index
        columns = data.columns
        data = data.values

    newData = _repmat(np.nanmin(data, axis=1), data.shape[1], axis=1)
    if isPdFrame:
        newData = pd.DataFrame(newData, index=index, columns=columns)
    return newData

def pn_Winsor(s1, Multiplier) :
    '''
    將data橫截面超過Multiplier倍標準差的值以Multiplier * 標準差代替。
    input:
        data: pd.DataFrame、np.array, numeric 2-d, M * N
        Multiplier: int、float, numeric 1-d
    output:
        newData: pd.DataFrame、np.array(depend on dtype of data), numeric 2-d, M * N
    '''
    if Multiplier > 10:
        Multiplier = 10
    data = s1.copy()
    isPdFrame = isinstance(data, pd.DataFrame)
    if isPdFrame:
        index = data.index
        columns = data.columns
        data = data.values
    panelStd = np.nanstd(data, ddof=1, axis=1)
    thresHold = _repmat(panelStd, data.shape[1], axis=1) * Multiplier
    newData = data.copy()
    # 若原始數據為正，且超過正thresHold，以正thresHold代替
    if any(sum((data > 0) & (data > thresHold))):
        newData[(data > 0) & (data > thresHold)] = thresHold[(data > 0) & (data > thresHold)]
    # 若原始數據為負，且超過負thresHold，以負thresHold代替
    if any(sum((data < 0) & (data < -thresHold))):
        newData[(data < 0) & (data < -thresHold)] = -thresHold[(data < 0) & (data < -thresHold)]
    if isPdFrame:
        newData = pd.DataFrame(newData, index=index, columns=columns)
    return newData

def pn_TransStd(s1: any) -> any:
    '''
    標準化data橫截面值。
    input:
        data: pd.DataFrame、np.array, numeric 2-d, M * N
    output:
        newData: pd.DataFrame、np.array(depend on dtype of data), numeric 2-d, M * N
    '''
    data = s1.copy()
    isPdFrame = isinstance(data, pd.DataFrame)
    if isPdFrame:
        index = data.index
        columns = data.columns
        data = data.values
    meanData = _repmat(np.nanmean(data, axis=1), data.shape[1], axis=1)
    stdData = _repmat(np.nanstd(data, ddof=1, axis=1), data.shape[1], axis=1)
    newData = np.divide((data - meanData), stdData)
    if isPdFrame:
        newData = pd.DataFrame(newData, index=index, columns=columns)
    return newData

def pn_Stand(s1: any) -> any:
    '''
    對data橫截面值標準化，方法可選mean或median。
    input:
        data: pd.DataFrame、np.array, numeric 2-d, M * N
        method: str，string 1-d，value ∈ ['mean', 'median']
    output:
        newData: pd.DataFrame、np.array(depend on dtype of data), numeric 2-d, M * N
    '''
    data = s1.copy()
    a = data.sub(data.median(1) ,axis = 0)
    newData = a.divide(data.std(1),axis = 0)
    return newData

def _repmat(data: any, repeatNum: int, axis: int = 0) -> any:
    '''
    沿著row(axis = 0)或columns(axis = 1)復製數據。
    input:
        data: pd.DataFrame、np.array, numeric 1-d, M * 1 or 1 * N
        repeatNum: int, numeric 1-d
        axis: int, numeric 1-d, value ∈ [0, 1]
    output:
        newData: pd.DataFrame、np.array(depend on dtype of data), numeric 2-d, M * N
    '''
    if axis == 0:
        return np.repeat(data.reshape(1, data.shape[0]), repeatNum, axis=0)
    else:
        return np.repeat(data.reshape(data.shape[0], 1), repeatNum, axis=1)



###########################

def ts_Delay(s1, n):
    n = int(n)
    tem = s1.copy()
    tem1 = tem.shift(n, axis=0)
    return tem1


def ts_Mean(s1, n):
    n = int(n)
    if n == 0:
        return s1
    tem = s1.copy()
    tem1 = tem.rolling(n, axis=0, min_periods=1).mean()
    return tem1


def ts_Stdev(s1, n):
    n = int(n)
    if n <= 1:
        n = 2
    tem = s1.copy()
    tem1 = tem.rolling(n, axis=0, min_periods=1).std()
    return tem1

def ts_Delta(s1, n):
    n = int(n)
    tem = s1.copy()
    tem1 = tem-tem.shift(n, axis=0)
    return tem1

def ts_Min(s1, n):
    n = int(n)
    if n == 0:
        return s1
    tem = s1.copy()
    tem1 = tem.rolling(n, axis=0, min_periods=1).min()
    return tem1

def ts_Max(s1, n):
    n = int(n)
    if n == 0:
        return s1
    tem = s1.copy()
    tem1 = tem.rolling(n, axis=0, min_periods=1).max()
    return tem1


def ts_Skewness(s1, n):
    n = int(n)
    if n <= 2:
        n = 3
    tem = s1.copy()
    tem1 = tem.rolling(n, axis=0, min_periods=1).skew()
    return tem1

def ts_Kurtosis(s1, n):
    n = int(n)
    if n <= 3:
        n = 4
    tem = s1.copy()
    tem1 = tem.rolling(n, axis=0, min_periods=1).kurt()
    return tem1


def ts_Scale(s1, n):
    n = int(n)
    if n <= 1:
        return s1
    tem = s1.copy()
    mins = tem.rolling(n, axis=0, min_periods=1).min()
    maxs = tem.rolling(n, axis=0, min_periods=1).max()
    tem1 = (tem - mins) / (maxs - mins)
    return tem1.replace([np.inf, -np.inf], np.nan)


def ts_Corr(s1, s2, n):
    n = int(n)
    if n <= 1:
        n = 5
    tem1 = s1.copy()
    tem2 = s2.copy()
    tem1[tem2.isna()] = np.nan
    tem2[tem1.isna()] = np.nan
    tem1_m = tem1.rolling(n, axis=0, min_periods=1).mean()
    tem2_m = tem2.rolling(n, axis=0, min_periods=1).mean()
    tem_prod_m = (tem1 * tem2).rolling(n, axis=0, min_periods=1).mean()
    tem1_std = tem1.rolling(n, axis=0, min_periods=1).std(ddof=0)
    tem2_std = tem2.rolling(n, axis=0, min_periods=1).std(ddof=0)
    res = (tem_prod_m - tem1_m * tem2_m) / (tem1_std * tem2_std)
    return res.replace([-np.inf, np.inf], np.nan)

def ts_Cov(s1, s2, n):
    n = int(n)
    if n <= 1:
        n = 2
    tem1 = s1.copy()
    tem2 = s2.copy()
    tem1[tem2.isna()] = np.nan
    tem2[tem1.isna()] = np.nan
    tem1_m = tem1.rolling(n, axis=0, min_periods=1).mean()
    tem2_m = tem2.rolling(n, axis=0, min_periods=1).mean()
    tem_prod_m = (tem1 * tem2).rolling(n, axis=0, min_periods=1).mean()
    res = tem_prod_m - tem1_m * tem2_m
    return res

def ts_Regression(s1, s2, n, rettype):
    n = int(n)
    if n <= 2:
        n = 3
    tem1 = s1.copy()
    tem2 = s2.copy()
    tem1[tem2.isna()] = np.nan
    tem2[tem1.isna()] = np.nan
    tem1_m = tem1.rolling(n, axis=0, min_periods=1).mean()
    tem2_m = tem2.rolling(n, axis=0, min_periods=1).mean()
    tem_prod_m = (tem1 * tem2).rolling(n, axis=0, min_periods=1).mean()
    tem2_var = tem2.rolling(n, axis=0, min_periods=1).var(ddof=0)
    beta = (tem_prod_m - tem1_m * tem2_m) / tem2_var
    beta = beta.replace([np.inf, -np.inf], np.nan)
    if rettype == 'A':
        return beta
    const = tem1_m - beta * tem2_m
    if rettype == 'B':
        return const
    y_est = const + beta * tem2
    if rettype == 'C':
        return y_est
    resid = tem1 - y_est
    if rettype == 'D':
        return resid

@njit
def _ts_entropy_v(input, days, bucket=10):
    res = np.full_like(input, fill_value=np.nan)
    for di in range(input.shape[0]):
        dcut = min(di + 1, days)
        cur = input[(di-dcut+1):(di+1)]
        valid = np.isfinite(cur)
        n = np.sum(valid)
        if n > 2:
            hist = cur[valid]
            hist = np.histogram(hist, bucket)[0] / n
            hist[hist==0] = 1
            res[di] = np.log(bucket) - np.mean(hist * np.log(hist))
    return res


@njit
def entropy(input, days, bucket=10):
    res = np.copy(input)
    for ii in prange(input.shape[1]):
        v = input[:, ii]
        res[:, ii] = _ts_entropy_v(v, days, bucket)
    return res

def ts_Entropy(s1, n):
    tem = s1.copy()
    arr = tem.values
    res = entropy(arr, n, 10)
    res = pd.DataFrame(res, index=tem.index, columns=tem.columns)
    return res

def ts_Partial_corr(s1, s2, s3, n):
    corr12 = ts_Corr(s1, s2, n)
    corr13 = ts_Corr(s1, s3, n)
    corr23 = ts_Corr(s2, s3, n)
    res = (corr12 - corr13 * corr23) / (np.sqrt(1 - corr13**2) * np.sqrt(1 - corr23**2))
    return res.replace([-np.inf, np.inf], np.nan)

# rank1.sub(cut, axis=0)
def ts_TransNorm(s1,n):  # ts normalization
    n =  int(n)
    if n <30:
        n = 30
    dfCleaned = s1.copy()
    df = (dfCleaned.rolling(window=n, min_periods=1).rank(ascending=True, pct=True)) - 1 / n / 2
    z = np.array(df)
    zz = norm.ppf(z)
    zzz = pd.DataFrame(zz, index =df.index, columns = df.columns)
    return zzz


def ts_Decay(dataTD, nPrds):
    nPrds = int(nPrds)
    if nPrds > 0:
        w = np.array([1-1/nPrds*(i-1) for i in range(nPrds,0,-1)])
        w = w / sum(w)
        transformedTD = dataTD*w[-1]
        for i in range(nPrds-1):
            transformedTD = transformedTD+np.roll(dataTD,shift=nPrds-i-1,axis=0)*w[i]
        transformedTD[:nPrds] = np.nan
        return transformedTD
    else:
        return dataTD

def ts_Decay2(dataTD, nPrds):
    nPrds = int(nPrds)
    if nPrds > 0:
        alpha =1 - 2/(nPrds+1)
        w = np.array([alpha**i for i in range(nPrds,0,-1)])
        w = w / sum(w)
        transformedTD = dataTD*w[-1]
        for i in range(nPrds-1):
            transformedTD = transformedTD+np.roll(dataTD,shift=nPrds-i-1,axis=0)*w[i]
        transformedTD[:nPrds] = np.nan
        return transformedTD
    else:
        return dataTD


def ts_Rank(s1: any, NPrds: int) -> any:  # too slow. probably because of package stats...
    '''
    取data在過去NPrds時間窗口內的排序值。
    input:
        data: pd.DataFrame、np.array, numeric 2-d, M * N
        NPrds: int, numeric 1-d
    output:
        newData: pd.DataFrame、np.array(depend on dtype of data), numeric 2-d, M * N
    '''
    NPrds = int(NPrds)
    if NPrds < 5:
        NPrds = 5
    data = s1.copy()
    newData = (data.rolling(window=NPrds, min_periods=1).rank(ascending=True, pct=True)) - 1 / NPrds / 2
    return newData

def ts_Median(s1: any, NPrds: int) -> any:
    '''
    取data在過去NPrds時間窗口內的中位數。
    input:
        data: pd.DataFrame、np.array, numeric 2-d, M * N
        NPrds: int, numeric 1-d
    output:
        newData: pd.DataFrame、np.array(depend on dtype of data), numeric 2-d, M * N
    '''
    NPrds = int(NPrds)
    if NPrds < 5:
        NPrds = 5
    data = s1.copy()
    newData = data.rolling(window=NPrds, min_periods=1).median()
    return newData

def ts_Argmax(s1: any, NPrds: int) -> any:
    '''
    取data在過去NPrds時間窗口內最大值的位置。
    例如若今日為過去NPrds天內最大值，則值為1；若最大值在距今2天前，則值為3。
    input:
        data: pd.DataFrame、np.array, numeric 2-d, M * N
        NPrds: int, numeric 1-d
    output:
        newData: pd.DataFrame、np.array(depend on dtype of data), numeric 2-d, M * N
    '''
    NPrds = int(NPrds)
    if NPrds < 2:
        NPrds = 2
    data = s1.copy()
    data[data.isna()] = -np.inf
    data = NPrds - _ts_Fun(data, 'lambda x: np.argmax(x, axis = 0)', NPrds)

    return data


def ts_Argmin(s1: any, NPrds: int) -> any:
    '''
    取data在過去NPrds時間窗口內最小值的位置。
    例如若今日為過去NPrds天內最小值，則值為1；若最小值在距今2天前，則值為3。
    input:
        data: pd.DataFrame、np.array, numeric 2-d, M * N
        NPrds: int, numeric 1-d
    output:
        newData: pd.DataFrame、np.array(depend on dtype of data), numeric 2-d, M * N
    '''
    NPrds = int(NPrds)
    if NPrds < 2:
        NPrds = 2
    data = s1.copy()
    data[data.isna()] = -np.inf
    data = NPrds - _ts_Fun(data, 'lambda x: np.argmin(x, axis = 0)', NPrds)
    return data


def ts_Sum(s1: any, NPrds: int) -> any:
    '''
    取data在過去NPrds時間窗口內的總和。
    input:
        data: pd.DataFrame、np.array, numeric 2-d, M * N
        NPrds: int, numeric 1-d
    output:
        newData: pd.DataFrame、np.array(depend on dtype of data), numeric 2-d, M * N
    '''
    NPrds = int(NPrds)
    if NPrds < 3:
        NPrds = 3
    data = s1.copy()
    newData = data.rolling(window=NPrds, min_periods=1).sum()
    return newData

def ts_Product(s1: any, NPrds: int) -> any:
    '''
    取data在過去NPrds時間窗口內的累乘值。
    input:
        data: pd.DataFrame、np.array, numeric 2-d, M * N
        NPrds: int, numeric 1-d
    output:
        newData: pd.DataFrame、np.array(depend on dtype of data), numeric 2-d, M * N
    '''
    NPrds = int(NPrds)
    if NPrds < 5:
        NPrds = 5
    data = s1.copy()
    isPdFrame = isinstance(data, pd.DataFrame)
    if isPdFrame:
        index = data.index
        columns = data.columns
        data = data.values

    tmpData = data.copy()
    tmpData[np.isnan(tmpData)] = 1
    newData = _ts_Fun(tmpData, 'lambda x: np.prod(x, 0)', NPrds)
    if isPdFrame:
        newData = pd.DataFrame(newData, index=index, columns=columns)
    return newData

def ts_Divide(s1: any, NPrds: int) -> any:
    '''
    取data與(NPrds - 1)天前數據相除值。
    input:
        data: pd.DataFrame、np.array, numeric 2-d, M * N
        NPrds: int, numeric 1-d
    output:
        newData: pd.DataFrame、np.array(depend on dtype of data), numeric 2-d, M * N
    '''
    NPrds = int(NPrds)
    if NPrds < 5:
        NPrds = 5
    data = s1.copy()
    data2 = data.copy().shift(NPrds)
    data2[data2 == 0] = np.nan
    newData = data / data2
    return newData

def ts_ChgRate(s1: any, NPrds: int) -> any:
    '''
    取data與(NPrds - 1)天前數據的變化率(相除值 - 1)。
    input:
        data: pd.DataFrame、np.array, numeric 2-d, M * N
        NPrds: int, numeric 1-d
    output:
        newData: pd.DataFrame、np.array(depend on dtype of data), numeric 2-d, M * N
    '''
    if NPrds<1:
        NPrds = 1
    data = s1.copy()
    data2 = data.copy().shift(NPrds)
    data2[data2 == 0] = np.nan
    newData = data / data2
    newData = newData - 1
    return newData


def _ts_Fun(data: any, FunHandle: str, NPrds: int) -> any:
    '''
    ts運算的通用函數，傳入FunHandle(函數)，即可運算出對應結果。
    FunHandle為string格式的函數，例如：'lambda x: np.nansum(x, 0)'，即為時序總和的函數。
    input:
        data: pd.DataFrame、np.array, numeric 2-d, M * N
        FunHandle: str, string 1-d
        NPrds: int, numeric 1-d
    output:
        newData: pd.DataFrame、np.array(depend on dtype of data), numeric 2-d, M * N
    '''
    TradeDay = np.arange(data.shape[0])
    PriceFlag = [0, 0, 0, 0, 0]
    # if abs(NPrds) <= 1: return ts_FillNonTradingDay(data)
    if abs(NPrds) <= 1: return data
    isPdFrame = isinstance(data, pd.DataFrame)
    if isPdFrame:
        index = data.index
        columns = data.columns
        data = data.values
    newData = np.full(data.shape, np.nan)
    data_Trd = data[TradeDay, :]
    data_Trd, AdjMT = Util_AdjForTS(data_Trd, PriceFlag[0])
    def __general_fun(i, data_Trd, AdjMT, FunHandle, NPrds):
        iData = data_Trd[i - NPrds + 1:i + 1, :] / AdjMT[i, :]
        # 使用FunHandle並作用於iData上
        generalRes = eval(FunHandle)(iData)
        if generalRes.ndim == 1:
            return generalRes
        else:
            return generalRes[-1, :]
    generalVals = list(
        map(lambda x: __general_fun(x, data_Trd, AdjMT, FunHandle, NPrds), range(NPrds - 1, data_Trd.shape[0])))
    generalVals = np.array(generalVals)
    newData_Trd = np.full(data_Trd.shape, np.nan)
    newData = np.full(data.shape, np.nan)
    newData_Trd[NPrds - 1:, :] = generalVals
    newData[TradeDay, :] = newData_Trd
    if isPdFrame:
        newData = pd.DataFrame(newData, index=index, columns=columns)
    return newData

def Util_AdjForTS(data, Type) :
    '''
    Util_函數，取得復權數據以及復權乘數。
    input:
        data: pd.DataFrame、np.array, numeric 2-d(M * N) or 3-d(M * N * P)
        Type: int, numeric 1-d
    output:
        newData: pd.DataFrame、np.array(depend on dtype of data), numeric 2-d(M * N) or 3-d(M * N * P)
        pAdjMat_Trd: pd.DataFrame、np.array(depend on dtype of data), numeric 2-d(M * N) or 3-d(M * N * P)
    '''
    TradeDay = np.arange(data.shape[0])
    PriceFlag = [0, 0, 0, 0, 0]
    pAdjMat = np.nan_to_num(data) * 0 + 1
    isPdFrame = isinstance(data, pd.DataFrame)
    if isPdFrame:
        index = data.index
        columns = data.columns
        data = data.values
    # 若Type非0，則需復權
    if Type != 0:
        # 二維數據
        if data.ndim == 2:
            pAdjMat_Trd = pAdjMat[TradeDay, :]
            newData = data * pAdjMat_Trd ** Type
        # 三維數據
        elif data.ndim == 3:
            pAdjMat_Trd = pAdjMat[TradeDay, :]
            newData = np.full(data.shape, np.nan)
            newData[:, :, 0] = data[:, :, 0] * pAdjMat_Trd ** Type
            newData[:, :, 1] = data[:, :, 1] * pAdjMat_Trd ** Type
            newData[:, :, 2] = data[:, :, 2] * pAdjMat_Trd ** Type
            newPAdjMat_Trd = np.full(data.shape, np.nan)
            newPAdjMat_Trd[:, :, 0] = pAdjMat_Trd
            newPAdjMat_Trd[:, :, 1] = pAdjMat_Trd
            newPAdjMat_Trd[:, :, 2] = pAdjMat_Trd
            pAdjMat_Trd = newPAdjMat_Trd
    # 若Type為0，則不用復權，復權乘數為1
    else:
        pAdjMat_Trd = np.full(data.shape, 1)
        newData = data.copy()
    if isPdFrame:
        newData = pd.DataFrame(newData, index=index, columns=columns)
    return newData, pAdjMat_Trd



# groupby_TransNorm

def _pn_Rank(s1):  # normalization
    # dfCleaned = pd.DataFrame({'a':[np.nan,-1.7,5,3],'b':[np.nan,2.9,-3.1,8],'c':[4,np.nan,-6.11,8.1],'d':[7,22,-3.21,81],'e':[9,12,-1.21,11]},index=['one','two','three','four'])
    rank_ = s1.rank(pct=True, axis=1)
    cut = rank_.min(axis=1) / 2
    rank_ = rank_.sub(cut, axis=0)
    return rank_

# def pn_GroupNorm(data_: any, Label_: any) -> any:
#     '''
#     將數據分組，在組內各自將數據轉換為正態分布。
#     input:
#         data: pd.DataFrame、np.array, numeric 2-d, M * N
#         Label: pd.DataFrame、np.array, numeric 2-d, M * N
#     output:
#         newData: pd.DataFrame、np.array(depend on dtype of data), numeric 2-d, M * N
#     '''
#     # 複製Label是由於下方會更改Label值，為避免原始值被修改，因此復製新的Label
#     Label = Label_.copy()
#     data = data_.copy()
#     isPdFrame_data = isinstance(data, pd.DataFrame)
#     isPdFrame_label = isinstance(Label, pd.DataFrame)
#     if isPdFrame_data:
#         index = data.index
#         columns = data.columns
#         data = data.values
#     if isPdFrame_label:
#         Label = Label.values
#         Label = Label.astype(float)
#     Label[np.isnan(data)] = np.nan
#     GrpNames = sorted(set(Label[~np.isnan(Label)]))
#     # assert len(GrpNames) <= data.shape[1], 'Too many Label!'
#     if len(GrpNames) >= 40:
#         Label_ = pd.DataFrame(Label)
#         Label_ = Label_.rank(pct=True, axis=1)
#         cut = Label_.min(axis=1) / 2
#         Label_ = Label_.sub(cut, axis=0)
#         Label_ = np.floor(Label_ * 10)
#         Label = Label_.values.astype(float)
#     GrpNames = sorted(set(Label[~np.isnan(Label)]))
#     # print(GrpNames)
#     ## PNs
#     def _pn_TransNorm(s1):  # normalization
#         # print(s1)
#         s1 = pd.DataFrame(s1)
#         rank_ = s1.rank(pct=True, axis=1)
#         cut = rank_.min(axis=1) / 2
#         rank_ = rank_.sub(cut, axis=0)
#         out = pd.DataFrame(norm.ppf(np.array(rank_)), index=rank_.index, columns=rank_.columns)
#         return out.values
#     def __grpTransNorm_fun(grpName, data, Label):
#         # 只保留grpName的數據，其他填上nan值
#         grpFlag = (Label == grpName)
#         tmpData = np.full(data.shape, np.nan)
#         tmpData[grpFlag] = data[grpFlag]
#         tmpData = _pn_TransNorm(tmpData)
#         tmpData[np.isnan(tmpData)] = 0
#         return tmpData
#     # 平行運算，取得每一分組轉換後的矩陣，每個矩陣大小都是M * N
#     grpTransNormVals = Parallel(n_jobs = 10)(delayed(__grpTransNorm_fun)(grpName, data, Label) for grpName in GrpNames)
#     grpTransNormVals = np.array(grpTransNormVals)
#     # 由於所有矩陣都只包含該組別之下的數據，彼此並不重疊，因此直接將所有組別的矩陣值相加即可得到最後包含所有數據的矩陣
#     newData = np.sum(grpTransNormVals, axis=0)
#     newData[np.isnan(Label)] = np.nan
#     if isPdFrame_data:
#         newData = pd.DataFrame(newData, index=index, columns=columns)
#     return newData

def pn_GroupNorm(data_: any, Label_: any) -> any:
    '''
    將數據分組，在組內各自將數據轉換為正態分布。
    input:
        data: pd.DataFrame、np.array, numeric 2-d, M * N
        Label: pd.DataFrame、np.array, numeric 2-d, M * N
    output:
        newData: pd.DataFrame、np.array(depend on dtype of data), numeric 2-d, M * N
    '''
    # 複製Label是由於下方會更改Label值，為避免原始值被修改，因此復製新的Label
    Label = Label_.copy()
    data = data_.copy()
    isPdFrame_data = isinstance(data, pd.DataFrame)
    isPdFrame_label = isinstance(Label, pd.DataFrame)
    if isPdFrame_data:
        index = data.index
        columns = data.columns
        data = data.values
    if isPdFrame_label:
        Label = Label.values
        Label = Label.astype(float)
    Label[np.isnan(data)] = np.nan
    GrpNames = sorted(set(Label[~np.isnan(Label)]))
    # assert len(GrpNames) <= data.shape[1], 'Too many Label!'
    if len(GrpNames) >= 40:
        Label_ = pd.DataFrame(Label)
        Label_ = Label_.rank(pct=True, axis=1)
        cut = Label_.min(axis=1) / 2
        Label_ = Label_.sub(cut, axis=0)
        Label_ = np.floor(Label_ * 10)
        Label = Label_.values.astype(float)
    GrpNames = sorted(set(Label[~np.isnan(Label)]))
    # print(GrpNames)
    newData = np.full(data.shape, 0)
    def _pn_TransNorm(s1):  # normalization
    # print(s1)
        s1 = pd.DataFrame(s1)
        rank_ = s1.rank(pct=True, axis=1)
        cut = rank_.min(axis=1) / 2
        rank_ = rank_.sub(cut, axis=0)
        out = pd.DataFrame(norm.ppf(np.array(rank_)), index=rank_.index, columns=rank_.columns)
        return out.values
    for grpName in GrpNames:
        grpFlag = (Label == grpName)
        tmpData = np.full(data.shape, np.nan)
        tmpData[grpFlag] = data[grpFlag]
        tmpData = _pn_TransNorm(tmpData)
        tmpData[np.isnan(tmpData)] = 0
        newData = newData + tmpData
    newData = newData.astype(float)
    newData[np.isnan(Label)] = np.nan
    if isPdFrame_data:
        newData = pd.DataFrame(newData, index=index, columns=columns)
    return newData

# def pn_GroupRank(data_: any, Label_: any) -> any:
#     '''
#     將數據分組，在組內各自將數據轉換為正態分布。
#     input:
#         data: pd.DataFrame、np.array, numeric 2-d, M * N
#         Label: pd.DataFrame、np.array, numeric 2-d, M * N
#     output:
#         newData: pd.DataFrame、np.array(depend on dtype of data), numeric 2-d, M * N
#     '''
#     # 複製Label是由於下方會更改Label值，為避免原始值被修改，因此復製新的Label
#     Label = Label_.copy()
#     data = data_.copy()
#     isPdFrame_data = isinstance(data, pd.DataFrame)
#     isPdFrame_label = isinstance(Label, pd.DataFrame)
#     if isPdFrame_data:
#         index = data.index
#         columns = data.columns
#         data = data.values
#     if isPdFrame_label:
#         Label = Label.values
#         Label = Label.astype(float)
#     Label[np.isnan(data)] = np.nan
#     GrpNames = sorted(set(Label[~np.isnan(Label)]))
#     # assert len(GrpNames) <= data.shape[1], 'Too many Label!'
#     if len(GrpNames) >= 40:
#         Label_ = pd.DataFrame(Label)
#         Label_ = Label_.rank(pct=True, axis=1)
#         cut = Label_.min(axis=1) / 2
#         Label_ = Label_.sub(cut, axis=0)
#         Label_ = np.floor(Label_ * 10)
#         Label = Label_.values.astype(float)
#     GrpNames = sorted(set(Label[~np.isnan(Label)]))
#     ## PNs
#     def _pn_ranks(s1):  # normalization
#         # print(s1)
#         s1 = pd.DataFrame(s1)
#         rank_ = s1.rank(pct=True, axis=1)
#         cut = rank_.min(axis=1) / 2
#         rank_ = rank_.sub(cut, axis=0)
#         return rank_.values
#     def __grpTransNorm_fun(grpName, data, Label):
#         # 只保留grpName的數據，其他填上nan值
#         grpFlag = (Label == grpName)
#         tmpData = np.full(data.shape, np.nan)
#         tmpData[grpFlag] = data[grpFlag]
#         tmpData = _pn_ranks(tmpData)
#         tmpData[np.isnan(tmpData)] = 0
#         return tmpData
#     # 平行運算，取得每一分組轉換後的矩陣，每個矩陣大小都是M * N
#     grpTransNormVals = Parallel(n_jobs = 10)(delayed(__grpTransNorm_fun)(grpName, data, Label) for grpName in GrpNames)
#     grpTransNormVals = np.array(grpTransNormVals)
#     # 由於所有矩陣都只包含該組別之下的數據，彼此並不重疊，因此直接將所有組別的矩陣值相加即可得到最後包含所有數據的矩陣
#     newData = np.sum(grpTransNormVals, axis=0)
#     newData[np.isnan(Label)] = np.nan
#     if isPdFrame_data:
#         newData = pd.DataFrame(newData, index=index, columns=columns)
#     return newData

def pn_GroupRank(data_: any, Label__: any) -> any:
    # 複製Label是由於下方會更改Label值，為避免原始值被修改，因此復製新的Label
    Label = Label__.copy()
    data = data_.copy()
    Label[data.isna()] = np.nan
    isPdFrame_data = isinstance(data, pd.DataFrame)
    isPdFrame_label = isinstance(Label, pd.DataFrame)
    if isPdFrame_data:
        index = data.index
        columns = data.columns
        data = data.values
    if isPdFrame_label:
        Label = Label.values
        Label = Label.astype(float)
    # Label[np.isnan(data)] = np.nan
    GrpNames = sorted(set(Label[~np.isnan(Label)]))
    # assert len(GrpNames) <= data.shape[1], 'Too many Label!'
    if len(GrpNames) >= 40:
        Label_ = pd.DataFrame(Label)
        Label_ = Label_.rank(pct=True, axis=1)
        cut = Label_.min(axis=1) / 2
        Label_ = Label_.sub(cut, axis=0)
        Label_ = np.floor(Label_ * 10)
        Label = Label_.values.astype(float)
    GrpNames = sorted(set(Label[~np.isnan(Label)]))
    ## PNs
    def _pn_ranks(s1):  # normalization
        # print(s1)
        s1 = pd.DataFrame(s1)
        rank_ = s1.rank(pct=True, axis=1)
        cut = rank_.min(axis=1) / 2
        rank_ = rank_.sub(cut, axis=0)
        return rank_.values
    newData = np.full(data.shape, 0)
    for grpName in GrpNames:
        grpFlag = (Label == grpName)
        tmpData = data.copy()
        tmpData = tmpData.astype(np.float64)
        tmpData[~grpFlag] = np.nan
        rankData = _pn_ranks(tmpData)
        rankData[np.isnan(rankData)] = 0
        newData = newData + rankData
    newData = newData.astype(float)
    newData[np.isnan(Label)] = np.nan
    if isPdFrame_data:
        newData = pd.DataFrame(newData, index=index, columns=columns)
    return newData

# def pn_GroupNeutral(data_: any, Label_: any) -> any:
#     '''
#     將數據分組，在組內各自將數據轉換為正態分布。
#     input:
#         data: pd.DataFrame、np.array, numeric 2-d, M * N
#         Label: pd.DataFrame、np.array, numeric 2-d, M * N
#     output:
#         newData: pd.DataFrame、np.array(depend on dtype of data), numeric 2-d, M * N
#     '''
#     # 複製Label是由於下方會更改Label值，為避免原始值被修改，因此復製新的Label
#     Label = Label_.copy()
#     data = data_.copy()
#     isPdFrame_data = isinstance(data, pd.DataFrame)
#     isPdFrame_label = isinstance(Label, pd.DataFrame)
#     if isPdFrame_data:
#         index = data.index
#         columns = data.columns
#         data = data.values
#     if isPdFrame_label:
#         Label = Label.values
#         Label = Label.astype(float)
#     Label[np.isnan(data)] = np.nan
#     GrpNames = sorted(set(Label[~np.isnan(Label)]))
#     # assert len(GrpNames) <= data.shape[1], 'Too many Label!'
#     if len(GrpNames) >= 40:
#         Label_ = pd.DataFrame(Label)
#         Label_ = Label_.rank(pct=True, axis=1)
#         cut = Label_.min(axis=1) / 2
#         Label_ = Label_.sub(cut, axis=0)
#         Label_ = np.floor(Label_ * 10)
#         Label = Label_.values.astype(float)
#     GrpNames = sorted(set(Label[~np.isnan(Label)]))
#     ## PNs
#     def _pn_ranks2(s1):  # normalization
#         # print(s1)
#         s1 = pd.DataFrame(s1)
#         mean = s1.mean(1)
#         std = s1.std(1)
#         rank_ = (s1.sub(mean, axis=0)).divide(std,axis=0)
#         return rank_.values
#     def __grpTransNorm_fun(grpName, data, Label):
#         # 只保留grpName的數據，其他填上nan值
#         grpFlag = (Label == grpName)
#         tmpData = np.full(data.shape, np.nan)
#         tmpData[grpFlag] = data[grpFlag]
#         tmpData = _pn_ranks2(tmpData)
#         tmpData[np.isnan(tmpData)] = 0
#         return tmpData
#     # 平行運算，取得每一分組轉換後的矩陣，每個矩陣大小都是M * N
#     grpTransNormVals = Parallel(n_jobs = 10)(delayed(__grpTransNorm_fun)(grpName, data, Label) for grpName in GrpNames)
#     grpTransNormVals = np.array(grpTransNormVals)
#     # 由於所有矩陣都只包含該組別之下的數據，彼此並不重疊，因此直接將所有組別的矩陣值相加即可得到最後包含所有數據的矩陣
#     newData = np.sum(grpTransNormVals, axis=0)
#     newData[np.isnan(Label)] = np.nan
#     if isPdFrame_data:
#         newData = pd.DataFrame(newData, index=index, columns=columns)
#     return newData

def pn_GroupNeutral(data_: any, Label_: any) -> any:
    '''
    將數據分組，在組內各自將數據轉換為正態分布。
    input:
        data: pd.DataFrame、np.array, numeric 2-d, M * N
        Label: pd.DataFrame、np.array, numeric 2-d, M * N
    output:
        newData: pd.DataFrame、np.array(depend on dtype of data), numeric 2-d, M * N
    '''
    # 複製Label是由於下方會更改Label值，為避免原始值被修改，因此復製新的Label
    Label = Label_.copy()
    data = data_.copy()
    isPdFrame_data = isinstance(data, pd.DataFrame)
    isPdFrame_label = isinstance(Label, pd.DataFrame)
    if isPdFrame_data:
        index = data.index
        columns = data.columns
        data = data.values
    if isPdFrame_label:
        Label = Label.values
        Label = Label.astype(float)
    Label[np.isnan(data)] = np.nan
    GrpNames = sorted(set(Label[~np.isnan(Label)]))
    # assert len(GrpNames) <= data.shape[1], 'Too many Label!'
    if len(GrpNames) >= 40:
        Label_ = pd.DataFrame(Label)
        Label_ = Label_.rank(pct=True, axis=1)
        cut = Label_.min(axis=1) / 2
        Label_ = Label_.sub(cut, axis=0)
        Label_ = np.floor(Label_ * 10)
        Label = Label_.values.astype(float)
    GrpNames = sorted(set(Label[~np.isnan(Label)]))
    ## PNs
    newData = np.full(data.shape, 0)
    for grpName in GrpNames:
        grpFlag = (Label == grpName)
        tmpData = np.full(data.shape, np.nan)
        tmpData[grpFlag] = data[grpFlag]
        meanMT = _repmat(np.nanmean(tmpData, axis = 1), tmpData.shape[1], axis = 1)
        tmpData -= meanMT
        tmpData[np.isnan(tmpData)] = 0
        newData = newData + tmpData
    newData = newData.astype(float)
    newData[np.isnan(Label)] = np.nan
    if isPdFrame_data:
        newData = pd.DataFrame(newData, index=index, columns=columns)
    return newData


#############################other
def ts_csMacd(data_):
    numS, numL, numD = 6, 14, 9
    data = data_.copy().cumsum()
    EMA1 = data.ewm(span=numS, adjust=False).mean()  # ewm
    EMA2 = data.ewm(span=numL, adjust=False).mean()
    DIF = EMA1 - EMA2
    DEA = DIF.ewm(numD, adjust=False).mean()
    BAR = 2 * (DIF - DEA)
    return BAR

def ts_Quantile(data_,N,rettype):
    data = data_.copy()
    if N < 5 :
        N = 5
    if rettype == 'A':
        return data.rolling(window=N).quantile(0.2)
    if rettype == 'B':
        return data.rolling(window=N).quantile(0.4)
    if rettype == 'C':
        return data.rolling(window=N).quantile(0.6)
    if rettype == 'D':
        return data.rolling(window=N).quantile(0.8)


def ts_MeanChg(s1, num):
    num = int(num)
    if num < 3 :
        num = 3
    tmp = s1.copy()
    tmp1 = tmp.rolling(num).mean()
    tmp2 = ts_Decay(s1,num)
    return tmp1 - tmp2


def ts_Stdev2(s1, num):
    num = int(num)
    if num < 5 :
        num = 5
    tmp = s1.copy()
    tmp1 = ts_Stdev(tmp, int(num / 3 * 2))
    tmp2 = ts_Stdev(tmp, int(num / 2))
    return (tmp1 + tmp2)/2


def ts_StdevChg(s1, num):
    num = int(num)
    tmp = s1.copy()
    tmp1 = ts_Stdev(tmp, num)
    tmp2 = ts_Stdev2(tmp, num)
    return tmp1 - tmp2


def ts_Cov2(s1,s2, num):
    num = int(num)
    if num < 5 :
        num = 5
    s11 = s1.copy()
    s22 = s2.copy()
    tmp1 = ts_Cov(s11,s22, int(num / 3 * 2))
    tmp2 = ts_Cov(s11,s22, int(num / 2))
    return (tmp1 + tmp2)/2


def ts_CovChg(s1,s2, num):
    num = int(num)
    tmp1 = s1.copy()
    tmp2 = s2.copy()
    return ts_Cov(tmp1,tmp2,num) - ts_Cov2(tmp1,tmp2,num)


def ts_Corr2(s1,s2, num):
    num = int(num)
    if num < 5 :
        num = 5
    s11 = s1.copy()
    s22 = s2.copy()
    tmp1 = ts_Corr(s11,s22, int(num / 3 * 2))
    tmp2 = ts_Corr(s11,s22, int(num / 2))
    return (tmp1 + tmp2)/2



def ts_CorrChg(s1,s2, num):
    num = int(num)
    tmp1 = s1.copy()
    tmp2 = s2.copy()
    return ts_Corr(tmp1,tmp2,num) - ts_Corr2(tmp1,tmp2,num)



def  Tot_Mean(Data):
    return ts_Mean(Data,10)

def  Tot_Sum(Data):
    return ts_Sum(Data,10)


def  Tot_Stdev(Data):
    return ts_Stdev(Data, 10)

def  Tot_Delta(Data):
    return ts_Delta(Data, 10)

def  Tot_Divide(Data):
    return ts_Divide(Data, 10)

def  Tot_ChgRate(Data):
    return ts_ChgRate(Data, 10)

def  Tot_Rank(Data):
    return ts_Rank(Data, 10)

def  Tot_Min(Data):
    return ts_Min(Data, 10)

def  Tot_Max(Data):
    return ts_Max(Data, 10)

def  Tot_ArgMax(Data):
    return ts_Argmax(Data, 10)

def  Tot_ArgMin(Data):
    return ts_Argmin(Data, 10)

def GetSingleBar(aa,idxx):
    out = aa.fillna(0) * np.nan
    for v in range(int(idxx.max().max())):
        # print(v)
        Tmp = aa.shift(v)
        tmp2 = (idxx - 1) == v
        out[tmp2] = Tmp[tmp2]
    return out


#%% talib


def get_KAMA(close,num):
    # 函数名：KAMA
    # 名称： 考夫曼的自适应移动平均线
    # 简介：短期均线贴近价格走势，灵敏度高，但会有很多噪声，产生虚假信号；长期均线在判断趋势上一般比较准确 ，但是长期均线有着严重滞后的问题。我们想得到这样的均线，当价格沿一个方向快速移动时，短期的移动 平均线是最合适的；当价格在横盘的过程中，长期移动平均线是合适的。
    # NOTE: The KAMA function has an unstable period.
    # df["KAMA"] = talib.KAMA(close_p, timeperiod=30)
    # open = df_all['open']
    from talib import KAMA
    close_ = close.copy()
    close_ = close_.fillna(0)
    tmp2 = pd.DataFrame(np.nan, index = close_.index, columns = close_.columns)
    for v in tmp2.columns:
        _tmp = KAMA(close_[v],timeperiod=num)
        tmp2[v] = _tmp
    return tmp2


def get_ADOSC(close,high,low,volume,num1, num2):
    # 函数名：ADOSC
    # 名称：Chaikin A/D Oscillator Chaikin震荡指标
    # 简介：将资金流动情况与价格行为相对比，检测市场中资金流入和流出的情况
    # 计算公式：fastperiod A/D - slowperiod A/D
    # 研判：
    # 1、交易信号是背离：看涨背离做多，看跌背离做空
    # 2、股价与90天移动平均结合，与其他指标结合
    # 3、由正变负卖出，由负变正买进
    # real = ADOSC(high, low, close, volume, fastperiod=3, slowperiod=10)
    # df["ADOSC"] = talib.ADOSC(high, low, close, volume, fastperiod=3, slowperiod=10)
    from  talib import ADOSC
    
    close_ = close.copy()
    close_ = close_.fillna(0)
    high_ = high.copy()
    high_ = high_.fillna(0)
    low_ = low.copy()
    low_ = low_.fillna(0)
    volume_ = volume.copy()
    volume_ = volume_.fillna(0)
    
    num1 = int(num1)
    num2 = int(num2)
    if num2 < num1:
        num2 = num1 * 2
    tmp2 = pd.DataFrame(np.nan, index = close_.index, columns = close_.columns)
    for v in tmp2.columns:
        _tmp = ADOSC(high_[v],low_[v],close_[v], volume_[v],  fastperiod=num1, slowperiod=num2)
        tmp2[v] = _tmp
    return tmp2

def get_HT_DCPERIOD(close):
    # 名称： 希尔伯特变换-主导周期
    # 简介：将价格作为信息信号，计算价格处在的周期的位置，作为择时的依据。
    # NOTE: The HT_DCPERIOD function has an unstable period.
    # real = HT_DCPERIOD(close)
    # df["HT_DCPERIOD"] = talib.HT_DCPERIOD(close)
    from  talib import HT_DCPERIOD
    close_ = close.copy()
    close_ = close_.fillna(0)

    tmp2 = pd.DataFrame(np.nan, index = close_.index, columns = close_.columns)
    for v in tmp2.columns:
        _tmp = HT_DCPERIOD(close_[v])
        tmp2[v] = _tmp
    return tmp2


def get_HT_DCPHASE(close):
    # 名称： 希尔伯特变换-主导循环阶段
    # NOTE: The HT_DCPHASE function has an unstable period.
    # real = HT_DCPHASE(close)
    # df["HT_DCPHASE"] = talib.HT_DCPHASE(close)
    from  talib import HT_DCPHASE
    close_ = close.copy()
    close_ = close_.fillna(0)

    
    tmp2 = pd.DataFrame(np.nan, index = close_.index, columns = close_.columns)
    for v in tmp2.columns:
        _tmp = HT_DCPHASE(close_[v])
        tmp2[v] = _tmp
    return tmp2

def get_CCI(close,high,low,num):
    # CCI - Commodity Channel Index
    # 函数名：CCI
    # 名称：顺势指标
    # 简介：CCI指标专门测量股价是否已超出常态分布范围
    # real = CCI(high, low, close, timeperiod=14)
    # df["CCI"] = talib.CCI(high, low, close, timeperiod=14)
    from  talib import CCI
    num = int(num)
    close_ = close.copy()
    close_ = close_.fillna(0)
    high_ = high.copy()
    high_ = high_.fillna(0)
    low_ = low.copy()
    low_ = low_.fillna(0)

    tmp2 = pd.DataFrame(np.nan, index = close_.index, columns = close_.columns)
    for v in tmp2.columns:
        _tmp = CCI(high_[v], low_[v], close_[v], timeperiod=num)
        tmp2[v] = _tmp
    return tmp2


def get_CMO(close,num):
    # 名称：钱德动量摆动指标
    # 简介：与其他动量指标摆动指标如相对强弱指标（RSI）和随机指标（KDJ）不同，钱德动量指标在计算公式的分子中采用上涨日和下跌日的数据。 计算公式：CMO=（Su－Sd）*100/（Su+Sd）
    # 其中：Su是今日收盘价与昨日收盘价（上涨日）差值加总。若当日下跌，则增加值为0；Sd是今日收盘价与做日收盘价（下跌日）差值的绝对值加总。若当日上涨，则增加值为0；
    # 指标应用
    # 本指标类似RSI指标。
    # 当本指标下穿-50水平时是买入信号，上穿+50水平是卖出信号。
    # 钱德动量摆动指标的取值介于-100和100之间。
    # 本指标也能给出良好的背离信号。
    # 当股票价格创出新低而本指标未能创出新低时，出现牛市背离；
    # 当股票价格创出新高而本指标未能创出新高时，当出现熊市背离时。
    # 我们可以用移动均值对该指标进行平滑。
    # NOTE: The CMO function has an unstable period.
    # real = CMO(close, timeperiod=14)
    # df["CMO"] = talib.CMO(close, timeperiod=14)
    from  talib import CMO
    num = int(num)
    close_ = close.copy()
    close_ = close_.fillna(0)

    tmp2 = pd.DataFrame(np.nan, index = close_.index, columns = close_.columns)
    for v in tmp2.columns:
        _tmp = CMO(close_[v], timeperiod=num)
        tmp2[v] = _tmp
    return tmp2

def get_DX(close,high,low,num):
    # DX - Directional Movement Index DMI指标又叫动向指标或趋向指标
    # 函数名：DX
    # 名称：动向指标或趋向指标
    # 简介：通过分析股票价格在涨跌过程中买卖双方力量均衡点的变化情况，即多空双方的力量的变化受价格波动的影响而发生由均衡到失衡的循环过程，从而提供对趋势判断依据的一种技术指标。
    # 分析和应用：百度百科 维基百科 同花顺学院
    # NOTE: The DX function has an unstable period.
    # real = DX(high, low, close, timeperiod=14)
    # df["DX"] = talib.DX(high, low, close, timeperiod=14)
    from  talib import DX
    num = int(num)
    close_ = close.copy()
    close_ = close_.fillna(0)
    high_ = high.copy()
    high_ = high_.fillna(0)
    low_ = low.copy()
    low_ = low_.fillna(0)
    tmp2 = pd.DataFrame(np.nan, index = close_.index, columns = close_.columns)
    for v in tmp2.columns:
        _tmp = DX(high_[v], low_[v], close_[v], timeperiod=num)
        tmp2[v] = _tmp
    return tmp2

def get_MINUS_DI(close,high,low,num):
    # MINUS_DI - Minus Directional Indicator
    # 函数名：DMI 中的DI指标 负方向指标
    # 名称：下升动向值
    # 简介：通过分析股票价格在涨跌过程中买卖双方力量均衡点的变化情况，即多空双方的力量的变化受价格波动的影响而发生由均衡到失衡的循环过程，从而提供对趋势判断依据的一种技术指标。
    # real = MINUS_DI(high, low, close, timeperiod=14)
    # df["MINUS_DI"] = talib.MINUS_DI(high, low, close, timeperiod=14)
    from  talib import MINUS_DI
    num = int(num)
    close_ = close.copy()
    close_ = close_.fillna(0)
    high_ = high.copy()
    high_ = high_.fillna(0)
    low_ = low.copy()
    low_ = low_.fillna(0)
    tmp2 = pd.DataFrame(np.nan, index = close_.index, columns = close_.columns)
    for v in tmp2.columns:
        _tmp = MINUS_DI(high_[v], low_[v], close_[v], timeperiod=num)
        tmp2[v] = _tmp
    return tmp2

def get_MINUS_DM(high,low,num):
    # MINUS_DM - Minus Directional Movement
    # 函数名：MINUS_DM
    # 名称： 上升动向值 DMI中的DM代表正趋向变动值即上升动向值
    # 简介：通过分析股票价格在涨跌过程中买卖双方力量均衡点的变化情况，即多空双方的力量的变化受价格波动的影响而发生由均衡到失衡的循环过程，从而提供对趋势判断依据的一种技术指标。
    # 分析和应用：百度百科 维基百科 同花顺学院
    # NOTE: The MINUS_DM function has an unstable period.
    # real = MINUS_DM(high, low, timeperiod=14)
    # df["MINUS_DM"] = talib.MINUS_DM(high, low, timeperiod=14)
    from  talib import MINUS_DM
    num = int(num)
    high_ = high.copy()
    high_ = high_.fillna(0)
    low_ = low.copy()
    low_ = low_.fillna(0)
    tmp2 = pd.DataFrame(np.nan, index = high_.index, columns = high_.columns)
    for v in tmp2.columns:
        _tmp = MINUS_DM(high_[v], low_[v], timeperiod=num)
        tmp2[v] = _tmp
    return tmp2

def get_ULTOSC(close,high,low,num1,num2,num3):
    # ULTOSC - Ultimate Oscillator 终极波动指标
    # 函数名：ULTOSC
    # 名称：终极波动指标
    # 简介：UOS是一种多方位功能的指标，除了趋势确认及超买超卖方面的作用之外，它的“突破”讯号不仅可以提供最适当的交易时机之外，更可以进一步加强指标的可靠度。
    # 分析和应用：百度百科 同花顺学院
    # real = ULTOSC(high, low, close, timeperiod1=7, timeperiod2=14, timeperiod3=28)
    # df["ULTOSC"] = talib.ULTOSC(high, low, close, timeperiod1=7, timeperiod2=14, timeperiod3=28)
    from  talib import ULTOSC
    num1 = int(num1)
    num2 = int(num2)
    num3 = int(num3)
    close_ = close.copy()
    close_ = close_.fillna(0)
    high_ = high.copy()
    high_ = high_.fillna(0)
    low_ = low.copy()
    low_ = low_.fillna(0)
    tmp2 = pd.DataFrame(np.nan, index = close_.index, columns = close_.columns)
    for v in tmp2.columns:
        _tmp = ULTOSC(high_[v], low_[v], close_[v], timeperiod1=num1, timeperiod2=num2, timeperiod3=num3)
        tmp2[v] = _tmp
    return tmp2


def get_LINEARREG_ANGLE(close,num):
    # LINEARREG_ANGLE - Linear Regression Angle
    # 函数名：LINEARREG_ANGLE
    # 名称：线性回归的角度
    # 简介：来确定价格的角度变化. 参考
    # real = LINEARREG_ANGLE(close, timeperiod=14)
    # df["LINEARREG_ANGLE"] = talib.LINEARREG_ANGLE(close, timeperiod=14)
    from  talib import LINEARREG_ANGLE
    num = int(num)
    close_ = close.copy()
    close_ = close_.fillna(0)
    tmp2 = pd.DataFrame(np.nan, index = close_.index, columns = close_.columns)
    for v in tmp2.columns:
        _tmp = LINEARREG_ANGLE(close_[v], timeperiod=num)
        tmp2[v] = _tmp
    return tmp2

def get_LINEARREG_SLOPE(close,num):
    # LINEARREG_SLOPE - Linear Regression Slope
    # 函数名：LINEARREG_SLOPE
    # 名称：线性回归斜率指标
    # real = LINEARREG_SLOPE(close, timeperiod=14)
    # df["LINEARREG_SLOPE"] = talib.LINEARREG_SLOPE(close, timeperiod=14)
    from talib import LINEARREG_ANGLE
    num = int(num)
    close_ = close.copy()
    close_ = close_.fillna(0)
    tmp2 = pd.DataFrame(np.nan, index = close_.index, columns = close_.columns)
    for v in tmp2.columns:
        _tmp = LINEARREG_ANGLE(close_[v], timeperiod=num)
        tmp2[v] = _tmp
    return tmp2


def get_ls_post2(f_D2):
    z_up = f_D2.copy()
    z_dn = f_D2.copy()
    z_up[z_up < 0] = 0
    z_dn[z_dn > 0] = 0
    z_up = z_up.div(z_up.sum(1), axis=0)
    z_dn = z_dn.div(z_dn.sum(1), axis=0) * -1
    return z_up.fillna(0) + z_dn.fillna(0), z_up.fillna(0), z_dn.fillna(0)



def calculate_fitness10_par(v, df ,rate_ave, rate, delayNum, universe,population):
    _expr = population[v]
    f = eval(_expr) 
    f = f.replace([np.inf, -np.inf], np.nan)
    f[universe == 0] = np.nan
    f = pn_TransNorm(f)
    f, f_up, f_dn = get_ls_post2(f)
    rets = (f.shift(delayNum) * rate).sum(1)
    # rets[:100] = np.nan
    rets_up = (f_up.shift(delayNum) * rate).sum(1) - rate_ave
    rets_dn = (f_dn.shift(delayNum) * rate).sum(1) + rate_ave
    to = round(((f.fillna(0) - f.shift(1).fillna(0)).abs().sum(1) / (f.fillna(0).abs().sum(1))).mean(), 2)
    rets[:100] = 0
    rets_up[:100] = 0
    rets_dn[:100] = 0
    # print( to,_expr3)
    
    return (f, f_up, f_dn, rets, rets_up, rets_dn , to)


def get_portFromFactor_both(f, num):
    f_ = f.copy()
    f_2 = f_.rank(axis=1,ascending=False,method = 'first')
    hold = f_.fillna(0) * 0
    hold[f_2 <= num] = 1
    hold = hold / Repmat(hold,hold.sum(1))
    hold2 = f_.fillna(0) * 0
    hold2[f_2 > Repmat(f_2,f_2.max(1)) - num] = 1
    hold2 = hold2 / Repmat(hold2,hold2.sum(1)) *-1
    return hold,hold2


def calculate_fitness9_neut_par(v, df ,rate_ave, rate, delayNum, universe, population):
    _expr = population[v]
    f = eval(_expr) 
    f = f.replace([np.inf, -np.inf], np.nan)
    f[universe == 0] = np.nan
    f = pn_TransNorm(f)
    f_up,f_dn = get_portFromFactor_both(f, 50 )       # 获取多空组合 待优化（）减少换手
    f = f_up + f_dn

    rets = (f.shift(delayNum) * rate).sum(1)
    # rets[:100] = np.nan
    rets_up = (f_up.shift(delayNum) * rate).sum(1) - rate_ave
    rets_dn = (f_dn.shift(delayNum) * rate).sum(1) + rate_ave
    rets[:100] = 0
    rets_up[:100] = 0
    rets_dn[:100] = 0
    # print( to,_expr3)
    return (f, f_up, f_dn, rets, rets_up, rets_dn)



def calculate_fitness9_neut3(_expr3, df ,rate_ave, rate, delayNum, universe, barraFactor):
    _expr = _expr3
    f = eval(_expr) 
    f = f.replace([np.inf, -np.inf], np.nan)
    f[universe == 0] = np.nan
    f = pn_TransNorm(f)
    # f = f.fillna(0)
    
    for v in barraFactor.keys():
        f = pn_CrossFit(barraFactor[v],f)
        f = pn_TransNorm(f)
    
    f, f_up,f_dn  = get_ls_post2(f)
    # f_up,f_dn = get_portFromFactor_both(f, 50  )       # 获取多空组合 待优化（）减少换手
    # f = f_up + f_dn

    rets = (f.shift(delayNum) * rate).sum(1)
    
    # rets[:100] = np.nan
    rets_up = (f_up.shift(delayNum) * rate).sum(1) - rate_ave
    rets_dn = (f_dn.shift(delayNum) * rate).sum(1) + rate_ave
    rets[:100] = 0
    rets_up[:100] = 0
    rets_dn[:100] = 0
    # print( to,_expr3)
    return (f, f_up, f_dn, rets, rets_up, rets_dn)

def get_ccf_1m_p1_neut(df, toTest1, rate, delayNum, barraFactor, sd, universe,ispic):
    # 算每一个因子值， 计算他们的多空收益
    vals = {}
    vals_up = {}
    vals_dn = {}
    returns = pd.DataFrame()
    returns_up = pd.DataFrame()
    returns_dn = pd.DataFrame()
    rate2 = rate.copy()
    rate2[universe == 0] = np.nan
    rate_ave = rate2.mean(1)
    rate_ave[:10] = np.nan
    population = toTest1
    for v in range(len(population)):
        _expr = population[v]
        # print(_expr)
        # vals[_expr], vals_up[_expr] , vals_dn[_expr] , returns[_expr] , returns_up[_expr] , returns_dn[_expr] = calculate_fitness9_neut2(_expr, df, rate_ave, rate, delayNum, universe, barraFactor )
        vals[_expr], vals_up[_expr] , vals_dn[_expr] , returns[_expr] , returns_up[_expr] , returns_dn[_expr] = calculate_fitness9_neut3(_expr, df, rate_ave, rate, delayNum, universe, barraFactor )
            
    return  vals, vals_up, vals_dn, returns, returns_up, returns_dn, universe



def get_ccf_1m_p1_neut_par(df, toTest1, rate, delayNum, barraFactor, universe, temp_folder):
    rate2 = rate.copy()
    rate2[universe == 0] = np.nan
    rate_ave = rate2.mean(1)
    rate_ave[:10] = np.nan
    population = toTest1
    import joblib
    results = joblib.Parallel(n_jobs= os.cpu_count(), temp_folder=temp_folder )(
        joblib.delayed(calculate_fitness9_neut3)(_expr, df, rate_ave, rate, delayNum, universe, barraFactor ) for _expr in population  )
    f, f_up, f_dn, rets, rets_up, rets_dn = zip(*results)

    vals = {}
    returns = pd.DataFrame()
    for v in range(len(population)):
        vals[population[v]] = f[v]
        returns[population[v]] = rets[v]

    return  vals, vals, vals, returns, returns, returns, universe