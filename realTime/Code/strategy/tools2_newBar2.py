from datetime import datetime, UTC, timedelta
import os
import pandas as pd
import numpy as np

from pathlib import Path
home_dir = str(Path.home())

# 普通时间转换会iso时间

def timeConvert1(timestamp_str):
    # timestamp_str = '2025-03-12 07:05:00'
    if not type(timestamp_str) == str:
        timestamp_str = str(timestamp_str)
    dt = datetime.strptime(timestamp_str, '%Y-%m-%d %H:%M:%S')
    iso_format = dt.strftime('%Y-%m-%dT%H:%M:%SZ')
    # iso_format = datetime.strptime(iso_format, '%Y-%m-%dT%H:%M:%SZ')
    return iso_format


# 获取当前的 ISO 8601 时间
def get_nowISO():
    now_iso = datetime.now(UTC).strftime('%Y-%m-%dT%H:%M:%SZ')
    # now = datetime.strptime(now_iso, '%Y-%m-%dT%H:%M:%SZ')
    return now_iso


def get_nowISO2():
    now_iso = datetime.now(UTC)
    # now = datetime.strptime(now_iso, '%Y-%m-%dT%H:%M:%SZ')
    return now_iso

def get_timedelta(now,iso_format):
    iso_format = datetime.strptime(iso_format, '%Y-%m-%dT%H:%M:%SZ')
    now = datetime.strptime(now, '%Y-%m-%dT%H:%M:%SZ')
    time_difference = (now - iso_format ).total_seconds()
    # 计算 1 分钟 的间隔数
    five_minute_intervals = int(time_difference // (1 * 60))
    return five_minute_intervals

def get_startTime(de):
    now = datetime.now(UTC)
    now_iso = now.strftime('%Y-%m-%dT%H:%M:%SZ')

    # 计算 1000 个 1 分钟的总时间差
    time_difference = timedelta(minutes=1 * de)

    # 减去时间差
    result_time = now - time_difference

    # 转换为 ISO 8601 格式
    result_iso = result_time.strftime('%Y-%m-%dT%H:%M:%SZ')
    return result_iso

def get_contracts():
    return ['NEIROETH/USDT:USDT', 'HIGH/USDT:USDT', 'HIPPO/USDT:USDT', 'PYR/USDT:USDT', 'RENDER/USDT:USDT', 'HMSTR/USDT:USDT', 'RLC/USDT:USDT', 'SPEC/USDT:USDT', 'LRC/USDT:USDT', 'FIDA/USDT:USDT', 'OXT/USDT:USDT', 'VVV/USDT:USDT', 'ZEN/USDT:USDT', '10000000AIDOGE/USDT:USDT', 'ELX/USDT:USDT', 'BMT/USDT:USDT', 'CHR/USDT:USDT', 'SD/USDT:USDT', 'RAYDIUM/USDT:USDT', 'MAGIC/USDT:USDT', 'QUICK/USDT:USDT', 'ORBS/USDT:USDT', 'HEI/USDT:USDT', 'DOGS/USDT:USDT', 'CRV/USDT:USDT', 'FLOW/USDT:USDT', 'IOTA/USDT:USDT', 'MAV/USDT:USDT', 'ALICE/USDT:USDT', 'PEAQ/USDT:USDT', 'STPT/USDT:USDT', 'BOBA/USDT:USDT', 'GOAT/USDT:USDT', 'SCR/USDT:USDT', 'AIOZ/USDT:USDT', 'ENA/USDT:USDT', 'CARV/USDT:USDT', 'MLN/USDT:USDT', 'LISTA/USDT:USDT', 'CRO/USDT:USDT', 'XTZ/USDT:USDT', 'SAFE/USDT:USDT', '1000LUNC/USDT:USDT', 'LUMIA/USDT:USDT', 'RIF/USDT:USDT', 'DUCK/USDT:USDT', 'SERAPH/USDT:USDT', 'GRIFFAIN/USDT:USDT', 'TSTBSC/USDT:USDT', 'PROM/USDT:USDT', 'XVG/USDT:USDT', 'VIC/USDT:USDT', '1000X/USDT:USDT', 'BSW/USDT:USDT', 'DEGEN/USDT:USDT', 'ILV/USDT:USDT', 'ZETA/USDT:USDT', 'SSV/USDT:USDT', 'OM/USDT:USDT', 'PRIME/USDT:USDT', 'BANANA/USDT:USDT', 'YFI/USDT:USDT', 'PROS/USDT:USDT', 'EIGEN/USDT:USDT', 'STEEM/USDT:USDT', 'GUN/USDT:USDT', 'PUMP/USDT:USDT', 'KNC/USDT:USDT', 'GLMR/USDT:USDT', 'MAVIA/USDT:USDT', 'CORE/USDT:USDT', 'ZEUS/USDT:USDT', 'XAI/USDT:USDT', 'NEO/USDT:USDT', 'WIF/USDT:USDT', 'ZEC/USDT:USDT', '1000CAT/USDT:USDT', 'AVAX/USDT:USDT', 'BAKE/USDT:USDT', 'SCA/USDT:USDT', 'FLR/USDT:USDT', 'ASTR/USDT:USDT', 'ZRC/USDT:USDT', 'AVA/USDT:USDT', 'BAN/USDT:USDT', 'STG/USDT:USDT', 'ALCH/USDT:USDT', 'COOKIE/USDT:USDT', 'SWELL/USDT:USDT', 'SLERF/USDT:USDT', 'BSV/USDT:USDT', 'RAD/USDT:USDT', 'MBOX/USDT:USDT', 'XTER/USDT:USDT', 'SOLAYER/USDT:USDT', 'HPOS10I/USDT:USDT', 'STX/USDT:USDT', 'SUI/USDT:USDT', 'APT/USDT:USDT', 'DBR/USDT:USDT', 'TWT/USDT:USDT', 'ZKJ/USDT:USDT', 'PEOPLE/USDT:USDT', 'PHA/USDT:USDT', 'TIA/USDT:USDT', 'ATH/USDT:USDT', 'BAND/USDT:USDT', 'SCRT/USDT:USDT', 'XLM/USDT:USDT', 'ROAM/USDT:USDT', 'OMNI/USDT:USDT', '1000BTT/USDT:USDT', 'JELLYJELLY/USDT:USDT', 'MASK/USDT:USDT', 'OGN/USDT:USDT', 'DOT/USDT:USDT', '1000XEC/USDT:USDT', 'AXS/USDT:USDT', 'CELR/USDT:USDT', 'REQ/USDT:USDT', 'GMT/USDT:USDT', 'BRETT/USDT:USDT', 'L3/USDT:USDT', 'RED/USDT:USDT', 'MBL/USDT:USDT', 'LUNA2/USDT:USDT', 'MVL/USDT:USDT', 'TRB/USDT:USDT', 'BIO/USDT:USDT', 'BEAM/USDT:USDT', '10000COQ/USDT:USDT', 'J/USDT:USDT', 'POWR/USDT:USDT', 'PARTI/USDT:USDT', 'AI/USDT:USDT', 'ZBCN/USDT:USDT', 'EGLD/USDT:USDT', '1000FLOKI/USDT:USDT', 'VANA/USDT:USDT', 'EOS/USDT:USDT', 'NFP/USDT:USDT', 'CLOUD/USDT:USDT', 'NC/USDT:USDT', 'ARK/USDT:USDT', 'DOGE/USDT:USDT', 'ADA/USDT:USDT', 'MEMEFI/USDT:USDT', 'QNT/USDT:USDT', 'PIXEL/USDT:USDT', 'PRCL/USDT:USDT', 'CTK/USDT:USDT', '1000NEIROCTO/USDT:USDT', 'BEL/USDT:USDT', 'COW/USDT:USDT', 'F/USDT:USDT', 'ETHW/USDT:USDT', 'COS/USDT:USDT', 'XNO/USDT:USDT', 'NOT/USDT:USDT', 'LUCE/USDT:USDT', 'PLUME/USDT:USDT', 'MOCA/USDT:USDT', 'TAIKO/USDT:USDT', 'SXP/USDT:USDT', 'ARPA/USDT:USDT', 'BADGER/USDT:USDT', 'AERGO/USDT:USDT', 'ARKM/USDT:USDT', 'HOT/USDT:USDT', 'HFT/USDT:USDT', 'UXLINK/USDT:USDT', 'SWEAT/USDT:USDT', 'FARTCOIN/USDT:USDT', 'CETUS/USDT:USDT', 'IDEX/USDT:USDT', 'MOBILE/USDT:USDT', 'DUSK/USDT:USDT', 'REZ/USDT:USDT', 'ANIME/USDT:USDT', 'PHB/USDT:USDT', 'IOST/USDT:USDT', 'DYM/USDT:USDT', 'HBAR/USDT:USDT', 'SEND/USDT:USDT', 'T/USDT:USDT', 'ONT/USDT:USDT', 'AVAAI/USDT:USDT', '10000LADYS/USDT:USDT', '1000RATS/USDT:USDT', 'THETA/USDT:USDT', 'JST/USDT:USDT', 'ALT/USDT:USDT', 'MELANIA/USDT:USDT', 'BICO/USDT:USDT', 'COMP/USDT:USDT', 'SYN/USDT:USDT', 'ZK/USDT:USDT', 'IMX/USDT:USDT', 'FORM/USDT:USDT', 'BERA/USDT:USDT', 'ONDO/USDT:USDT', 'TUT/USDT:USDT', 'MOVR/USDT:USDT', 'WOO/USDT:USDT', '1000TOSHI/USDT:USDT', 'AUDIO/USDT:USDT', 'FB/USDT:USDT', 'LTC/USDT:USDT', 'PNUT/USDT:USDT', 'GRT/USDT:USDT', 'B3/USDT:USDT', 'MEW/USDT:USDT', 'PENGU/USDT:USDT', 'MYRIA/USDT:USDT', 'LQTY/USDT:USDT', 'VANRY/USDT:USDT', 'ACX/USDT:USDT', 'PYTH/USDT:USDT', 'MORPHO/USDT:USDT', 'POL/USDT:USDT', 'XVS/USDT:USDT', 'POLYX/USDT:USDT', 'GTC/USDT:USDT', 'NIL/USDT:USDT', 'CVC/USDT:USDT', 'PROMPT/USDT:USDT', 'MANA/USDT:USDT', 'ZRX/USDT:USDT', 'SIREN/USDT:USDT', '1000000MOG/USDT:USDT', 'AVL/USDT:USDT', 'SYS/USDT:USDT', 'DYDX/USDT:USDT', 'TRUMP/USDT:USDT', 'PERP/USDT:USDT', 'VTHO/USDT:USDT', 'SKL/USDT:USDT', 'MAJOR/USDT:USDT', 'YGG/USDT:USDT', 'ACT/USDT:USDT', 'BABY/USDT:USDT', 'CFX/USDT:USDT', 'THE/USDT:USDT', 'XRP/USDT:USDT', 'CHZ/USDT:USDT', 'INJ/USDT:USDT', 'KDA/USDT:USDT', 'JAILSTOOL/USDT:USDT', 'ONG/USDT:USDT', 'BNB/USDT:USDT', 'USTC/USDT:USDT', 'AGI/USDT:USDT', 'APE/USDT:USDT', 'TRX/USDT:USDT', 'FIL/USDT:USDT', 'CGPT/USDT:USDT', 'ETHBTC/USDT:USDT', 'GRASS/USDT:USDT', 'SWARMS/USDT:USDT', 'XION/USDT:USDT', 'BB/USDT:USDT', 'SAGA/USDT:USDT', 'AR/USDT:USDT', 'FWOG/USDT:USDT', 'HIFI/USDT:USDT', 'ALGO/USDT:USDT', 'ETC/USDT:USDT', 'AKT/USDT:USDT', '1000MUMU/USDT:USDT', 'STRK/USDT:USDT', 'VET/USDT:USDT', 'UMA/USDT:USDT', 'SNT/USDT:USDT', 'OG/USDT:USDT', 'ACE/USDT:USDT', 'ACH/USDT:USDT', 'AIXBT/USDT:USDT', 'CTSI/USDT:USDT', 'FLOCK/USDT:USDT', 'RONIN/USDT:USDT', 'TON/USDT:USDT', 'RARE/USDT:USDT', 'CELO/USDT:USDT', 'SNX/USDT:USDT', 'G/USDT:USDT', 'TAO/USDT:USDT', 'WAXP/USDT:USDT', 'DENT/USDT:USDT', 'TRU/USDT:USDT', 'HIVE/USDT:USDT', '1000000BABYDOGE/USDT:USDT', 'GLM/USDT:USDT', 'RVN/USDT:USDT', 'LOOKS/USDT:USDT', 'NEAR/USDT:USDT', 'RDNT/USDT:USDT', 'SHELL/USDT:USDT', 'XCN/USDT:USDT', 'ID/USDT:USDT', 'CPOOL/USDT:USDT', 'SUSHI/USDT:USDT', 'AVAIL/USDT:USDT', 'COTI/USDT:USDT', 'CAKE/USDT:USDT', 'ICX/USDT:USDT', 'ORDER/USDT:USDT', 'VRA/USDT:USDT', 'WLD/USDT:USDT', 'VR/USDT:USDT', 'BR/USDT:USDT', 'MERL/USDT:USDT', 'ANKR/USDT:USDT', 'MOVE/USDT:USDT', 'FXS/USDT:USDT', 'CTC/USDT:USDT', 'FOXY/USDT:USDT', 'GALA/USDT:USDT', 'AXL/USDT:USDT', 'ORCA/USDT:USDT', 'FLM/USDT:USDT', 'S/USDT:USDT', 'GPS/USDT:USDT', 'A8/USDT:USDT', 'ATOM/USDT:USDT', 'JUP/USDT:USDT', '1000BONK/USDT:USDT', 'BCH/USDT:USDT', 'BROCCOLI/USDT:USDT', 'DGB/USDT:USDT', 'XRD/USDT:USDT', 'SHIB1000/USDT:USDT', 'ENJ/USDT:USDT', 'MINA/USDT:USDT', 'MTL/USDT:USDT', 'DASH/USDT:USDT', 'PONKE/USDT:USDT', 'DATA/USDT:USDT', 'CHILLGUY/USDT:USDT', 'AGLD/USDT:USDT', 'CHESS/USDT:USDT', 'ME/USDT:USDT', 'FTN/USDT:USDT', '1000TURBO/USDT:USDT', 'MUBARAK/USDT:USDT', 'ARB/USDT:USDT', 'DEEP/USDT:USDT', 'BANANAS31/USDT:USDT', 'SC/USDT:USDT', 'KMNO/USDT:USDT', 'SOLO/USDT:USDT', 'RSS3/USDT:USDT', 'KAIA/USDT:USDT', 'TAI/USDT:USDT', 'EPIC/USDT:USDT', 'BAT/USDT:USDT', 'GODS/USDT:USDT', 'MAX/USDT:USDT', 'WAVES/USDT:USDT', 'SUNDOG/USDT:USDT', 'ROSE/USDT:USDT', 'BAL/USDT:USDT', 'OL/USDT:USDT', 'RPL/USDT:USDT', 'VELO/USDT:USDT', 'HYPE/USDT:USDT', 'CYBER/USDT:USDT', 'AI16Z/USDT:USDT', 'FLUX/USDT:USDT', 'MNT/USDT:USDT', 'BLAST/USDT:USDT', 'XMR/USDT:USDT', 'OSMO/USDT:USDT', 'OMG/USDT:USDT', 'DRIFT/USDT:USDT', 'SUPER/USDT:USDT', 'VELODROME/USDT:USDT', 'AEVO/USDT:USDT', 'LPT/USDT:USDT', 'ICP/USDT:USDT', 'SEI/USDT:USDT', 'MKR/USDT:USDT', 'NMR/USDT:USDT', 'SPELL/USDT:USDT', 'LSK/USDT:USDT', 'FUEL/USDT:USDT', 'MANEKI/USDT:USDT', 'MICHI/USDT:USDT', 'ORDI/USDT:USDT', 'CVX/USDT:USDT', 'GOMINING/USDT:USDT', 'KAS/USDT:USDT', 'FIO/USDT:USDT', 'ARC/USDT:USDT', 'ALPACA/USDT:USDT', 'AERO/USDT:USDT', 'REX/USDT:USDT', 'DODO/USDT:USDT', 'DOG/USDT:USDT', 'ZENT/USDT:USDT', 'LINK/USDT:USDT', 'PAXG/USDT:USDT', 'MOODENG/USDT:USDT', 'USUAL/USDT:USDT', 'NKN/USDT:USDT', 'SOL/USDT:USDT', 'SLP/USDT:USDT', 'KAVA/USDT:USDT', 'KSM/USDT:USDT', '10000SATS/USDT:USDT', 'BLUR/USDT:USDT', 'ZEREBRO/USDT:USDT', 'GMX/USDT:USDT', 'SUN/USDT:USDT', 'ETH/USDT:USDT', '1000CATS/USDT:USDT', 'TLM/USDT:USDT', 'ZRO/USDT:USDT', 'HNT/USDT:USDT', 'OP/USDT:USDT', '1000000CHEEMS/USDT:USDT', 'SFP/USDT:USDT', 'XAUT/USDT:USDT', 'VOXEL/USDT:USDT', 'ETHFI/USDT:USDT', 'CKB/USDT:USDT', 'QTUM/USDT:USDT', 'WAL/USDT:USDT', 'SPX/USDT:USDT', 'STORJ/USDT:USDT', 'ONE/USDT:USDT', 'BOME/USDT:USDT', '10000WEN/USDT:USDT', 'SAND/USDT:USDT', 'HOOK/USDT:USDT', 'MEME/USDT:USDT', 'LDO/USDT:USDT', 'SLF/USDT:USDT', 'RSR/USDT:USDT', 'CATI/USDT:USDT', '1INCH/USDT:USDT', 'RUNE/USDT:USDT', 'PUFFER/USDT:USDT', 'NTRN/USDT:USDT', 'PORTAL/USDT:USDT', 'ALPHA/USDT:USDT', 'IO/USDT:USDT', 'C98/USDT:USDT', 'SOLV/USDT:USDT', 'BUZZ/USDT:USDT', 'QI/USDT:USDT', 'AAVE/USDT:USDT', 'PIPPIN/USDT:USDT', 'VIDT/USDT:USDT', 'BIGTIME/USDT:USDT', 'ATA/USDT:USDT', 'KOMA/USDT:USDT', 'TNSR/USDT:USDT', '10000WHY/USDT:USDT', 'COOK/USDT:USDT', 'W/USDT:USDT', 'AUCTION/USDT:USDT', 'MDT/USDT:USDT', '1000APU/USDT:USDT', 'VIRTUAL/USDT:USDT', 'IOTX/USDT:USDT', 'JASMY/USDT:USDT', 'KAITO/USDT:USDT', 'FORTH/USDT:USDT', 'SONIC/USDT:USDT', 'XDC/USDT:USDT', '10000ELON/USDT:USDT', 'API3/USDT:USDT', 'NS/USDT:USDT', 'POPCAT/USDT:USDT', 'GIGA/USDT:USDT', 'ALU/USDT:USDT', 'DEXE/USDT:USDT', 'LEVER/USDT:USDT', 'UNI/USDT:USDT', 'BNT/USDT:USDT', 'ALEO/USDT:USDT', 'GAS/USDT:USDT', '1000000PEIPEI/USDT:USDT', '1000PEPE/USDT:USDT', 'ZIL/USDT:USDT', 'EDU/USDT:USDT', 'JTO/USDT:USDT', 'VINE/USDT:USDT', 'PENDLE/USDT:USDT', 'GNO/USDT:USDT', 'XEM/USDT:USDT', 'TOKEN/USDT:USDT', 'METIS/USDT:USDT', 'MANTA/USDT:USDT', 'ENS/USDT:USDT', 'IP/USDT:USDT', 'BTC/USDT:USDT', 'JOE/USDT:USDT', '10000QUBIC/USDT:USDT', 'MYRO/USDT:USDT', 'XCH/USDT:USDT', 'MASA/USDT:USDT']

def get_usdt_contracts_by_ccxt():
    import ccxt
    # 初始化 Bybit 交易所
    exchange = ccxt.bybit()

    # 获取所有市场信息
    markets = exchange.load_markets()

    # 筛选以 USDT 为计价单位的合约
    usdt_contracts = [symbol for symbol in markets if symbol.endswith("/USDT:USDT")]

    return list(usdt_contracts)

def get_initialData(path, lookback):
    if os.path.exists(path + 'open' + '.pkl'):
        cut = 60*2
        Open = pd.read_pickle(path + 'open' + '.pkl').replace({None: np.nan}).iloc[:-cut]
        High = pd.read_pickle(path + 'high' + '.pkl').replace({None: np.nan}).iloc[:-cut]
        Low = pd.read_pickle(path + 'low' + '.pkl').replace({None: np.nan}).iloc[:-cut]
        Close = pd.read_pickle(path + 'close' + '.pkl').replace({None: np.nan}).iloc[:-cut]
        Volume = pd.read_pickle(path + 'volume' + '.pkl').replace({None: np.nan}).iloc[:-cut]
        
        cut = ['USDE/USDT:USDT','USDC/USDT:USDT','FDUSD/USDT:USDT','PYUSD/USDT:USDT','DAI/USDT:USDT',  # 稳定币
                 'NULS/USDT:USDT', 'TROY/USDT:USDT','BUZZ/USDT:USDT','PROS/USDT:USDT','VIDT/USDT:USDT','JAILSTOOL/USDT:USDT']          # 下架币
        left = list(set(list(Open.columns)) - set(cut))
        Open = Open[left]
        High = High[left]
        Low = Low[left]
        Close = Close[left]
        Volume = Volume[left]
    
        timeToBeUpdate = get_timedelta(get_nowISO(),timeConvert1(Open.index[-1]))
    else:
        # usdt_contracts = get_contracts() 
        usdt_contracts = get_usdt_contracts_by_ccxt()
        Open = pd.DataFrame(columns = usdt_contracts )
        High = pd.DataFrame(columns = usdt_contracts )
        Low = pd.DataFrame(columns = usdt_contracts )
        Close = pd.DataFrame(columns = usdt_contracts )
        Volume = pd.DataFrame(columns = usdt_contracts )
        timeToBeUpdate = lookback
    return  Open, High, Low, Close, Volume, timeToBeUpdate


def get_initialData2(path, lookback):
    if os.path.exists(path + 'open' + '.pkl'):
        cut = 60*2
        Open = pd.read_pickle(path + 'open' + '.pkl').replace({None: np.nan}).iloc[:-cut]
        Open = Open.tail(80000)
        High = pd.read_pickle(path + 'high' + '.pkl').replace({None: np.nan}).iloc[:-cut]
        High = High.tail(80000)
        Low = pd.read_pickle(path + 'low' + '.pkl').replace({None: np.nan}).iloc[:-cut]
        Low = Low.tail(80000)
        Close = pd.read_pickle(path + 'close' + '.pkl').replace({None: np.nan}).iloc[:-cut]
        Close = Close.tail(80000)
        Volume = pd.read_pickle(path + 'volume' + '.pkl').replace({None: np.nan}).iloc[:-cut]
        Volume = Volume.tail(80000)
        
        cut = ['USDE/USDT:USDT','USDC/USDT:USDT','FDUSD/USDT:USDT','PYUSD/USDT:USDT','DAI/USDT:USDT',  # 稳定币
                 'NULS/USDT:USDT', 'TROY/USDT:USDT','BUZZ/USDT:USDT','PROS/USDT:USDT','VIDT/USDT:USDT','JAILSTOOL/USDT:USDT'  # 下架币
                 'LUCE/USDT:USDT', '10000000AIDOGE/USDT:USDT','MAX/USDT:USDT' ,'MANEKI/USDT:USDT',   # 下架币 # 20250430
                 'MEMEFI/USDT:USDT','VRA/USDT:USDT',   # 下架币 # 20250513
                 ]         
        left = list(set(list(Open.columns)) - set(cut))
        Open = Open[left]
        High = High[left]
        Low = Low[left]
        Close = Close[left]
        Volume = Volume[left]
        
        # Open = Open[Open.index > sd0]
        # High = High[High.index > sd0]
        # Low = Low[Low.index > sd0]
        # Close = Close[Close.index > sd0]
        # Volume = Volume[Volume.index > sd0]

        timeToBeUpdate = get_timedelta(get_nowISO(),timeConvert1(Open.index[-1]))
    else:
        # usdt_contracts = get_contracts() 
        usdt_contracts = get_usdt_contracts_by_ccxt()
        Open = pd.DataFrame(columns = usdt_contracts )
        High = pd.DataFrame(columns = usdt_contracts )
        Low = pd.DataFrame(columns = usdt_contracts )
        Close = pd.DataFrame(columns = usdt_contracts )
        Volume = pd.DataFrame(columns = usdt_contracts )
        timeToBeUpdate = lookback
    return  Open, High, Low, Close, Volume, timeToBeUpdate


def fetch_all_ohlcv(exchange, symbol, timeframe, since, limit=1000):
    """
    分页获取 Bybit 的历史 K 线数据
    :param symbol: 交易对，例如 'BTC/USDT'
    :param timeframe: 时间周期，例如 '1m', '5m', '1h', '1d'
    :param since: 起始时间，Unix 时间戳（毫秒）
    :param limit: 每次获取的最大数据量，默认为 1000
    :return: 所有历史 K 线数据（列表形式）
    """
    all_data = []
    while True:
        # 获取 K 线数据
        ohlcv = exchange.fetch_ohlcv(symbol, timeframe, since=since, limit=limit)
        if not ohlcv:
            break  # 如果没有数据，退出循环

        all_data.extend(ohlcv)  # 添加数据到结果列表
        since = ohlcv[-1][0]    # 更新起始时间为最后一条数据的时间戳 + 1 毫秒

        # 如果获取的数据量少于 limit，说明已经获取完所有数据
        if len(ohlcv) < limit:
            break

    return all_data

def fetch_candles2(bybit, symbol,since ,timeframe, limit=1000):
    """
    获取 Bybit 4 小时合约数据，并转换为 Pandas DataFrame
    :param bybit: 初始化后的 Bybit 实例
    :param symbol: 交易对，例如 'BTC/USDT'
    :param limit: 返回的K线数量
    :return: Pandas DataFrame 格式的 K 线数据
    """
    try:
        candles = fetch_all_ohlcv(bybit, symbol, timeframe, since)
        # candles = bybit.fetch_ohlcv(symbol, timeframe, limit=limit)
        # 转换为 Pandas DataFrame
        df = pd.DataFrame(candles, columns=['timestamp', 'open', 'high', 'low', 'close', 'volume'])
        # 将时间戳转换为可读时间格式，并设置为索引
        df['timestamp'] = pd.to_datetime(df['timestamp'], unit='ms')  # 转换为 datetime
        df.set_index('timestamp', inplace=True)  # 设置为索引
        df = df[~df.index.duplicated(keep='first')]
        return df
    except Exception as e:
        print(f"获取 K 线数据时出错: {e}")
        return None
    
    # 这一步比较耗时 60s
def download_dt(bybit,since,timeframe,Open, High, Low, Close, Volume,path):
    # utc_now = get_nowISO2()
    
    candles = fetch_candles2(bybit, 'BTC/USDT:USDT',since ,timeframe, 1000)
    Open_up = pd.DataFrame(0, columns = Open.columns , index = candles.index)
    High_up = pd.DataFrame(0, columns = Open.columns , index = candles.index)
    Low_up = pd.DataFrame(0, columns = Open.columns , index = candles.index)
    Close_up = pd.DataFrame(0, columns = Open.columns  , index = candles.index)
    Volume_up = pd.DataFrame(0, columns = Open.columns  , index = candles.index)
    
    a = 0
    noExsit = []
    for symbol in Open.columns: # 先解决for循环
        a = a + 1
        try:
            candles = fetch_candles2(bybit, symbol,since ,timeframe, 1000)
            # print(candles)
            Open_up[symbol] = candles['open']
            High_up[symbol] = candles['high']
            Low_up[symbol] = candles['low']
            Close_up[symbol] = candles['close']
            Volume_up[symbol] = candles['volume']
            # print(a, symbol , 'ok' )
        except:
            noExsit.append(symbol)
            print(a, symbol,'no exist!')
            
    Open_up = Open_up.replace({None: np.nan})
    High_up = High_up.replace({None: np.nan})
    Low_up = Low_up.replace({None: np.nan})
    Close_up = Close_up.replace({None: np.nan})
    Volume_up = Volume_up.replace({None: np.nan})
    
    if len(Open) > 0:
        ind_new = Open_up.index
        Open = Open[~Open.index.isin(ind_new)]
        High = High[~High.index.isin(ind_new)]
        Low = Low[~Low.index.isin(ind_new)]
        Close = Close[~Close.index.isin(ind_new)]
        Volume = Volume[~Volume.index.isin(ind_new)]
        
        Open = pd.concat([Open, Open_up], axis = 0)
        High = pd.concat([High, High_up], axis = 0)
        Low = pd.concat([Low, Low_up], axis = 0)
        Close = pd.concat([Close, Close_up], axis = 0)
        Volume = pd.concat([Volume, Volume_up], axis = 0)
    else:
        Open = Open_up
        High = High_up
        Low = Low_up
        Close = Close_up
        Volume = Volume_up
        
    Open = Open[~Open.index.duplicated()]
    High = High[~High.index.duplicated()]
    Low = Low[~Low.index.duplicated()]
    Close = Close[~Close.index.duplicated()]
    Volume = Volume[~Volume.index.duplicated()]
    
    # Open = Open.drop(columns=noExsit, errors="ignore")
    # High = High.drop(columns=noExsit, errors="ignore")
    # Low = Low.drop(columns=noExsit, errors="ignore")
    # Close = Close.drop(columns=noExsit, errors="ignore")
    # Volume = Volume.drop(columns=noExsit, errors="ignore")
    
    # pd.to_pickle(Open, path + 'open.pkl')
    # pd.to_pickle(High, path + 'high.pkl')
    # pd.to_pickle(Low, path + 'low.pkl')
    # pd.to_pickle(Close, path + 'close.pkl')
    # pd.to_pickle(Volume, path + 'volume.pkl')
    

    
    return Open, High, Low, Close, Volume



def download_dt_par(bybit,since,timeframe,Open, High, Low, Close, Volume, path,temp_folder):
    candles = fetch_candles2(bybit, 'BTC/USDT:USDT',since ,timeframe, 1000)
    Open_up = pd.DataFrame(0, columns = Open.columns , index = candles.index)
    High_up = pd.DataFrame(0, columns = Open.columns , index = candles.index)
    Low_up = pd.DataFrame(0, columns = Open.columns , index = candles.index)
    Close_up = pd.DataFrame(0, columns = Open.columns  , index = candles.index)
    Volume_up = pd.DataFrame(0, columns = Open.columns  , index = candles.index)
    
    # a = 0
    # for symbol in Open.columns:
    #     a = a + 1
    #     try:
    #         candles = fetch_candles2(bybit, symbol,since ,timeframe, 1000)
    #         # print(a , symbol )
    #         Open_up[symbol] = candles['open']
    #         High_up[symbol] = candles['high']
    #         Low_up[symbol] = candles['low']
    #         Close_up[symbol] = candles['close']
    #         Volume_up[symbol] = candles['volume']
    #     except:
    #         print(symbol,'no exist!')
    import joblib
    cols =  list(Open.columns)
    results = joblib.Parallel(n_jobs= os.cpu_count() - 1, temp_folder=temp_folder )(
        joblib.delayed(fetch_candles2)(bybit, symbol,since ,timeframe, 1000) for symbol in cols  )
    
    for v in range(len(cols)):
        try:
            candles = results[v]
            Open_up[cols[v]] = candles['open']
            High_up[cols[v]] = candles['high']
            Low_up[cols[v]] = candles['low']
            Close_up[cols[v]] = candles['close']
            Volume_up[cols[v]] = candles['volume']
        except:
            print(v)
            
    Open_up = Open_up.replace({None: np.nan})
    High_up = High_up.replace({None: np.nan})
    Low_up = Low_up.replace({None: np.nan})
    Close_up = Close_up.replace({None: np.nan})
    Volume_up = Volume_up.replace({None: np.nan})
    
    Open = pd.concat([Open, Open_up], axis = 0)
    High = pd.concat([High, High_up], axis = 0)
    Low = pd.concat([Low, Low_up], axis = 0)
    Close = pd.concat([Close, Close_up], axis = 0)
    Volume = pd.concat([Volume, Volume_up], axis = 0)
    
    Open = Open[~Open.index.duplicated()]
    High = High[~High.index.duplicated()]
    Low = Low[~Low.index.duplicated()]
    Close = Close[~Close.index.duplicated()]
    Volume = Volume[~Volume.index.duplicated()]
    return Open, High, Low, Close, Volume



def clear_data(Open, High, Low, Close, Volume, sd0, end0):
    Open = Open[Open.index >= sd0]
    High = High[High.index >= sd0]
    Low = Low[Low.index>= sd0]
    Close = Close[Close.index>= sd0]
    Volume = Volume[Volume.index>= sd0]

    Open = Open[Open.index<= end0]
    High = High[High.index<= end0]
    Low = Low[Low.index<= end0]
    Close = Close[Close.index<= end0]
    Volume = Volume[Volume.index<= end0]
    
    VWAP = (Open + High + Low + Close) * 4
    Amount = VWAP * Volume
    df = {}
    df['open'],df['high'],df['low'],df['close'],df['volume'], df['amount'], df['vwap']= Open ,High,Low,Close,Volume, Amount, VWAP
    df['totalRet'] = df['close'] / df['close'].shift(1) - 1
    df['totalRet'][df['close'].fillna(0) == 0] = np.nan
    df['totalRet'] = df['totalRet'].replace([np.inf, -np.inf], np.nan) 
    
    # for v in df.keys():
    #     df[v] = df[v][df[v].index >= sd0]
    #     df[v] = df[v][df[v].index <= end0]

    return df


def get_time(timestamps_ms):
    timestamps_sec = np.array(timestamps_ms) / 1000  # 转换为秒
    dt_array = pd.to_datetime(timestamps_sec, unit='s')  # 批量转换

    # 格式化为字符串（可选）
    formatted_times = dt_array.strftime("%Y-%m-%d %H:%M:%S")  # 格式化输出
    return formatted_times

def get_col(string_array):
    processed_array = list(map(lambda s: s[:-4] + '/USDT:USDT', string_array))
    return processed_array

def clear_data2(Open, High, Low, Close, Volume, sd0, end0):
    datelist = pd.to_datetime(get_time(Open.index))
    codelist = get_col(Open.columns)
    

    Open.index = datelist
    Open.columns = codelist
    High.index = datelist
    High.columns = codelist
    Low.index = datelist
    Low.columns = codelist
    Close.index = datelist
    Close.columns = codelist
    Volume.index = datelist
    Volume.columns = codelist

    print(Open)

    Open = Open[Open.index >= sd0]
    High = High[High.index >= sd0]
    Low = Low[Low.index>= sd0]
    Close = Close[Close.index>= sd0]
    Volume = Volume[Volume.index>= sd0]

    Open = Open[Open.index<= end0]
    High = High[High.index<= end0]
    Low = Low[Low.index<= end0]
    Close = Close[Close.index<= end0]
    Volume = Volume[Volume.index<= end0]

    df = {}
    df['open'],df['high'],df['low'],df['close'],df['volume']= Open ,High,Low,Close,Volume

    return df

def updateData(old, new, cut, path):
    
    if old['open'].index[-cut] > new['open'].index[0]:
        out = {}
        for v in old.keys():
            old[v] = old[v].iloc[:-cut]
            new[v] = new[v][ new[v].index > old[v].index[-1]]
            print(old[v])
            print(new[v])
            out[v] = pd.concat([old['open'],new['open']], axis = 0)
    for v in out.keys():
        pd.to_pickle(out[v], path + v +  '.pkl')
    return 





def get_snap(df_all_future, frq, cut):
    VWAP = (df_all_future['open'] + df_all_future['high']+df_all_future['low']+df_all_future['close']) / 4
    df_all_future['amount'] = VWAP * df_all_future['volume']
    
    df_all_future['open'] = df_all_future['open'].shift(frq-1)
    df_all_future['high'] = df_all_future['high'].rolling(frq).max()
    df_all_future['low'] = df_all_future['low'].rolling(frq).min()
    df_all_future['volume'] = df_all_future['volume'].rolling(frq).sum()
    df_all_future['amount'] = df_all_future['amount'].rolling(frq).sum()
    
    for v in df_all_future.keys():
        df_all_future[v] = df_all_future[v][df_all_future[v].index.minute % frq == cut]
        
    df_all_future['vwap'] = df_all_future['amount'] / df_all_future['volume']
    df_all_future['totalRet'] = df_all_future['close'] / df_all_future['close'].shift(1) - 1
    df_all_future['totalRet'][df_all_future['close'].fillna(0) == 0] = np.nan
    df_all_future['totalRet'] = df_all_future['totalRet'].replace([np.inf, -np.inf], np.nan) 
    
    # for v in df_all_future.keys():
    #     df_all_future[v].index = df_all_future[v].index.strftime('%Y-%m-%d %H:%M:%S')
    
    return df_all_future