import h5py
import pandas as pd
# from collections import defaultdict
import time

def extract_market_data_from_h5(file_path, expected_pairs=None):
    """
    从HDF5文件中提取标准化市场数据
    
    参数:
        file_path: HDF5文件路径
        expected_pairs: 预期存在的交易对列表(如['BTCUSDT', 'CHZUSDT'])，为None则自动检测
        
    返回:
        dict: 以交易对名为键，对应DataFrame为值的字典
    """
    result_dict = {}
    standard_fields = ['timestamp', 'open', 'high', 'low', 'close', 'volume']
    
    with h5py.File(file_path, 'r') as f:
        # 确定要处理的交易对列表
        if expected_pairs is None:
            # 自动检测所有可能的交易对组
            potential_pairs = [name for name in f.keys() 
                             if isinstance(f[name], h5py.Group) and 
                             any(field in f[name] for field in standard_fields)]
            expected_pairs = potential_pairs if potential_pairs else []
        
        for pair in expected_pairs:
            try:
                # 构建路径字典
                paths = {field: f"{pair}/{field}" for field in standard_fields}
                
                # 验证路径是否存在
                missing_fields = [field for field, path in paths.items() 
                                if path.rstrip('/') not in f]
                if missing_fields:
                    print(f"警告: 交易对 {pair} 缺少字段: {missing_fields}")
                    continue
                
                # 读取数据
                data = {field: f[path][:] for field, path in paths.items()}
                
                # 转换为DataFrame
                df = pd.DataFrame(data)
                
                # 处理时间戳
                if 'timestamp' in df.columns:
                    try:
                        df['datetime'] = pd.to_datetime(df['timestamp'], unit='ms')
                    except:
                        try:
                            df['datetime'] = pd.to_datetime(df['timestamp'], unit='s')
                        except Exception as e:
                            print(f"交易对 {pair} 时间戳转换失败: {e}")
                            df['datetime'] = df['timestamp']  # 保留原始值
                
                # 设置索引
                if 'datetime' in df.columns:
                    df.set_index('datetime', inplace=True)
                
                result_dict[pair] = df
                
            except Exception as e:
                print(f"处理交易对 {pair} 时出错: {e}")
    
    return result_dict

def get_dfs(all_market_data):
    candles = all_market_data['BTCUSDT']
    Open_up = pd.DataFrame(0, columns = all_market_data.keys() , index = candles.index)
    High_up = pd.DataFrame(0, columns = all_market_data.keys() , index = candles.index)
    Low_up = pd.DataFrame(0, columns = all_market_data.keys() , index = candles.index)
    Close_up = pd.DataFrame(0, columns = all_market_data.keys()  , index = candles.index)
    Volume_up = pd.DataFrame(0, columns = all_market_data.keys()  , index = candles.index)

    a = 0
    noExsit = []
    for symbol in all_market_data.keys(): # 先解决for循环
        a = a + 1
        try:
            candles = all_market_data[symbol]
            # print(candles)
            Open_up[symbol] = candles['open']
            High_up[symbol] = candles['high']
            Low_up[symbol] = candles['low']
            Close_up[symbol] = candles['close']
            Volume_up[symbol] = candles['volume']
            # print(a, symbol , 'ok' )
        except:
            noExsit.append(symbol)
            print(a, symbol,'no exist!')
    return Open_up, High_up, Low_up, Close_up, Volume_up


def tic():
    """记录开始时间"""
    global start_time
    start_time = time.time()

def toc(s = ' '):
    """计算经过的时间并打印结果"""
    if 'start_time' in globals():
        elapsed_time = time.time() - start_time
        print(s, f"Elapsed time: {elapsed_time:.2f} seconds")
    else:
        print("Call tic() first to start the timer.")


tic()
# 使用示例
file_path = '/home/<USER>/realTime/Data/hdf5/kline_data.h5'

# 方法1: 自动检测所有交易对
all_market_data = extract_market_data_from_h5(file_path)
Open, High, Low, Close, Volume = get_dfs(all_market_data)
print(Open)
toc('finish loadData')

