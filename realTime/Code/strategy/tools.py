
import whfactor.operator_funcs as of
from pymatreader import read_mat
import pandas as pd
import numpy as np
import time
import random
import os
import whfactor.testing as tt

from pathlib import Path
home_dir = str(Path.home())

def get_maxCorr(_pool_rets,_name):
    _pool_rets = _pool_rets.fillna(0)
    _corr = _pool_rets.corr().abs()
    _corr = _corr[_name]
    _corr = _corr[_corr.index.isin([_name]) == 0]
    _maxCorr = _corr.max()
    return _maxCorr , _corr


def get_mid_px(data_):
    try:
        data_['close/vwap'] = data_['close'] / data_['vwap']
    except:
        nn = 1
    try:
        data_['close_vwap_retG'] = (data_['close']/data_['close'].shift(1)-1) -  (data_['vwap'] / data_['vwap'].shift(1) - 1)
    except:
        nn = 1
    # try:
    #     data_['high-low'] = data_['high'] - data_['low']
    # except:
    #     nn = 1
    # try:
    #     data_['(high-low)/close'] = (data_['high'] - data_['low']) / data_[
    #         'close']
    # except:
    #     nn = 1
    try:
        data_['totalRet/amount'] = data_['totalRet'] / data_['amount']
    except:
        nn = 1
    return data_



def get_pool_data(start_date, end_date):
    if os.path.exists(home_dir + '/bn_mf_11/Strategy/LS_future_L_TO/GP/bybit_5m24h_d1_pn6_3_ts_reg2_nopre/pool1/pool_rets.pkl.zip'):
        pool_rets = pd.read_pickle(home_dir + '/bn_mf_11/Strategy/LS_future_L_TO/GP/bybit_5m24h_d1_pn6_3_ts_reg2_nopre/pool1/pool_rets.pkl.zip')
        # pool_rets = pool_rets[pool_rets.index > start_date ]
        # pool_rets = pool_rets[pool_rets.index <= end_date ]
        pool_table = pd.read_pickle(home_dir + '/bn_mf_11/Strategy/LS_future_L_TO/GP/bybit_5m24h_d1_pn6_3_ts_reg2_nopre/pool1/pool_table.pkl.zip')
    else:
        pool_rets = pd.DataFrame()
        pool_table = pd.DataFrame(
            columns=['expr', 'expr2','result', 'fac_ic', 'lsret', 'sharpe', 'maxCorr', 'SDate', 'EDate','rets_up', 'rets_dn', 'pct' , 'vCorr'])
    return pool_rets, pool_table




def save_print(pool_rets,pool_table,expr):
    pd.to_pickle(pool_rets, home_dir + '/bn_mf_11/Strategy/LS_future_L_TO/GP/bybit_5m24h_d1_pn6_3_ts_reg2_nopre/pool1/pool_rets.pkl.zip', compression='zip')
    pd.to_pickle(pool_table, home_dir + '/bn_mf_11/Strategy/LS_future_L_TO/GP/bybit_5m24h_d1_pn6_3_ts_reg2_nopre/pool1/pool_table.pkl.zip', compression='zip')
    pool_table.to_csv(home_dir + '/bn_mf_11/Strategy/LS_future_L_TO/GP/bybit_5m24h_d1_pn6_3_ts_reg2_nopre/pool1/pool_table.csv')
    # print(pool_table)
    print('--------factor:',len(pool_table), expr)



def calculate_fitness3(v, of, tt, opdata):
    try:
        factor_data = of.calc(opdata.data, v)
        # barraFactor = opdata.barraFactor
        # for v in barraFactor.keys():
        #     factor_data = of.pn_TransNorm(factor_data)
        #     factor_data = of.pn_CrossFit(barraFactor[v], factor_data)
        #     # print(factor_data)
        factor_data = of.pn_TransNorm(factor_data)
        result, fac_ic, lsret, sharpe, dir ,rets ,to , rets_up,rets_dn, pct_, vCorr= tt.factor_withPool3(factor_data, opdata.rate,
                                                                              opdata.poolRets,1,0.000,opdata.gapCorr, opdata.rate_ave,  opdata.lastMom)
        return (result, fac_ic, lsret, sharpe, dir ,rets,to, rets_up,rets_dn, pct_,vCorr)
    except Exception as e:
        print(f"Error: {e}")
        # print(v)
        # print(of.parse_expression(v))
        # print(v)
        return (0,0,0,0,0,0,0,0,0,0,0)




def isPass(rets):
    rets_ = rets.copy()
    rets_.index = pd.to_datetime(rets_.index)
    ret_y = rets_.resample('1Y').mean() / rets_.resample('1Y').std() * 15
    ret_num = rets_.resample('1Y').count()
    ret_y.ewm(10).mean()
    ret_y[ret_num < 120] = np.nan
    pass1 = ret_y.std()
    ret_y_ = rets_.resample('1Y').mean() * 250
    ret_y_[ret_num < 120] = np.nan
    pass2 = ret_y_.tail(3).mean() / ret_y_.head(3).mean()
    z = 0
    if pass2 < 0.3 and ret_y_.tail(3).mean() < 0.02:
        z = 1
    return z


def my_select1(fitnessValues, sharpes, retss, population):
    _rets = pd.DataFrame()
    _fitnessValues, _sharpes, _population = [], [], []
    _population_good = []
    for v in range(len(fitnessValues)):
        if fitnessValues[v] > 0 and sharpes[v] > 0:  # 是否只用取不为零的数： 待商榷
            _fitnessValues.append(fitnessValues[v])
            _sharpes.append(sharpes[v])
            _population.append(population[v])
            if sharpes[v] > 2 :   # 更大的因子，更多的权重
                _fitnessValues.append(fitnessValues[v])
                _sharpes.append(sharpes[v])
                _population.append(population[v])
                _fitnessValues.append(fitnessValues[v])
                _sharpes.append(sharpes[v])
                _population.append(population[v])
                _population_good.append(population[v])
            elif sharpes[v] > 1 and  sharpes[v] <= 2:
                _fitnessValues.append(fitnessValues[v])
                _sharpes.append(sharpes[v])
                _population.append(population[v])
    _population = random.sample(_population, len(_population))  # 打乱次序
    _population_good = random.sample(_population_good, len(_population_good))  # 打乱次序
    return _population , _population_good


def cutFactors(srs, _rets, corGap):
    # srs['formulas'] = srs.index
    # srs.index = range(len(srs.index))
    # returns2.columns = range(len(srs.index))
    cor = _rets.corr()
    deleList = []
    for vvv in srs.index:
        # print(vvv)
        if vvv in deleList:
            continue
        cor_ = cor[vvv]
        cor_ = cor_[cor_.index != vvv]
        cor_ = cor_[cor_.values > corGap]
        deleList = deleList + list(cor_.index)
    tmp_ = srs[~srs.index.isin(deleList)]
    return tmp_


def my_select2(fitnessValues, sharpes, retss, population):
    _rets = pd.DataFrame()
    _fitnessValues, _sharpes, _population = [], [], []
    for v in range(len(fitnessValues)):
        if fitnessValues[v] > 0 and sharpes[v] > 0:
            _fitnessValues.append(fitnessValues[v])
            _sharpes.append(sharpes[v])
            _population.append(population[v])
            _rets[v] = retss[v]
            print(v)
    res = pd.DataFrame()
    res['sr'] = _sharpes
    res['population'] = _population
    _rets.columns = range(len(_rets.columns))
    sr = res['sr']
    srs = sr.sort_values(ascending=False)
    _index = cutFactors(srs, _rets, 0.7)
    res = res[res.index.isin(_index.index)]
    return list(res['population'])


def get_unique(offspring, offsprings):   # 删除和之前一样的，和互相有重复的
    offspring_list = []
    for v in offspring:
        offspring_list.append(of.parse_expression(v))
    new1, new2 = [], []
    for v in offsprings:
        f = of.parse_expression(v)
        if f in offspring_list:
            continue
        if f in new2:
            continue
        new1.append(v)
        new2.append(f)
    # check = []
    # for v in new1:
    #     ff = of.parse_expression(v)
    #     if ff in new2:
    #         check.append(1)
    # print(len(check) == len(new2))
    return new1

def delete_temp_files(temp_folder):
    try:
        # 遍历目录树
        for root, dirs, files in os.walk(temp_folder, topdown=False):
            for file in files:
                file_path = os.path.join(root, file)
                os.remove(file_path)
                # print(f"Deleted file: {file_path}")
            for dir in dirs:
                dir_path = os.path.join(root, dir)
                os.rmdir(dir_path)
                # print(f"Deleted directory: {dir_path}")
        # print(f"All temporary files and directories in {temp_folder} deleted successfully.")
    except Exception as e:
        print(f"Error deleting temporary files: {e}")


def get_best_decayNum(factor_data, rate_idio, universe):
    maxsr = -100000
    maxvalue = factor_data.copy()
    for v in [5,10,20]:
        factor_data_ = of.ts_Decay(factor_data, v)
        c = tt.overall_performance_withRets(factor_data_, rate_idio, universe, 1, 0.0002)
        # _result, _fac_ic, _lsret, _sharpe, _dir, _rets, _to
        print(v,c[3] , c[2] ,c[6])
        if c[3] > maxsr :
            maxsr = c[3]
            store = c
            maxvalue = factor_data_
            maxind = v
    if maxind == 20 and c[6] > 0.5:
        factor_data_ = of.ts_Decay(factor_data, 30)
        c = tt.overall_performance_withRets(factor_data_, rate_idio, universe, 1, 0.0002)
        print(v,c[3] , c[2] ,c[6])
        if c[3] > maxsr:
            store = c
            maxind = 30
            maxvalue = factor_data_
    return store[0], store[1], store[2], store[3], store[4], store[5], store[6], maxind, maxvalue


def get_factor_data3(start_date, end_date, path):
    # if os.path.exists(loadpath):
    #     df = pd.read_pickle(loadpath)
    base = 'USDT'
    frq = '1m'
    fileNames = os.listdir(path)

    # 获取货币名称序列
    codes = []
    for v in fileNames:
        if v == 'futures':
            continue
        t = list(v.split('_'))
        base_ = t[1].split('-')[0]
        freq_ = t[1].split('-')[1].split('.')[0]
        if base_ == base and freq_ == frq:
            codes.append(t[0])
        # if t[0] == '0':
        #     break
    raw_data = {}
    date = []
    for v in range(len(codes)):
        print(v, codes[v])
        fileName_ = codes[v] + '_' + base + '-' + frq + '.feather'
        tmp = pd.read_feather(path + fileName_)
        tmp = tmp[tmp['date'] >= start_date]
        tmp = tmp[tmp['date'] <= end_date]
        raw_data[codes[v]] = tmp.copy()
        date_ = list(tmp['date'])
        if len(date) < len(date_):
            date = date_.copy()
    date = np.sort(date)

    # Open = pd.DataFrame(0, columns = codes, index =date )
    # High = pd.DataFrame(0, columns = codes, index =date )
    # Low = pd.DataFrame(0, columns = codes, index =date )
    # Close = pd.DataFrame(0, columns = codes, index =date )
    # Volume = pd.DataFrame(0, columns = codes, index =date )

    Open = pd.DataFrame(index=date)
    High = pd.DataFrame(index=date)
    Low = pd.DataFrame(index=date)
    Close = pd.DataFrame(index=date)
    Volume = pd.DataFrame(index=date)
    for v in range(len(codes)):
        # if v > 30 :    ## 后面需要注释掉这一行以获取更多的数据
        #     continue
        # fileName_ = codes[v] + '_' + base + '-' + frq + '.feather'
        # tmp = pd.read_feather(path+fileName_)
        print(v, codes[v], 'get in.')
        tmp = raw_data[codes[v]].copy()
        tmp.index = tmp['date']
        # print(codes[v],len(tmp))
        Open[codes[v]] = tmp['open']
        High[codes[v]] = tmp['high']
        Low[codes[v]] = tmp['low']
        Close[codes[v]] = tmp['close']
        Volume[codes[v]] = tmp['volume']
        raw_data.pop(codes[v])

    Amount = (Close + Open + High + Low) / 4 * Volume
    VWAP = Amount / Volume

    df = {}
    df['Open'], df['High'], df['Low'], df['Close'], df['Volume'], df['Amount'], df[
        'VWAP'] = Open, High, Low, Close, Volume, Amount, VWAP
    # for v in df.keys():
    #     df[v] = df[v].sort_index()

    # 删去交易低的  累计交易额 《 300w usdt
    Value_cs = Amount.fillna(0).cumsum()
    chose1 = (Value_cs > 1000000000) * 1
    # chose1.sum(1).plot()
    chose2 = chose1.sum()
    chose2 = chose2[chose2 > 1 * 60 * 24 * 365]
    chose2 = chose2.index

    for v in df.keys():
        df[v][chose1 == 0] = np.nan
        df[v] = df[v][chose2]
        df[v] = df[v].replace([np.inf, -np.inf], np.nan)

    # 把退市的负收益加上
    Close2 = df['Close'].copy().fillna(0)
    tt = Close2 / Close2.shift(1) - 1
    tt = tt.replace([np.inf, -np.inf], np.nan)
    df['TotalRet'] = tt
    return df

def get_Min_data(start_date, end_date, dataPath, path_store):
    if os.path.exists(path_store + 'close.pkl'):
        Open = pd.read_pickle(path_store + 'open.pkl')
        High = pd.read_pickle(path_store + 'high.pkl')
        Low = pd.read_pickle(path_store + 'low.pkl')
        Close = pd.read_pickle(path_store + 'close.pkl')
        Volume = pd.read_pickle(path_store + 'volume.pkl')
        
        Open = Open[start_date:end_date]
        High = High[start_date:end_date]
        Low = Low[start_date:end_date]
        Close = Close[start_date:end_date]
        Volume = Volume[start_date:end_date]
    else:
        base = 'USDT'
        frq = '1h'
        fileNames = os.listdir(dataPath)

        # 获取货币名称序列
        codes = []
        for v in fileNames:
            t = list(v.split('_'))
            try:
                base_ = t[1].split('-')[0]
                freq_ = t[2].split('-')[1].split('.')[0]
                if base_ == base and freq_ == frq:
                    codes.append(t[0])
            except:
                print(t)
        print(len(codes))

        # 获取全时段时间序列
        raw_data = {}
        date = []
        codes2 = []
        for v in range(len(codes)):
            fileName_ = codes[v] + '_' + base + '_USDT-' + frq + '-futures.feather'
            tmp = pd.read_feather(dataPath + fileName_)
            tmp = tmp[tmp['date'] >= start_date]
            tmp = tmp[tmp['date'] <= end_date]
            if len(tmp) > 0:
                raw_data[codes[v]] = tmp.copy()
                date_ = list(tmp['date'])
                if len(date) < len(date_):
                    date = date_.copy()
                print(v, codes[v], date_[0], date_[-1])
                codes2.append(codes[v])
        date = np.sort(date)
        codes = codes2

        Open = pd.DataFrame(np.nan, index=date, columns=codes)
        High = pd.DataFrame(np.nan, index=date, columns=codes)
        Low = pd.DataFrame(np.nan, index=date, columns=codes)
        Close = pd.DataFrame(np.nan, index=date, columns=codes)
        Volume = pd.DataFrame(np.nan, index=date, columns=codes)
        for v in range(len(codes)):
            # if v > 30 :    ## 后面需要注释掉这一行以获取更多的数据
            #     continue
            # fileName_ = codes[v] + '_' + base + '-' + frq + '.feather'
            # tmp = pd.read_feather(path+fileName_)
            print(v, codes[v], 'get in.')
            tmp = raw_data[codes[v]].copy()
            tmp.index = tmp['date']
            # print(codes[v],len(tmp))
            Open[codes[v]] = tmp['open']
            High[codes[v]] = tmp['high']
            Low[codes[v]] = tmp['low']
            Close[codes[v]] = tmp['close']
            Volume[codes[v]] = tmp['volume']
            raw_data.pop(codes[v])

        pd.to_pickle(Open, path_store + 'open.pkl')
        pd.to_pickle(High, path_store + 'high.pkl')
        pd.to_pickle(Low, path_store + 'low.pkl')
        pd.to_pickle(Close, path_store + 'close.pkl')
        pd.to_pickle(Volume, path_store + 'volume.pkl')
        Open = Open[start_date:end_date]
        High = High[start_date:end_date]
        Low = Low[start_date:end_date]
        Close = Close[start_date:end_date]
        Volume = Volume[start_date:end_date]

    Amount = (Open + High + Low + Close) * 4 * Volume
    VWAP = (Open + High + Low + Close) * 4
    df = {}
    df['open'], df['high'], df['low'], df['close'], df['volume'], df['amount'], df[
        'vwap'] = Open, High, Low, Close, Volume, Amount, VWAP
    return df

def get_rawData(dataPath, path_store, start_date , end_date):
    df_all = get_Min_data(start_date , end_date, dataPath, path_store)
    df_all['totalRet'] = df_all['close'] / df_all['close'].shift(1) - 1
    df_all['totalRet'][df_all['close'].fillna(0) == 0] = np.nan
    df_all['totalRet'] = df_all['totalRet'].replace([np.inf, -np.inf], np.nan)
    return  df_all


