import ccxt
import os
from datetime import datetime
import pandas as pd
import numpy as np
import optuna
from pathlib import Path
import warnings
warnings.filterwarnings('ignore')
import time
from datetime import datetime, UTC, timedelta
from Comp import get_cf6,get_cf6_rt
import test_operator_funcs as of
from test_operator_funcs import *
# import joblib
from getMinFactor_fast_15min_sele2 import get_data_feature_fast_fromExpr

import matplotlib.pyplot as plt
import tools2_newBar as tl2
import math
import json

import copy
from pathlib import Path
home_dir = str(Path.home())

# 初始化 Bybit 交易所
def initialize_bybit(api_key, secret):
    """
    初始化 Bybit 实例
    :param api_key: Bybit API 密钥
    :param secret: Bybit API 密钥
    :return: 初始化后的 Bybit 实例
    """
    bybit = ccxt.bybit({
        'apiKey': api_key,
        'secret': secret,
        'options': {
            'defaultType': 'future',  # 如果是合约交易，设置为 'future'
        },
    })
    return bybit


# 市价下单函数
def place_market_order(bybit, symbol, side, amount):
    """
    市价下单
    :param bybit: 初始化后的 Bybit 实例
    :param symbol: 交易对，例如 'BTC/USDT'
    :param side: 买入或卖出，'buy' 或 'sell'
    :param amount: 下单数量
    :return: 下单结果
    """
    try:
        order = bybit.create_order(
            symbol=symbol,
            type='market',
            side=side,
            amount=amount
        )
        print(f"市价下单成功: {order}")
        return order
    except Exception as e:
        print(f"市价下单失败: {e}")
        return None


# 获取持仓信息函数
def fetch_positions(bybit):
    """
    获取用户当前的持仓信息
    :param bybit: 初始化后的 Bybit 实例
    :return: 持仓信息列表
    """
    try:
        # 调用 Bybit 的合约账户持仓接口
        positions = bybit.get_positions()['result']
        # 过滤出有仓位的合约（未平仓的持仓）
        active_positions = [
            position for position in positions if float(position['size']) != 0
        ]
        # 格式化持仓信息
        formatted_positions = []
        for position in active_positions:
            formatted_positions.append({
                'symbol': position['symbol'],                  # 交易对（如 BTCUSDT）
                'positionAmt': float(position['size']),        # 持仓数量（正数为多仓，负数为空仓）
                'entryPrice': float(position['entry_price']),  # 开仓价格
                'unrealizedProfit': float(position['unrealised_pnl']),  # 未实现盈亏
                'leverage': int(position['leverage']),         # 杠杆倍数
                'marginType': position['position_margin_mode'], # 保证金类型（cross 或 isolated）
                'liquidationPrice': float(position['liq_price']),  # 强平价格
            })
        return formatted_positions
    except Exception as e:
        print(f"获取持仓信息失败: {e}")
        return None



def calculate_fitness9_neut2(_expr3, df ,rate_ave, rate, delayNum, universe, barraFactor):
    _expr = _expr3
    f = eval(_expr) 
    f = f.replace([np.inf, -np.inf], np.nan)
    f[universe == 0] = np.nan
    f = of.pn_TransNorm(f)
    
    # print(rate.tail(5))
    # print(f.tail(5))
    f, f_up,f_dn  = get_ls_post2(f)
    # f_up,f_dn = get_portFromFactor_both(f, 50  )       # 获取多空组合 待优化（）减少换手
    # f = f_up + f_dn

    # rests = (f.shift(delayNum) * rate)
    
    rets = (f.shift(delayNum) * rate).sum(1)
    
    # winpct = (rests > 0).sum(1) / (f.shift(delayNum).abs() > 0).sum(1) 
    # rests_win = rests.copy()
    # rests_win[rests_win <=0] = np.nan
    # rests_win = rests_win.mean(1)
    # rests_loss = rests.copy()
    # rests_loss[rests_loss >=0] = np.nan
    # rests_loss = rests_loss.mean(1) * -1
    # wl = rests_win/rests_loss
    
    # rets[:100] = np.nan
    rets_up = (f_up.shift(delayNum) * rate).sum(1) - rate_ave
    rets_dn = (f_dn.shift(delayNum) * rate).sum(1) + rate_ave
    rets[:100] = 0
    rets_up[:100] = 0
    rets_dn[:100] = 0
    # print( to,_expr3)
    return (f, f_up, f_dn, rets, rets_up, rets_dn)



def calculate_fitness9_neut3(_expr3, df ,rate_ave, rate, delayNum, universe, barraFactor):
    _expr = _expr3
    f = eval(_expr) 
    f = f.replace([np.inf, -np.inf], np.nan)
    f[universe == 0] = np.nan
    f = of.pn_TransNorm(f)
    # f = f.fillna(0)
    
    for v in barraFactor.keys():
        f = of.pn_CrossFit(barraFactor[v],f)
        f = of.pn_TransNorm(f)
    
    f, f_up,f_dn  = get_ls_post2(f)
    # f_up,f_dn = get_portFromFactor_both(f, 50  )       # 获取多空组合 待优化（）减少换手
    # f = f_up + f_dn

    rets = (f.shift(delayNum) * rate).sum(1)
    
    # rets[:100] = np.nan
    rets_up = (f_up.shift(delayNum) * rate).sum(1) - rate_ave
    rets_dn = (f_dn.shift(delayNum) * rate).sum(1) + rate_ave
    rets[:100] = 0
    rets_up[:100] = 0
    rets_dn[:100] = 0
    # print( to,_expr3)
    return (f, f_up, f_dn, rets, rets_up, rets_dn)


def get_ccf_1m_p1_neut(df, toTest1, rate, delayNum, barraFactor, sd, universe,ispic):
    # 算每一个因子值， 计算他们的多空收益
    vals = {}
    vals_up = {}
    vals_dn = {}
    returns = pd.DataFrame()
    returns_up = pd.DataFrame()
    returns_dn = pd.DataFrame()
    rate2 = rate.copy()
    rate2[universe == 0] = np.nan
    rate_ave = rate2.mean(1)
    rate_ave[:10] = np.nan
    population = toTest1
    for v in range(len(population)):
        _expr = population[v]
        # print(_expr)
        # vals[_expr], vals_up[_expr] , vals_dn[_expr] , returns[_expr] , returns_up[_expr] , returns_dn[_expr] = calculate_fitness9_neut2(_expr, df, rate_ave, rate, delayNum, universe, barraFactor )
        vals[_expr], vals_up[_expr] , vals_dn[_expr] , returns[_expr] , returns_up[_expr] , returns_dn[_expr] = calculate_fitness9_neut3(_expr, df, rate_ave, rate, delayNum, universe, barraFactor )
        # '''
        if ispic:
            r = returns[_expr] .copy()
            r = r[r.index >  sd]
            r.index = pd.to_datetime(r.index)
            r2 = returns_up[_expr] .copy()
            r2 = r2[r2.index > sd]
            r2.index = pd.to_datetime(r2.index)
            r3 = returns_dn[_expr] .copy()
            r3 = r3[r3.index > sd]
            r3.index = pd.to_datetime(r3.index)
            
            r.cumsum().plot()
            r2.cumsum().plot()
            r3.cumsum().plot()
            # print(v,'OTS:', round(r2.sum()/(r2.sum() + r3.sum()),3))
            to = (vals[_expr]- vals[_expr].shift(1)).abs().sum(1)
            to = to[to.index >  sd ]
            to = round(to.mean(),2)
            sr = round(r.mean() / r.std() * (365*24*60/frq/frq1)**0.5 ,2)
            plt.title(str(v)+'__' + str(sr) +'__'+ str(to))
            plt.show()
            savefigpath = factor_path
            plt.savefig(savefigpath + 'FR_' + str(v)+ factor_pic_label  + str(sr) +'__'+ str(to)+ '.png' )
            plt.close()
        # if is_qtl_pic:
        #     cf_1 =  vals[_expr].copy()
        #     cf_1[universe==0] = np.nan
        #     zz = Per_QtlRet(cf_1, 10, 0,  rate,1)
        #     (zz.mean()*12*365).plot()
        #     cor1 = (cf_1.shift(1).corrwith(rate, axis=1))
        #     ics[_expr] = cor1
        #     cor1 = cor1[cor1.index > sd]
        #     cor1 = cor1.tail(len(cor1) - 10) 
        #     cor1 = round( cor1.mean(),3)
        #     plt.title(str(v) +'_'+ str(cor1) +  '_fut_corr')
        #     savefigpath = factor_path
        #     plt.savefig(savefigpath + 'FR_' + str(v)+'_'+ str(cor1) + '_Q.png' )
        #     plt.close()
            
    return  vals, vals_up, vals_dn, returns, returns_up, returns_dn, universe


def get_ccf_1m_p2_opt(vals, returns,rate, universe, delayNum, is_real_trading,sd,opt1,opt2):
    # 因子组合参数
    config = {}
    config['EDate'] = 5000000000
    if is_real_trading:
        config['SDate'] = len(rate) - 5
    else:
        z = returns[returns.index < sd]
        config['SDate'] = len(z)
    config['cov_method'] = 'ret_cov'
    config['FactorNAmes'] = returns.columns
    config['exRet'] = opt1
    config['TotalRet'] = rate
    config['delayNum'] = delayNum
    config['cost'] = 0.000
    config['fb'] = rate.fillna(0) * 0
    config['get_dire'] = 0
    config['maxWgt'] =  0.1  #1/len(returns.columns) * 10   # max(1/len(returns.columns) * 3,0.1)
    config['universe'] = universe
    config['num'] = 10
    config['base'] = rate.mean(1) 
    config['Nrd']  = 5
    # config['method'] = method_cf
    
    config['rolling_window'] = opt2   #合成因子阶段，回看长度1   120 
    config['rolling_window_cov']= opt2  #合成因子阶段，回看长度2  120
    cf_ts , wgt = get_cf6(vals,returns ,config)   # (wgt - wgt.shift(1)).abs().sum(1)

    # cf10  = pn_TransNorm(ts_Decay3(cf_ts,nrd,'exp'))
    cf10  = pn_TransNorm(cf_ts)
    cf10, f_up, f_dn = get_ls_post2(cf10)

    return cf10

def max_drawdown(prices):
    """
    计算时间序列最大回撤
    prices: list or ndarray，代表一段时间内的价格变动序列
    return: float，最大回撤率
    """
    prices1 = prices.copy().fillna(0)
    prices = prices1.cumsum()
    max_drawdown = 0
    peak = prices[0]
    for price in prices:
        if price > peak:
            peak = price
        else:
            drawdown = (peak - price)
            if drawdown > max_drawdown:
                max_drawdown = drawdown
    return round(max_drawdown, 4)

def get_lsRet(ls2,ret, costR, delayNum,sd,name = '0'):
    tos2 = (ls2 - ls2.shift(1)).abs().sum(1)
    ls_ret = (ls2.shift(delayNum) * ret).sum(1) - tos2 * costR
    ls_ret = ls_ret[ls_ret.index > sd ]
    # tos2 = (ls2 - ls2.shift(1)).abs().sum(1)
    tos2 = tos2[tos2.index > sd ]
    to = round(tos2.mean(),3)
    sr = round( (ls_ret.mean() / ls_ret.std()) * (365*24*60/frq/frq1) ** 0.5,3)
    ar = round( ls_ret.mean() *  (365*24*60/frq/frq1),3)
    ls_ret.index = pd.to_datetime(ls_ret.index)
    ls_ret.cumsum().plot()
    md = round(  max_drawdown(ls_ret) ,2)
    CARMAR =  round(ar/md,2)
    print(name, str(sr) +'  ' +   str(ar) +'  ' +   str(to))
    plt.title("Sharpe:"+ str(sr) +'  ' + "AR:" +  str(ar) +'  ' + "TO:" +  str(to)+'  ' + "MD:" +  str(md)+'  ' + "CARMAR:" +  str(CARMAR)) 
    # plt.show()
    savefigpath = positionRet_path
    plt.savefig(savefigpath  + name + '.png' )
    plt.close()
    return sr


def get_portFromFactor_both(f, num):
    f_ = f.copy()
    f_2 = f_.rank(axis=1,ascending=False,method = 'first')
    hold = f_.fillna(0) * 0
    hold[f_2 <= num] = 1
    hold = hold / of.Repmat(hold,hold.sum(1))
    hold2 = f_.fillna(0) * 0
    hold2[f_2 > of.Repmat(f_2,f_2.max(1)) - num] = 1
    hold2 = hold2 / of.Repmat(hold2,hold2.sum(1)) *-1
    return hold,hold2


def get_snap(df_all_future, frq):
    df_all_future['open'] = df_all_future['open'].shift(frq-1)
    df_all_future['high'] = df_all_future['high'].rolling(frq).max()
    df_all_future['low'] = df_all_future['low'].rolling(frq).min()
    df_all_future['amount'] = df_all_future['amount'].rolling(frq).sum()
    df_all_future['volume'] = df_all_future['volume'].rolling(frq).sum()
    for v in df_all_future.keys():
        df_all_future[v] = df_all_future[v][::frq] 
    df_all_future['vwap'] = df_all_future['amount'] / df_all_future['volume']
    df_all_future['totalRet'] = df_all_future['close'] / df_all_future['close'].shift(1) - 1
    df_all_future['totalRet'][df_all_future['close'].fillna(0) == 0] = np.nan
    df_all_future['totalRet'] = df_all_future['totalRet'].replace([np.inf, -np.inf], np.nan)
    return df_all_future


def get_snap3(df_all_future, frq, mask):
    df_all_future['open'] = df_all_future['open'].shift(frq-1)
    df_all_future['high'] = df_all_future['high'].rolling(frq).max()
    df_all_future['low'] = df_all_future['low'].rolling(frq).min()
    df_all_future['amount'] = df_all_future['amount'].rolling(frq).sum()
    df_all_future['volume'] = df_all_future['volume'].rolling(frq).sum()
    
    for v in df_all_future.keys():
        df_all_future[v] = df_all_future[v][df_all_future[v].index.isin(mask)] 
    df_all_future['vwap'] = df_all_future['amount'] / df_all_future['volume']
    df_all_future['totalRet'] = df_all_future['close'] / df_all_future['close'].shift(1) - 1
    df_all_future['totalRet'][df_all_future['close'].fillna(0) == 0] = np.nan
    df_all_future['totalRet'] = df_all_future['totalRet'].replace([np.inf, -np.inf], np.nan)
    return df_all_future


def get_factorRet(cf_1m,frets,delayNum,sd, name):
    rate2 = frets.copy()
    rate_ave = rate2.mean(1)

    f = cf_1m.copy()
    f, f_up, f_dn = get_ls_post2(f)
    # f_up,f_dn = get_portFromFactor_both(f, 50  )       # 获取多空组合 待优化（）减少换手
    # f = f_up + f_dn
    
    ret = (f.shift(delayNum) * frets).sum(1)
    rets_up = (f_up.shift(delayNum) * frets).sum(1) - rate_ave
    rets_dn = (f_dn.shift(delayNum) * frets).sum(1) + rate_ave
    rs = pd.DataFrame()
    rs['ls'] = ret .copy()
    rs['l'] = rets_up .copy()
    rs['s'] = rets_dn .copy()
    rs = rs[rs.index >  sd ]
    rs.index = pd.to_datetime(rs.index)
    rs.cumsum().plot()
    to = (f - f.shift(1)).abs().sum(1)
    to = to[to.index >  sd ]
    to = to.mean()
    plt.title(str(round(to,2)) + '_' + str(round(rs['ls'].mean()/rs['ls'].std()*(365*(int(1440 / frq1 / frq )))**0.5,2)) )
    plt.show()
    savefigpath = positionRet_path
    plt.savefig(savefigpath + name + '.png' )
    plt.close()
    print(name, str(round(to,2)) + '_' + str(round(rs['ls'].mean()/rs['ls'].std()*(365*int(1440 / frq1 / frq ))**0.5,2)))
    return rs
   


def plot_save(rs,name):
    rs.plot()
    plt.title(name)
    plt.show()
    savefigpath = positionRet_path
    plt.savefig(savefigpath + name + '.png' )
    plt.close()

def tic():
    """记录开始时间"""
    global start_time
    start_time = time.time()

def toc():
    """计算经过的时间并打印结果"""
    if 'start_time' in globals():
        elapsed_time = time.time() - start_time
        print(f"Elapsed time: {elapsed_time:.4f} seconds")
    else:
        print("Call tic() first to start the timer.")


def get_R2(TotalRet_, barraFactor):
    TotalRet = TotalRet_.copy()
    IdioRet = TotalRet.copy()
    for v in barraFactor.keys():
        IdioRet = pn_CrossFit(barraFactor[v], IdioRet)
    y_test = TotalRet.copy()
    y_pred = y_test - IdioRet
    idx = y_pred.abs().sum(1) > 0
    y_test = y_test[idx]
    y_pred =  y_pred[idx]
    SStot = ((y_test - Repmat(y_test, y_test.mean(1))) ** 2).sum(1)
    SSres = ((y_test - y_pred) ** 2).sum(1)
    r2 = 1 - SSres / SStot
    print('R2:',round(r2.tail(int(len(r2)/3*2)).mean(),3))
    
    y_test = y_test.resample('1D').sum()
    y_pred = y_pred.resample('1D').sum()
    SStot = ((y_test - Repmat(y_test, y_test.mean(1))) ** 2).sum(1)
    SSres = ((y_test - y_pred) ** 2).sum(1)
    r2 = 1 - SSres / SStot
    print('R2_1D:',round(r2.tail(int(len(r2)/3*2)).mean(),3))
    
    return IdioRet



def get_minBarraFV_s(df,length):
    dabin = Repmat(df['totalRet'], df['totalRet'][base_coin])
    # dabin = df['totalRet']['BTC']*0.7 + df['totalRet']['ETH']*0.3# + df['totalRet']['DOGE']*0.2 + df['totalRet']['BNB']*0.1
    shift_= 1
    barraFactor = {}
    # barraFactor['SIZ'] = log((ts_Sum(df['amount'], length )  / ts_Sum(df['volume'], length ) ) .shift(shift_))  #
    # barraFactor['VOL'] = log(ts_Stdev(df['totalRet'], length ).shift(shift_))  #
    
    # barraFactor['MOM'] = ( ts_Sum(df['totalRet'] - dabin , length ) / ts_Stdev(df['totalRet'], length ) ).shift(shift_)  # 
    # barraFactor['BTA'] = ts_Corr(df['totalRet'], dabin, length ).shift( shift_)
    # barraFactor['AMT'] = log( ts_Sum(df['amount'], length ).shift(shift_))  # 0.0228318
    # # barraFactor['BTA2'] = ts_Cov(df['totalRet'], Repmat(df['totalRet'], dabin), length ).shift( shift_)
    # barraFactor['CAU'] = ts_Corr(df['totalRet'], dabin.shift(1), length ).shift(shift_)
    # # barraFactor['P2A'] = (ts_Sum(df['totalRet'], length ).abs() / ts_Sum(df['amount'], length )).shift( shift_ )
    # # barraFactor['SKW'] = ts_Skewness(df['totalRet'], length).shift(shift_)  #
    # # barraFactor['KUR'] = ts_Kurtosis(df['totalRet'], length ).shift(shift_)  # 
    # # barraFactor['SPR'] = (ts_Mean(df['totalRet'], length )/ts_Stdev(df['totalRet'], length )).shift(shift_)  

    # barraFactor['alf'] = ts_Regression(df['totalRet'], dabin, length,'B' ).shift( shift_)
    # barraFactor['bet'] = ts_Regression(df['totalRet'], dabin, length,'A' ).shift( shift_)
    barraFactor['bet'] = ts_Regression(df['totalRet'], dabin, length,'A' ).shift( shift_) + ts_Regression(df['totalRet'], dabin, 30,'A' ).shift( shift_)
    # barraFactor['res'] = ts_Regression(df['totalRet'], Repmat(df['totalRet'], dabin), length,'D' ).shift( shift_)
    for v in barraFactor.keys():
        barraFactor[v] = pn_TransNorm(barraFactor[v])# .fillna(0)
        # ret = (barraFactor[v].shift(1) * df['totalRet']).tail(int(len( df['totalRet'])/2)).mean(1)
        # plot_save(ret.cumsum(),v )
    
    IdioRet = get_R2( df['totalRet'].copy().fillna(0), barraFactor)
    
    return barraFactor, IdioRet

def prepareDt(df,expr,lb):
    
    df_D1 = {}
    for v in df.keys():
        df_D1[v] = df[v].copy().shift(TradeDelay)  # TradeDelay = 1   #  TradeDelay
        
    amount_cut = 2000000 # USD
    days = 7
    df_id = df.copy()
    universe_min = (df_id['amount'].rolling( int(1440 / frq1) *days).sum()>amount_cut*days) * 1
    for v in df_id.keys():
        df_id[v][universe_min == 0] = np.nan
    # 对各个品种取特异值收益
    barraFactor, IdioRet = get_minBarraFV_s(df_id,  int(1440 / frq1)* 7)
    
    tmp = df['open'].copy()
    # selected_hours = [1,3,5,7,9,11,13,15,17,19,21,23] 
    # # selected_hours = [0,2,4,6,8,10,12,14,16,18,20,22] 
    # # selected_hours = [0,4,8,12,16,20,22] 
    # mask = (tmp.index.hour.isin(selected_hours)) & (tmp.index.minute == 49)
    mask =  (tmp.index.minute == mt )
    mask = tmp[mask].index
    
    IdioRet = ts_Sum(IdioRet, frq)
    IdioRet = IdioRet[IdioRet.index.isin(mask)]
    
    barraFactorD1 = {}
    for v in barraFactor.keys():
        tmp = barraFactor[v].shift(TradeDelay)    #  TradeDelay
        tmp = tmp[tmp.index.isin(mask)]  #  10 min - 2h
        barraFactorD1[v] = tmp
    
    # barraFactorD1, IdioRetD1 = get_minBarraFV_s(df_D1, 24*1*14)
    # for v in barraFactorD1.keys():
    #     barraFactorD1[v] = barraFactorD1[v][barraFactorD1[v].index.isin(mask)]
        
    dff = get_data_feature_fast_fromExpr(df_D1, expr)

    # 10 min - 2h
    df = get_snap3(df, frq,mask)
    df_D1 = get_snap3(df_D1, frq,mask)
    
    universe = (df_D1['amount'].rolling( int(1440 / frq1 / frq )*days).sum()>amount_cut*days) * 1
    universe = universe * (df_D1['close'] > 0)
    # betas = of.ts_Regression(df_D1['totalRet'], Repmat(df_D1['totalRet'],df_D1['totalRet'][base_coin]),  48*3, 'A')
    # universe = universe * (betas > 0.5)
    # betas = of.ts_Corr(df_D1['totalRet'], Repmat(df_D1['totalRet'],df_D1['totalRet'][base_coin]),  48*3)
    # universe = universe * (betas > 0.3)
    
    # plot_save(universe.sum(1).tail(12000),'zzz')
    # 把那些交易量不足的品种再去掉
    for v in df_D1.keys():
        df_D1[v][universe == 0 ] = np.nan
        
    universe2 = df['close'] > 0
    for v in df.keys():
        df[v][universe2 == 0 ] = np.nan    

    # base = of.Repmat(df['totalRet'],df['totalRet'][base_coin])
    # beta = of.ts_Regression(df['totalRet'], base, 48*lb, 'A')
    # beta[beta < 0.75] = 0.75
    # beta[beta > 3] = 3
    # IdioRet = df['totalRet'] - beta * base
    
    # 不重要
    baseD = of.Repmat(df_D1['totalRet'],df_D1['totalRet'][base_coin])
    betaD = of.ts_Regression(df_D1['totalRet'], baseD,  int(1440 / frq1 / frq )*lb, 'A')
    betaD[betaD < 1] = 1
    betaD[betaD > 3] = 3
    # IdioRetD1 = df_D1['totalRet'] - betaD  * baseD
    
    # coin = 'ETH/USDT:USDT'
    # res = pd.DataFrame()
    # res['tt'] = df['totalRet'][coin]
    # res['bs'] = df['totalRet']['BTC/USDT:USDT']
    # res['res'] = IdioRet[coin]
    # plot_save(res.tail(12000).cumsum(),'ZZ')
      
    for v in dff.keys():
        # dff[v] =  dff[v][::frq]
        dff[v] =  dff[v][dff[v].index.isin(mask)]   #  10 min - 2h
    return dff,  df, IdioRet, universe, betaD, barraFactorD1




def get_total_ccf(df, expr, sd):
    # prepare data
    df_D1,  df, IdioRet, universe, betaD, barraFactorD1 = prepareDt(df,expr,opt_config['lb'])  #    10 min - 2h
    
    # get factor value and factor return
    vals, vals_up, vals_dn, returns, returns_up, returns_dn, universe = get_ccf_1m_p1_neut(df_D1, expr, IdioRet, 1, barraFactorD1, sd, universe,is_pic)
    
    costr = 0.0003
    holdNum = 10

    cf_1 = get_ccf_1m_p2_opt(vals.copy(), returns.copy(), IdioRet, universe, 1, 0 ,sd,3,int(1440/frq/frq1*6))
    cf_1[universe==0] = np.nan
    sr = get_pers(cf_1,IdioRet,df['totalRet'], betaD ,costr,holdNum,sd, 'm' + str(mt) + '_' + str(frq1) + '_' + str(frq) + '_')
    

    return cf_1


def get_last_ccf(df, expr, sd):
    # 检查点3.5*： 计算universe，计算因子特异值收益，计算barra风格因子
    df_D1,  df, IdioRet, universe, betaD, barraFactorD1 = prepareDt(df,expr,opt_config['lb'])  #    10 min - 1h
    print('get data:')
    toc()
    # 检查点4 ： 获取2h数据，进行数据基本准备
    
    vals, vals_up, vals_dn, returns, returns_up, returns_dn, universe = get_ccf_1m_p1_neut(df_D1, expr, IdioRet, 1, barraFactorD1, sd, universe,is_pic)
    print('get factors1:')
    toc()
    # 检查点5* ： 计算因子值和 收益
    # 此函数用来对因子做组合，得到币的评分
    cf_2 = get_ccf_1m_p2_opt(vals.copy(), returns.copy(), IdioRet, universe, 1, 1 ,sd,3,int(1440/frq/frq1*6))
    cf_2[universe==0] = np.nan
    print('get c-mark-factor1:')
    toc()
    # 检查点6 ： 计算因子权重和计算组合因子（等同于机器学习boosting对多因子组合出单一信号）
    
    # rate2 = IdioRet.copy()
    # rate2[universe == 0] = np.nan
    # rate_ave = rate2.mean(1)
    # rate_ave[:10] = np.nan
    # temp_folder = home_dir + r"/realTime/Tmp"
    # results = joblib.Parallel(n_jobs= 4, temp_folder=temp_folder )(
    #     joblib.delayed(of.calculate_fitness9_neut_par)(v, df ,rate_ave, IdioRet, 1, universe, expr) for v in expr  )
    # f, f_up, f_dn, rets, rets_up, rets_dn = zip(*results)
    # vals2 = {}
    # returns2 = pd.DataFrame()
    # for v in range(len(expr)):
    #     vals2[expr[v]] = f[v]
    #     returns2[expr[v]] = rets[v]
    # delete_temp_files(temp_folder)
    # print('get factors2:')
    # toc()
    # cf_2 = get_ccf_1m_p2_opt(vals2, returns2, IdioRet, universe, 1, 1 ,sd,3,48*7)
    # print('get c-mark-factor2:')
    # toc()
    
    return cf_2, betaD


def get_pers(cf_1,IdioRet,totalRet, betaD ,costr, holdNum,sd, name):
    ls_port_up,ls_port_dn = get_portFromFactor_both(cf_1, holdNum )       # 获取多空组合 待优化（）减少换手
    ls_port = ls_port_up + ls_port_dn
    r3 = get_factorRet(cf_1,IdioRet,1,sd,name +  '_Factor_id' )
    r3 = get_factorRet(cf_1,totalRet,1,sd,name +  '_Factor_tt' )
    sr = get_lsRet(ls_port,totalRet, costr,1,sd,name + '_Port' + str(holdNum)+ '_Bt000')
    # ls_port_beta = ls_port / betaD
    # r1 = get_lsRet(ls_port_beta,totalRet, costr,1,sd,name + '_Port' + str(holdNum) + '_Bt000_Beta')
    
    ls_port_dn2 = ls_port_dn.copy()
    ls_port_dn2[base_coin] = 1
    r1 = get_lsRet(ls_port_dn2,totalRet, costr,1,sd,name + '_Port' + str(holdNum) + '_Bt100')
    s_port_beta = ls_port_dn / betaD
    # s_port_beta[base_coin] = 1
    # sr = get_lsRet(s_port_beta,totalRet, costr,1, sd,name + '_Port' + str(holdNum) + '_Bt100_Beta')
    
    # cut = 30
    # ls_port_dn3 = ls_port_dn.copy()
    # ls_port_up3 = ls_port_up.copy()
    # ls_port_up3 = ls_port_up3 * (100 - cut)/100 
    # ls_port_up3['BTC/USDT:USDT'] = cut/100 
    # ls_port3 = ls_port_dn3 + ls_port_up3
    # r1 = get_lsRet(ls_port3,totalRet, costr,1,sd,name + '_Port' + str(holdNum) + '_Bt' + str(cut))
    # ls_port3_beta = ls_port3 / betaD
    # r1 = get_lsRet(ls_port3_beta,totalRet, costr,1,sd,name + '_Port' + str(holdNum) + '_Bt' + str(cut) + '_Beta')
    
    # cut = 50
    # ls_port_up,ls_port_dn = get_portFromFactor_both(cf_1, holdNum )  
    # ls_port_up2,ls_port_dn2 = get_portFromFactor_both(cf_1, int(holdNum/2) )  
    # ls_port_dn3 = ls_port_dn.copy()
    # ls_port_up3 = ls_port_up2.copy()
    # ls_port_up3 = ls_port_up3 * (100 - cut)/100 
    # ls_port_up3['BTC/USDT:USDT'] = cut/100 
    # ls_port3 = ls_port_dn3 + ls_port_up3
    # r1 = get_lsRet(ls_port3,totalRet, costr,1,sd,name + '_Port' + str(holdNum) + '_Bt' + str(cut))
    # ls_port3_beta = ls_port3 / betaD
    # r1 = get_lsRet(ls_port3_beta,totalRet, costr,1,sd,name + '_Port' + str(holdNum) + '_Bt' + str(cut) + '_Beta')
    
    # print()
    
    # zz = Per_QtlRet(cf_1, 10, 0,  IdioRet,1)
    # (zz.mean()*12*365).plot()
    # zz = Per_QtlRet(cf_1, 10, 0,  totalRet,1)
    # (zz.mean()*12*365).plot()
    
    # cor1 = (cf_1.shift(1).corrwith(IdioRet, axis=1))
    # cor1 = cor1[cor1.index > sd]
    # cor1 = cor1.tail(len(cor1) - 10) 
    
    # cor2 = (cf_1.shift(1).corrwith(totalRet, axis=1))
    # cor2 = cor2[cor2.index > sd]
    # cor2 = cor2.tail(len(cor2) - 10) 
    
    # cor1_m = round( cor1.mean(),3)
    # cor2_m = round( cor2.mean(),3)
    
    # plt.title(str(cor1_m) + '__' + str(cor2_m))
    # plt.savefig(positionRet_path +name+ '_Q.png' )
    # plt.close()
    
    return sr


def get_port_fromFactor(totalInvestUSD, cf_1 , close, holdNum):
    ls_port_up,ls_port_dn = get_portFromFactor_both(cf_1, holdNum)       # 获取多空组合 待优化（）减少换手
    ls_port = ls_port_up + ls_port_dn
    ls_port_num = totalInvestUSD * ls_port / close
    port = ls_port_num.iloc[-1]
    port = port[port.abs() > 0].sort_values()
    print('check port00', ls_port.index[-1],ls_port.iloc[-1].sum(), ls_port.iloc[-1].abs().sum())
    
    ls_port3 = ls_port_dn.copy()
    ls_port3['BTC/USDT:USDT'] = 1
    ls_port3_num = totalInvestUSD * ls_port3 / close
    port3 = ls_port3_num.iloc[-1]
    port3 = port3[port3.abs() > 0].sort_values()
    print('check port30', ls_port3.index[-1], ls_port3.iloc[-1].sum(), ls_port3.iloc[-1].abs().sum())

    return port, port3


def GetRet_(f_, TotalRet,delay_):
    f1_stand_D2 = f_.shift(delay_)
    StraRet = f1_stand_D2 * TotalRet
    # StraRet = StraRet.iloc[SDate:EDate]
    StraRetLine = StraRet.mean(1)
    StraRetLine[StraRetLine == 0] = np.nan
    ret = StraRetLine.mean() * 250
    StraRetLine[StraRetLine.isna()] = 0
    return ret, StraRetLine


def pn_Rank_(dfCleaned ):  # normalization
    # dfCleaned = pd.DataFrame({'a':[np.nan,-1.7,5,3],'b':[np.nan,2.9,-3.1,8],'c':[4,np.nan,-6.11,8.1],'d':[7,22,-3.21,81],'e':[9,12,-1.21,11]},index=['one','two','three','four'])
    rank1 = dfCleaned.rank(ascending=True, pct=True,axis = 1)
    count_pn =  Repmat(dfCleaned, ( (~dfCleaned.isna()) * 1 ).sum(1))
    count_pn = 1 / count_pn / 2
    rank2 = rank1 - count_pn
    return rank2


def Per_QtlRet(CF, Q, plotfig, TotalRet, delay_):
    CF2 = CF.copy(deep=True)
    CF2[CF2 == 0] = np.nan
    rank = pn_Rank_(CF2)
    Rets = list()
    RetLine = pd.DataFrame()
    for v in range(Q):
        f_ = rank.copy(deep=True)
        low_ = v / Q
        up_ = v / Q + 1 / Q
        f_[f_ < low_] = np.nan
        f_[f_ >= up_] = np.nan
        f_[~f_.isna()] = 1
        [ret, StraRetLine] = GetRet_(f_, TotalRet,delay_)
        Rets.append(round(ret, 4))
        RetLine[v] = StraRetLine
        # print(Rets)
    if plotfig == 1:
        RetLine.cumsum().plot()
        plt.title('Quantile Return')
    Decayed = pd.DataFrame()
    Decayed['Annualized Return'] = Rets
    Decayed.index = range(Q)
    Decayed.index = Decayed.index + 1
    return RetLine



def delete_temp_files(temp_folder):
    import os
    try:
        # 遍历目录树
        for root, dirs, files in os.walk(temp_folder, topdown=False):
            for file in files:
                file_path = os.path.join(root, file)
                os.remove(file_path)
                # print(f"Deleted file: {file_path}")
            for dir in dirs:
                dir_path = os.path.join(root, dir)
                os.rmdir(dir_path)
                # print(f"Deleted directory: {dir_path}")
        # print(f"All temporary files and directories in {temp_folder} deleted successfully.")
    except Exception as e:
        print(f"Error deleting temporary files: {e}")
        


def get_credentials():
    root = Path(".")
    file_path = f"{root}/credentials.json"

    with open(file_path) as file:

        file = file.read()
        credentials = json.loads(file)

        api_key = credentials["bybit_api_key"]
        api_secret = credentials["bybit_secret_key"]

    return api_key, api_secret


def get_port(totalInvestUSD):
    api_key = 'd1ubUCxROgnt1SYk00'
    secret = '74YWiFb21mZOs0c5U558Xw01AOXdTrPCZ6md'
    # api_key, secret = get_credentials()
    
    global is_pic , factor_path, positionRet_path, base_coin, factor_pic_label, opt_config,frq, frq1,TradeDelay, mt
    is_pic = 0
    factor_path = home_dir + "/realTime/Pic/factor/"
    positionRet_path = home_dir + "/realTime/Pic/total59/"
    base_coin = 'BTC/USDT:USDT'
    factor_pic_label = '_0_'
    opt_config =  {'factorNum': 100, 'lb': 3 }   # 150
    frq = 6
    frq1 = 10
    TradeDelay = 1
    mt = 59
    
    sd0 = '2025-03-01 00:00:00'
    # sd = '2025-03-15 00:00:00'
    end0 = '2025-12-31 00:05:30'
     
    tic()
    path = home_dir + "/realTime/Data/1m_newbar/"
    
    lookback = 100000
    # 初始化 Bybit
    bybit = initialize_bybit(api_key, secret)
    # # 获取并打印 USDT 合约名称
    Open, High, Low, Close, Volume, timeToBeUpdate = tl2.get_initialData2(path, lookback)
    since = bybit.parse8601(tl2.get_startTime(timeToBeUpdate + 1))
    timeframe = '1m'
    # 这一步比较耗时 60s
    Open, High, Low, Close, Volume = tl2.download_dt(bybit,since,timeframe,Open, High, Low, Close, Volume,path)
    print('finish raw data')
    toc()
    df_1min = tl2.clear_data(Open, High, Low, Close, Volume, sd0, end0)
    
    # Open = pd.read_pickle(path + 'open' + '.pkl').replace({None: np.nan})
    # High = pd.read_pickle(path + 'high' + '.pkl').replace({None: np.nan})
    # Low = pd.read_pickle(path + 'low' + '.pkl').replace({None: np.nan})
    # Close = pd.read_pickle(path + 'close' + '.pkl').replace({None: np.nan})
    # Volume = pd.read_pickle(path + 'volume' + '.pkl').replace({None: np.nan})
    # df_1min = tl2.clear_data(Open, High, Low, Close, Volume, sd0, end0)
    
    sd = Open.index.values[15000]  # 业绩的开始计算时间
    # 检查点1* ： 获取1分钟的级别的 Open, High, Low, Close, Volume

    
    df_10min = tl2.get_snap(df_1min, frq1, 9)
    # 检查点2 ： 获取10分钟的级别的 Open, High, Low, Close, Volume

    # expr = pd.read_csv(home_dir + "/realTime/Expr/result_1m10m2h_newBar.csv")
    # expr = pd.read_csv(home_dir + "/realTime/Expr/result_1m10m2h_newBar2.csv")
    # expr = list(expr.sort_values(by = 'result',ascending=False).head(opt_config['factorNum'])['expr'])
    expr = pd.read_csv(home_dir + "/realTime/Expr/result_1m10m2h_0508.csv")
    expr = list(expr.sort_values(by = 'results_is',ascending=False).head(opt_config['factorNum'])['expr'])
    expr.append("pn_GroupNeutral(ts_Delta(df['p6_tn6'],9),Max(df['p5_to2'],ts_Delta(df['p2_et7'],11)))")
    
    
    # # 历史回测
    # df_tt = copy.deepcopy( df_10min )
    # mark = get_total_ccf(df_tt, expr, sd)


    # # 逐次回测 比较
    # df_rt = copy.deepcopy( df_10min )
    # lookback = 3997 
    # dateline = mark.index
    # dat = dateline[-1]
    # for dat in dateline[-20:]:
    #     df_tmp = {}
    #     for ll in df_rt.keys():
    #         df_tmp[ll] = df_rt[ll][df_rt[ll].index <= dat].tail(lookback)
    #     tic()
    #     ccf_rt,betaD = get_last_ccf(df_tmp.copy(), expr, sd)
    #     corr1 = round(np.corrcoef(mark.loc[dat].fillna(0) ,ccf_rt.loc[dat].fillna(0) )[0,1],4)
    #     print(dat, corr1)
    #     print()
    #     if corr1 < 0.97 :
    #         print(dat)
    #         break


    # 实盘
    df_rt = copy.deepcopy(df_10min)
    lookback = 3997 
    df_tmp = {}
    for ll in df_rt.keys():
        df_tmp[ll] = df_rt[ll].tail(lookback)
    # 检查点3 ： 获取固定lookback的数据，只是做了一个切片
    
    ccf_rt_ , betaD = get_last_ccf(df_tmp, expr, sd) # 此函数用来对特征进行组合
    # corr1 = round(np.corrcoef(mark.iloc[-1].fillna(0) ,ccf_rt_.iloc[-1].fillna(0) )[0,1],4)
    # print('CCCCCorr1:',corr1)
    
    # 从因子（对每个币的评分）获取持仓
    holdNum = 10
    port00,port100 = get_port_fromFactor(totalInvestUSD, ccf_rt_ , df_tmp['close'], holdNum)
    print('finish')
    toc()
    # 检查点7 ： 得到仓位

    return port00, port100
    

def format_symbol(s: str) -> str:
    # 分步处理：
    # 1. 按冒号 `:` 分割，取第一部分（忽略结算货币）
    # 2. 按斜杠 `/` 分割，合并所有部分
    return ''.join(s.split(':')[0].split('/'))

def convert_series_to_json(series, instrument_info, output_path):
    # 初始化结果字典
    result = {
        "trading_account_id": 0,
        "max_self_order_time": 10,
        "symbol_target": {}
    }
    
    # 遍历Series的索引和值
    for key, value in series.items():
        # 获取小数位数并格式化数值
        round_value = int(instrument_info.loc[key][2])
        formatted_symbol = format_symbol(key)
        rounded_value = round(value, round_value)
        
        # 直接存储带符号的数值到symbol_target
        result["symbol_target"][formatted_symbol] = rounded_value
    
    # 导出为JSON文件
    with open(output_path, 'w') as f:
        json.dump(result, f, indent=4)
    return result

# 示例用法
if __name__ == '__main__':
    # port = get_port(100000)
    
    import argparse
    # 解析命令行参数
    parser = argparse.ArgumentParser(description="Example script that accepts arguments")
    parser.add_argument("--total_invest_usd", type=float, help="totalInvestUSD", default=300)
    parser.add_argument("--portfolio_target", type=str, help="portfolio_target", default="portfolio_target.json")
    args = parser.parse_args()
    totalInvestUSD = args.total_invest_usd
    tl2.get_contracts()
    port = get_port(totalInvestUSD) # btc00, btc100
    instrument_info = pd.read_csv(home_dir + "/realTime/instrument_info.csv", index_col=-1)
    port[1].to_csv(home_dir + f"/realTime/Code/target_pos/{datetime.now(UTC).strftime("%Y%m%d%H")}.csv")
    convert_series_to_json(port[1], instrument_info, args.portfolio_target)


    
    
    


