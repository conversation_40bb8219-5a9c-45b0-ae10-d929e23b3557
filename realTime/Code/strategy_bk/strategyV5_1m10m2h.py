import ccxt
import os
from datetime import datetime
import pandas as pd
import numpy as np
import optuna

import warnings
warnings.filterwarnings('ignore')
import time
from datetime import datetime
from Comp import get_cf6,get_cf6_rt
import test_operator_funcs as of
from test_operator_funcs import *
# import joblib
from getMinFactor_fast_15min_sele2 import get_data_feature_fast_fromExpr

import matplotlib.pyplot as plt
import tools2 as tl2
import math

from pathlib import Path
home_dir = str(Path.home())

# 初始化 Bybit 交易所
def initialize_bybit(api_key, secret):
    """
    初始化 Bybit 实例
    :param api_key: Bybit API 密钥
    :param secret: Bybit API 密钥
    :return: 初始化后的 Bybit 实例
    """
    bybit = ccxt.bybit({
        'apiKey': api_key,
        'secret': secret,
        'options': {
            'defaultType': 'future',  # 如果是合约交易，设置为 'future'
        },
    })
    return bybit


# 市价下单函数
def place_market_order(bybit, symbol, side, amount):
    """
    市价下单
    :param bybit: 初始化后的 Bybit 实例
    :param symbol: 交易对，例如 'BTC/USDT'
    :param side: 买入或卖出，'buy' 或 'sell'
    :param amount: 下单数量
    :return: 下单结果
    """
    try:
        order = bybit.create_order(
            symbol=symbol,
            type='market',
            side=side,
            amount=amount
        )
        print(f"市价下单成功: {order}")
        return order
    except Exception as e:
        print(f"市价下单失败: {e}")
        return None


# 获取持仓信息函数
def fetch_positions(bybit):
    """
    获取用户当前的持仓信息
    :param bybit: 初始化后的 Bybit 实例
    :return: 持仓信息列表
    """
    try:
        # 调用 Bybit 的合约账户持仓接口
        positions = bybit.get_positions()['result']
        # 过滤出有仓位的合约（未平仓的持仓）
        active_positions = [
            position for position in positions if float(position['size']) != 0
        ]
        # 格式化持仓信息
        formatted_positions = []
        for position in active_positions:
            formatted_positions.append({
                'symbol': position['symbol'],                  # 交易对（如 BTCUSDT）
                'positionAmt': float(position['size']),        # 持仓数量（正数为多仓，负数为空仓）
                'entryPrice': float(position['entry_price']),  # 开仓价格
                'unrealizedProfit': float(position['unrealised_pnl']),  # 未实现盈亏
                'leverage': int(position['leverage']),         # 杠杆倍数
                'marginType': position['position_margin_mode'], # 保证金类型（cross 或 isolated）
                'liquidationPrice': float(position['liq_price']),  # 强平价格
            })
        return formatted_positions
    except Exception as e:
        print(f"获取持仓信息失败: {e}")
        return None


def get_usdt_contracts_by_ccxt():
    # 初始化 Bybit 交易所
    exchange = ccxt.bybit()

    # 获取所有市场信息
    markets = exchange.load_markets()

    # 筛选以 USDT 为计价单位的合约
    usdt_contracts = [symbol for symbol in markets if symbol.endswith("USDT")]

    return usdt_contracts


def calculate_fitness9_neut(_expr3, df ,rate_ave, rate, delayNum, universe, barraFactor):
    _expr = _expr3
    f = eval(_expr) 
    f = f.replace([np.inf, -np.inf], np.nan)
    f[universe == 0] = np.nan
    f = of.pn_TransNorm(f)
    
    # print(rate.tail(5))
    # print(f.tail(5))
    f, f_up,f_dn  = get_ls_post2(f)
    # f_up,f_dn = get_portFromFactor_both(f, 20  )       # 获取多空组合 待优化（）减少换手
    # f = f_up + f_dn

    rests = (f.shift(delayNum) * rate)
    
    rets = (f.shift(delayNum) * rate).sum(1)
    
    winpct = (rests > 0).sum(1) / (f.shift(delayNum).abs() > 0).sum(1) 
    
    rests_win = rests.copy()
    rests_win[rests_win <=0] = np.nan
    rests_win = rests_win.mean(1)
    
    rests_loss = rests.copy()
    rests_loss[rests_loss >=0] = np.nan
    rests_loss = rests_loss.mean(1) * -1
    
    wl = rests_win/rests_loss
    
    # rets[:100] = np.nan
    rets_up = (f_up.shift(delayNum) * rate).sum(1) - rate_ave
    rets_dn = (f_dn.shift(delayNum) * rate).sum(1) + rate_ave
    rets[:100] = 0
    rets_up[:100] = 0
    rets_dn[:100] = 0
    # print( to,_expr3)
    return (f, f_up, f_dn, rets, rets_up, rets_dn, winpct, wl)

def calculate_fitness9_neut2(_expr3, df ,rate_ave, rate, delayNum, universe, barraFactor):
    _expr = _expr3
    f = eval(_expr) 
    f = f.replace([np.inf, -np.inf], np.nan)
    f[universe == 0] = np.nan
    f = of.pn_TransNorm(f)
    
    # print(rate.tail(5))
    # print(f.tail(5))
    f, f_up,f_dn  = get_ls_post2(f)
    # f_up,f_dn = get_portFromFactor_both(f, 50  )       # 获取多空组合 待优化（）减少换手
    # f = f_up + f_dn

    rests = (f.shift(delayNum) * rate)
    
    rets = (f.shift(delayNum) * rate).sum(1)
    
    winpct = (rests > 0).sum(1) / (f.shift(delayNum).abs() > 0).sum(1) 
    
    rests_win = rests.copy()
    rests_win[rests_win <=0] = np.nan
    rests_win = rests_win.mean(1)
    
    rests_loss = rests.copy()
    rests_loss[rests_loss >=0] = np.nan
    rests_loss = rests_loss.mean(1) * -1
    
    wl = rests_win/rests_loss
    
    # rets[:100] = np.nan
    rets_up = (f_up.shift(delayNum) * rate).sum(1) - rate_ave
    rets_dn = (f_dn.shift(delayNum) * rate).sum(1) + rate_ave
    rets[:100] = 0
    rets_up[:100] = 0
    rets_dn[:100] = 0
    # print( to,_expr3)
    return (f, f_up, f_dn, rets, rets_up, rets_dn, winpct, wl)



def calculate_fitness9_neut2_rt(_expr3, df ,rate_ave, rate, delayNum, universe, barraFactor):
    _expr = _expr3
    f = eval(_expr) 
    f = f.replace([np.inf, -np.inf], np.nan)
    f[universe == 0] = np.nan
    f = of.pn_TransNorm(f)
    f, f_up,f_dn  = get_ls_post2(f)
    rests = (f.shift(delayNum) * rate)
    rets = (f.shift(delayNum) * rate).sum(1)
    
    # winpct = (rests > 0).sum(1) / (f.shift(delayNum).abs() > 0).sum(1) 
    # rests_win = rests.copy()
    # rests_win[rests_win <=0] = np.nan
    # rests_win = rests_win.mean(1)
    # rests_loss = rests.copy()
    # rests_loss[rests_loss >=0] = np.nan
    # rests_loss = rests_loss.mean(1) * -1
    # wl = rests_win/rests_loss
    
    # rets[:100] = np.nan
    # rets_up = (f_up.shift(delayNum) * rate).sum(1) - rate_ave
    # rets_dn = (f_dn.shift(delayNum) * rate).sum(1) + rate_ave
    rets[:100] = 0
    # rets_up[:100] = 0
    # rets_dn[:100] = 0
    # print( to,_expr3)
    return (f, f_up, f_dn, rets, rets, rets, rets, rets)


def get_ccf_1m_p1_neut(df, toTest1, rate, delayNum, barraFactor, sd, universe,ispic):
    # 算每一个因子值， 计算他们的多空收益
    vals = {}
    vals_up = {}
    vals_dn = {}
    returns = pd.DataFrame()
    returns_up = pd.DataFrame()
    returns_dn = pd.DataFrame()
    winpcts = pd.DataFrame()
    wls = pd.DataFrame()
    rate2 = rate.copy()
    rate2[universe == 0] = np.nan
    rate_ave = rate2.mean(1)
    rate_ave[:10] = np.nan
    population = toTest1
    for v in range(len(population)):
        _expr = population[v]
        # print(_expr)
        # vals[_expr], vals_up[_expr] , vals_dn[_expr] , returns[_expr] , returns_up[_expr] , returns_dn[_expr], winpcts[_expr], wls[_expr] = calculate_fitness9_neut2(_expr, df, rate_ave, rate, delayNum, universe, barraFactor )
        vals[_expr], vals_up[_expr] , vals_dn[_expr] , returns[_expr] , returns_up[_expr] , returns_dn[_expr], winpcts[_expr], wls[_expr] = calculate_fitness9_neut2_rt(_expr, df, rate_ave, rate, delayNum, universe, barraFactor )
        # '''
        if ispic:
            r = returns[_expr] .copy()
            r = r[r.index >  sd]
            r.index = pd.to_datetime(r.index)
            r2 = returns_up[_expr] .copy()
            r2 = r2[r2.index > sd]
            r2.index = pd.to_datetime(r2.index)
            r3 = returns_dn[_expr] .copy()
            r3 = r3[r3.index > sd]
            r3.index = pd.to_datetime(r3.index)
            
            r.cumsum().plot()
            r2.cumsum().plot()
            r3.cumsum().plot()
            # print(v,'OTS:', round(r2.sum()/(r2.sum() + r3.sum()),3))
            to = (vals[_expr]- vals[_expr].shift(1)).abs().sum(1)
            to = to[to.index >  sd ]
            to = round(to.mean(),3)
            sr = round(r.mean() / r.std() * (365*24*2)**0.5 ,4)
            plt.title(str(v)+'__' + str(sr) +'__'+ str(to))
            plt.show()
            savefigpath = factor_path
            plt.savefig(savefigpath + 'FR_' + str(v)+ factor_pic_label  + str(sr) +'__'+ str(to)+ '.png' )
            plt.close()
    return  vals, vals_up, vals_dn, returns, returns_up, returns_dn, universe, winpcts, wls


def get_ccf_1m_p2_opt(vals, returns,rate, universe, delayNum, is_real_trading,sd,opt1,opt2):
    # 因子组合参数
    config = {}
    config['EDate'] = 5000000000
    if is_real_trading:
        config['SDate'] = len(rate) - 1
    else:
        z = returns[returns.index < sd]
        config['SDate'] = len(z)
    config['cov_method'] = 'ret_cov'
    config['FactorNAmes'] = returns.columns
    config['exRet'] = opt1
    config['TotalRet'] = rate
    config['delayNum'] = delayNum
    config['cost'] = 0.000
    config['fb'] = rate.fillna(0) * 0
    config['get_dire'] = 0
    config['maxWgt'] =  0.1 #1/len(returns.columns) * 10   # max(1/len(returns.columns) * 3,0.1)
    config['universe'] = universe
    config['num'] = 10
    config['base'] = rate.mean(1)
    config['Nrd']  = 5
    
    config['rolling_window'] = opt2   #合成因子阶段，回看长度1   120 
    config['rolling_window_cov']= opt2  #合成因子阶段，回看长度2  120
    
    # if is_real_trading:
    #     cf_ts , wgt = get_cf6_rt(vals,returns ,config)   #
    # else:
    #     cf_ts , wgt = get_cf6(vals,returns ,config)   # (wgt - wgt.shift(1)).abs().sum(1)

    cf_ts , wgt = get_cf6(vals,returns ,config) 
    
    # cf10  = pn_TransNorm(ts_Decay3(cf_ts,nrd,'exp'))
    cf10  = pn_TransNorm(cf_ts)
    cf10, f_up, f_dn = get_ls_post2(cf10)

    return cf10

def get_ccf_1m_p2_opt2(vals, returns,returns_cov,rate, universe, delayNum, is_real_trading,sd,opt1,opt2):
    # 因子组合参数
    config = {}
    config['EDate'] = 5000000000
    if is_real_trading:
        config['SDate'] = len(rate) - 10
    else:
        z = returns[returns.index < sd]
        config['SDate'] = len(z)
    config['cov_method'] = 'ret_cov'
    config['FactorNAmes'] = returns.columns
    config['exRet'] = opt1
    config['TotalRet'] = rate
    config['delayNum'] = delayNum
    config['cost'] = 0.000
    config['fb'] = rate.fillna(0) * 0
    config['get_dire'] = 0
    config['maxWgt'] =  0.15 #1/len(returns.columns) * 10   # max(1/len(returns.columns) * 3,0.1)
    config['universe'] = universe
    config['num'] = 10
    config['base'] = rate.mean(1)
    config['Nrd']  = 5
    
    config['rolling_window'] = opt2   #合成因子阶段，回看长度1   120 
    config['rolling_window_cov']= opt2  #合成因子阶段，回看长度2  120
    cf_ts , wgt = get_cf8(vals,returns ,returns_cov, config)   # (wgt - wgt.shift(1)).abs().sum(1)

    # cf10  = pn_TransNorm(ts_Decay3(cf_ts,nrd,'exp'))
    cf10  = pn_TransNorm(cf_ts)
    cf10, f_up, f_dn = get_ls_post2(cf10)

    return cf10


def get_lsRet(ls2,ret, costR, delayNum,frq,sd,name = '0'):
    tos2 = (ls2 - ls2.shift(1)).abs().sum(1)
    ls_ret = (ls2.shift(delayNum) * ret).sum(1) - tos2 * costR
    ls_ret = ls_ret[ls_ret.index > sd ]
    # tos2 = (ls2 - ls2.shift(1)).abs().sum(1)
    tos2 = tos2[tos2.index > sd ]
    to = round(tos2.mean(),3)
    sr = round( (ls_ret.mean() / ls_ret.std()) * (365*24*60/frq) ** 0.5,3)
    ar = round( ls_ret.mean() * (365*24*60/frq),3)
    ls_ret.index = pd.to_datetime(ls_ret.index)
    ls_ret.cumsum().plot()
    print(name, str(sr) +'  ' +   str(ar) +'  ' +   str(to))
    plt.title(str(sr) +'  ' +   str(ar) +'  ' +   str(to)) 
    # plt.show()
    savefigpath = positionRet_path
    plt.savefig(savefigpath  + name + '.png' )
    plt.close()
    return sr


def get_portFromFactor_both(f, num):
    f_ = f.copy()
    f_2 = f_.rank(axis=1,ascending=False,method = 'first')
    hold = f_.fillna(0) * 0
    hold[f_2 <= num] = 1
    hold = hold / of.Repmat(hold,hold.sum(1))
    hold2 = f_.fillna(0) * 0
    hold2[f_2 > of.Repmat(f_2,f_2.max(1)) - num] = 1
    hold2 = hold2 / of.Repmat(hold2,hold2.sum(1)) *-1
    return hold,hold2


def get_snap(df_all_future, frq):
    df_all_future['open'] = df_all_future['open'].shift(frq-1)
    df_all_future['high'] = df_all_future['high'].rolling(frq).max()
    df_all_future['low'] = df_all_future['low'].rolling(frq).min()
    df_all_future['amount'] = df_all_future['amount'].rolling(frq).sum()
    df_all_future['volume'] = df_all_future['volume'].rolling(frq).sum()
    for v in df_all_future.keys():
        df_all_future[v] = df_all_future[v][::frq] 
    df_all_future['vwap'] = df_all_future['amount'] / df_all_future['volume']
    df_all_future['totalRet'] = df_all_future['close'] / df_all_future['close'].shift(1) - 1
    df_all_future['totalRet'][df_all_future['close'].fillna(0) == 0] = np.nan
    df_all_future['totalRet'] = df_all_future['totalRet'].replace([np.inf, -np.inf], np.nan)
    return df_all_future


def get_snap3(df_all_future, frq, mask):
    df_all_future['open'] = df_all_future['open'].shift(frq-1)
    df_all_future['high'] = df_all_future['high'].rolling(frq).max()
    df_all_future['low'] = df_all_future['low'].rolling(frq).min()
    df_all_future['amount'] = df_all_future['amount'].rolling(frq).sum()
    df_all_future['volume'] = df_all_future['volume'].rolling(frq).sum()
    
    for v in df_all_future.keys():
        df_all_future[v] = df_all_future[v][df_all_future[v].index.isin(mask)] 
    df_all_future['vwap'] = df_all_future['amount'] / df_all_future['volume']
    df_all_future['totalRet'] = df_all_future['close'] / df_all_future['close'].shift(1) - 1
    df_all_future['totalRet'][df_all_future['close'].fillna(0) == 0] = np.nan
    df_all_future['totalRet'] = df_all_future['totalRet'].replace([np.inf, -np.inf], np.nan)
    return df_all_future


def get_factorRet(cf_1m,frets,delayNum,sd, name):
    rate2 = frets.copy()
    rate_ave = rate2.mean(1)

    f = cf_1m.copy()
    f, f_up, f_dn = get_ls_post2(f)
    # f_up,f_dn = get_portFromFactor_both(f, 50  )       # 获取多空组合 待优化（）减少换手
    # f = f_up + f_dn
    
    ret = (f.shift(delayNum) * frets).sum(1)
    rets_up = (f_up.shift(delayNum) * frets).sum(1) - rate_ave
    rets_dn = (f_dn.shift(delayNum) * frets).sum(1) + rate_ave
    rs = pd.DataFrame()
    rs['ls'] = ret .copy()
    rs['l'] = rets_up .copy()
    rs['s'] = rets_dn .copy()
    rs = rs[rs.index >  sd ]
    rs.index = pd.to_datetime(rs.index)
    rs.cumsum().plot()
    to = (f - f.shift(1)).abs().sum(1)
    to = to[to.index >  sd ]
    to = to.mean()
    plt.title(str(round(to,3)) + '_' + str(round(rs['ls'].mean()/rs['ls'].std()*(365*24*2)**0.5,2)) )
    plt.show()
    savefigpath = positionRet_path
    plt.savefig(savefigpath + name + '.png' )
    plt.close()
    print(name, str(round(to,3)) + '_' + str(round(rs['ls'].mean()/rs['ls'].std()*(365*24*2)**0.5,2)))
    return rs
   

def plot_save(rs,name):
    rs.plot()
    plt.title(name)
    plt.show()
    savefigpath = positionRet_path
    plt.savefig(savefigpath + name + '.png' )
    plt.close()

def tic():
    """记录开始时间"""
    global start_time
    start_time = time.time()

def toc():
    """计算经过的时间并打印结果"""
    if 'start_time' in globals():
        elapsed_time = time.time() - start_time
        print(f"Elapsed time: {elapsed_time:.4f} seconds")
    else:
        print("Call tic() first to start the timer.")

def ts_Regression2(s1, s2, n, rettype):
    n = int(n)
    if n <= 2:
        n = 3
    tem1 = s1.copy()
    tem2 = s2.copy()
    tem1[tem2.isna()] = np.nan
    tem2[tem1.isna()] = np.nan
    tem1_m = tem1.rolling(n, axis=0, min_periods=1).mean()
    tem2_m = tem2.rolling(n, axis=0, min_periods=1).mean()
    tem_prod_m = (tem1 * tem2).rolling(n, axis=0, min_periods=1).mean()
    tem2_var = tem2.rolling(n, axis=0, min_periods=1).var(ddof=0)
    beta = (tem_prod_m - tem1_m * tem2_m) / tem2_var
    beta = beta.replace([np.inf, -np.inf], np.nan)
    beta[beta < 1] = 1
    beta[beta > 3 ] = 3
    if rettype == 'A':
        return beta
    const = tem1_m - beta * tem2_m
    if rettype == 'B':
        return const
    y_est = const + beta * tem2
    if rettype == 'C':
        return y_est
    resid = tem1 - y_est
    if rettype == 'D':
        return resid




def get_R2(TotalRet_, barraFactor):
    TotalRet = TotalRet_.copy()
    IdioRet = TotalRet.copy()
    for v in barraFactor.keys():
        IdioRet = pn_CrossFit(barraFactor[v], IdioRet)
    y_test = TotalRet.copy()
    y_pred = y_test - IdioRet
    idx = y_pred.abs().sum(1) > 0
    y_test = y_test[idx]
    y_pred =  y_pred[idx]
    SStot = ((y_test - Repmat(y_test, y_test.mean(1))) ** 2).sum(1)
    SSres = ((y_test - y_pred) ** 2).sum(1)
    r2 = 1 - SSres / SStot
    print('R2:',round(r2.tail(int(len(r2)/3*2)).mean(),3))
    return IdioRet



def get_minBarraFV_s(df,length):
    dabin = Repmat(df['totalRet'], df['totalRet'][base_coin])
    # dabin = df['totalRet']['BTC']*0.7 + df['totalRet']['ETH']*0.3# + df['totalRet']['DOGE']*0.2 + df['totalRet']['BNB']*0.1
    shift_= 1
    barraFactor = {}
    barraFactor['SIZ'] = log((ts_Sum(df['amount'], length )  / ts_Sum(df['volume'], length ) ) .shift(shift_))  #
    barraFactor['VOL'] = log(ts_Stdev(df['totalRet'], length ).shift(shift_))  #
    
    barraFactor['MOM'] = ( ts_Sum(df['totalRet'] - dabin , length ) / ts_Stdev(df['totalRet'], length ) ).shift(shift_)  # 
    barraFactor['BTA'] = ts_Corr(df['totalRet'], dabin, length ).shift( shift_)
    barraFactor['AMT'] = log( ts_Sum(df['amount'], length ).shift(shift_))  # 0.0228318
    # barraFactor['BTA2'] = ts_Cov(df['totalRet'], Repmat(df['totalRet'], dabin), length ).shift( shift_)
    barraFactor['CAU'] = ts_Corr(df['totalRet'], dabin.shift(1), length ).shift(shift_)
    # barraFactor['P2A'] = (ts_Sum(df['totalRet'], length ).abs() / ts_Sum(df['amount'], length )).shift( shift_ )
    # barraFactor['SKW'] = ts_Skewness(df['totalRet'], length).shift(shift_)  #
    # barraFactor['KUR'] = ts_Kurtosis(df['totalRet'], length ).shift(shift_)  # 
    # barraFactor['SPR'] = (ts_Mean(df['totalRet'], length )/ts_Stdev(df['totalRet'], length )).shift(shift_)  

    barraFactor['alf'] = ts_Regression(df['totalRet'], dabin, length,'B' ).shift( shift_)
    barraFactor['bet'] = ts_Regression(df['totalRet'], dabin, length,'A' ).shift( shift_)
    # barraFactor['res'] = ts_Regression(df['totalRet'], Repmat(df['totalRet'], dabin), length,'D' ).shift( shift_)
    for v in barraFactor.keys():
        barraFactor[v] = pn_TransNorm(barraFactor[v])# .fillna(0)
        # ret = (barraFactor[v].shift(1) * df['totalRet']).tail(int(len( df['totalRet'])/2)).mean(1)
        # plot_save(ret.cumsum(),v )
    
    IdioRet = get_R2( df['totalRet'].copy().fillna(0), barraFactor)
    
    return barraFactor, IdioRet

def prepareDt(df,expr,lb):
    
    df_D1 = {}
    for v in df.keys():
        df_D1[v] = df[v].copy().shift(1)  # testing
        
    barraFactor, IdioRet = get_minBarraFV_s(df, 24*1*14)
    
    tmp = df['open'].copy()
    # selected_hours = [1,3,5,7,9,11,13,15,17,19,21,23] 
    selected_hours = [0,2,4,6,8,10,12,14,16,18,20,22] 
    mask = (tmp.index.hour.isin(selected_hours)) & (tmp.index.minute == 49)
    mask = tmp[mask].index
    
    IdioRet = ts_Sum(IdioRet, frq)
    IdioRet = IdioRet[IdioRet.index.isin(mask)]
    
    # barraFactorD1, IdioRetD1 = get_minBarraFV_s(df_D1, 24*1*14)
    # for v in barraFactorD1.keys():
    #     barraFactorD1[v] = barraFactorD1[v][barraFactorD1[v].index.isin(mask)]
        
    dff = get_data_feature_fast_fromExpr(df_D1, expr)

    df = get_snap3(df, frq,mask)
    df_D1 = get_snap3(df_D1, frq,mask)
    
    universe = (df_D1['amount'].rolling(12).sum()>1000000) * 1
    universe = universe * (df_D1['close'] > 0)
    # betas = of.ts_Regression(df_D1['totalRet'], Repmat(df_D1['totalRet'],df_D1['totalRet'][base_coin]),  48*3, 'A')
    # universe = universe * (betas > 0.5)
    # betas = of.ts_Corr(df_D1['totalRet'], Repmat(df_D1['totalRet'],df_D1['totalRet'][base_coin]),  48*3)
    # universe = universe * (betas > 0.3)
    
    # plot_save(universe.sum(1).tail(12000),'zzz')
    
    for v in df_D1.keys():
        df_D1[v][universe == 0 ] = np.nan
        
    universe2 = df['close'] > 0
    for v in df.keys():
        df[v][universe2 == 0 ] = np.nan    

    # base = of.Repmat(df['totalRet'],df['totalRet'][base_coin])
    # beta = of.ts_Regression(df['totalRet'], base, 48*lb, 'A')
    # beta[beta < 0.75] = 0.75
    # beta[beta > 3] = 3
    # IdioRet = df['totalRet'] - beta * base
    
    baseD = of.Repmat(df_D1['totalRet'],df_D1['totalRet'][base_coin])
    betaD = of.ts_Regression(df_D1['totalRet'], baseD,  48*lb, 'A')
    betaD[betaD < 0.75] = 0.75
    betaD[betaD > 3] = 3
    IdioRetD1 = df_D1['totalRet'] - betaD  * baseD
    
    # barraFactorD1, IdioRetD1 = get_minBarraFV_s(df_D1, 48*7)
    # barraFactor, IdioRet = get_minBarraFV_s(df, 48*7)
    
    # IdioRet = df['totalRet']
    
    coin = 'ETH/USDT:USDT'
    res = pd.DataFrame()
    res['tt'] = df['totalRet'][coin]
    res['bs'] = df['totalRet']['BTC/USDT:USDT']
    res['res'] = IdioRet[coin]
    plot_save(res.tail(12000).cumsum(),'ZZ')
      
    for v in dff.keys():
        # dff[v] =  dff[v][::frq]
        dff[v] =  dff[v][dff[v].index.isin(mask)]
    for v in dff.keys():
        df_D1[v] = dff[v]
    df_D1['IdioRet'] = IdioRetD1
    return df_D1,  df, IdioRet, universe, betaD, 0




def get_total_ccf(df, expr, sd, opt_config):
    df_D1,  df, IdioRet, universe, betaD, barraFactorD1 = prepareDt(df,expr,opt_config['lb'])
    
    vals, vals_up, vals_dn, returns, returns_up, returns_dn, universe, winpcts, wls = get_ccf_1m_p1_neut(df_D1, expr, IdioRet, 1, {}, sd, universe,is_pic)
    
    costr = 0.0001
    holdNum = 30
    
    # equal = IdioRet.copy().fillna(0) * 0
    # for v in vals.keys():
    #     tmp = vals[v].copy()
    #     tmp[universe == 0] = np.nan
    #     tmp = pn_TransNorm(tmp).fillna(0)
    #     # tmp = pn_Rank(tmp).fillna(0)
    #     equal = equal + tmp
    # equal[universe == 0] = np.nan
    # equal = pn_TransNorm(equal)
    # sr = get_pers(equal,IdioRet,df['totalRet'], betaD ,costr,holdNum, sd, '0')

    # for v in range(20):
    #     v =(v + 1) 
    #     cf_1 = get_ccf_1m_p2_opt(vals, returns, IdioRet, universe, 1, 0 ,sd,1,12*v)
    #     sr = get_pers(cf_1,IdioRet,df['totalRet'], betaD ,costr,holdNum,sd, 'opt' +  str(v))
    #     print(v, sr)

    cf_1 = get_ccf_1m_p2_opt(vals, returns, IdioRet, universe, 1, 0 ,sd,1,int(24/2*3))
    sr = get_pers(cf_1,IdioRet,df['totalRet'], betaD ,costr,holdNum,sd, 's0')
    
    a = 1
    
    # winpcts[winpcts < 0.5] = 0
    # cf_1 = get_ccf_1m_p2_opt2(vals, winpcts, returns, IdioRet, universe, 1, 0 ,sd,1,int(24/2*6))  
    # sr = get_pers(cf_1,IdioRet,df['totalRet'], betaD ,costr,holdNum,sd, 'wp6')
    
    # cf_1 = get_ccf_1m_p2_opt(vals, returns, IdioRet, universe, 1, 0 ,sd,1,int(24/2*7))
    # sr = get_pers(cf_1,IdioRet,df['totalRet'], betaD ,costr,holdNum,sd, '8')
    
    # cf_s = vals[list(vals.keys())[18]]
    # sr = get_pers(cf_s,IdioRet,df['totalRet'], betaD ,costr,holdNum,sd, '2')

        
    # import optuna
    # def objective(trial):
    #     # 定义优化参数范围
    #     opt_config = {
    #         'lb': trial.suggest_int('lb', 1, 14, step=1),
    #     }

    #     return sr
    
    # study = optuna.create_study(
    # direction='maximize',
    # sampler=optuna.samplers.TPESampler(seed=42)
    # )
    
    # study.optimize(objective, n_trials=300, n_jobs=3,  show_progress_bar=True)
    
    # print("\n最佳参数组合:")
    # print(f"夏普比率: {study.best_value:.4f}")
    # print("优化参数:")
    # for key, value in study.best_params.items():
    #     print(f"{key}: {value}")

    return cf_1


def get_pers(cf_1,IdioRet,totalRet, betaD ,costr, holdNum,sd, name):
    ls_port_up,ls_port_dn = get_portFromFactor_both(cf_1, holdNum )       # 获取多空组合 待优化（）减少换手
    ls_port = ls_port_up + ls_port_dn
    r3 = get_factorRet(cf_1,IdioRet,1,sd,name +  '_Factor_id' )
    r3 = get_factorRet(cf_1,totalRet,1,sd,name +  '_Factor_tt' )
    sr = get_lsRet(ls_port,totalRet, costr,1,frq1*frq,sd,name + '_ls')
    ls_port_dn2 = ls_port_dn.copy()
    ls_port_dn2[base_coin] = 1
    r1 = get_lsRet(ls_port_dn2,totalRet, costr,1,frq1*frq,sd,name + '_lsB')
    ls_port_beta = ls_port / betaD
    r1 = get_lsRet(ls_port_beta,totalRet, costr,1,frq1*frq,sd,name + '_ls_Beta')
    s_port_beta = ls_port_dn / betaD
    s_port_beta[base_coin] = 1
    sr = get_lsRet(s_port_beta,totalRet, costr,1,frq1*frq,sd,name + '_lsB_Beta')
    print()
    return sr


def get_pers2(cf_1,IdioRet,totalRet, betaD ,costr, holdNum,sd, name):
    ls_port_up,ls_port_dn = get_portFromFactor_both(cf_1, holdNum )       # 获取多空组合 待优化（）减少换手
    ls_port = ls_port_up + ls_port_dn
    r3 = get_factorRet(cf_1,IdioRet,1,sd,name +  '_Factor_id' )
    r3 = get_factorRet(cf_1,totalRet,1,sd,name +  '_Factor_tt' )
    sr = get_lsRet(ls_port_up,totalRet, costr,1,frq1*frq,sd,name + '_l')
    ls_port_up2 = ls_port_dn.copy()
    ls_port_up2[base_coin] = -1
    r1 = get_lsRet(ls_port_up2,totalRet, costr,1,frq1*frq,sd,name + '_lsB')
    ls_port_beta = ls_port_up / betaD
    r1 = get_lsRet(ls_port_beta,totalRet, costr,1,frq1*frq,sd,name + '_l_Beta')
    s_port_beta = ls_port_up / betaD
    s_port_beta[base_coin] = -1
    sr = get_lsRet(s_port_beta,totalRet, costr,1,frq1*frq,sd,name + '_lsB_Beta')
    print()
    return sr


def delete_temp_files(temp_folder):
    import os
    try:
        # 遍历目录树
        for root, dirs, files in os.walk(temp_folder, topdown=False):
            for file in files:
                file_path = os.path.join(root, file)
                os.remove(file_path)
                # print(f"Deleted file: {file_path}")
            for dir in dirs:
                dir_path = os.path.join(root, dir)
                os.rmdir(dir_path)
                # print(f"Deleted directory: {dir_path}")
        # print(f"All temporary files and directories in {temp_folder} deleted successfully.")
    except Exception as e:
        print(f"Error deleting temporary files: {e}")
        
def get_last_ccf(df, expr, sd):
    df_D1,  df, IdioRet, universe, betaD, barraFactorD1 = prepareDt(df,expr,opt_config['lb'])
    print('get data:')
    toc()
    
    vals, vals_up, vals_dn, returns, returns_up, returns_dn, universe, winpcts, wls = get_ccf_1m_p1_neut(df_D1, expr, IdioRet, 1, {}, sd, universe,is_pic)
    print('get factors1:')
    toc()
    
    cf_1 = get_ccf_1m_p2_opt(vals, returns, IdioRet, universe, 1, 1 ,sd,1,int(24/2*3))
    print('get c-mark-factor1:')
    toc()
    
    # rate2 = IdioRet.copy()
    # rate2[universe == 0] = np.nan
    # rate_ave = rate2.mean(1)
    # rate_ave[:10] = np.nan
    # temp_folder = home_dir + r"/realTime/Tmp"
    # results = joblib.Parallel(n_jobs= 4, temp_folder=temp_folder )(
    #     joblib.delayed(of.calculate_fitness9_neut_par)(v, df ,rate_ave, IdioRet, 1, universe, expr) for v in expr  )
    # f, f_up, f_dn, rets, rets_up, rets_dn = zip(*results)
    # vals2 = {}
    # returns2 = pd.DataFrame()
    # for v in range(len(expr)):
    #     vals2[expr[v]] = f[v]
    #     returns2[expr[v]] = rets[v]
    # delete_temp_files(temp_folder)
    # print('get factors2:')
    # toc()
    # cf_2 = get_ccf_1m_p2_opt(vals2, returns2, IdioRet, universe, 1, 1 ,sd,3,48*7)
    # print('get c-mark-factor2:')
    # toc()
    


    
    return cf_1 


# 示例用法
if __name__ == '__main__':
    # Bybit API 密钥
    api_key = '1Vo3MyCK4SJdjRlUay'
    secret = 'n1W9eMAsigOkG04yLXdc3PTPO5BqyQkTjhBc'
    frq = 12
    frq1 = 10
     
    tic()
    path = home_dir + '/realTime/Data/1m/'
    
    lookback = 100000
    # 初始化 Bybit
    bybit = initialize_bybit(api_key, secret)
    # # 获取并打印 USDT 合约名称
    # usdt_contracts_current = get_usdt_contracts_by_ccxt()
    Open, High, Low, Close, Volume, timeToBeUpdate = tl2.get_initialData(path, lookback)
    # since = bybit.parse8601('2025-01-01T00:00:00Z') 
    since = bybit.parse8601(tl2.get_startTime(timeToBeUpdate + 1))
    timeframe = '1m'   
    Open, High, Low, Close, Volume = tl2.download_dt(bybit,since,timeframe,Open, High, Low, Close, Volume,path)
    print('finish raw data')
    toc()
    sd0 = '2025-03-05 00:00:00'
    sd = '2025-03-25 00:00:00'
    end0 = '2025-12-31 00:05:30'
    df = tl2.clear_data(Open, High, Low, Close, Volume, sd0, end0)
    
    # Open = pd.read_pickle(path + 'open' + '.pkl').replace({None: np.nan})
    # High = pd.read_pickle(path + 'high' + '.pkl').replace({None: np.nan})
    # Low = pd.read_pickle(path + 'low' + '.pkl').replace({None: np.nan})
    # Close = pd.read_pickle(path + 'close' + '.pkl').replace({None: np.nan})
    # Volume = pd.read_pickle(path + 'volume' + '.pkl').replace({None: np.nan})
    
    df = tl2.get_snap(df, frq1 ,9)
    df['vwap'] = (df['open']+df['high']+df['low']+df['close']) * 4
    df['amount'] = df['vwap']  * df['volume']
    df['totalRet'] = df['close'] / df['close'].shift(1) - 1
    df['totalRet'][df['close'].fillna(0) == 0] = np.nan
    df['totalRet'] = df['totalRet'].replace([np.inf, -np.inf], np.nan) 
    
    global is_pic , factor_path, positionRet_path, base_coin, factor_pic_label
    is_pic = 0
    # factor_path = home_dir + '/realTime/Pic/factor/'
    # positionRet_path =  home_dir + '/realTime/Pic/'
    factor_path = home_dir + '/realTime/Pic/factor/'
    positionRet_path = home_dir + '/realTime/Pic/total2/'
    base_coin = 'BTC/USDT:USDT'
    factor_pic_label = '_0_'

    opt_config =  {'factorNum': 300, 'lb': 7 } 
    expr = pd.read_csv(home_dir + '/realTime/Expr/result_1m10m2h.csv')
    expr = list(expr.sort_values(by = 'result',ascending=False).head(opt_config['factorNum'])['expr'])

    
    df_tt = df.copy()
    mark = get_total_ccf(df_tt, expr, sd, opt_config)
    a = 1
    
    
    # import optuna
    # def objective(trial):
    #     # 定义优化参数范围
    #     opt_config = {
    #         'factorNum': trial.suggest_int('factorNum', 30, 100, step=1),
    #         'lb': trial.suggest_int('lb', 1, 14, step=1),
    #     }
    #     expr =  pd.read_csv(home_dir + '/realTime/Expr/result_pncut2.csv')
    #     expr = list(expr.sort_values(by = 'result',ascending=False).head(opt_config['factorNum'])['expr'])
    #     df_tt = df.copy()
    #     sr = get_total_ccf(df_tt, expr, sd,  opt_config)
    #     return sr
    
    # study = optuna.create_study(
    # direction='maximize',
    # sampler=optuna.samplers.TPESampler(seed=42)
    # )
    
    # study.optimize(objective, n_trials=300, n_jobs=3,  show_progress_bar=True)
    
    # print("\n最佳参数组合:")
    # print(f"夏普比率: {study.best_value:.4f}")
    # print("优化参数:")
    # for key, value in study.best_params.items():
    #     print(f"{key}: {value}")

    df_rt = df.copy()    
    sd_s = pd.to_datetime('2025-04-06 00:00:00', format="%Y-%m-%d %H:%M:%S")
    lookback = 2997 
    dateline = mark.index
    dat = dateline[-10]
    for dat in dateline[-5:]:
        if dat < sd_s:
            continue
        df_tmp = {}
        for ll in df_rt.keys():
            df_tmp[ll] = df[ll][df[ll].index <= dat].tail(lookback)
            # df_tmp[ll] = df_rt[ll].copy().tail(lookback)
        tic()
        ccf_rt = get_last_ccf(df_tmp, expr, sd)
        # toc()
        corr1 = round(np.corrcoef(mark.loc[dat] ,ccf_rt.loc[dat] )[0,1],4)
        # corr2 = round(np.corrcoef(equal.loc[dat].fillna(0)  ,equal_rt.loc[dat].fillna(0)  )[0,1],4)
        print(dat, corr1)
        print()


        if corr1 < 0.98 :
            # print(dat,)
            break
    
    a = 1
    
    # 实盘
    tic()
    df_rt = df.copy()    
    lookback = 2997 
    df_tmp = {}
    for ll in df_rt.keys():
        df_tmp[ll] = df[ll].tail(lookback)
    ccf_rt_ = get_last_ccf(df_tmp, expr, sd)
    print(round(np.corrcoef(mark.iloc[-1] ,ccf_rt_.iloc[-1] )[0,1],4))
    