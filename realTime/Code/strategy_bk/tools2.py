from datetime import datetime, UTC, timedelta
import os
import pandas as pd
import numpy as np

# 普通时间转换会iso时间

def timeConvert1(timestamp_str):
    # timestamp_str = '2025-03-12 07:05:00'
    if not type(timestamp_str) == str:
        timestamp_str = str(timestamp_str)
    dt = datetime.strptime(timestamp_str, '%Y-%m-%d %H:%M:%S')
    iso_format = dt.strftime('%Y-%m-%dT%H:%M:%SZ')
    # iso_format = datetime.strptime(iso_format, '%Y-%m-%dT%H:%M:%SZ')
    return iso_format


# 获取当前的 ISO 8601 时间
def get_nowISO():
    now_iso = datetime.now(UTC).strftime('%Y-%m-%dT%H:%M:%SZ')
    # now = datetime.strptime(now_iso, '%Y-%m-%dT%H:%M:%SZ')
    return now_iso


def get_nowISO2():
    now_iso = datetime.now(UTC)
    # now = datetime.strptime(now_iso, '%Y-%m-%dT%H:%M:%SZ')
    return now_iso

def get_timedelta(now,iso_format):
    iso_format = datetime.strptime(iso_format, '%Y-%m-%dT%H:%M:%SZ')
    now = datetime.strptime(now, '%Y-%m-%dT%H:%M:%SZ')
    time_difference = (now - iso_format ).total_seconds()
    # 计算 1 分钟 的间隔数
    five_minute_intervals = int(time_difference // (1 * 60))
    return five_minute_intervals

def get_startTime(de):
    now = datetime.now(UTC)
    now_iso = now.strftime('%Y-%m-%dT%H:%M:%SZ')

    # 计算 1000 个 1 分钟的总时间差
    time_difference = timedelta(minutes=1 * de)

    # 减去时间差
    result_time = now - time_difference

    # 转换为 ISO 8601 格式
    result_iso = result_time.strftime('%Y-%m-%dT%H:%M:%SZ')
    return result_iso

def get_contracts():
    return ['JAILSTOOL/USDT:USDT', 'ANKR/USDT:USDT', 'ME/USDT:USDT', 'MON/USDT:USDT', 'SUN/USDT:USDT', 'KSM/USDT:USDT', 'OMNI/USDT:USDT', 'ATH/USDT:USDT', 'OG/USDT:USDT', 'ETHBTC/USDT:USDT', 'W/USDT:USDT', 'ILV/USDT:USDT', 'SONIC/USDT:USDT', 'PHB/USDT:USDT', 'MAX/USDT:USDT', 'STRK/USDT:USDT', 'BIO/USDT:USDT', 'LTO/USDT:USDT', 'MOBILE/USDT:USDT', '10000LADYS/USDT:USDT', 'ETHW/USDT:USDT', 'USUAL/USDT:USDT', 'HOT/USDT:USDT', 'ZIL/USDT:USDT', 'AERGO/USDT:USDT', 'J/USDT:USDT', 'XTER/USDT:USDT', 'ROAM/USDT:USDT', 'BUZZ/USDT:USDT', 'CFX/USDT:USDT', 'RON/USDT:USDT', 'MERL/USDT:USDT', 'GTC/USDT:USDT', 'ICX/USDT:USDT', 'FORTH/USDT:USDT', '1000PEPE/USDT:USDT', 'HPOS10I/USDT:USDT', 'XDC/USDT:USDT', 'OM/USDT:USDT', 'ORDI/USDT:USDT', 'CETUS/USDT:USDT', 'XLM/USDT:USDT', 'ZBCN/USDT:USDT', 'ONT/USDT:USDT', 'SPELL/USDT:USDT', '1000LUNC/USDT:USDT', 'SCA/USDT:USDT', 'SYS/USDT:USDT', 'STMX/USDT:USDT', 'MDT/USDT:USDT', 'VIDT/USDT:USDT', 'TLM/USDT:USDT', 'GOAT/USDT:USDT', 'ALPHA/USDT:USDT', '10000ELON/USDT:USDT', 'AAVE/USDT:USDT', 'F/USDT:USDT', 'AVAX/USDT:USDT', 'SIREN/USDT:USDT', 'COW/USDT:USDT', 'QUICK/USDT:USDT', 'FWOG/USDT:USDT', 'LDO/USDT:USDT', 'SEI/USDT:USDT', 'FLR/USDT:USDT', 'BRETT/USDT:USDT', 'MINA/USDT:USDT', 'SWARMS/USDT:USDT', 'XRP/USDT:USDT', 'SEND/USDT:USDT', 'REZ/USDT:USDT', 'LIT/USDT:USDT', 'SHIB1000/USDT:USDT', 'RED/USDT:USDT', 'ATOM/USDT:USDT', 'QTUM/USDT:USDT', 'DUCK/USDT:USDT', 'TRB/USDT:USDT', 'ZEREBRO/USDT:USDT', 'MYRIA/USDT:USDT', 'PHA/USDT:USDT', 'AUCTION/USDT:USDT', 'ZEUS/USDT:USDT', 'BIGTIME/USDT:USDT', 'ORCA/USDT:USDT', 'USDE/USDT:USDT', 'RSR/USDT:USDT', 'ARB/USDT:USDT', 'GALA/USDT:USDT', 'TRUMP/USDT:USDT', 'TOKEN/USDT:USDT', 'LINK/USDT:USDT', 'SOLV/USDT:USDT', 'KAVA/USDT:USDT', 'BLZ/USDT:USDT', 'RENDER/USDT:USDT', 'HNT/USDT:USDT', 'CAKE/USDT:USDT', 'SLP/USDT:USDT', '1000CAT/USDT:USDT', 'GRASS/USDT:USDT', 'CTC/USDT:USDT', 'BNT/USDT:USDT', 'MASA/USDT:USDT', 'FIL/USDT:USDT', 'REN/USDT:USDT', 'XEM/USDT:USDT', 'GPS/USDT:USDT', 'MELANIA/USDT:USDT', 'BERA/USDT:USDT', 'DOGE/USDT:USDT', 'DYM/USDT:USDT', 'JST/USDT:USDT', 'SUI/USDT:USDT', 'FDUSD/USDT:USDT', 'RAYDIUM/USDT:USDT', 'ALPACA/USDT:USDT', 'ZRX/USDT:USDT', 'NULS/USDT:USDT', 'ACX/USDT:USDT', 'VR/USDT:USDT', 'AIOZ/USDT:USDT', 'ZEC/USDT:USDT', 'FIRE/USDT:USDT', 'ARPA/USDT:USDT', 'PIXEL/USDT:USDT', 'CARV/USDT:USDT', 'AEVO/USDT:USDT', 'BICO/USDT:USDT', 'REX/USDT:USDT', 'MOVR/USDT:USDT', 'SERAPH/USDT:USDT', 'CVC/USDT:USDT', '1000000PEIPEI/USDT:USDT', 'AIXBT/USDT:USDT', 'KMNO/USDT:USDT', 'BB/USDT:USDT', 'RPL/USDT:USDT', 'TAIKO/USDT:USDT', 'EDU/USDT:USDT', 'DOG/USDT:USDT', 'RUNE/USDT:USDT', 'BAT/USDT:USDT', '1000RATS/USDT:USDT', 'AXS/USDT:USDT', 'MOODENG/USDT:USDT', 'BAN/USDT:USDT', 'DYDX/USDT:USDT', 'VET/USDT:USDT', 'KNC/USDT:USDT', '1000TURBO/USDT:USDT', 'KAS/USDT:USDT', 'POPCAT/USDT:USDT', 'SPEC/USDT:USDT', 'TUT/USDT:USDT', 'MKR/USDT:USDT', 'UMA/USDT:USDT', 'MASK/USDT:USDT', 'GMX/USDT:USDT', 'DBR/USDT:USDT', 'LISTA/USDT:USDT', 'JUP/USDT:USDT', 'PIRATE/USDT:USDT', 'BILLY/USDT:USDT', 'DAR/USDT:USDT', 'ONG/USDT:USDT', 'CKB/USDT:USDT', 'VELO/USDT:USDT', '1000CATS/USDT:USDT', 'C98/USDT:USDT', 'LTC/USDT:USDT', 'OP/USDT:USDT', '1000XEC/USDT:USDT', 'MEME/USDT:USDT', 'NIL/USDT:USDT', 'BOME/USDT:USDT', 'PRIME/USDT:USDT', 'PENDLE/USDT:USDT', 'ZETA/USDT:USDT', 'MTL/USDT:USDT', 'AI16Z/USDT:USDT', 'ZKJ/USDT:USDT', '10000SATS/USDT:USDT', 'YFI/USDT:USDT', 'MAGIC/USDT:USDT', 'VELODROME/USDT:USDT', 'DASH/USDT:USDT', 'MAV/USDT:USDT', 'ZRO/USDT:USDT', 'L3/USDT:USDT', 'SSV/USDT:USDT', 'QNT/USDT:USDT', 'NEAR/USDT:USDT', 'MNT/USDT:USDT', 'DODO/USDT:USDT', 'RARE/USDT:USDT', 'GMT/USDT:USDT', 'SFP/USDT:USDT', 'DATA/USDT:USDT', '1INCH/USDT:USDT', 'PYR/USDT:USDT', 'AXL/USDT:USDT', 'S/USDT:USDT', 'STORJ/USDT:USDT', 'BADGER/USDT:USDT', 'KDA/USDT:USDT', 'BAL/USDT:USDT', 'CPOOL/USDT:USDT', 'KAITO/USDT:USDT', 'MOVE/USDT:USDT', 'XAI/USDT:USDT', 'USTC/USDT:USDT', 'STG/USDT:USDT', 'RLC/USDT:USDT', 'SILLY/USDT:USDT', 'DENT/USDT:USDT', 'ASTR/USDT:USDT', 'COS/USDT:USDT', 'PARTI/USDT:USDT', 'BENDOG/USDT:USDT', 'GODS/USDT:USDT', 'XCH/USDT:USDT', 'VIRTUAL/USDT:USDT', 'ANIME/USDT:USDT', 'HBAR/USDT:USDT', 'SYN/USDT:USDT', 'VVV/USDT:USDT', 'ZENT/USDT:USDT', 'OXT/USDT:USDT', 'DOP1/USDT:USDT', 'TAI/USDT:USDT', 'BEL/USDT:USDT', 'SNT/USDT:USDT', 'JASMY/USDT:USDT', 'SCRT/USDT:USDT', 'UXLINK/USDT:USDT', 'CHR/USDT:USDT', 'DUSK/USDT:USDT', 'JOE/USDT:USDT', 'TAO/USDT:USDT', 'MEW/USDT:USDT', 'SPX/USDT:USDT', 'IOTX/USDT:USDT', 'HMSTR/USDT:USDT', 'B3/USDT:USDT', 'MORPHO/USDT:USDT', 'NEO/USDT:USDT', 'AI/USDT:USDT', 'CTK/USDT:USDT', 'MAJOR/USDT:USDT', 'XVS/USDT:USDT', 'SC/USDT:USDT', '10000COQ/USDT:USDT', 'FOXY/USDT:USDT', 'EGLD/USDT:USDT', 'DEXE/USDT:USDT', 'NFP/USDT:USDT', 'LINA/USDT:USDT', 'THE/USDT:USDT', 'ADA/USDT:USDT', 'ALU/USDT:USDT', 'SLF/USDT:USDT', 'GME/USDT:USDT', 'CELO/USDT:USDT', 'SUNDOG/USDT:USDT', 'DOGS/USDT:USDT', '10000QUBIC/USDT:USDT', 'WAVES/USDT:USDT', 'ALGO/USDT:USDT', 'ENJ/USDT:USDT', 'DEGEN/USDT:USDT', 'TIA/USDT:USDT', 'PUFFER/USDT:USDT', 'LAI/USDT:USDT', 'ETHFI/USDT:USDT', 'PRCL/USDT:USDT', 'APE/USDT:USDT', 'LUCE/USDT:USDT', 'AMB/USDT:USDT', 'SAND/USDT:USDT', 'ARK/USDT:USDT', 'FTM/USDT:USDT', '10000000AIDOGE/USDT:USDT', 'COOK/USDT:USDT', 'CLOUD/USDT:USDT', '10000WEN/USDT:USDT', 'NMR/USDT:USDT', 'NC/USDT:USDT', 'GRIFFAIN/USDT:USDT', 'FB/USDT:USDT', 'NS/USDT:USDT', 'ETH/USDT:USDT', 'BSV/USDT:USDT', 'ALICE/USDT:USDT', 'HYPE/USDT:USDT', 'KOMA/USDT:USDT', 'SWEAT/USDT:USDT', 'ALT/USDT:USDT', '1000BTT/USDT:USDT', 'COOKIE/USDT:USDT', 'CELR/USDT:USDT', 'SD/USDT:USDT', 'COMP/USDT:USDT', 'PENG/USDT:USDT', 'JTO/USDT:USDT', 'PNUT/USDT:USDT', '1000BONK/USDT:USDT', 'THETA/USDT:USDT', 'AGLD/USDT:USDT', 'ALEO/USDT:USDT', 'LEVER/USDT:USDT', 'PONKE/USDT:USDT', 'XRD/USDT:USDT', '1CAT/USDT:USDT', 'DOT/USDT:USDT', 'PYTH/USDT:USDT', 'PERP/USDT:USDT', '1000NEIROCTO/USDT:USDT', 'ONDO/USDT:USDT', 'BOND/USDT:USDT', 'BEAM/USDT:USDT', 'LPT/USDT:USDT', 'VTHO/USDT:USDT', 'GOMINING/USDT:USDT', 'BNB/USDT:USDT', 'BR/USDT:USDT', 'AGI/USDT:USDT', 'EPIC/USDT:USDT', 'GNO/USDT:USDT', 'G/USDT:USDT', 'TWT/USDT:USDT', 'TRU/USDT:USDT', 'ARC/USDT:USDT', 'BSW/USDT:USDT', 'DRIFT/USDT:USDT', 'LRC/USDT:USDT', 'HIGH/USDT:USDT', 'XMR/USDT:USDT', 'BAKE/USDT:USDT', 'ACH/USDT:USDT', 'AVAIL/USDT:USDT', 'HOOK/USDT:USDT', 'GLM/USDT:USDT', 'SXP/USDT:USDT', 'GLMR/USDT:USDT', 'LUNA2/USDT:USDT', 'CRV/USDT:USDT', 'ZEN/USDT:USDT', 'VRA/USDT:USDT', 'HIVE/USDT:USDT', 'ZRC/USDT:USDT', 'TON/USDT:USDT', 'HIFI/USDT:USDT', 'SOL/USDT:USDT', 'SOLO/USDT:USDT', 'REEF/USDT:USDT', 'RONIN/USDT:USDT', 'SNX/USDT:USDT', 'IOST/USDT:USDT', 'MBOX/USDT:USDT', 'SOLAYER/USDT:USDT', 'WIF/USDT:USDT', 'RSS3/USDT:USDT', 'ORBS/USDT:USDT', '1000TOSHI/USDT:USDT', '10000WHY/USDT:USDT', 'BCH/USDT:USDT', 'VIC/USDT:USDT', 'AVAAI/USDT:USDT', 'FTN/USDT:USDT', 'ROSE/USDT:USDT', 'ICP/USDT:USDT', 'HEI/USDT:USDT', 'MANA/USDT:USDT', 'OMG/USDT:USDT', 'APT/USDT:USDT', 'CATI/USDT:USDT', 'PROM/USDT:USDT', 'ETC/USDT:USDT', 'VOXEL/USDT:USDT', 'MOCA/USDT:USDT', 'T/USDT:USDT', 'HIPPO/USDT:USDT', 'ENA/USDT:USDT', 'WOO/USDT:USDT', 'KAIA/USDT:USDT', 'FLUX/USDT:USDT', 'STX/USDT:USDT', 'VANRY/USDT:USDT', 'PIPPIN/USDT:USDT', 'CHESS/USDT:USDT', 'DEEP/USDT:USDT', 'POLYX/USDT:USDT', 'NOT/USDT:USDT', 'FUEL/USDT:USDT', 'FIDA/USDT:USDT', 'RIF/USDT:USDT', 'TROY/USDT:USDT', 'AUDIO/USDT:USDT', 'STPT/USDT:USDT', 'BNX/USDT:USDT', 'GIGA/USDT:USDT', 'REQ/USDT:USDT', '1000FLOKI/USDT:USDT', 'EOS/USDT:USDT', 'MBL/USDT:USDT', 'PENGU/USDT:USDT', 'ELX/USDT:USDT', 'EIGEN/USDT:USDT', 'SKL/USDT:USDT', '1000APU/USDT:USDT', 'NKN/USDT:USDT', 'IDEX/USDT:USDT', 'ZK/USDT:USDT', 'AKT/USDT:USDT', 'POWR/USDT:USDT', 'API3/USDT:USDT', 'LQTY/USDT:USDT', 'MANEKI/USDT:USDT', 'GRT/USDT:USDT', 'CYBER/USDT:USDT', 'FLOCK/USDT:USDT', 'BAND/USDT:USDT', 'UNI/USDT:USDT', 'MEMEFI/USDT:USDT', 'IO/USDT:USDT', 'COTI/USDT:USDT', 'XNO/USDT:USDT', 'PEAQ/USDT:USDT', 'XCN/USDT:USDT', 'ORDER/USDT:USDT', 'ACE/USDT:USDT', 'AVL/USDT:USDT', 'ACT/USDT:USDT', 'STEEM/USDT:USDT', 'BROCCOLI/USDT:USDT', '1000000MOG/USDT:USDT', 'NYAN/USDT:USDT', 'MOTHER/USDT:USDT', 'CGPT/USDT:USDT', 'IMX/USDT:USDT', 'XTZ/USDT:USDT', 'RDNT/USDT:USDT', 'FLOW/USDT:USDT', 'VANA/USDT:USDT', 'SWELL/USDT:USDT', 'GEMS/USDT:USDT', 'PLUME/USDT:USDT', 'BOBA/USDT:USDT', 'SHELL/USDT:USDT', 'SCR/USDT:USDT', 'AKRO/USDT:USDT', 'BTC/USDT:USDT', 'RIFSOL/USDT:USDT', 'KEY/USDT:USDT', 'LSK/USDT:USDT', 'AR/USDT:USDT', 'XION/USDT:USDT', '1000MUMU/USDT:USDT', 'BLAST/USDT:USDT', 'MYRO/USDT:USDT', 'ID/USDT:USDT', 'QI/USDT:USDT', 'CTSI/USDT:USDT', 'MANTA/USDT:USDT', 'HFT/USDT:USDT', 'TNSR/USDT:USDT', 'JELLYJELLY/USDT:USDT', 'MAVIA/USDT:USDT', 'A8/USDT:USDT', 'RVN/USDT:USDT', 'FORM/USDT:USDT', 'SAFE/USDT:USDT', 'OGN/USDT:USDT', 'OSMO/USDT:USDT', 'INJ/USDT:USDT', 'AVA/USDT:USDT', 'SAGA/USDT:USDT', 'WLD/USDT:USDT', 'VINE/USDT:USDT', 'CHILLGUY/USDT:USDT', 'ATA/USDT:USDT', 'CRO/USDT:USDT', 'URO/USDT:USDT', 'GAS/USDT:USDT', 'USDC/USDT:USDT', 'XVG/USDT:USDT', 'PORTAL/USDT:USDT', 'SLERF/USDT:USDT', 'TRX/USDT:USDT', 'MUBARAK/USDT:USDT', 'FIO/USDT:USDT', 'POL/USDT:USDT', 'NEIROETH/USDT:USDT', 'BMT/USDT:USDT', 'ENS/USDT:USDT', 'WAXP/USDT:USDT', 'ALCH/USDT:USDT', 'LUMIA/USDT:USDT', 'AERO/USDT:USDT', 'TSTBSC/USDT:USDT', '1000X/USDT:USDT', 'DGB/USDT:USDT', '1000000CHEEMS/USDT:USDT', 'MVL/USDT:USDT', 'BLUR/USDT:USDT', 'BLUE/USDT:USDT', 'LOOKS/USDT:USDT', 'CVX/USDT:USDT', 'IP/USDT:USDT', 'OL/USDT:USDT', 'PAXG/USDT:USDT', 'CHZ/USDT:USDT', 'ARKM/USDT:USDT', 'RAD/USDT:USDT', 'IOTA/USDT:USDT', 'ONE/USDT:USDT', 'CORE/USDT:USDT', 'YGG/USDT:USDT', 'MICHI/USDT:USDT', 'BANANA/USDT:USDT', 'NTRN/USDT:USDT', '1000000BABYDOGE/USDT:USDT', 'FXS/USDT:USDT', 'SUSHI/USDT:USDT', 'SUPER/USDT:USDT', 'FLM/USDT:USDT', 'PROS/USDT:USDT', 'PEOPLE/USDT:USDT', 'FARTCOIN/USDT:USDT', 'METIS/USDT:USDT', 'COMBO/USDT:USDT']

def get_usdt_contracts_by_ccxt():
    # 初始化 Bybit 交易所
    exchange = ccxt.bybit()

    # 获取所有市场信息
    markets = exchange.load_markets()

    # 筛选以 USDT 为计价单位的合约
    usdt_contracts = [symbol for symbol in markets if symbol.endswith("/USDT:USDT")]

    return list(usdt_contracts)

def get_initialData(path, lookback):
    if os.path.exists(path + 'open' + '.pkl'):
        cut = 10
        Open = pd.read_pickle(path + 'open' + '.pkl').replace({None: np.nan}).iloc[:-cut]
        High = pd.read_pickle(path + 'high' + '.pkl').replace({None: np.nan}).iloc[:-cut]
        Low = pd.read_pickle(path + 'low' + '.pkl').replace({None: np.nan}).iloc[:-cut]
        Close = pd.read_pickle(path + 'close' + '.pkl').replace({None: np.nan}).iloc[:-cut]
        Volume = pd.read_pickle(path + 'volume' + '.pkl').replace({None: np.nan}).iloc[:-cut]
        # usdt_contract = list(Volume)
        timeToBeUpdate = get_timedelta(get_nowISO(),timeConvert1(Open.index[-1]))
    else:
        usdt_contracts = get_contracts() 
        # usdt_contracts = get_usdt_contracts_by_ccxt()
        Open = pd.DataFrame(columns = usdt_contracts )
        High = pd.DataFrame(columns = usdt_contracts )
        Low = pd.DataFrame(columns = usdt_contracts )
        Close = pd.DataFrame(columns = usdt_contracts )
        Volume = pd.DataFrame(columns = usdt_contracts )
        timeToBeUpdate = lookback
    return  Open, High, Low, Close, Volume, timeToBeUpdate


def fetch_all_ohlcv(exchange, symbol, timeframe, since, limit=1000):
    """
    分页获取 Bybit 的历史 K 线数据
    :param symbol: 交易对，例如 'BTC/USDT'
    :param timeframe: 时间周期，例如 '1m', '5m', '1h', '1d'
    :param since: 起始时间，Unix 时间戳（毫秒）
    :param limit: 每次获取的最大数据量，默认为 1000
    :return: 所有历史 K 线数据（列表形式）
    """
    all_data = []
    while True:
        # 获取 K 线数据
        ohlcv = exchange.fetch_ohlcv(symbol, timeframe, since=since, limit=limit)
        if not ohlcv:
            break  # 如果没有数据，退出循环

        all_data.extend(ohlcv)  # 添加数据到结果列表
        since = ohlcv[-1][0]    # 更新起始时间为最后一条数据的时间戳 + 1 毫秒

        # 如果获取的数据量少于 limit，说明已经获取完所有数据
        if len(ohlcv) < limit:
            break

    return all_data

def fetch_candles2(bybit, symbol,since ,timeframe, limit=1000):
    """
    获取 Bybit 4 小时合约数据，并转换为 Pandas DataFrame
    :param bybit: 初始化后的 Bybit 实例
    :param symbol: 交易对，例如 'BTC/USDT'
    :param limit: 返回的K线数量
    :return: Pandas DataFrame 格式的 K 线数据
    """
    try:
        candles = fetch_all_ohlcv(bybit, symbol, timeframe, since)
        # candles = bybit.fetch_ohlcv(symbol, timeframe, limit=limit)
        # 转换为 Pandas DataFrame
        df = pd.DataFrame(candles, columns=['timestamp', 'open', 'high', 'low', 'close', 'volume'])
        # 将时间戳转换为可读时间格式，并设置为索引
        df['timestamp'] = pd.to_datetime(df['timestamp'], unit='ms')  # 转换为 datetime
        df.set_index('timestamp', inplace=True)  # 设置为索引
        df = df[~df.index.duplicated(keep='first')]
        return df
    except Exception as e:
        print(f"获取 K 线数据时出错: {e}")
        return None
    
    
def download_dt(bybit,since,timeframe,Open, High, Low, Close, Volume,path):
    # utc_now = get_nowISO2()
    
    candles = fetch_candles2(bybit, 'BTC/USDT:USDT',since ,timeframe, 1000)
    Open_up = pd.DataFrame(0, columns = Open.columns , index = candles.index)
    High_up = pd.DataFrame(0, columns = Open.columns , index = candles.index)
    Low_up = pd.DataFrame(0, columns = Open.columns , index = candles.index)
    Close_up = pd.DataFrame(0, columns = Open.columns  , index = candles.index)
    Volume_up = pd.DataFrame(0, columns = Open.columns  , index = candles.index)
    
    a = 0
    noExsit = []
    for symbol in Open.columns:
        a = a + 1
        try:
            candles = fetch_candles2(bybit, symbol,since ,timeframe, 1000)
            # print(a , symbol )
            Open_up[symbol] = candles['open']
            High_up[symbol] = candles['high']
            Low_up[symbol] = candles['low']
            Close_up[symbol] = candles['close']
            Volume_up[symbol] = candles['volume']
        except:
            noExsit.append(symbol)
            print(symbol,'no exist!')
            
    Open_up = Open_up.replace({None: np.nan})
    High_up = High_up.replace({None: np.nan})
    Low_up = Low_up.replace({None: np.nan})
    Close_up = Close_up.replace({None: np.nan})
    Volume_up = Volume_up.replace({None: np.nan})
    
    if len(Open) > 0:
        Open = pd.concat([Open.iloc[:-1], Open_up], axis = 0)
        High = pd.concat([High.iloc[:-1], High_up], axis = 0)
        Low = pd.concat([Low.iloc[:-1], Low_up], axis = 0)
        Close = pd.concat([Close.iloc[:-1], Close_up], axis = 0)
        Volume = pd.concat([Volume.iloc[:-1], Volume_up], axis = 0)
    else:
        Open = Open_up
        High = High_up
        Low = Low_up
        Close = Close_up
        Volume = Volume_up
        
    Open = Open[~Open.index.duplicated()]
    High = High[~High.index.duplicated()]
    Low = Low[~Low.index.duplicated()]
    Close = Close[~Close.index.duplicated()]
    Volume = Volume[~Volume.index.duplicated()]
    
    # Open = Open.drop(columns=noExsit, errors="ignore")
    # High = High.drop(columns=noExsit, errors="ignore")
    # Low = Low.drop(columns=noExsit, errors="ignore")
    # Close = Close.drop(columns=noExsit, errors="ignore")
    # Volume = Volume.drop(columns=noExsit, errors="ignore")
    
    pd.to_pickle(Open, path + 'open.pkl')
    pd.to_pickle(High, path + 'high.pkl')
    pd.to_pickle(Low, path + 'low.pkl')
    pd.to_pickle(Close, path + 'close.pkl')
    pd.to_pickle(Volume, path + 'volume.pkl')
    
    return Open, High, Low, Close, Volume

def download_dt_par(bybit,since,timeframe,Open, High, Low, Close, Volume):
    candles = fetch_candles2(bybit, 'BTC/USDT:USDT',since ,timeframe, 1000)
    Open_up = pd.DataFrame(0, columns = Open.columns , index = candles.index)
    High_up = pd.DataFrame(0, columns = Open.columns , index = candles.index)
    Low_up = pd.DataFrame(0, columns = Open.columns , index = candles.index)
    Close_up = pd.DataFrame(0, columns = Open.columns  , index = candles.index)
    Volume_up = pd.DataFrame(0, columns = Open.columns  , index = candles.index)
    
    a = 0
    for symbol in Open.columns:
        a = a + 1
        try:
            candles = fetch_candles2(bybit, symbol,since ,timeframe, 1000)
            # print(a , symbol )
            Open_up[symbol] = candles['open']
            High_up[symbol] = candles['high']
            Low_up[symbol] = candles['low']
            Close_up[symbol] = candles['close']
            Volume_up[symbol] = candles['volume']
        except:
            print(symbol,'no exist!')
            
    import joblib
    temp_folder = r"/home/<USER>/realTime/Tmp"
    results = joblib.Parallel(n_jobs= 3, temp_folder=temp_folder )(
        joblib.delayed(fetch_candles2)(bybit, symbol,since ,timeframe, 1000) for symbol in Open.columns  )
    

            
    Open_up = Open_up.replace({None: np.nan})
    High_up = High_up.replace({None: np.nan})
    Low_up = Low_up.replace({None: np.nan})
    Close_up = Close_up.replace({None: np.nan})
    Volume_up = Volume_up.replace({None: np.nan})
    
    Open = pd.concat([Open, Open_up], axis = 0)
    High = pd.concat([High, High_up], axis = 0)
    Low = pd.concat([Low, Low_up], axis = 0)
    Close = pd.concat([Close, Close_up], axis = 0)
    Volume = pd.concat([Volume, Volume_up], axis = 0)
    
    Open = Open[~Open.index.duplicated()]
    High = High[~High.index.duplicated()]
    Low = Low[~Low.index.duplicated()]
    Close = Close[~Close.index.duplicated()]
    Volume = Volume[~Volume.index.duplicated()]
    return Open, High, Low, Close, Volume


def clear_data(Open, High, Low, Close, Volume, sd0, end0):
    Open = Open[Open.index >= sd0]
    High = High[High.index >= sd0]
    Low = Low[Low.index>= sd0]
    Close = Close[Close.index>= sd0]
    Volume = Volume[Volume.index>= sd0]

    Open = Open[Open.index<= end0]
    High = High[High.index<= end0]
    Low = Low[Low.index<= end0]
    Close = Close[Close.index<= end0]
    Volume = Volume[Volume.index<= end0]
    
    VWAP = (Open + High+Low+Close) * 4
    Amount = VWAP * Volume
    df = {}
    df['open'],df['high'],df['low'],df['close'],df['volume'], df['amount'], df['vwap']= Open ,High,Low,Close,Volume, Amount, VWAP
    df['totalRet'] = df['close'] / df['close'].shift(1) - 1
    df['totalRet'][df['close'].fillna(0) == 0] = np.nan
    df['totalRet'] = df['totalRet'].replace([np.inf, -np.inf], np.nan) 
    
    # for v in df.keys():
    #     df[v] = df[v][df[v].index >= sd0]
    #     df[v] = df[v][df[v].index <= end0]

    return df


def get_snap(df_all_future, frq,cut):
    df_all_future['open'] = df_all_future['open'].shift(frq-1)
    df_all_future['high'] = df_all_future['high'].rolling(frq).max()
    df_all_future['low'] = df_all_future['low'].rolling(frq).min()
    df_all_future['volume'] = df_all_future['volume'].rolling(frq).sum()
    for v in df_all_future.keys():
        df_all_future[v] = df_all_future[v][df_all_future[v].index.minute % 10 == cut]
    # df_all_future['vwap'] = df_all_future['amount'] / df_all_future['volume']
    # df_all_future['totalRet'] = df_all_future['close'] / df_all_future['close'].shift(1) - 1
    # df_all_future['totalRet'][df_all_future['close'].fillna(0) == 0] = np.nan
    # df_all_future['totalRet'] = df_all_future['totalRet'].replace([np.inf, -np.inf], np.nan)
    
    # for v in df_all_future.keys():
    #     df_all_future[v].index = df_all_future[v].index.strftime('%Y-%m-%d %H:%M:%S')
    
    return df_all_future