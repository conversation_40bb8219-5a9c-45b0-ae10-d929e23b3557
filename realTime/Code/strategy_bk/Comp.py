# import datetime
# import pymysql
import pandas as pd
# import time
import numpy as np
# import math
# import scipy.stats as st

from test_operator_funcs import ts_Delay,Repmat,ts_Decay,ts_Mean,ts_Stdev




#  一次合成
def Com_part(fs_v,fs_w):
    n = 0 
    for v in range(len(fs_v)):
        if n == 0 :
            val__ = fs_v[v]
            val__[val__.isna()] = 0 
            out = val__ * fs_w[v]
            n = 1 
        else:
            val__ = fs_v[v]
            val__[val__.isna()] = 0 
            out = val__ * fs_w[v]   + out
    out[out == 0 ] = np.nan
    divided = np.sum(fs_w)
    out = out / divided
    return out

def getValueCors(sf_value3,config):
    valNames = config['FactorNAmes']
    tmp = sf_value3[valNames[0]]
    corrss = list()
    for v in range(len(tmp.index)):
        # print(v)
        tmp_ = pd.DataFrame(0,index = tmp.columns,columns = valNames )
        for vv in valNames:
            tmp2 = sf_value3[vv]
            tmp3 = tmp2.iloc[v]
            tmp_[vv] = tmp3
        tmp_ = tmp_.fillna(0)
        tmp_ = tmp_[tmp_.abs().sum(1) > 0 ]
        corrs = tmp_.corr().fillna(0)
        corrss.append(corrs)
    return corrss


def getValueCovs(sf_value3,config):
    valNames = config['FactorNAmes']
    tmp = sf_value3[valNames[0]]
    corrss = list()
    for v in range(len(tmp.index)):
        if v < config['SDate'] - config['rolling_window'] -1 :
            corrs = pd.DataFrame(0,index = valNames,columns = valNames)
            # continue
        # print(v)
        tmp_ = pd.DataFrame(0,index = tmp.columns,columns = valNames )
        for vv in valNames:
            tmp2 = sf_value3[vv]
            tmp3 = tmp2.iloc[v]
            tmp_[vv] = tmp3
        tmp_ = tmp_.fillna(0)
        tmp_ = tmp_[tmp_.abs().sum(1) > 0 ]
        corrs = tmp_.cov().fillna(0)
        corrss.append(corrs)
    return corrss


def getValueCov2(sf_value3,config):
    valNames ,rolling_window= config['FactorNAmes'],config['rolling_window']
    tmp = sf_value3[valNames[0]]

    allF = {}
    for vv in valNames:
        print(vv)
        b = np.zeros((tmp.shape[0],tmp.shape[1] *rolling_window))
        for l in range(config['rolling_window']):
            b[:,l*tmp.shape[1]: (l+1)*tmp.shape[1]] = np.array(ts_Delay(sf_value3[vv],l))
        allF[vv] = pd.DataFrame(b,index = tmp.index) 
        
    corrss = list()
    for v in range(len(tmp.index)):
        if v < config['SDate']:
            corrs = pd.DataFrame(0,index = valNames,columns = valNames)
            continue
        print(v)
        tmp_ = pd.DataFrame()
        for vv in valNames:
            tmp2 = allF[vv]
            tmp3 = tmp2.iloc[v]
            tmp_[vv] = tmp3
        tmp_ = tmp_[tmp_.abs().sum(1) > 0 ]
        corrs = tmp_.cov().fillna(0)
        corrss.append(corrs)
    return corrss

        
                    
def getlistMean(corrss):
    tmp5 = pd.DataFrame(0,columns = corrss[0].columns,index = corrss[0].columns )
    count = pd.DataFrame(0,columns = corrss[0].columns,index = corrss[0].columns )
    for v in corrss:
        tmp5 = tmp5 + v
        count = count + (v!=0) 
    out =tmp5/  count  
    return out
       
 
def getlistMeanRoll(corrss,config):
    out = list()
    for v in range(len(corrss)):
        if v <= config['rolling_window']:
            out.append(pd.DataFrame(np.nan,columns = corrss[0].columns,index = corrss[0].columns ))
        else:
            tmp5 = pd.DataFrame(0,columns = corrss[0].columns,index = corrss[0].columns )
            n = 0
            for c in range(config['rolling_window']):
                # print(c)
                tmp = corrss[v - c]
                tmp5 = tmp5 + tmp
                n = n + 1
            tmp5 = tmp5 / n
            out.append(tmp5 )  
    return out
    

def Get_cov_part(vals,rets,config):
    method = config['cov_method'] 
    if method == 'ret_cov':
        cov = rets.cov().to_numpy()
    elif method == 'val_cov':
        covs = getValueCovs(vals,config)
        cov = getlistMean(covs)
    return cov



# rolling 滚动合成因子
def Com_roll(sf_value,sr_rolling,config):
    # Initializing a result like the factor value : 
    tmp = sf_value[list(sf_value.keys())[0]]
    out = pd.DataFrame(0,index = tmp.index,columns = tmp.columns)
    # weighting the factors, one by one
    for v in config['FactorNAmes']:
        val =  sf_value[v]
        val[val.isna()] = 0 
        out = out + val * Repmat(out,sr_rolling[v])    
    return out    



def Get_cov_roll2(vals,rets,config):
    method,rolling_window = config['cov_method'] , config['rolling_window']
    if method == 'ret_cov':
        z = rets.ewm(rolling_window).cov()
        lens = len(rets.columns)
        cov = list()
        for v in rets.index:
            zz = z.iloc[0:lens]
            z =  z.iloc[lens:]
            cov.append(zz)
    elif method == 'val_cov':
        covs = getValueCovs(vals,config)
        cov = getlistMeanRoll(covs,config)
    elif method == 'val_cov2':
        cov = getValueCov2(vals,config)
    return cov

# def Get_cor_roll(vals,rets,config):
#     method,rolling_window = config['cor_method'] , config['rolling_window']
#     if method == 'ret_cor':
#         z = rets.rolling(window=rolling_window).corr()
#         lens = len(rets.columns)
#         cov = list()
#         for v in rets.index:
#             zz = z.iloc[0:lens]
#             z =  z.iloc[lens:]
#             cov.append(zz)
#     elif method == 'val_cor':
#         covs = getValueCors(vals,config)
#         cov = getlistMeanRoll(covs,config)
#     elif method == 'val_cor2':
#         cov = getValueCov2(vals,config)
#     return cov
        
    
def Markowitz(A_return,A_cov,max_wgt):
    import scipy.optimize as sco
    def sharp(w):
        Rf = 0.00
        Rp_opt = np.sum(np.dot(w, A_return))
        Vp_opt = np.sqrt(np.dot(np.dot(w, A_cov), w.T))
        SR = (Rp_opt-Rf)/Vp_opt
        return (-SR)
    cons =  ({'type': 'eq', 'fun': lambda x: np.sum(x) - 1},\
             {'type': 'ineq', 'fun': lambda x: max_wgt - x} )   # constrain: let the max of x be max_wgt
    bnds = tuple((0, 1) for x in range(len(A_return)))       
    result_sr = sco.minimize(sharp, len(A_return) * [1.0 / len(A_return), ], 
                method='SLSQP', bounds=bnds,   constraints=cons)
    w_mar = result_sr.x
    return w_mar




def Markowitz2(A_return, A_cov, fixed_weight):
    import scipy.optimize as sco
    def sharp(w):
        Rf = 0.00
        Rp_opt = np.sum(np.dot(w, A_return))
        Vp_opt = np.sqrt(np.dot(np.dot(w, A_cov), w.T))
        SR = (Rp_opt - Rf) / Vp_opt
        return -SR
    cons = ({'type': 'eq', 'fun': lambda x: np.sum(x) - 1},
            {'type': 'eq', 'fun': lambda x: x})  # 约束：确保权重之和为1且每个权重要么是0，要么是fixed_weight
    bnds = tuple((0, fixed_weight) for x in range(len(A_return)))
    result_sr = sco.minimize(sharp, len(A_return) * [1.0 / len(A_return), ],
                             method='SLSQP', bounds=bnds, constraints=cons)
    w_mar = result_sr.x
    return w_mar
    
    


def F_CompositeF_dict(fs_v,fs_w):
    out = fs_v[list(fs_v.keys())[0]].fillna(0)* 0   # initital
    for v in range(len(list(fs_v.keys()))):
        out = out + fs_v[list(fs_v.keys())[v]].fillna(0) * fs_w[v]  # value * wgt
    return out



def Get_cov_roll(vals,rets,config):
    method,rolling_window = config['cov_method'] , config['rolling_window']
    if method == 'ret_cov':
        z = rets.rolling(window=rolling_window).cov()
        lens = len(rets.columns)
        cov = list()
        for v in rets.index:
            zz = z.iloc[0:lens]
            z =  z.iloc[lens:]
            cov.append(zz)
    elif method == 'val_cov':
        covs = getValueCovs(vals,config)
        cov = getlistMeanRoll(covs,config)
    elif method == 'val_cov2':
        cov = getValueCov2(vals,config)
    return cov

def get_markWgt(vals,rets,config):
    rolling_window,SDate,EDate = config['rolling_window'],config['SDate'],config['EDate']
    valNames = config['FactorNAmes']
    out = pd.DataFrame(np.nan, index = vals[valNames[0]].index,columns = rets.columns)                                                                           
    cov_r = Get_cov_roll(vals,rets,config)
    
    if config['exRet'] == 1:
        rets_rolling = ts_Decay(rets,rolling_window)	
    elif config['exRet'] == 2 :	
        rets_rolling = ts_Decay(rets,rolling_window)	
        rets_rolling[rets_rolling < 0 ] = 0
    elif config['exRet'] == 3 :
        rets_rolling = ts_Mean(rets,rolling_window)	
    gap = 0
    for v in range(len(rets.index)):
        # if v < rolling_window:
        #     continue
        if v < SDate:
            continue
        if v > EDate:
            continue
        # print(v)
        A_return = rets_rolling.iloc[v].fillna(0)
        A_cov = cov_r[v].fillna(0)
        w_mar = Markowitz(A_return,A_cov,config['maxWgt'])
        # print(np.max(w_mar),np.min(w_mar),np.sum(w_mar))
        out.iloc[v] = w_mar
        gap = gap + 1
    out = out.fillna(method = 'ffill')
    return out


def get_markWgt2(vals,rets,config):
    rolling_window,SDate,EDate = config['rolling_window'],config['SDate']-30,config['EDate']
    valNames = config['FactorNAmes']
    out = pd.DataFrame(np.nan, index = rets.index,columns = rets.columns) .T                                                                         
    cov_r = Get_cov_roll(vals,rets,config)
    
    if config['exRet'] == 1:
        rets_rolling = ts_Decay_old(rets,rolling_window)	
    elif config['exRet'] == 3 :
        rets_rolling = ts_Mean(rets,rolling_window)	
    gap = 0
    for v in range(len(rets.index)):
        # if v < rolling_window:
        #     continue
        if v < SDate:
            continue
        if v > EDate:
            continue
        # print(v)
        A_return = rets_rolling.iloc[v].fillna(0)
        A_return = A_return[A_return > 0 ]
        A_cov = cov_r[v].fillna(0)
        A_cov = A_cov.droplevel(level=0) 
        A_cov = (A_cov[A_return.index]).T
        A_cov = (A_cov[A_return.index])
        w_mar = Markowitz(A_return,A_cov,config['maxWgt'])
        
        dat = rets.index.values[v]
        weights_pd = pd.DataFrame(w_mar,index = A_return.index ,columns = [0])
        out[dat] = weights_pd[0]
        out[dat] = out[dat].fillna(0)
        
        # print(np.max(w_mar),np.min(w_mar),np.sum(w_mar))
        # out.iloc[v] = w_mar
        gap = gap + 1
    out = out.T
    out = out.fillna(method = 'ffill')
    out = out/Repmat(out,out.sum(1))
    return out


def get_cf(vals,returns,config):
    w_rolling = get_markWgt2(vals, returns, config)
    cf = config['TotalRet'].copy().fillna(0) * 0
    for v in w_rolling.columns:
        if type(vals[v]) == int:
            continue
        cf = cf + Repmat(cf, w_rolling[v]).fillna(0) * vals[v].fillna(0)
    cf[config['universe'] == 0] = np.nan
    cf = cf + np.random.rand(*cf.shape) / 100000000000000.0
    cf.loc[:config['SDate'], :] = np.nan
    return cf




def Get_cov_roll_short(rets,config):
    rolling_window = 30
    z = rets.rolling(window=rolling_window).cov()
    lens = len(rets.columns)
    cov = list()
    for v in rets.index:
        zz = z.iloc[0:lens]
        z =  z.iloc[lens:]
        cov.append(zz)
    return cov

def ts_Decay_old(dfCleaned, num):
    sums = 0
    for v in range(num):
        # print(v)
        # print( (num - v ) / 10)
        if v == 0:
            dfCleaned2 = dfCleaned.copy()
        else:
            dfCleaned2 = dfCleaned2 + ts_Delay(dfCleaned,v) * (num - v ) / num
        sums = sums + (num - v ) / num
    dfCleaned2 = dfCleaned2 / sums
    return dfCleaned2


def get_markWgt3(vals,rets,config):
    rolling_window,SDate,EDate = config['rolling_window'],config['SDate']-30,config['EDate']
    valNames = config['FactorNAmes']
    out = pd.DataFrame(np.nan, index = rets.index,columns = rets.columns) .T                                                                         
    cov_r = Get_cov_roll(vals,rets,config)
    cov_r_s = Get_cov_roll_short(rets,config)
    
    if config['exRet'] == 1:
        rets_rolling = ts_Decay_old(rets,rolling_window)	
    elif config['exRet'] == 3 :
        rets_rolling = ts_Mean(rets,rolling_window)	
    gap = 0
    for v in range(len(rets.index)):
        # if v < rolling_window:
        #     continue
        if v < SDate:
            continue
        if v > EDate:
            continue
        # print(v)
        A_return = rets_rolling.iloc[v].fillna(0)
        A_return = A_return[A_return > 0 ]
        A_cov = cov_r[v].fillna(0)
        A_cov = A_cov.droplevel(level=0) 
        A_cov = (A_cov[A_return.index]).T
        A_cov = (A_cov[A_return.index])
        
        A_cov2 = cov_r_s[v].fillna(0)
        A_cov2 = A_cov2.droplevel(level=0) 
        A_cov2 = (A_cov2[A_return.index]).T
        A_cov2 = (A_cov2[A_return.index])
        
        A_cov = A_cov2 + A_cov 
        
        w_mar = Markowitz(A_return,A_cov,config['maxWgt'])
        
        dat = rets.index.values[v]
        weights_pd = pd.DataFrame(w_mar,index = A_return.index ,columns = [0])
        out[dat] = weights_pd[0]
        out[dat] = out[dat].fillna(0)
        
        # print(np.max(w_mar),np.min(w_mar),np.sum(w_mar))
        # out.iloc[v] = w_mar
        gap = gap + 1
    out = out.T
    out = out.fillna(method = 'ffill')
    out = out/Repmat(out,out.sum(1))
    return out

def get_cf2(vals,returns,config):
    w_rolling = get_markWgt3(vals, returns, config)
    cf = config['TotalRet'].copy().fillna(0) * 0
    for v in w_rolling.columns:
        cf = cf + Repmat(cf, w_rolling[v]).fillna(0) * vals[v].fillna(0)
    cf[config['universe'] == 0] = np.nan
    cf = cf + np.random.rand(*cf.shape) / 100000000000000.0
    cf.loc[:config['SDate'], :] = np.nan
    return cf


def Markowitz_ls(A_return,A_cov,max_wgt):
    import scipy.optimize as sco
    def sharp(w):
        Rf = 0.00
        Rp_opt = np.sum(np.dot(w, A_return))
        Vp_opt = np.sqrt(np.dot(np.dot(w, A_cov), w.T))
        SR = (Rp_opt-Rf)/Vp_opt
        return (-SR)
    cons =  ({'type': 'eq', 'fun': lambda x: np.sum(x) - 1},\
             {'type': 'ineq', 'fun': lambda x: max_wgt - x} )   # constrain: let the max of x be max_wgt
    bnds = tuple((-1, 1) for x in range(len(A_return)))       
    result_sr = sco.minimize(sharp, len(A_return) * [1.0 / len(A_return), ], 
                method='SLSQP', bounds=bnds,   constraints=cons)
    w_mar = result_sr.x
    return w_mar


def get_markWgt3(vals,rets,config):   # ls
    rolling_window,SDate,EDate = config['rolling_window'],config['SDate'],config['EDate']
    valNames = config['FactorNAmes']
    out = pd.DataFrame(np.nan, index = vals[valNames[0]].index,columns = rets.columns)                                                                           
    cov_r = Get_cov_roll(vals,rets,config)
    rets_rolling = ts_Mean(rets,rolling_window)	
    gap = 0
    for v in range(len(rets.index)):
        if v < rolling_window:
            continue
        if v < SDate:
            continue
        if v > EDate:
            continue
        # print(v)
        A_return = rets_rolling.iloc[v]
        A_cov = cov_r[v]
        w_mar = Markowitz(A_return,A_cov,config['maxWgt'])
        out.iloc[v] = w_mar
        gap = gap + 1
    out = out.fillna(method = 'ffill')
    return out



def get_markWgt4_ts_Decay_gap(rets,config):   
    rolling_window,SDate,EDate = config['rolling_window'],config['SDate'],config['EDate']
    out = pd.DataFrame(np.nan, index = rets.index,columns = rets.columns)    
    gap = 10
    for v in range(len(rets.index)):
        if v < rolling_window:
            continue
        if v < SDate:
            continue
        if v > EDate:
            continue
        # print(v)
        if gap == 10:
            dat_2 = rets.index.values[v]
            dat_1 = rets.index.values[v - rolling_window]
            tmp_returuns = rets[rets.index <= dat_2]
            tmp_returuns = tmp_returuns[tmp_returuns.index >= dat_1 ]
            # A_return = tmp_returuns.mean(0)
            A_cov = tmp_returuns.fillna(0).cov()
            A_return = ts_Decay(tmp_returuns,rolling_window).iloc[-1]
            w_mar = Markowitz(A_return,A_cov,config['maxWgt'])
            print(v)
            out.iloc[v] = w_mar
            gap = 0
        gap = gap + 1
    out = out.fillna(method = 'ffill')
    return out


def get_markWgt4_ts_Decay(rets,config):   
    rolling_window,SDate,EDate = config['rolling_window'],config['SDate'],config['EDate']
    out = pd.DataFrame(np.nan, index = rets.index,columns = rets.columns)    
    for v in range(len(rets.index)):
        if v < rolling_window:
            continue
        if v < SDate:
            continue
        if v > EDate:
            continue
        print(v)
        dat_2 = rets.index.values[v]
        dat_1 = rets.index.values[v - rolling_window]
        tmp_returuns = rets[rets.index <= dat_2]
        tmp_returuns = tmp_returuns[tmp_returuns.index >= dat_1 ]
        # A_return = tmp_returuns.mean(0)
        A_cov = tmp_returuns.fillna(0).cov()
        A_return = ts_Decay(tmp_returuns,rolling_window).iloc[-1]
        w_mar = Markowitz(A_return,A_cov,config['maxWgt'])
        out.iloc[v] = w_mar
    out = out.fillna(method = 'ffill')
    return out

def get_markWgt4(rets,config):   
    rolling_window,SDate,EDate = config['rolling_window'],config['SDate'],config['EDate']
    out = pd.DataFrame(np.nan, index = rets.index,columns = rets.columns)    
    gap = 0
    for v in range(len(rets.index)):
        if v < rolling_window:
            continue
        if v < SDate:
            continue
        if v > EDate:
            continue
        # print(v)
        dat_2 = rets.index.values[v]
        dat_1 = rets.index.values[v - rolling_window]
        tmp_returuns = rets[rets.index <= dat_2]
        tmp_returuns = tmp_returuns[tmp_returuns.index >= dat_1 ]
        A_return = tmp_returuns.mean(0)
        A_cov = tmp_returuns.fillna(0).cov()
        
        w_mar = Markowitz(A_return,A_cov,config['maxWgt'])
        out.iloc[v] = w_mar
        gap = gap + 1
    out = out.fillna(method = 'ffill')
    return out


def get_markWgt5(returns,returns_up,config):   
    rolling_window,SDate,EDate = config['rolling_window'],config['SDate'],config['EDate']
    out = pd.DataFrame(np.nan, index = returns.index,columns = returns.columns)    
    gap = 0
    returns_up_tsDecay = ts_Decay(returns_up,60)
    for v in range(len(returns.index)):
        if v < rolling_window:
            continue
        if v < SDate:
            continue
        if v > EDate:
            continue
        # print(v)
        dat_2 = returns.index.values[v]
        dat_1 = returns.index.values[v - rolling_window]
        tmp_returuns = returns[returns.index <= dat_2]
        tmp_returuns = tmp_returuns[tmp_returuns.index >= dat_1 ]
        A_return = tmp_returuns.mean(0)
        A_return_up = returns_up_tsDecay.iloc[v].fillna(0)
        A_return = A_return * (A_return_up>0)
        A_cov = tmp_returuns.fillna(0).cov()
        w_mar = Markowitz(A_return,A_cov,config['maxWgt'])
        out.iloc[v] = w_mar
        gap = gap + 1
    out = out.fillna(method = 'ffill')
    return out



def get_markWgt4_ewm(rets,config):   
    rolling_window,SDate,EDate = config['rolling_window'],config['SDate'],config['EDate']
    out = pd.DataFrame(np.nan, index = rets.index,columns = rets.columns)    
    gap = 0
    for v in range(len(rets.index)):
        if v < rolling_window:
            continue
        if v < SDate:
            continue
        if v > EDate:
            continue
        # print(v)
        dat_2 = rets.index.values[v]
        dat_1 = rets.index.values[v - rolling_window]
        tmp_returuns = rets[rets.index <= dat_2]
        tmp_returuns = tmp_returuns[tmp_returuns.index >= dat_1 ]
        A_return = ts_Decay(tmp_returuns,rolling_window).iloc[-1]
        # A_cov =  tmp_returuns.ewm(rolling_window).cov()
        # A_cov = A_cov[A_cov.index == dat_2]
        A_cov1 = tmp_returuns.fillna(0).cov()
        A_cov2 = tmp_returuns.tail(100).fillna(0).cov()
        A_cov = A_cov1 + A_cov2
        
        w_mar = Markowitz(A_return,A_cov,config['maxWgt'])
        out.iloc[v] = w_mar
        gap = gap + 1
    out = out.fillna(method = 'ffill')
    return out




def get_markWgt4_ls(vals,rets,config):   # convert nega to posi
    rolling_window,SDate,EDate = config['rolling_window'],config['SDate'],config['EDate']
    valNames = config['FactorNAmes']
    # out = pd.DataFrame(np.nan, index = vals[valNames[0]].index,columns = rets.columns)    
    outCF = pd.DataFrame(np.nan, index = vals[valNames[0]].index,columns = vals[valNames[0]].columns) .T                                                                      
    # cov_r = Get_cov_roll(vals,rets,config)
    # rets_rolling = ts_Mean(rets,rolling_window)	
    gap = 0
    for v in range(len(rets.index)):
        if v < rolling_window:
            continue
        if v < SDate:
            continue
        if v > EDate:
            continue
        # print(v)
        dat_2 = rets.index.values[v]
        dat_1 = rets.index.values[v - rolling_window]
        tmp_returuns = rets[rets.index <= dat_2]
        tmp_returuns = tmp_returuns[tmp_returuns.index >= dat_1 ]
        
        A_return = tmp_returuns.mean(0)
        A_return_ = ( A_return > 0 ) * 1 * 2 - 1
        A_return_2 = pd.DataFrame(A_return_.values, index = A_return_.index)
        A_return_3 = Repmat(tmp_returuns.T,A_return_2).T
        A_return_4 = A_return_2.T
        tmp_returuns = tmp_returuns * A_return_3
        
        A_return = tmp_returuns.mean(0)
        A_cov = tmp_returuns.fillna(0).cov()
        
        w_mar = Markowitz(A_return,A_cov,config['maxWgt'])
        tmp_today = outCF[outCF.columns.values[0]].fillna(0)
        for vv in range(len(valNames)):
            dirs = A_return_4[valNames[vv]].values[0]
            tmp = vals[valNames[vv]].iloc[v].fillna(0) * dirs
            tmp_today = tmp_today + tmp * w_mar[vv]
            outCF[outCF.columns.values[v]] = tmp_today.values
        gap = gap + 1
    return outCF.T



def dict2Pd(sf_value,key):
    fs = pd.DataFrame()
    for v in key:
        fs[v]= sf_value[v]
    return fs


def Get_cov_roll_(vals,rets,config):
    method,rolling_window = config['cov_method'] , config['rolling_window_cov']
    if method == 'ret_cov':
        z = rets.rolling(window=rolling_window).cov()
        lens = len(rets.columns)
        cov = list()
        for v in rets.index:
            zz = z.iloc[0:lens]
            z =  z.iloc[lens:]
            cov.append(zz)
    elif method == 'val_cov':
        covs = getValueCovs(vals,config)
        cov = getlistMeanRoll(covs,config)
    elif method == 'val_cov2':
        cov = getValueCov2(vals,config)
    return cov

def ts_Decay3(dataTD, nPrds, decayMethod='linear'):
    '''
    calculate nPrds moving average of Data, skipping non trade days;
    decay can be linear or exp;
    data is pd DataFrame or Series;
    trdDay is boolean pd Series indicating trade days, sharing same index as data;
    any NaNs are preserved;
    '''
    if decayMethod == 'linear':
        w = np.array([1-1/nPrds*(i-1) for i in range(nPrds,0,-1)])
        w = w / sum(w)
    elif decayMethod == 'exp':
        alpha =1 - 2/(nPrds+1)
        w = np.array([alpha**i for i in range(nPrds,0,-1)])
        w = w / sum(w)
    transformedTD = dataTD*w[-1]
    for i in range(nPrds-1):
        transformedTD = transformedTD+np.roll(dataTD,shift=nPrds-i-1,axis=0)*w[i]
    transformedTD[:nPrds] = np.nan
    return transformedTD


def ts_rolling_max_drawdown(data, window):
    # 计算滚动最大值
    rolling_max = data.rolling(window=window, min_periods=1).max()
    # 计算每个窗口的回撤
    rolling_drawdown = (rolling_max - data )
    # 计算滚动最大回撤
    rolling_max_drawdown = rolling_drawdown.rolling(window=window, min_periods=1).max()
    return rolling_max_drawdown


def get_markWgt2_(vals,rets,config):
    rolling_window,SDate,EDate = config['rolling_window'],config['SDate'],config['EDate']
    out = pd.DataFrame(np.nan, index = rets.index,columns = rets.columns)                                                                          
    cov_r = Get_cov_roll_(vals,rets,config)

    if config['exRet'] == 1:
        rets_rolling = ts_Decay3(rets,rolling_window,'linear')	
    elif config['exRet'] == 2:
        rets_rolling = ts_Decay3(rets,rolling_window,'exp')	
    elif config['exRet'] == 3 :
        rets_rolling = ts_Mean(rets,rolling_window)	
    elif config['exRet'] == 4 :
        rets_rolling1 = ts_Mean(rets,rolling_window)* rolling_window
        md = ts_rolling_max_drawdown(rets.fillna(0).cumsum(), rolling_window)
        rets_rolling = rets_rolling1 / md
    elif config['exRet'] == 5 :
        rets_rolling = ts_Mean(rets,rolling_window)	 / ts_Stdev(rets,rolling_window)	
    elif config['exRet'] == 6 :
        rets_rolling = ts_Mean(rets,rolling_window)		
        rets_rolling = (rets_rolling > 0)*1
    for v in range(len(rets.index)):
        # if v < rolling_window:
        #     continue
        if v < SDate:
            continue
        if v > EDate:
            continue
        # print(rets.index.values[v])
        A_return = rets_rolling.iloc[v].fillna(0)
        A_return = A_return[A_return > 0 ]     # test for now
        if len(A_return) < 3:
            continue
        A_cov = cov_r[v].fillna(0)
        A_cov = A_cov.droplevel(level=0) 
        A_cov = (A_cov[A_return.index]).T
        A_cov = (A_cov[A_return.index])
        w_mar = Markowitz(A_return,A_cov,config['maxWgt'])
        
        weights_pd = pd.Series(w_mar,index = A_return.index )
        out.iloc[v , out.columns.get_indexer(weights_pd.index)] = weights_pd
        # print(rets.index.values[v],out.shape,round(np.max(w_mar),4),round(np.min(w_mar),4),round(np.sum(w_mar),4), len(w_mar))

    # out = out.fillna(method = 'ffill')
    out = out.fillna(0)
    out = out/Repmat(out,out.sum(1))
    return out


def get_markWgt2_rt(vals,rets,config):
    rolling_window,SDate,EDate = config['rolling_window'],config['SDate'],config['EDate']
    out = pd.DataFrame(np.nan, index = rets.index,columns = rets.columns)                                                                          
    cov_r = Get_cov_roll_(vals,rets,config)

    if config['exRet'] == 1:
        rets_rolling = ts_Decay3(rets,rolling_window,'linear')	
    elif config['exRet'] == 2:
        rets_rolling = ts_Decay3(rets,rolling_window,'exp')	
    elif config['exRet'] == 3 :
        rets_rolling = ts_Mean(rets,rolling_window)	
    elif config['exRet'] == 4 :
        rets_rolling1 = ts_Mean(rets,rolling_window)* rolling_window
        md = ts_rolling_max_drawdown(rets.fillna(0).cumsum(), rolling_window)
        rets_rolling = rets_rolling1 / md
    elif config['exRet'] == 5 :
        rets_rolling = ts_Mean(rets,rolling_window)	 / ts_Stdev(rets,rolling_window)	
    elif config['exRet'] == 6 :
        rets_rolling = ts_Mean(rets,rolling_window)		
        rets_rolling = (rets_rolling > 0)*1
    for v in range(len(rets.index)):
        # if v < rolling_window:
        #     continue
        if v < SDate:
            continue
        if v > EDate:
            continue
        # print(rets.index.values[v])
        A_return = rets_rolling.iloc[v].fillna(0)
        A_return = A_return[A_return > 0 ]     # test for now
        if len(A_return) < 3:
            continue
        A_cov = cov_r[v].fillna(0)
        A_cov = A_cov.droplevel(level=0) 
        
        ret_tmp = rets.iloc[v-rolling_window:v +1]
        A_cov = ret_tmp.cov()
        
        A_cov = (A_cov[A_return.index]).T
        A_cov = (A_cov[A_return.index])
        w_mar = Markowitz(A_return,A_cov,config['maxWgt'])
        
        weights_pd = pd.Series(w_mar,index = A_return.index )
        out.iloc[v , out.columns.get_indexer(weights_pd.index)] = weights_pd
        # print(rets.index.values[v],out.shape,round(np.max(w_mar),4),round(np.min(w_mar),4),round(np.sum(w_mar),4), len(w_mar))

    # out = out.fillna(method = 'ffill')
    out = out.fillna(0)
    out = out/Repmat(out,out.sum(1))
    return out



def pn_TransNorm(s1):  # normalization
    # print(s1)
    from scipy.stats import norm
    dfCleaned = s1.copy()
    rank_ = dfCleaned.rank(pct=True, axis=1)
    cut = rank_.min(axis=1) / 2
    rank_ = rank_.sub(cut, axis=0)
    out = pd.DataFrame(norm.ppf(np.array(rank_)), index=rank_.index, columns=rank_.columns)
    return out

def get_cf6(vals,returns,config):
    # get factor weight
    w_rolling = get_markWgt2_(vals, returns, config)  

    # get composite factor:   #  把多因子，按照因子收益得到的因子权重，整理成1个因子
    cf = vals[list(vals.keys())[0]].copy().fillna(0) * 0
    for v in w_rolling.columns:
        if type(vals[v]) == int:
            continue
        tmp = vals[v].fillna(0)
        tmp[config['universe'] == 0 ] = np.nan
        tmp = pn_TransNorm(tmp)
        cf = cf + Repmat(cf, w_rolling[v]).fillna(0) * tmp.fillna(0)
    cf[config['universe'] == 0] = np.nan
    # cf = cf + np.random.rand(*cf.shape) / 100000000000000.0
    # print(w_rolling)
    return cf , w_rolling



def get_cf6_rt(vals,returns,config):
    w_rolling = get_markWgt2_rt(vals, returns, config)
    cf = config['TotalRet'].copy().fillna(0) * 0
    for v in w_rolling.columns:
        if type(vals[v]) == int:
            continue
        tmp = vals[v].fillna(0)
        tmp[config['universe'] == 0 ] = np.nan
        tmp = pn_TransNorm(tmp)
        cf = cf + Repmat(cf, w_rolling[v]).fillna(0) * tmp.fillna(0)
    cf[config['universe'] == 0] = np.nan
    # cf = cf + np.random.rand(*cf.shape) / 100000000000000.0
    # print(w_rolling)
    return cf , w_rolling

def Markowitz7(A_return,A_cov,max_wgt):
    import scipy.optimize as sco
    def sharp(w):
        Rf = 0.00
        Rp_opt = np.sum(np.dot(w, A_return))
        Vp_opt = np.sqrt(np.dot(np.dot(w, A_cov), w.T))
        SR = (Rp_opt-Rf)/Vp_opt
        return (-SR)
    cons =  ({'type': 'eq', 'fun': lambda x: np.sum(x) - 1},\
            #  {'type': 'ineq', 'fun': lambda x: max_wgt - x} 
             )   # constrain: let the max of x be max_wgt
    bnds = tuple((-max_wgt, max_wgt) for x in range(len(A_return)))
    result_sr = sco.minimize(sharp, len(A_return) * [1.0 / len(A_return), ], 
                method='SLSQP', bounds=bnds,   constraints=cons)
    w_mar = result_sr.x
    return w_mar


def get_markWgt7(vals,rets,config):
    rolling_window,SDate,EDate = config['rolling_window'],config['SDate']-30,config['EDate']
    out = pd.DataFrame(np.nan, index = rets.index,columns = rets.columns)   
    # cov_r = Get_cov_roll_(vals,rets,config)
    if config['exRet'] == 1:
        rets_rolling = ts_Decay3(rets,rolling_window,'linear')	
    elif config['exRet'] == 2:
        rets_rolling = ts_Decay3(rets,rolling_window,'exp')	
    elif config['exRet'] == 3 :
        rets_rolling = ts_Mean(rets,rolling_window)	
    for v in range(len(rets.index)):
        if v < SDate:
            continue
        if v > EDate:
            continue
        # print(rets.index.values[v])
        A_return = rets_rolling.iloc[v].fillna(0)
        # A_return = A_return[A_return > 0 ]     # test for now
        if len(A_return) < 3:
            continue
        rets_ = rets.iloc[v-config['rolling_window_cov']:v + 1]
        A_cov = rets_.fillna(0).cov()
        w_mar = Markowitz7(A_return,A_cov,config['maxWgt'])
        
        weights_pd = pd.Series(w_mar,index = A_return.index )
        out.iloc[v , out.columns.get_indexer(weights_pd.index)] = weights_pd
        # print(rets.index.values[v],out.shape,round(np.max(w_mar),4),round(np.min(w_mar),4),round(np.sum(w_mar),4), len(w_mar))

    # out = out.fillna(method = 'ffill')
    out = out.fillna(0)
    out = out/Repmat(out,out.sum(1))
    return out


def get_cf7(vals,returns,config):
    w_rolling = get_markWgt7(vals, returns, config)
    cf = config['TotalRet'].copy().fillna(0) * 0
    for v in w_rolling.columns:
        if type(vals[v]) == int:
            continue
        cf = cf + Repmat(cf, w_rolling[v]).fillna(0) * vals[v].fillna(0)
    cf[config['universe'] == 0] = np.nan
    # cf = cf + np.random.rand(*cf.shape) / 100000000000000.0
    # print(w_rolling)
    return cf , w_rolling




def get_markWg8(vals,rets,config):
    rolling_window = config['rolling_window']
    out = pd.DataFrame(np.nan, index = rets.index,columns = rets.columns)   
    if config['exRet'] == 1:
        rets_rolling = ts_Decay3(rets,rolling_window,'linear')	
    elif config['exRet'] == 2:
        rets_rolling = ts_Decay3(rets,rolling_window,'exp')	
    elif config['exRet'] == 3 :
        rets_rolling = ts_Mean(rets,rolling_window)	
    A_return = rets_rolling.tail(1).fillna(0)
    rets_ = rets.tail(config['rolling_window_cov'])
    A_cov = rets_.fillna(0).cov()
    w_mar = Markowitz7(A_return,A_cov,config['maxWgt'])
    # weights_pd = pd.DataFrame(w_mar,index = A_return.index )
    out.iloc[-1] = w_mar
    return out


def get_cf8(vals,returns,config):
    w_rolling = get_markWgt7(vals, returns, config)
    cf = config['TotalRet'].copy().fillna(0) * 0
    for v in w_rolling.columns:
        if type(vals[v]) == int:
            continue
        cf = cf + Repmat(cf, w_rolling[v]).fillna(0) * vals[v].fillna(0)
    cf[config['universe'] == 0] = np.nan
    # cf = cf + np.random.rand(*cf.shape) / 100000000000000.0
    # print(w_rolling)
    return cf , w_rolling
