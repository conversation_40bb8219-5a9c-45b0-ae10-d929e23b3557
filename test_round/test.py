# def test_python_round():
#     test_values = [
#         # .5 cases that highlight the difference
#         0.5,
#         1.5,
#         2.5,
#         3.5,
#         4.5,
#         -0.5,
#         -1.5,
#         -2.5,
#         -3.5,
#         -4.5,
#         # Non-.5 cases (should behave similarly)
#         2.3,
#         2.7,
#         -2.3,
#         -2.7,
#         # Integers and zero
#         0.0,
#         2.0,
#         -2.0,
#         # Values very close to .5 to show rounding threshold
#         2.499999999999999,   # Should round to 2
#         2.500000000000001,   # Should round to 3
#         # A value that might result from a mean calculation (like your 0.41xxx data)
#         0.41375,
#         123.4565, # Another .5 case
#         123.456  # Non .5 case
#     ]

#     print("Python round() behavior (Python 3):")
#     print("----------------------------------------------------")
#     print(f"{'Original Value':<25} | {'round(value)':<20} | {'Type of round(value)':<25}")
#     print("----------------------------------------------------")

#     for val in test_values:
#         rounded_val = round(val)
#         # Ensuring original value is printed with enough precision for comparison
#         print(f"{val:<25.17f} | {rounded_val:<20} | {str(type(rounded_val)):<25}")
#     print("----------------------------------------------------")

#     # Test a specific mean-like value
#     mean_example = 0.8275 / 2.0 # This is 0.41375, not an X.5
#     print("\nExample with a mean-like value:")
#     print(f"Mean (0.8275 / 2.0) = {mean_example:.17f}")
#     print(f"round(Mean)         = {round(mean_example)} (type: {type(round(mean_example))})") # Should be 0

#     mean_example_half = 0.5
#     print("\nExample with a .5 value for mean:")
#     print(f"Mean (e.g. 0.5)     = {mean_example_half:.17f}")
#     print(f"round(Mean)         = {round(mean_example_half)} (type: {type(round(mean_example_half))})") # Should be 0


# if __name__ == "__main__":
#     test_python_round()

import pandas as pd

def compare_rounding_from_file(file_path):
    """
    Reads data from a file, compares 'rounding' values with Python's round()
    applied to 'origin' values, and prints the comparison.

    Args:
        file_path (str): The path to the input data file.
    """
    rounding_values_file = []
    origin_values = []
    python_round_results = []
    original_lines = []

    try:
        with open(file_path, 'r') as f:
            for line_number, line in enumerate(f, 1):
                line = line.strip()
                if not line:  # Skip empty lines
                    continue
                original_lines.append(line)
                parts = line.split(' ')
                try:
                    if len(parts) == 2 and parts[0].startswith('rounding:') and parts[1].startswith('origin:'):
                        rounding_val_str = parts[0].split(':')[1]
                        origin_val_str = parts[1].split(':')[1]

                        rounding_val_file = int(rounding_val_str)
                        origin_val = float(origin_val_str)

                        rounding_values_file.append(rounding_val_file)
                        origin_values.append(origin_val)

                        # Apply Python's round() function (rounds to the nearest integer)
                        python_round_result = round(origin_val)
                        python_round_results.append(python_round_result)
                    else:
                        print(f"Warning: Line {line_number} has unexpected format: '{line}'. Skipping.")
                        # Append None to keep lists aligned for DataFrame, or handle as needed
                        rounding_values_file.append(None)
                        origin_values.append(None)
                        python_round_results.append(None)

                except ValueError as ve:
                    print(f"Warning: Could not parse data on line {line_number}: '{line}'. Error: {ve}. Skipping.")
                    rounding_values_file.append(None)
                    origin_values.append(None)
                    python_round_results.append(None)
                except IndexError:
                    print(f"Warning: Line {line_number} is missing parts: '{line}'. Skipping.")
                    rounding_values_file.append(None)
                    origin_values.append(None)
                    python_round_results.append(None)


    except FileNotFoundError:
        print(f"Error: The file '{file_path}' was not found.")
        return
    except Exception as e:
        print(f"An unexpected error occurred: {e}")
        return

    if not origin_values:
        print("No valid data was processed.")
        return

    # Create a Pandas DataFrame for comparison
    # Filter out rows where parsing might have failed (None values)
    # This is a more robust way to build the DataFrame when some lines might fail
    valid_data = []
    for i in range(len(original_lines)):
        if origin_values[i] is not None: # Check if origin_values[i] was successfully parsed
             valid_data.append({
                'Original_Line': original_lines[i],
                'File_Rounding': rounding_values_file[i],
                'Origin': origin_values[i],
                'Python_round(Origin)': python_round_results[i]
            })

    if not valid_data:
        print("No valid data could be structured for the DataFrame.")
        return

    df = pd.DataFrame(valid_data)

    # Add a column to check if the 'File_Rounding' matches 'Python_round(Origin)'
    # Ensure types are compatible for comparison, especially if None was introduced and not fully filtered
    df.dropna(subset=['File_Rounding', 'Python_round(Origin)'], inplace=True)
    df['File_Rounding'] = df['File_Rounding'].astype(int)
    df['Python_round(Origin)'] = df['Python_round(Origin)'].astype(int)

    df['Match'] = df['File_Rounding'] == df['Python_round(Origin)']

    # Print the DataFrame to console
    print("\n--- Comparison Results ---")
    print(df.to_string()) # .to_string() ensures all rows are printed

    # Save the DataFrame to a CSV file
    output_csv_file = "rounding_comparison_output.csv"
    try:
        df.to_csv(output_csv_file, index=False)
        print(f"\nResults have been saved to '{output_csv_file}'")
    except Exception as e:
        print(f"\nError saving results to CSV: {e}")

    # Display summary of matches
    if not df.empty:
        match_summary = df['Match'].value_counts(normalize=True) * 100
        print("\n--- Match Summary ---")
        print(match_summary)

        mismatched_rows = df[df['Match'] == False]
        if not mismatched_rows.empty:
            print("\n--- Mismatched Rows ---")
            print(mismatched_rows.to_string())
        else:
            print("\nAll processed rows match!")
    else:
        print("\nNo data to summarize.")


if __name__ == "__main__":
    # --- IMPORTANT ---
    # Replace 'your_data_file.txt' with the actual path to your data file.
    file_path = 'round.log'  # <--- MODIFY THIS LINE WITH YOUR FILE NAME/PATH

    # If you want to use the exact data from your initial prompt,
    # you can uncomment the following lines and comment out the file_path line above.
    #
    # import io
    # file_content_string = """rounding:0 origin:0.050274
    # rounding:0 origin:0.215215
    # rounding:0 origin:0.0904188
    # rounding:3 origin:2.78334
    # rounding:0 origin:0.0020951
    # rounding:0 origin:0.024004
    # rounding:43 origin:43.0362
    # rounding:2 origin:1.66931
    # rounding:0 origin:0.409434
    # rounding:0 origin:0.0352404
    # rounding:0 origin:0.348369
    # rounding:1 origin:1.1874
    # rounding:103159 origin:103159
    # rounding:2 origin:1.67918
    # rounding:0 origin:0.0750088
    # rounding:0 origin:0.0780747
    # rounding:0 origin:0.415619
    # rounding:0 origin:0.00993916
    # rounding:0 origin:0.00505606
    # rounding:0 origin:0.33817
    # rounding:0 origin:0.104687
    # rounding:21 origin:20.7105
    # rounding:2 origin:1.68112
    # rounding:0 origin:0.128998
    # rounding:0 origin:0.20303
    # rounding:0 origin:0.00666368
    # rounding:1 origin:0.81922
    # rounding:0 origin:0.0646508
    # rounding:1 origin:0.933967
    # rounding:0 origin:0.000602823"""
    # file_path = io.StringIO(file_content_string) # This treats the string as a file

    compare_rounding_from_file(file_path)