// #include <iostream>
// #include <cmath>     // For std::round
// #include <vector>
// #include <iomanip>   // For std::fixed and std::setprecision
// #include <limits>    // For numeric_limits

// int main() {
//     std::vector<double> test_values = {
//         // .5 cases that highlight the difference
//         0.5,
//         1.5,
//         2.5,
//         3.5,
//         4.5,
//         -0.5,
//         -1.5,
//         -2.5,
//         -3.5,
//         -4.5,
//         // Non-.5 cases (should behave similarly)
//         2.3,
//         2.7,
//         -2.3,
//         -2.7,
//         // Integers and zero
//         0.0,
//         2.0,
//         -2.0,
//         // Values very close to .5 to show rounding threshold
//         2.499999999999999,  // Should round to 2.0
//         2.500000000000001,  // Should round to 3.0
//         // A value that might result from a mean calculation (like your 0.41xxx data)
//         0.41375,
//         123.4565, // Another .5 case
//         123.456 // Non .5 case
//     };

//     std::cout << std::fixed << std::setprecision(17); // Show enough precision for doubles
//     std::cout << "C++ std::round() behavior:" << std::endl;
//     std::cout << "----------------------------------------------------" << std::endl;
//     std::cout << std::setw(25) << "Original Value" << " | " << std::setw(20) << "std::round(value)" << std::endl;
//     std::cout << "----------------------------------------------------" << std::endl;

//     for (double val : test_values) {
//         double rounded_val = std::round(val);
//         std::cout << std::setw(25) << val << " | " << std::setw(20) << rounded_val << std::endl;
//     }
//     std::cout << "----------------------------------------------------" << std::endl;

//     // Test a specific mean-like value
//     double mean_example = 0.8275 / 2.0; // This is 0.41375, not an X.5
//     std::cout << "\nExample with a mean-like value:" << std::endl;
//     std::cout << "Mean (0.8275 / 2.0) = " << mean_example << std::endl;
//     std::cout << "std::round(Mean)    = " << std::round(mean_example) << std::endl; // Should be 0.0

//     double mean_example_half = 2.0 / 8.0; // This is 0.25, not an X.5
//     if (test_values.size() > 0) { // ensure test_values is not empty
//         mean_example_half = test_values[0]; // Use 0.5 for a direct X.5 test
//         mean_example_half = 0.5; // ensure it is 0.5
//     }
//     std::cout << "\nExample with a .5 value for mean:" << std::endl;
//     std::cout << "Mean (e.g. 0.5)     = " << mean_example_half << std::endl;
//     std::cout << "std::round(Mean)    = " << std::round(mean_example_half) << std::endl; // Should be 1.0


//     return 0;
// }
#include <iostream>
#include <cmath>     // For std::round, std::fabs, std::fmod, std::copysign
#include <vector>
#include <iomanip>   // For std::fixed and std::setprecision
#include <limits>    // For std::numeric_limits for NaN/Inf checks if needed

// Function to mimic Python 3's round() behavior for a single argument
// Rounds to the nearest integer, with ties (x.5) going to the nearest even integer.
double pythonic_round(double val) {
    // Handle non-finite cases like std::round does (passthrough)
    // Python's round() would raise an error for these.
    // If you need exact Python error handling, this part would need to change.
    // For numerical calculations where std::round was used, passthrough is often preferred.
    if (std::isnan(val) || std::isinf(val)) {
        return val;
    }

    double rounded_val_std = std::round(val); // This rounds .5 away from zero

    // Check if 'val' was exactly a ".5" case.
    // std::fabs(val - rounded_val_std) will be 0.5 if val was X.5
    // Example: val = 2.5, rounded_val_std = 3.0. fabs(2.5 - 3.0) = 0.5
    // Example: val = 2.4, rounded_val_std = 2.0. fabs(2.4 - 2.0) = 0.4
    if (std::fabs(val - rounded_val_std) == 0.5) {
        // It's an X.5 case. Apply "round half to even" rule.
        // We need to check if 'rounded_val_std' (which is X+1 or X-1) is odd.
        // If it's odd, the other candidate (X) is even.
        // std::fmod can check for even/odd on a double representing an integer.
        // A value is odd if fmod(value, 2.0) is non-zero (i.e., +/-1.0 for integers).
        // A value is even if fmod(value, 2.0) is zero.
        if (std::fmod(rounded_val_std, 2.0) != 0.0) {
            // rounded_val_std is odd (e.g., 3.0 from 2.5, or 1.0 from 0.5, or -3.0 from -2.5)
            // The even number we want is closer to zero.
            // This can be found by subtracting 1 (with the correct sign) from rounded_val_std.
            return rounded_val_std - std::copysign(1.0, val);
            // Explanation:
            // If val =  2.5, rounded_val_std =  3.0 (odd).  Result:  3.0 - copysign(1.0,  2.5) =  3.0 - 1.0 =  2.0
            // If val =  0.5, rounded_val_std =  1.0 (odd).  Result:  1.0 - copysign(1.0,  0.5) =  1.0 - 1.0 =  0.0
            // If val = -2.5, rounded_val_std = -3.0 (odd).  Result: -3.0 - copysign(1.0, -2.5) = -3.0 - (-1.0) = -2.0
        } else {
            // rounded_val_std is already even (e.g., 2.0 from 1.5, or 4.0 from 3.5, or -2.0 from -1.5)
            // This is the correct "round half to even" result.
            return rounded_val_std;
        }
    } else {
        // Not an X.5 case, std::round already gives the nearest integer.
        return rounded_val_std;
    }
}

int main() {
    std::vector<double> test_values = {
        0.5, 1.5, 2.5, 3.5, 4.5,
        -0.5, -1.5, -2.5, -3.5, -4.5,
        2.3, 2.7, -2.3, -2.7,
        0.0, 2.0, -2.0,
        2.499999999999999,
        2.500000000000001,
        0.41375,
        // Python test output specific values for comparison
        // Python round(0.5) = 0
        // Python round(1.5) = 2
        // Python round(2.5) = 2
        // Python round(3.5) = 4
        // Python round(4.5) = 4
        // Python round(-0.5) = 0
        // Python round(-1.5) = -2
        // Python round(-2.5) = -2
        // Python round(-3.5) = -4
        // Python round(-4.5) = -4
    };

    std::cout << std::fixed << std::setprecision(17);
    std::cout << "C++ pythonic_round() behavior:" << std::endl;
    std::cout << "----------------------------------------------------" << std::endl;
    std::cout << std::setw(25) << "Original Value" << " | " << std::setw(20) << "pythonic_round(val)" << " | " << std::setw(20) << "std::round(val)" << std::endl;
    std::cout << "----------------------------------------------------" << std::endl;

    for (double val : test_values) {
        std::cout << std::setw(25) << val << " | " 
                  << std::setw(20) << pythonic_round(val) << " | "
                  << std::setw(20) << std::round(val) << std::endl;
    }
    std::cout << "----------------------------------------------------" << std::endl;
    
    // Directly test cases from Python output
    std::cout << "\nDirectly testing values from Python's round output:" << std::endl;
    std::cout << "pythonic_round(0.5) should be 0.0: " << pythonic_round(0.5) << std::endl;
    std::cout << "pythonic_round(1.5) should be 2.0: " << pythonic_round(1.5) << std::endl;
    std::cout << "pythonic_round(2.5) should be 2.0: " << pythonic_round(2.5) << std::endl;
    std::cout << "pythonic_round(3.5) should be 4.0: " << pythonic_round(3.5) << std::endl;
    std::cout << "pythonic_round(4.5) should be 4.0: " << pythonic_round(4.5) << std::endl;
    std::cout << "pythonic_round(-0.5) should be 0.0: " << pythonic_round(-0.5) << std::endl;
    std::cout << "pythonic_round(-1.5) should be -2.0: " << pythonic_round(-1.5) << std::endl;
    std::cout << "pythonic_round(-2.5) should be -2.0: " << pythonic_round(-2.5) << std::endl;
    std::cout << "pythonic_round(-3.5) should be -4.0: " << pythonic_round(-3.5) << std::endl;
    std::cout << "pythonic_round(-4.5) should be -4.0: " << pythonic_round(-4.5) << std::endl;


    return 0;
}