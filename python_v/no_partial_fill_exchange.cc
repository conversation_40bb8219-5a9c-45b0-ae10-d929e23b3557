#include <fast_trader_elite/cpp_backtest/proc/no_partial_fill_exchange.h>
#include <fast_trader_elite/cpp_backtest/backtest_logger.h>
#include <iostream>
#include <limits>

namespace fast_trader_elite {
namespace cpp_backtest {

no_partial_fill_exchange::no_partial_fill_exchange(market_depth* depth, fee_model* fee_model, queue_model* queue_model, latency_model* order_latency)
    : exchange_processor(depth, fee_model, queue_model, order_latency) {
}

void no_partial_fill_exchange::process_transaction(const transaction_field* trans) {
    // 获取成交价格的tick
    int64_t price_tick = depth_->price_to_tick(trans->price);
    double qty = trans->volume;

    // 记录成交信息 - 在性能模式下减少日志输出
#ifndef PERFORMANCE_MODE
    BT_LOG_DEBUG("exchange_process_transaction price:{} volume:{} is_maker:{} timestamp:{}",
                trans->price, trans->volume, trans->is_maker, current_timestamp_);
#endif

    // 确定交易方向
    side_type trade_side = !trans->is_maker ? side_type::buy : side_type::sell;
    side_type opposite_side = trade_side == side_type::buy ? side_type::sell : side_type::buy;

    // 使用价格索引快速查找可能匹配的订单，而不是遍历所有订单
    std::unordered_set<int64_t> order_ids_to_check;

    if (trade_side == side_type::buy) {
        // 买方发起成交，检查卖单
        // 查找价格小于等于成交价格的所有卖单
        auto& price_map = sell_orders_;
        for (auto it = price_map.begin(); it != price_map.end(); ++it) {
            if (it->first <= price_tick) {
                // 将这个价格层的所有订单ID添加到检查列表
                order_ids_to_check.insert(it->second.begin(), it->second.end());
            }
        }
    } else {
        // 卖方发起成交，检查买单
        // 查找价格大于等于成交价格的所有买单
        auto& price_map = buy_orders_;
        for (auto it = price_map.begin(); it != price_map.end(); ++it) {
            if (it->first >= price_tick) {
                // 将这个价格层的所有订单ID添加到检查列表
                order_ids_to_check.insert(it->second.begin(), it->second.end());
            }
        }
    }

    // 只检查可能匹配的订单
    for (int64_t order_id : order_ids_to_check) {
        auto it = orders_.find(order_id);
        if (it == orders_.end()) continue;

        auto& order = it->second;
        if (order.status != order_status::new_order) continue;

        int64_t order_price_tick = depth_->price_to_tick(order.price);
        bool can_fill = false;

        // 检查价格是否可以成交
        if (trade_side == side_type::buy) {
            // 买方发起成交，检查卖单
            if (order_price_tick < price_tick) {
                // 如果订单价格小于成交价格，直接成交
                can_fill = true;
            } else if (order_price_tick == price_tick) {
                // 如果订单价格等于成交价格，更新队列位置
                queue_model_->trade(order, qty, depth_);
                double filled_qty = queue_model_->is_filled(order, depth_);
                can_fill = (filled_qty > 0.0 && filled_qty >= order.quantity);
            }
        } else {
            // 卖方发起成交，检查买单
            if (order_price_tick > price_tick) {
                // 如果订单价格大于成交价格，直接成交
                can_fill = true;
            } else if (order_price_tick == price_tick) {
                // 如果订单价格等于成交价格，更新队列位置
                queue_model_->trade(order, qty, depth_);
                double filled_qty = queue_model_->is_filled(order, depth_);
                can_fill = (filled_qty > 0.0 && filled_qty >= order.quantity);
            }
        }

        // 如果可以成交，则成交订单
        if (can_fill) {
#ifndef PERFORMANCE_MODE
            BT_LOG_DEBUG("exchange_transaction_match order_id:{} instrument_idx:{} side:{} price:{} quantity:{}",
                        order.order_id, order.instrument_idx,
                        static_cast<int>(order.side), order.price, order.quantity);
#endif
            fill(order, order.price, order.quantity, true);
            send_order_report(order);
            filled_orders_.push_back(order.order_id);
        }
    }

    // 移除已成交订单
    remove_filled_orders();
}

backtest_error no_partial_fill_exchange::process_order(const internal_order& order) {
    // 复制订单
    internal_order order_copy = order;

    // 设置交易所时间戳
    order_copy.exchange_timestamp = current_timestamp_;

    // 记录订单处理信息 - 在性能模式下减少日志输出
#ifndef PERFORMANCE_MODE
    BT_LOG_DEBUG("exchange_process_order order_id:{} instrument_idx:{} side:{} type:{} price:{} quantity:{} request:{} timestamp:{}",
                order_copy.order_id, order_copy.instrument_idx,
                static_cast<int>(order_copy.side), static_cast<int>(order_copy.type),
                order_copy.price, order_copy.quantity,
                static_cast<int>(order_copy.request), current_timestamp_);
#endif

    // 根据请求类型处理 - 使用switch-case优化分支预测
    switch (order.request) {
        case order_status::new_order:
            return process_new_order(order_copy);
        case order_status::canceled:
            return process_cancel_order(order_copy);
        case order_status::none:
            // 已经处理过的订单，不需要再处理
            return backtest_error::none;
        default:
            // 处理无效操作
            order_copy.status = order_status::rejected;
            order_copy.error_id = static_cast<int>(backtest_error::invalid_operation);
#ifndef PERFORMANCE_MODE
            BT_LOG_WARN("exchange_order_rejected order_id:{} instrument_idx:{} reason:invalid_operation timestamp:{}",
                       order_copy.order_id, order_copy.instrument_idx, current_timestamp_);
#endif
            send_order_report(order_copy);
            return backtest_error::none;
    }
}

void no_partial_fill_exchange::try_match_orders() {
    // 使用价格索引快速查找可能匹配的订单，而不是遍历所有订单
    std::vector<int64_t> buy_orders_to_match;
    std::vector<int64_t> sell_orders_to_match;
    std::vector<int64_t> ioc_fok_orders;

    // 预分配空间，减少内存分配
    buy_orders_to_match.reserve(100);
    sell_orders_to_match.reserve(100);
    ioc_fok_orders.reserve(20);

    // 快速筛选可能匹配的订单
    int64_t best_ask_tick = depth_->best_ask_tick();
    int64_t best_bid_tick = depth_->best_bid_tick();

    // 只有当市场有深度时才进行匹配
    bool has_ask = (best_ask_tick != std::numeric_limits<int64_t>::max());
    bool has_bid = (best_bid_tick != std::numeric_limits<int64_t>::min());

    if (has_ask || has_bid) {
        for (auto& [order_id, order] : orders_) {
            if (order.status != order_status::new_order) {
                continue;
            }

            int64_t order_price_tick = depth_->price_to_tick(order.price);

            if (order.side == side_type::buy && has_ask) {
                // 买单检查卖盘
                if (order_price_tick >= best_ask_tick) {
                    buy_orders_to_match.push_back(order_id);
                } else if (order.time_in_force == time_in_force_type::ioc ||
                           order.time_in_force == time_in_force_type::fok) {
                    ioc_fok_orders.push_back(order_id);
                }
            } else if (order.side == side_type::sell && has_bid) {
                // 卖单检查买盘
                if (order_price_tick <= best_bid_tick) {
                    sell_orders_to_match.push_back(order_id);
                } else if (order.time_in_force == time_in_force_type::ioc ||
                           order.time_in_force == time_in_force_type::fok) {
                    ioc_fok_orders.push_back(order_id);
                }
            }
        }
    }

    // 处理可能匹配的买单
    for (int64_t order_id : buy_orders_to_match) {
        auto it = orders_.find(order_id);
        if (it == orders_.end() || it->second.status != order_status::new_order) continue;

        auto& order = it->second;
        double filled_qty = queue_model_->is_filled(order, depth_);

        if (filled_qty > 0.0 && (order.type == order_type::market || filled_qty >= order.quantity)) {
            // 成交订单
            double market_price = depth_->tick_to_price(best_ask_tick);
            fill(order, market_price, order.quantity, true);
            send_order_report(order);
            filled_orders_.push_back(order_id);
        } else if (order.time_in_force == time_in_force_type::ioc ||
                   order.time_in_force == time_in_force_type::fok) {
            // 如果是IOC或FOK订单且未成交，则取消
            order.status = order_status::canceled;
            send_order_report(order);
        }
    }

    // 处理可能匹配的卖单
    for (int64_t order_id : sell_orders_to_match) {
        auto it = orders_.find(order_id);
        if (it == orders_.end() || it->second.status != order_status::new_order) continue;

        auto& order = it->second;
        double filled_qty = queue_model_->is_filled(order, depth_);

        if (filled_qty > 0.0 && (order.type == order_type::market || filled_qty >= order.quantity)) {
            // 成交订单
            double market_price = depth_->tick_to_price(best_bid_tick);
            fill(order, market_price, order.quantity, true);
            send_order_report(order);
            filled_orders_.push_back(order_id);
        } else if (order.time_in_force == time_in_force_type::ioc ||
                   order.time_in_force == time_in_force_type::fok) {
            // 如果是IOC或FOK订单且未成交，则取消
            order.status = order_status::canceled;
            send_order_report(order);
        }
    }

    // 处理未匹配的IOC或FOK订单
    for (int64_t order_id : ioc_fok_orders) {
        auto it = orders_.find(order_id);
        if (it == orders_.end() || it->second.status != order_status::new_order) continue;

        // 取消订单
        it->second.status = order_status::canceled;
        send_order_report(it->second);
    }

    // 移除已成交订单
    remove_filled_orders();
}

backtest_error no_partial_fill_exchange::process_new_order(internal_order& order) {
    // 检查订单ID是否已存在
    if (orders_.find(order.order_id) != orders_.end()) {
        order.status = order_status::rejected;
        order.error_id = static_cast<int>(backtest_error::order_id_exist);
        send_order_report(order);
        return backtest_error::none;
    }

    // 检查价格是否有效
    if (order.price < 0.0) {
        order.status = order_status::rejected;
        order.error_id = static_cast<int>(backtest_error::invalid_price);
        send_order_report(order);
        return backtest_error::none;
    }

    // 检查数量是否有效
    if (order.quantity <= 0.0) {
        order.status = order_status::rejected;
        order.error_id = static_cast<int>(backtest_error::invalid_quantity);
        send_order_report(order);
        return backtest_error::none;
    }

    // 检查方向是否有效
    if (order.side != side_type::buy && order.side != side_type::sell) {
        order.status = order_status::rejected;
        order.error_id = static_cast<int>(backtest_error::invalid_side);
        send_order_report(order);
        return backtest_error::none;
    }

    // 检查订单类型是否有效
    if (order.type != order_type::limit && order.type != order_type::market) {
        order.status = order_status::rejected;
        order.error_id = static_cast<int>(backtest_error::invalid_order_type);
        send_order_report(order);
        return backtest_error::none;
    }

    // 检查有效期类型是否有效
    if (order.time_in_force != time_in_force_type::gtc &&
        order.time_in_force != time_in_force_type::gtx &&
        order.time_in_force != time_in_force_type::ioc &&
        order.time_in_force != time_in_force_type::fok) {
        order.status = order_status::rejected;
        order.error_id = static_cast<int>(backtest_error::invalid_time_in_force);
        send_order_report(order);
        return backtest_error::none;
    }

    // 处理买单
    if (order.side == side_type::buy) {
        if (order.type == order_type::limit) {
            // 检查买单价格是否大于等于当前最佳卖价
            if (depth_->price_to_tick(order.price) >= depth_->best_ask_tick()) {
                // 如果是GTX订单，则拒绝
                if (order.time_in_force == time_in_force_type::gtx) {
                    order.status = order_status::rejected;
                    send_order_report(order);
                    return backtest_error::none;
                }

                // 如果是FOK订单，需要检查是否有足够的市场深度来完全成交订单
                if (order.time_in_force == time_in_force_type::fok) {
                    bool execute = false;
                    double cum_qty = 0.0;
                    int64_t order_price_tick = depth_->price_to_tick(order.price);

                    // 获取卖单价格和数量列表
                    const std::vector<int64_t>& ask_price_ticks = depth_->ask_prices();
                    const std::vector<double>& ask_quantities = depth_->ask_qtys();

                    // 遍历卖单深度，但只考虑价格不高于订单价格的档位
                    for (size_t i = 0; i < ask_price_ticks.size(); ++i) {
                        int64_t t = ask_price_ticks[i];
                        if (t > order_price_tick) {
                            break; // 超过订单价格，不再考虑
                        }
                        cum_qty += ask_quantities[i];
                        if (cum_qty >= order.quantity) {
                            execute = true;
                            break;
                        }
                    }

                    if (!execute) {
                        // 如果没有足够的市场深度，则取消订单
                        order.status = order_status::canceled;
                        send_order_report(order);
                        return backtest_error::none;
                    }
                }

                // 吃单 价格简化处理
                fill(order, depth_->tick_to_price(depth_->best_ask_tick()), order.quantity, false);
                send_order_report(order);
                return backtest_error::none;
            } else {
                // 如果是IOC或FOK订单，则拒绝
                if (order.time_in_force == time_in_force_type::ioc ||
                    order.time_in_force == time_in_force_type::fok) {
                    order.status = order_status::canceled;
                    send_order_report(order);
                    return backtest_error::none;
                }

                // 挂单
                order.status = order_status::new_order;
                queue_model_->new_order(order, depth_);
                orders_[order.order_id] = order;
                buy_orders_[depth_->price_to_tick(order.price)].insert(order.order_id);
                send_order_report(order);
                return backtest_error::none;
            }
        } else if (order.type == order_type::market) {
            // 对于市价单，需要检查是否有足够的市场深度来完全成交订单
            bool execute = false;
            double cum_qty = 0.0;
            double avg_price = 0.0;

            // 获取卖单价格和数量列表
            const std::vector<int64_t>& ask_price_ticks = depth_->ask_prices();
            const std::vector<double>& ask_quantities = depth_->ask_qtys();

            // 遍历卖单深度
            for (size_t i = 0; i < ask_price_ticks.size(); ++i) {
                int64_t t = ask_price_ticks[i];
                double qty = ask_quantities[i];
                if (qty > 0.0) {
                    double price = depth_->tick_to_price(t);
                    double exec_qty = std::min(qty, order.quantity - cum_qty);

                    // 更新加权平均价格
                    if (cum_qty == 0.0) {
                        avg_price = price;
                    } else {
                        avg_price = (cum_qty * avg_price + exec_qty * price) / (cum_qty + exec_qty);
                    }

                    cum_qty += exec_qty;

                    if (cum_qty >= order.quantity) {
                        execute = true;
                        break;
                    }
                }
            }

            if (execute) {
                // 吃单
                fill(order, avg_price, order.quantity, false);
                send_order_report(order);
                return backtest_error::none;
            } else {
                // 如果没有足够的市场深度，则取消订单
                order.status = order_status::canceled;
                send_order_report(order);
                return backtest_error::none;
            }
        }
    }
    // 处理卖单
    else if (order.side == side_type::sell) {
        if (order.type == order_type::limit) {
            // 检查卖单价格是否小于等于当前最佳买价
            if (depth_->price_to_tick(order.price) <= depth_->best_bid_tick()) {
                // 如果是GTX订单，则拒绝
                if (order.time_in_force == time_in_force_type::gtx) {
                    order.status = order_status::rejected;
                    send_order_report(order);
                    return backtest_error::none;
                }

                // 如果是FOK订单，需要检查是否有足够的市场深度来完全成交订单
                if (order.time_in_force == time_in_force_type::fok) {
                    bool execute = false;
                    double cum_qty = 0.0;
                    int64_t order_price_tick = depth_->price_to_tick(order.price);

                    // 获取买单价格和数量列表
                    const std::vector<int64_t>& bid_price_ticks = depth_->bid_prices();
                    const std::vector<double>& bid_quantities = depth_->bid_qtys();

                    // 遍历买单深度，但只考虑价格不低于订单价格的档位
                    for (size_t i = 0; i < bid_price_ticks.size(); ++i) {
                        int64_t t = bid_price_ticks[i];
                        if (t < order_price_tick) {
                            break; // 低于订单价格，不再考虑
                        }
                        cum_qty += bid_quantities[i];
                        if (cum_qty >= order.quantity) {
                            execute = true;
                            break;
                        }
                    }

                    if (!execute) {
                        // 如果没有足够的市场深度，则取消订单
                        order.status = order_status::canceled;
                        send_order_report(order);
                        return backtest_error::none;
                    }
                }

                // 吃单
                fill(order, depth_->tick_to_price(depth_->best_bid_tick()), order.quantity, false);
                send_order_report(order);
                return backtest_error::none;
            } else {
                // 如果是IOC或FOK订单，则拒绝
                if (order.time_in_force == time_in_force_type::ioc ||
                    order.time_in_force == time_in_force_type::fok) {
                    order.status = order_status::canceled;
                    send_order_report(order);
                    return backtest_error::none;
                }

                // 挂单
                order.status = order_status::new_order;
                queue_model_->new_order(order, depth_);
                orders_[order.order_id] = order;
                sell_orders_[depth_->price_to_tick(order.price)].insert(order.order_id);
                send_order_report(order);
                return backtest_error::none;
            }
        } else if (order.type == order_type::market) {
            // 对于市价单，需要检查是否有足够的市场深度来完全成交订单
            bool execute = false;
            double cum_qty = 0.0;
            double avg_price = 0.0;

            // 获取买单价格和数量列表
            const std::vector<int64_t>& bid_price_ticks = depth_->bid_prices();
            const std::vector<double>& bid_quantities = depth_->bid_qtys();

            // 遍历买单深度
            for (size_t i = 0; i < bid_price_ticks.size(); ++i) {
                int64_t t = bid_price_ticks[i];
                double qty = bid_quantities[i];
                if (qty > 0.0) {
                    double price = depth_->tick_to_price(t);
                    double exec_qty = std::min(qty, order.quantity - cum_qty);

                    // 更新加权平均价格
                    if (cum_qty == 0.0) {
                        avg_price = price;
                    } else {
                        avg_price = (cum_qty * avg_price + exec_qty * price) / (cum_qty + exec_qty);
                    }

                    cum_qty += exec_qty;

                    if (cum_qty >= order.quantity) {
                        execute = true;
                        break;
                    }
                }
            }

            if (execute) {
                // 吃单
                fill(order, avg_price, order.quantity, false);
                send_order_report(order);
                return backtest_error::none;
            } else {
                // 如果没有足够的市场深度，则取消订单
                order.status = order_status::canceled;
                send_order_report(order);
                return backtest_error::none;
            }
        }
    }

    return backtest_error::invalid_operation;
}

backtest_error no_partial_fill_exchange::process_cancel_order(internal_order& order) {
    // 记录取消订单信息
    BT_LOG_DEBUG("exchange_process_cancel_order order_id:{} instrument_idx:{} timestamp:{}",
                order.order_id, order.instrument_idx, current_timestamp_);

    // 查找订单
    auto it = orders_.find(order.order_id);
    if (it == orders_.end()) {
        order.status = order_status::cancel_rejected;
        order.error_id = static_cast<int>(backtest_error::order_not_found);
        BT_LOG_WARN("exchange_cancel_rejected order_id:{} instrument_idx:{} reason:order_not_found timestamp:{}",
                   order.order_id, order.instrument_idx, current_timestamp_);
        send_order_report(order);
        return backtest_error::none;
    }

    // 获取订单
    internal_order& existing_order = it->second;

    // 检查订单状态
    if (existing_order.status != order_status::new_order) {
        order.status = order_status::cancel_rejected;
        order.error_id = static_cast<int>(backtest_error::invalid_state);
        BT_LOG_WARN("exchange_cancel_rejected order_id:{} instrument_idx:{} reason:invalid_state status:{} timestamp:{}",
                   order.order_id, order.instrument_idx,
                   static_cast<int>(existing_order.status), current_timestamp_);
        send_order_report(order);
        return backtest_error::none;
    }

    // 从价格层中移除订单
    if (existing_order.side == side_type::buy) {
        auto price_it = buy_orders_.find(depth_->price_to_tick(existing_order.price));
        if (price_it != buy_orders_.end()) {
            price_it->second.erase(existing_order.order_id);
            if (price_it->second.empty()) {
                buy_orders_.erase(price_it);
            }
        }
    } else {
        auto price_it = sell_orders_.find(depth_->price_to_tick(existing_order.price));
        if (price_it != sell_orders_.end()) {
            price_it->second.erase(existing_order.order_id);
            if (price_it->second.empty()) {
                sell_orders_.erase(price_it);
            }
        }
    }

    // 更新订单状态
    existing_order.status = order_status::canceled;
    existing_order.exchange_timestamp = current_timestamp_;

    BT_LOG_INFO("exchange_order_canceled order_id:{} instrument_idx:{} side:{} price:{} quantity:{} timestamp:{}",
               existing_order.order_id, existing_order.instrument_idx,
               static_cast<int>(existing_order.side), existing_order.price,
               existing_order.quantity, current_timestamp_);

    // 发送订单回报
    send_order_report(existing_order);

    return backtest_error::none;
}

backtest_error no_partial_fill_exchange::process_modify_order(internal_order& order) {
    // 查找订单
    auto it = orders_.find(order.order_id);
    if (it == orders_.end()) {
        order.status = order_status::rejected;
        order.error_id = static_cast<int>(backtest_error::order_not_found);
        send_order_report(order);
        return backtest_error::none;
    }

    // 获取订单
    internal_order& existing_order = it->second;

    // 检查订单状态
    if (existing_order.status != order_status::new_order) {
        order.status = order_status::rejected;
        order.error_id = static_cast<int>(backtest_error::invalid_state);
        send_order_report(order);
        return backtest_error::none;
    }

    // 从价格层中移除订单
    if (existing_order.side == side_type::buy) {
        auto price_it = buy_orders_.find(depth_->price_to_tick(existing_order.price));
        if (price_it != buy_orders_.end()) {
            price_it->second.erase(existing_order.order_id);
            if (price_it->second.empty()) {
                buy_orders_.erase(price_it);
            }
        }
    } else {
        auto price_it = sell_orders_.find(depth_->price_to_tick(existing_order.price));
        if (price_it != sell_orders_.end()) {
            price_it->second.erase(existing_order.order_id);
            if (price_it->second.empty()) {
                sell_orders_.erase(price_it);
            }
        }
    }

    // 更新订单
    existing_order.price = order.price;
    existing_order.quantity = order.quantity;
    existing_order.leaves_quantity = order.quantity;
    existing_order.exchange_timestamp = current_timestamp_;

    // 重新初始化队列位置
    queue_model_->new_order(existing_order, depth_);

    // 添加到价格层
    if (existing_order.side == side_type::buy) {
        buy_orders_[depth_->price_to_tick(existing_order.price)].insert(existing_order.order_id);
    } else {
        sell_orders_[depth_->price_to_tick(existing_order.price)].insert(existing_order.order_id);
    }

    // 发送订单回报
    send_order_report(existing_order);

    return backtest_error::none;
}

void no_partial_fill_exchange::fill(internal_order& order, double fill_price, double fill_quantity, bool is_maker) {
    // 记录成交前信息 - 在性能模式下减少日志输出
#ifndef PERFORMANCE_MODE
    BT_LOG_DEBUG("exchange_fill_order_start order_id:{} instrument_idx:{} side:{} fill_price:{} fill_qty:{} is_maker:{} timestamp:{}",
                order.order_id, order.instrument_idx,
                static_cast<int>(order.side), fill_price, order.quantity,
                is_maker, current_timestamp_);
#endif

    // 更新订单状态
    order.status = order_status::filled;
    order.executed_quantity = order.quantity;
    order.leaves_quantity = 0.0;
    order.executed_price = fill_price;
    order.is_maker = is_maker;
    order.exchange_timestamp = current_timestamp_;

    // 计算手续费
    double fee = fee_model_->calculate_fee(fill_price, order.quantity, is_maker);
    order.fee = fee;

    // 记录成交信息 - 在性能模式下减少日志输出
#ifndef PERFORMANCE_MODE
    BT_LOG_INFO("exchange_order_filled order_id:{} instrument_idx:{} side:{} fill_price:{} fill_qty:{} fee:{} is_maker:{} timestamp:{}",
               order.order_id, order.instrument_idx,
               static_cast<int>(order.side), fill_price, order.quantity,
               fee, is_maker, current_timestamp_);
#endif

    // 更新状态
    state_.apply_fill(order);
}

void no_partial_fill_exchange::on_best_bid_update(int64_t prev_best_tick, int64_t new_best_tick) {
    // 如果最佳买价上升，检查是否有卖单可以成交
    if (new_best_tick > prev_best_tick) {
        // 使用价格索引快速查找可能匹配的卖单
        std::vector<int64_t> orders_to_fill;
        orders_to_fill.reserve(20); // 预分配空间

        // 查找价格小于等于新最佳买价的所有卖单
        for (auto it = sell_orders_.begin(); it != sell_orders_.end(); ++it) {
            if (it->first <= new_best_tick) {
                // 将这个价格层的所有订单ID添加到成交列表
                const auto& order_ids = it->second;
                for (int64_t order_id : order_ids) {
                    auto order_it = orders_.find(order_id);
                    if (order_it != orders_.end() && order_it->second.status == order_status::new_order) {
                        orders_to_fill.push_back(order_id);
                    }
                }
            }
        }

        // 处理需要成交的订单
        for (int64_t order_id : orders_to_fill) {
            auto it = orders_.find(order_id);
            if (it == orders_.end() || it->second.status != order_status::new_order) continue;

            // 全部成交交易所直接成交
            fill(it->second, it->second.price, it->second.quantity, true);
            send_order_report(it->second);
            filled_orders_.push_back(order_id);
        }

        // 如果有订单成交，移除已成交订单
        if (!orders_to_fill.empty()) {
            remove_filled_orders();
        }
    }
}

void no_partial_fill_exchange::on_best_ask_update(int64_t prev_best_tick, int64_t new_best_tick) {
    // 如果最佳卖价下降，检查是否有买单可以成交
    if (new_best_tick < prev_best_tick) {
        // 使用价格索引快速查找可能匹配的买单
        std::vector<int64_t> orders_to_fill;
        orders_to_fill.reserve(20); // 预分配空间

        // 查找价格大于等于新最佳卖价的所有买单
        for (auto it = buy_orders_.begin(); it != buy_orders_.end(); ++it) {
            if (it->first >= new_best_tick) {
                // 将这个价格层的所有订单ID添加到成交列表
                const auto& order_ids = it->second;
                for (int64_t order_id : order_ids) {
                    auto order_it = orders_.find(order_id);
                    if (order_it != orders_.end() && order_it->second.status == order_status::new_order) {
                        orders_to_fill.push_back(order_id);
                    }
                }
            }
        }

        // 处理需要成交的订单
        for (int64_t order_id : orders_to_fill) {
            auto it = orders_.find(order_id);
            if (it == orders_.end() || it->second.status != order_status::new_order) continue;

            // 全部成交交易所直接成交
            fill(it->second, it->second.price, it->second.quantity, true);
            send_order_report(it->second);
            filled_orders_.push_back(order_id);
        }

        // 如果有订单成交，移除已成交订单
        if (!orders_to_fill.empty()) {
            remove_filled_orders();
        }
    }
}

void no_partial_fill_exchange::on_bid_qty_change(int64_t price_tick, double prev_qty, double new_qty) {
    // 全部成交交易所不需要精确跟踪队列位置
    // 只需要在最佳价格变化时检查是否有订单可以成交
}

void no_partial_fill_exchange::on_ask_qty_change(int64_t price_tick, double prev_qty, double new_qty) {
    // 全部成交交易所不需要精确跟踪队列位置
    // 只需要在最佳价格变化时检查是否有订单可以成交
}

void no_partial_fill_exchange::remove_filled_orders() {
    // 如果没有已成交订单，直接返回
    if (filled_orders_.empty()) {
        return;
    }

    // 使用哈希表优化查找效率
    std::unordered_map<int64_t, std::vector<int64_t>> buy_price_orders;
    std::unordered_map<int64_t, std::vector<int64_t>> sell_price_orders;

    // 预分配空间，减少内存分配
    buy_price_orders.reserve(10);
    sell_price_orders.reserve(10);

    // 按价格分组订单，减少对价格层的查找次数
    for (int64_t order_id : filled_orders_) {
        auto it = orders_.find(order_id);
        if (it == orders_.end()) continue;

        int64_t price_tick = depth_->price_to_tick(it->second.price);

        if (it->second.side == side_type::buy) {
            buy_price_orders[price_tick].push_back(order_id);
        } else {
            sell_price_orders[price_tick].push_back(order_id);
        }
    }

    // 批量处理买单
    for (auto& [price_tick, order_ids] : buy_price_orders) {
        auto price_it = buy_orders_.find(price_tick);
        if (price_it != buy_orders_.end()) {
            // 批量移除订单
            for (int64_t order_id : order_ids) {
                price_it->second.erase(order_id);
            }

            // 如果价格层为空，移除价格层
            if (price_it->second.empty()) {
                buy_orders_.erase(price_it);
            }
        }
    }

    // 批量处理卖单
    for (auto& [price_tick, order_ids] : sell_price_orders) {
        auto price_it = sell_orders_.find(price_tick);
        if (price_it != sell_orders_.end()) {
            // 批量移除订单
            for (int64_t order_id : order_ids) {
                price_it->second.erase(order_id);
            }

            // 如果价格层为空，移除价格层
            if (price_it->second.empty()) {
                sell_orders_.erase(price_it);
            }
        }
    }

    // 从订单列表中移除已成交订单
    for (int64_t order_id : filled_orders_) {
        orders_.erase(order_id);
    }

    filled_orders_.clear();
}

} // namespace cpp_backtest
} // namespace fast_trader_elite
