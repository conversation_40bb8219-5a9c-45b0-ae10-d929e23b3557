import ccxt.async_support as ccxt_async
import json
import os
from datetime import datetime
import numpy as np
import pandas as pd
import pprint
import asyncio
import sys
from time import time, sleep
from typing import Iterator
from scipy.stats import skew
from math import sqrt, floor
# import torch
import logging 
import pickle
import traceback

project_root = os.path.abspath(os.path.join(os.path.dirname(__file__)))
sys.path.append(project_root)
from utils.functions import *
# from model.model_lstm import LSTMRegression
from utils.market_volatility import market_stats, ShortTermVolatility
from utils.AS_api.src.IntensityInfo import IntensityInfo
from utils.AS_api.src.EstimationExecutor import EstimationExecutor
from utils.AS_api.src.IntensityEstimator import IntensityEstimator
from utils.AS_api.src.calibration.AkSolverFactory import AkSolverFactory,SolverType
from utils.bar import volume_bar_system
from bot_logger import logger


def convert2bps(x):
    return x * 10000

def make_get_filepath(filepath: str) -> str:
    '''
    if not is path, creates dir and subdirs for path, returns path
    '''
    dirpath = os.path.dirname(filepath) if filepath[-1] != '/' else filepath
    if not os.path.isdir(dirpath):
        os.makedirs(dirpath)
    return filepath


def load_key_secret(exchange: str, user: str) -> (str, str):
    try:
        return json.load(open(f'api_key_secrets/{exchange}/{user}.json'))
    except (FileNotFoundError):
        print(f'\n\nPlease specify {exchange} API key/secret in file\n\napi_key_secre' +
              f'ts/{exchange}/{user}.json\n\nformatted thus:\n["Ktnks95U...", "yDKRQqA6..."]\n\n')
        raise Exception('api key secret missing')


def init_ccxt(exchange: str = None, user: str = None):
    if user is None:
        cc = getattr(ccxt_async, exchange)
    try:
        cc = getattr(ccxt_async, exchange)({'apiKey': (ks := load_key_secret(exchange, user))[0],
                                            'secret': ks[1]})
    except Exception as e:
        print('error init ccxt', e)
        cc = getattr(ccxt_async, exchange)
    # print('ccxt enableRateLimit true')
    # cc.enableRateLimit = True
    return cc


def print_(args, r=False, n=False):
    line = ts_to_date(time())[:19] + '  '
    str_args = '{} ' * len(args)
    line += str_args.format(*args)
    if n:
        print('\n' + line, end=' ')
    elif r:
        print('\r' + line, end=' ')
    else:
        print(line)
    return line


def load_live_settings(exchange: str, user: str = 'default', do_print=True) -> dict:
    fpath = f'live_settings/{exchange}/'
    try:
        settings = json.load(open(f'{fpath}{user}.json'))
    except FileNotFoundError:
        print_([f'settings for user {user} not found, using default settings'])
        settings = json.load(open(f'{fpath}default.json'))
    if do_print:
        print('\nloaded settings:')
        pprint.pprint(settings)

    return settings

def ts_to_date(timestamp: float) -> str:
    return str(datetime.fromtimestamp(timestamp)).replace(' ', 'T')

def unrealize_profit(price_, pos_price, qty):
    return (price_ - pos_price) * qty

def filter_orders(actual_orders: [dict],
                  ideal_orders: [dict],
                  keys: [str] = ['symbol', 'side', 'qty', 'price']) -> ([dict], [dict]):
    # returns (orders_to_delete, orders_to_create)
    # print_([actual_orders,ideal_orders],n=True)
    if not actual_orders:
        return [], ideal_orders
    if not ideal_orders:
        return actual_orders, []
    actual_orders = actual_orders.copy()
    orders_to_create = []
    ideal_orders_cropped = [{k: o[k] for k in keys} for o in ideal_orders]
    actual_orders_cropped = [{k: o[k] for k in keys} for o in actual_orders]
    for ioc, io in zip(ideal_orders_cropped, ideal_orders):
        matches = [(aoc, ao) for aoc, ao in zip(
            actual_orders_cropped, actual_orders) if aoc == ioc]
        if matches:
            actual_orders.remove(matches[0][1])
            actual_orders_cropped.remove(matches[0][0])
        else:
            orders_to_create.append(io)
    return actual_orders, orders_to_create

def load_LSTM_model(model_path, input_size, hidden_size, num_layers, output_size, seq_length, device="cpu"):  

    model = LSTMRegression(input_size, hidden_size, num_layers, output_size, seq_length)  
    model.load_state_dict(torch.load(model_path, map_location=device))  # 加载权重  
    model.to(device)  
    return model 

def infer_with_scaler(model, scaler, input_data, seq_length=300, prediction_length=200):  

    if input_data.shape[0] != seq_length:  
        raise ValueError(f"Input data must have {seq_length} time steps, but got {input_data.shape[0]}.")   
    normalized_input = scaler.transform(input_data)  
    input_tensor = torch.tensor(normalized_input, dtype=torch.float32).unsqueeze(0) 
    model.eval()  
    with torch.no_grad():  
        predicted_sequence = model(input_tensor)  # (1, prediction_length)  
        predicted_sequence = predicted_sequence.squeeze(0).numpy()  # 转换为 numpy 数组  

    input_size = input_data.shape[1]  
    denormalized_output = scaler.inverse_transform(  
        np.concatenate([predicted_sequence[:, None], np.zeros((prediction_length, input_size - 1))], axis=1)  
    )[:, 0]  

    return denormalized_output 

def load_scaler():
    with open("model_weights/LSTM_feature_scaler.pkl", "rb") as f:  
        feature_scaler = pickle.load(f)  
    return feature_scaler

class Bot:
    def __init__(self, user: str, settings: dict):
        self.settings = settings
        self.indicator_settings = settings['indicator_settings']
        self.user = user
        self.symbol = settings['symbol']
        self.leverage = settings['leverage']
        self.stop_loss_liq_diff = settings['stop_loss_liq_diff']
        self.stop_loss_pos_price_diff = settings['stop_loss_pos_price_diff']
        self.stop_loss_pos_reduction = settings['stop_loss_pos_reduction']
        self.grid_coefficient = settings['grid_coefficient']
        self.grid_spacing = settings['grid_spacing']
        self.max_markup = settings['max_markup']
        self.min_markup = settings['min_markup'] if self.max_markup >= settings['min_markup'] \
            else settings['max_markup']
        self.balance_pct = settings['balance_pct']
        self.n_entry_orders = settings['n_entry_orders']
        self.n_close_orders = settings['n_close_orders']
        self.entry_qty_pct = settings['entry_qty_pct']
        self.ddown_factor = settings['ddown_factor']
        self.ema_spread = settings['indicator_settings']['tick_ema']['spread'] \
            if 'spread' in settings['indicator_settings']['tick_ema'] else 0.0
        self.min_close_qty_multiplier = settings['min_close_qty_multiplier']
        self.max_sl_pct = settings['stop_loss_danger'] if 'stop_loss_danger' in settings else 4
        self.sl_cd_by_hour = settings['stop_loss_cooldown'] if 'stop_loss_cooldown' in settings else 4
        self.first_imbalance = settings['indicator_settings']['tick_ema'][
            'first_imbalance'] if 'first_imbalance' in settings['indicator_settings']['tick_ema'] else -1.0
        self.num_std = settings['indicator_settings']['tick_ema'][
            'num_std'] if 'num_std' in settings['indicator_settings']['tick_ema'] else 0.0
        self.std_spacing = settings['indicator_settings']['tick_ema'][
            'std_spacing'] if 'std_spacing' in settings['indicator_settings']['tick_ema'] else 0.0
        self.std_width = settings['indicator_settings']['tick_ema']['std_width'] if 'std_width' in settings['indicator_settings']['tick_ema'] else 1.0
        self.ema_shift_multi = settings['indicator_settings']['tick_ema']['ema_shift_multi']
        self.market_stop_loss = settings['market_stop_loss']

        self.ts_locked = {'cancel_orders': 0, 'decide': 0, 'update_open_orders': 0,
                          'update_position': 0, 'print': 0, 'create_orders': 0, 'decide_lock': 0}
        self.ts_released = {k: 1 for k in self.ts_locked}
        self.trade_agg = pd.DataFrame()
        self.position = {}
        self.open_orders = []
        self.highest_bid = 0.0
        self.lowest_ask = 9.9e9
        self.price = 0
        self.mid_price = 0
        self.ob = [0.0, 0.0]
        self.extreme_situation = False
        self.timestamp_sl = -3600
        # self.dom_ask[n][0] is price; self.dom_ask[n][1] is volume
        self.dom_ask = np.array([])
        self.dom_bid = np.array([])
        self.trade_list = np.array([])
        self.indicators = {'tick': {}, 'ohlcv': {}}
        self.ohlcvs = {}
        self.trade_imbalance = 0.0
        self.delta_qty = 0.0
        self.timestamp_longp_market_check = 0.0
        self.timestamp_midp_market_check = 0.0 
        self.mid_period_vol = None
        self.as_ask_price = 0.0
        self.as_bid_price = 0.0
        self.log_filepath = make_get_filepath(
            f"logs/{self.exchange}/{settings['symbol']}.log")

        self.my_trades = []
        self.my_trades_cache_filepath = \
            make_get_filepath(os.path.join('historical_data', self.exchange, 'my_trades',
                                           self.symbol, 'my_trades.txt'))

        self.log_level = 0
        self.latest_histotical_price = np.array([])
        self.histotical_qty = np.array([])
        self.std_dev = 0.0
        self.std_width = 1.0 
        self.short_std = 0.0 # len = 100
        self.long_std = 0.0 # len = 100
        self.stop_websocket = False
        self.no_trade_time = 0.0
        self.last_atr_fix = 1.0 # for test purposes
        self.use_as = True
        self.intensty_info = None
        self.solver = AkSolverFactory(SolverType.MULTI_CURVE)
        self.short_term_vol = ShortTermVolatility(180)
        self.intensityestimator = None
        self.empintensityestimator = None
        self.buy_est = None
        self.sell_est = None
        self.buy_ak = [0,0]
        self.sell_ak = [0,0]
        self.as_ask_price = 9e8
        self.as_bid_price = 0
        self.norm_spread = np.array([])
        self.spread_slop = 0.0
        self.norm_spreads_ma = 0.0
        self.spread_ma_quene = np.array([])
        self.vol_bar_threadhold = 0.0
        self.volume_bars_sys = None
        self.reverse_trade = False
        self.reverse_trailing_price = -1.0
        # logger = logging.getLogger(f"{__name__}.{self.__class__.__name__}")
        # handler = logging.FileHandler(f'bot_{self.settings["config_name"]}.log', mode='w')
        # handler.setFormatter(logging.Formatter('%(asctime)s - %(levelname)s - %(message)s'))
        # logger.setLevel(logging.DEBUG)
        # logger.addHandler(handler)
        handler = logging.FileHandler(f'bot_{self.settings["config_name"]}.log', mode='a')
        handler.setFormatter(logging.Formatter('%(asctime)s - %(levelname)s - %(message)s'))
        logger.setLevel(logging.DEBUG)
        logger.addHandler(handler)
        logger.info(f"setting:{self.settings}")
        self.custom_spread_ratio = 0.8
        self.no_trade_his = []
        
    
    def cal_price_bps(self, x):
        return x * 10000 / self.price
        
    def dump_log(self, data) -> None:
        if self.settings['logging_level'] > 0:
            with open(self.log_filepath, 'a') as f:
                f.write(json.dumps(
                    {**{'log_timestamp': timestamp_2_date(self.cc.milliseconds())}, **data}) + '\n')
        else:
            if data['log_type'] == 'error' or 'error' in data['data']:
                with open(self.log_filepath, 'a') as f:
                    f.write(json.dumps(
                        {**{'log_timestamp': timestamp_2_date(self.cc.milliseconds())}, **data}) + '\n')

    async def update_open_orders(self) -> None:
        if self.ts_locked['update_open_orders'] > self.ts_released['update_open_orders']:
            return
        try:
            open_orders = await self.fetch_open_orders()
            self.highest_bid, self.lowest_ask = 0.0, 9.9e9
            for o in open_orders:
                if o['side'] == 'buy':
                    self.highest_bid = max(self.highest_bid, o['price'])
                elif o['side'] == 'sell':
                    self.lowest_ask = min(self.lowest_ask, o['price'])
            if self.open_orders != open_orders:
                self.dump_log({'log_type': 'open_orders', 'data': open_orders})
            self.open_orders = open_orders
            self.ts_released['update_open_orders'] = time()
        except Exception as e:
            print('error with update open orders', e)
            logger.error(f"update_open_orders error:{e} trace:{traceback.format_exc()}")

    async def update_position(self) -> None:
        # also updates open orders
        if self.ts_locked['update_position'] > self.ts_released['update_position']:
            return
        self.ts_locked['update_position'] = time()
        try:
            position, _ = await asyncio.gather(self.fetch_position(),
                                               self.update_open_orders())
            if self.position != position:
                self.dump_log({'log_type': 'position', 'data': position})
            self.position = position
            self.ts_released['update_position'] = time()
        except Exception as e:
            print('error with update position', e)
            logger.error(f"update_position error:{e} trace:{traceback.format_exc()}")

    async def create_orders(self, orders_to_create: [dict]) -> dict:
        if self.ts_locked['create_orders'] > self.ts_released['create_orders']:
            return
        self.ts_locked['create_orders'] = time()
        creations = []
        for oc in sorted(orders_to_create, key=lambda x: x['qty']):
            try:
                creations.append(
                    (oc, asyncio.create_task(self.execute_order(oc))))
            except Exception as e:
                print_(['error creating order a', oc, e], n=True)
        created_orders = []
        for oc, c in creations:
            try:
                o = await c
                created_orders.append(o)
                print_([' created order', o['symbol'],
                       o['side'], o['qty'], o['price']], n=True)
                self.dump_log({'log_type': 'create_order', 'data': o})
            except Exception as e:
                print_(['error creating order b', oc, c.exception(), e], n=True)
                self.dump_log({'log_type': 'create_order', 'data': {'result': str(c.exception()),
                               'error': repr(e), 'data': oc}})
                logger.error(f"create_orders error:{e} trace:{traceback.format_exc()}")
        self.ts_released['create_orders'] = time()
        return created_orders

    async def cancel_orders(self, orders_to_cancel: [dict]) -> [dict]:
        if self.ts_locked['cancel_orders'] > self.ts_released['cancel_orders']:
            return
        self.ts_locked['cancel_orders'] = time()
        deletions = []
        logger.debug(f"cancel_order:{orders_to_cancel}")
        for oc in orders_to_cancel:
            try:
                deletions.append((oc,
                                  asyncio.create_task(self.execute_cancellation(oc['order_id']))))
            except Exception as e:
                print_(['error cancelling order', oc, e])
        canceled_orders = []
        for oc, c in deletions:
            try:
                o = await c
                canceled_orders.append(o)
                print_(['cancelled order', o['symbol'],
                       o['side'], o['qty'], o['price']], n=True)
                self.dump_log({'log_type': 'cancel_order', 'data': o})
            except Exception as e:
                print_(['error cancelling order', oc, c.exception(), e], n=True)
                self.dump_log({'log_type': 'cancel_order', 'data': {'result': str(c.exception()),
                               'error': repr(e), 'data': oc}})
                logger.error(f"cancel_orders error:{e} trace:{traceback.format_exc()}")
        self.ts_released['cancel_orders'] = time()
        return canceled_orders

    def stop(self) -> None:
        self.stop_websocket = True

    def as_bid_ask(self):
        # 使用 IntensityInfo 计算 bid_price 和 ask_price
        def time_punish_as(time):
            if time < 300:
                return -1
            elif time>=300 and time < 900:
                return -2
            elif time>=900 and time < 1500:
                return -3
            elif time>=1500 and time < 2400:
                return -4
            elif time>=2400 and time < 3600:
                return -5
            else: 
                return -6
            
        self.intensty_info = IntensityInfo(self.buy_ak[0], self.buy_ak[1], self.sell_ak[0], self.sell_ak[1])
        time_punish = int(time_punish_as(self.no_trade_time))

        bid_spread = 0.0
        ask_spread = 0.0
        no_trade_punish = no_trade_punishment(self.no_trade_time)
        if self.sell_ak[0] * self.sell_ak[1] != 0:
            filtered_ask_intensity = self.sell_est.intensity_estimates[self.sell_est.intensity_estimates > 1e-5]
            if len(filtered_ask_intensity) and filtered_ask_intensity[-1] * self.sell_ak[0] > 0: 
                fix_time_punish = time_punish
                if self.sell_est.intensity_estimates[2] == 0.0:
                    fix_time_punish = -1
                ask_spread = self.intensty_info.get_sell_spread(filtered_ask_intensity[fix_time_punish])
                ask_spread = ask_spread * no_trade_punish * self.custom_spread_ratio
                self.as_ask_price = round_up(self.dom_ask[0, 0] + ask_spread, self.price_step)
        if  self.buy_ak[0] * self.buy_ak[1] != 0:
            filtered_bid_intensity = self.buy_est.intensity_estimates[self.buy_est.intensity_estimates > 1e-5]
            if len(filtered_bid_intensity) and filtered_bid_intensity[-1] * self.buy_ak[0] > 0:
                fix_time_punish = time_punish
                if self.buy_est.intensity_estimates[2] == 0.0:
                    fix_time_punish = -1
                bid_spread = self.intensty_info.get_buy_spread(filtered_bid_intensity[fix_time_punish])
                bid_spread = bid_spread * no_trade_punish * self.custom_spread_ratio
                self.as_bid_price = round_dn(self.dom_bid[0, 0] - bid_spread, self.price_step)
        logger.debug(f"as_bid_ask no_trade_punish:{no_trade_punish:.2f} "
                     f"bid_sp:{self.cal_price_bps(bid_spread):.2f} "
                     f"ask_sp:{self.cal_price_bps(ask_spread):.2f} "
                     f"as_bp:{self.as_bid_price} as_ap:{self.as_ask_price} "
                     f"buy_ak:{self.buy_ak} sell_ak:{self.sell_ak}")
        return self.as_bid_price, self.as_ask_price
    
    def calc_initial_bid_ask(self):
        
        # 老方案
        # Using more factor to adjust the price of maker
        # 中期 去修正 ema_spread 15m * 16
        # 短期 微调挂单间距 获得较优成交价格 1m * 10
        std_ratio  = max(np.log(1.71828 +self.short_std/self.long_std), 0.999) 
        atr_fix = 1.0
        if std_ratio > 1.0:
            atr_fix = self.last_atr_fix * 0.6 + std_ratio * 0.4
        else:
            atr_fix = self.last_atr_fix * 0.9 + std_ratio * 0.1 # 防爆拉
        std_ = self.std_dev * self.std_width * max(1.0, atr_fix)
        self.last_atr_fix = atr_fix
        ema_spread = self.ema_spread
        short_vol = self.get_short_term_vol()
        if self.mid_period_vol is not None:
            ema_spread = 0.6 * self.ema_spread + 0.2 * self.mid_period_vol['natr'] + 0.02 * self.mid_period_vol['ewma_vol'] + 5 * short_vol
            # 后面的是修正系数 前面的是加权平均
        
        no_trade_punish = no_trade_punishment(self.no_trade_time)
        # agg_100  = self.trade_agg.tail(100)
        # time_100ticks = (agg_100['timestamp'].max() - agg_100['timestamp'].min()) / 1000
        
        ema_spread *= no_trade_punish
        ema_spread *= self.custom_spread_ratio
        fair_p = self.indicators['tick_ema']
        fair_p = self.mid_price + self.ema_shift_multi * (self.mid_price - self.indicators['tick_ema'])
        
        if self.indicator_settings['do_long'] and \
                (not self.indicator_settings['funding_fee_collect_mode'] or
                 self.position['predicted_funding_rate'] < 0.0):
            bid_price = min(self.ob[0],
                            round_dn(fair_p * (1 - ema_spread * atr_fix),
                                     self.price_step))
            if self.first_imbalance != -1.0:
                if self.price < self.indicators['tick_ema']:
                    if self.trade_imbalance < -self.first_imbalance and self.trade_imbalance > -0.3333:
                        bid_price = min(bid_price, round_dn(
                            fair_p - std_ * self.num_std, self.price_step))
                    elif self.trade_imbalance > -0.5 and self.trade_imbalance <= -0.3333:
                        bid_price = min(bid_price, round_dn(
                            fair_p - std_* (self.num_std + self.std_spacing), self.price_step))
                    elif self.trade_imbalance <= -0.5:
                        bid_price = min(bid_price, round_dn(
                            fair_p - std_*(self.num_std + 2.5 * self.std_spacing), self.price_step))
        else:
            bid_price = -1.0

        if self.indicator_settings['do_shrt'] and \
                (not self.indicator_settings['funding_fee_collect_mode'] or
                 self.position['predicted_funding_rate'] > 0.0):
            ask_price = max(self.ob[1],
                            round_up(fair_p * (1 + ema_spread * atr_fix),
                                     self.price_step))
            if self.first_imbalance != -1.0:
                if self.price > self.indicators['tick_ema']:
                    if self.trade_imbalance > self.first_imbalance and self.trade_imbalance < 0.3333:
                        ask_price = max(ask_price, round_up(
                            fair_p + self.num_std * std_, self.price_step))
                    elif self.trade_imbalance >= 0.3333 and self.trade_imbalance < 0.5:
                        ask_price = max(ask_price, round_up(
                            fair_p + std_*(self.num_std + self.std_spacing), self.price_step))
                    elif self.trade_imbalance >= 0.5:
                        ask_price = max(ask_price, round_up(
                            fair_p + std_*(self.num_std + 2.5 * self.std_spacing), self.price_step))
        else:
            ask_price = -1.0
        logger.debug(
            f"calc_initial_bid_ask mid_p:{self.mid_price:.6f} "
            f"ema_spread:{convert2bps(ema_spread):.2f} "
            f"ema_spread_raw:{convert2bps(self.ema_spread):.2f} "
            f"natr:{convert2bps(self.mid_period_vol['natr']):.2f} "
            f"ewma_vol:{convert2bps(self.mid_period_vol['ewma_vol']):.2f} "
            f"std_ratio:{std_ratio:.4f} "
            f"std_dev:{self.cal_price_bps(self.std_dev):.4f} "
            f"atr_fix:{atr_fix:.4f} "
            f"tick_ema:{self.indicators['tick_ema']:.6f} "
            f"tick_ema_bps:{self.cal_price_bps(self.indicators['tick_ema'] - self.mid_price):.2f} "
            f"imb:{self.trade_imbalance:.3f} "
            f"bid:{self.cal_price_bps(self.mid_price - bid_price):.2f} "
            f"ask:{self.cal_price_bps(ask_price - self.mid_price):.2f} "
            f"no_trade_punish:{no_trade_punish} no_trade_time:{self.no_trade_time} "
            f"short_vol:{convert2bps(short_vol):.2f}"
        )
        return bid_price, ask_price

    def calc_orders(self):
        last_price_diff_limit = 0.15
        balance = self.position['wallet_balance'] * \
            min(1.0, abs(self.balance_pct))
        
        orders = []

        if self.reverse_trade and self.position['size']!=0:
            unr_profit = unrealize_profit(self.price, self.position['price'], self.position['size'])
            pct_uprofit =  unr_profit / self.position['wallet_balance']
            if pct_uprofit < -self.max_sl_pct or time() - self.timestamp_sl > 3600 * self.sl_cd_by_hour -10:
                orders += self.ret_sl_morder(self.position['size'])
                self.reverse_trailing_price = -1.0
                self.reverse_trade = False ##end reverse trade
            elif  pct_uprofit >= 0.2 *self.max_sl_pct:  
                if  self.position['size'] > 0:
                    self.reverse_trailing_price = max(round_up((pct_uprofit/self.max_sl_pct- 0.12) *self.max_sl_pct 
                        * self.position['wallet_balance']/ self.position['size'] + self.position['price'], 
                            self.price_step), self.reverse_trailing_price)
                    orders.append({'side': 'sell', 'qty': abs(self.position['size']),
                               'price': self.reverse_trailing_price, 'type': 'limit', 'reduce_only': True,
                               'custom_id': 'close'})
                else:
                    if self.reverse_trailing_price < 0:
                       self.reverse_trailing_price = self.position['price'] 
                    self.reverse_trailing_price = min(round_dn((pct_uprofit/self.max_sl_pct- 0.12) *self.max_sl_pct
                            * self.position['wallet_balance']/ self.position['size'] + self.position['price'], 
                            self.price_step), self.reverse_trailing_price)
                    orders.append({'side': 'buy', 'qty': abs(self.position['size']),
                               'price': self.reverse_trailing_price, 'type': 'limit', 'reduce_only': True,
                               'custom_id': 'close'})
            self.extreme_situation = False
            return orders
        elif self.reverse_trade and self.position['size']==0:
            self.reverse_trade = False
            self.reverse_trailing_price = -1.0
            self.extreme_situation = False   
            return orders
    
        temp_vbar = None                
        if self.position['size'] != 0:
            self.extreme_situation = self.sl_filter() 
            temp_vbar = self.volume_bars_sys.get_bars()
            temp_vbar['cdf'] = temp_vbar['vpin'].rank(pct=True)
        bar_len = self.volume_bars_sys.get_bars_length()

        if self.extreme_situation:
            if temp_vbar['cdf'].iloc[-1] > 0.91 and bar_len > 80 and 'n_reverse_qty' in self.settings \
                 and self.trade_imbalance * self.position['size'] < 0:
                orders += self.ret_sl_morder(round_up(self.position['size']*self.settings['n_reverse_qty'], self.qty_step), False)
                self.reverse_trade = True
                logger.info(f"reverse {temp_vbar['cdf'].iloc[-1]:.4f} orders:{orders}")
                print("reverse mode on")
            else:
                orders += self.ret_sl_morder(self.position['size']) # in future, we will add reverse order to get more profit 
                                                               # orders += self.reverse_order(self.position['size'])
                logger.info(f"not reverse cdf:{temp_vbar['cdf'].iloc[-1]:.4f} bar_len:{bar_len} orders:{orders}")
            self.timestamp_sl = time()                          # this line may changed after we start reverse order
            self.dump_log({'log_type': 'error', 'data': {'result': 'stop loss in self.calc_orders()',
                               }}) 
            return orders
        
        diff_liq = calc_diff(self.position['liquidation_price'], self.price)
        diff_price = calc_diff(self.position['price'], self.price)
        if diff_liq < self.stop_loss_liq_diff or diff_price > self.stop_loss_pos_price_diff:  
            abs_pos_size = abs(self.position['size'])         
            stop_loss_qty = min(abs_pos_size, round_up(abs_pos_size * self.stop_loss_pos_reduction,
                                                       self.qty_step))
            
            if stop_loss_qty > 0.0:
                logger.info(f"market_order_stop_loss diff_liq:{diff_liq:.4f} diff_price:{diff_price:.4f} " 
                             f"pos:{abs_pos_size:.6f} stop_loss_qty:{stop_loss_qty:.6f}")
            if stop_loss_qty > 0.0:
                if self.position['size'] > 0.0:
                    # controlled long loss
                    orders.append(
                        {'side': 'sell', 'type': 'market' if self.market_stop_loss else 'limit',
                         'qty': stop_loss_qty,
                         'price': self.ob[1], 'reduce_only': True, 'custom_id': 'stop_loss'}
                    )
                else:
                    # controlled shrt loss
                    orders.append(
                        {'side': 'buy', 'type': 'market' if self.market_stop_loss else 'limit',
                         'qty': stop_loss_qty,
                         'price': self.ob[0], 'reduce_only': True, 'custom_id': 'stop_loss'}
                    )
        else:
            stop_loss_qty = 0.0
        if self.position['size'] == 0:  # no pos
            bid_price, ask_price = self.calc_initial_bid_ask()
            bid_price_as, ask_price_as = self.as_bid_ask()
            bid_v = 0.0
            ask_v = 0.0
            if bid_price_as != 0:
                bid_price = round_dn((bid_price_as * 0.2 + bid_price * 0.8) , self.price_step)
            if ask_price_as !=9e8:
                ask_price = round_up((ask_price_as *0.2 + ask_price * 0.8),self.price_step)
            if bid_price > 0.0:
                bid_v = self.calc_min_entry_qty(balance, bid_price)
                orders.append({'side': 'buy', 'qty': bid_v,
                               'price': bid_price,
                               'type': 'limit', 'reduce_only': False, 'custom_id': 'entry'})
            if ask_price > 0.0:
                ask_v = self.calc_min_entry_qty(balance, ask_price)
                orders.append({'side': 'sell', 'qty': ask_v,
                               'price': ask_price, 'type': 'limit', 'reduce_only': False,
                               'custom_id': 'entry'})
            logger.debug(f"cal_price mid_p:{self.mid_price:.6f} bid_price_as:{bid_price_as:.6f} ask_price_as:{ask_price_as:.6f} bid_price:{bid_price} ask_price:{ask_price} "
                              f"a_sp:{self.cal_price_bps(ask_price - self.mid_price):.2f} b_sp:{self.cal_price_bps(self.mid_price - bid_price):.2f}")
            logger.debug(f"open_order bid:{bid_price:.6f}@{bid_v:.6f} ask:{ask_price:.6f}@{ask_v:.6f} balance:{balance:.6f}")
        elif self.position['size'] > 0.0:  # long pos
            pos_size = self.position['size']
            pos_price = self.position['price']
            pos_margin = self.calc_margin_cost(pos_size, pos_price)
            bid_price = min(self.ob[0], calc_long_reentry_price(self.price_step,
                                                                self.grid_spacing,
                                                                self.grid_coefficient,
                                                                balance,
                                                                pos_margin,
                                                                pos_price))
            modified_grid_spacing = self.grid_spacing * (1 + pos_margin / balance * self.grid_coefficient)
            space_round = round_up(pos_price * self.grid_spacing / 4, self.price_step)
            round_p = round_dn(pos_price * (1 - modified_grid_spacing), space_round)
            logger.debug(f"open_entry_order pos_margin:{pos_margin:.6f} balance:{balance:.6f} "
                              f"modified_grid_spacing:{modified_grid_spacing:.6f} pos_price:{pos_price:.6f} "
                              f"space_round:{space_round} round_p:{round_p}")
            for k in range(self.n_entry_orders):
                max_pos_size = self.calc_max_pos_size(min(balance, self.position['equity']),
                                                      bid_price)
                min_qty_ = self.calc_min_qty(bid_price)
                bid_qty = calc_reentry_qty(self.qty_step, self.ddown_factor,
                                           min_qty_, max_pos_size, pos_size)
                if bid_qty < min_qty_ and k > 0:
                    break
                new_pos_size = pos_size + bid_qty
                if new_pos_size >= max_pos_size and k > 0:
                    break
                pos_price = pos_price * (bid_qty / new_pos_size) + \
                    bid_price * (pos_size / new_pos_size)
                pos_size = new_pos_size
                pos_margin = self.calc_margin_cost(pos_size, pos_price)
                logger.debug(f"has_long_pos pos:{self.position['size']:.6f} k:{k} "
                                  f"bid:{bid_price:.6f}@{bid_qty:.6f} max_pos_size:{max_pos_size} "
                                  f"diff:{calc_diff(bid_price, self.position['price']):.4f}")
                if calc_diff(bid_price, self.price) > last_price_diff_limit and k > 0:
                    break
                orders.append({'side': 'buy', 'qty': bid_qty, 'price': bid_price,
                               'type': 'limit', 'reduce_only': False, 'custom_id': 'entry'})
                
                bid_price = min(self.ob[0], calc_long_reentry_price(self.price_step,
                                                                    self.grid_spacing,
                                                                    self.grid_coefficient,
                                                                    balance,
                                                                    pos_margin,
                                                                    pos_price))
            min_close_qty = calc_min_close_qty(self.qty_step,
                                   self.min_qty,
                                   self.min_close_qty_multiplier,
                                   self.calc_min_entry_qty(balance, self.position['price']))
            ask_qtys, ask_prices = calc_long_closes(
                self.price_step,
                self.qty_step,
                min_close_qty,
                self.min_markup,
                self.max_markup,
                self.position['size'] - stop_loss_qty,
                self.position['price'],
                self.ob[1],
                self.n_close_orders
            )
            logger.debug(f"close_long ask_prices:{ask_prices} ask_qtys:{ask_qtys} min_close_qty:{min_close_qty:.6f}")
            close_orders = sorted([{'side': 'sell', 'qty': abs_qty, 'price': float(price_),
                                    'type': 'limit', 'reduce_only': True, 'custom_id': 'close'}
                                   for qty_, price_ in zip(ask_qtys, ask_prices)
                                   if (abs_qty := abs(float(qty_))) > 0.0
                                   and calc_diff(price_, self.price) < last_price_diff_limit],
                                  key=lambda x: x['price'])[:self.n_entry_orders]
            logger.debug(f"close_long:{close_orders}")
            orders += close_orders
        else:  # shrt pos
            pos_size = self.position['size']
            pos_price = self.position['price']
            pos_margin = self.calc_margin_cost(-pos_size, pos_price)
            modified_grid_spacing = self.grid_spacing * (1 + pos_margin / balance * self.grid_coefficient)
            space_round = round_up(pos_price * self.grid_spacing / 4, self.price_step)
            round_p = round_up(pos_price * (1 + modified_grid_spacing), space_round)
            ask_price = max(self.ob[1], calc_shrt_reentry_price(self.price_step,
                                                                self.grid_spacing,
                                                                self.grid_coefficient,
                                                                balance,
                                                                pos_margin,
                                                                pos_price))
            
            logger.debug(f"open_entry_order pos_margin:{pos_margin:.6f} balance:{balance:.6f} "
                              f"modified_grid_spacing:{modified_grid_spacing:.6f} pos_price:{pos_price:.6f} "
                              f"space_round:{space_round} round_p:{round_p}")
            for k in range(self.n_entry_orders):
                max_pos_size = self.calc_max_pos_size(min(balance, self.position['equity']),
                                                      ask_price)
                min_qty_ = self.calc_min_qty(ask_price)
                ask_qty = calc_reentry_qty(self.qty_step, self.ddown_factor,
                                           min_qty_, max_pos_size, pos_size)
                if ask_qty < min_qty_ and k > 0:
                    break
                new_pos_size = pos_size - ask_qty
                if abs(new_pos_size) >= max_pos_size and k > 0:
                    break
                pos_price = pos_price * (-ask_qty / new_pos_size) + \
                    ask_price * (pos_size / new_pos_size)
                pos_size = new_pos_size
                pos_margin = self.calc_margin_cost(-pos_size, pos_price)
                logger.debug(f"has_short_pos pos:{self.position['size']:.6f} k:{k} "
                                  f"bid:{ask_price:.6f}@{ask_qty:.6f} max_pos_size:{max_pos_size} "
                                  f"diff:{calc_diff(ask_price, self.position['price']):.4f}")
                if calc_diff(ask_price, self.price) > last_price_diff_limit and k > 0:
                    break
                orders.append({'side': 'sell', 'qty': ask_qty, 'price': ask_price,
                               'type': 'limit', 'reduce_only': False, 'custom_id': 'entry'})
                ask_price = max(self.ob[1], calc_shrt_reentry_price(self.price_step,
                                                                    self.grid_spacing,
                                                                    self.grid_coefficient,
                                                                    balance,
                                                                    pos_margin,
                                                                    pos_price))
            min_close_qty = calc_min_close_qty(self.qty_step,
                                               self.min_qty,
                                               self.min_close_qty_multiplier,
                                               self.calc_min_entry_qty(balance, self.position['price']))
            bid_qtys, bid_prices = calc_shrt_closes(
                self.price_step,
                self.qty_step,
                min_close_qty,
                self.min_markup,
                self.max_markup,
                self.position['size'] + stop_loss_qty,
                self.position['price'],
                self.ob[0],
                self.n_close_orders
            )
            logger.debug(f"close_short bid_prices:{bid_prices} bid_qtys:{bid_qtys} min_close_qty:{min_close_qty:.6f}")
            close_orders = sorted([{'side': 'buy', 'qty': float(qty_), 'price': float(price_),
                                    'type': 'limit', 'reduce_only': True, 'custom_id': 'close'}
                                   for qty_, price_ in zip(bid_qtys, bid_prices) if qty_ > 0.0],
                                  key=lambda x: x['price'], reverse=True)[:self.n_entry_orders]
            logger.debug(f"close_short:{close_orders}")
            orders += close_orders

        return orders
    
    async def cancel_all_order(self):
        await asyncio.sleep(0.1)
        await self.update_open_orders()
        await asyncio.sleep(0.1)
        tasks = []
        tasks.append(self.cancel_orders(self.open_orders))
        results = await asyncio.gather(*tasks)
        await asyncio.sleep(0.01)
        if any(results):
            print()
        return results
    
    async def cancel_and_create(self):
        await asyncio.sleep(0.01)
        await self.update_position()
        await asyncio.sleep(0.01)
        if any([self.ts_locked[k_] > self.ts_released[k_]
                for k_ in [x for x in self.ts_locked if x != 'decide' and x!='decide_lock']]):
            return
        n_orders_limit = 8
        to_cancel, to_create = filter_orders(self.open_orders,
                                             self.calc_orders(),
                                             keys=['side', 'qty', 'price'])
        to_cancel = sorted(
            to_cancel, key=lambda x: calc_diff(x['price'], self.price))
        to_create = sorted(
            to_create, key=lambda x: calc_diff(x['price'], self.price))
        tasks = []
        if to_cancel:
            tasks.append(self.cancel_orders(to_cancel[:n_orders_limit]))
        tasks.append(self.create_orders(to_create[:n_orders_limit]))
        results = await asyncio.gather(*tasks)
        await asyncio.sleep(0.01)
        await self.update_position()
        if any(results):
            print()
        return results

    def ret_sl_morder(self,size, reduce_only = True):
        market_order = []
        offset = 0.0 if not reduce_only else 2 * self.price_step
        if size > 0: 
            market_order.append({'side': 'sell','qty': abs(size), 'price': 
                                    self.ob[1] - offset, 'type': 'market', 
                                'reduce_only': reduce_only, 'custom_id': 'stop_loss'})
        else:
            market_order.append({'side': 'buy','qty': abs(size), 'price': 
                                    self.ob[0] + offset, 'type': 'market', 
                                'reduce_only':reduce_only, 'custom_id': 'stop_loss'})
        return market_order
    async def force_close(self):
        unclosed_profit = unrealize_profit(self.price, self.position['price'], self.position['size'])
        api_key, api_secret = load_key_secret(self.exchange, self.user)
        if self.exchange == 'binance':
            from utils.force_close import close_all_positions, cancel_all_open_orders
            self.timestamp_sl = time()
            _, self.position['size'] = close_all_positions(api_key, api_secret)

            if  self.position['size'] == 0.0 and cancel_all_open_orders(api_key, api_secret):
                return 'force stop loss successed', unclosed_profit
            else:
                return 'force stop loss failed', unclosed_profit             
    
    async def check_status(self, update_online = False):
        await asyncio.sleep(0.01)
        if update_online:
            await self.update_position()
            await asyncio.sleep(0.01)
        unclosed_profit = unrealize_profit(self.price, self.position['price'], self.position['size'])
        if unclosed_profit / self.position['wallet_balance'] < -self.max_sl_pct:
            return await self.force_close()
        if self.highest_bid == 0.0 or self.lowest_ask == 9.9e9:
            return 'exit or ddown issue', unclosed_profit
        return 'no problem', unclosed_profit
    
    async def decide(self):
        t0 = time()
        
        if time() - self.timestamp_sl < 3600 * self.sl_cd_by_hour and not self.reverse_trade:
            line = f"We are suffering extreme situation, Pause trading {1.0:.1f} hour. "
            if self.reverse_trade:
                line += f"{3600 * self.sl_cd_by_hour - time() + self.timestamp_sl:.0f} seconds left to restart to trade, but we have reverse trade to minimize loss"
            else:
                line += f"{3600 * self.sl_cd_by_hour - time() + self.timestamp_sl:.0f} seconds left to restart to trade"
            print_([line],r=True)
            if time() - self.timestamp_sl > 3600 * self.sl_cd_by_hour - 10 and self.extreme_situation:
                self.extreme_situation = False  # end self.extreme_situation
            self.ts_released['decide_lock'] = time()
            return 
        
        if time() - self.timestamp_midp_market_check > 3600 * 0.2:
            self.timestamp_midp_market_check = time()
            await asyncio.sleep(0.01)
            df  = await market_stats(self.cc, '15m', self.symbol, 4) # here must >= 4
            await asyncio.sleep(0.01)
            self.mid_period_vol = df.mean(numeric_only=True)
            logger.debug(f"update_market_stats {self.mid_period_vol}") 

        if (self.sl_filter() or self.reverse_trade) and time() - self.ts_locked['decide'] > 1.5:
            logger.debug(f"may_stop_loss_or_reverse ts_locked:{self.ts_locked} ts_released:{self.ts_released}")
            self.ts_locked['decide'] = time()
            await self.cancel_and_create() ## sl in cancel_and_create()
            self.ts_released['decide'] = time()
            self.ts_released['decide_lock'] = time()
            t1 = time()
            logger.debug(f"decide lact:{int(t1 * 1000) - int(t0 * 1000)}")
            return
        
        if self.price <= self.highest_bid:
            logger.debug(f"bid_may_take ts_locked:{self.ts_locked} ts_released:{self.ts_released}")
            self.ts_locked['decide'] = time()
            print_(['bid maybe taken'], n=True)
            await self.cancel_and_create()
            self.ts_released['decide'] = time()
            self.ts_released['decide_lock'] = time()
            t1 = time()
            logger.debug(f"decide lact:{int(t1 * 1000) - int(t0 * 1000)}")
            return
        if self.price >= self.lowest_ask:
            logger.debug(f"ask_may_take ts_locked:{self.ts_locked} ts_released:{self.ts_released}")
            self.ts_locked['decide'] = time()
            print_(['ask maybe taken'], n=True)
            await self.cancel_and_create()
            self.ts_released['decide'] = time()
            self.ts_released['decide_lock'] = time()
            t1 = time()
            logger.debug(f"decide lact:{int(t1 * 1000) - int(t0 * 1000)}")
            return
        
        if time() - self.ts_locked['decide'] > 1.8:
            logger.debug(f"ts_need_decide ts_locked:{self.ts_locked} ts_released:{self.ts_released}")
            self.ts_locked['decide'] = time()
            await self.cancel_and_create()
            self.ts_released['decide'] = time()
            self.ts_released['decide_lock'] = time()
            t1 = time()
            logger.debug(f"decide lact:{int(t1 * 1000) - int(t0 * 1000)}")
            return

        if time() - self.ts_released['print'] >= 0.9999:
            unprofit = 0.0
            if self.position['size'] != 0:
                unprofit= unrealize_profit(self.price, self.position['price'], self.position['size'])
                # status_code, unprofit = await self.check_status(update_online=False)
                # if status_code != 'no problem':
                #     self.dump_log({'log_type': 'error', 'data': {'result': status_code}} )  
                #     if status_code.startswith('force stop loss'):
                #         line = f"We are suffering stop loss"     
            self.ts_released['print'] = time()
            line = f"{self.symbol} "
            if self.position['size'] == 0:
                self.no_trade_time += 1.0
                line += f"no position bid {self.highest_bid} ask {self.lowest_ask} "
                ratio = (self.price - self.highest_bid) / \
                    (self.lowest_ask - self.highest_bid)
            elif self.position['size'] > 0.0:
                if self.no_trade_time!=0.0:
                  self.no_trade_his.append(no_trade_punishment(self.no_trade_time))
                  logger.debug(f"long_pos no_trade_his:{self.no_trade_his}")
                self.no_trade_time = 0.0
                line += f"long {self.position['size']} "
                line += f"@ {round_(self.position['price'], self.price_step)} "
                line += f"exit {self.lowest_ask} ddown {self.highest_bid} "
                ratio = (self.price - self.highest_bid) / \
                    (self.lowest_ask - self.highest_bid)
            else:
                if self.no_trade_time!=0.0:
                    self.no_trade_his.append(no_trade_punishment(self.no_trade_time))
                    logger.debug(f"short_pos no_trade_his:{self.no_trade_his}")
                self.no_trade_time = 0.0
                line += f"shrt {self.position['size']} "
                line += f"@ {round_(self.position['price'], self.price_step)} "
                ratio = 1 - (self.price - self.highest_bid) / \
                    (self.lowest_ask - self.highest_bid)
                line += f"exit {self.highest_bid} ddown {self.lowest_ask } "
            line += f" ema price {self.indicators['tick_ema']:.4f} "
            line += f" as ask price {self.as_ask_price:.4f} "
            line += f" as bid price {self.as_bid_price:.4f} "
            line += f" unrealized profit: {unprofit:.4f} "
            # liq_diff = calc_diff(
            #     self.position['liquidation_price'], self.price)
            # line += f"pct {ratio:.2f} liq_diff {liq_diff:.3f} last {self.price}   "
            print_([line], r=True)

        self.ts_released['decide_lock'] = time()

    def init_tick_ema(self, ticks: [dict]):
        print_(['initiating tick ema...'])
        ema_span = self.indicator_settings['tick_ema']['span']
        ema = ticks[0]['price']
        alpha = 2 / (ema_span + 1)
        for t in ticks:
            ema = ema * (1 - alpha) + t['price'] * alpha
        self.indicators['tick_ema'] = ema
        self.indicator_settings['tick_ema']['alpha'] = alpha
        self.indicator_settings['tick_ema']['alpha_'] = 1 - alpha
    
    async def init_volume_bar(self, ticks):
        print_((['initiating volume bar param']))
        ohlcv = await self.cc.fetch_ohlcv(self.symbol, '1h', limit=8)     
        bar = pd.DataFrame(ohlcv, columns=['timestamp', 'open', 'high', 'low', 'close', 'volume'])
        # self.vol_bar_threadhold = max(round(bar['volume'].sum()*6 / self.settings['volbars_in_day']), 20000)
        self.vol_bar_threadhold = round(bar['volume'].sum()*3 / self.settings['volbars_in_day'])
        self.volume_bars_sys = volume_bar_system(volume_threshold=self.vol_bar_threadhold, 
                                                 max_bars=250)
        logger.info(f"init_volume_bar vol_bar_threadhold:{self.vol_bar_threadhold} sum:{bar['volume'].sum():.6f}")
        for trade in ticks:
            self.volume_bars_sys.process_trade(trade)
            
    def update_volume_bar(self, websocket_tick):
        self.volume_bars_sys.process_trade(websocket_tick)

    def update_tick_ema(self, websocket_tick):
        self.indicators['tick_ema'] = \
            self.indicators['tick_ema'] * self.indicator_settings['tick_ema']['alpha_'] + \
            websocket_tick['price'] * \
            self.indicator_settings['tick_ema']['alpha']

    def init_std_dev(self, ticks):
        self.latest_histotical_price = np.array(
            [tick['price'] for tick in ticks[-400:]])
        self.long_std= np.std(self.latest_histotical_price)
        self.std_dev = np.std(self.latest_histotical_price[-300:])
        self.short_std = np.std(self.latest_histotical_price[-80:])

    def update_std_dev(self, websocket_tick):
        self.latest_histotical_price = np.delete(
            self.latest_histotical_price, 0)
        self.latest_histotical_price = np.append(
            self.latest_histotical_price, websocket_tick['price'])
        self.long_std = np.std(self.latest_histotical_price)
        self.std_dev = np.std(self.latest_histotical_price[-300:])
        self.short_std = np.std(self.latest_histotical_price[-80:])
        
    def skew_order_book(self):
        bid_vol = self.dom_bid[:1]
        ask_vol = self.dom_ask[:1]
        bid_vol = self.dom_bid[:, 1][::-1]
        ask_vol = self.dom_ask[:, 1]
        dom = np.concatenate((bid_vol, ask_vol))
        skewness = skew(dom)
        return skewness

    def init_trade_qty_imbalance(self, ticks):
        print_(['computing tick imbalance ...'])
        last = ticks[300:]
        self.trade_agg = pd.DataFrame(last)
        total_buy_qty = self.trade_agg[self.trade_agg['is_buyer_maker'] == False]['qty'].sum()
        total_sell_qty = self.trade_agg[self.trade_agg['is_buyer_maker'] == True]['qty'].sum()
        self.delta_qty = total_buy_qty - total_sell_qty
        self.trade_imbalance = self.delta_qty / (total_buy_qty + total_sell_qty)

    def update_trade_qty_imbalance(self, websocket_tick):
        df = pd.DataFrame([websocket_tick])
        self.trade_agg = pd.concat([self.trade_agg, df], ignore_index=True)
        self.trade_agg = self.trade_agg.tail(300)
        total_buy_qty = self.trade_agg[self.trade_agg['is_buyer_maker'] == False]['qty'].sum()
        total_sell_qty = self.trade_agg[self.trade_agg['is_buyer_maker'] == True]['qty'].sum()
        self.delta_qty = total_buy_qty - total_sell_qty
        self.trade_imbalance = self.delta_qty /(total_buy_qty + total_sell_qty)

    def update_risk_monitor(self):
        x, xq = self.dom_bid[0,0], self.dom_bid[0,1]
        y, yq = self.dom_ask[0,0], self.dom_ask[0,1]
        spread: float = (y - x) / ((y + x) / 2)
        spread = floor(spread * 10**6) / 10**6 
        self.norm_spread = np.append(self.norm_spread, spread)
        sma = round(np.mean(list(self.norm_spread)), 5)
        self.spread_slop = round(self.norm_spreads_ma / sma, 5) if sma != 0 else 1 
        self.norm_spreads_ma = sma 
        self.spread_ma_quene = np.append(self.spread_ma_quene, sma)
        self.norm_spread = self.norm_spread[-500:]
        self.spread_ma_quene = self.spread_ma_quene[-500:]
        
    def init_fancy_indicator_001(self, ticks: [dict]):
        pass

    def update_fancy_indicator_001(self, websocket_tick: dict):
        pass

    def init_fancy_indicator_002(self, ticks: [dict]):
        pass

    def update_fancy_indicator_002(self, websocket_tick: dict):
        pass

    def init_tick_rsi(self):
        pass

    def update_tick_rsi(self):
        pass

    def init_ohlcv_rsi(self, ticks: [dict]):
        print_(['initiation ohlcv rsi'])
        self.indicator_settings['max_periods_in_memory'] = \
            self.indicator_settings['ohlcv_rsi']['n_periods']
        n_periods = self.indicator_settings['ohlcv_rsi']['n_periods']
        self.init_ohlcv(
            self.indicator_settings['ohlcv_rsi']['period_ms'], ticks)
        ohlcvs = self.ohlcvs[self.indicator_settings['ohlcv_rsi']['period_ms']]
        upchange_smoothed = 0.0
        dnchange_smoothed = 0.0
        for i in range(1, len(ohlcvs)):
            if ohlcvs[i]['close'] == ohlcvs[i - 1]['close']:
                upchange = 0
                dnchange = 0
            elif ohlcvs[i]['close'] > ohlcvs[i - 1]['close']:
                upchange = ohlcvs[i]['close'] - ohlcvs[i - 1]['close']
                dnchange = 0
            else:
                upchange = 0
                dnchange = ohlcvs[i - 1]['close'] - ohlcvs[i]['close']
            upchange_smoothed = (upchange_smoothed *
                                 (n_periods - 1) + upchange) / n_periods
            dnchange_smoothed = (dnchange_smoothed *
                                 (n_periods - 1) + dnchange) / n_periods
            self.indicator_settings['ohlcv_rsi']['upchange_smoothed'] = upchange_smoothed
            self.indicator_settings['ohlcv_rsi']['dnchange_smoothed'] = dnchange_smoothed
            rs = upchange_smoothed / dnchange_smoothed if dnchange_smoothed > 0.0 else 9e9
            rsi = 100 - 100 / (1 + rs)
        self.indicators['ohlcv_rsi'] = rsi

    def update_ohlcv_rsi(self, websocket_tick: dict):
        ohlcvs = self.ohlcvs[self.indicator_settings['ohlcv_rsi']['period_ms']]
        if self.update_ohlcv(self.indicator_settings['ohlcv_rsi']['period_ms'], websocket_tick):
            if ohlcvs[-1]['close'] == ohlcvs[-2]['close']:
                upchange = 0
                dnchange = 0
            elif ohlcvs[-1]['close'] > ohlcvs[-2]['close']:
                upchange = ohlcvs[i]['close'] - ohlcvs[-2]['close']
                dnchange = 0
            else:
                upchange = 0
                dnchange = ohlcvs[i - 1]['close'] - ohlcvs[i]['close']
            upchange_smoothed = (self.indicator_settings['ohlcv_rsi']['upchange_smoothed'] *
                                 (n_periods - 1) + upchange) / n_periods
            dnchange_smoothed = (self.indicator_settings['ohlcv_rsi']['dnchange_smoothed'] *
                                 (n_periods - 1) + dnchange) / n_periods
            rs = upchange_smoothed / dnchange_smoothed if dnchange_smoothed > 0.0 else 9e9
            rsi = 100 - 100 / (1 + rs)
            self.indicators['ohlcv_rsi'] = rsi
            self.indicator_settings['ohlcv_rsi']['upchange_smoothed'] = upchange_smoothed
            self.indicator_settings['ohlcv_rsi']['dnchange_smoothed'] = dnchange_smoothed

    async def fetch_ticks(self):
        condensed_ticks = []
        if self.exchange == "binance":
            n_ticks_to_fetch = int(self.indicator_settings['tick_ema']['span'])
            # each fetch contains 1000 ticks
            ticks = await self.fetch_trades()
            additional_ticks = await asyncio.gather(
                *[self.fetch_trades(from_id=ticks[0]['trade_id'] - 1000 * i)
                  for i in range(1, n_ticks_to_fetch // 1000)])
            ticks = sorted(ticks + flatten(additional_ticks),
                          key=lambda x: x['trade_id'])
            condensed_ticks = [ticks[0]]
            for i in range(1, len(ticks)):
                if ticks[i]['price'] != condensed_ticks[-1]['price']:
                    condensed_ticks.append(ticks[i])
        elif self.exchange == "bybit":
            condensed_ticks = await self.fetch_trades()
        return condensed_ticks

    async def init_factor(self):
        # called upon websocket start 
        ticks = await self.fetch_ticks()  
        await self.init_volume_bar(ticks)
        self.init_tick_ema(ticks)
        self.init_std_dev(ticks)
        self.init_trade_qty_imbalance(ticks)    
        # self.init_ohlcv_rsi(ticks)

    def update_immediate_factor(self, websocket_tick):
        # the factors need to be updated by every tick
        self.update_trade_qty_imbalance(websocket_tick)
        self.update_std_dev(websocket_tick)
        self.update_volume_bar(websocket_tick)
        self.update_risk_monitor()

    def update_factor(self, websocket_tick: dict):
        # called each websocket tick
        # {'price': float, 'qty': float, 'timestamp': int, 'side': 'buy'|'sell'}
        self.update_tick_ema(websocket_tick)
        # self.update_ohlcv_rsi(websocket_tick)

    def update_emp_ak(self, time_window): # 单位 ms
        buy_ak = self.buy_est.estimate_ak(self.dom_bid[0,2], self.dom_bid[0,2] - time_window)
        sell_ak = self.sell_est.estimate_ak(self.dom_ask[0,2], self.dom_ask[0,2] - time_window)
        if not np.any(np.isnan(buy_ak)): 
            self.buy_ak = buy_ak
        if not np.any(np.isnan(sell_ak)):
            self.sell_ak = sell_ak

    def update_tick_intensty(self, time_window):
        mid_price = (self.dom_bid[0,0] + self.dom_ask[0,0]) / 2
        self.sell_est.on_tick(mid_price, self.dom_ask[0,0], self.dom_ask[0,2],self.dom_ask[0,2] - time_window)
        self.buy_est.on_tick(mid_price, self.dom_bid[0,0], self.dom_bid[0,2], self.dom_bid[0,2] - time_window)  

    def update_short_term_vol(self, price, ts):
        self.short_term_vol.feed_md(price, ts)
    
    def get_short_term_vol(self):
        return self.short_term_vol.calculate() / self.mid_price
        
    def init_ohlcv(self, period_ms: int, ticks: [dict]):
        print_([f'initiating ohlcvs {period_ms}...'])
        self.ohlcvs[period_ms] = [{
            'timestamp': ticks[0]['timestamp'] - ticks[0]['timestamp'] % 10000,
            'open': ticks[0]['price'],
            'high': ticks[0]['price'],
            'low': ticks[0]['price'],
            'close': ticks[0]['price'],
            'volume': ticks[0]['qty']
        }]
        for t in ticks[1:]:
            self.update_ohlcv(period_ms, t)

    def update_ohlcv(self, period_ms, websocket_tick) -> bool:
        if websocket_tick['timestamp'] > round(self.ohlcvs[period_ms][-1]['timestamp'] + period_ms):
            new_ohlcv = True
            while websocket_tick['timestamp'] > \
                    round(self.ohlcvs[period_ms][-1]['timestamp'] + period_ms * 2):
                # fill empty ohlcvs
                self.ohlcvs[period_ms].append({
                    'timestamp': int(round(self.ohlcvs[period_ms][-1]['timestamp'] + period_ms)),
                    'open': self.ohlcvs[period_ms][-1]['close'],
                    'high': self.ohlcvs[period_ms][-1]['close'],
                    'low': self.ohlcvs[period_ms][-1]['close'],
                    'close': self.ohlcvs[period_ms][-1]['close'],
                    'volume': 0.0
                })
            # new ohlcv
            self.ohlcvs[period_ms].append({
                'timestamp': int(round(self.ohlcvs[period_ms][-1]['timestamp'] + period_ms)),
                'open': websocket_tick['price'],
                'high': websocket_tick['price'],
                'low': websocket_tick['price'],
                'close': websocket_tick['price'],
                'volume': websocket_tick['qty']
            })
        else:
            new_ohlcv = False
            # update current ohlcv
            self.ohlcvs[period_ms][-1]['high'] = \
                max(self.ohlcvs[period_ms][-1]
                    ['high'], websocket_tick['price'])
            self.ohlcvs[period_ms][-1]['low'] = \
                min(self.ohlcvs[period_ms][-1]['low'], websocket_tick['price'])
            self.ohlcvs[period_ms][-1]['close'] = \
                websocket_tick['price']
            self.ohlcvs[period_ms][-1]['volume'] = \
                round(self.ohlcvs[period_ms][-1]
                      ['volume'] + websocket_tick['qty'], 10)
        if len(self.ohlcvs[period_ms]) > self.indicator_settings['max_periods_in_memory'] + 20:
            self.ohlcvs[period_ms] = \
                self.ohlcvs[period_ms][-self.indicator_settings['max_periods_in_memory']:]
        return new_ohlcv

    def load_cached_my_trades(self) -> [dict]:
        if os.path.exists(self.my_trades_cache_filepath):
            with open(self.my_trades_cache_filepath) as f:
                mtd = {(t := json.loads(line))[
                    'order_id']: t for line in f.readlines()}
            return sorted(mtd.values(), key=lambda x: x['timestamp'])
        return []

    async def update_my_trades(self):
        mt = await self.fetch_my_trades()
        if self.my_trades:
            mt = [e for e in mt if e['timestamp']
                  >= self.my_trades[-1]['timestamp']]
            if mt[0]['order_id'] == self.my_trades[-1]['order_id']:
                mt = mt[1:]
        with open(self.my_trades_cache_filepath, 'a') as f:
            for t in mt:
                f.write(json.dumps(t) + '\n')
        self.my_trades += mt

    def flush_stuck_locks(self, timeout: float = 4.0) -> None:
        now = time()
        for key in self.ts_locked:
            if self.ts_locked[key] > self.ts_released[key]:
                if now - self.ts_locked[key] > timeout:
                    print('flushing', key)
                    self.ts_released[key] = now
    
    def sl_filter(self):
        unr_profit = unrealize_profit(self.price, self.position['price'], self.position['size'])
        if unr_profit / self.position['wallet_balance'] < -self.max_sl_pct:
            #### 
            logger.debug(f"sl_filter unr_profit:{unr_profit} wallet_balance:{self.position['wallet_balance']}")
            return True
        return False
        # to return reverse trade/stop loss, in the future we consider keep pos wait for profit;


async def start_bot(bot):
    await bot.start_websocket()
