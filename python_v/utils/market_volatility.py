import ccxt  
import numpy as np  
import pandas as pd  
from collections import deque

exchange = ccxt.binance()  
def time_to_days(time_frame):
    if time_frame == '1m':
        return 60*24
    elif time_frame == '5m':
        return 60*24//5
    elif time_frame == '15m':
        return 60*24//15
    elif time_frame == '30m':
        return 60*24//30
    elif time_frame == '1h':
        return 24
    elif time_frame == '4h':
        return 24//4
    elif time_frame == '1d':
        return 1
    return 96 

# 计算对数收益率  
def calculate_log_returns(prices):  
    return np.log(prices / prices.shift(1))  

# 计算历史波动率（日化）  
def historical_volatility(log_returns, window=10, time_frame = None):  
    return log_returns.rolling(window=window).std() * np.sqrt(time_to_days(time_frame))   

# 计算已实现波动率（日化）  
def realized_volatility(log_returns, window=10, time_frame = None):  
    return log_returns.rolling(window=window).apply(lambda x: np.sqrt(np.sum(x**2)) * np.sqrt(time_to_days(time_frame)))  

# 计算 Parkinson 波动率（日化）  
def parkinson_volatility(high, low, window=10, time_frame = None):  
    return (np.log(high / low)**2).rolling(window=window).apply(lambda x: np.sqrt(np.sum(x) / (4 * window * np.log(2)))) * np.sqrt(time_to_days(time_frame))  

# 计算 Rogers-Satchell 波动率（日化）  
def rogers_satchell_volatility(open, high, low, close, window=10, time_frame = None):  
    term1 = np.log(high / close) * np.log(high / open)  
    term2 = np.log(low / close) * np.log(low / open)  
    return (term1 + term2).rolling(window=window).apply(lambda x: np.sqrt(np.sum(x) / window)) * np.sqrt(time_to_days(time_frame))  

# 计算 EWMA 波动率（日化）  
def ewma_volatility(log_returns, lambda_=0.94, window=10, time_frame = None):  
    var = log_returns.ewm(alpha=1 - lambda_, adjust=False).var()  
    return np.sqrt(var) * np.sqrt(time_to_days(time_frame))  


def calculate_natr(df, period=4):  
    high = df['high']  
    low = df['low']  
    close = df['close']  
    
    df['prev_close'] = close.shift(1)  
    df['tr'] = np.maximum.reduce([  
        high - low,  
        abs(high - df['prev_close']),  
        abs(low - df['prev_close'])  
    ])  
    df['atr'] = df['tr'].rolling(window=period).mean()  
    df['natr'] =df['atr'] / close   

async def market_stats(exchange,time_frame,symbol,period):
    ohlcv = await exchange.fetch_ohlcv(symbol, time_frame, limit=period)  
    df = pd.DataFrame(ohlcv, columns=['timestamp', 'open', 'high', 'low', 'close', 'volume'])
    df['log_return'] = calculate_log_returns(df['close'])  
    df['timestamp'] = pd.to_datetime(df['timestamp'], unit='ms') 
    df['historical_vol'] = historical_volatility(df['log_return'], time_frame=time_frame)  
    df['realized_vol'] = realized_volatility(df['log_return'], time_frame=time_frame)  
    df['parkinson_vol'] = parkinson_volatility(df['high'], df['low'], time_frame=time_frame)  
    df['rogers_satchell_vol'] = rogers_satchell_volatility(df['open'], df['high'], df['low'], df['close'], time_frame=time_frame)  
    df['ewma_vol'] = ewma_volatility(df['log_return'], time_frame=time_frame)    
    calculate_natr(df,period=3)
    return df


class ShortTermVolatility:
    def __init__(self, len: int = 180):
        self.prices = deque(maxlen=len)
        self.last_ts = 0

    def feed_md(self, price: float, ts: int) -> None:
        # ts 单位毫秒
        if ts - self.last_ts > 1000:
            self.prices.append(price)
            self.last_ts = ts

    def calculate(self) -> float:
        if len(self.prices) < 180:
            return 0
        np_sampling_buffer = np.array(self.prices, dtype=np.float64)
        vol = np.sqrt(
            np.sum(np.square(np.diff(np_sampling_buffer))) / np_sampling_buffer.size)
        return vol

    def is_full(self):
        return len(self.prices) == 1000
            
# here is example
# symbol = 'TRUMPUSDT'  
# timeframe = '15m' 
# limit = 20  
# ohlcv = exchange.fetch_ohlcv(symbol, timeframe, limit=limit)  
# # ["BTC", "ETH", "XRP", "BNB", "SOL", "DOGE", "ADA", "TRX", "LINK", "LTC", "XLM", "SUI", "AVAX", "SHIB", "HBAR", "TON", "HYPE", "OM", "DOT", "BCH"]
# # todo 方案 
# # 这些 作为数字货币整个市场的活跃程度 用4h x 60
# # 单币长期 单币种 近期活跃程度  下行 低波动停（和中期比较） 1h * 240
# # 单币中期 去修正 ema_spread 15m * 16
# # 单笔短期 微调挂单间距 获得较优成交价格 1m * 10
# df = pd.DataFrame(ohlcv, columns=['timestamp', 'open', 'high', 'low', 'close', 'volume'])  
# df['log_return'] = calculate_log_returns(df['close'])  
# df['timestamp'] = pd.to_datetime(df['timestamp'], unit='ms') 
# df['historical_vol'] = historical_volatility(df['log_return'], time_frame=timeframe)  
# df['realized_vol'] = realized_volatility(df['log_return'], time_frame=timeframe)  
# df['parkinson_vol'] = parkinson_volatility(df['high'], df['low'], time_frame=timeframe)  
# df['rogers_satchell_vol'] = rogers_satchell_volatility(df['open'], df['high'], df['low'], df['close'], time_frame=timeframe)  
# df['ewma_vol'] = ewma_volatility(df['log_return'], time_frame=timeframe)  
# calculate_natr(df,period=3)

# print(df.tail(15))
# mean_values = df.mean(numeric_only=True) 
# print(mean_values)  