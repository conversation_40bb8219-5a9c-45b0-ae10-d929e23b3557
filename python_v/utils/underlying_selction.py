import ccxt  
import pandas as pd  
import numpy as np  
import time  
import seaborn as sns  
import matplotlib.pyplot as plt  
from datetime import datetime, timedelta  

exchange = ccxt.binance({  
    'rateLimit': 1200,  
    'enableRateLimit': True,  
})  

def fetch_ohlcv(symbol, timeframe='1h', since=None):  
    ohlcv = exchange.fetch_ohlcv(symbol, timeframe, since=since)  
    df = pd.DataFrame(ohlcv, columns=['timestamp', 'open', 'high', 'low', 'close', 'volume'])  
    df['timestamp'] = pd.to_datetime(df['timestamp'], unit='ms')  
    return df  

def calculate_natr(df, period=24):  
    high = df['high']  
    low = df['low']  
    close = df['close']  
    
    df['prev_close'] = close.shift(1)  
    df['tr'] = np.maximum.reduce([  
        high - low,  
        abs(high - df['prev_close']),  
        abs(low - df['prev_close'])  
    ])  
    df['atr'] = df['tr'].rolling(window=period).mean()  
    df['natr'] = (df['atr'] / close) * 100  
    parkinson_vols = []  
    for i in range(len(df)):  
        if i < period - 1: 
            parkinson_vols.append(np.nan)  
        else:  
            hl_window = df.iloc[i - period + 1:i + 1][['high', 'low']]  
            parkinson_vol = parkinson_volatility(hl_window['high'], hl_window['low'])  
            parkinson_vols.append(parkinson_vol)  
      
    df['parkinson_vol'] = parkinson_vols 
    
    return df['natr'].mean(), df['parkinson_vol'].mean(), df['volume'].mean()  

def parkinson_volatility(high, low):  

    high = np.asarray(high)  
    low = np.asarray(low)  
    log_hl = np.log(high / low)  
    n = len(log_hl)  
    parkinson_vol = np.sqrt((1 / (4 * n * np.log(2))) * np.sum(log_hl ** 2))  
    return parkinson_vol  


def analyze_correlation(symbols, days=10):  

    exchange = ccxt.binance()  
    since = exchange.parse8601((datetime.utcnow() - timedelta(days=days)).strftime('%Y-%m-%d %H:%M:%S'))  
    prices = {}  
    symbols = pd.concat([pd.Series(['BTCUSDT'], name='symbol'), symbols], ignore_index=False)  
    for symbol in symbols:  
        data = exchange.fetch_ohlcv(symbol, timeframe='15m', since=since)  
        df = pd.DataFrame(data, columns=['timestamp', 'open', 'high', 'low', 'close', 'volume'])  
        df['timestamp'] = pd.to_datetime(df['timestamp'], unit='ms')  
        df.set_index('timestamp', inplace=True)  
        prices[symbol] = df['close'] 

    price_df = pd.DataFrame(prices)  

    correlation_matrix = price_df.corr()  

    # print("Correlation Matrix:")  
    # print(correlation_matrix)  
    plt.figure(figsize=(8, 6))  
    sns.heatmap(correlation_matrix, annot=True, cmap='coolwarm', fmt=".2f", linewidths=0.5)  
    plt.title(f'Binance Trading Pair Price Correlation Analysis (Last {days} Days)')  
    plt.show()  

    return correlation_matrix

def calculate_returns(prices):  

    return prices.pct_change().dropna() 

def analyze_returns_correlation(symbols, days=10):  

    exchange = ccxt.binance()  

    since = exchange.parse8601((datetime.utcnow() - timedelta(days=days)).strftime('%Y-%m-%d %H:%M:%S'))  

    prices = {}  
    for symbol in symbols:  
        data = exchange.fetch_ohlcv(symbol, timeframe='1d', since=since)  
        df = pd.DataFrame(data, columns=['timestamp', 'open', 'high', 'low', 'close', 'volume'])  
        df['timestamp'] = pd.to_datetime(df['timestamp'], unit='ms')  
        df.set_index('timestamp', inplace=True)  
        prices[symbol] = df['close'] 

    price_df = pd.DataFrame(prices)  

    returns_df = calculate_returns(price_df)  
    correlation_matrix = returns_df.corr()  
    print("Correlation Matrix of Returns:")  
    print(correlation_matrix)  

    plt.figure(figsize=(8, 6))  
    sns.heatmap(correlation_matrix, annot=True, cmap='coolwarm', fmt=".2f", linewidths=0.5)  
    plt.title(f'Binance Trading Pair Returns Correlation Analysis (Last {days} Days)')  
    plt.show()  


def selection():  
    days = 10
    symbols = ['BTCUSDT','ETHUSDT','AAVEUSDT', 'ALGOUSDT', 'ALPHAUSDT', 'ATOMUSDT', 'AVAXUSDT', 'AXSUSDT',
                    'BALUSDT', 'BANDUSDT', 'BATUSDT', 'BELUSDT', 'BLZUSDT', 'BZRXUSDT', 'COMPUSDT',
                     'DASHUSDT', 'DEFIUSDT', 'DOGEUSDT', 'EGLDUSDT', 'ENJUSDT',
                    'FILUSDT', 'FLMUSDT', 'FTMUSDT', 'HNTUSDT', 'ICXUSDT', 'IOSTUSDT', 'IOTAUSDT',
                    'KAVAUSDT', 'KNCUSDT', 'KSMUSDT', 'LRCUSDT', 'MATICUSDT', 'MKRUSDT', 'NEARUSDT',
                    'NEOUSDT', 'OCEANUSDT', 'OMGUSDT', 'ONTUSDT', 'QTUMUSDT', 'RENUSDT', 'RLCUSDT',
                    'RSRUSDT', 'RUNEUSDT', 'SNXUSDT', 'SOLUSDT', 'SRMUSDT', 'STORJUSDT',
                    'SUSHIUSDT', 'SXPUSDT', 'THETAUSDT', 'TOMOUSDT', 'TRBUSDT', 'UNIUSDT',
                    'VETUSDT', 'WAVESUSDT', 'YFIIUSDT', 'YFIUSDT', 'ZECUSDT', 'ZILUSDT', 'ZRXUSDT',
                    'ZENUSDT', 'SKLUSDT', 'GRTUSDT', '1INCHUSDT','TONUSDT','TRUMPUSDT','VINEUSDT',
                    'CTKUSDT']  
    one_week_ago = int((time.time() - days * 24 * 60 * 60) * 1000)  
    results = []  

    for symbol in symbols:  
        print(f"processing: {symbol}")  
        try:  
            df = fetch_ohlcv(symbol, '1h', since=one_week_ago)  
            natr, parkinson_vol, total_volume = calculate_natr(df)  
            results.append({  
                'symbol': symbol,  
                'natr': natr,  
                'parkinson_vol': parkinson_vol,
                'total_volume': total_volume  
            })  
        except Exception as e:  
            print(f"error in {symbol} : {e}")  

    results_df = pd.DataFrame(results)  
    filtered_df = results_df.sort_values(by=['natr', 'parkinson_vol', 'total_volume'], ascending=[False, False, False]).head(11)  
    colr_matrix = analyze_correlation(filtered_df['symbol'], days=days)
    print("\nresult:")  
    print(filtered_df)  
    return filtered_df

if __name__ == '__main__':  
    selection()