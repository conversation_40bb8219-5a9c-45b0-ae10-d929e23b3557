import sys
import numpy as np
from datetime import datetime
import random

from bot_logger import logger

if '--jit' in sys.argv:
    print('using numba')
    from numba import njit
else:
    print('not using numba')

    def njit(pyfunc=None, **kwargs):
        def wrap(func):
            return func
        if pyfunc is not None:
            return wrap(pyfunc)
        else:
            return wrap


@njit
def round_up(n: float, step: float, safety_rounding=10) -> float:
    return np.round(np.ceil(n / step) * step, safety_rounding)


@njit
def round_dn(n: float, step: float, safety_rounding=10) -> float:
    return np.round(np.floor(n / step) * step, safety_rounding)


@njit
def round_(n: float, step: float, safety_rounding=10) -> float:
    return np.round(np.round(n / step) * step, safety_rounding)


@njit
def calc_diff(x, y):
    return abs(x - y) / abs(y)


def sort_dict_keys(d):
    if type(d) == list:
        return [sort_dict_keys(e) for e in d]
    if type(d) != dict:
        return d
    return {key: sort_dict_keys(d[key]) for key in sorted(d)}

@njit
def calc_min_qty_inverse(qty_step: float, min_qty: float, min_cost: float, price: float) -> float:
    return min_qty

@njit
def calc_long_pnl_inverse(entry_price: float, close_price: float, qty: float) -> float:
    return abs(qty) * (1 / entry_price - 1 / close_price)


@njit
def calc_shrt_pnl_inverse(entry_price: float, close_price: float, qty: float) -> float:
    return abs(qty) * (1 / close_price - 1 / entry_price)


@njit
def calc_cost_inverse(qty: float, price: float) -> float:
    return abs(qty / price)


@njit
def calc_margin_cost_inverse(leverage: float, qty: float, price: float) -> float:
    return calc_cost_inverse(qty, price) / leverage


@njit
def calc_max_pos_size_inverse(leverage: float, balance: float, price: float) -> float:
    return balance * price * leverage


@njit
def calc_min_entry_qty_inverse(qty_step: float, min_qty: float, min_cost: float,
                               entry_qty_pct: float, leverage: float, balance: float,
                               price: float) -> float:
    return calc_min_entry_qty(calc_min_qty_inverse(qty_step, min_qty, min_cost, price),
                              qty_step,
                              balance * leverage * price,
                              entry_qty_pct)


@njit
def calc_min_qty_linear(qty_step: float, min_qty: float, min_cost: float, price: float) -> float:
    return max(min_qty, round_up(min_cost / price, qty_step))


@njit
def calc_long_pnl_linear(entry_price: float, close_price: float, qty: float) -> float:
    return abs(qty) * (close_price - entry_price)


@njit
def calc_shrt_pnl_linear(entry_price: float, close_price: float, qty: float) -> float:
    return abs(qty) * (entry_price - close_price)


@njit
def calc_cost_linear(qty: float, price: float) -> float:
    return abs(qty * price)


@njit
def calc_margin_cost_linear(leverage: float, qty: float, price: float) -> float:
    return calc_cost_linear(qty, price) / leverage


@njit
def calc_max_pos_size_linear(leverage: float, balance: float, price: float) -> float:
    return (balance / price) * leverage


@njit
def calc_min_entry_qty_linear(qty_step: float, min_qty: float, min_cost: float,
                              entry_qty_pct: float, leverage: float, balance: float,
                              price: float) -> float:
    return calc_min_entry_qty(calc_min_qty_linear(qty_step, min_qty, min_cost, price),
                              qty_step,
                              (balance * leverage) / price,
                              entry_qty_pct)


@njit
def calc_no_pos_bid_price(price_step: float,
                          ema_spread: float,
                          ema: float,
                          highest_bid: float) -> float:
    return min(highest_bid, round_dn(ema * (1 - ema_spread), price_step))


@njit
def calc_no_pos_ask_price(price_step: float,
                          ema_spread: float,
                          ema: float,
                          lowest_ask: float) -> float:
    return max(lowest_ask, round_up(ema * (1 + ema_spread), price_step))


@njit
def calc_pos_reduction_qty(qty_step: float,
                           stop_loss_pos_reduction: float,
                           pos_size: float) -> float:
    return min(abs(pos_size), round_up(abs(pos_size) * stop_loss_pos_reduction, qty_step))


@njit
def calc_min_close_qty(qty_step: float, min_qty: float, min_close_qty_multiplier: float,
                       min_entry_qty) -> float:
    return max(min_qty, round_dn(min_entry_qty * min_close_qty_multiplier, qty_step))


@njit
def calc_long_reentry_price(price_step: float,
                            grid_spacing: float,
                            grid_coefficient: float,
                            balance: float,
                            pos_margin: float,
                            pos_price: float):
    modified_grid_spacing = grid_spacing * \
        (1 + pos_margin / balance * grid_coefficient)
    return round_dn(pos_price * (1 - modified_grid_spacing),
                    round_up(pos_price * grid_spacing / 4, price_step))


@njit
def calc_std_width(pos_price):
    return


@njit
def calc_shrt_reentry_price(price_step: float,
                            grid_spacing: float,
                            grid_coefficient: float,
                            balance: float,
                            pos_margin: float,
                            pos_price: float):
    modified_grid_spacing = grid_spacing * \
        (1 + pos_margin / balance * grid_coefficient)
    return round_up(pos_price * (1 + modified_grid_spacing),
                    round_up(pos_price * grid_spacing / 4, price_step))


@njit
def calc_min_entry_qty(min_qty: float,
                       qty_step: float,
                       leveraged_balance_ito_contracts: float,
                       qty_balance_pct: float) -> float:
    return max(min_qty, round_dn(leveraged_balance_ito_contracts * abs(qty_balance_pct), qty_step))


@njit
def calc_reentry_qty(qty_step: float,
                     ddown_factor: float,
                     min_entry_qty: float,
                     max_pos_size: float,
                     pos_size: float):
    abs_pos_size = abs(pos_size)
    qty_available = max(0.0, round_dn(max_pos_size - abs_pos_size, qty_step))
    return min(qty_available,
               max(min_entry_qty, round_dn(abs_pos_size * ddown_factor, qty_step)))


@njit(fastmath=True)
def calc_long_closes(price_step: float,
                     qty_step: float,
                     min_qty: float,
                     min_markup: float,
                     max_markup: float,
                     pos_size: float,
                     pos_price: float,
                     lowest_ask: float,
                     n_orders: int = 10,
                     single_order_price_diff_threshold: float = 0.003):
    n_orders = int(round(min(n_orders, pos_size / min_qty)))

    prices = np.linspace(pos_price * (1 + min_markup),
                         pos_price * (1 + max_markup), n_orders)
    for i in range(len(prices)):
        prices[i] = round_up(prices[i], price_step)

    prices = np.unique(prices)
    prices = prices[np.where(prices >= lowest_ask)]
    diff = 0
    if len(prices) > 1:
      diff = calc_diff(prices[0], prices[1])
    logger.debug(f"calc_long_closes prices:{prices} diff:{diff:.6f} n_orders:{n_orders} pos_size:{pos_size} min_qty:{min_qty} lowest_ask:{lowest_ask}")
    if len(prices) == 0:
        return (np.array([-pos_size]),
                np.array([max(lowest_ask, round_up(pos_price * (1 + min_markup), price_step))]))
    elif len(prices) == 1:
        return np.array([-pos_size]), prices
    elif calc_diff(prices[1], prices[0]) > single_order_price_diff_threshold:
        #
        return (np.array([-pos_size]),
                np.array([max(lowest_ask, round_up(pos_price * (1 + min_markup), price_step))]))
    qtys = np.repeat(pos_size / len(prices), len(prices))
    for i in range(len(qtys)):
        qtys[i] = round_up(qtys[i], qty_step)
    qtys_sum = qtys.sum()
    while qtys_sum > pos_size:
        for i in range(len(qtys)):
            qtys[i] = round_(qtys[i] - qty_step, qty_step)
            qtys_sum = round_(qtys_sum - qty_step, qty_step)
            if qtys_sum <= pos_size:
                break
    return qtys * -1, prices


@njit(fastmath=True)
def calc_shrt_closes(price_step: float,
                     qty_step: float,
                     min_qty: float,
                     min_markup: float,
                     max_markup: float,
                     pos_size: float,
                     pos_price: float,
                     highest_bid: float,
                     n_orders: int = 10,
                     single_order_price_diff_threshold: float = 0.003):
    abs_pos_size = abs(pos_size)
    n_orders = int(round(min(n_orders, abs_pos_size / min_qty)))

    prices = np.linspace(pos_price * (1 - min_markup),
                         pos_price * (1 - max_markup), n_orders)
    for i in range(len(prices)):
        prices[i] = round_dn(prices[i], price_step)

    prices = np.unique(prices)
    prices = -np.sort(-prices[np.where(prices <= highest_bid)])
    diff = 0
    if len(prices) > 1:
      diff = calc_diff(prices[0], prices[1])
    logger.debug(f"calc_shrt_closes prices:{prices} diff:{diff:.6f} n_orders:{n_orders} pos_size:{pos_size} min_qty:{min_qty} highest_bid:{highest_bid}")
    if len(prices) == 0:
        return (np.array([-pos_size]),
                np.array([min(highest_bid, round_dn(pos_price * (1 - min_markup), price_step))]))
    elif len(prices) == 1:
        return np.array([-pos_size]), prices
    elif calc_diff(prices[0], prices[1]) > single_order_price_diff_threshold:
        #
        return (np.array([-pos_size]),
                np.array([min(highest_bid, round_dn(pos_price * (1 - min_markup), price_step))]))
    qtys = np.repeat(abs_pos_size / len(prices), len(prices))
    for i in range(len(qtys)):
        qtys[i] = round_up(qtys[i], qty_step)
    qtys_sum = qtys.sum()
    while qtys_sum > abs_pos_size:
        for i in range(len(qtys) - 1, -1, -1):
            qtys[i] = round_(qtys[i] - qty_step, qty_step)
            qtys_sum = round_(qtys_sum - qty_step, qty_step)
            if qtys_sum <= abs_pos_size:
                break
    return qtys, prices

@njit(fastmath=True)
def weighted_std(price_list:np.array,weight = None):
    if weight is None: # default decay weight is linear
        return np.std(price_list)
    mean = np.mean(price_list)
    weighted_variance = np.sum(weight * (price_list - mean) ** 2) / np.sum(weight)    
    return np.sqrt(weighted_variance)

@njit(fastmath=True)
def linear_weight_decay(price_list:np.array,impact:int = -1):
    l = len(price_list)
    if impact < 0:
        impact = 2*l
    return  np.arange(impact, l + impact)

@njit(fastmath=True)
def exp_weight_decay(price_list:np.array, k:int = 0.004):
    l = len(price_list)
    x = np.arange(0, l)
    return np.exp(k * (x-l))

@njit(fastmath=True)
def hyperbolic_weight_decay(price_list:np.array, k:int = 0.007):
    l = len(price_list)
    x = np.arange(0, l)
    return 1 / (1 + k*(l-x)) 

def flatten(lst: list) -> list:
    return [y for x in lst for y in x]

def float_equal(x: float, y: float, epsilon:float = 0.3):
    return (x + epsilon > y) and (x - epsilon < y) 

def calculate_atr(df, period=14):  
    df['tr'] = df[['high', 'low', 'close']].apply(  
        lambda row: max(row['high'] - row['low'], abs(row['high'] - row['close']), abs(row['low'] - row['close'])),  
        axis=1  
    )  
    df['atr'] = df['tr'].rolling(window=period).mean()  
    return df  

def calculate_natr(df, period=24):  
    df = calculate_atr(df, period)  
    df['natr'] = (df['atr'] / df['close']) * 100  
    return df  

def timestamp_2_date(timestamp):
    timestamp_in_seconds = timestamp / 1000 
    dt_object = datetime.utcfromtimestamp(timestamp_in_seconds) 
    formatted_date = dt_object.strftime('%Y/%m/%d %H:%M:%S')  
    return formatted_date

def no_trade_punishment(time): # seconds
  #  res = max(0.4, 3600/(3600 + max(0, time - 200)))
   res = max(0.7, 1200/(1200 + max(0, time - 200)))
   return res