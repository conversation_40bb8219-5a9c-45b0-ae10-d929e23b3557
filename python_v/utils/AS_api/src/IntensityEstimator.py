from typing import Optional, Tuple
from concurrent.futures import Future
from .calibration.AkSolverFactory import AkSolverFactory
from .calibration.SpreadIntensityCurve import SpreadIntensityCurve
from .EstimationExecutor import EstimationExecutor

"""
估计买卖限价单的执行强度，校准A和k参数
"""
class IntensityEstimator:
    def __init__(self, spread_step: float, n_spreads: int, w: int, dt: int, solver_factory: AkSolverFactory):
        """
        @param spread_step 用于估计的最小价差，必须大于或等于最小变动单位
        @param n_spreads 要测试的价差数量，价差为 (0,1,..nSpreads-1) * +/-spreadStep 的倍数
        @param w 滑动窗口宽度，以时间单位表示
        @param dt 时间缩放量，以时间单位表示
        @param solver_factory AkSolver工厂
        """
        self.w = w
        self.sell_execution_intensity = SpreadIntensityCurve(spread_step, n_spreads, dt, solver_factory)
        self.buy_execution_intensity = SpreadIntensityCurve(-spread_step, n_spreads, dt, solver_factory)
        self.init_done_ts: Optional[int] = None
        self.is_initializing = True
        self.is_initialized = False

    def on_tick(self, bid: float, ask: float, ts: int) -> bool:
        """
        @param bid 市场最佳买价
        @param ask 市场最佳卖价
        @param ts 时间戳
        @return 一旦估计器用足够的数据初始化，返回true
        """
        if self.is_initializing:
            self._init(ts)
        
        mid_price = (bid + ask) / 2
        window_start = ts - self.w
        self.sell_execution_intensity.on_tick(mid_price, bid, ts, window_start)
        self.buy_execution_intensity.on_tick(mid_price, ask, ts, window_start)
        return self.is_initialized

    def on_tick_async(self, bid: float, ask: float, ts: int) -> Future[bool]:
        """
        {@link #on_tick}的异步并行实现
        @param bid 市场最佳买价
        @param ask 市场最佳卖价
        @param ts 时间戳
        @return 一旦估计器用足够的数据初始化，返回true
        """
        def task():
            if self.is_initializing:
                self._init(ts)
            
            mid_price = (bid + ask) / 2
            window_start = ts - self.w
            sell_result = self.sell_execution_intensity.on_tick_async(mid_price, bid, ts, window_start)
            buy_result = self.buy_execution_intensity.on_tick_async(mid_price, ask, ts, window_start)
            sell_result.result()
            buy_result.result()
            return self.is_initialized
        
        return EstimationExecutor.submit(task)

    def _init(self, ts: int) -> None:
        """
        当w时间过去后，将估计器设置为已初始化
        @param ts 时间戳
        """
        if self.init_done_ts is None:
            self.init_done_ts = ts + self.w
            return
        
        if self.init_done_ts <= ts:
            self.is_initialized = True
            self.is_initializing = False

    def estimate(self, ts: int) -> Tuple[Tuple[float, float], Tuple[float, float]]:
        """
        执行所有参数的估计
        @param ts 时间戳
        @return 包含买卖估计结果的元组
        """
        window_start = ts - self.w
        buy_est = self.buy_execution_intensity.estimate_ak(ts, window_start)
        sell_est = self.sell_execution_intensity.estimate_ak(ts, window_start)
        return buy_est, sell_est

    def estimate_async(self, ts: int) -> Future[Tuple[Tuple[float, float], Tuple[float, float]]]:
        """
        {@link #estimate}的异步并行实现
        @param ts 时间戳
        @return 包含买卖估计结果的Future
        """
        def task():
            window_start = ts - self.w
            buy_est = self.buy_execution_intensity.estimate_ak_async(ts, window_start)
            sell_est = self.sell_execution_intensity.estimate_ak_async(ts, window_start)
            return buy_est.result(), sell_est.result()
        
        return EstimationExecutor.submit(task)
