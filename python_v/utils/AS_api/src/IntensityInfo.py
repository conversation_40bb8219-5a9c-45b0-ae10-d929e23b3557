import math
from typing import Tuple

"""
保存买卖限价单的估计执行强度参数A和k的信息
便于计算强度和价差

λ(δ) = A * e^(-k * δ)
λ - 泊松订单执行强度
δ - 价差（与中间价的距离）
A - 参数，与交易强度正相关
k - 参数，与市场深度正相关
"""
class IntensityInfo:
    
    def __init__(self, buy_a: float, buy_k: float, sell_a: float, sell_k: float):
        """
        初始化强度参数

        Args:
            buy_a (float): 买单A参数
            buy_k (float): 买单k参数
            sell_a (float): 卖单A参数
            sell_k (float): 卖单k参数
        """
        self.buy_a = buy_a
        self.buy_k = buy_k
        self.sell_a = sell_a
        self.sell_k = sell_k

    @classmethod
    def from_arrays(cls, buy_ak: Tuple[float, float], sell_ak: Tuple[float, float]):
        return cls(buy_ak[0], buy_ak[1], sell_ak[0], sell_ak[1])

    def get_sell_fill_intensity(self, spread: float) -> float:
        return self.get_intensity(spread, self.sell_a, self.sell_k)

    def get_buy_fill_intensity(self, spread: float) -> float:
        return self.get_intensity(spread, self.buy_a, self.buy_k)

    def get_sell_spread(self, intensity: float) -> float:
        return self.get_spread(intensity, self.sell_a, self.sell_k)

    def get_buy_spread(self, intensity: float) -> float:
        return self.get_spread(intensity, self.buy_a, self.buy_k)

    @staticmethod
    def get_intensity(target_spread: float, a: float, k: float) -> float:
        """  
        计算目标价差的订单泊松强度 λ，使用提供的 A 和 k  

        参数:  
            targetSpread: δ，与中间价格的距离  
            a: 参数 A  
            k: 参数 k  

        返回:  
            指定价差的强度 λ  
        """      
        return a * math.exp(-k * target_spread)

    @staticmethod
    def get_spread(target_intensity: float, a: float, k: float) -> float:
        """  
        计算订单的价差 δ（与中间价格的距离），根据目标执行强度 λ 和提供的 A 和 k  

        参数:  
            targetIntensity: 泊松强度 λ  
            a: 参数 A  
            k: 参数 k  

        返回:  
            价差 δ（与中间价格的距离）  
        """  
        return -(math.log(target_intensity / a)) / k
