import numpy as np
from .AbstractAkSolver import AbstractAkSolver

"""
A和k的实现求解器
从N个点创建Ns = (N*(N-1))/2个唯一点对(δx,λx)(δy,λy)。
对于每组点，求解以下方程组以得到A'和k'：
λx = A' e-k'δx
λy = A' e-k'δy
最终估计值为A = mean(A'1,A'2,... A'Ns)和k = mean(k'1,k'2,... k'Ns)
"""
class AkMultiCurveSolver(AbstractAkSolver):
    def __init__(self, spread_specification: np.ndarray):
        """
        @param spread_specification 价差数组（价差-强度曲线的X轴）
        """
        super().__init__(spread_specification)
        n_estimates = len(spread_specification) * (len(spread_specification) - 1) // 2
        self.k_estimates = np.zeros(n_estimates)
        self.a_estimates = np.zeros(n_estimates)

    def solve_ak(self, intensities: np.ndarray) -> np.ndarray:
        """
        @param intensities 强度数组（价差-强度曲线的Y轴）
        @return 返回估计的A和k数组 [A, k]
        """
        est_idx = 0
        with np.errstate(divide='ignore', invalid='ignore'):
            for i in range(len(intensities) - 1):
                for j in range(i + 1, len(intensities)):
                    
                    self.k_estimates[est_idx] = (
                        np.log(intensities[j] / intensities[i]) / 
                        (self.spread_specification[i] - self.spread_specification[j])
                    )
                    self.a_estimates[est_idx] = (
                        intensities[i] * np.exp(self.k_estimates[est_idx] * self.spread_specification[i])
                    )
                    est_idx += 1
        
        return np.array([np.mean(self.a_estimates), np.mean(self.k_estimates)])
