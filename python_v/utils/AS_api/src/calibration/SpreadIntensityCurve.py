import numpy as np
from typing import List
from concurrent.futures import Future
from .EmpiricalIntensityEstimator import EmpiricalIntensityEstimator
from .AbstractAkSolver import AbstractAkSolver
from .AkSolverFactory import AkSolverFactory
from ..EstimationExecutor import EstimationExecutor

"""
构建价差δ（X）-强度λ（Y）曲线
返回指定求解器提供的A和k估计值
"""
class SpreadIntensityCurve:
    def __init__(self, spread_step: float, n_spreads: int, dt: int, solver_factory: AkSolverFactory):
        """
        @param spread_step 用于估计的最小价差，买入限价单使用负号，卖出限价单使用正号
        @param n_spreads 要测试的价差数量，价差为 (1,2,..nSpreads) * spreadStep 的倍数
        @param dt 时间间隔
        @param solver_factory Ak估计器工厂
        """
        self.intensity_estimators: List[EmpiricalIntensityEstimator] = []
        spread_specification = np.zeros(n_spreads)
        self.intensity_estimates = np.zeros(n_spreads)
        
        for i in range(n_spreads):
            spread_specification[i] = i * spread_step
            self.intensity_estimators.append(
                EmpiricalIntensityEstimator(spread_specification[i], np.sign(spread_step), dt)
            )
        
        self.ak_solver: AbstractAkSolver = solver_factory.get_solver(spread_specification)

    def on_tick(self, ref_price: float, fill_price: float, ts: int, window_start: int):
        """
        @param ref_price 参考价格（中间价）
        @param fill_price 所有订单被完全填充的价格
        @param ts 当前时间戳
        @param window_start 评估窗口的开始时间，旧数据将被删除
        """
        for estimator in self.intensity_estimators:
            estimator.on_tick(ref_price, fill_price, ts, window_start)

    def on_tick_async(self, ref_price: float, fill_price: float, ts: int, window_start: int) -> Future[None]:
        """
        {@link #on_tick}的异步并行实现
        @param ref_price 参考价格（中间价）
        @param fill_price 所有订单被完全填充的价格
        @param ts 当前时间戳
        @param window_start 评估窗口的开始时间
        @return Future对象，表示异步操作的结果
        """
        def task():
            tasks = [
                EstimationExecutor.submit(lambda ie=ie: ie.on_tick(ref_price, fill_price, ts, window_start))
                for ie in self.intensity_estimators
            ]
            for task in tasks:
                task.result()
            return None
        return EstimationExecutor.submit(task)

    def estimate_ak(self, ts: int, window_start: int) -> np.ndarray:
        """
        @param ts 当前时间戳
        @param window_start 评估窗口的开始时间，旧数据将被删除
        @return 包含A和k估计值的数组 [A, k]
        """
        for i, estimator in enumerate(self.intensity_estimators):
            self.intensity_estimates[i] = estimator.estimate_intensity(ts, window_start)
        return self.ak_solver.solve_ak(self.intensity_estimates)

    def estimate_ak_async(self, ts: int, window_start: int) -> Future[np.ndarray]:
        """
        {@link #estimate_ak}的异步并行实现
        @param ts 当前时间戳
        @param window_start 评估窗口的开始时间
        @return Future对象，包含A和k估计值的数组 [A, k]
        """
        def task():
            tasks = [
                EstimationExecutor.submit(lambda ie=ie, i=i: self.intensity_estimates.__setitem__(i, ie.estimate_intensity(ts, window_start)))
                for i, ie in enumerate(self.intensity_estimators)
            ]
            for task in tasks:
                task.result()
            return self.ak_solver.solve_ak(self.intensity_estimates)
        return EstimationExecutor.submit(task)
