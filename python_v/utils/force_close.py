from binance.client import Client  
import time  

api_key = "y" 
api_secret = "y"
  
def close_all_positions(api_key, api_secret):  
    client = Client(api_key, api_secret)
    status = True
    try:  
        positions = client.futures_position_information()  
        
        for position in positions:  
            symbol = position["symbol"] 
            position_amt = float(position["positionAmt"]) 
            if position_amt != 0: 
                order_side = "SELL" if position_amt > 0 else "BUY"  
                abs_position_amt = abs(position_amt) 
                
                try:   
                    response = client.futures_create_order(  
                        symbol=symbol,  
                        side=order_side,  
                        type="MARKET",  
                        quantity=abs_position_amt  
                    )  
                    print(f"平仓成功: {symbol}, 数量: {abs_position_amt}, 方向: {order_side}")  
                except Exception as e:  
                    print(f"平仓失败: {symbol}, 错误: {str(e)}")  
                    status = False

    except Exception as e:  
        print(f"获取持仓信息失败: {str(e)}")
        status = False
    finally:   
        client.session.close()  
    if status:
        return status, 0.0
    else:
        return status, 1.0
    
def cancel_all_open_orders(api_key, api_secret):  
    client = Client(api_key, api_secret)
    status = True
    try:   
        open_orders = client.futures_get_open_orders()  

        if not open_orders:  
            print("没有任何挂单需要取消！")  
            return True 

        for order in open_orders:  
            symbol = order['symbol']  
            try:  
                response = client.futures_cancel_order(symbol=symbol, orderId=order['orderId'])  
                print(f"取消挂单成功: {symbol}, 订单ID: {order['orderId']}")  
            except Exception as e:  
                print(f"取消挂单失败: {symbol}, 错误: {str(e)}")  
                status = False
    except Exception as e:  
        print(f"获取挂单失败: {str(e)}") 
        status = False
    finally:   
        client.session.close() 
    return status
  