#!/usr/bin/env python3
"""
比较Python和C++的性能
Compare Python vs C++ Performance

用法:
python compare_python_cpp_performance.py --category core_math
python compare_python_cpp_performance.py --category all
"""

import json
import os
import pandas as pd
import numpy as np
import argparse
import time
from pathlib import Path

def load_benchmark_results(cpp_file, python_file):
    """加载C++和Python的性能测试结果"""
    cpp_results = {}
    python_results = {}

    # 加载C++结果
    if os.path.exists(cpp_file):
        with open(cpp_file, 'r') as f:
            cpp_results = json.load(f)
        print(f"C++结果加载成功: {len(cpp_results)} 个算子")
    else:
        print(f"C++结果文件不存在: {cpp_file}")

    # 加载Python结果
    if os.path.exists(python_file):
        with open(python_file, 'r') as f:
            python_results = json.load(f)
        print(f"Python结果加载成功: {len(python_results)} 个算子")
    else:
        print(f"Python结果文件不存在: {python_file}")

    return cpp_results, python_results

def compare_performance(cpp_results, python_results, category, output_dir, timestamp):
    """比较C++和Python的性能，只返回数据不保存单独文件"""

    # 找到共同的算子
    common_operators = set(cpp_results.keys()) & set(python_results.keys())

    if not common_operators:
        print("没有找到共同的算子进行比较")
        return None

    print(f"找到 {len(common_operators)} 个共同算子进行比较")

    # 创建比较数据
    comparison_data = []

    for op in sorted(common_operators):
        cpp_time = cpp_results[op]
        python_time = python_results[op]

        # 跳过错误的结果
        if cpp_time < 0 or python_time < 0:
            continue

        speedup = python_time / cpp_time if cpp_time > 0 else float('inf')

        comparison_data.append({
            'Operator': op,
            'C++_Time_μs': cpp_time,
            'Python_Time_μs': python_time,
            'Speedup': speedup,
            'Performance_Ratio': f"{speedup:.2f}x"
        })

    if not comparison_data:
        print("没有有效的比较数据")
        return None

    # 创建DataFrame
    df = pd.DataFrame(comparison_data)
    df = df.sort_values('Speedup', ascending=False)

    # 打印摘要到控制台
    print("\n性能比较摘要:")
    print("=" * 50)
    print(f"测试分类: {category}")
    print(f"测试算子总数: {len(comparison_data)}")
    print(f"C++平均执行时间: {df['C++_Time_μs'].mean():.3f} μs")
    print(f"Python平均执行时间: {df['Python_Time_μs'].mean():.3f} μs")
    print(f"平均加速比: {df['Speedup'].mean():.2f}x")

    print(f"\n前5名最快的算子:")
    for i, row in df.head().iterrows():
        print(f"  {row['Operator']}: {row['Speedup']:.2f}x")

    print(f"\n后5名最慢的算子:")
    for i, row in df.tail().iterrows():
        print(f"  {row['Operator']}: {row['Speedup']:.2f}x")

    return df

def generate_summary_report(all_results, output_dir, timestamp):
    """生成所有分类的汇总报告"""
    if not all_results:
        return

    # 合并所有数据
    all_data = []
    category_summaries = {}

    for category, df in all_results.items():
        if df is not None and not df.empty:
            # 添加分类信息
            df_copy = df.copy()
            df_copy['Category'] = category
            all_data.append(df_copy)

            # 计算分类汇总
            category_summaries[category] = {
                'total_operators': len(df),
                'cpp_avg_time': df['C++_Time_μs'].mean(),
                'python_avg_time': df['Python_Time_μs'].mean(),
                'avg_speedup': df['Speedup'].mean(),
                'max_speedup': df['Speedup'].max(),
                'min_speedup': df['Speedup'].min()
            }

    if not all_data:
        print("没有有效的数据生成汇总报告")
        return

    # 合并所有DataFrame
    combined_df = pd.concat(all_data, ignore_index=True)

    # 生成汇总报告
    summary_report_file = output_dir / f"python_vs_cpp_summary_report_{timestamp}.txt"
    with open(summary_report_file, 'w', encoding='utf-8') as f:
        f.write("Feature Operators 性能比较汇总报告 (Python vs C++)\n")
        f.write("=" * 80 + "\n\n")
        f.write(f"测试时间: {timestamp}\n")
        f.write(f"总测试分类数: {len(category_summaries)}\n")
        f.write(f"总测试算子数: {len(combined_df)}\n\n")

        # 整体统计
        f.write("整体性能统计:\n")
        f.write("-" * 50 + "\n")
        f.write(f"C++总平均执行时间: {combined_df['C++_Time_μs'].mean():.3f} μs\n")
        f.write(f"Python总平均执行时间: {combined_df['Python_Time_μs'].mean():.3f} μs\n")
        f.write(f"总平均加速比: {combined_df['Speedup'].mean():.2f}x\n")
        f.write(f"最大加速比: {combined_df['Speedup'].max():.2f}x ({combined_df.loc[combined_df['Speedup'].idxmax(), 'Operator']})\n")
        f.write(f"最小加速比: {combined_df['Speedup'].min():.2f}x ({combined_df.loc[combined_df['Speedup'].idxmin(), 'Operator']})\n\n")

        # 分类汇总
        f.write("各分类性能汇总:\n")
        f.write("-" * 80 + "\n")
        f.write(f"{'分类':<15} {'算子数':<8} {'C++平均(μs)':<12} {'Python平均(μs)':<15} {'平均加速比':<10}\n")
        f.write("-" * 80 + "\n")

        for category, summary in category_summaries.items():
            f.write(f"{category:<15} {summary['total_operators']:<8} "
                   f"{summary['cpp_avg_time']:<12.3f} {summary['python_avg_time']:<15.3f} "
                   f"{summary['avg_speedup']:<10.2f}\n")

        f.write("\n")

        # 前10名最快的算子
        top_10 = combined_df.nlargest(10, 'Speedup')
        f.write("前10名最快的算子 (按加速比排序):\n")
        f.write("-" * 80 + "\n")
        f.write(f"{'排名':<4} {'算子':<20} {'分类':<15} {'C++时间(μs)':<12} {'Python时间(μs)':<15} {'加速比':<10}\n")
        f.write("-" * 80 + "\n")

        for i, (_, row) in enumerate(top_10.iterrows(), 1):
            f.write(f"{i:<4} {row['Operator']:<20} {row['Category']:<15} "
                   f"{row['C++_Time_μs']:<12.3f} {row['Python_Time_μs']:<15.3f} "
                   f"{row['Speedup']:<10.2f}\n")

        f.write("\n")

        # 后10名最慢的算子
        bottom_10 = combined_df.nsmallest(10, 'Speedup')
        f.write("后10名最慢的算子 (按加速比排序):\n")
        f.write("-" * 80 + "\n")
        f.write(f"{'排名':<4} {'算子':<20} {'分类':<15} {'C++时间(μs)':<12} {'Python时间(μs)':<15} {'加速比':<10}\n")
        f.write("-" * 80 + "\n")

        for i, (_, row) in enumerate(bottom_10.iterrows(), 1):
            f.write(f"{i:<4} {row['Operator']:<20} {row['Category']:<15} "
                   f"{row['C++_Time_μs']:<12.3f} {row['Python_Time_μs']:<15.3f} "
                   f"{row['Speedup']:<10.2f}\n")

        f.write("\n" + "=" * 80 + "\n")

        # 按分类详细列出所有算子
        f.write("按分类详细性能列表:\n")
        f.write("=" * 80 + "\n")

        for category in sorted(category_summaries.keys()):
            category_data = combined_df[combined_df['Category'] == category].sort_values('Speedup', ascending=False)
            f.write(f"\n{category.upper()} ({len(category_data)} 个算子):\n")
            f.write("-" * 60 + "\n")
            f.write(f"{'算子':<20} {'C++时间(μs)':<12} {'Python时间(μs)':<15} {'加速比':<10}\n")
            f.write("-" * 60 + "\n")

            for _, row in category_data.iterrows():
                f.write(f"{row['Operator']:<20} {row['C++_Time_μs']:<12.3f} "
                       f"{row['Python_Time_μs']:<15.3f} {row['Speedup']:<10.2f}\n")

    # 保存汇总CSV
    summary_csv_file = output_dir / f"python_vs_cpp_summary_detailed_{timestamp}.csv"
    combined_df.to_csv(summary_csv_file, index=False)

    # 保存汇总JSON
    summary_json_file = output_dir / f"python_vs_cpp_summary_results_{timestamp}.json"
    with open(summary_json_file, 'w') as f:
        json.dump({
            "timestamp": timestamp,
            "summary": {
                "total_categories": len(category_summaries),
                "total_operators": len(combined_df),
                "overall_cpp_avg_time": combined_df['C++_Time_μs'].mean(),
                "overall_python_avg_time": combined_df['Python_Time_μs'].mean(),
                "overall_avg_speedup": combined_df['Speedup'].mean(),
                "max_speedup": combined_df['Speedup'].max(),
                "min_speedup": combined_df['Speedup'].min()
            },
            "category_summaries": category_summaries,
            "detailed_results": combined_df.to_dict('records')
        }, f, indent=2, default=str)

    print(f"\n🎉 汇总报告已生成:")
    print(f"📄 详细报告: {summary_report_file}")
    print(f"📊 详细数据: {summary_csv_file}")
    print(f"📋 JSON结果: {summary_json_file}")

    # 打印汇总到控制台
    print(f"\n📈 整体性能汇总:")
    print("=" * 60)
    print(f"总测试分类数: {len(category_summaries)}")
    print(f"总测试算子数: {len(combined_df)}")
    print(f"C++总平均执行时间: {combined_df['C++_Time_μs'].mean():.3f} μs")
    print(f"Python总平均执行时间: {combined_df['Python_Time_μs'].mean():.3f} μs")
    print(f"总平均加速比: {combined_df['Speedup'].mean():.2f}x")

    print(f"\n🏆 各分类平均加速比排名:")
    sorted_categories = sorted(category_summaries.items(), key=lambda x: x[1]['avg_speedup'], reverse=True)
    for i, (category, summary) in enumerate(sorted_categories, 1):
        print(f"  {i}. {category}: {summary['avg_speedup']:.2f}x ({summary['total_operators']} 个算子)")

    return combined_df

def main():
    """主函数"""
    parser = argparse.ArgumentParser(description='Compare Python vs C++ Performance')
    parser.add_argument('--category',
                       choices=['core_math', 'logical_ops', 'comparison_ops', 'data_utils',
                               'reduction_ops', 'timeseries_ops', 'panel_ops', 'group_ops', 'all'],
                       default='all',
                       help='测试分类')
    parser.add_argument('--cpp-results-dir',
                       default="/home/<USER>/git/feature_operators/tests/test_benchmark/results/cpp",
                       help='C++结果目录')
    parser.add_argument('--python-results-dir',
                       default="/home/<USER>/git/feature_operators/tests/test_benchmark/results/python",
                       help='Python结果目录')
    parser.add_argument('--output-dir',
                       default="/home/<USER>/git/feature_operators/tests/test_benchmark/results/comparison",
                       help='输出目录')

    args = parser.parse_args()

    # 设置路径
    cpp_results_dir = Path(args.cpp_results_dir)
    python_results_dir = Path(args.python_results_dir)
    output_dir = Path(args.output_dir)

    # 创建输出目录
    output_dir.mkdir(parents=True, exist_ok=True)

    print("开始性能比较分析...")
    print("=" * 50)
    print(f"分类: {args.category}")
    print(f"C++结果目录: {cpp_results_dir}")
    print(f"Python结果目录: {python_results_dir}")
    print("=" * 50)

    if args.category == "all":
        # 比较所有分类
        categories = ['core_math', 'logical_ops', 'comparison_ops', 'data_utils',
                     'reduction_ops', 'timeseries_ops', 'panel_ops', 'group_ops']

        all_success = True
        all_results = {}  # 收集所有分类的结果
        timestamp = time.strftime("%Y%m%d_%H%M%S")  # 统一时间戳

        for cat in categories:
            print(f"\n{'='*20} {cat.upper()} {'='*20}")

            cpp_file = cpp_results_dir / f"{cat}_benchmark.json"
            python_file = python_results_dir / f"{cat}_benchmark.json"

            # 加载结果
            cpp_results, python_results = load_benchmark_results(cpp_file, python_file)

            if not cpp_results or not python_results:
                print(f"缺少 {cat} 分类的性能测试结果文件")
                all_success = False
                continue

            # 比较性能
            df = compare_performance(cpp_results, python_results, cat, output_dir, timestamp)
            if df is None:
                all_success = False
            else:
                all_results[cat] = df  # 收集结果

        # 生成汇总报告
        if all_results:
            print(f"\n{'='*20} 生成汇总报告 {'='*20}")
            generate_summary_report(all_results, output_dir, timestamp)

        return 0 if all_success else 1
    else:
        # 比较指定分类
        cpp_file = cpp_results_dir / f"{args.category}_benchmark.json"
        python_file = python_results_dir / f"{args.category}_benchmark.json"

        # 加载结果
        cpp_results, python_results = load_benchmark_results(cpp_file, python_file)

        if not cpp_results or not python_results:
            print("缺少性能测试结果文件，请先运行性能测试")
            return 1

        # 比较性能
        timestamp = time.strftime("%Y%m%d_%H%M%S")
        df = compare_performance(cpp_results, python_results, args.category, output_dir, timestamp)

        if df is not None and not df.empty:
            # 为单个分类也生成汇总报告
            all_results = {args.category: df}
            print(f"\n{'='*20} 生成汇总报告 {'='*20}")
            generate_summary_report(all_results, output_dir, timestamp)
            print("\n性能比较分析完成!")
            return 0
        else:
            print("\n性能比较分析失败!")
            return 1

if __name__ == "__main__":
    import sys
    sys.exit(main())
