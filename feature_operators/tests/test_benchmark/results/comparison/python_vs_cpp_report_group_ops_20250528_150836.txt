Feature Operators 性能比较报告 (<PERSON> vs C++)
============================================================

测试分类: group_ops
测试时间: 20250528_150836

测试算子总数: 2
C++平均执行时间: 20267.233 μs
Python平均执行时间: 166606.605 μs
平均加速比: 8.68x

性能排名 (按加速比排序):
------------------------------------------------------------
排名   算子                   C++时间(μs)    Python时间(μs)    加速比       
------------------------------------------------------------
1    <USER>         <GROUP>.872    135719.939      10.05     
2    pn_GroupNorm         27023.593    197493.271      7.31      

============================================================
分类性能统计:
------------------------------
面板算子 (2个): 平均加速比 8.68x
