{"timestamp": "20250528_150836", "category": "timeseries_ops", "summary": {"total_operators": 7, "cpp_avg_time": 6845.701057, "python_avg_time": 58980.71227683908, "avg_speedup": 11.860027231466347, "max_speedup": 41.33785847025065, "min_speedup": 2.4386818837380155}, "detailed_results": [{"Operator": "ts_<PERSON>rr", "C++_Time_μs": 5036.095367, "Python_Time_μs": 208181.397523731, "Speedup": 41.33785847025065, "Performance_Ratio": "41.34x"}, {"Operator": "ts_<PERSON><PERSON>", "C++_Time_μs": 270.8811, "Python_Time_μs": 2115.5174200733504, "Speedup": 7.809763841306575, "Performance_Ratio": "7.81x"}, {"Operator": "ts_<PERSON>", "C++_Time_μs": 13169.746033, "Python_Time_μs": 37771.337665617466, "Speedup": 2.868038424656952, "Performance_Ratio": "2.87x"}, {"Operator": "ts_Mean", "C++_Time_μs": 4331.994633, "Python_Time_μs": 39351.28025089701, "Speedup": 9.08387096122633, "Performance_Ratio": "9.08x"}, {"Operator": "ts_<PERSON>", "C++_Time_μs": 15372.573, "Python_Time_μs": 37488.815281540155, "Speedup": 2.4386818837380155, "Performance_Ratio": "2.44x"}, {"Operator": "ts_Stdev", "C++_Time_μs": 6525.131633, "Python_Time_μs": 49951.72365258137, "Speedup": 7.655282140203434, "Performance_Ratio": "7.66x"}, {"Operator": "ts_Sum", "C++_Time_μs": 3213.485633, "Python_Time_μs": 38004.914143433176, "Speedup": 11.826694898882462, "Performance_Ratio": "11.83x"}]}