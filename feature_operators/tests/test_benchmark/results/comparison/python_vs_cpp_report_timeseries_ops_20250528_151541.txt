Feature Operators 性能比较报告 (Python vs C++)
============================================================

测试分类: timeseries_ops
测试时间: 20250528_151541

测试算子总数: 7
C++平均执行时间: 6836.038 μs
Python平均执行时间: 58309.720 μs
平均加速比: 11.81x

性能排名 (按加速比排序):
------------------------------------------------------------
排名   算子                   C++时间(μs)    Python时间(μs)    加速比       
------------------------------------------------------------
1    <USER>              <GROUP>.132     205930.140      40.66     
2    ts_Sum               3018.303     37251.426       12.34     
3    ts_Mean              4174.096     37881.165       9.08      
4    ts_Delay             275.323      2139.009        7.77      
5    ts_Stdev             6536.642     49363.172       7.55      
6    ts_Min               13387.199    37342.776       2.79      
7    ts_Max               15395.568    38260.349       2.49      

============================================================
分类性能统计:
------------------------------
时间序列算子 (7个): 平均加速比 11.81x
