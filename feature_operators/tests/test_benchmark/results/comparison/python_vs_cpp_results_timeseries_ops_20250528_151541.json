{"timestamp": "20250528_151541", "category": "timeseries_ops", "summary": {"total_operators": 7, "cpp_avg_time": 6836.037600000001, "python_avg_time": 58309.719525277615, "avg_speedup": 11.809857975613713, "max_speedup": 40.656423842071774, "min_speedup": 2.4851534875034056}, "detailed_results": [{"Operator": "ts_<PERSON>rr", "C++_Time_μs": 5065.131667, "Python_Time_μs": 205930.13986945152, "Speedup": 40.656423842071774, "Performance_Ratio": "40.66x"}, {"Operator": "ts_<PERSON><PERSON>", "C++_Time_μs": 275.323067, "Python_Time_μs": 2139.0085419019065, "Speedup": 7.7690858423493685, "Performance_Ratio": "7.77x"}, {"Operator": "ts_<PERSON>", "C++_Time_μs": 15395.5679, "Python_Time_μs": 38260.34925878048, "Speedup": 2.4851534875034056, "Performance_Ratio": "2.49x"}, {"Operator": "ts_Mean", "C++_Time_μs": 4174.096367, "Python_Time_μs": 37881.16478050748, "Speedup": 9.075297130174638, "Performance_Ratio": "9.08x"}, {"Operator": "ts_<PERSON>", "C++_Time_μs": 13387.199033, "Python_Time_μs": 37342.77583037814, "Speedup": 2.7894390557970077, "Performance_Ratio": "2.79x"}, {"Operator": "ts_Stdev", "C++_Time_μs": 6536.641833, "Python_Time_μs": 49363.17211637894, "Speedup": 7.551763333149256, "Performance_Ratio": "7.55x"}, {"Operator": "ts_Sum", "C++_Time_μs": 3018.303333, "Python_Time_μs": 37251.42627954483, "Speedup": 12.341843138250555, "Performance_Ratio": "12.34x"}]}