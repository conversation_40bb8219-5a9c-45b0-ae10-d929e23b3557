Feature Operators 性能比较报告 (<PERSON> vs C++)
============================================================

测试分类: comparison_ops
测试时间: 20250528_151541

测试算子总数: 3
C++平均执行时间: 303.781 μs
Python平均执行时间: 3249.476 μs
平均加速比: 10.84x

性能排名 (按加速比排序):
------------------------------------------------------------
排名   算子                   C++时间(μs)    Python时间(μs)    加速比       
------------------------------------------------------------
1    <USER>                <GROUP>.699      4184.209        14.10     
2    <PERSON>han                296.502      4039.681        13.62     
3    Equal                318.142      1524.538        4.79      

============================================================
分类性能统计:
------------------------------
基本算子 (3个): 平均加速比 10.84x
