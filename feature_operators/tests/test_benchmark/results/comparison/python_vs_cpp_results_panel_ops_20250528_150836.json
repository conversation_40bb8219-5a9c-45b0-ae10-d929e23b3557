{"timestamp": "20250528_150836", "category": "panel_ops", "summary": {"total_operators": 5, "cpp_avg_time": 15260.7196864, "python_avg_time": 43846.81424126028, "avg_speedup": 4.871290654206534, "max_speedup": 11.26735934363916, "min_speedup": 1.1619098710255418}, "detailed_results": [{"Operator": "Tot_Mean", "C++_Time_μs": 4259.2147, "Python_Time_μs": 34210.75042958061, "Speedup": 8.032173261794153, "Performance_Ratio": "8.03x"}, {"Operator": "Tot_Rank", "C++_Time_μs": 55672.514433, "Python_Time_μs": 132168.5202109317, "Speedup": 2.3740354025143247, "Performance_Ratio": "2.37x"}, {"Operator": "Tot_Sum", "C++_Time_μs": 2913.921533, "Python_Time_μs": 32832.2010114789, "Speedup": 11.26735934363916, "Performance_Ratio": "11.27x"}, {"Operator": "pn_Mean", "C++_Time_μs": 1243.805933, "Python_Time_μs": 1445.1903911928337, "Speedup": 1.1619098710255418, "Performance_Ratio": "1.16x"}, {"Operator": "pn_Rank", "C++_Time_μs": 12214.141833, "Python_Time_μs": 18577.40916311741, "Speedup": 1.5209753920594913, "Performance_Ratio": "1.52x"}]}