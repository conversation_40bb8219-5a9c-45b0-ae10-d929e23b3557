{"timestamp": "20250528_150836", "category": "reduction_ops", "summary": {"total_operators": 2, "cpp_avg_time": 302.70358350000004, "python_avg_time": 12027.290121962627, "avg_speedup": 39.734314397793305, "max_speedup": 40.62168161609267, "min_speedup": 38.84694717949394}, "detailed_results": [{"Operator": "Max", "C++_Time_μs": 302.219867, "Python_Time_μs": 12276.679215331873, "Speedup": 40.62168161609267, "Performance_Ratio": "40.62x"}, {"Operator": "Min", "C++_Time_μs": 303.1873, "Python_Time_μs": 11777.901028593382, "Speedup": 38.84694717949394, "Performance_Ratio": "38.85x"}]}