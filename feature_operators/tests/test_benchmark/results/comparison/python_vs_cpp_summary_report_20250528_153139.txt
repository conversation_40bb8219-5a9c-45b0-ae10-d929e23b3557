Feature Operators 性能比较汇总报告 (Python vs C++)
================================================================================

测试时间: 20250528_153139
总测试分类数: 1
总测试算子数: 10

整体性能统计:
--------------------------------------------------
C++总平均执行时间: 3149.013 μs
Python总平均执行时间: 2763.402 μs
总平均加速比: 2.74x
最大加速比: 8.99x (Abs)
最小加速比: 0.02x (Power)

各分类性能汇总:
--------------------------------------------------------------------------------
分类              算子数      C++平均(μs)    Python平均(μs)    平均加速比     
--------------------------------------------------------------------------------
core_math       10       3149.013     2763.402        2.74      

前10名最快的算子 (按加速比排序):
--------------------------------------------------------------------------------
排名   算子                   分类              C++时间(μs)    Python时间(μs)    加速比       
--------------------------------------------------------------------------------
1    <USER>                  <GROUP>       222.810      2003.909        8.99      
2    Sign                 core_math       545.274      2612.104        4.79      
3    Minus                core_math       222.780      586.810         2.63      
4    Multiply             core_math       220.785      579.463         2.62      
5    Add                  core_math       239.423      586.278         2.45      
6    Divide               core_math       373.550      644.788         1.73      
7    Log                  core_math       4855.715     8112.748        1.67      
8    Exp                  core_math       8901.763     11514.055       1.29      
9    Sqrt                 core_math       625.985      729.498         1.17      
10   Power                core_math       15282.046    264.370         0.02      

后10名最慢的算子 (按加速比排序):
--------------------------------------------------------------------------------
排名   算子                   分类              C++时间(μs)    Python时间(μs)    加速比       
--------------------------------------------------------------------------------
1    <USER>                <GROUP>       15282.046    264.370         0.02      
2    Sqrt                 core_math       625.985      729.498         1.17      
3    Exp                  core_math       8901.763     11514.055       1.29      
4    Log                  core_math       4855.715     8112.748        1.67      
5    Divide               core_math       373.550      644.788         1.73      
6    Add                  core_math       239.423      586.278         2.45      
7    Multiply             core_math       220.785      579.463         2.62      
8    Minus                core_math       222.780      586.810         2.63      
9    Sign                 core_math       545.274      2612.104        4.79      
10   Abs                  core_math       222.810      2003.909        8.99      

================================================================================
按分类详细性能列表:
================================================================================

CORE_MATH (10 个算子):
------------------------------------------------------------
算子                   C++时间(μs)    Python时间(μs)    加速比       
------------------------------------------------------------
Abs                  222.810      2003.909        8.99      
Sign                 545.274      2612.104        4.79      
Minus                222.780      586.810         2.63      
Multiply             220.785      579.463         2.62      
Add                  239.423      586.278         2.45      
Divide               373.550      644.788         1.73      
Log                  4855.715     8112.748        1.67      
Exp                  8901.763     11514.055       1.29      
Sqrt                 625.985      729.498         1.17      
Power                15282.046    264.370         0.02      
