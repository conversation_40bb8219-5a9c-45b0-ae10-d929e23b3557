{"timestamp": "20250528_153139", "summary": {"total_categories": 1, "total_operators": 10, "overall_cpp_avg_time": 3149.0130732999996, "overall_python_avg_time": 2763.402458901207, "overall_avg_speedup": 2.736456640699699, "max_speedup": 8.993821421273655, "min_speedup": 0.01729938723615783}, "category_summaries": {"core_math": {"total_operators": 10, "cpp_avg_time": 3149.0130732999996, "python_avg_time": 2763.402458901207, "avg_speedup": 2.736456640699699, "max_speedup": 8.993821421273655, "min_speedup": 0.01729938723615783}}, "detailed_results": [{"Operator": "Abs", "C++_Time_μs": 222.809533, "Python_Time_μs": 2003.9091507593791, "Speedup": 8.993821421273655, "Performance_Ratio": "8.99x", "Category": "core_math"}, {"Operator": "Sign", "C++_Time_μs": 545.2737, "Python_Time_μs": 2612.1042047937713, "Speedup": 4.790445981153632, "Performance_Ratio": "4.79x", "Category": "core_math"}, {"Operator": "Minus", "C++_Time_μs": 222.780033, "Python_Time_μs": 586.8101492524147, "Speedup": 2.6340338555045224, "Performance_Ratio": "2.63x", "Category": "core_math"}, {"Operator": "Multiply", "C++_Time_μs": 220.784667, "Python_Time_μs": 579.4633800784746, "Speedup": 2.624563507747912, "Performance_Ratio": "2.62x", "Category": "core_math"}, {"Operator": "Add", "C++_Time_μs": 239.422833, "Python_Time_μs": 586.2781157096227, "Speedup": 2.4487143033247074, "Performance_Ratio": "2.45x", "Category": "core_math"}, {"Operator": "Divide", "C++_Time_μs": 373.550467, "Python_Time_μs": 644.7884564598402, "Speedup": 1.7261080186518416, "Performance_Ratio": "1.73x", "Category": "core_math"}, {"Operator": "Log", "C++_Time_μs": 4855.7152, "Python_Time_μs": 8112.748463948567, "Speedup": 1.670762828913147, "Performance_Ratio": "1.67x", "Category": "core_math"}, {"Operator": "Exp", "C++_Time_μs": 8901.762833, "Python_Time_μs": 11514.055108030638, "Speedup": 1.293457860430355, "Performance_Ratio": "1.29x", "Category": "core_math"}, {"Operator": "Sqrt", "C++_Time_μs": 625.9851, "Python_Time_μs": 729.4975221157074, "Speedup": 1.1653592427610615, "Performance_Ratio": "1.17x", "Category": "core_math"}, {"Operator": "Power", "C++_Time_μs": 15282.046367, "Python_Time_μs": 264.37003786365193, "Speedup": 0.01729938723615783, "Performance_Ratio": "0.02x", "Category": "core_math"}]}