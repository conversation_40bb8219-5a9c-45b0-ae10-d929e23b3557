{"timestamp": "20250528_153638", "summary": {"total_categories": 8, "total_operators": 60, "overall_cpp_avg_time": 6951.579669466665, "overall_python_avg_time": 26749.455849640068, "overall_avg_speedup": 8.793587228360154, "max_speedup": 41.63727431679413, "min_speedup": 0.016934123229058}, "category_summaries": {"core_math": {"total_operators": 17, "cpp_avg_time": 2166.3175863529414, "python_avg_time": 2374.6284729271542, "avg_speedup": 2.3107239028169797, "max_speedup": 8.983203028065619, "min_speedup": 0.016934123229058}, "logical_ops": {"total_operators": 4, "cpp_avg_time": 471.5553, "python_avg_time": 12914.643017575147, "avg_speedup": 27.28364530416296, "max_speedup": 35.1705281594802, "min_speedup": 23.994159395332268}, "comparison_ops": {"total_operators": 6, "cpp_avg_time": 297.24850533333336, "python_avg_time": 3634.0561902357476, "avg_speedup": 12.445505302502573, "max_speedup": 15.622329202187728, "min_speedup": 4.747991848975151}, "data_utils": {"total_operators": 3, "cpp_avg_time": 125.14547799999998, "python_avg_time": 1612.2430459492737, "avg_speedup": 14.56713664832643, "max_speedup": 20.28234766761957, "min_speedup": 3.8141853331630697}, "reduction_ops": {"total_operators": 2, "cpp_avg_time": 300.28923299999997, "python_avg_time": 12348.18605395655, "avg_speedup": 41.12308460848871, "max_speedup": 41.63727431679413, "min_speedup": 40.6088949001833}, "timeseries_ops": {"total_operators": 14, "cpp_avg_time": 13281.016723928571, "python_avg_time": 54564.407064268984, "avg_speedup": 8.26666605767964, "max_speedup": 37.99709411040221, "min_speedup": 2.1123420907880868}, "panel_ops": {"total_operators": 11, "cpp_avg_time": 13151.902915090906, "python_avg_time": 26307.62844769792, "avg_speedup": 2.562589405516698, "max_speedup": 10.126882002303278, "min_speedup": 0.4513269407966363}, "group_ops": {"total_operators": 3, "cpp_avg_time": 15005.495955666667, "python_avg_time": 136105.68155224124, "avg_speedup": 11.551975630632638, "max_speedup": 17.464916989693073, "min_speedup": 7.151145409953862}}, "detailed_results": [{"Operator": "Abs", "C++_Time_μs": 223.0729, "Python_Time_μs": 2003.9091507593791, "Speedup": 8.983203028065619, "Performance_Ratio": "8.98x", "Category": "core_math"}, {"Operator": "Sign", "C++_Time_μs": 546.204667, "Python_Time_μs": 2612.1042047937713, "Speedup": 4.782281006752679, "Performance_Ratio": "4.78x", "Category": "core_math"}, {"Operator": "SignedPower", "C++_Time_μs": 2545.472767, "Python_Time_μs": 8250.358421355486, "Speedup": 3.241189035025133, "Performance_Ratio": "3.24x", "Category": "core_math"}, {"Operator": "Softsign", "C++_Time_μs": 1047.3517, "Python_Time_μs": 3136.102482676506, "Speedup": 2.994316505789322, "Performance_Ratio": "2.99x", "Category": "core_math"}, {"Operator": "Minus", "C++_Time_μs": 219.663667, "Python_Time_μs": 586.8101492524147, "Speedup": 2.6714028645092895, "Performance_Ratio": "2.67x", "Category": "core_math"}, {"Operator": "Multiply", "C++_Time_μs": 219.221867, "Python_Time_μs": 579.4633800784746, "Speedup": 2.6432736296259836, "Performance_Ratio": "2.64x", "Category": "core_math"}, {"Operator": "Divide", "C++_Time_μs": 373.962933, "Python_Time_μs": 644.7884564598402, "Speedup": 1.7242041912743264, "Performance_Ratio": "1.72x", "Category": "core_math"}, {"Operator": "Log", "C++_Time_μs": 4958.1761, "Python_Time_μs": 8112.748463948567, "Speedup": 1.6362364507280345, "Performance_Ratio": "1.64x", "Category": "core_math"}, {"Operator": "Reverse", "C++_Time_μs": 163.956767, "Python_Time_μs": 261.7303592463334, "Speedup": 1.5963376445836688, "Performance_Ratio": "1.60x", "Category": "core_math"}, {"Operator": "Add", "C++_Time_μs": 389.0801, "Python_Time_μs": 586.2781157096227, "Speedup": 1.506831410060866, "Performance_Ratio": "1.51x", "Category": "core_math"}, {"Operator": "Ceil", "C++_Time_μs": 162.149833, "Python_Time_μs": 213.530162970225, "Speedup": 1.3168694596819288, "Performance_Ratio": "1.32x", "Category": "core_math"}, {"Operator": "Floor", "C++_Time_μs": 165.172, "Python_Time_μs": 211.64420371254286, "Speedup": 1.2813564267099924, "Performance_Ratio": "1.28x", "Category": "core_math"}, {"Operator": "Exp", "C++_Time_μs": 9036.217633, "Python_Time_μs": 11514.055108030638, "Speedup": 1.2742117969781568, "Performance_Ratio": "1.27x", "Category": "core_math"}, {"Operator": "inv", "C++_Time_μs": 371.9485, "Python_Time_μs": 458.4168704847495, "Speedup": 1.2324740400478815, "Performance_Ratio": "1.23x", "Category": "core_math"}, {"Operator": "Round", "C++_Time_μs": 166.4466, "Python_Time_μs": 202.87695030371347, "Speedup": 1.2188710992216931, "Performance_Ratio": "1.22x", "Category": "core_math"}, {"Operator": "Sqrt", "C++_Time_μs": 627.625367, "Python_Time_μs": 729.4975221157074, "Speedup": 1.1623136356050239, "Performance_Ratio": "1.16x", "Category": "core_math"}, {"Operator": "Power", "C++_Time_μs": 15611.675567, "Python_Time_μs": 264.37003786365193, "Speedup": 0.016934123229058, "Performance_Ratio": "0.02x", "Category": "core_math"}, {"Operator": "<PERSON><PERSON>", "C++_Time_μs": 499.822333, "Python_Time_μs": 17579.01543751359, "Speedup": 35.1705281594802, "Performance_Ratio": "35.17x", "Category": "logical_ops"}, {"Operator": "Not", "C++_Time_μs": 373.488967, "Python_Time_μs": 9498.085112621387, "Speedup": 25.43069796388761, "Performance_Ratio": "25.43x", "Category": "logical_ops"}, {"Operator": "And", "C++_Time_μs": 509.232, "Python_Time_μs": 12496.143703659376, "Speedup": 24.53919569795177, "Performance_Ratio": "24.54x", "Category": "logical_ops"}, {"Operator": "Or", "C++_Time_μs": 503.6779, "Python_Time_μs": 12085.327816506227, "Speedup": 23.994159395332268, "Performance_Ratio": "23.99x", "Category": "logical_ops"}, {"Operator": "<PERSON><PERSON><PERSON>", "C++_Time_μs": 272.557433, "Python_Time_μs": 4257.9819448292255, "Speedup": 15.622329202187728, "Performance_Ratio": "15.62x", "Category": "comparison_ops"}, {"Operator": "<PERSON><PERSON>", "C++_Time_μs": 272.745933, "Python_Time_μs": 4215.3034048775835, "Speedup": 15.455055034230643, "Performance_Ratio": "15.46x", "Category": "comparison_ops"}, {"Operator": "<PERSON><PERSON>", "C++_Time_μs": 297.733433, "Python_Time_μs": 4245.473816990852, "Speedup": 14.25931167424806, "Performance_Ratio": "14.26x", "Category": "comparison_ops"}, {"Operator": "Mthan", "C++_Time_μs": 297.8421, "Python_Time_μs": 4198.314591000478, "Speedup": 14.095772864213883, "Performance_Ratio": "14.10x", "Category": "comparison_ops"}, {"Operator": "UnEqual", "C++_Time_μs": 319.6311, "Python_Time_μs": 3353.75207165877, "Speedup": 10.492571191159966, "Performance_Ratio": "10.49x", "Category": "comparison_ops"}, {"Operator": "Equal", "C++_Time_μs": 322.981033, "Python_Time_μs": 1533.5113120575745, "Speedup": 4.747991848975151, "Performance_Ratio": "4.75x", "Category": "comparison_ops"}, {"Operator": "get<PERSON><PERSON>", "C++_Time_μs": 103.9702, "Python_Time_μs": 2108.7597434719405, "Speedup": 20.28234766761957, "Performance_Ratio": "20.28x", "Category": "data_utils"}, {"Operator": "getInf", "C++_Time_μs": 107.186367, "Python_Time_μs": 2101.375535130501, "Speedup": 19.60487694419665, "Performance_Ratio": "19.60x", "Category": "data_utils"}, {"Operator": "FilterInf", "C++_Time_μs": 164.279867, "Python_Time_μs": 626.5938592453798, "Speedup": 3.8141853331630697, "Performance_Ratio": "3.81x", "Category": "data_utils"}, {"Operator": "Max", "C++_Time_μs": 299.057233, "Python_Time_μs": 12451.928046842417, "Speedup": 41.63727431679413, "Performance_Ratio": "41.64x", "Category": "reduction_ops"}, {"Operator": "Min", "C++_Time_μs": 301.521233, "Python_Time_μs": 12244.44406107068, "Speedup": 40.6088949001833, "Performance_Ratio": "40.61x", "Category": "reduction_ops"}, {"Operator": "ts_<PERSON>rr", "C++_Time_μs": 5127.0262, "Python_Time_μs": 194812.09702789783, "Speedup": 37.99709411040221, "Performance_Ratio": "38.00x", "Category": "timeseries_ops"}, {"Operator": "ts_Sum", "C++_Time_μs": 3011.292967, "Python_Time_μs": 36105.62747344375, "Speedup": 11.990074652023637, "Performance_Ratio": "11.99x", "Category": "timeseries_ops"}, {"Operator": "ts_<PERSON>", "C++_Time_μs": 289.9912, "Python_Time_μs": 3215.3268344700336, "Speedup": 11.087670365411205, "Performance_Ratio": "11.09x", "Category": "timeseries_ops"}, {"Operator": "ts_Mean", "C++_Time_μs": 4171.141667, "Python_Time_μs": 36317.55985940496, "Speedup": 8.70686319448976, "Performance_Ratio": "8.71x", "Category": "timeseries_ops"}, {"Operator": "ts_<PERSON><PERSON>", "C++_Time_μs": 278.434, "Python_Time_μs": 2133.612179507812, "Speedup": 7.662901008884734, "Performance_Ratio": "7.66x", "Category": "timeseries_ops"}, {"Operator": "ts_Stdev", "C++_Time_μs": 6595.8135, "Python_Time_μs": 46166.97129483024, "Speedup": 6.999435519944619, "Performance_Ratio": "7.00x", "Category": "timeseries_ops"}, {"Operator": "ts_ChgRate", "C++_Time_μs": 786.106033, "Python_Time_μs": 4831.009637564421, "Speedup": 6.145493654498414, "Performance_Ratio": "6.15x", "Category": "timeseries_ops"}, {"Operator": "ts_<PERSON><PERSON><PERSON>", "C++_Time_μs": 9299.2062, "Python_Time_μs": 50039.38268249234, "Speedup": 5.381038080701161, "Performance_Ratio": "5.38x", "Category": "timeseries_ops"}, {"Operator": "ts_<PERSON><PERSON><PERSON>", "C++_Time_μs": 9550.833867, "Python_Time_μs": 50531.2484378616, "Speedup": 5.290768234641475, "Performance_Ratio": "5.29x", "Category": "timeseries_ops"}, {"Operator": "ts_Divide", "C++_Time_μs": 751.273, "Python_Time_μs": 3775.91239909331, "Speedup": 5.026019035814291, "Performance_Ratio": "5.03x", "Category": "timeseries_ops"}, {"Operator": "ts_<PERSON>", "C++_Time_μs": 13408.326767, "Python_Time_μs": 35213.95611266295, "Speedup": 2.626275203803209, "Performance_Ratio": "2.63x", "Category": "timeseries_ops"}, {"Operator": "ts_Rank", "C++_Time_μs": 53218.529667, "Python_Time_μs": 130781.7118242383, "Speedup": 2.4574469201341733, "Performance_Ratio": "2.46x", "Category": "timeseries_ops"}, {"Operator": "ts_<PERSON>", "C++_Time_μs": 15699.3023, "Python_Time_μs": 35321.90319771568, "Speedup": 2.249902735977998, "Performance_Ratio": "2.25x", "Category": "timeseries_ops"}, {"Operator": "ts_Median", "C++_Time_μs": 63746.956767, "Python_Time_μs": 134655.37993858257, "Speedup": 2.1123420907880868, "Performance_Ratio": "2.11x", "Category": "timeseries_ops"}, {"Operator": "Tot_Sum", "C++_Time_μs": 3011.312733, "Python_Time_μs": 30495.208719124395, "Speedup": 10.126882002303278, "Performance_Ratio": "10.13x", "Category": "panel_ops"}, {"Operator": "Tot_Mean", "C++_Time_μs": 4175.855567, "Python_Time_μs": 30900.31451235215, "Speedup": 7.399756532899298, "Performance_Ratio": "7.40x", "Category": "panel_ops"}, {"Operator": "Tot_Rank", "C++_Time_μs": 59503.598133, "Python_Time_μs": 130358.49509760737, "Speedup": 2.1907665954289928, "Performance_Ratio": "2.19x", "Category": "panel_ops"}, {"Operator": "pn_Rank2", "C++_Time_μs": 11337.0367, "Python_Time_μs": 16410.36355867982, "Speedup": 1.4475002589239054, "Performance_Ratio": "1.45x", "Category": "panel_ops"}, {"Operator": "pn_Rank", "C++_Time_μs": 12378.7844, "Python_Time_μs": 17914.629448205233, "Speedup": 1.4472042544181667, "Performance_Ratio": "1.45x", "Category": "panel_ops"}, {"Operator": "pn_TransNorm", "C++_Time_μs": 26569.4614, "Python_Time_μs": 37161.76108767589, "Speedup": 1.3986644489404625, "Performance_Ratio": "1.40x", "Category": "panel_ops"}, {"Operator": "pn_RankCentered", "C++_Time_μs": 14028.1707, "Python_Time_μs": 18725.976285835106, "Speedup": 1.3348836912738098, "Performance_Ratio": "1.33x", "Category": "panel_ops"}, {"Operator": "pn_Mean", "C++_Time_μs": 1238.3571, "Python_Time_μs": 1489.3182553350925, "Speedup": 1.2026565320577502, "Performance_Ratio": "1.20x", "Category": "panel_ops"}, {"Operator": "pn_FillMin", "C++_Time_μs": 1111.6486, "Python_Time_μs": 664.5705240468184, "Speedup": 0.597824280124869, "Performance_Ratio": "0.60x", "Category": "panel_ops"}, {"Operator": "pn_FillMax", "C++_Time_μs": 1114.8952, "Python_Time_μs": 658.9230460425218, "Speedup": 0.591017923516508, "Performance_Ratio": "0.59x", "Category": "panel_ops"}, {"Operator": "pn_Stand", "C++_Time_μs": 10201.811533, "Python_Time_μs": 4604.352389772733, "Speedup": 0.4513269407966363, "Performance_Ratio": "0.45x", "Category": "panel_ops"}, {"Operator": "pn_GroupNeutral", "C++_Time_μs": 4592.410367, "Python_Time_μs": 80206.06584226091, "Speedup": 17.464916989693073, "Performance_Ratio": "17.46x", "Category": "group_ops"}, {"Operator": "pn_GroupRank", "C++_Time_μs": 13512.052033, "Python_Time_μs": 135659.17142356434, "Speedup": 10.03986449225098, "Performance_Ratio": "10.04x", "Category": "group_ops"}, {"Operator": "pn_GroupNorm", "C++_Time_μs": 26912.025467, "Python_Time_μs": 192451.80739089847, "Speedup": 7.151145409953862, "Performance_Ratio": "7.15x", "Category": "group_ops"}]}