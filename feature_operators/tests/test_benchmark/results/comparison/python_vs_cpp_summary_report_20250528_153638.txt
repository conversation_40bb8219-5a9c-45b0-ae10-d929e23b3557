Feature Operators 性能比较汇总报告 (Python vs C++)
================================================================================

测试时间: 20250528_153638
总测试分类数: 8
总测试算子数: 60

整体性能统计:
--------------------------------------------------
C++总平均执行时间: 6951.580 μs
Python总平均执行时间: 26749.456 μs
总平均加速比: 8.79x
最大加速比: 41.64x (Max)
最小加速比: 0.02x (Power)

各分类性能汇总:
--------------------------------------------------------------------------------
分类              算子数      C++平均(μs)    Python平均(μs)    平均加速比     
--------------------------------------------------------------------------------
core_math       17       2166.318     2374.628        2.31      
logical_ops     4        471.555      12914.643       27.28     
comparison_ops  6        297.249      3634.056        12.45     
data_utils      3        125.145      1612.243        14.57     
reduction_ops   2        300.289      12348.186       41.12     
timeseries_ops  14       13281.017    54564.407       8.27      
panel_ops       11       13151.903    26307.628       2.56      
group_ops       3        15005.496    136105.682      11.55     

前10名最快的算子 (按加速比排序):
--------------------------------------------------------------------------------
排名   算子                   分类              C++时间(μs)    Python时间(μs)    加速比       
--------------------------------------------------------------------------------
1    <USER>                  <GROUP>   299.057      12451.928       41.64     
2    Min                  reduction_ops   301.521      12244.444       40.61     
3    ts_Corr              timeseries_ops  5127.026     194812.097      38.00     
4    Xor                  logical_ops     499.822      17579.015       35.17     
5    Not                  logical_ops     373.489      9498.085        25.43     
6    And                  logical_ops     509.232      12496.144       24.54     
7    Or                   logical_ops     503.678      12085.328       23.99     
8    getNan               data_utils      103.970      2108.760        20.28     
9    getInf               data_utils      107.186      2101.376        19.60     
10   pn_GroupNeutral      group_ops       4592.410     80206.066       17.46     

后10名最慢的算子 (按加速比排序):
--------------------------------------------------------------------------------
排名   算子                   分类              C++时间(μs)    Python时间(μs)    加速比       
--------------------------------------------------------------------------------
1    <USER>                <GROUP>       15611.676    264.370         0.02      
2    pn_Stand             panel_ops       10201.812    4604.352        0.45      
3    pn_FillMax           panel_ops       1114.895     658.923         0.59      
4    pn_FillMin           panel_ops       1111.649     664.571         0.60      
5    Sqrt                 core_math       627.625      729.498         1.16      
6    pn_Mean              panel_ops       1238.357     1489.318        1.20      
7    Round                core_math       166.447      202.877         1.22      
8    inv                  core_math       371.949      458.417         1.23      
9    Exp                  core_math       9036.218     11514.055       1.27      
10   Floor                core_math       165.172      211.644         1.28      

================================================================================
按分类详细性能列表:
================================================================================

COMPARISON_OPS (6 个算子):
------------------------------------------------------------
算子                   C++时间(μs)    Python时间(μs)    加速比       
------------------------------------------------------------
LEthan               272.557      4257.982        15.62     
MEthan               272.746      4215.303        15.46     
Lthan                297.733      4245.474        14.26     
Mthan                297.842      4198.315        14.10     
UnEqual              319.631      3353.752        10.49     
Equal                322.981      1533.511        4.75      

CORE_MATH (17 个算子):
------------------------------------------------------------
算子                   C++时间(μs)    Python时间(μs)    加速比       
------------------------------------------------------------
Abs                  223.073      2003.909        8.98      
Sign                 546.205      2612.104        4.78      
SignedPower          2545.473     8250.358        3.24      
Softsign             1047.352     3136.102        2.99      
Minus                219.664      586.810         2.67      
Multiply             219.222      579.463         2.64      
Divide               373.963      644.788         1.72      
Log                  4958.176     8112.748        1.64      
Reverse              163.957      261.730         1.60      
Add                  389.080      586.278         1.51      
Ceil                 162.150      213.530         1.32      
Floor                165.172      211.644         1.28      
Exp                  9036.218     11514.055       1.27      
inv                  371.949      458.417         1.23      
Round                166.447      202.877         1.22      
Sqrt                 627.625      729.498         1.16      
Power                15611.676    264.370         0.02      

DATA_UTILS (3 个算子):
------------------------------------------------------------
算子                   C++时间(μs)    Python时间(μs)    加速比       
------------------------------------------------------------
getNan               103.970      2108.760        20.28     
getInf               107.186      2101.376        19.60     
FilterInf            164.280      626.594         3.81      

GROUP_OPS (3 个算子):
------------------------------------------------------------
算子                   C++时间(μs)    Python时间(μs)    加速比       
------------------------------------------------------------
pn_GroupNeutral      4592.410     80206.066       17.46     
pn_GroupRank         13512.052    135659.171      10.04     
pn_GroupNorm         26912.025    192451.807      7.15      

LOGICAL_OPS (4 个算子):
------------------------------------------------------------
算子                   C++时间(μs)    Python时间(μs)    加速比       
------------------------------------------------------------
Xor                  499.822      17579.015       35.17     
Not                  373.489      9498.085        25.43     
And                  509.232      12496.144       24.54     
Or                   503.678      12085.328       23.99     

PANEL_OPS (11 个算子):
------------------------------------------------------------
算子                   C++时间(μs)    Python时间(μs)    加速比       
------------------------------------------------------------
Tot_Sum              3011.313     30495.209       10.13     
Tot_Mean             4175.856     30900.315       7.40      
Tot_Rank             59503.598    130358.495      2.19      
pn_Rank2             11337.037    16410.364       1.45      
pn_Rank              12378.784    17914.629       1.45      
pn_TransNorm         26569.461    37161.761       1.40      
pn_RankCentered      14028.171    18725.976       1.33      
pn_Mean              1238.357     1489.318        1.20      
pn_FillMin           1111.649     664.571         0.60      
pn_FillMax           1114.895     658.923         0.59      
pn_Stand             10201.812    4604.352        0.45      

REDUCTION_OPS (2 个算子):
------------------------------------------------------------
算子                   C++时间(μs)    Python时间(μs)    加速比       
------------------------------------------------------------
Max                  299.057      12451.928       41.64     
Min                  301.521      12244.444       40.61     

TIMESERIES_OPS (14 个算子):
------------------------------------------------------------
算子                   C++时间(μs)    Python时间(μs)    加速比       
------------------------------------------------------------
ts_Corr              5127.026     194812.097      38.00     
ts_Sum               3011.293     36105.627       11.99     
ts_Delta             289.991      3215.327        11.09     
ts_Mean              4171.142     36317.560       8.71      
ts_Delay             278.434      2133.612        7.66      
ts_Stdev             6595.814     46166.971       7.00      
ts_ChgRate           786.106      4831.010        6.15      
ts_Argmax            9299.206     50039.383       5.38      
ts_Argmin            9550.834     50531.248       5.29      
ts_Divide            751.273      3775.912        5.03      
ts_Min               13408.327    35213.956       2.63      
ts_Rank              53218.530    130781.712      2.46      
ts_Max               15699.302    35321.903       2.25      
ts_Median            63746.957    134655.380      2.11      
