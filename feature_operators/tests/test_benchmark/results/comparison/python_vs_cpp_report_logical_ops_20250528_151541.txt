Feature Operators 性能比较报告 (<PERSON> vs C++)
============================================================

测试分类: logical_ops
测试时间: 20250528_151541

测试算子总数: 4
C++平均执行时间: 618.554 μs
Python平均执行时间: 12660.398 μs
平均加速比: 21.44x

性能排名 (按加速比排序):
------------------------------------------------------------
排名   算子                   C++时间(μs)    Python时间(μs)    加速比       
------------------------------------------------------------
1    <USER>                  <GROUP>.376      16795.036       33.56     
2    Not                  491.777      9534.242        19.39     
3    And                  735.802      12428.527       16.89     
4    Or                   746.263      11883.786       15.92     

============================================================
分类性能统计:
------------------------------
基本算子 (4个): 平均加速比 21.44x
