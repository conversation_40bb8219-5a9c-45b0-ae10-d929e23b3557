Feature Operators 性能比较报告 (Python vs C++)
============================================================

测试分类: core_math
测试时间: 20250528_151541

测试算子总数: 10
C++平均执行时间: 3144.063 μs
Python平均执行时间: 2761.907 μs
平均加速比: 2.77x

性能排名 (按加速比排序):
------------------------------------------------------------
排名   算子                   C++时间(μs)    Python时间(μs)    加速比       
------------------------------------------------------------
1    <USER>                  <GROUP>.303      1975.259        8.85      
2    Sign                 545.826      2597.012        4.76      
3    Minus                222.528      624.346         2.81      
4    Multiply             223.255      618.216         2.77      
5    Add                  242.304      624.054         2.58      
6    Divide               374.879      653.181         1.74      
7    Log                  4852.183     7983.912        1.65      
8    Exp                  8860.613     11479.301       1.30      
9    Sqrt                 628.859      803.934         1.28      
10   Power                15266.881    259.859         0.02      

============================================================
分类性能统计:
------------------------------
基本算子 (10个): 平均加速比 2.77x
