Feature Operators 性能比较报告 (<PERSON> vs C++)
============================================================

测试分类: reduction_ops
测试时间: 20250528_151541

测试算子总数: 2
C++平均执行时间: 301.632 μs
Python平均执行时间: 12214.666 μs
平均加速比: 40.50x

性能排名 (按加速比排序):
------------------------------------------------------------
排名   算子                   C++时间(μs)    Python时间(μs)    加速比       
------------------------------------------------------------
1    <USER>                  <GROUP>.032      12276.726       40.92     
2    Min                  303.233      12152.605       40.08     

============================================================
分类性能统计:
------------------------------
基本算子 (2个): 平均加速比 40.50x
