{"timestamp": "20250528_150836", "category": "core_math", "summary": {"total_operators": 10, "cpp_avg_time": 3170.9208000000003, "python_avg_time": 2802.60715012749, "avg_speedup": 2.855310794170296, "max_speedup": 9.380579757328254, "min_speedup": 0.019422740650423577}, "detailed_results": [{"Operator": "Abs", "C++_Time_μs": 222.6187, "Python_Time_μs": 2088.2924708227315, "Speedup": 9.380579757328254, "Performance_Ratio": "9.38x"}, {"Operator": "Add", "C++_Time_μs": 241.1027, "Python_Time_μs": 635.4639306664467, "Speedup": 2.6356566337351124, "Performance_Ratio": "2.64x"}, {"Operator": "Divide", "C++_Time_μs": 373.083733, "Python_Time_μs": 659.2451905210813, "Speedup": 1.7670167102168493, "Performance_Ratio": "1.77x"}, {"Operator": "Exp", "C++_Time_μs": 9034.716667, "Python_Time_μs": 11524.741910398006, "Speedup": 1.2756063455197233, "Performance_Ratio": "1.28x"}, {"Operator": "Log", "C++_Time_μs": 4912.146067, "Python_Time_μs": 8118.646312505007, "Speedup": 1.6527697266672114, "Performance_Ratio": "1.65x"}, {"Operator": "Minus", "C++_Time_μs": 223.274267, "Python_Time_μs": 642.5734919806322, "Speedup": 2.8779558908176024, "Performance_Ratio": "2.88x"}, {"Operator": "Multiply", "C++_Time_μs": 223.0571, "Python_Time_μs": 631.1078555881977, "Speedup": 2.829355602615643, "Performance_Ratio": "2.83x"}, {"Operator": "Power", "C++_Time_μs": 15305.5844, "Python_Time_μs": 297.276396304369, "Speedup": 0.019422740650423577, "Performance_Ratio": "0.02x"}, {"Operator": "Sign", "C++_Time_μs": 544.709933, "Python_Time_μs": 2697.0572769641876, "Speedup": 4.95136422813165, "Performance_Ratio": "4.95x"}, {"Operator": "Sqrt", "C++_Time_μs": 628.914433, "Python_Time_μs": 731.6666655242443, "Speedup": 1.1633803060204921, "Performance_Ratio": "1.16x"}]}