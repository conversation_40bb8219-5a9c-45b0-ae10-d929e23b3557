{"timestamp": "20250528_151541", "category": "logical_ops", "summary": {"total_operators": 4, "cpp_avg_time": 618.55447475, "python_avg_time": 12660.398008301854, "avg_speedup": 21.441925817784668, "max_speedup": 33.56485656832356, "min_speedup": 15.924393547648881}, "detailed_results": [{"Operator": "And", "C++_Time_μs": 735.802233, "Python_Time_μs": 12428.527139127254, "Speedup": 16.89112451922561, "Performance_Ratio": "16.89x"}, {"Operator": "Not", "C++_Time_μs": 491.777, "Python_Time_μs": 9534.242314596971, "Speedup": 19.387328635940623, "Performance_Ratio": "19.39x"}, {"Operator": "Or", "C++_Time_μs": 746.263033, "Python_Time_μs": 11883.786227554083, "Speedup": 15.924393547648881, "Performance_Ratio": "15.92x"}, {"Operator": "<PERSON><PERSON>", "C++_Time_μs": 500.375633, "Python_Time_μs": 16795.03635192911, "Speedup": 33.56485656832356, "Performance_Ratio": "33.56x"}]}