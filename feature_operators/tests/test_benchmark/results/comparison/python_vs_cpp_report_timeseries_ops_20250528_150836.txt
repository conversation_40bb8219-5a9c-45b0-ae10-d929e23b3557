Feature Operators 性能比较报告 (Python vs C++)
============================================================

测试分类: timeseries_ops
测试时间: 20250528_150836

测试算子总数: 7
C++平均执行时间: 6845.701 μs
Python平均执行时间: 58980.712 μs
平均加速比: 11.86x

性能排名 (按加速比排序):
------------------------------------------------------------
排名   算子                   C++时间(μs)    Python时间(μs)    加速比       
------------------------------------------------------------
1    <USER>              <GROUP>.095     208181.398      41.34     
2    ts_Sum               3213.486     38004.914       11.83     
3    ts_Mean              4331.995     39351.280       9.08      
4    ts_Delay             270.881      2115.517        7.81      
5    ts_Stdev             6525.132     49951.724       7.66      
6    ts_Max               13169.746    37771.338       2.87      
7    ts_Min               15372.573    37488.815       2.44      

============================================================
分类性能统计:
------------------------------
时间序列算子 (7个): 平均加速比 11.86x
