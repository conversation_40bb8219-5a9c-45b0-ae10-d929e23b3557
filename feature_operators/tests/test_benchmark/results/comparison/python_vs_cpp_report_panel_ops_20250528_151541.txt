Feature Operators 性能比较报告 (<PERSON> vs C++)
============================================================

测试分类: panel_ops
测试时间: 20250528_151541

测试算子总数: 5
C++平均执行时间: 15997.851 μs
Python平均执行时间: 45180.231 μs
平均加速比: 4.86x

性能排名 (按加速比排序):
------------------------------------------------------------
排名   算子                   C++时间(μs)    Python时间(μs)    加速比       
------------------------------------------------------------
1    <USER>              <GROUP>.414     32723.802       10.92     
2    Tot_Mean             4152.349     33043.432       7.96      
3    Tot_Rank             59044.670    134631.180      2.28      
4    pn_Rank              12550.364    23998.000       1.91      
5    pn_Mean              1245.458     1504.742        1.21      

============================================================
分类性能统计:
------------------------------
面板算子 (2个): 平均加速比 1.56x
Tot算子 (3个): 平均加速比 7.05x
