Feature Operators 性能比较报告 (<PERSON> vs C++)
============================================================

测试分类: group_ops
测试时间: 20250528_151541

测试算子总数: 2
C++平均执行时间: 20190.120 μs
Python平均执行时间: 167995.103 μs
平均加速比: 8.79x

性能排名 (按加速比排序):
------------------------------------------------------------
排名   算子                   C++时间(μs)    Python时间(μs)    加速比       
------------------------------------------------------------
1    <USER>         <GROUP>.357    138014.337      10.21     
2    pn_GroupNorm         26863.882    197975.870      7.37      

============================================================
分类性能统计:
------------------------------
面板算子 (2个): 平均加速比 8.79x
