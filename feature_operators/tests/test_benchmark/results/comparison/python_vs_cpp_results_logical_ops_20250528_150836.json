{"timestamp": "20250528_150836", "category": "logical_ops", "summary": {"total_operators": 4, "cpp_avg_time": 469.7360585, "python_avg_time": 12961.759045720102, "avg_speedup": 27.611114923212135, "max_speedup": 33.78710213628535, "min_speedup": 23.78815218983495}, "detailed_results": [{"Operator": "And", "C++_Time_μs": 504.241967, "Python_Time_μs": 12447.362703581652, "Speedup": 24.685296976841382, "Performance_Ratio": "24.69x"}, {"Operator": "Not", "C++_Time_μs": 364.973833, "Python_Time_μs": 10286.389073977867, "Speedup": 28.183908389886863, "Performance_Ratio": "28.18x"}, {"Operator": "Or", "C++_Time_μs": 500.303867, "Python_Time_μs": 11901.304529358944, "Speedup": 23.78815218983495, "Performance_Ratio": "23.79x"}, {"Operator": "<PERSON><PERSON>", "C++_Time_μs": 509.424567, "Python_Time_μs": 17211.97987596194, "Speedup": 33.78710213628535, "Performance_Ratio": "33.79x"}]}