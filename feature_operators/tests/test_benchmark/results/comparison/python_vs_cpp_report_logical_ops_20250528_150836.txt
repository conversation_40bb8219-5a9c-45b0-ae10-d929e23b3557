Feature Operators 性能比较报告 (<PERSON> vs C++)
============================================================

测试分类: logical_ops
测试时间: 20250528_150836

测试算子总数: 4
C++平均执行时间: 469.736 μs
Python平均执行时间: 12961.759 μs
平均加速比: 27.61x

性能排名 (按加速比排序):
------------------------------------------------------------
排名   算子                   C++时间(μs)    Python时间(μs)    加速比       
------------------------------------------------------------
1    <USER>                  <GROUP>.425      17211.980       33.79     
2    Not                  364.974      10286.389       28.18     
3    And                  504.242      12447.363       24.69     
4    Or                   500.304      11901.305       23.79     

============================================================
分类性能统计:
------------------------------
基本算子 (4个): 平均加速比 27.61x
