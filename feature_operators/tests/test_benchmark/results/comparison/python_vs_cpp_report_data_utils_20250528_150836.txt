Feature Operators 性能比较报告 (<PERSON> vs C++)
============================================================

测试分类: data_utils
测试时间: 20250528_150836

测试算子总数: 3
C++平均执行时间: 126.817 μs
Python平均执行时间: 1611.315 μs
平均加速比: 14.35x

性能排名 (按加速比排序):
------------------------------------------------------------
排名   算子                   C++时间(μs)    Python时间(μs)    加速比       
------------------------------------------------------------
1    <USER>               <GROUP>.650      2099.073        19.68     
2    getNan               107.401      2097.272        19.53     
3    FilterInf            166.401      637.599         3.83      

============================================================
分类性能统计:
------------------------------
基本算子 (3个): 平均加速比 14.35x
