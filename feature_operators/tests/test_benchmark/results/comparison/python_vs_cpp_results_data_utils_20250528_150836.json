{"timestamp": "20250528_150836", "category": "data_utils", "summary": {"total_operators": 3, "cpp_avg_time": 126.81738899999999, "python_avg_time": 1611.3145276904104, "avg_speedup": 14.347026574043184, "max_speedup": 19.681835642561296, "min_speedup": 3.8316948006993807}, "detailed_results": [{"Operator": "FilterInf", "C++_Time_μs": 166.401233, "Python_Time_μs": 637.5987393160661, "Speedup": 3.8316948006993807, "Performance_Ratio": "3.83x"}, {"Operator": "getInf", "C++_Time_μs": 106.650267, "Python_Time_μs": 2099.073026329279, "Speedup": 19.681835642561296, "Performance_Ratio": "19.68x"}, {"Operator": "get<PERSON><PERSON>", "C++_Time_μs": 107.400667, "Python_Time_μs": 2097.2718174258866, "Speedup": 19.52754927886888, "Performance_Ratio": "19.53x"}]}