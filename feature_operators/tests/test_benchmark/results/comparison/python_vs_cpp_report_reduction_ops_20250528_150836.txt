Feature Operators 性能比较报告 (<PERSON> vs C++)
============================================================

测试分类: reduction_ops
测试时间: 20250528_150836

测试算子总数: 2
C++平均执行时间: 302.704 μs
Python平均执行时间: 12027.290 μs
平均加速比: 39.73x

性能排名 (按加速比排序):
------------------------------------------------------------
排名   算子                   C++时间(μs)    Python时间(μs)    加速比       
------------------------------------------------------------
1    <USER>                  <GROUP>.220      12276.679       40.62     
2    Min                  303.187      11777.901       38.85     

============================================================
分类性能统计:
------------------------------
基本算子 (2个): 平均加速比 39.73x
