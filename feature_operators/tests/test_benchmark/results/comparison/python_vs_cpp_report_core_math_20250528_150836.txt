Feature Operators 性能比较报告 (Python vs C++)
============================================================

测试分类: core_math
测试时间: 20250528_150836

测试算子总数: 10
C++平均执行时间: 3170.921 μs
Python平均执行时间: 2802.607 μs
平均加速比: 2.86x

性能排名 (按加速比排序):
------------------------------------------------------------
排名   算子                   C++时间(μs)    Python时间(μs)    加速比       
------------------------------------------------------------
1    <USER>                  <GROUP>.619      2088.292        9.38      
2    Sign                 544.710      2697.057        4.95      
3    Minus                223.274      642.573         2.88      
4    Multiply             223.057      631.108         2.83      
5    Add                  241.103      635.464         2.64      
6    Divide               373.084      659.245         1.77      
7    Log                  4912.146     8118.646        1.65      
8    Exp                  9034.717     11524.742       1.28      
9    Sqrt                 628.914      731.667         1.16      
10   Power                15305.584    297.276         0.02      

============================================================
分类性能统计:
------------------------------
基本算子 (10个): 平均加速比 2.86x
