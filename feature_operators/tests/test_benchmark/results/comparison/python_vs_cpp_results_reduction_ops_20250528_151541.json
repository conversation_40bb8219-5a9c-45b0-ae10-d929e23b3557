{"timestamp": "20250528_151541", "category": "reduction_ops", "summary": {"total_operators": 2, "cpp_avg_time": 301.63213299999995, "python_avg_time": 12214.66554949681, "avg_speedup": 40.49747149651675, "max_speedup": 40.91809078184522, "min_speedup": 40.07685221118828}, "detailed_results": [{"Operator": "Max", "C++_Time_μs": 300.031733, "Python_Time_μs": 12276.725688328346, "Speedup": 40.91809078184522, "Performance_Ratio": "40.92x"}, {"Operator": "Min", "C++_Time_μs": 303.232533, "Python_Time_μs": 12152.605410665274, "Speedup": 40.07685221118828, "Performance_Ratio": "40.08x"}]}