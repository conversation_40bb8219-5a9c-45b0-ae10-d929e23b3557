Feature Operators 性能比较报告 (<PERSON> vs C++)
============================================================

测试分类: comparison_ops
测试时间: 20250528_150836

测试算子总数: 3
C++平均执行时间: 308.374 μs
Python平均执行时间: 4193.360 μs
平均加速比: 13.61x

性能排名 (按加速比排序):
------------------------------------------------------------
排名   算子                   C++时间(μs)    Python时间(μs)    加速比       
------------------------------------------------------------
1    <PERSON>han                301.037      4275.048        14.20     
2    Mthan                301.184      4097.776        13.61     
3    Equal                322.902      4207.256        13.03     

============================================================
分类性能统计:
------------------------------
基本算子 (3个): 平均加速比 13.61x
