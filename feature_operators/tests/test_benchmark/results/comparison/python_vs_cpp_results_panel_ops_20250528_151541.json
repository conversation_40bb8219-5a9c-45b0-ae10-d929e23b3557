{"timestamp": "20250528_151541", "category": "panel_ops", "summary": {"total_operators": 5, "cpp_avg_time": 15997.850693399998, "python_avg_time": 45180.23098508517, "avg_speedup": 4.855847069629698, "max_speedup": 10.920988498375378, "min_speedup": 1.2081836181480883}, "detailed_results": [{"Operator": "Tot_Mean", "C++_Time_μs": 4152.3485, "Python_Time_μs": 33043.43161483606, "Speedup": 7.957769347836787, "Performance_Ratio": "7.96x"}, {"Operator": "Tot_Rank", "C++_Time_μs": 59044.669733, "Python_Time_μs": 134631.18026653925, "Speedup": 2.2801580714286565, "Performance_Ratio": "2.28x"}, {"Operator": "Tot_Sum", "C++_Time_μs": 2996.4139, "Python_Time_μs": 32723.801738272112, "Speedup": 10.920988498375378, "Performance_Ratio": "10.92x"}, {"Operator": "pn_Mean", "C++_Time_μs": 1245.457767, "Python_Time_μs": 1504.7416711846988, "Speedup": 1.2081836181480883, "Performance_Ratio": "1.21x"}, {"Operator": "pn_Rank", "C++_Time_μs": 12550.363567, "Python_Time_μs": 23997.999634593725, "Speedup": 1.9121358123595882, "Performance_Ratio": "1.91x"}]}