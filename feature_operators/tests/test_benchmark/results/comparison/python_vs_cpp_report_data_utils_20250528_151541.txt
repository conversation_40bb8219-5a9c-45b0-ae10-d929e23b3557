Feature Operators 性能比较报告 (<PERSON> vs C++)
============================================================

测试分类: data_utils
测试时间: 20250528_151541

测试算子总数: 3
C++平均执行时间: 123.553 μs
Python平均执行时间: 1605.486 μs
平均加速比: 14.73x

性能排名 (按加速比排序):
------------------------------------------------------------
排名   算子                   C++时间(μs)    Python时间(μs)    加速比       
------------------------------------------------------------
1    <USER>               <GROUP>.718      2090.308        20.15     
2    getInf               103.789      2090.363        20.14     
3    FilterInf            163.151      635.787         3.90      

============================================================
分类性能统计:
------------------------------
基本算子 (3个): 平均加速比 14.73x
