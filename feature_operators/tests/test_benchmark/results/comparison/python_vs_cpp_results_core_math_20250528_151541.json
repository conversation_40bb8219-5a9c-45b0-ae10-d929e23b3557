{"timestamp": "20250528_151541", "category": "core_math", "summary": {"total_operators": 10, "cpp_avg_time": 3144.0631466000004, "python_avg_time": 2761.907322953145, "avg_speedup": 2.773267097786866, "max_speedup": 8.845654916146543, "min_speedup": 0.017021124282281228}, "detailed_results": [{"Operator": "Abs", "C++_Time_μs": 223.3027, "Python_Time_μs": 1975.2586260437965, "Speedup": 8.845654916146543, "Performance_Ratio": "8.85x"}, {"Operator": "Add", "C++_Time_μs": 242.303733, "Python_Time_μs": 624.0542667607466, "Speedup": 2.5755041370359186, "Performance_Ratio": "2.58x"}, {"Operator": "Divide", "C++_Time_μs": 374.878533, "Python_Time_μs": 653.1806973119577, "Speedup": 1.7423795704833216, "Performance_Ratio": "1.74x"}, {"Operator": "Exp", "C++_Time_μs": 8860.6133, "Python_Time_μs": 11479.30072620511, "Speedup": 1.2955424571124337, "Performance_Ratio": "1.30x"}, {"Operator": "Log", "C++_Time_μs": 4852.183033, "Python_Time_μs": 7983.911968767643, "Speedup": 1.645426793356425, "Performance_Ratio": "1.65x"}, {"Operator": "Minus", "C++_Time_μs": 222.527967, "Python_Time_μs": 624.3456155061722, "Speedup": 2.8056950500346423, "Performance_Ratio": "2.81x"}, {"Operator": "Multiply", "C++_Time_μs": 223.2553, "Python_Time_μs": 618.2159607609113, "Speedup": 2.7690986989375452, "Performance_Ratio": "2.77x"}, {"Operator": "Power", "C++_Time_μs": 15266.8815, "Python_Time_μs": 259.85948741436005, "Speedup": 0.017021124282281228, "Performance_Ratio": "0.02x"}, {"Operator": "Sign", "C++_Time_μs": 545.826033, "Python_Time_μs": 2597.011625766754, "Speedup": 4.757947530448321, "Performance_Ratio": "4.76x"}, {"Operator": "Sqrt", "C++_Time_μs": 628.859367, "Python_Time_μs": 803.9342549939951, "Speedup": 1.2784007000312283, "Performance_Ratio": "1.28x"}]}