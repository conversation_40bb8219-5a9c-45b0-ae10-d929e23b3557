Feature Operators 性能比较报告 (<PERSON> vs C++)
============================================================

测试分类: panel_ops
测试时间: 20250528_150836

测试算子总数: 5
C++平均执行时间: 15260.720 μs
Python平均执行时间: 43846.814 μs
平均加速比: 4.87x

性能排名 (按加速比排序):
------------------------------------------------------------
排名   算子                   C++时间(μs)    Python时间(μs)    加速比       
------------------------------------------------------------
1    <USER>              <GROUP>.922     32832.201       11.27     
2    Tot_Mean             4259.215     34210.750       8.03      
3    Tot_Rank             55672.514    132168.520      2.37      
4    pn_Rank              12214.142    18577.409       1.52      
5    pn_Mean              1243.806     1445.190        1.16      

============================================================
分类性能统计:
------------------------------
面板算子 (2个): 平均加速比 1.34x
Tot算子 (3个): 平均加速比 7.22x
