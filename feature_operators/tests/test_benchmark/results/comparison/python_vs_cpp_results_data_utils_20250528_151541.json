{"timestamp": "20250528_151541", "category": "data_utils", "summary": {"total_operators": 3, "cpp_avg_time": 123.552578, "python_avg_time": 1605.4859281414085, "avg_speedup": 14.730408127992739, "max_speedup": 20.153727124444835, "min_speedup": 3.8969280396763915}, "detailed_results": [{"Operator": "FilterInf", "C++_Time_μs": 163.1509, "Python_Time_μs": 635.787316908439, "Speedup": 3.8969280396763915, "Performance_Ratio": "3.90x"}, {"Operator": "getInf", "C++_Time_μs": 103.788667, "Python_Time_μs": 2090.3628319501877, "Speedup": 20.140569219856996, "Performance_Ratio": "20.14x"}, {"Operator": "get<PERSON><PERSON>", "C++_Time_μs": 103.718167, "Python_Time_μs": 2090.307635565599, "Speedup": 20.153727124444835, "Performance_Ratio": "20.15x"}]}