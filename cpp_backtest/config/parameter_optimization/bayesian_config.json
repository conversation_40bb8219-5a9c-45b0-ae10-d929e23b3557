{"base_config_path": "/home/<USER>/git/fast_trader_elite/cpp_backtest/examples/market_making_config.json", "backtest_config_path": "/home/<USER>/git/fast_trader_elite/cpp_backtest/config/backtest_config.json", "output_path": "/home/<USER>/git/fast_trader_elite/cpp_backtest/optimization_results", "optimization_algorithm": "bayesian", "optimization_target": {"type": "composite", "components": [{"metric": "return", "weight": 0.4, "transform": "identity", "description": "总收益率"}], "normalize": "z_score", "description": "综合考虑收益率、风险调整收益和交易效率的优化目标"}, "constraints": [{"metric": "max_drawdown", "operator": "<=", "value": 0.2, "description": "最大回撤不超过20%"}, {"metric": "win_rate", "operator": ">=", "value": 0.5, "description": "胜率不低于50%"}], "max_iterations": 100, "parallel_jobs": 4, "parameters": [{"name": "gamma", "type": "int", "values": [2, 3, 4], "description": "伽马参数"}, {"name": "refresh_window", "type": "int", "values": [1, 3, 5, 10], "description": "refresh_window"}, {"name": "indicator_settings.tick_ema.span", "type": "int", "min": 500, "max": 2000, "step": 500, "description": "EMA跨度"}, {"name": "indicator_settings.tick_ema.spread", "type": "float", "min": 0.005, "max": 0.025, "step": 0.005, "description": "EMA价差"}, {"name": "stop_loss_danger", "type": "float", "min": 0.002, "max": 0.02, "step": 0.005, "description": "止损幅度"}]}