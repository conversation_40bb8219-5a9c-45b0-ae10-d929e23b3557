{"base_config_path": "/home/<USER>/git/cpp_backtest/examples/market_making_config.json", "backtest_config_path": "/home/<USER>/git/cpp_backtest/config/backtest_config.json", "output_path": "/home/<USER>/git/cpp_backtest/build/output/optimization_results", "optimization_algorithm": "random_search", "optimization_target": {"type": "composite", "components": [{"metric": "return", "weight": 0.4, "transform": "identity", "description": "总收益率"}, {"metric": "sharpe_ratio", "weight": 0.3, "transform": "identity", "description": "夏普比率"}, {"metric": "avg_position_time", "weight": 0.1, "transform": "inverse", "description": "平均持仓时间（越短越好）"}, {"metric": "position_cycles", "weight": 0.2, "transform": "identity", "description": "交易周期数量（越多越好）"}], "normalize": "rank", "description": "综合考虑收益率、风险调整收益、交易效率和交易频率的优化目标"}, "constraints": [{"metric": "win_rate", "operator": ">=", "value": 0.5, "description": "胜率不低于50%"}], "max_iterations": 100, "parallel_jobs": 4, "parameters": [{"name": "grid_spacing", "type": "float", "min": 0.001, "max": 0.01, "step": 0.001, "description": "网格间距"}, {"name": "gamma", "type": "int", "values": [2, 3, 4, 5, 6], "description": "伽马参数"}, {"name": "refresh_window", "type": "float", "values": [1, 3, 5, 10], "description": "刷新窗口"}, {"name": "indicator_settings.tick_ema.span", "type": "int", "min": 500, "max": 2000, "step": 100, "description": "EMA跨度"}, {"name": "indicator_settings.tick_ema.spread", "type": "float", "min": 0.002, "max": 0.025, "step": 0.001, "description": "EMA价差"}, {"name": "indicator_settings.tick_ema.ema_shift_multi", "type": "float", "min": -0.5, "max": 0.5, "step": 0.01, "description": "EMA偏移倍数"}, {"name": "stop_loss_danger", "type": "float", "min": 0.005, "max": 0.02, "step": 0.001, "description": "止损幅度"}, {"name": "min_markup", "type": "float", "min": 0.005, "max": 0.025, "step": 0.001, "description": "最小止盈"}, {"name": "max_markup", "type": "float", "min": 0.005, "max": 0.04, "step": 0.005, "description": "最大止盈"}, {"name": "natr_ratio", "type": "float", "min": 0.025, "max": 0.25, "step": 0.01, "description": "NATR比率"}]}