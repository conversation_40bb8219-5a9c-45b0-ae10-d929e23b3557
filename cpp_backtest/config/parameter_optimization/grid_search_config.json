{"base_config_path": "/home/<USER>/git/fast_trader_elite/cpp_backtest/examples/market_making_config.json", "backtest_config_path": "/home/<USER>/git/fast_trader_elite/cpp_backtest/config/backtest_config.json", "output_path": "/home/<USER>/git/fast_trader_elite/cpp_backtest/optimization_results", "optimization_algorithm": "grid_search", "optimization_target": {"type": "composite", "components": [{"metric": "return", "weight": 1, "transform": "identity", "description": "总收益率"}], "normalize": "z_score", "description": "综合考虑收益率、风险调整收益和交易效率的优化目标"}, "constraints": [{"metric": "win_rate", "operator": ">=", "value": 0.5, "description": "胜率不低于50%"}], "max_iterations": 100, "parallel_jobs": 4, "parameters": [{"name": "grid_spacing", "type": "float", "min": 0.002, "max": 0.005, "step": 0.001, "description": "网格间距"}, {"name": "gamma", "type": "int", "values": [2, 3, 4], "description": "伽马参数"}, {"name": "refresh_window", "type": "float", "values": [1, 5, 10, 30, 60], "description": "refresh_window"}, {"name": "indicator_settings.tick_ema.span", "type": "int", "min": 500, "max": 2000, "step": 500, "description": "EMA跨度"}, {"name": "indicator_settings.tick_ema.spread", "type": "float", "min": 0.005, "max": 0.025, "step": 0.005, "description": "EMA价差"}, {"name": "stop_loss_danger", "type": "float", "min": 0.005, "max": 0.02, "step": 0.05, "description": "止损幅度"}]}