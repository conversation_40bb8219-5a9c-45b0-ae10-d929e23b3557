{"backtest": {"start_time": "2024-01-01 08:00:00", "end_time": "2026-02-01 08:00:00", "data_file": "/home/<USER>/git/cpp_backtest/build/bin/test.bin", "output_path": "/home/<USER>/git/cpp_backtest/build/output", "exchange": "no_partial_fill", "initial_balance": 10000.0, "latency": {"type": "constant", "order_latency": 100, "cancel_latency": 100, "md_latency": 50, "response_latency": 50}, "queue": {"type": "prob", "power": 2.0}, "fee": {"type": "fixed_fee", "maker_fee": 0.0002, "taker_fee": 0.0004}, "asset": {"type": "linear", "contract_value": 1.0}}, "common": {"log_dir": "../log/backtest", "log_level": "INFO"}, "instruments": [{"exchange": "bybit", "instrument": "HAEDALUSDT", "instrument_idx": 1}], "strategy": {"path": "/home/<USER>/git/cpp_backtest/build/lib/libmarket_making.so", "config_path": "/home/<USER>/git/cpp_backtest/build/output/optimization_results/best_config.json", "log_level": "INFO", "log_file": "../log/backtest/test.log"}}