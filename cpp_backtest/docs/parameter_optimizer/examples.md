# 参数优化使用示例

本文档提供了参数优化功能的使用示例，帮助用户快速上手。

## 基本使用流程

参数优化的基本使用流程包括以下步骤：

1. 创建参数优化配置文件
2. 运行参数优化
3. 分析优化结果

下面将详细介绍每个步骤。

## 创建参数优化配置文件

参数优化配置文件是一个 JSON 文件，定义了要优化的参数、优化目标和约束条件。以下是一个简单的配置文件示例：

```json
{
  "base_config_path": "/home/<USER>/git/fast_trader_elite/cpp_backtest/examples/market_making_config.json",
  "backtest_config_path": "/home/<USER>/git/fast_trader_elite/cpp_backtest/config/backtest_config.json",
  "output_path": "/home/<USER>/git/fast_trader_elite/cpp_backtest/optimization_results",
  "optimization_algorithm": "grid_search",
  "optimization_target": {
    "type": "single",
    "metric": "sharpe_ratio"
  },
  "max_iterations": 100,
  "parallel_jobs": 4,
  "parameters": [
    {
      "name": "grid_spacing",
      "type": "float",
      "min": 0.002,
      "max": 0.005,
      "step": 0.001,
      "description": "网格间距"
    },
    {
      "name": "min_markup",
      "type": "float",
      "min": 0.01,
      "max": 0.03,
      "step": 0.01,
      "description": "最小加价"
    }
  ]
}
```

将这个配置文件保存为 `grid_search_config.json`。

## 运行参数优化

有两种方式运行参数优化：使用命令行工具或使用脚本。

### 使用命令行工具

```bash
cd /home/<USER>/git/fast_trader_elite/cpp_backtest
./build/bin/parameter_optimizer_cli --config=config/parameter_optimization/grid_search_config.json
```

### 使用脚本

我们提供了两个脚本来简化参数优化的运行：

1. `run_parameter_optimization.sh`：运行单次参数优化
2. `run_multiple_optimizations.sh`：运行多次参数优化，用于比较不同算法或参数设置的性能

#### 运行单次参数优化

```bash
cd /home/<USER>/git/fast_trader_elite/cpp_backtest
./scripts/run_parameter_optimization.sh config/parameter_optimization/grid_search_config.json optimization_results
```

这个脚本会运行参数优化，并将结果保存到 `optimization_results` 目录中。

#### 运行多次参数优化

```bash
cd /home/<USER>/git/fast_trader_elite/cpp_backtest
./scripts/run_multiple_optimizations.sh config/parameter_optimization/grid_search_config.json 5 4
```

这个脚本会运行 5 次参数优化，每次使用 4 个并行任务，并将结果保存到 `optimization_results_YYYYMMDD_HHMMSS` 目录中。

## 分析优化结果

参数优化完成后，会生成以下结果文件：

- `results.json`：包含所有参数组合的评估结果
- `results.csv`：CSV 格式的评估结果，方便导入到电子表格软件中分析
- `results.html`：HTML 格式的评估结果，提供交互式的结果查看
- `best_config.json`：最佳参数组合的配置文件
- `sensitivity_analysis.json`：参数敏感性分析结果

### 查看最佳结果

最简单的方法是查看命令行输出，它会显示最佳参数组合及其性能指标：

```
Best result:
Score: 2.345
Parameters:
  symbol_configs[0].grid_spacing: 0.003
  symbol_configs[0].min_markup: 0.02
Metrics:
  sharpe_ratio: 2.345
  return: 0.123
  max_drawdown: 0.05
```

### 使用最佳配置

最佳参数组合的配置文件保存在 `best_config.json` 中，可以直接用于回测或实盘交易。

### 参数敏感性分析

参数敏感性分析结果保存在 `sensitivity_analysis.json` 中，可以用于分析参数对性能指标的影响。

## 使用随机搜索优化器（Random Search）

随机搜索优化器是一种简单但有效的优化算法，它从参数空间中随机采样点进行评估。相比网格搜索，随机搜索在高维参数空间中更有效，因为它不需要评估所有可能的参数组合。

在随机搜索中，参数配置与网格搜索类似，但参数的处理方式有所不同：

- `min` 和 `max`：定义参数的取值范围，随机搜索会在这个范围内随机采样。
- `step`：对于随机搜索来说，`step` 参数用于离散化随机值。例如，如果 `min=1`，`max=10`，`step=3`，则随机搜索会从 `{1, 4, 7, 10}` 中随机选择一个值，而不是在 `[1, 10]` 范围内生成任意随机值。
- `values`：如果提供了预定义的值列表，随机搜索会从这个列表中随机选择一个值。

以下是使用随机搜索优化器的配置示例：

```json
{
  "base_config_path": "/home/<USER>/git/fast_trader_elite/cpp_backtest/examples/market_making_config.json",
  "backtest_config_path": "/home/<USER>/git/fast_trader_elite/cpp_backtest/config/backtest_config.json",
  "output_path": "/home/<USER>/git/fast_trader_elite/cpp_backtest/optimization_results",
  "optimization_algorithm": "random_search",
  "optimization_target": {
    "type": "single",
    "metric": "sharpe_ratio",
    "description": "夏普比率"
  },
  "constraints": [
    {
      "metric": "max_drawdown",
      "operator": "<=",
      "value": 0.2,
      "description": "最大回撤不超过20%"
    },
    {
      "metric": "win_rate",
      "operator": ">=",
      "value": 0.5,
      "description": "胜率不低于50%"
    }
  ],
  "max_iterations": 100,
  "parallel_jobs": 4,
  "parameters": [
    {
      "name": "grid_spacing",
      "type": "float",
      "min": 0.002,
      "max": 0.005,
      "step": 0.0005,
      "description": "网格间距"
    },
    {
      "name": "min_markup",
      "type": "float",
      "min": 0.01,
      "max": 0.03,
      "step": 0.005,
      "description": "最小加价"
    },
    {
      "name": "max_markup",
      "type": "float",
      "min": 0.015,
      "max": 0.035,
      "step": 0.005,
      "description": "最大加价"
    },
    {
      "name": "gamma",
      "type": "int",
      "values": [1, 2, 3, 4, 5],
      "description": "伽马参数"
    },
    {
      "name": "indicator_settings.tick_ema.span",
      "type": "int",
      "min": 300,
      "max": 700,
      "step": 100,
      "description": "EMA跨度"
    },
    {
      "name": "indicator_settings.tick_ema.spread",
      "type": "float",
      "min": 0.005,
      "max": 0.025,
      "step": 0.005,
      "description": "EMA价差"
    }
  ]
}
```

将这个配置文件保存为 `random_search_config.json`，然后使用以下命令运行：

```bash
cd /home/<USER>/git/fast_trader_elite/cpp_backtest
./build/bin/parameter_optimizer_cli --config=config/parameter_optimization/random_search_config.json
```

随机搜索优化器的优点是：

1. **计算效率高**：不需要评估所有可能的参数组合，特别适合参数空间较大的情况。
2. **易于实现**：算法简单，容易理解和实现。
3. **适合高维参数空间**：在高维参数空间中，随机搜索通常比网格搜索更有效。

随机搜索优化器的缺点是：

1. **结果可能不稳定**：由于随机性，每次运行可能得到不同的结果。
2. **可能错过最优解**：由于是随机采样，可能错过参数空间中的最优解。

为了提高随机搜索的可靠性，建议多次运行优化，取平均结果。

## 使用贝叶斯优化器（Bayesian Optimization）

贝叶斯优化是一种基于概率模型的优化算法，特别适合评估成本高的场景。我们使用树形Parzen估计器（TPE）实现贝叶斯优化，它通过构建两个概率分布（一个针对好的样本，一个针对差的样本），然后选择使得好样本概率与差样本概率比值最大的点作为下一个评估点。

在贝叶斯优化中，参数配置与网格搜索和随机搜索类似，需要定义参数的取值范围：

- `min` 和 `max`：定义参数的取值范围，贝叶斯优化会在这个范围内搜索最优值。
- `step`：对于贝叶斯优化来说，`step` 参数用于离散化参数空间。例如，如果 `min=1`，`max=10`，`step=3`，则贝叶斯优化会在 `{1, 4, 7, 10}` 这些值中搜索，而不是在连续的 `[1, 10]` 范围内搜索。
- `values`：如果提供了预定义的值列表，贝叶斯优化会在这个列表中搜索最优值。

以下是使用贝叶斯优化器的配置示例：

```json
{
  "base_config_path": "/home/<USER>/git/fast_trader_elite/cpp_backtest/examples/market_making_config.json",
  "backtest_config_path": "/home/<USER>/git/fast_trader_elite/cpp_backtest/config/backtest_config.json",
  "output_path": "/home/<USER>/git/fast_trader_elite/cpp_backtest/optimization_results",
  "optimization_algorithm": "bayesian",
  "optimization_target": {
    "type": "single",
    "metric": "sharpe_ratio",
    "description": "夏普比率"
  },
  "constraints": [
    {
      "metric": "max_drawdown",
      "operator": "<=",
      "value": 0.2,
      "description": "最大回撤不超过20%"
    },
    {
      "metric": "win_rate",
      "operator": ">=",
      "value": 0.5,
      "description": "胜率不低于50%"
    }
  ],
  "max_iterations": 50,
  "parallel_jobs": 4,
  "parameters": [
    {
      "name": "grid_spacing",
      "type": "float",
      "min": 0.002,
      "max": 0.005,
      "step": 0.0005,
      "description": "网格间距"
    },
    {
      "name": "min_markup",
      "type": "float",
      "min": 0.01,
      "max": 0.03,
      "step": 0.005,
      "description": "最小加价"
    },
    {
      "name": "max_markup",
      "type": "float",
      "min": 0.015,
      "max": 0.035,
      "step": 0.005,
      "description": "最大加价"
    },
    {
      "name": "gamma",
      "type": "int",
      "values": [1, 2, 3, 4, 5],
      "description": "伽马参数"
    },
    {
      "name": "indicator_settings.tick_ema.span",
      "type": "int",
      "min": 300,
      "max": 700,
      "step": 100,
      "description": "EMA跨度"
    },
    {
      "name": "indicator_settings.tick_ema.spread",
      "type": "float",
      "min": 0.005,
      "max": 0.025,
      "step": 0.005,
      "description": "EMA价差"
    }
  ]
}
```

将这个配置文件保存为 `bayesian_config.json`，然后使用以下命令运行：

```bash
cd /home/<USER>/git/fast_trader_elite/cpp_backtest
./build/bin/parameter_optimizer_cli --config=config/parameter_optimization/bayesian_config.json
```

贝叶斯优化器的优点是：

1. **样本效率高**：通常需要更少的样本点就能找到较好的解，特别适合评估成本高的场景。
2. **自适应采样**：根据已有的评估结果自适应地选择下一个评估点，避免在无效区域浪费计算资源。
3. **适合噪声函数**：对于有噪声的目标函数，贝叶斯优化通常比其他方法更稳健。

贝叶斯优化器的缺点是：

1. **计算复杂度高**：随着样本数量的增加，计算复杂度会迅速增长。
2. **超参数敏感**：贝叶斯优化本身也有一些超参数，如带宽参数，需要合理设置。

贝叶斯优化特别适合以下场景：

1. **评估成本高**：当每次评估都很耗时或资源时，贝叶斯优化能以更少的评估次数找到较好的解。
2. **参数空间复杂**：当参数空间有多个局部最优解时，贝叶斯优化能更好地探索整个参数空间。
3. **参数数量适中**：当参数数量在2-20之间时，贝叶斯优化通常表现最佳。

## 使用遗传算法优化器（Genetic Algorithm）

遗传算法是一种基于自然选择和遗传学原理的优化算法，特别适合复杂参数空间和多目标优化问题。它通过模拟自然选择过程，包括选择、交叉和变异操作，来搜索最优解。

在遗传算法中，参数配置与其他优化算法类似，需要定义参数的取值范围：

- `min` 和 `max`：定义参数的取值范围，遗传算法会在这个范围内搜索最优值。
- `step`：对于遗传算法来说，`step` 参数用于离散化参数空间。例如，如果 `min=1`，`max=10`，`step=3`，则遗传算法会在 `{1, 4, 7, 10}` 这些值中搜索，而不是在连续的 `[1, 10]` 范围内搜索。
- `values`：如果提供了预定义的值列表，遗传算法会在这个列表中搜索最优值。

此外，遗传算法还有一些特有的参数，可以在配置文件的 `genetic_config` 部分设置：

- `population_size`：种群大小，即每一代中个体的数量。默认值为 50。
- `elite_count`：精英数量，即每一代中直接保留到下一代的最优个体数量。默认值为 5。
- `crossover_rate`：交叉概率，即两个父代个体进行交叉操作的概率。默认值为 0.8。
- `mutation_rate`：变异概率，即个体中每个参数发生变异的概率。默认值为 0.1。

以下是使用遗传算法优化器的配置示例：

```json
{
  "base_config_path": "/home/<USER>/git/fast_trader_elite/cpp_backtest/examples/market_making_config.json",
  "backtest_config_path": "/home/<USER>/git/fast_trader_elite/cpp_backtest/config/backtest_config.json",
  "output_path": "/home/<USER>/git/fast_trader_elite/cpp_backtest/optimization_results",
  "optimization_algorithm": "genetic",
  "optimization_target": {
    "type": "single",
    "metric": "sharpe_ratio",
    "description": "夏普比率"
  },
  "constraints": [
    {
      "metric": "max_drawdown",
      "operator": "<=",
      "value": 0.2,
      "description": "最大回撤不超过20%"
    },
    {
      "metric": "win_rate",
      "operator": ">=",
      "value": 0.5,
      "description": "胜率不低于50%"
    }
  ],
  "max_iterations": 100,
  "parallel_jobs": 4,
  "genetic_config": {
    "population_size": 20,
    "elite_count": 2,
    "crossover_rate": 0.8,
    "mutation_rate": 0.1
  },
  "parameters": [
    {
      "name": "grid_spacing",
      "type": "float",
      "min": 0.002,
      "max": 0.005,
      "step": 0.0005,
      "description": "网格间距"
    },
    {
      "name": "min_markup",
      "type": "float",
      "min": 0.01,
      "max": 0.03,
      "step": 0.005,
      "description": "最小加价"
    },
    {
      "name": "max_markup",
      "type": "float",
      "min": 0.015,
      "max": 0.035,
      "step": 0.005,
      "description": "最大加价"
    },
    {
      "name": "gamma",
      "type": "int",
      "values": [1, 2, 3, 4, 5],
      "description": "伽马参数"
    },
    {
      "name": "indicator_settings.tick_ema.span",
      "type": "int",
      "min": 300,
      "max": 700,
      "step": 100,
      "description": "EMA跨度"
    },
    {
      "name": "indicator_settings.tick_ema.spread",
      "type": "float",
      "min": 0.005,
      "max": 0.025,
      "step": 0.005,
      "description": "EMA价差"
    }
  ]
}
```

将这个配置文件保存为 `genetic_config.json`，然后使用以下命令运行：

```bash
cd /home/<USER>/git/fast_trader_elite/cpp_backtest
./build/bin/parameter_optimizer_cli --config=config/parameter_optimization/genetic_config.json
```

遗传算法优化器的优点是：

1. **全局搜索能力强**：能够在复杂的参数空间中找到全局最优解或接近全局最优的解。
2. **适应性强**：能够处理各种类型的优化问题，包括非凸、非光滑的目标函数。
3. **并行性好**：遗传算法的各个操作（如评估、交叉、变异）可以很容易地并行化，提高计算效率。

遗传算法优化器的缺点是：

1. **收敛速度慢**：相比贝叶斯优化等方法，遗传算法可能需要更多的迭代才能收敛到最优解。
2. **参数敏感**：遗传算法的性能受到种群大小、交叉概率、变异概率等参数的影响，需要合理设置。

遗传算法特别适合以下场景：

1. **复杂参数空间**：当参数空间有多个局部最优解，或者参数之间有复杂的相互作用时，遗传算法能够更好地探索整个参数空间。
2. **多目标优化**：当需要同时优化多个目标时，遗传算法能够找到一组帕累托最优解。
3. **离散参数**：当参数是离散的，或者参数空间不连续时，遗传算法也能有效地工作。

## 高级示例

### 使用组合指标

以下是使用组合指标作为优化目标的配置示例：

```json
{
  "base_config_path": "/home/<USER>/git/fast_trader_elite/cpp_backtest/examples/market_making_config.json",
  "backtest_config_path": "/home/<USER>/git/fast_trader_elite/cpp_backtest/config/backtest_config.json",
  "output_path": "/home/<USER>/git/fast_trader_elite/cpp_backtest/optimization_results",
  "optimization_algorithm": "grid_search",
  "optimization_target": {
    "type": "composite",
    "components": [
      {
        "metric": "sharpe_ratio",
        "weight": 0.4,
        "transform": "identity",
        "description": "夏普比率"
      },
      {
        "metric": "return",
        "weight": 0.3,
        "transform": "identity",
        "description": "总收益率"
      },
      {
        "metric": "avg_position_time",
        "weight": 0.3,
        "transform": "inverse",
        "description": "平均持仓时间（越短越好）"
      }
    ],
    "normalize": "z_score",
    "description": "综合考虑收益率、风险调整收益和交易效率的优化目标"
  },
  "max_iterations": 100,
  "parallel_jobs": 4,
  "parameters": [
    {
      "name": "grid_spacing",
      "type": "float",
      "min": 0.002,
      "max": 0.005,
      "step": 0.001,
      "description": "网格间距"
    },
    {
      "name": "min_markup",
      "type": "float",
      "min": 0.01,
      "max": 0.03,
      "step": 0.01,
      "description": "最小加价"
    }
  ]
}
```

### 添加约束条件

以下是添加约束条件的配置示例：

```json
{
  "base_config_path": "/home/<USER>/git/fast_trader_elite/cpp_backtest/examples/market_making_config.json",
  "backtest_config_path": "/home/<USER>/git/fast_trader_elite/cpp_backtest/config/backtest_config.json",
  "output_path": "/home/<USER>/git/fast_trader_elite/cpp_backtest/optimization_results",
  "optimization_algorithm": "grid_search",
  "optimization_target": {
    "type": "single",
    "metric": "return"
  },
  "constraints": [
    {
      "metric": "max_drawdown",
      "operator": "<=",
      "value": 0.2,
      "description": "最大回撤不超过20%"
    },
    {
      "metric": "win_rate",
      "operator": ">=",
      "value": 0.5,
      "description": "胜率不低于50%"
    }
  ],
  "max_iterations": 100,
  "parallel_jobs": 4,
  "parameters": [
    {
      "name": "grid_spacing",
      "type": "float",
      "min": 0.002,
      "max": 0.005,
      "step": 0.001,
      "description": "网格间距"
    },
    {
      "name": "min_markup",
      "type": "float",
      "min": 0.01,
      "max": 0.03,
      "step": 0.01,
      "description": "最小加价"
    }
  ]
}
```

### 优化多个参数

以下是优化多个参数的配置示例：

```json
{
  "base_config_path": "/home/<USER>/git/fast_trader_elite/cpp_backtest/examples/market_making_config.json",
  "backtest_config_path": "/home/<USER>/git/fast_trader_elite/cpp_backtest/config/backtest_config.json",
  "output_path": "/home/<USER>/git/fast_trader_elite/cpp_backtest/optimization_results",
  "optimization_algorithm": "grid_search",
  "optimization_target": {
    "type": "single",
    "metric": "sharpe_ratio"
  },
  "max_iterations": 1000,
  "parallel_jobs": 8,
  "parameters": [
    {
      "name": "grid_spacing",
      "type": "float",
      "min": 0.002,
      "max": 0.005,
      "step": 0.001,
      "description": "网格间距"
    },
    {
      "name": "min_markup",
      "type": "float",
      "min": 0.01,
      "max": 0.03,
      "step": 0.01,
      "description": "最小加价"
    },
    {
      "name": "max_markup",
      "type": "float",
      "min": 0.02,
      "max": 0.04,
      "step": 0.01,
      "description": "最大加价"
    },
    {
      "name": "gamma",
      "type": "int",
      "values": [1, 2, 3, 4, 5],
      "description": "伽马参数"
    }
  ]
}
```

### 优化嵌套参数

以下是优化嵌套参数的配置示例：

```json
{
  "base_config_path": "/home/<USER>/git/fast_trader_elite/cpp_backtest/examples/market_making_config.json",
  "backtest_config_path": "/home/<USER>/git/fast_trader_elite/cpp_backtest/config/backtest_config.json",
  "output_path": "/home/<USER>/git/fast_trader_elite/cpp_backtest/optimization_results",
  "optimization_algorithm": "grid_search",
  "optimization_target": {
    "type": "single",
    "metric": "sharpe_ratio"
  },
  "max_iterations": 100,
  "parallel_jobs": 4,
  "parameters": [
    {
      "name": "indicator_settings.tick_ema.span",
      "type": "int",
      "min": 300,
      "max": 700,
      "step": 100,
      "description": "EMA跨度"
    },
    {
      "name": "indicator_settings.tick_ema.spread",
      "type": "float",
      "min": 0.005,
      "max": 0.025,
      "step": 0.005,
      "description": "EMA价差"
    }
  ]
}
```

这将优化 `symbol_configs[0].indicator_settings.tick_ema.span` 和 `symbol_configs[0].indicator_settings.tick_ema.spread` 参数。


## 最佳实践

1. **从简单开始**：首先使用较少的参数和较大的步长进行粗略搜索，然后在有希望的区域进行细致搜索。

2. **合理设置参数范围**：参数范围应该基于对策略的理解和先验知识，避免设置过大的范围。

3. **使用并行计算**：设置合适的并行任务数，提高优化效率。通常设置为 CPU 核心数的 1-2 倍较为合适。

4. **避免过拟合**：过度优化可能导致过拟合，使策略在实际交易中表现不佳。建议使用交叉验证等方法评估策略的稳健性。

5. **分析参数敏感性**：了解参数对性能指标的影响，找出关键参数，有助于更好地理解和改进策略。

6. **多次运行优化**：由于随机因素的影响，建议多次运行优化，取平均结果，提高结果的可靠性。

7. **结合专业知识**：参数优化是一个工具，而不是万能的解决方案。结合专业知识和经验，对优化结果进行分析和判断。
