# 优化目标

优化目标是参数优化过程中用于评估参数组合性能的函数。本文档详细介绍了支持的优化目标和指标计算方法。

## 优化目标类型

参数优化功能支持两种类型的优化目标：

1. **单一指标（Single Metric）**：使用单一性能指标作为优化目标。
2. **组合指标（Composite Metric）**：使用多个性能指标的加权组合作为优化目标。

## 支持的指标

以下是支持的性能指标列表：

### 收益相关指标

- `return`：总收益率，计算公式为 (final_equity - initial_equity) / initial_equity。
- `annual_return`：年化收益率，计算公式为 return * (252 / trading_days)，假设一年有 252 个交易日。

### 风险调整收益指标

- `sharpe_ratio`：夏普比率，计算公式为 (mean_daily_return - risk_free_rate) / std_dev_daily_return * sqrt(252)。
- `sortino_ratio`：索提诺比率，计算公式为 (mean_daily_return - risk_free_rate) / downside_std_dev * sqrt(252)。
- `return_over_mdd`：收益回撤比，计算公式为 return / max_drawdown。

### 风险指标

- `max_drawdown`：最大回撤，计算公式为 max((peak - equity) / peak)。

### 交易效率指标

- `return_over_trade`：收益交易比，计算公式为 return / trading_value。
- `avg_position_time`：平均持仓时间（秒）。
- `win_rate`：胜率，计算公式为 win_count / total_trades。
- `profit_loss_ratio`：盈亏比，计算公式为 avg_profit / avg_loss。
- `daily_trading_value`：日均交易金额（USDT），计算公式为 total_trading_value / trading_days。
- `max_leverage`：最大杠杆。

### 交易量和交易次数指标

- `total_trades`：总交易次数，表示策略在回测期间的总成交笔数。
- `position_cycles`：总交易周期数，表示策略在回测期间完整的持仓周期数量（从建仓到完全平仓算作一个周期）。
- `daily_trades`：日均交易次数，计算公式为 total_trades / trading_days。
- `total_trading_value`：总交易金额（USDT），表示策略在回测期间的总交易金额（价格 * 数量）。

## 指标转换

在组合指标中，可以对各个指标进行转换，以适应不同的优化需求。支持的转换函数包括：

- `identity`：保持原值，f(x) = x。
- `inverse`：取倒数，f(x) = 1/x。适用于希望最小化的指标，如平均持仓时间。
- `negative`：取负值，f(x) = -x。适用于希望最小化的指标，如最大回撤。
- `log`：取对数，f(x) = log(x)。适用于范围跨度很大的指标。
- `exp`：取指数，f(x) = exp(x)。用于强调差异。
- `threshold`：阈值函数，f(x) = x if x > threshold else 0。用于设置最低要求。

## 指标归一化

为了合理组合不同量级和单位的指标，可以对指标进行归一化处理。支持的归一化方法包括：

- `min_max`：Min-Max 归一化，将指标缩放到 [0,1] 范围，计算公式为 (x - min) / (max - min)。对异常值敏感。
- `z_score`：Z-Score 归一化，基于均值和标准差进行标准化，计算公式为 (x - mean) / std_dev。适合近似正态分布的数据。
- `percentile`：百分位数归一化，基于中位数和四分位距(IQR)进行归一化，计算公式为 (x - median) / IQR。比Z-Score更稳健，对异常值不敏感。
- `rank`：排名归一化，基于所有参数组合的排名进行归一化，计算公式为 rank(x) / (n-1)，其中n是参数组合的数量。完全消除量纲影响，对异常值极不敏感，特别适合指标值差异很大的情况。

## 约束条件

除了优化目标外，还可以添加约束条件，筛选满足特定要求的参数组合。约束条件由指标、操作符和阈值组成，例如：

- `max_drawdown <= 0.2`：最大回撤不超过 20%。
- `win_rate >= 0.5`：胜率不低于 50%。

不满足约束条件的参数组合将被排除在优化结果之外。

## 单一指标示例

以下是使用单一指标作为优化目标的配置示例：

```json
"optimization_target": {
  "type": "single",
  "metric": "sharpe_ratio",
  "normalize": true
}
```

这个配置使用夏普比率作为优化目标，并对结果进行归一化处理。

## 组合指标示例

以下是使用组合指标作为优化目标的配置示例：

```json
"optimization_target": {
  "type": "composite",
  "components": [
    {
      "metric": "sharpe_ratio",
      "weight": 0.4,
      "transform": "identity",
      "description": "夏普比率"
    },
    {
      "metric": "return",
      "weight": 0.3,
      "transform": "identity",
      "description": "总收益率"
    },
    {
      "metric": "avg_position_time",
      "weight": 0.3,
      "transform": "inverse",
      "description": "平均持仓时间（越短越好）"
    }
  ],
  "normalize": "z_score",
  "description": "综合考虑收益率、风险调整收益和交易效率的优化目标"
}
```

这个配置使用夏普比率、总收益率和平均持仓时间的加权组合作为优化目标。其中，夏普比率的权重为 0.4，总收益率的权重为 0.3，平均持仓时间的权重为 0.3。平均持仓时间使用倒数转换，因为我们希望持仓时间越短越好。所有指标都使用 Z-Score 归一化处理。

### 使用排名归一化的组合指标示例

当不同指标的数值范围差异很大时，可以使用排名归一化来确保公平比较：

```json
"optimization_target": {
  "type": "composite",
  "components": [
    {
      "metric": "return",
      "weight": 0.4,
      "transform": "identity",
      "description": "总收益率"
    },
    {
      "metric": "sharpe_ratio",
      "weight": 0.4,
      "transform": "identity",
      "description": "夏普比率"
    },
    {
      "metric": "avg_position_time",
      "weight": 0.15,
      "transform": "negative",
      "description": "平均持仓时间（越短越好）"
    },
    {
      "metric": "position_cycles",
      "weight": 0.05,
      "transform": "identity",
      "description": "交易周期数量（越多越好）"
    }
  ],
  "normalize": "rank",
  "description": "使用排名归一化综合考虑收益率、风险和效率的优化目标"
}
```

这个配置使用排名归一化方法，完全基于各指标在所有参数组合中的相对排名进行评分，而不受原始数值范围的影响。这种方法特别适合指标值差异很大的情况，例如 `return` 可能在 0.1-0.5 范围内，而 `avg_position_time` 可能在 1000-100000 范围内。

## 约束条件示例

以下是添加约束条件的配置示例：

```json
"constraints": [
  {
    "metric": "max_drawdown",
    "operator": "<=",
    "value": 0.2,
    "description": "最大回撤不超过20%"
  },
  {
    "metric": "win_rate",
    "operator": ">=",
    "value": 0.5,
    "description": "胜率不低于50%"
  },
  {
    "metric": "daily_trades",
    "operator": ">=",
    "value": 5.0,
    "description": "日均交易次数不少于5次"
  },
  {
    "metric": "total_trading_value",
    "operator": ">=",
    "value": 100.0,
    "description": "总交易金额不少于100 USDT"
  }
]
```

这个配置添加了四个约束条件：最大回撤不超过 20%，胜率不低于 50%，日均交易次数不少于5次，总交易金额不少于100 USDT。

## 自定义优化目标

如果内置的指标和转换函数不能满足需求，可以通过扩展 `metric_calculator` 类来添加自定义指标，或者通过扩展 `optimization_target` 类来创建自定义优化目标。

### 添加自定义指标

要添加自定义指标，需要实现一个计算函数，并将其注册到 `metric_calculator` 中：

```cpp
// 定义指标计算函数
double calculate_custom_metric(const backtest_data& data, uint16_t instrument_idx) {
    // 自定义指标计算逻辑
    return result;
}

// 注册指标
metric_calculator& calculator = get_metric_calculator();
calculator.register_metric("custom_metric", calculate_custom_metric);
```

### 创建自定义优化目标

要创建自定义优化目标，需要继承 `optimization_target` 类，并实现 `calculate_score` 和 `get_required_metrics` 方法：

```cpp
class custom_target : public optimization_target {
public:
    double calculate_score(const std::unordered_map<std::string, double>& metrics) const override {
        // 自定义得分计算逻辑
        return score;
    }

    std::vector<std::string> get_required_metrics() const override {
        // 返回所需的指标列表
        return {"metric1", "metric2", "metric3"};
    }
};
```

## 最佳实践

1. **选择合适的优化目标**：根据策略的特点和交易目标选择合适的优化目标。例如，如果追求稳定的收益，可以使用夏普比率或索提诺比率；如果追求高收益，可以使用总收益率或年化收益率。

2. **使用组合指标**：单一指标可能无法全面反映策略的性能。使用组合指标可以平衡多个方面的考量，如收益、风险和交易效率。

3. **添加约束条件**：约束条件可以帮助筛选出满足特定要求的参数组合，避免过度优化导致的过拟合。

4. **归一化处理**：在组合指标中，不同指标的量级和单位可能差异很大。使用归一化处理可以使它们具有可比性。
   - 当指标值差异特别大时（如收益率和持仓时间），推荐使用 `rank` 归一化，它完全基于排名而非原始值。
   - 当数据中可能存在异常值时，推荐使用 `percentile` 归一化，它基于中位数和四分位距，对异常值不敏感。
   - 当数据近似正态分布时，可以使用 `z_score` 归一化。
   - 当数据分布均匀且无异常值时，可以使用 `min_max` 归一化。

5. **避免过拟合**：过度优化可能导致过拟合，使策略在实际交易中表现不佳。建议使用交叉验证等方法评估策略的稳健性。
