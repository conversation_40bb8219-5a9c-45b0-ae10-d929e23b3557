# 优化算法

参数优化功能支持多种优化算法，用于在参数空间中寻找最优参数组合。本文档详细介绍了支持的优化算法及其适用场景。

## 网格搜索（Grid Search）

网格搜索是最简单直接的优化算法，它遍历参数空间中的所有可能组合，找出最优解。

### 工作原理

1. 根据参数定义，生成所有可能的参数组合。
2. 对每个参数组合，执行回测并计算性能指标。
3. 根据优化目标，找出性能最好的参数组合。

### 优点

- 简单直接，容易理解和实现。
- 保证找到全局最优解（在给定的参数空间内）。
- 结果可重现，不受随机因素影响。

### 缺点

- 计算量随参数数量和取值范围呈指数级增长，参数空间大时效率低下。
- 对于连续参数，只能在离散点上进行搜索，可能错过最优解。

### 适用场景

- 参数数量少（通常不超过 3-4 个）。
- 参数取值范围小，或者步长较大。
- 计算资源充足，或者单次评估耗时短。

### 配置示例

```json
{
  "optimization_algorithm": "grid_search",
  "max_iterations": 1000,
  "parallel_jobs": 8,
  "parameters": [
    {
      "name": "param1",
      "type": "float",
      "min": 0.1,
      "max": 0.5,
      "step": 0.1
    },
    {
      "name": "param2",
      "type": "int",
      "values": [1, 2, 3, 4, 5]
    }
  ]
}
```

### 实现细节

网格搜索算法的实现非常直接：

1. 使用 `parameter_space::generate_grid()` 方法生成所有可能的参数组合。
2. 对每个参数组合，调用评估回调函数执行回测并计算性能指标。
3. 根据优化目标，对结果进行排序，找出最优参数组合。

为了提高效率，实现了并行计算，可以同时评估多个参数组合。用户可以通过 `parallel_jobs` 参数指定并行任务数。

## 随机搜索（Random Search）

随机搜索是一种在参数空间中随机采样的优化算法，适用于参数空间较大的情况。

> 注意：随机搜索算法目前尚未实现，计划在未来版本中添加。

### 工作原理

1. 根据参数定义，在参数空间中随机生成参数组合。
2. 对每个参数组合，执行回测并计算性能指标。
3. 根据优化目标，找出性能最好的参数组合。

### 优点

- 对参数空间的覆盖更加均匀，不受网格限制。
- 在高维参数空间中更加高效，通常只需要网格搜索的一小部分计算量就能找到接近最优的解。
- 可以设置最大迭代次数，控制计算成本。

### 缺点

- 结果受随机因素影响，不保证可重现。
- 不保证找到全局最优解，但通常可以找到接近最优的解。

### 适用场景

- 参数数量多（通常超过 4 个）。
- 参数取值范围大，或者需要细粒度搜索。
- 计算资源有限，或者单次评估耗时长。

### 配置示例

```json
{
  "optimization_algorithm": "random_search",
  "max_iterations": 500,
  "parallel_jobs": 8,
  "parameters": [
    {
      "name": "param1",
      "type": "float",
      "min": 0.1,
      "max": 0.5
    },
    {
      "name": "param2",
      "type": "int",
      "min": 1,
      "max": 100
    }
  ]
}
```

## 贝叶斯优化（Bayesian Optimization）

贝叶斯优化是一种基于概率模型的优化算法，适用于计算成本高的场景。

> 注意：贝叶斯优化算法目前尚未实现，计划在未来版本中添加。

### 工作原理

1. 构建参数与目标函数关系的概率模型（通常是高斯过程）。
2. 根据已有结果，计算每个参数组合的期望改进（Expected Improvement）。
3. 选择期望改进最大的参数组合进行评估。
4. 更新概率模型，重复步骤 2-4，直到达到最大迭代次数。

### 优点

- 在较少的迭代次数内找到接近最优的解。
- 自适应采样，根据已有结果动态调整搜索方向。
- 适合计算成本高的场景，如单次评估耗时长。

### 缺点

- 实现复杂，需要构建和更新概率模型。
- 计算期望改进的过程可能比较耗时。
- 对初始点的选择比较敏感。

### 适用场景

- 单次评估耗时长，计算资源有限。
- 参数空间较大，但预算有限，无法进行大量评估。
- 目标函数较为平滑，没有大量局部最优点。

### 配置示例

```json
{
  "optimization_algorithm": "bayesian",
  "max_iterations": 100,
  "parallel_jobs": 4,
  "initial_points": 10,
  "acquisition_function": "expected_improvement",
  "parameters": [
    {
      "name": "param1",
      "type": "float",
      "min": 0.1,
      "max": 0.5
    },
    {
      "name": "param2",
      "type": "int",
      "min": 1,
      "max": 100
    }
  ]
}
```

## 遗传算法（Genetic Algorithm）

遗传算法是一种基于进化理论的优化算法，适用于复杂参数空间。

> 注意：遗传算法目前尚未实现，计划在未来版本中添加。

### 工作原理

1. 初始化一个参数组合的种群。
2. 评估每个个体的适应度（即目标函数值）。
3. 选择适应度高的个体作为父代。
4. 通过交叉和变异操作生成子代。
5. 用子代替换适应度低的个体，形成新的种群。
6. 重复步骤 2-5，直到达到最大迭代次数或满足终止条件。

### 优点

- 适用于复杂的参数空间，包括非连续、非线性和多模态的情况。
- 可以处理多目标优化问题。
- 不容易陷入局部最优解。

### 缺点

- 实现复杂，需要设计合适的编码、交叉和变异操作。
- 收敛速度可能较慢，需要较多的迭代次数。
- 结果受随机因素影响，不保证可重现。

### 适用场景

- 参数空间复杂，存在多个局部最优解。
- 参数之间存在复杂的相互作用。
- 多目标优化问题。

### 配置示例

```json
{
  "optimization_algorithm": "genetic",
  "max_iterations": 50,
  "population_size": 100,
  "crossover_rate": 0.8,
  "mutation_rate": 0.1,
  "parameters": [
    {
      "name": "param1",
      "type": "float",
      "min": 0.1,
      "max": 0.5
    },
    {
      "name": "param2",
      "type": "int",
      "min": 1,
      "max": 100
    }
  ]
}
```

## 算法选择指南

选择合适的优化算法取决于多种因素，包括参数空间的大小和复杂性、计算资源的限制、优化目标的特性等。以下是一些选择指南：

1. **网格搜索**：当参数数量少（≤ 3-4 个）且取值范围小时，网格搜索是最简单直接的选择。它保证找到全局最优解，但计算量可能很大。

2. **随机搜索**：当参数数量多（> 4 个）或取值范围大时，随机搜索通常比网格搜索更高效。它不保证找到全局最优解，但通常可以找到接近最优的解。

3. **贝叶斯优化**：当单次评估耗时长，计算资源有限时，贝叶斯优化是一个好选择。它通过构建概率模型，在较少的迭代次数内找到接近最优的解。

4. **遗传算法**：当参数空间复杂，存在多个局部最优解，或者是多目标优化问题时，遗传算法是一个好选择。它通过模拟自然选择过程，避免陷入局部最优解。

## 并行计算

所有优化算法都支持并行计算，可以同时评估多个参数组合，提高优化效率。用户可以通过 `parallel_jobs` 参数指定并行任务数。

并行计算的实现基于 C++ 标准库的线程池，每个工作线程负责评估一个参数组合。为了避免资源竞争，使用了互斥锁和条件变量来协调线程间的工作。

> 注意：并行任务数不宜设置过大，以免占用过多系统资源。建议根据系统的 CPU 核心数来设置，通常设置为 CPU 核心数的 1-2 倍较为合适。
