# 参数优化配置文件格式

参数优化功能使用 JSON 格式的配置文件来定义优化任务。本文档详细介绍了配置文件的格式和选项。

## 基本结构

参数优化配置文件的基本结构如下：

```json
{
  "base_config_path": "/path/to/strategy/config.json",
  "backtest_config_path": "/path/to/backtest/config.json",
  "output_path": "/path/to/output/directory",
  "optimization_algorithm": "grid_search",
  "optimization_target": {
    // 优化目标定义
  },
  "constraints": [
    // 约束条件定义
  ],
  "max_iterations": 1000,
  "parallel_jobs": 8,
  "parameters": [
    // 参数定义
  ]
}
```

## 字段说明

### 基本配置

- `base_config_path`（必需）：策略配置文件的路径。这是要优化的策略的基础配置文件。
- `backtest_config_path`（必需）：回测配置文件的路径。这是用于运行回测的配置文件。
- `output_path`（必需）：输出目录的路径。优化结果和报告将保存在这个目录中。
- `optimization_algorithm`（必需）：优化算法的名称。目前支持 `"grid_search"`。
- `max_iterations`（可选）：最大迭代次数。默认为 1000。
- `parallel_jobs`（可选）：并行任务数。默认为 1。

### 优化目标

`optimization_target` 字段定义了优化的目标函数。它可以是单一指标或组合指标。

#### 单一指标

```json
"optimization_target": {
  "type": "single",
  "metric": "sharpe_ratio",
  "normalize": true
}
```

- `type`：优化目标类型，设置为 `"single"` 表示单一指标。
- `metric`：指标名称，如 `"sharpe_ratio"`、`"return"` 等。
- `normalize`（可选）：是否归一化指标值。可以是布尔值或字符串（`"min_max"`、`"z_score"`、`"percentile"`）。

#### 组合指标

```json
"optimization_target": {
  "type": "composite",
  "components": [
    {
      "metric": "sharpe_ratio",
      "weight": 0.4,
      "transform": "identity",
      "description": "夏普比率"
    },
    {
      "metric": "return",
      "weight": 0.3,
      "transform": "identity",
      "description": "总收益率"
    },
    {
      "metric": "avg_position_time",
      "weight": 0.3,
      "transform": "inverse",
      "description": "平均持仓时间（越短越好）"
    }
  ],
  "normalize": "z_score",
  "description": "综合考虑收益率、风险调整收益和交易效率的优化目标"
}
```

- `type`：优化目标类型，设置为 `"composite"` 表示组合指标。
- `components`：组件列表，每个组件包含以下字段：
  - `metric`：指标名称。
  - `weight`（可选）：权重，默认为 1.0。
  - `transform`（可选）：转换函数，可以是 `"identity"`（保持原值）、`"inverse"`（取倒数）、`"negative"`（取负值）、`"log"`（取对数）、`"exp"`（取指数）或 `"threshold"`（阈值函数）。默认为 `"identity"`。
  - `transform_param`（可选）：转换函数的参数，仅在 `transform` 为 `"threshold"` 时使用。
  - `description`（可选）：描述。
- `normalize`（可选）：归一化类型，可以是 `"min_max"`、`"z_score"` 或 `"percentile"`。默认为 `"none"`。
- `description`（可选）：描述。

### 约束条件

`constraints` 字段定义了优化过程中的约束条件。它是一个约束条件对象的数组。

```json
"constraints": [
  {
    "metric": "max_drawdown",
    "operator": "<=",
    "value": 0.2,
    "description": "最大回撤不超过20%"
  },
  {
    "metric": "win_rate",
    "operator": ">=",
    "value": 0.5,
    "description": "胜率不低于50%"
  }
]
```

每个约束条件对象包含以下字段：

- `metric`：指标名称。
- `operator`：操作符，可以是 `"<"`、`"<="`、`"=="`、`">="` 或 `">"`。
- `value`：阈值。
- `description`（可选）：描述。

### 参数定义

`parameters` 字段定义了要优化的参数。它是一个参数对象的数组。

```json
"parameters": [
  {
    "name": "grid_spacing",
    "type": "float",
    "min": 0.002,
    "max": 0.005,
    "step": 0.0005,
    "description": "网格间距"
  },
  {
    "name": "min_markup",
    "type": "float",
    "min": 0.01,
    "max": 0.03,
    "step": 0.005,
    "description": "最小加价"
  },
  {
    "name": "gamma",
    "type": "int",
    "values": [1, 2, 3, 4, 5],
    "description": "伽马参数"
  },
  {
    "name": "use_dynamic_grid",
    "type": "boolean",
    "description": "是否使用动态网格"
  },
  {
    "name": "grid_type",
    "type": "enum",
    "values": ["linear", "exponential", "fibonacci"],
    "description": "网格类型"
  }
]
```

每个参数对象包含以下字段：

- `name`：参数名称。直接对应策略配置中的参数名。
- `type`：参数类型，可以是 `"int"`、`"float"`、`"boolean"`、`"string"` 或 `"enum"`。
- `description`（可选）：描述。
- `optimize`（可选）：是否需要优化。默认为 `true`。

根据参数类型，还需要提供以下字段：

#### 数值型参数（int 和 float）

可以通过范围和步长定义：

- `min`：最小值。
- `max`：最大值。
- `step`（可选）：步长。对于 `int` 类型，默认为 1；对于 `float` 类型，默认为 0.1。

或者通过离散值列表定义：

- `values`：可能的值列表。

#### 布尔型参数（boolean）

不需要额外字段，会自动使用 `[false, true]` 作为可能的值。

#### 字符串型参数（string 和 enum）

- `values`：可能的值列表。

#### 默认值

对于所有类型的参数，都可以指定默认值：

- `default`：默认值。如果不指定，将使用最小值或第一个值作为默认值。

## 参数设置

在当前实现中，所有参数都会被设置到策略配置的 `symbol_configs[0]` 对象中。这意味着参数优化器只支持优化单一交易品种的参数。

### 嵌套参数

参数优化器支持优化嵌套参数，例如 `indicator_settings.tick_ema.span`。嵌套参数使用点号（`.`）分隔各个层级。例如：

```json
{
  "name": "indicator_settings.tick_ema.span",
  "type": "int",
  "min": 300,
  "max": 700,
  "step": 100,
  "description": "EMA跨度"
}
```

这将优化 `symbol_configs[0].indicator_settings.tick_ema.span` 参数。

## 完整示例

以下是一个完整的参数优化配置文件示例：

```json
{
  "base_config_path": "/home/<USER>/git/fast_trader_elite/cpp_backtest/examples/market_making_config.json",
  "backtest_config_path": "/home/<USER>/git/fast_trader_elite/cpp_backtest/config/backtest_config.json",
  "output_path": "/home/<USER>/git/fast_trader_elite/cpp_backtest/optimization_results",
  "optimization_algorithm": "grid_search",
  "optimization_target": {
    "type": "composite",
    "components": [
      {
        "metric": "sharpe_ratio",
        "weight": 0.4,
        "transform": "identity",
        "description": "夏普比率"
      },
      {
        "metric": "return",
        "weight": 0.3,
        "transform": "identity",
        "description": "总收益率"
      },
      {
        "metric": "avg_position_time",
        "weight": 0.3,
        "transform": "inverse",
        "description": "平均持仓时间（越短越好）"
      }
    ],
    "normalize": "z_score",
    "description": "综合考虑收益率、风险调整收益和交易效率的优化目标"
  },
  "constraints": [
    {
      "metric": "max_drawdown",
      "operator": "<=",
      "value": 0.2,
      "description": "最大回撤不超过20%"
    },
    {
      "metric": "win_rate",
      "operator": ">=",
      "value": 0.5,
      "description": "胜率不低于50%"
    }
  ],
  "max_iterations": 1000,
  "parallel_jobs": 8,
  "parameters": [
    {
      "name": "grid_spacing",
      "type": "float",
      "min": 0.002,
      "max": 0.005,
      "step": 0.0005,
      "description": "网格间距"
    },
    {
      "name": "min_markup",
      "type": "float",
      "min": 0.01,
      "max": 0.03,
      "step": 0.005,
      "description": "最小加价"
    },
    {
      "name": "gamma",
      "type": "int",
      "values": [1, 2, 3, 4, 5],
      "description": "伽马参数"
    },
    {
      "name": "indicator_settings.tick_ema.span",
      "type": "int",
      "min": 300,
      "max": 700,
      "step": 100,
      "description": "EMA跨度"
    },
    {
      "name": "indicator_settings.tick_ema.spread",
      "type": "float",
      "min": 0.005,
      "max": 0.025,
      "step": 0.005,
      "description": "EMA价差"
    }
  ]
}
```
