# 使用指南

本文档详细介绍了如何使用Fast Trader Elite回测系统进行策略回测和参数优化。

## 前提条件

在使用回测系统之前，请确保：

1. 已经编译好回测系统和策略库
2. 准备好市场数据文件
3. 创建好回测配置文件和策略配置文件

## 基本使用流程

### 1. 准备配置文件

创建回测配置文件（例如`backtest_config.json`）和策略配置文件（例如`strategy_config.json`）。详细配置说明请参考[配置文件详解](configuration.md)。

### 2. 准备市场数据

回测系统使用二进制格式的市场数据文件。您可以使用提供的数据预处理工具将原始市场数据转换为二进制格式：

```bash
# 使用数据预处理工具处理市场数据
./bin/preprocess_data --config=config.json --output_file=data.bin
```

详细的数据预处理说明请参考[数据预处理指南](data_preprocessing.md)。

### 3. 编译策略库

将您的策略实现为动态库（.so文件）：

```bash
# 编译策略库
cd /home/<USER>/git/fast_trader_elite/cpp_backtest
mkdir -p build && cd build
cmake ..
make
```

### 4. 运行回测

使用回测引擎运行回测：

```bash
# 运行回测
./bin/backtest_engine --config /path/to/backtest_config.json
```

### 5. 分析结果

回测完成后，结果将保存在配置文件中指定的输出路径。结果包括：

- `equity.csv`：权益曲线数据
- `trades.csv`：交易记录
- `metrics.json`：性能指标
- `pnl.csv`：盈亏数据

您可以使用这些数据进行进一步分析和可视化。

## 命令行参数

回测引擎支持以下命令行参数：

- `--config`：指定回测配置文件路径
- `--start_time`：指定回测开始时间，格式为"YYYY-MM-DD HH:MM:SS"（覆盖配置文件中的设置）
- `--end_time`：指定回测结束时间，格式为"YYYY-MM-DD HH:MM:SS"（覆盖配置文件中的设置）
- `--data_file`：指定市场数据文件路径（覆盖配置文件中的设置）
- `--output_path`：指定结果输出路径（覆盖配置文件中的设置）
- `--strategy_config`：指定策略配置文件路径（覆盖配置文件中的设置）
- `--log_level`：指定日志级别（覆盖配置文件中的设置）

示例：

```bash
./bin/backtest_engine --config /path/to/backtest_config.json --start_time "2024-01-01 08:00:00" --end_time "2024-01-31 08:00:00" --log_level DEBUG
```

## 参数优化

回测系统提供了参数优化功能，可以帮助您找到最佳的策略参数组合。

### 1. 准备参数优化配置文件

创建参数优化配置文件（例如`optimization_config.json`）。详细配置说明请参考[参数优化配置](parameter_optimizer/configuration.md)。

### 2. 运行参数优化

使用参数优化器运行优化：

```bash
# 运行参数优化
./bin/parameter_optimizer --config /path/to/optimization_config.json
```

### 3. 分析优化结果

优化完成后，结果将保存在配置文件中指定的输出路径。结果包括：

- `optimization_results.csv`：所有参数组合的性能指标
- `best_config.json`：最佳参数组合的配置文件
- `optimization_report.html`：优化结果报告（如果启用了报告生成）

## 高级用法

### 自定义模型

回测系统支持自定义模型，包括订单队列模型、延迟模型、手续费模型和资产模型。您可以通过继承相应的基类并实现所需的接口来创建自定义模型。

### 批量回测

您可以编写脚本来批量运行多个回测，例如测试不同的时间段或不同的策略参数：

```bash
#!/bin/bash

# 批量回测不同时间段
for month in {1..12}; do
    start_date="2024-${month}-01 00:00:00"
    end_date="2024-${month}-31 23:59:59"

    ./bin/backtest_engine --config /path/to/backtest_config.json \
                         --start_time "$start_date" \
                         --end_time "$end_date" \
                         --output_path "/path/to/output/month_${month}"
done
```

### 集成到其他系统

回测系统可以集成到其他系统中，例如交易平台或研究环境。您可以使用回测系统的API来实现集成：

```cpp
#include <fast_trader_elite/cpp_backtest/backtest_engine.h>

// 创建回测引擎
auto engine = std::make_unique<fast_trader_elite::cpp_backtest::backtest_engine>();

// 加载配置
engine->load_config("/path/to/backtest_config.json");

// 运行回测
engine->run();

// 获取结果
auto results = engine->get_results();

// 分析结果
double return_rate = results.get_return(0);
double sharpe_ratio = results.get_sharpe_ratio(0);
double max_drawdown = results.get_max_drawdown(0);

std::cout << "Return: " << return_rate << std::endl;
std::cout << "Sharpe Ratio: " << sharpe_ratio << std::endl;
std::cout << "Max Drawdown: " << max_drawdown << std::endl;
```

## 常见问题

### 如何调试策略？

1. 将日志级别设置为DEBUG
2. 在策略中添加详细的日志输出
3. 运行回测并分析日志文件
4. 使用回测结果中的交易记录和权益曲线进行分析

### 如何处理大量市场数据？

1. 将市场数据分割为多个文件
2. 使用批量回测脚本分别处理每个文件
3. 合并结果进行分析

### 如何评估策略性能？

回测系统提供了多种性能指标，包括：

- 收益率
- 年化收益率
- 夏普比率
- 索提诺比率
- 最大回撤
- 收益回撤比
- 胜率
- 盈亏比
- 平均持仓时间
- 日均交易量
- 最大杠杆

您可以根据自己的需求选择合适的指标来评估策略性能。

### 如何优化策略参数？

1. 确定需要优化的参数及其取值范围
2. 创建参数优化配置文件
3. 运行参数优化
4. 分析优化结果，选择最佳参数组合
5. 使用最佳参数组合进行回测验证

## 下一步

- [示例说明](examples.md)：查看回测系统的使用示例
- [参数优化](parameter_optimizer/overview.md)：了解如何使用参数优化功能
