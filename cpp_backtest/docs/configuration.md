# 配置文件详解

回测系统使用JSON格式的配置文件来设置回测参数、模型选择和策略参数。本文档详细介绍了配置文件的结构和各个参数的含义。

## 配置文件结构

回测配置文件主要包含以下几个部分：

```json
{
  "backtest": {
    // 回测相关配置
  },
  "common": {
    // 通用配置
  },
  "instruments": [
    // 交易品种配置
  ],
  "strategy": {
    // 策略配置
  }
}
```

## 回测配置（backtest）

回测部分包含与回测执行相关的参数：

```json
"backtest": {
  "start_time": "2024-01-01 08:00:00",
  "end_time": "2024-02-01 08:00:00",
  "data_file": "/path/to/data.bin",
  "output_path": "/path/to/output",
  "exchange": "no_partial_fill",
  "initial_balance": 10000.0,
  "latency": {
    // 延迟模型配置
  },
  "queue": {
    // 队列模型配置
  },
  "fee": {
    // 手续费模型配置
  },
  "asset": {
    // 资产模型配置
  }
}
```

### 参数说明

- `start_time`：回测开始时间，格式为"YYYY-MM-DD HH:MM:SS"
- `end_time`：回测结束时间，格式为"YYYY-MM-DD HH:MM:SS"
- `data_file`：市场数据文件路径
- `output_path`：回测结果输出路径
- `exchange`：交易所模拟器类型，可选值：
  - `no_partial_fill`：不支持部分成交的交易所
  - `partial_fill`：支持部分成交的交易所
- `initial_balance`：初始资金

### 延迟模型配置（latency）

```json
"latency": {
  "type": "constant",
  "order_latency": 100,
  "cancel_latency": 100,
  "md_latency": 50,
  "response_latency": 50
}
```

#### 常数延迟模型（constant）

- `order_latency`：下单延迟（纳秒）
- `cancel_latency`：撤单延迟（纳秒）
- `md_latency`：行情数据延迟（纳秒）
- `response_latency`：响应延迟（纳秒）

#### 随机延迟模型（random）

```json
"latency": {
  "type": "random",
  "order_latency_mean": 100,
  "order_latency_std": 20,
  "cancel_latency_mean": 100,
  "cancel_latency_std": 20,
  "md_latency_mean": 50,
  "md_latency_std": 10,
  "response_latency_mean": 50,
  "response_latency_std": 10
}
```

- `*_mean`：平均延迟（纳秒）
- `*_std`：延迟标准差（纳秒）

#### 指数延迟模型（exponential）

```json
"latency": {
  "type": "exponential",
  "order_latency_lambda": 0.01,
  "cancel_latency_lambda": 0.01,
  "md_latency_lambda": 0.02,
  "response_latency_lambda": 0.02
}
```

- `*_lambda`：指数分布参数

### 队列模型配置（queue）

```json
"queue": {
  "type": "prob",
  "power": 2.0
}
```

#### 概率队列模型（prob）

- `power`：幂函数参数，控制队列位置变化的概率分布

#### FIFO队列模型（fifo）

```json
"queue": {
  "type": "fifo"
}
```

### 手续费模型配置（fee）

```json
"fee": {
  "type": "fixed_fee",
  "maker_fee": 0.0002,
  "taker_fee": 0.0004
}
```

#### 固定手续费模型（fixed_fee）

- `maker_fee`：做市商手续费率
- `taker_fee`：吃单手续费率

#### 分层手续费模型（tiered_fee）

```json
"fee": {
  "type": "tiered_fee",
  "tiers": [
    {
      "volume": 0,
      "maker_fee": 0.0002,
      "taker_fee": 0.0004
    },
    {
      "volume": 10000,
      "maker_fee": 0.00015,
      "taker_fee": 0.00035
    }
  ]
}
```

- `tiers`：费率层级，按交易量排序
  - `volume`：交易量阈值
  - `maker_fee`：该层级的做市商手续费率
  - `taker_fee`：该层级的吃单手续费率

### 资产模型配置（asset）

```json
"asset": {
  "type": "linear",
  "contract_value": 1.0
}
```

#### 线性资产模型（linear）

- `contract_value`：合约价值，通常为1.0

#### 反向资产模型（inverse）

```json
"asset": {
  "type": "inverse",
  "contract_value": 1.0
}
```

## 通用配置（common）

```json
"common": {
  "log_dir": "../log/backtest",
  "log_level": "INFO"
}
```

- `log_dir`：日志目录
- `log_level`：日志级别，可选值：
  - `DEBUG`：调试级别
  - `INFO`：信息级别
  - `WARNING`：警告级别
  - `ERROR`：错误级别

## 交易品种配置（instruments）

```json
"instruments": [
  {
    "exchange": "bybit",
    "instrument": "BTCUSDT",
    "instrument_idx": 1
  }
]
```

- `exchange`：交易所名称
- `instrument`：交易品种名称
- `instrument_idx`：交易品种索引，用于在策略中引用

## 策略配置（strategy）

```json
"strategy": {
  "path": "/path/to/libstrategy.so",
  "config_path": "/path/to/strategy_config.json",
  "log_level": "INFO",
  "log_file": "../log/backtest/strategy.log"
}
```

- `path`：策略动态库路径
- `config_path`：策略配置文件路径
- `log_level`：策略日志级别
- `log_file`：策略日志文件路径

## 完整配置示例

```json
{
  "backtest": {
    "start_time": "2024-01-01 08:00:00",
    "end_time": "2024-02-01 08:00:00",
    "data_file": "/home/<USER>/git/fast_trader_elite/cpp_backtest/build/bin/test.bin",
    "output_path": "/home/<USER>/git/fast_trader_elite/cpp_backtest/build/output",
    "exchange": "no_partial_fill",
    "initial_balance": 10000.0,
    "latency": {
      "type": "constant",
      "order_latency": 100,
      "cancel_latency": 100,
      "md_latency": 50,
      "response_latency": 50
    },
    "queue": {
      "type": "prob",
      "power": 2.0
    },
    "fee": {
      "type": "fixed_fee",
      "maker_fee": 0.0002,
      "taker_fee": 0.0004
    },
    "asset": {
      "type": "linear",
      "contract_value": 1.0
    }
  },
  "common": {
    "log_dir": "../log/backtest",
    "log_level": "INFO"
  },
  "instruments": [
    {
      "exchange": "bybit",
      "instrument": "BTCUSDT",
      "instrument_idx": 1
    }
  ],
  "strategy": {
    "path": "/home/<USER>/git/fast_trader_elite/cpp_backtest/build/lib/libstrategy.so",
    "config_path": "/home/<USER>/git/fast_trader_elite/cpp_backtest/examples/strategy_config.json",
    "log_level": "INFO",
    "log_file": "../log/backtest/strategy.log"
  }
}
```
