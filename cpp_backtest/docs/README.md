# Fast Trader Elite 回测系统文档

欢迎使用Fast Trader Elite回测系统！本文档提供了系统的详细说明和使用指南。

## 文档目录

### 基础文档

- [系统概述](overview.md)：回测系统的基本介绍和架构说明
- [配置文件详解](configuration.md)：配置文件的结构和参数说明
- [数据预处理](data_preprocessing.md)：如何使用数据预处理工具
- [使用指南](usage.md)：如何使用回测系统进行策略回测
- [示例说明](examples.md)：回测系统的使用示例

### 参数优化文档

- [优化器概述](parameter_optimizer/overview.md)：参数优化功能的基本介绍
- [优化算法](parameter_optimizer/algorithms.md)：支持的优化算法说明
- [优化目标](parameter_optimizer/optimization_targets.md)：优化目标的定义和使用
- [配置说明](parameter_optimizer/configuration.md)：参数优化配置文件的说明
- [使用示例](parameter_optimizer/examples.md)：参数优化的使用示例

## 快速入门

### 安装和编译

```bash
# 克隆代码库
git clone https://github.com/your-username/fast_trader_elite.git
cd fast_trader_elite/cpp_backtest

# 编译
mkdir -p build && cd build
cmake ..
make
```

### 运行回测

```bash
# 运行回测
cd build
./bin/backtest_engine --config /path/to/backtest_config.json
```

### 运行参数优化

```bash
# 运行参数优化
cd build
./bin/parameter_optimizer --config /path/to/optimization_config.json
```

## 系统要求

- C++17或更高版本
- CMake 3.10或更高版本
- nlohmann_json库
- fmt库
- 支持的操作系统：Linux（推荐Ubuntu 20.04或更高版本）

## 贡献指南

我们欢迎您对Fast Trader Elite回测系统做出贡献。如果您发现了bug或有改进建议，请提交issue或pull request。

## 许可证

Fast Trader Elite回测系统采用[MIT许可证](LICENSE)。
