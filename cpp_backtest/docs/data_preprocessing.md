# 数据预处理工具使用指南

本文档详细介绍了如何使用 Fast Trader Elite 回测系统中的数据预处理工具。

## 概述

数据预处理是回测过程中的重要一步，它将原始市场数据转换为回测系统可以使用的格式。Fast Trader Elite 回测系统提供了 `preprocess_data` 工具，用于处理不同来源的市场数据。

## 支持的数据源

目前，数据预处理工具支持以下数据源：

1. **Bybit 交易所数据**：
   - 深度行情数据（JSON 格式）
   - 成交数据（CSV 格式）
   - K线数据（CSV 格式）

2. **SHM TAR 文件**：
   - 共享内存记录文件

## 基本用法

### 命令行格式

```bash
preprocess_data --config=<config_file> --output_file=<output_file> [options]
```

### 参数说明

- `--config=<config_file>`：配置文件路径，JSON 格式
- `--output_file=<output_file>`：输出文件路径，二进制格式
- `[options]`：可选参数

### 可选参数

- `--parallel=<bool>`：是否使用并行算法进行排序（true/false，默认为 false，需要 TBB 库支持）
- `--shm_tar_path=<path>`：指定 SHM TAR 文件路径（覆盖配置文件中的设置）

## 配置文件格式

### Bybit 交易所数据配置

```json
{
    "exchange": "bybit",
    "instrument": {
        "symbol": "BTCUSDT",
        "tick_size": 0.1,
        "step_size": 0.001,
        "price_tick": 0.000001,
        "depth_level": 5
    },
    "depth_data_path": "path/to/depth_data.json",
    "transaction_data_path": "path/to/transaction_data.csv",
    "kline_data_path": "path/to/kline_data.csv",
    "latency": 1000000
}
```

### SHM TAR 文件配置

```json
{
    "exchange": "shm_tar",
    "shm_tar_path": "path/to/record_bybit.shm",
    "start_page": 0,
    "end_page": 10,
    "instrument": {
        "symbol": "BTCUSDT",
        "tick_size": 0.1,
        "step_size": 0.001
    }
}
```

## 使用示例

### 处理 Bybit 交易所数据

```bash
# 使用标准算法
preprocess_data --config=bybit_config.json --output_file=bybit_data.bin

# 使用并行算法
preprocess_data --config=bybit_config.json --output_file=bybit_data.bin --parallel=true

# 显式指定使用串行算法
preprocess_data --config=bybit_config.json --output_file=bybit_data.bin --parallel=false
```

### 处理 SHM TAR 文件

```bash
# 使用配置文件中指定的 SHM TAR 路径
preprocess_data --config=shm_tar_config.json --output_file=shm_tar_data.bin

# 使用命令行指定的 SHM TAR 路径
preprocess_data --config=shm_tar_config.json --output_file=shm_tar_data.bin --shm_tar_path=/path/to/new_record.shm

# 使用并行算法和命令行指定的 SHM TAR 路径
preprocess_data --config=shm_tar_config.json --output_file=shm_tar_data.bin --parallel=true --shm_tar_path=/path/to/new_record.shm
```

## 性能优化

数据预处理工具提供了多种性能优化选项：

### 并行排序

使用 `--parallel=true` 选项可以启用并行排序算法，显著提高大数据集的处理速度。这需要系统安装 Intel TBB 库。

```bash
preprocess_data --config=config.json --output_file=output.bin --parallel=true
```

### 内存映射文件

数据预处理工具内部使用内存映射文件技术进行文件读写，这可以显著提高大文件的处理速度。内存映射文件的主要优势包括：

1. **减少系统调用**：传统 I/O 每次读写都需要系统调用，而内存映射只需要少量系统调用即可完成整个文件的读写
2. **减少数据复制**：传统 I/O 需要在内核空间和用户空间之间复制数据，内存映射直接在用户空间访问文件数据
3. **利用操作系统页缓存**：内存映射利用操作系统的页缓存机制，提高缓存效率
4. **支持并行访问**：多个线程可以同时访问内存映射区域，提高并行处理效率

这些优化对于处理大型数据文件（如高频交易数据）特别有效，可以将文件读写速度提高数倍。

### 性能比较

下表展示了不同优化选项的性能比较（基于典型的高频交易数据集）：

| 优化选项 | 相对性能提升 | 适用场景 |
|---------|------------|---------|
| 标准 I/O | 基准线 (1x) | 小数据集，低频数据 |
| 内存映射文件 | 2-5x | 大文件，I/O 瓶颈系统 |
| 并行排序 | 2-8x（取决于核心数） | 大数据集，CPU 瓶颈系统 |
| 内存映射 + 并行排序 | 4-10x | 大数据集，混合瓶颈系统 |

*注：实际性能提升取决于硬件配置、数据大小和特性。*

## 输出文件格式

预处理后的数据将保存为二进制格式，包含以下信息：

- 文件头：魔数、版本号、记录数量、品种数量、开始时间、结束时间
- 品种信息：品种ID、交易所ID、价格精度、数量精度等
- 记录数据：每条记录包含头部和数据部分
  - 记录头部：记录类型、标志、品种索引、交易所时间戳、本地时间戳、数据大小
  - 记录数据：根据记录类型不同，包含深度行情、成交数据或K线数据

## 常见问题

### 如何处理大量数据？

对于非常大的数据集，建议：

1. 使用 `--parallel=true` 选项启用并行排序
2. 将数据分割为多个较小的文件分别处理
3. 使用高性能存储设备（如 SSD）

### 并行和非并行处理结果不一致怎么办？

如果您发现并行和非并行处理的结果不一致，可能是由于以下原因：

1. 排序算法的稳定性问题
2. 数据中存在重复的时间戳
3. TBB 库版本问题

解决方法：

1. 确保使用最新版本的 TBB 库
2. 如果问题仍然存在，请使用 `--parallel=false` 选项禁用并行处理

### 如何验证预处理结果？

预处理工具会在处理过程中进行事件顺序验证，确保数据的时间顺序正确。如果验证失败，工具会输出错误信息。

您也可以使用回测系统的其他工具查看预处理后的数据：

```bash
./bin/view_binary_data --input_file=output.bin
```

### 如何安装 TBB 库？

在 Ubuntu/Debian 系统上：

```bash
sudo apt-get install libtbb-dev
```

在 CentOS/RHEL 系统上：

```bash
sudo yum install tbb-devel
```

## 下一步

- [回测系统使用指南](usage.md)：了解如何使用预处理后的数据进行回测
- [配置文件详解](configuration.md)：了解更多配置选项
