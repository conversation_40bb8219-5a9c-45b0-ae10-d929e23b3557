# 回测系统概述

## 简介

Fast Trader Elite 回测系统是一个高性能的C++回测框架，专为高频交易策略设计。该系统提供了精确的市场模拟、订单执行和性能评估功能，使交易者能够在真实市场环境之前测试和优化他们的交易策略。

## 系统特点

- **高性能**：使用C++实现，提供高效的回测执行速度
- **精确模拟**：模拟真实市场环境，包括订单队列、延迟和手续费
- **灵活配置**：通过JSON配置文件提供灵活的系统配置
- **多种模型**：支持多种订单队列模型、延迟模型和手续费模型
- **参数优化**：内置参数优化功能，支持网格搜索、随机搜索、遗传算法和TPE优化
- **性能指标**：提供全面的性能评估指标，如收益率、夏普比率、最大回撤等
- **数据导出**：支持将回测结果导出为CSV文件，便于进一步分析

## 系统架构

回测系统由以下主要组件组成：

1. **回测引擎**：负责协调整个回测过程，包括数据加载、策略执行和结果计算
2. **交易所模拟器**：模拟交易所的行为，包括订单匹配和执行
3. **订单队列模型**：模拟订单在交易所订单簿中的位置和执行顺序
4. **延迟模型**：模拟网络延迟和交易所处理延迟
5. **手续费模型**：计算交易手续费
6. **资产模型**：处理不同类型资产的特性，如线性合约和反向合约
7. **策略接口**：提供统一的策略接口，便于用户实现自己的交易策略
8. **参数优化器**：优化策略参数，寻找最佳参数组合

## 工作流程

回测系统的基本工作流程如下：

1. **配置系统**：通过JSON配置文件设置回测参数、模型选择和策略参数
2. **加载数据**：从二进制文件或其他数据源加载市场数据
3. **初始化策略**：加载和初始化交易策略
4. **执行回测**：按时间顺序处理市场数据，执行策略逻辑，模拟订单执行
5. **计算结果**：计算各种性能指标，如收益率、夏普比率、最大回撤等
6. **导出结果**：将回测结果导出为CSV文件或其他格式
7. **参数优化**（可选）：使用参数优化器寻找最佳参数组合

## 支持的模型

### 订单队列模型

- **概率队列模型**：基于概率分布模拟订单在队列中的位置变化
- **FIFO队列模型**：先进先出模型，简化的队列模拟

### 延迟模型

- **常数延迟模型**：使用固定的延迟值
- **随机延迟模型**：基于正态分布的随机延迟
- **指数延迟模型**：基于指数分布的随机延迟

### 手续费模型

- **固定手续费模型**：使用固定的maker和taker费率
- **分层手续费模型**：基于交易量的分层费率
- **自定义手续费模型**：支持自定义费率计算逻辑

### 资产模型

- **线性资产模型**：适用于线性合约（如USDT合约）
- **反向资产模型**：适用于反向合约（如BTC合约）

## 下一步

- [配置文件详解](configuration.md)：了解如何配置回测系统
- [使用指南](usage.md)：学习如何使用回测系统
- [示例说明](examples.md)：查看回测系统的使用示例
- [参数优化](parameter_optimizer/overview.md)：了解如何使用参数优化功能
