# 示例说明

本文档提供了Fast Trader Elite回测系统的使用示例，帮助您快速上手和理解系统的功能。

## 基本回测示例

以下是一个基本的回测示例，展示了如何使用回测系统测试一个简单的做市策略。

### 1. 回测配置文件

首先，创建回测配置文件`backtest_config.json`：

```json
{
  "backtest": {
    "start_time": "2024-01-01 08:00:00",
    "end_time": "2024-02-01 08:00:00",
    "data_file": "/home/<USER>/git/fast_trader_elite/cpp_backtest/build/bin/test.bin",
    "output_path": "/home/<USER>/git/fast_trader_elite/cpp_backtest/build/output",
    "exchange": "no_partial_fill",
    "initial_balance": 10000.0,
    "latency": {
      "type": "constant",
      "order_latency": 100,
      "cancel_latency": 100,
      "md_latency": 50,
      "response_latency": 50
    },
    "queue": {
      "type": "prob",
      "power": 2.0
    },
    "fee": {
      "type": "fixed_fee",
      "maker_fee": 0.0002,
      "taker_fee": 0.0004
    },
    "asset": {
      "type": "linear",
      "contract_value": 1.0
    }
  },
  "common": {
    "log_dir": "../log/backtest",
    "log_level": "INFO"
  },
  "instruments": [
    {
      "exchange": "bybit",
      "instrument": "BTCUSDT",
      "instrument_idx": 1
    }
  ],
  "strategy": {
    "path": "/home/<USER>/git/fast_trader_elite/cpp_backtest/build/lib/libmarket_making.so",
    "config_path": "/home/<USER>/git/fast_trader_elite/cpp_backtest/examples/market_making_config.json",
    "log_level": "INFO",
    "log_file": "../log/backtest/strategy.log"
  }
}
```

### 2. 策略配置文件

然后，创建策略配置文件`market_making_config.json`：

```json
{
  "log_level": 1,
  "cross_mode": true,
  "symbol_configs": [
    {
      "symbol": "BTCUSDT",
      "trading_account_id": 0,
      "init_usdt": 100,
      "logging_level": 1,
      "refresh_window": 3,
      "balance_pct": 0.2,
      "ddown_factor": 1,
      "entry_qty_pct": 0.17,
      "min_close_qty_multiplier": 0.3,
      "leverage": 10,
      "n_entry_orders": 2,
      "n_close_orders": 3,
      "grid_spacing": 0.0035,
      "grid_coefficient": 30,
      "min_markup": 0.02,
      "max_markup": 0.025,
      "gamma": 3,
      "ewma_vol_ratio": 0,
      "natr_ratio": 0.05,
      "indicator_settings": {
        "do_long": true,
        "do_shrt": true,
        "funding_fee_collect_mode": false,
        "kline_sec": 180,
        "tick_ema": {
          "span": 500,
          "spread": 0.015,
          "ema_shift_multi": -0.2,
          "first_imbalance": 0.25,
          "num_std": 1.5,
          "std_spacing": 0.35,
          "std_width": 3
        }
      },
      "market_stop_loss": true,
      "stop_loss_liq_diff": 0.38,
      "stop_loss_pos_price_diff": 0.05,
      "stop_loss_pos_reduction": 0.56,
      "stop_loss_cooldown": 600,
      "stop_loss_danger": 0.01,
      "volbars_in_day": 2880,
      "n_reverse_qty": 1.5
    }
  ]
}
```

### 3. 运行回测

使用以下命令运行回测：

```bash
cd /home/<USER>/git/fast_trader_elite/cpp_backtest/build
./bin/backtest_engine --config /home/<USER>/git/fast_trader_elite/cpp_backtest/config/backtest_config.json
```

### 4. 分析结果

回测完成后，结果将保存在`/home/<USER>/git/fast_trader_elite/cpp_backtest/build/output`目录中。您可以查看以下文件：

- `equity.csv`：权益曲线数据
- `trades.csv`：交易记录
- `metrics.json`：性能指标
- `pnl.csv`：盈亏数据

## 参数优化示例

以下是一个参数优化示例，展示了如何使用参数优化功能找到最佳的策略参数组合。

### 1. 参数优化配置文件

创建参数优化配置文件`optimization_config.json`：

```json
{
  "base_config_path": "/home/<USER>/git/fast_trader_elite/cpp_backtest/examples/market_making_config.json",
  "backtest_config_path": "/home/<USER>/git/fast_trader_elite/cpp_backtest/config/backtest_config.json",
  "output_path": "/home/<USER>/git/fast_trader_elite/cpp_backtest/optimization_results",
  "optimization_algorithm": "grid_search",
  "optimization_target": {
    "type": "composite",
    "components": [
      {
        "metric": "sharpe_ratio",
        "weight": 0.4,
        "transform": "identity",
        "description": "夏普比率"
      },
      {
        "metric": "return",
        "weight": 0.3,
        "transform": "identity",
        "description": "总收益率"
      },
      {
        "metric": "avg_position_time",
        "weight": 0.3,
        "transform": "inverse",
        "description": "平均持仓时间（越短越好）"
      }
    ],
    "normalize": "min_max",
    "description": "综合考虑收益率、风险调整收益和交易效率的优化目标"
  },
  "parameters": [
    {
      "name": "symbol_configs[0].grid_spacing",
      "type": "double",
      "min": 0.001,
      "max": 0.01,
      "step": 0.001,
      "description": "网格间距"
    },
    {
      "name": "symbol_configs[0].min_markup",
      "type": "double",
      "min": 0.01,
      "max": 0.05,
      "step": 0.005,
      "description": "最小加价"
    },
    {
      "name": "symbol_configs[0].max_markup",
      "type": "double",
      "min": 0.015,
      "max": 0.06,
      "step": 0.005,
      "description": "最大加价"
    }
  ],
  "max_iterations": 100,
  "parallel_jobs": 4,
  "generate_report": true
}
```

### 2. 运行参数优化

使用以下命令运行参数优化：

```bash
cd /home/<USER>/git/fast_trader_elite/cpp_backtest/build
./bin/parameter_optimizer --config /home/<USER>/git/fast_trader_elite/cpp_backtest/config/parameter_optimization/optimization_config.json
```

### 3. 分析优化结果

优化完成后，结果将保存在`/home/<USER>/git/fast_trader_elite/cpp_backtest/optimization_results`目录中。您可以查看以下文件：

- `optimization_results.csv`：所有参数组合的性能指标
- `best_config.json`：最佳参数组合的配置文件
- `optimization_report.html`：优化结果报告（如果启用了报告生成）

### 4. 使用最佳参数进行回测

使用最佳参数组合进行回测验证：

```bash
cd /home/<USER>/git/fast_trader_elite/cpp_backtest/build
./bin/backtest_engine --config /home/<USER>/git/fast_trader_elite/cpp_backtest/config/backtest_config.json --strategy_config /home/<USER>/git/fast_trader_elite/cpp_backtest/optimization_results/best_config.json
```

## 自定义策略示例

以下是一个自定义策略示例，展示了如何实现自己的交易策略并在回测系统中使用。

### 1. 创建策略类

创建一个新的C++文件`my_strategy.cc`：

```cpp
#include <fast_trader_elite/cpp_backtest/strategy/i_strategy.h>
#include <fast_trader_elite/cpp_backtest/strategy/i_strategy_ctx.h>
#include <fast_trader_elite/cpp_backtest/backtest_logger.h>
#include <nlohmann_json/json.hpp>
#include <memory>
#include <string>

namespace fast_trader_elite {
namespace cpp_backtest {

class my_strategy : public i_strategy {
public:
    my_strategy() = default;
    ~my_strategy() = default;

    // 初始化策略
    bool init(i_strategy_ctx* ctx, const std::string& config_str) override {
        ctx_ = ctx;
        
        // 解析配置
        auto config = nlohmann::json::parse(config_str);
        
        // 获取参数
        instrument_idx_ = config["instrument_idx"].get<uint16_t>();
        threshold_ = config["threshold"].get<double>();
        
        BT_LOG_INFO("Strategy initialized with instrument_idx: {}, threshold: {}", 
                   instrument_idx_, threshold_);
        
        return true;
    }

    // 处理行情深度更新
    void on_depth_market_data(const market_depth* depth) override {
        if (depth->instrument_idx() != instrument_idx_) {
            return;
        }
        
        // 获取当前时间
        int64_t now = depth->local_timestamp();
        
        // 获取买一卖一价格
        double bid_price = depth->bid_price(0);
        double ask_price = depth->ask_price(0);
        
        // 计算价差
        double spread = ask_price - bid_price;
        
        // 获取当前持仓
        double position = ctx_->get_position(instrument_idx_);
        
        // 交易逻辑
        if (spread > threshold_ && position <= 0) {
            // 价差大于阈值，且没有多头持仓，买入
            double qty = 1.0;
            ctx_->insert_order(instrument_idx_, bid_price, qty, direction_type::BUY);
            BT_LOG_INFO("Buy order placed at price: {}, qty: {}, spread: {}", 
                       bid_price, qty, spread);
        } else if (spread < threshold_ && position >= 0) {
            // 价差小于阈值，且没有空头持仓，卖出
            double qty = 1.0;
            ctx_->insert_order(instrument_idx_, ask_price, qty, direction_type::SELL);
            BT_LOG_INFO("Sell order placed at price: {}, qty: {}, spread: {}", 
                       ask_price, qty, spread);
        }
    }

    // 处理成交回报
    void on_trade(const trade_field* trade) override {
        BT_LOG_INFO("Trade executed: order_id: {}, price: {}, volume: {}, direction: {}", 
                   trade->order_id, trade->last_price, trade->volume, 
                   trade->direction == direction_type::BUY ? "BUY" : "SELL");
    }

    // 处理订单回报
    void on_order(const order_field* order) override {
        BT_LOG_INFO("Order status: order_id: {}, status: {}", 
                   order->order_id, static_cast<int>(order->order_status));
    }

private:
    i_strategy_ctx* ctx_ = nullptr;
    uint16_t instrument_idx_ = 0;
    double threshold_ = 0.0;
};

// 导出策略创建函数
extern "C" {
    i_strategy* create_strategy() {
        return new my_strategy();
    }

    void destroy_strategy(i_strategy* strategy) {
        delete strategy;
    }
}

} // namespace cpp_backtest
} // namespace fast_trader_elite
```

### 2. 编译策略库

使用以下命令编译策略库：

```bash
cd /home/<USER>/git/fast_trader_elite/cpp_backtest
mkdir -p build && cd build
cmake .. -DCMAKE_BUILD_TYPE=Release
make my_strategy
```

### 3. 创建策略配置文件

创建策略配置文件`my_strategy_config.json`：

```json
{
  "instrument_idx": 1,
  "threshold": 0.002
}
```

### 4. 更新回测配置文件

更新回测配置文件，使用新的策略库和配置文件：

```json
"strategy": {
  "path": "/home/<USER>/git/fast_trader_elite/cpp_backtest/build/lib/libmy_strategy.so",
  "config_path": "/home/<USER>/git/fast_trader_elite/cpp_backtest/examples/my_strategy_config.json",
  "log_level": "INFO",
  "log_file": "../log/backtest/my_strategy.log"
}
```

### 5. 运行回测

使用以下命令运行回测：

```bash
cd /home/<USER>/git/fast_trader_elite/cpp_backtest/build
./bin/backtest_engine --config /home/<USER>/git/fast_trader_elite/cpp_backtest/config/backtest_config.json
```

## 下一步

- [参数优化](parameter_optimizer/overview.md)：了解如何使用参数优化功能
- [配置文件详解](configuration.md)：了解如何配置回测系统
- [使用指南](usage.md)：学习如何使用回测系统
