#include <iostream>
#include <memory>
#include <cstring>
#include <fast_trader_elite/cpp_backtest/backtest_engine.h>
#include <fast_trader_elite/cpp_backtest/models/fee_model.h>
#include <fast_trader_elite/cpp_backtest/models/queue_model.h>
#include <fast_trader_elite/cpp_backtest/models/latency_model.h>
#include <i_strategy.h>

// 简单策略示例
class simple_strategy : public fast_trader_elite::strategy::i_strategy {
public:
    simple_strategy() = default;
    ~simple_strategy() = default;

    // 策略初始化
    bool on_start(fast_trader_elite::strategy::i_strategy_ctx* ctx, const std::string& strategy_name) override {
        std::cout << "Strategy started" << std::endl;
        ctx_ = ctx;

        // 订阅行情
        fast_trader_elite::md_sub_code_field sub;
        sub.md_id = 1; // 设置行情插件ID
        sub.sub_code.push_back("BTCUSDT"); // 添加订阅代码
        ctx->subscribe(&sub);

        return true;
    }

    // 策略结束
    bool on_stop(fast_trader_elite::strategy::i_strategy_ctx* ctx) override {
        std::cout << "Strategy stopped" << std::endl;
        return true;
    }

    // 行情回调
    void on_depth_data(fast_trader_elite::strategy::i_strategy_ctx* ctx, fast_trader_elite::depth_market_data_field* depth) override {
        std::cout << "Received depth data: " << depth->instrument_id
                  << " bid: " << depth->bid_price[0] << " ask: " << depth->ask_price[0] << std::endl;

        // 简单做市策略：在买一卖一价差上下单
        if (!has_order_) {
            // 买单
            std::cout<<"send order"<<std::endl;
            fast_trader_elite::order_input_field buy_order;
            std::memset(&buy_order, 0, sizeof(buy_order));
            std::strcpy(buy_order.instrument_id, depth->instrument_id);
            buy_order.exchange_id = depth->exchange_id;
            buy_order.direction = fast_trader_elite::direction_type::BUY;
            buy_order.offset = fast_trader_elite::offset_type::OPEN;
            // 不需要设置 hedge_flag
            buy_order.price = depth->bid_price[0];
            buy_order.volume = 0.01;
            buy_order.pricetype = fast_trader_elite::price_type::LIMIT;

            buy_order_id_ = ctx->insert_order(&buy_order);

            // 卖单
            fast_trader_elite::order_input_field sell_order;
            std::memset(&sell_order, 0, sizeof(sell_order));
            std::strcpy(sell_order.instrument_id, depth->instrument_id);
            sell_order.exchange_id = depth->exchange_id;
            sell_order.direction = fast_trader_elite::direction_type::SELL;
            sell_order.offset = fast_trader_elite::offset_type::OPEN;
            // 不需要设置 hedge_flag
            sell_order.price = depth->ask_price[0];
            sell_order.volume = 0.01;
            sell_order.pricetype = fast_trader_elite::price_type::LIMIT;

            sell_order_id_ = ctx->insert_order(&sell_order);
            std::cout<<"send order end"<<std::endl;

            has_order_ = true;
        }
    }
    void on_transaction_data(fast_trader_elite::strategy::i_strategy_ctx* ctx, fast_trader_elite::transaction_field* trans) override {
        std::cout << "Received trans data: " << trans->instrument_id
                  << " price: " << trans->price << " volume: " << trans->volume << std::endl;
    }

    // 订单回调
    void on_order(fast_trader_elite::strategy::i_strategy_ctx* ctx, fast_trader_elite::order_field* order) override {
        std::cout << "Received order: " << order->order_id
                  << " status: " << static_cast<int>(order->order_status)
                  << " price: " << order->limit_price
                  << " volume: " << order->volume
                  << " traded: " << order->volume_traded
                  << std::endl;
    }

    // 成交回调
    void on_trade(fast_trader_elite::strategy::i_strategy_ctx* ctx, fast_trader_elite::trade_field* trade) override {
        std::cout << "Received trade: " << trade->order_id
                  << " price: " << trade->last_price
                  << " volume: " << trade->volume
                  << std::endl;
    }

private:
    fast_trader_elite::strategy::i_strategy_ctx* ctx_ = nullptr;
    int64_t buy_order_id_ = 0;
    int64_t sell_order_id_ = 0;
    bool has_order_ = false;
};

int main(int argc, char** argv) {
    if (argc < 2) {
        std::cerr << "Usage: " << argv[0] << " <data_file>" << std::endl;
        return 1;
    }

    std::string data_file = argv[1];

    // 创建策略
    auto strategy = std::make_unique<simple_strategy>();

    // 创建统一回测引擎 - 直接模式
    auto engine = std::make_unique<fast_trader_elite::cpp_backtest::backtest_engine>(
        data_file, strategy.get());

    // 创建模型
    auto fee_model = std::make_unique<fast_trader_elite::cpp_backtest::fixed_fee_model>(0.0002, 0.0005);
    auto prob = std::make_unique<fast_trader_elite::cpp_backtest::power_prob_queue_func>(3.0);
    auto queue_model = std::make_unique<fast_trader_elite::cpp_backtest::prob_queue_model>(std::move(prob));
    auto latency_model = std::make_unique<fast_trader_elite::cpp_backtest::constant_latency_model>(
        1000000, 2000000, 1000000);

    // 设置模型
    engine->set_fee_model(fee_model.get());
    engine->set_queue_model(queue_model.get());
    engine->set_latency_model(latency_model.get());

    // 设置使用部分成交交易所
    engine->use_partial_fill_exchange(false);

    // 运行回测
    engine->run();

    std::cout << "Backtest completed successfully" << std::endl;

    return 0;
}
