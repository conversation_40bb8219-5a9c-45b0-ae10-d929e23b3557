#include <fast_trader_elite/cpp_backtest/backtest_engine.h>
#include <fast_trader_elite/cpp_backtest/models/fee_model.h>
#include <fast_trader_elite/cpp_backtest/models/latency_model.h>
#include <fast_trader_elite/cpp_backtest/models/queue_model.h>
#include <fast_trader_elite/data_model/field.h>
#include "i_strategy.h"
#include "i_strategy_ctx.h"

#include <chrono>
#include <cmath>
#include <cstring>
#include <deque>
#include <iostream>
#include <memory>
#include <string>
#include <unordered_map>

/**
 * @brief 接口验证策略
 * 这个策略专门用于验证回测框架的接口，不关注策略逻辑本身
 * 它会记录所有接口调用和数据传递，并输出详细的日志
 */
class interface_validation_strategy : public fast_trader_elite::strategy::i_strategy {
public:
    interface_validation_strategy()
        : ctx_(nullptr)
        , instrument_id_("BTCUSDT")
        , order_count_(0)
        , trade_count_(0)
        , cancel_count_(0)
        , depth_count_(0)
        , transaction_count_(0)
        , timer_count_(0)
        , position_(0.0)
        , last_market_timestamp_(0)
        , last_order_time_(0)
        , last_cancel_time_(0) {
        std::cout << "接口验证策略构造函数被调用" << std::endl;
    }

    ~interface_validation_strategy() {
        std::cout << "接口验证策略析构函数被调用" << std::endl;
    }

    // 策略初始化
    bool on_start(fast_trader_elite::strategy::i_strategy_ctx* ctx, const std::string& strategy_name) override {
        std::cout << "\n====== 接口验证策略启动 ======" << std::endl;
        ctx_ = ctx;

        // 订阅行情
        fast_trader_elite::md_sub_code_field sub;
        sub.md_id = 1; // 设置行情插件ID
        sub.sub_code.push_back(instrument_id_); // 添加订阅代码
        ctx->subscribe(&sub);
        std::cout << "接口验证: subscribe() 被调用 - 订阅品种: " << instrument_id_ << std::endl;

        // 注册定时器，每秒触发一次
        ctx->register_timer(1000, [this]() {
            timer_count_++;
            std::cout << "接口验证: 定时器回调被触发 #" << timer_count_ << " - 当前时间戳: " << get_current_timestamp() << std::endl;

            // 每隔3次定时器触发，下一个订单
            if (timer_count_ % 3 == 0) {
                place_test_order();
            }

            // 每隔5次定时器触发，取消所有订单
            if (timer_count_ % 5 == 0) {
                cancel_all_orders();
            }

            // 输出当前状态
            print_current_state();
        }, true);
        std::cout << "接口验证: register_timer() 被调用 - 间隔: 1000ms" << std::endl;

        // 下初始测试订单
        place_test_order();

        return true;
    }

    // 策略停止
    bool on_stop(fast_trader_elite::strategy::i_strategy_ctx* ctx) override {
        std::cout << "\n====== 接口验证策略停止 ======" << std::endl;
        print_statistics();
        return true;
    }

    // 行情回调
    void on_depth_data(fast_trader_elite::strategy::i_strategy_ctx* ctx, fast_trader_elite::depth_market_data_field* depth) override {
        // 只处理我们关注的品种
        if (std::strcmp(depth->instrument_id, instrument_id_.c_str()) != 0) {
            return;
        }

        depth_count_++;
        last_market_timestamp_ = depth->exchange_timestamp;

        // 每10条行情输出一次详细信息
        if (depth_count_ % 10 == 0) {
            std::cout << "\n====== 行情回调 #" << depth_count_ << " ======" << std::endl;
            std::cout << "接口验证: on_depth_data() 被调用" << std::endl;
            std::cout << "时间戳: " << depth->exchange_timestamp << std::endl;
            std::cout << "品种: " << depth->instrument_id << std::endl;
            std::cout << "交易所: " << static_cast<int>(depth->exchange_id) << std::endl;
            std::cout << "买一价/量: " << depth->bid_price[0] << "/" << depth->bid_volume[0] << std::endl;
            std::cout << "卖一价/量: " << depth->ask_price[0] << "/" << depth->ask_volume[0] << std::endl;
            std::cout << "价差: " << (depth->ask_price[0] - depth->bid_price[0]) << std::endl;
            std::cout << "中间价: " << (depth->bid_price[0] + depth->ask_price[0]) / 2.0 << std::endl;

            // 输出完整的深度信息
            std::cout << "完整深度信息:" << std::endl;
            for (int i = 0; i < depth->depth; i++) {
                std::cout << "  Level " << (i+1) << ": Bid " << depth->bid_price[i] << "/" << depth->bid_volume[i]
                          << " Ask " << depth->ask_price[i] << "/" << depth->ask_volume[i] << std::endl;
            }
        }
    }

    // 成交数据回调
    void on_transaction_data(fast_trader_elite::strategy::i_strategy_ctx* ctx, fast_trader_elite::transaction_field* trans) override {
        // 只处理我们关注的品种
        if (std::strcmp(trans->instrument_id, instrument_id_.c_str()) != 0) {
            return;
        }

        transaction_count_++;
        last_market_timestamp_ = trans->exchange_timestamp;

        std::cout << "\n====== 成交数据回调 #" << transaction_count_ << " ======" << std::endl;
        std::cout << "接口验证: on_transaction_data() 被调用" << std::endl;
        std::cout << "时间戳: " << trans->exchange_timestamp << std::endl;
        std::cout << "品种: " << trans->instrument_id << std::endl;
        std::cout << "交易所: " << static_cast<int>(trans->exchange_id) << std::endl;
        std::cout << "价格: " << trans->price << std::endl;
        std::cout << "数量: " << trans->volume << std::endl;
        std::cout << "是否Maker: " << (trans->is_maker ? "是" : "否") << std::endl;
    }

    // 订单回调
    void on_order(fast_trader_elite::strategy::i_strategy_ctx* ctx, fast_trader_elite::order_field* order) override {
        std::cout << "\n====== 订单回调 ======" << std::endl;
        std::cout << "接口验证: on_order() 被调用" << std::endl;
        std::cout << "订单ID: " << order->order_id << std::endl;
        std::cout << "品种: " << order->instrument_id << std::endl;
        std::cout << "交易所: " << static_cast<int>(order->exchange_id) << std::endl;
        std::cout << "方向: " << (order->direction == fast_trader_elite::direction_type::BUY ? "买" : "卖") << std::endl;
        std::cout << "价格: " << order->limit_price << std::endl;
        std::cout << "数量: " << order->volume << std::endl;
        std::cout << "已成交: " << order->volume_traded << std::endl;
        std::cout << "剩余: " << order->volume_left << std::endl;
        std::cout << "状态: " << static_cast<int>(order->order_status) << std::endl;
        std::cout << "时间戳: " << order->timestamp << std::endl;

        // 更新订单状态
        auto order_id = order->order_id;

        // 记录订单信息
        if (active_orders_.find(order_id) == active_orders_.end()) {
            // 新订单
            active_orders_[order_id] = {
                order->direction,
                order->limit_price,
                order->volume,
                order->volume_traded,
                order->order_status
            };

            std::cout << "新订单添加到活跃订单列表" << std::endl;
        } else {
            // 更新现有订单
            active_orders_[order_id].status = order->order_status;
            active_orders_[order_id].traded_volume = order->volume_traded;

            std::cout << "更新现有订单状态" << std::endl;

            // 检查订单是否已完成
            if (order->order_status == fast_trader_elite::order_status_type::FILLED ||
                order->order_status == fast_trader_elite::order_status_type::CANCELLED ||
                order->order_status == fast_trader_elite::order_status_type::ERROR_INSERT) {

                std::cout << "订单已完成，从活跃订单列表中移除" << std::endl;

                // 从活跃订单列表中移除
                active_orders_.erase(order_id);
            }
        }

        std::cout << "当前活跃订单数量: " << active_orders_.size() << std::endl;
    }

    // 成交回调
    void on_trade(fast_trader_elite::strategy::i_strategy_ctx* ctx, fast_trader_elite::trade_field* trade) override {
        trade_count_++;

        std::cout << "\n====== 成交回调 #" << trade_count_ << " ======" << std::endl;
        std::cout << "接口验证: on_trade() 被调用" << std::endl;
        std::cout << "订单ID: " << trade->order_id << std::endl;
        std::cout << "成交ID: " << trade->trade_id << std::endl;
        std::cout << "品种: " << trade->instrument_id << std::endl;
        std::cout << "交易所: " << static_cast<int>(trade->exchange_id) << std::endl;
        std::cout << "方向: " << (trade->direction == fast_trader_elite::direction_type::BUY ? "买" : "卖") << std::endl;
        std::cout << "挂单价格: " << trade->limit_price << std::endl;
        std::cout << "成交价格: " << trade->last_price << std::endl;
        std::cout << "成交数量: " << trade->volume << std::endl;
        std::cout << "手续费: " << trade->fee << std::endl;
        std::cout << "是否Maker: " << (trade->is_maker ? "是" : "否") << std::endl;
        std::cout << "时间戳: " << trade->timestamp << std::endl;

        // 更新持仓
        if (trade->direction == fast_trader_elite::direction_type::BUY) {
            position_ += trade->volume;
        } else {
            position_ -= trade->volume;
        }

        std::cout << "持仓更新: " << position_ << std::endl;
    }

private:
    // 下测试订单
    void place_test_order() {
        if (!ctx_) return;

        std::cout << "\n====== 下测试订单 ======" << std::endl;

        // 获取当前市场价格
        double bid_price = 0.0;
        double ask_price = 0.0;

        // 如果有活跃订单，使用最后一个订单的价格作为参考
        if (!active_orders_.empty()) {
            for (const auto& [order_id, order_info] : active_orders_) {
                if (order_info.direction == fast_trader_elite::direction_type::BUY) {
                    bid_price = order_info.price + 10.0; // 比上一个买单高10
                } else {
                    ask_price = order_info.price - 10.0; // 比上一个卖单低10
                }
            }
        }

        // 如果没有参考价格，使用默认价格
        if (bid_price <= 0.0) bid_price = 50000.0;
        if (ask_price <= 0.0) ask_price = 50010.0;

        // 交替下买单和卖单
        if (order_count_ % 2 == 0) {
            // 买单
            fast_trader_elite::order_input_field buy_order;
            std::memset(&buy_order, 0, sizeof(buy_order));
            std::strcpy(buy_order.instrument_id, instrument_id_.c_str());
            buy_order.exchange_id = fast_trader_elite::exchange_type::BYBIT;
            buy_order.direction = fast_trader_elite::direction_type::BUY;
            buy_order.offset = fast_trader_elite::offset_type::OPEN;
            buy_order.price = bid_price;
            buy_order.volume = 0.01;
            buy_order.pricetype = fast_trader_elite::price_type::LIMIT;

            int64_t order_id = ctx_->insert_order(&buy_order);
            order_count_++;
            last_order_time_ = get_current_timestamp();

            std::cout << "接口验证: insert_order() 被调用 - 买单" << std::endl;
            std::cout << "订单ID: " << order_id << std::endl;
            std::cout << "品种: " << buy_order.instrument_id << std::endl;
            std::cout << "交易所: " << static_cast<int>(buy_order.exchange_id) << std::endl;
            std::cout << "方向: 买" << std::endl;
            std::cout << "价格: " << buy_order.price << std::endl;
            std::cout << "数量: " << buy_order.volume << std::endl;
        } else {
            // 卖单
            fast_trader_elite::order_input_field sell_order;
            std::memset(&sell_order, 0, sizeof(sell_order));
            std::strcpy(sell_order.instrument_id, instrument_id_.c_str());
            sell_order.exchange_id = fast_trader_elite::exchange_type::BYBIT;
            sell_order.direction = fast_trader_elite::direction_type::SELL;
            sell_order.offset = fast_trader_elite::offset_type::OPEN;
            sell_order.price = ask_price;
            sell_order.volume = 0.01;
            sell_order.pricetype = fast_trader_elite::price_type::LIMIT;

            int64_t order_id = ctx_->insert_order(&sell_order);
            order_count_++;
            last_order_time_ = get_current_timestamp();

            std::cout << "接口验证: insert_order() 被调用 - 卖单" << std::endl;
            std::cout << "订单ID: " << order_id << std::endl;
            std::cout << "品种: " << sell_order.instrument_id << std::endl;
            std::cout << "交易所: " << static_cast<int>(sell_order.exchange_id) << std::endl;
            std::cout << "方向: 卖" << std::endl;
            std::cout << "价格: " << sell_order.price << std::endl;
            std::cout << "数量: " << sell_order.volume << std::endl;
        }
    }

    // 取消所有订单
    void cancel_all_orders() {
        if (!ctx_ || active_orders_.empty()) return;

        std::cout << "\n====== 取消所有订单 ======" << std::endl;
        std::cout << "接口验证: cancel_order() 被调用" << std::endl;
        std::cout << "活跃订单数量: " << active_orders_.size() << std::endl;

        for (auto& [order_id, order_info] : active_orders_) {
            fast_trader_elite::order_action_field action;
            std::memset(&action, 0, sizeof(action));
            std::strcpy(action.instrument_id, instrument_id_.c_str());
            action.exchange_id = fast_trader_elite::exchange_type::BYBIT;
            action.order_id = order_id;

            ctx_->cancel_order(&action);
            cancel_count_++;

            std::cout << "取消订单 - 订单ID: " << order_id
                      << " 方向: " << (order_info.direction == fast_trader_elite::direction_type::BUY ? "买" : "卖")
                      << " 价格: " << order_info.price
                      << " 剩余数量: " << (order_info.volume - order_info.traded_volume)
                      << std::endl;
        }

        last_cancel_time_ = get_current_timestamp();
    }

    // 获取当前时间戳
    int64_t get_current_timestamp() const {
        return last_market_timestamp_;
    }

    // 打印当前状态
    void print_current_state() {
        std::cout << "\n====== 当前状态 ======" << std::endl;
        std::cout << "当前时间戳: " << get_current_timestamp() << std::endl;
        std::cout << "持仓: " << position_ << std::endl;
        std::cout << "活跃订单数量: " << active_orders_.size() << std::endl;
        std::cout << "订单总数: " << order_count_ << std::endl;
        std::cout << "成交总数: " << trade_count_ << std::endl;
        std::cout << "取消总数: " << cancel_count_ << std::endl;
        std::cout << "行情回调总数: " << depth_count_ << std::endl;
        std::cout << "成交数据回调总数: " << transaction_count_ << std::endl;
        std::cout << "定时器回调总数: " << timer_count_ << std::endl;

        // 输出活跃订单详情
        if (!active_orders_.empty()) {
            std::cout << "\n活跃订单详情:" << std::endl;
            for (const auto& [order_id, order_info] : active_orders_) {
                std::cout << "  订单ID: " << order_id
                          << " 方向: " << (order_info.direction == fast_trader_elite::direction_type::BUY ? "买" : "卖")
                          << " 价格: " << order_info.price
                          << " 总量: " << order_info.volume
                          << " 已成交: " << order_info.traded_volume
                          << " 剩余: " << (order_info.volume - order_info.traded_volume)
                          << " 状态: " << static_cast<int>(order_info.status)
                          << std::endl;
            }
        }
    }

    // 打印统计信息
    void print_statistics() {
        std::cout << "\n====== 策略统计信息 ======" << std::endl;
        std::cout << "交易品种: " << instrument_id_ << std::endl;
        std::cout << "订单总数: " << order_count_ << std::endl;
        std::cout << "成交总数: " << trade_count_ << std::endl;
        std::cout << "取消总数: " << cancel_count_ << std::endl;
        std::cout << "行情回调总数: " << depth_count_ << std::endl;
        std::cout << "成交数据回调总数: " << transaction_count_ << std::endl;
        std::cout << "定时器回调总数: " << timer_count_ << std::endl;
        std::cout << "最终持仓: " << position_ << std::endl;
    }

private:
    // 订单信息结构
    struct OrderInfo {
        fast_trader_elite::direction_type direction;
        double price;
        double volume;
        double traded_volume;
        fast_trader_elite::order_status_type status;
    };

    fast_trader_elite::strategy::i_strategy_ctx* ctx_;
    std::string instrument_id_;

    // 统计数据
    int order_count_;
    int trade_count_;
    int cancel_count_;
    int depth_count_;
    int transaction_count_;
    int timer_count_;
    double position_;

    // 时间戳
    int64_t last_market_timestamp_;
    int64_t last_order_time_;
    int64_t last_cancel_time_;

    // 活跃订单
    std::unordered_map<int64_t, OrderInfo> active_orders_;
};

int main(int argc, char** argv) {
    if (argc < 2) {
        std::cerr << "Usage: " << argv[0] << " <data_file>" << std::endl;
        return 1;
    }

    std::string data_file = argv[1];
    std::cout << "\n====== 接口验证程序启动 ======" << std::endl;
    std::cout << "数据文件: " << data_file << std::endl;

    // 创建策略
    std::cout << "创建接口验证策略..." << std::endl;
    auto strategy = std::make_unique<interface_validation_strategy>();

    // 创建回测引擎
    std::cout << "创建回测引擎..." << std::endl;
    auto engine = std::make_unique<fast_trader_elite::cpp_backtest::backtest_engine>(
        data_file, strategy.get());

    // 手续费模型：maker -0.0001 (返佣), taker 0.0004
    std::cout << "配置手续费模型: fixed_fee_model" << std::endl;
    std::cout << "  Maker费率: -0.0001 (返佣)" << std::endl;
    std::cout << "  Taker费率: 0.0004" << std::endl;
    auto fee_model = std::make_unique<fast_trader_elite::cpp_backtest::fixed_fee_model>(-0.0001, 0.0004);

    // 使用固定概率队列模型，成交概率为1.0（完全成交）
    std::cout << "配置队列模型: fixed_prob_queue_model" << std::endl;
    std::cout << "  成交概率: 1.0 (完全成交)" << std::endl;
    auto queue_model = std::make_unique<fast_trader_elite::cpp_backtest::fixed_prob_queue_model>(1.0);

    // 延迟模型：下单延迟1ms，撤单延迟2ms，行情延迟1ms
    std::cout << "配置延迟模型: constant_latency_model" << std::endl;
    std::cout << "  下单延迟: 1ms" << std::endl;
    std::cout << "  撤单延迟: 2ms" << std::endl;
    std::cout << "  行情延迟: 1ms" << std::endl;
    auto latency_model = std::make_unique<fast_trader_elite::cpp_backtest::constant_latency_model>(
        1000000, 2000000, 1000000);

    // 设置模型
    std::cout << "设置回测引擎模型..." << std::endl;
    engine->set_fee_model(fee_model.get());
    engine->set_queue_model(queue_model.get());
    engine->set_latency_model(latency_model.get());

    // 设置使用部分成交交易所
    std::cout << "设置使用部分成交交易所: true" << std::endl;
    engine->use_partial_fill_exchange(true);

    // 记录开始时间
    std::cout << "\n====== 开始回测 ======" << std::endl;
    auto start_time = std::chrono::high_resolution_clock::now();

    // 运行回测
    engine->run();

    // 记录结束时间并计算耗时
    auto end_time = std::chrono::high_resolution_clock::now();
    auto duration = std::chrono::duration_cast<std::chrono::milliseconds>(end_time - start_time).count();

    std::cout << "\n====== 回测完成 ======" << std::endl;
    std::cout << "耗时: " << duration << "ms" << std::endl;

    return 0;
}
