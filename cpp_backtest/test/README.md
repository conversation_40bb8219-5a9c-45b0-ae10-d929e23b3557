# cpp_backtest 模块测试

本目录包含 cpp_backtest 模块的单元测试，使用 Google Test 框架实现。

## 测试结构

测试按照模块的结构组织，并已统一到一个可执行文件中：

- `models/`: 模型相关测试
  - `fee_model_test.cc`: 费用模型测试
  - `latency_model_test.cc`: 延迟模型测试
  - `queue_model_test.cc`: 队列模型测试
  - `probability_test.cc`: 概率函数测试
  - `model_factory_test.cc`: 模型工厂测试
  - `mock_types.h`: 测试用的模拟类型实现
- `proc/`: 处理器相关测试
  - `simple_no_partial_fill_exchange_test.cc`: 全部成交交易所测试
  - `mock_objects.h`: 测试用的模拟对象

## 运行测试

要运行测试，请按照以下步骤操作：

1. 在项目根目录下创建构建目录（如果尚未创建）：
   ```bash
   mkdir -p build && cd build
   ```

2. 配置 CMake：
   ```bash
   cmake ..
   ```

3. 构建测试：
   ```bash
   make cpp_backtest_tests
   ```

4. 运行测试：
   ```bash
   ./bin/cpp_backtest_tests
   ```

5. 运行特定测试（使用过滤器）：
   ```bash
   ./bin/cpp_backtest_tests --gtest_filter="SimpleNoPartialFillExchange*"
   ```

或者使用提供的脚本运行测试：

```bash
./run_tests.sh
```

## 测试场景说明

### 费用模型测试

- 测试固定费率模型的计算
- 测试做市商和吃单方费率
- 测试零费率和负费率（返佣）
- 测试边界条件

### 延迟模型测试

- 测试固定延迟模型的各种延迟计算
- 测试不同订单类型和方向的延迟
- 测试零延迟和高延迟情况

### 队列模型测试

- 测试保守队列位置模型
- 测试概率队列模型
- 测试队列位置的初始化、更新和成交处理
- 测试订单成交条件判断

### 概率函数测试

- 测试各种概率函数的计算
- 测试边界条件和特殊情况

### 交易所处理器测试

- 测试全部成交交易所的初始化和基本功能
- 测试不同类型订单的处理逻辑
- 测试市场深度变化触发订单成交的逻辑
