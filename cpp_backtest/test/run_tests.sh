#!/bin/bash

# 设置工作目录
SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
ROOT_DIR="$(cd "${SCRIPT_DIR}/../.." && pwd)"
BUILD_DIR="${ROOT_DIR}/build"
TEST_BUILD_DIR="${BUILD_DIR}/cpp_backtest/test"
BIN_DIR="${BUILD_DIR}/bin"

# 创建构建目录
mkdir -p "${BUILD_DIR}"
cd "${ROOT_DIR}"

# 编译测试
echo "正在编译测试..."
cmake -B "${BUILD_DIR}" .
cmake --build "${BUILD_DIR}" --target cpp_backtest_tests

# 运行测试
echo "正在运行所有测试..."
"${BIN_DIR}/cpp_backtest_tests"

# 如果需要运行特定测试，可以使用以下命令（取消注释并修改过滤器）
# "${BIN_DIR}/cpp_backtest_tests" --gtest_filter="SimpleNoPartialFillExchange*"
# "${BIN_DIR}/cpp_backtest_tests" --gtest_filter="FixedFeeModel*"

echo "测试完成！"
