cmake_minimum_required(VERSION 3.10)

# 设置测试项目名称
project(cpp_backtest_tests)

# 查找GTest包
find_package(GTest REQUIRED)
include_directories(${GTEST_INCLUDE_DIRS})

# 添加统一测试可执行文件
add_executable(cpp_backtest_tests
    test_main.cc
    # 模型测试文件
    models/fee_model_test.cc
    models/latency_model_test.cc
    models/queue_model_test.cc
    models/probability_test.cc
    models/model_factory_test.cc
    # 处理器测试文件
    proc/simple_no_partial_fill_exchange_test.cc
    # 数据预处理器测试文件
    data_preprocessor/data_preprocessor_test.cc
    # 仓位周期测试文件
    position_cycle_test.cc
    # 暂时注释掉的测试文件
    # proc/partial_fill_exchange_test.cc
    # proc/local_processor_test.cc
)

# 链接测试库
target_link_libraries(cpp_backtest_tests
    PRIVATE
    fast_trader_elite_backtest
    ${GTEST_BOTH_LIBRARIES}
    pthread
)

# 添加测试
add_test(NAME cpp_backtest_tests COMMAND cpp_backtest_tests)

# 设置包含目录
target_include_directories(cpp_backtest_tests
    PRIVATE
    ${CMAKE_CURRENT_SOURCE_DIR}
    ${CMAKE_SOURCE_DIR}/include
)
