#pragma once

#include <fast_trader_elite/cpp_backtest/market/market_depth.h>
#include <fast_trader_elite/cpp_backtest/models/fee_model.h>
#include <fast_trader_elite/cpp_backtest/models/queue_model.h>
#include <fast_trader_elite/cpp_backtest/models/latency_model.h>
#include <fast_trader_elite/cpp_backtest/types.h>
#include <unordered_map>
#include <vector>
#include <limits>
#include <memory>

namespace fast_trader_elite {
namespace cpp_backtest {
namespace test {

/**
 * @brief 模拟市场深度类
 * 用于测试交易所处理器
 */
class MockMarketDepth : public market_depth {
public:
    MockMarketDepth(double tick_size = 0.01, double lot_size = 0.001)
        : tick_size_(tick_size)
        , lot_size_(lot_size)
        , best_bid_tick_(std::numeric_limits<int64_t>::min())
        , best_ask_tick_(std::numeric_limits<int64_t>::max()) {
    }

    int64_t best_bid_tick() const override { return best_bid_tick_; }
    int64_t best_ask_tick() const override { return best_ask_tick_; }
    double bid_qty(int64_t price_tick) const override {
        auto it = bid_depth_.find(price_tick);
        return it != bid_depth_.end() ? it->second : 0.0;
    }
    double ask_qty(int64_t price_tick) const override {
        auto it = ask_depth_.find(price_tick);
        return it != ask_depth_.end() ? it->second : 0.0;
    }
    const std::vector<int64_t>& bid_prices() const override {
        bid_prices_.clear();
        for (const auto& [price, qty] : bid_depth_) {
            bid_prices_.push_back(price);
        }
        return bid_prices_;
    }
    const std::vector<int64_t>& ask_prices() const override {
        ask_prices_.clear();
        for (const auto& [price, qty] : ask_depth_) {
            ask_prices_.push_back(price);
        }
        return ask_prices_;
    }
    const std::vector<double>& bid_qtys() const override {
        bid_qtys_.clear();
        for (const auto& [price, qty] : bid_depth_) {
            bid_qtys_.push_back(qty);
        }
        return bid_qtys_;
    }
    const std::vector<double>& ask_qtys() const override {
        ask_qtys_.clear();
        for (const auto& [price, qty] : ask_depth_) {
            ask_qtys_.push_back(qty);
        }
        return ask_qtys_;
    }
    double tick_size() const override { return tick_size_; }
    double lot_size() const override { return lot_size_; }
    void set_tick_size(double tick_size) override { tick_size_ = tick_size; }
    void set_lot_size(double lot_size) override { lot_size_ = lot_size; }
    void clear() override {
        bid_depth_.clear();
        ask_depth_.clear();
        best_bid_tick_ = std::numeric_limits<int64_t>::min();
        best_ask_tick_ = std::numeric_limits<int64_t>::max();
    }
    void clear_depth(side_type side, double clear_upto_price) override {
        int64_t price_tick = price_to_tick(clear_upto_price);
        if (side == side_type::buy) {
            auto it = bid_depth_.begin();
            while (it != bid_depth_.end()) {
                if (it->first <= price_tick) {
                    it = bid_depth_.erase(it);
                } else {
                    ++it;
                }
            }
            update_best_bid();
        } else {
            auto it = ask_depth_.begin();
            while (it != ask_depth_.end()) {
                if (it->first >= price_tick) {
                    it = ask_depth_.erase(it);
                } else {
                    ++it;
                }
            }
            update_best_ask();
        }
    }

    std::tuple<int64_t, int64_t, int64_t, double, double, int64_t>
    update_bid_depth(double price, double qty, int64_t timestamp) override {
        int64_t price_tick = price_to_tick(price);
        int64_t prev_best_bid_tick = best_bid_tick_;
        double prev_qty = bid_qty(price_tick);

        if (qty <= 0.0) {
            bid_depth_.erase(price_tick);
        } else {
            bid_depth_[price_tick] = qty;
        }

        update_best_bid();

        return std::make_tuple(price_tick, prev_best_bid_tick, best_bid_tick_, prev_qty, qty, timestamp);
    }

    std::tuple<int64_t, int64_t, int64_t, double, double, int64_t>
    update_ask_depth(double price, double qty, int64_t timestamp) override {
        int64_t price_tick = price_to_tick(price);
        int64_t prev_best_ask_tick = best_ask_tick_;
        double prev_qty = ask_qty(price_tick);

        if (qty <= 0.0) {
            ask_depth_.erase(price_tick);
        } else {
            ask_depth_[price_tick] = qty;
        }

        update_best_ask();

        return std::make_tuple(price_tick, prev_best_ask_tick, best_ask_tick_, prev_qty, qty, timestamp);
    }

    int64_t price_to_tick(double price) const override {
        return static_cast<int64_t>(price / tick_size_ + 0.5);
    }

    double tick_to_price(int64_t tick) const override {
        return tick * tick_size_;
    }

    // 设置最佳买价（用于测试）
    void set_best_bid_tick(int64_t tick) {
        best_bid_tick_ = tick;
    }

    // 设置最佳卖价（用于测试）
    void set_best_ask_tick(int64_t tick) {
        best_ask_tick_ = tick;
    }

private:
    void update_best_bid() {
        best_bid_tick_ = std::numeric_limits<int64_t>::min();
        for (const auto& [price, qty] : bid_depth_) {
            if (qty > 0.0 && price > best_bid_tick_) {
                best_bid_tick_ = price;
            }
        }
    }

    void update_best_ask() {
        best_ask_tick_ = std::numeric_limits<int64_t>::max();
        for (const auto& [price, qty] : ask_depth_) {
            if (qty > 0.0 && price < best_ask_tick_) {
                best_ask_tick_ = price;
            }
        }
    }

    double tick_size_;
    double lot_size_;
    int64_t best_bid_tick_;
    int64_t best_ask_tick_;
    std::unordered_map<int64_t, double> bid_depth_;
    std::unordered_map<int64_t, double> ask_depth_;
    mutable std::vector<int64_t> bid_prices_;
    mutable std::vector<int64_t> ask_prices_;
    mutable std::vector<double> bid_qtys_;
    mutable std::vector<double> ask_qtys_;
};

/**
 * @brief 模拟费用模型类
 * 用于测试交易所处理器
 */
class MockFeeModel : public fee_model {
public:
    MockFeeModel(double maker_fee_rate = 0.001, double taker_fee_rate = 0.002)
        : maker_fee_rate_(maker_fee_rate)
        , taker_fee_rate_(taker_fee_rate) {
    }

    double calculate_fee(double price, double quantity, bool is_maker) const override {
        return price * quantity * (is_maker ? maker_fee_rate_ : taker_fee_rate_);
    }

private:
    double maker_fee_rate_;
    double taker_fee_rate_;
};

/**
 * @brief 模拟队列模型类
 * 用于测试交易所处理器
 */
class MockQueueModel : public queue_model {
public:
    MockQueueModel(double fill_ratio = 1.0)
        : fill_ratio_(fill_ratio) {
    }

    void new_order(internal_order& order, const market_depth* depth) override {
        // 简单实现，不做任何操作
    }

    void update_depth(internal_order& order, double prev_qty, double new_qty, const market_depth* depth) override {
        // 简单实现，不做任何操作
    }

    void trade(internal_order& order, double qty, const market_depth* depth) override {
        // 简单实现，不做任何操作
    }

    double is_filled(const internal_order& order, const market_depth* depth) const override {
        // 返回可成交数量，根据fill_ratio_计算
        return (order.quantity - order.executed_quantity) * fill_ratio_;
    }

    // 设置成交比例（用于测试）
    void set_fill_ratio(double ratio) {
        fill_ratio_ = ratio;
    }

private:
    double fill_ratio_;
};

/**
 * @brief 模拟延迟模型类
 * 用于测试处理器
 */
class MockLatencyModel : public latency_model {
public:
    MockLatencyModel(int64_t entry_latency = 100, int64_t response_latency = 200, int64_t market_latency = 50)
        : entry_latency_(entry_latency)
        , response_latency_(response_latency)
        , market_latency_(market_latency) {
    }

    int64_t entry(int64_t timestamp, const internal_order& order) const override {
        return entry_latency_;
    }

    int64_t response(int64_t timestamp, const internal_order& order) const override {
        return response_latency_;
    }

    int64_t market(int64_t timestamp) const override {
        return market_latency_;
    }

private:
    int64_t entry_latency_;
    int64_t response_latency_;
    int64_t market_latency_;
};

} // namespace test
} // namespace cpp_backtest
} // namespace fast_trader_elite
