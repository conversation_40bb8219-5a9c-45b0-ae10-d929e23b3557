#include <gtest/gtest.h>
#include <fast_trader_elite/cpp_backtest/proc/no_partial_fill_exchange.h>
#include <fast_trader_elite/cpp_backtest/types.h>
#include "mock_objects.h"
#include <memory>
#include <limits>
#include <cstring>

using namespace fast_trader_elite::cpp_backtest;
using namespace fast_trader_elite::cpp_backtest::test;
using fast_trader_elite::transaction_field;
using fast_trader_elite::depth_market_data_field;
using fast_trader_elite::kline_market_data_field;

/**
 * @brief 测试用交易所处理器
 * 继承自 no_partial_fill_exchange，暴露 protected 方法供测试使用
 */
class TestNoPartialFillExchange : public no_partial_fill_exchange {
public:
    TestNoPartialFillExchange(market_depth* depth, fee_model* fee_model, queue_model* queue_model, latency_model* order_latency)
        : no_partial_fill_exchange(depth, fee_model, queue_model, order_latency) {
    }

    // 暴露 protected 方法
    using no_partial_fill_exchange::process_order;
    using no_partial_fill_exchange::process_transaction;
    using no_partial_fill_exchange::process_depth;
    using no_partial_fill_exchange::process_kline;
    using no_partial_fill_exchange::try_match_orders;
    using no_partial_fill_exchange::state_;
    using no_partial_fill_exchange::set_order_buses;

    // 延迟模型现在在构造函数中设置

    // 获取内部订单列表
    const std::unordered_map<int64_t, internal_order>& orders() const {
        return orders_;
    }
};

/**
 * @brief 全部成交交易所简单测试类
 */
class SimpleNoPartialFillExchangeTest : public ::testing::Test {
protected:
    void SetUp() override {
        // 创建模拟对象
        depth_ = std::make_unique<MockMarketDepth>(0.01, 1.0);
        fee_model_ = std::make_unique<MockFeeModel>(0.001, 0.002);
        queue_model_ = std::make_unique<MockQueueModel>(1.0);
        latency_model_ = std::make_unique<MockLatencyModel>(100, 200, 50);

        // 创建测试对象
        exchange_ = std::make_unique<TestNoPartialFillExchange>(
            depth_.get(), fee_model_.get(), queue_model_.get(), latency_model_.get());

        // 设置初始市场深度
        depth_->update_bid_depth(99.0, 10.0, 1000000);
        depth_->update_ask_depth(101.0, 20.0, 1000000);
    }

    void TearDown() override {
        exchange_.reset();
        latency_model_.reset();
        queue_model_.reset();
        fee_model_.reset();
        depth_.reset();
    }

    // 创建测试订单
    internal_order create_test_order(int64_t order_id, double price, double quantity,
                                    side_type side, order_type type = order_type::limit,
                                    time_in_force_type tif = time_in_force_type::gtc) {
        internal_order order;
        order.order_id = order_id;
        order.price = price;
        order.quantity = quantity;
        order.leaves_quantity = quantity;
        order.executed_quantity = 0.0;
        order.side = side;
        order.type = type;
        order.time_in_force = tif;
        order.status = order_status::new_order;
        order.request = order_status::new_order;
        return order;
    }

    std::unique_ptr<MockMarketDepth> depth_;
    std::unique_ptr<MockFeeModel> fee_model_;
    std::unique_ptr<MockQueueModel> queue_model_;
    std::unique_ptr<MockLatencyModel> latency_model_;
    std::unique_ptr<TestNoPartialFillExchange> exchange_;
};

/**
 * @brief 测试场景1：基本初始化测试
 */
TEST_F(SimpleNoPartialFillExchangeTest, BasicInitialization) {
    // 验证交易所已正确初始化
    EXPECT_EQ(9900, depth_->best_bid_tick());  // 99.0 / 0.01 = 9900
    EXPECT_EQ(10100, depth_->best_ask_tick()); // 101.0 / 0.01 = 10100
    EXPECT_EQ(10.0, depth_->bid_qty(9900));
    EXPECT_EQ(20.0, depth_->ask_qty(10100));
}

/**
 * @brief 测试场景2：测试处理交易所成交
 */
TEST_F(SimpleNoPartialFillExchangeTest, ProcessTransaction) {
    // 创建交易所成交
    fast_trader_elite::transaction_field trans;
    trans.price = 100.0;
    trans.volume = 5.0;
    trans.is_maker = false;

    // 设置当前时间戳
    exchange_->set_current_timestamp(1000000);

    // 处理成交
    exchange_->process_transaction(&trans);

    // 验证当前时间戳未变
    EXPECT_EQ(1000000, exchange_->current_timestamp());
}

/**
 * @brief 测试场景3：测试处理深度行情
 */
TEST_F(SimpleNoPartialFillExchangeTest, ProcessDepth) {
    // 创建深度行情
    fast_trader_elite::depth_market_data_field depth_data;
    memset(&depth_data, 0, sizeof(fast_trader_elite::depth_market_data_field));
    depth_data.bid_price[0] = 99.0;
    depth_data.bid_volume[0] = 10.0;
    depth_data.ask_price[0] = 101.0;
    depth_data.ask_volume[0] = 20.0;
    depth_data.depth = 1;
    depth_data.exchange_timestamp = 2000000;
    depth_data.local_timestamp = 2000000;

    // 设置当前时间戳
    exchange_->set_current_timestamp(1000000);

    // 处理深度行情
    exchange_->process_depth(&depth_data);

    // 验证深度数据已更新
    EXPECT_EQ(9900, depth_->best_bid_tick());  // 99.0 / 0.01 = 9900
    EXPECT_EQ(10100, depth_->best_ask_tick()); // 101.0 / 0.01 = 10100
    EXPECT_EQ(10.0, depth_->bid_qty(9900));
    EXPECT_EQ(20.0, depth_->ask_qty(10100));
}

/**
 * @brief 测试场景4：测试尝试操作
 */
TEST_F(SimpleNoPartialFillExchangeTest, TryMatchOrders) {
    // 设置当前时间戳
    exchange_->set_current_timestamp(1000000);

    // 调用尝试操作
    exchange_->try_match_orders();

    // 验证当前时间戳未变
    EXPECT_EQ(1000000, exchange_->current_timestamp());
}

/**
 * @brief 测试场景5：测试处理K线数据
 */
TEST_F(SimpleNoPartialFillExchangeTest, ProcessKline) {
    // 创建K线数据
    fast_trader_elite::kline_market_data_field kline_data;
    memset(&kline_data, 0, sizeof(fast_trader_elite::kline_market_data_field));
    kline_data.open = 100.0;
    kline_data.high = 105.0;
    kline_data.low = 95.0;
    kline_data.close = 102.0;
    kline_data.volume = 1000.0;
    kline_data.exchange_timestamp = 2000000;
    kline_data.local_timestamp = 2000000;

    // 设置当前时间戳
    exchange_->set_current_timestamp(1000000);

    // 处理K线数据
    exchange_->process_kline(&kline_data);

    // 验证当前时间戳未变
    EXPECT_EQ(1000000, exchange_->current_timestamp());
}

/**
 * @brief 测试场景6：测试创建内部订单
 */
TEST_F(SimpleNoPartialFillExchangeTest, CreateInternalOrder) {
    // 设置当前时间戳
    exchange_->set_current_timestamp(1000000);

    // 验证初始状态
    EXPECT_EQ(0, exchange_->orders().size());
}

/**
 * @brief 测试场景7：测试订单总线
 */
TEST_F(SimpleNoPartialFillExchangeTest, OrderBus) {
    // 设置当前时间戳
    exchange_->set_current_timestamp(1000000);

    // 验证订单总线初始状态
    order_bus orders_to, orders_from;
    exchange_->set_order_buses(&orders_to, &orders_from);

    EXPECT_EQ(0, orders_to.size());
    EXPECT_EQ(0, orders_from.size());
}

/**
 * @brief 测试场景8：测试处理新订单 - 限价单
 * 测试不同价格的限价单处理逻辑
 */
TEST_F(SimpleNoPartialFillExchangeTest, ProcessNewLimitOrder) {
    // 设置当前时间戳
    exchange_->set_current_timestamp(1000000);

    // 创建并设置订单总线
    std::unique_ptr<order_bus> orders_to = std::make_unique<order_bus>();
    std::unique_ptr<order_bus> orders_from = std::make_unique<order_bus>();
    exchange_->set_order_buses(orders_to.get(), orders_from.get());

    // 测试场景1：买单价格高于卖一价（应该立即成交）
    {
        // 创建买单，价格为102.0，高于卖一价101.0
        internal_order buy_order = create_test_order(1, 102.0, 5.0, side_type::buy);

        // 处理订单
        backtest_error result = exchange_->process_order(buy_order);

        // 验证处理成功
        EXPECT_EQ(backtest_error::none, result);

        // 验证订单已成交（不在订单列表中）
        EXPECT_EQ(0, exchange_->orders().count(1));

        // 验证订单回报已发送
        EXPECT_EQ(1, orders_to->size());
        auto order_result = orders_to->pop_front();
        ASSERT_TRUE(order_result.has_value());
        auto report = order_result.value().first;
        EXPECT_EQ(1, report.order_id);
        EXPECT_EQ(order_status::filled, report.status);
        EXPECT_EQ(5.0, report.executed_quantity);
        EXPECT_EQ(0.0, report.leaves_quantity);
        EXPECT_EQ(101.0, report.executed_price); // 成交价应该是卖一价

        // 清空订单总线，准备下一个测试
        orders_to->reset();
    }

    // 测试场景2：买单价格等于卖一价（应该立即成交）
    {
        // 创建买单，价格为101.0，等于卖一价
        internal_order buy_order = create_test_order(2, 101.0, 5.0, side_type::buy);

        // 处理订单
        backtest_error result = exchange_->process_order(buy_order);

        // 验证处理成功
        EXPECT_EQ(backtest_error::none, result);

        // 验证订单已成交（不在订单列表中）
        EXPECT_EQ(0, exchange_->orders().count(2));

        // 验证订单回报已发送
        EXPECT_EQ(1, orders_to->size());
        auto order_result = orders_to->pop_front();
        ASSERT_TRUE(order_result.has_value());
        auto report = order_result.value().first;
        EXPECT_EQ(2, report.order_id);
        EXPECT_EQ(order_status::filled, report.status);

        // 清空订单总线，准备下一个测试
        orders_to->reset();
    }

    // 测试场景3：买单价格低于卖一价（应该挂单）
    {
        // 创建买单，价格为100.0，低于卖一价101.0
        internal_order buy_order = create_test_order(3, 100.0, 5.0, side_type::buy);

        // 处理订单
        backtest_error result = exchange_->process_order(buy_order);

        // 验证处理成功
        EXPECT_EQ(backtest_error::none, result);

        // 验证订单已添加到订单列表中
        EXPECT_EQ(1, exchange_->orders().count(3));

        // 验证订单回报已发送
        EXPECT_EQ(1, orders_to->size());
        auto order_result = orders_to->pop_front();
        ASSERT_TRUE(order_result.has_value());
        auto report = order_result.value().first;
        EXPECT_EQ(3, report.order_id);
        EXPECT_EQ(order_status::new_order, report.status);

        // 清空订单总线，准备下一个测试
        orders_to->reset();
    }

    // 测试场景4：卖单价格低于买一价（应该立即成交）
    {
        // 创建卖单，价格为98.0，低于买一价99.0
        internal_order sell_order = create_test_order(4, 98.0, 5.0, side_type::sell);

        // 处理订单
        backtest_error result = exchange_->process_order(sell_order);

        // 验证处理成功
        EXPECT_EQ(backtest_error::none, result);

        // 验证订单已成交（不在订单列表中）
        EXPECT_EQ(0, exchange_->orders().count(4));

        // 验证订单回报已发送
        EXPECT_EQ(1, orders_to->size());
        auto order_result = orders_to->pop_front();
        ASSERT_TRUE(order_result.has_value());
        auto report = order_result.value().first;
        EXPECT_EQ(4, report.order_id);
        EXPECT_EQ(order_status::filled, report.status);
        EXPECT_EQ(5.0, report.executed_quantity);
        EXPECT_EQ(0.0, report.leaves_quantity);
        EXPECT_EQ(99.0, report.executed_price); // 成交价应该是买一价

        // 清空订单总线，准备下一个测试
        orders_to->reset();
    }

    // 测试场景5：卖单价格等于买一价（应该立即成交）
    {
        // 创建卖单，价格为99.0，等于买一价
        internal_order sell_order = create_test_order(5, 99.0, 5.0, side_type::sell);

        // 处理订单
        backtest_error result = exchange_->process_order(sell_order);

        // 验证处理成功
        EXPECT_EQ(backtest_error::none, result);

        // 验证订单已成交（不在订单列表中）
        EXPECT_EQ(0, exchange_->orders().count(5));

        // 验证订单回报已发送
        EXPECT_EQ(1, orders_to->size());
        auto order_result = orders_to->pop_front();
        ASSERT_TRUE(order_result.has_value());
        auto report = order_result.value().first;
        EXPECT_EQ(5, report.order_id);
        EXPECT_EQ(order_status::filled, report.status);

        // 清空订单总线，准备下一个测试
        orders_to->reset();
    }

    // 测试场景6：卖单价格高于买一价（应该挂单）
    {
        // 创建卖单，价格为100.0，高于买一价99.0
        internal_order sell_order = create_test_order(6, 100.0, 5.0, side_type::sell);

        // 处理订单
        backtest_error result = exchange_->process_order(sell_order);

        // 验证处理成功
        EXPECT_EQ(backtest_error::none, result);

        // 验证订单已添加到订单列表中
        EXPECT_EQ(1, exchange_->orders().count(6));

        // 验证订单回报已发送
        EXPECT_EQ(1, orders_to->size());
        auto order_result = orders_to->pop_front();
        ASSERT_TRUE(order_result.has_value());
        auto report = order_result.value().first;
        EXPECT_EQ(6, report.order_id);
        EXPECT_EQ(order_status::new_order, report.status);

        // 清空订单总线，准备下一个测试
        orders_to->reset();
    }
}

/**
 * @brief 测试场景9：测试处理新订单 - 市价单
 * 测试市价单的处理逻辑
 */
TEST_F(SimpleNoPartialFillExchangeTest, ProcessNewMarketOrder) {
    // 设置当前时间戳
    exchange_->set_current_timestamp(1000000);

    // 创建并设置订单总线
    std::unique_ptr<order_bus> orders_to = std::make_unique<order_bus>();
    std::unique_ptr<order_bus> orders_from = std::make_unique<order_bus>();
    exchange_->set_order_buses(orders_to.get(), orders_from.get());

    // 测试场景1：市价买单（应该立即成交，成交价为卖一价）
    {
        // 创建市价买单
        internal_order buy_order = create_test_order(1, 0.0, 5.0, side_type::buy, order_type::market);

        // 处理订单
        backtest_error result = exchange_->process_order(buy_order);

        // 验证处理成功
        EXPECT_EQ(backtest_error::none, result);

        // 验证订单已成交（不在订单列表中）
        EXPECT_EQ(0, exchange_->orders().count(1));

        // 验证订单回报已发送
        EXPECT_EQ(1, orders_to->size());
        auto order_result = orders_to->pop_front();
        ASSERT_TRUE(order_result.has_value());
        auto report = order_result.value().first;
        EXPECT_EQ(1, report.order_id);
        EXPECT_EQ(order_status::filled, report.status);
        EXPECT_EQ(5.0, report.executed_quantity);
        EXPECT_EQ(0.0, report.leaves_quantity);
        EXPECT_EQ(101.0, report.executed_price); // 成交价应该是卖一价

        // 清空订单总线，准备下一个测试
        orders_to->reset();
    }

    // 测试场景2：市价卖单（应该立即成交，成交价为买一价）
    {
        // 创建市价卖单
        internal_order sell_order = create_test_order(2, 0.0, 5.0, side_type::sell, order_type::market);

        // 处理订单
        backtest_error result = exchange_->process_order(sell_order);

        // 验证处理成功
        EXPECT_EQ(backtest_error::none, result);

        // 验证订单已成交（不在订单列表中）
        EXPECT_EQ(0, exchange_->orders().count(2));

        // 验证订单回报已发送
        EXPECT_EQ(1, orders_to->size());
        auto order_result = orders_to->pop_front();
        ASSERT_TRUE(order_result.has_value());
        auto report = order_result.value().first;
        EXPECT_EQ(2, report.order_id);
        EXPECT_EQ(order_status::filled, report.status);
        EXPECT_EQ(5.0, report.executed_quantity);
        EXPECT_EQ(0.0, report.leaves_quantity);
        EXPECT_EQ(99.0, report.executed_price); // 成交价应该是买一价

        // 清空订单总线，准备下一个测试
        orders_to->reset();
    }

    // 测试场景3：空市场深度下的市价买单（应该拒绝）
    {
        // 清空市场深度
        depth_->clear();

        // 创建市价买单
        internal_order buy_order = create_test_order(3, 0.0, 5.0, side_type::buy, order_type::market);

        // 处理订单
        backtest_error result = exchange_->process_order(buy_order);

        // 验证处理成功但订单被拒绝
        EXPECT_EQ(backtest_error::none, result);

        // 验证订单回报已发送，状态为拒绝
        EXPECT_EQ(1, orders_to->size());
        auto order_result = orders_to->pop_front();
        ASSERT_TRUE(order_result.has_value());
        auto report = order_result.value().first;
        EXPECT_EQ(3, report.order_id);
        EXPECT_EQ(order_status::canceled, report.status);

        // 清空订单总线，准备下一个测试
        orders_to->reset();

        // 恢复市场深度
        depth_->update_bid_depth(99.0, 10.0, 1000000);
        depth_->update_ask_depth(101.0, 20.0, 1000000);
    }
}

/**
 * @brief 测试场景10：测试处理取消订单
 * 测试取消订单的处理逻辑
 */
TEST_F(SimpleNoPartialFillExchangeTest, ProcessCancelOrder) {
    // 设置当前时间戳
    exchange_->set_current_timestamp(1000000);

    // 创建并设置订单总线
    std::unique_ptr<order_bus> orders_to = std::make_unique<order_bus>();
    std::unique_ptr<order_bus> orders_from = std::make_unique<order_bus>();
    exchange_->set_order_buses(orders_to.get(), orders_from.get());

    // 测试场景1：取消一个不存在的订单（应该被拒绝）
    {
        // 创建取消订单
        internal_order cancel_order;
        cancel_order.order_id = 999; // 不存在的订单ID
        cancel_order.request = order_status::canceled;

        // 处理取消订单
        backtest_error result = exchange_->process_order(cancel_order);

        // 验证处理成功
        EXPECT_EQ(backtest_error::none, result);

        // 验证订单回报已发送，状态为拒绝
        EXPECT_EQ(1, orders_to->size());
        auto order_result = orders_to->pop_front();
        ASSERT_TRUE(order_result.has_value());
        auto report = order_result.value().first;
        EXPECT_EQ(999, report.order_id);
        EXPECT_EQ(order_status::rejected, report.status);

        // 清空订单总线，准备下一个测试
        orders_to->reset();
    }

    // 测试场景2：取消一个存在的订单（应该成功）
    {
        // 先创建一个新订单（买单，价格低于卖一价，会挂单）
        internal_order buy_order = create_test_order(100, 98.0, 5.0, side_type::buy);
        backtest_error result = exchange_->process_order(buy_order);
        EXPECT_EQ(backtest_error::none, result);

        // 验证订单已添加到订单列表中
        EXPECT_EQ(1, exchange_->orders().count(100));

        // 清空订单总线
        orders_to->reset();

        // 创建取消订单
        internal_order cancel_order;
        cancel_order.order_id = 100;
        cancel_order.request = order_status::canceled;

        // 处理取消订单
        result = exchange_->process_order(cancel_order);

        // 验证处理成功
        EXPECT_EQ(backtest_error::none, result);

        // 验证订单仍然在订单列表中，但状态已更新为取消
        EXPECT_EQ(1, exchange_->orders().count(100));
        EXPECT_EQ(order_status::canceled, exchange_->orders().at(100).status);

        // 验证订单回报已发送，状态为取消
        EXPECT_EQ(1, orders_to->size());
        auto order_result = orders_to->pop_front();
        ASSERT_TRUE(order_result.has_value());
        auto report = order_result.value().first;
        EXPECT_EQ(100, report.order_id);
        EXPECT_EQ(order_status::canceled, report.status);

        // 清空订单总线，准备下一个测试
        orders_to->reset();
    }

    // 测试场景3：取消一个已成交的订单（应该被拒绝）
    {
        // 先创建一个新订单（买单，价格高于卖一价，会立即成交）
        internal_order buy_order = create_test_order(101, 102.0, 5.0, side_type::buy);
        backtest_error result = exchange_->process_order(buy_order);
        EXPECT_EQ(backtest_error::none, result);

        // 验证订单已成交（不在订单列表中）
        EXPECT_EQ(0, exchange_->orders().count(101));

        // 清空订单总线
        orders_to->reset();

        // 创建取消订单
        internal_order cancel_order;
        cancel_order.order_id = 101;
        cancel_order.request = order_status::canceled;

        // 处理取消订单
        result = exchange_->process_order(cancel_order);

        // 验证处理成功
        EXPECT_EQ(backtest_error::none, result);

        // 验证订单回报已发送，状态为拒绝
        EXPECT_EQ(1, orders_to->size());
        auto order_result = orders_to->pop_front();
        ASSERT_TRUE(order_result.has_value());
        auto report = order_result.value().first;
        EXPECT_EQ(101, report.order_id);
        EXPECT_EQ(order_status::rejected, report.status);

        // 清空订单总线
        orders_to->reset();
    }
}

/**
 * @brief 测试场景11：测试不同时效类型订单
 * 测试IOC、FOK、GTX等不同时效类型订单的处理逻辑
 */
TEST_F(SimpleNoPartialFillExchangeTest, ProcessDifferentTimeInForceOrders) {
    // 设置当前时间戳
    exchange_->set_current_timestamp(1000000);

    // 创建并设置订单总线
    std::unique_ptr<order_bus> orders_to = std::make_unique<order_bus>();
    std::unique_ptr<order_bus> orders_from = std::make_unique<order_bus>();
    exchange_->set_order_buses(orders_to.get(), orders_from.get());

    // 测试场景1：IOC订单（价格不能立即成交，应该被取消）
    {
        // 创建买单，价格低于卖一价，使用IOC时效
        internal_order buy_order = create_test_order(200, 98.0, 5.0, side_type::buy);
        buy_order.time_in_force = time_in_force_type::ioc;

        // 处理订单
        backtest_error result = exchange_->process_order(buy_order);

        // 验证处理成功
        EXPECT_EQ(backtest_error::none, result);

        // 验证订单回报已发送，状态为取消
        EXPECT_EQ(1, orders_to->size());
        auto order_result = orders_to->pop_front();
        ASSERT_TRUE(order_result.has_value());
        auto report = order_result.value().first;
        EXPECT_EQ(200, report.order_id);
        EXPECT_EQ(order_status::canceled, report.status);

        // 清空订单总线，准备下一个测试
        orders_to->reset();
    }

    // 测试场景2：IOC订单（价格可以立即成交，应该成交）
    {
        // 创建买单，价格高于卖一价，使用IOC时效
        internal_order buy_order = create_test_order(201, 102.0, 5.0, side_type::buy);
        buy_order.time_in_force = time_in_force_type::ioc;

        // 处理订单
        backtest_error result = exchange_->process_order(buy_order);

        // 验证处理成功
        EXPECT_EQ(backtest_error::none, result);

        // 验证订单已成交（不在订单列表中）
        EXPECT_EQ(0, exchange_->orders().count(201));

        // 验证订单回报已发送，状态为成交
        EXPECT_EQ(1, orders_to->size());
        auto order_result = orders_to->pop_front();
        ASSERT_TRUE(order_result.has_value());
        auto report = order_result.value().first;
        EXPECT_EQ(201, report.order_id);
        EXPECT_EQ(order_status::filled, report.status);

        // 清空订单总线，准备下一个测试
        orders_to->reset();
    }

    // 测试场景3：FOK订单（数量不能完全成交，应该被取消）
    {
        // 设置市场深度，卖一价数量为2.0，不足以满足订单数量
        depth_->clear();
        depth_->update_bid_depth(99.0, 10.0, 1000000);
        depth_->update_ask_depth(101.0, 2.0, 1000000);

        // 创建买单，价格高于卖一价，使用FOK时效，数量为5.0
        internal_order buy_order = create_test_order(202, 102.0, 5.0, side_type::buy);
        buy_order.time_in_force = time_in_force_type::fok;

        // 处理订单
        backtest_error result = exchange_->process_order(buy_order);

        // 验证处理成功
        EXPECT_EQ(backtest_error::none, result);

        // 验证订单回报已发送，状态为取消
        EXPECT_EQ(1, orders_to->size());
        auto order_result = orders_to->pop_front();
        ASSERT_TRUE(order_result.has_value());
        auto report = order_result.value().first;
        EXPECT_EQ(202, report.order_id);
        EXPECT_EQ(order_status::canceled, report.status);

        // 清空订单总线，准备下一个测试
        orders_to->reset();

        // 恢复市场深度
        depth_->clear();
        depth_->update_bid_depth(99.0, 10.0, 1000000);
        depth_->update_ask_depth(101.0, 20.0, 1000000);
    }

    // 测试场景4：FOK订单（数量可以完全成交，应该成交）
    {
        // 创建买单，价格高于卖一价，使用FOK时效，数量为5.0
        internal_order buy_order = create_test_order(203, 102.0, 5.0, side_type::buy);
        buy_order.time_in_force = time_in_force_type::fok;

        // 处理订单
        backtest_error result = exchange_->process_order(buy_order);

        // 验证处理成功
        EXPECT_EQ(backtest_error::none, result);

        // 验证订单已成交（不在订单列表中）
        EXPECT_EQ(0, exchange_->orders().count(203));

        // 验证订单回报已发送，状态为成交
        EXPECT_EQ(1, orders_to->size());
        auto order_result = orders_to->pop_front();
        ASSERT_TRUE(order_result.has_value());
        auto report = order_result.value().first;
        EXPECT_EQ(203, report.order_id);
        EXPECT_EQ(order_status::filled, report.status);

        // 清空订单总线，准备下一个测试
        orders_to->reset();
    }

    // 测试场景5：GTX订单（价格会立即成交，应该被拒绝）
    {
        // 创建买单，价格高于卖一价，使用GTX时效
        internal_order buy_order = create_test_order(204, 102.0, 5.0, side_type::buy);
        buy_order.time_in_force = time_in_force_type::gtx;

        // 处理订单
        backtest_error result = exchange_->process_order(buy_order);

        // 验证处理成功
        EXPECT_EQ(backtest_error::none, result);

        // 验证订单回报已发送，状态为拒绝
        EXPECT_EQ(1, orders_to->size());
        auto order_result = orders_to->pop_front();
        ASSERT_TRUE(order_result.has_value());
        auto report = order_result.value().first;
        EXPECT_EQ(204, report.order_id);
        EXPECT_EQ(order_status::rejected, report.status);

        // 清空订单总线，准备下一个测试
        orders_to->reset();
    }

    // 测试场景6：GTX订单（价格不会立即成交，应该挂单）
    {
        // 创建买单，价格低于卖一价，使用GTX时效
        internal_order buy_order = create_test_order(205, 98.0, 5.0, side_type::buy);
        buy_order.time_in_force = time_in_force_type::gtx;

        // 处理订单
        backtest_error result = exchange_->process_order(buy_order);

        // 验证处理成功
        EXPECT_EQ(backtest_error::none, result);

        // 验证订单已添加到订单列表中
        EXPECT_EQ(1, exchange_->orders().count(205));

        // 验证订单回报已发送，状态为新订单
        EXPECT_EQ(1, orders_to->size());
        auto order_result = orders_to->pop_front();
        ASSERT_TRUE(order_result.has_value());
        auto report = order_result.value().first;
        EXPECT_EQ(205, report.order_id);
        EXPECT_EQ(order_status::new_order, report.status);

        // 清空订单总线
        orders_to->reset();
    }
}

/**
 * @brief 测试场景12：测试市场深度变化触发订单成交
 * 测试最佳买价和卖价变化触发订单成交的逻辑
 */
TEST_F(SimpleNoPartialFillExchangeTest, ProcessMarketDepthChange) {
    // 设置当前时间戳
    exchange_->set_current_timestamp(1000000);

    // 创建并设置订单总线
    std::unique_ptr<order_bus> orders_to = std::make_unique<order_bus>();
    std::unique_ptr<order_bus> orders_from = std::make_unique<order_bus>();
    exchange_->set_order_buses(orders_to.get(), orders_from.get());

    // 测试场景1：最佳卖价下降触发买单成交
    {
        // 创建买单，价格为100.0，低于当前卖一价101.0
        internal_order buy_order = create_test_order(300, 100.0, 5.0, side_type::buy);
        backtest_error result = exchange_->process_order(buy_order);
        EXPECT_EQ(backtest_error::none, result);

        // 验证订单已添加到订单列表中
        EXPECT_EQ(1, exchange_->orders().count(300));

        // 清空订单总线
        orders_to->reset();

        // 直接更新深度，最佳卖价下降到100.0
        depth_->clear();
        depth_->update_bid_depth(99.0, 10.0, 1000000);
        depth_->update_ask_depth(100.0, 20.0, 1000000); // 新的卖一价，与买单价格相等

        // 尝试撤合订单
        exchange_->try_match_orders();

        // 验证订单已成交（状态为成交）
        EXPECT_EQ(1, exchange_->orders().count(300));
        EXPECT_EQ(order_status::filled, exchange_->orders().at(300).status);

        // 验证订单回报已发送，状态为成交
        EXPECT_EQ(1, orders_to->size());
        auto order_result = orders_to->pop_front();
        ASSERT_TRUE(order_result.has_value());
        auto report = order_result.value().first;
        EXPECT_EQ(300, report.order_id);
        EXPECT_EQ(order_status::filled, report.status);
        EXPECT_EQ(100.0, report.executed_price); // 成交价应该是订单价格

        // 清空订单总线，准备下一个测试
        orders_to->reset();

        // 恢复市场深度
        depth_->clear();
        depth_->update_bid_depth(99.0, 10.0, 1000000);
        depth_->update_ask_depth(101.0, 20.0, 1000000);
    }

    // 测试场景2：最佳买价上涨触发卖单成交
    {
        // 创建卖单，价格为100.0，高于当前买一价99.0
        internal_order sell_order = create_test_order(301, 100.0, 5.0, side_type::sell);
        backtest_error result = exchange_->process_order(sell_order);
        EXPECT_EQ(backtest_error::none, result);

        // 验证订单已添加到订单列表中
        EXPECT_EQ(1, exchange_->orders().count(301));

        // 清空订单总线
        orders_to->reset();

        // 直接更新深度，最佳买价上涨到100.0
        depth_->clear();
        depth_->update_bid_depth(100.0, 10.0, 1000000); // 新的买一价，与卖单价格相等
        depth_->update_ask_depth(101.0, 20.0, 1000000);

        // 尝试撤合订单
        exchange_->try_match_orders();

        // 验证订单已成交（状态为成交）
        EXPECT_EQ(1, exchange_->orders().count(301));
        EXPECT_EQ(order_status::filled, exchange_->orders().at(301).status);

        // 验证订单回报已发送，状态为成交
        EXPECT_EQ(1, orders_to->size());
        auto order_result = orders_to->pop_front();
        ASSERT_TRUE(order_result.has_value());
        auto report = order_result.value().first;
        EXPECT_EQ(301, report.order_id);
        EXPECT_EQ(order_status::filled, report.status);
        EXPECT_EQ(100.0, report.executed_price); // 成交价应该是订单价格

        // 清空订单总线
        orders_to->reset();
    }
}






