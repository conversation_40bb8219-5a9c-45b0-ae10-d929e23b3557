#pragma once

#include <cstdint>

namespace fast_trader_elite {
namespace cpp_backtest {
namespace test {

// 定义事件类型常量
const uint8_t DEPTH_EVENT = 1;
const uint8_t TRANSACTION_EVENT = 2;
const uint8_t KLINE_EVENT = 3;

// 定义事件标志常量
const uint8_t LOCAL_EVENT = 1;
const uint8_t EXCH_EVENT = 2;

// 模拟记录头结构
struct mock_record_header {
    uint8_t flags;        // 事件标志
    uint8_t type;         // 事件类型
    int64_t local_timestamp;  // 本地时间戳
    int64_t exch_timestamp;   // 交易所时间戳
};

// 错误类型扩展
enum class mock_backtest_error {
    none = 0,
    invalid_event_type,
    invalid_operation,
    invalid_price,
    invalid_quantity,
    invalid_side,
    invalid_order_type,
    invalid_order_status,
    order_id_exist,
    order_id_not_exist
};

} // namespace test
} // namespace cpp_backtest
} // namespace fast_trader_elite
