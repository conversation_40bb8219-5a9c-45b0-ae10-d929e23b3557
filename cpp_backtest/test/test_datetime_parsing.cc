#include <iostream>
#include <iomanip>
#include <sstream>
#include <ctime>
#include <stdexcept>

// 辅助函数：将日期时间字符串转换为毫秒时间戳
int64_t parse_datetime_to_timestamp(const std::string& datetime_str) {
  struct tm tm = {};
  std::istringstream ss(datetime_str);
  ss >> std::get_time(&tm, "%Y-%m-%d %H:%M:%S");

  if (ss.fail()) {
    throw std::runtime_error("Failed to parse datetime string: " + datetime_str);
  }

  // 设置为UTC时间
  tm.tm_isdst = 0;

  // 转换为时间戳（秒）
  time_t timestamp = std::mktime(&tm);

  // 获取本地时区与UTC的差异（秒）
  time_t now = time(nullptr);
  struct tm utc_tm;
  struct tm local_tm;
  gmtime_r(&now, &utc_tm);
  localtime_r(&now, &local_tm);

  // 计算时区差异（秒）
  time_t utc_time = mktime(&utc_tm);
  time_t local_time = mktime(&local_tm);
  int timezone_offset = difftime(local_time, utc_time);

  // 调整时间戳为UTC时间
  timestamp -= timezone_offset;

  // 调整为UTC+8时区（增加8小时）
  timestamp += 8 * 3600;

  // 转换为毫秒
  return static_cast<int64_t>(timestamp) * 1000;
}

// 辅助函数：将毫秒时间戳转换为可读的日期时间字符串
std::string format_timestamp(int64_t timestamp) {
    // 将毫秒时间戳转换为秒
    time_t seconds = timestamp / 1000;

    // 获取毫秒部分
    int milliseconds = timestamp % 1000;

    // 转换为本地时间
    struct tm timeinfo;
    #ifdef _WIN32
        localtime_s(&timeinfo, &seconds);
    #else
        localtime_r(&seconds, &timeinfo);
    #endif

    // 格式化为字符串
    std::ostringstream oss;
    oss << std::put_time(&timeinfo, "%Y-%m-%d %H:%M:%S");
    oss << "." << std::setfill('0') << std::setw(3) << milliseconds;

    return oss.str();
}

int main() {
    // 测试日期时间字符串转换为时间戳
    std::string test_date = "2024-11-01 12:00:00";
    int64_t timestamp = parse_datetime_to_timestamp(test_date);
    std::cout << "Original date string: " << test_date << std::endl;
    std::cout << "Converted to timestamp: " << timestamp << std::endl;

    // 测试时间戳转换回日期时间字符串
    std::string formatted_date = format_timestamp(timestamp);
    std::cout << "Formatted back to date string: " << formatted_date << std::endl;

    // 测试配置文件中的日期
    std::string start_time = "2021-01-01 00:00:00";
    std::string end_time = "2021-02-01 00:00:00";

    int64_t start_timestamp = parse_datetime_to_timestamp(start_time);
    int64_t end_timestamp = parse_datetime_to_timestamp(end_time);

    std::cout << "Start time: " << start_time << " -> " << start_timestamp << std::endl;
    std::cout << "End time: " << end_time << " -> " << end_timestamp << std::endl;

    // 验证转换回来是否一致
    std::cout << "Start time formatted back: " << format_timestamp(start_timestamp) << std::endl;
    std::cout << "End time formatted back: " << format_timestamp(end_timestamp) << std::endl;

    // 检查原始配置中的时间戳
    int64_t original_start_timestamp = 1609459200000;
    int64_t original_end_timestamp = 1612137600000;

    std::cout << "\nOriginal config timestamps:" << std::endl;
    std::cout << "Original start timestamp: " << original_start_timestamp << " -> "
              << format_timestamp(original_start_timestamp) << std::endl;
    std::cout << "Original end timestamp: " << original_end_timestamp << " -> "
              << format_timestamp(original_end_timestamp) << std::endl;

    // 测试原始配置中的日期时间对应的时间戳
    std::string original_start_time = "2021-01-01 08:00:00";
    std::string original_end_time = "2021-02-01 08:00:00";

    int64_t converted_start_timestamp = parse_datetime_to_timestamp(original_start_time);
    int64_t converted_end_timestamp = parse_datetime_to_timestamp(original_end_time);

    std::cout << "\nConverting original date strings:" << std::endl;
    std::cout << "Original start time: " << original_start_time << " -> "
              << converted_start_timestamp << std::endl;
    std::cout << "Original end time: " << original_end_time << " -> "
              << converted_end_timestamp << std::endl;

    // 检查是否与原始时间戳匹配
    std::cout << "\nComparing timestamps:" << std::endl;
    std::cout << "Start time match: "
              << (converted_start_timestamp == original_start_timestamp ? "Yes" : "No")
              << " (Diff: " << (converted_start_timestamp - original_start_timestamp) << ")" << std::endl;
    std::cout << "End time match: "
              << (converted_end_timestamp == original_end_timestamp ? "Yes" : "No")
              << " (Diff: " << (converted_end_timestamp - original_end_timestamp) << ")" << std::endl;

    return 0;
}
