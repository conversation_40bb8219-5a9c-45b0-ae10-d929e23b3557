#include <gtest/gtest.h>
#include <fast_trader_elite/cpp_backtest/types.h>
#include <vector>
#include <memory>
#include <algorithm>

using namespace fast_trader_elite::cpp_backtest;

// 创建一个简化的记录结构用于测试
struct test_record {
    record_header header;
};

// 创建一个测试用的数据预处理器类，实现与原始类相同的逻辑
class TestDataPreprocessor {
public:
    TestDataPreprocessor() {}

    // 获取记录
    const std::vector<test_record>& get_records() const {
        return records_;
    }

    // 添加测试记录
    void add_test_record(uint8_t type, uint8_t flags, int64_t exch_timestamp, int64_t local_timestamp) {
        test_record rec;
        rec.header.record_type = type;
        rec.header.flags = flags;
        rec.header.instrument_idx = 0;
        rec.header.exch_timestamp = exch_timestamp;
        rec.header.local_timestamp = local_timestamp;
        rec.header.data_size = 0;
        records_.push_back(rec);
    }

    // 实现correct_event_order函数的逻辑
    void correct_event_order() {
        if (records_.empty()) {
            return;
        }

        // 按交易所时间戳排序
        std::vector<size_t> sorted_exch_index(records_.size());
        for (size_t i = 0; i < records_.size(); i++) {
            sorted_exch_index[i] = i;
        }
        std::sort(sorted_exch_index.begin(), sorted_exch_index.end(), [this](size_t a, size_t b) {
            return records_[a].header.exch_timestamp < records_[b].header.exch_timestamp;
        });

        // 按本地时间戳排序
        std::vector<size_t> sorted_local_index(records_.size());
        for (size_t i = 0; i < records_.size(); i++) {
            sorted_local_index[i] = i;
        }
        std::sort(sorted_local_index.begin(), sorted_local_index.end(), [this](size_t a, size_t b) {
            return records_[a].header.local_timestamp < records_[b].header.local_timestamp;
        });

        // 创建新的记录数组
        std::vector<test_record> sorted_final;

        size_t exch_idx = 0;
        size_t local_idx = 0;
        std::cout<<records_.size()<<std::endl;


        while (exch_idx < records_.size() || local_idx < records_.size()) {
            if (exch_idx < records_.size() && local_idx < records_.size()) {
                const auto& exch_record = records_[sorted_exch_index[exch_idx]];
                const auto& local_record = records_[sorted_local_index[local_idx]];

                // 如果交易所时间戳和本地时间戳都相同
                if (exch_record.header.exch_timestamp == local_record.header.exch_timestamp &&
                    exch_record.header.local_timestamp == local_record.header.local_timestamp) {

                    // 创建新记录，设置两个标志
                    test_record new_record = exch_record;
                    new_record.header.flags |= (EXCH_EVENT | LOCAL_EVENT);
                    sorted_final.push_back(new_record);

                    exch_idx++;
                    local_idx++;
                }
                // 如果交易所时间戳相同但本地时间戳更小，或者交易所时间戳更小
                else if ((exch_record.header.exch_timestamp == local_record.header.exch_timestamp &&
                          exch_record.header.local_timestamp < local_record.header.local_timestamp) ||
                         (exch_record.header.exch_timestamp < local_record.header.exch_timestamp)) {
                    // 创建新记录，只设置交易所标志
                    test_record new_record = exch_record;
                    new_record.header.flags |= EXCH_EVENT;
                    sorted_final.push_back(new_record);

                    exch_idx++;
                }
                // 如果交易所时间戳相同但本地时间戳更大，或者交易所时间戳更大
                else if ((exch_record.header.exch_timestamp == local_record.header.exch_timestamp &&
                          exch_record.header.local_timestamp > local_record.header.local_timestamp) ||
                         (exch_record.header.exch_timestamp > local_record.header.exch_timestamp)) {
                    // 创建新记录，只设置本地标志
                    test_record new_record = local_record;
                    new_record.header.flags |= LOCAL_EVENT;
                    sorted_final.push_back(new_record);

                    local_idx++;
                }
            }
            // 如果还有交易所事件未处理
            else if (exch_idx < records_.size()) {
                const auto& exch_record = records_[sorted_exch_index[exch_idx]];

                // 创建新记录，只设置交易所标志
                test_record new_record = exch_record;
                new_record.header.flags |= EXCH_EVENT;
                sorted_final.push_back(new_record);

                exch_idx++;
            }
            // 如果还有本地事件未处理
            else if (local_idx < records_.size()) {
                const auto& local_record = records_[sorted_local_index[local_idx]];

                // 创建新记录，只设置本地标志
                test_record new_record = local_record;
                new_record.header.flags |= LOCAL_EVENT;
                sorted_final.push_back(new_record);

                local_idx++;
            }
        }
        std::cout<<sorted_final.size()<<std::endl;

        // 替换原来的记录数组
        records_ = std::move(sorted_final);
    }

    // 实现validate_event_order函数的逻辑
    bool validate_event_order() const {
        // 验证交易所事件顺序
        int64_t last_exch_timestamp = 0;
        bool first_exch = true;

        // 验证本地事件顺序
        int64_t last_local_timestamp = 0;
        bool first_local = true;

        for (const auto& record : records_) {
            // 检查交易所事件
            if (record.header.flags & EXCH_EVENT) {
                if (!first_exch && record.header.exch_timestamp < last_exch_timestamp) {
                    return false;
                }
                first_exch = false;
                last_exch_timestamp = record.header.exch_timestamp;
            }

            // 检查本地事件
            if (record.header.flags & LOCAL_EVENT) {
                if (!first_local && record.header.local_timestamp < last_local_timestamp) {
                    return false;
                }
                first_local = false;
                last_local_timestamp = record.header.local_timestamp;
            }
        }

        return true;
    }

private:
    std::vector<test_record> records_;
};

/**
 * @brief 测试数据预处理器的事件顺序校正功能
 * 测试场景：
 * 1. 测试空记录集
 * 2. 测试正常顺序的记录
 * 3. 测试交易所时间戳乱序但本地时间戳有序的记录
 * 4. 测试交易所时间戳有序但本地时间戳乱序的记录
 * 5. 测试交易所和本地时间戳都乱序的记录
 * 6. 测试相同时间戳的记录
 */
class DataPreprocessorTest : public ::testing::Test {
protected:
    void SetUp() override {
        preprocessor_ = std::make_unique<TestDataPreprocessor>();
    }

    void TearDown() override {
        preprocessor_.reset();
    }

    std::unique_ptr<TestDataPreprocessor> preprocessor_;
};

// 测试场景1：测试空记录集
TEST_F(DataPreprocessorTest, EmptyRecords) {
    // 校正空记录集
    preprocessor_->correct_event_order();

    // 验证结果仍然为空
    EXPECT_TRUE(preprocessor_->get_records().empty());

    // 验证事件顺序
    EXPECT_TRUE(preprocessor_->validate_event_order());
}

// 测试场景2：测试正常顺序的记录
TEST_F(DataPreprocessorTest, OrderedRecords) {
    // 添加按顺序排列的记录
    preprocessor_->add_test_record(DEPTH_MARKET_DATA, 0, 1000, 1100);
    preprocessor_->add_test_record(DEPTH_MARKET_DATA, 0, 2000, 2100);
    preprocessor_->add_test_record(DEPTH_MARKET_DATA, 0, 3000, 3100);

    // 校正事件顺序
    preprocessor_->correct_event_order();

    // 验证记录数量不变
    EXPECT_EQ(3, preprocessor_->get_records().size());

    // 验证每个记录都有正确的标志
    for (const auto& record : preprocessor_->get_records()) {
        EXPECT_TRUE(record.header.flags & EXCH_EVENT);
        EXPECT_TRUE(record.header.flags & LOCAL_EVENT);
    }

    // 验证事件顺序
    EXPECT_TRUE(preprocessor_->validate_event_order());
}

// 测试场景3：测试交易所时间戳乱序但本地时间戳有序的记录
TEST_F(DataPreprocessorTest, ExchangeTimestampOutOfOrder) {
    // 添加交易所时间戳乱序的记录
    preprocessor_->add_test_record(DEPTH_MARKET_DATA, 0, 3000, 3100);
    preprocessor_->add_test_record(DEPTH_MARKET_DATA, 0, 1000, 4100);
    preprocessor_->add_test_record(DEPTH_MARKET_DATA, 0, 2000, 5100);

    // 校正事件顺序
    preprocessor_->correct_event_order();

    // 验证记录数量增加（每个记录可能分为EXCH_EVENT和LOCAL_EVENT两部分）
    // 注意：实际数量可能因算法实现而异，这里我们只验证数量增加
    EXPECT_GT(preprocessor_->get_records().size(), 3);

    // 验证事件顺序
    EXPECT_TRUE(preprocessor_->validate_event_order());

    // 验证按交易所时间戳排序的事件
    int64_t last_exch_ts = 0;
    for (const auto& record : preprocessor_->get_records()) {
        if (record.header.flags & EXCH_EVENT) {
            EXPECT_GE(record.header.exch_timestamp, last_exch_ts);
            last_exch_ts = record.header.exch_timestamp;
        }
    }

    // 验证按本地时间戳排序的事件
    int64_t last_local_ts = 0;
    for (const auto& record : preprocessor_->get_records()) {
        if (record.header.flags & LOCAL_EVENT) {
            EXPECT_GE(record.header.local_timestamp, last_local_ts);
            last_local_ts = record.header.local_timestamp;
        }
    }
}

// 测试场景4：测试交易所时间戳有序但本地时间戳乱序的记录
TEST_F(DataPreprocessorTest, LocalTimestampOutOfOrder) {
    // 添加本地时间戳乱序的记录
    preprocessor_->add_test_record(DEPTH_MARKET_DATA, 0, 1000, 5100);
    preprocessor_->add_test_record(DEPTH_MARKET_DATA, 0, 2000, 3100);
    preprocessor_->add_test_record(DEPTH_MARKET_DATA, 0, 3000, 4100);

    // 校正事件顺序
    preprocessor_->correct_event_order();

    // 验证记录数量增加
    // 注意：实际数量可能因算法实现而异，这里我们只验证数量增加
    EXPECT_GT(preprocessor_->get_records().size(), 3);

    // 验证事件顺序
    EXPECT_TRUE(preprocessor_->validate_event_order());

    // 验证按交易所时间戳排序的事件
    int64_t last_exch_ts = 0;
    for (const auto& record : preprocessor_->get_records()) {
        if (record.header.flags & EXCH_EVENT) {
            EXPECT_GE(record.header.exch_timestamp, last_exch_ts);
            last_exch_ts = record.header.exch_timestamp;
        }
    }

    // 验证按本地时间戳排序的事件
    int64_t last_local_ts = 0;
    for (const auto& record : preprocessor_->get_records()) {
        if (record.header.flags & LOCAL_EVENT) {
            EXPECT_GE(record.header.local_timestamp, last_local_ts);
            last_local_ts = record.header.local_timestamp;
        }
    }
}

// 测试场景5：测试交易所和本地时间戳都乱序的记录
TEST_F(DataPreprocessorTest, BothTimestampsOutOfOrder) {
    // 添加两个时间戳都乱序的记录
    preprocessor_->add_test_record(DEPTH_MARKET_DATA, 0, 3000, 4100);
    preprocessor_->add_test_record(DEPTH_MARKET_DATA, 0, 1000, 5100);
    preprocessor_->add_test_record(DEPTH_MARKET_DATA, 0, 2000, 3100);

    // 校正事件顺序
    preprocessor_->correct_event_order();

    // 验证记录数量增加
    // 注意：实际数量可能因算法实现而异，这里我们只验证数量增加
    EXPECT_GT(preprocessor_->get_records().size(), 3);

    // 验证事件顺序
    EXPECT_TRUE(preprocessor_->validate_event_order());

    // 验证按交易所时间戳排序的事件
    int64_t last_exch_ts = 0;
    for (const auto& record : preprocessor_->get_records()) {
        if (record.header.flags & EXCH_EVENT) {
            EXPECT_GE(record.header.exch_timestamp, last_exch_ts);
            last_exch_ts = record.header.exch_timestamp;
        }
    }

    // 验证按本地时间戳排序的事件
    int64_t last_local_ts = 0;
    for (const auto& record : preprocessor_->get_records()) {
        if (record.header.flags & LOCAL_EVENT) {
            EXPECT_GE(record.header.local_timestamp, last_local_ts);
            last_local_ts = record.header.local_timestamp;
        }
    }
}

// 测试场景6：测试相同时间戳的记录
TEST_F(DataPreprocessorTest, SameTimestamps) {
    // 添加相同时间戳的记录
    preprocessor_->add_test_record(DEPTH_MARKET_DATA, 0, 1000, 2000);
    preprocessor_->add_test_record(TRANSACTION_DATA, 0, 1000, 2000);
    preprocessor_->add_test_record(KLINE_DATA, 0, 1000, 2000);

    // 校正事件顺序
    preprocessor_->correct_event_order();

    // 验证记录数量不变
    EXPECT_EQ(3, preprocessor_->get_records().size());

    // 验证每个记录都有正确的标志
    for (const auto& record : preprocessor_->get_records()) {
        EXPECT_TRUE(record.header.flags & EXCH_EVENT);
        EXPECT_TRUE(record.header.flags & LOCAL_EVENT);
    }

    // 验证事件顺序
    EXPECT_TRUE(preprocessor_->validate_event_order());
}

// 测试场景7：测试实际场景 - 使用文档中提供的实际数据
TEST_F(DataPreprocessorTest, RealWorldScenario) {
    // 使用文档中提供的实际数据
    // 1676419207212385000 {'stream': 'btcusdt@trade', 'data': {'e': 'trade', 'E': 1676419206968, 'T': 1676419205111, 's': 'BTCUSDT', 't': 3288803051, 'p': '22177.90', 'q': '0.300', 'X': 'MARKET', 'm': True}}
    preprocessor_->add_test_record(TRANSACTION_DATA, 0, 1676419205111000000, 1676419207212385000);

    // 1676419207212480000 {'stream': 'btcusdt@trade', 'data': {'e': 'trade', 'E': 1676419206968, 'T': 1676419205111, 's': 'BTCUSDT', 't': 3288803052, 'p': '22177.90', 'q': '0.119', 'X': 'MARKET', 'm': True}}
    preprocessor_->add_test_record(TRANSACTION_DATA, 0, 1676419205111000000, 1676419207212480000);

    // 1676419207212527000 {'stream': 'btcusdt@depth@0ms', 'data': {'e': 'depthUpdate', 'E': 1676419206974, 'T': 1676419205108, 's': 'BTCUSDT', ...}}
    preprocessor_->add_test_record(DEPTH_MARKET_DATA, 0, 1676419205108000000, 1676419207212527000);

    // 1676419207212584000 {'stream': 'btcusdt@trade', 'data': {'e': 'trade', 'E': 1676419206976, 'T': 1676419205116, 's': 'BTCUSDT', 't': 3288803053, 'p': '22177.90', 'q': '0.001', 'X': 'MARKET', 'm': True}}
    preprocessor_->add_test_record(TRANSACTION_DATA, 0, 1676419205116000000, 1676419207212584000);

    // 1676419207212621000 {'stream': 'btcusdt@trade', 'data': {'e': 'trade', 'E': 1676419206976, 'T': 1676419205116, 's': 'BTCUSDT', 't': 3288803054, 'p': '22177.90', 'q': '0.005', 'X': 'MARKET', 'm': True}}
    preprocessor_->add_test_record(TRANSACTION_DATA, 0, 1676419205116000000, 1676419207212621000);

    // 校正事件顺序前，先检查原始数据
    // 验证深度行情的交易所时间戳早于部分交易数据，但本地时间戳晚于这些交易数据
    EXPECT_LT(1676419205108000000, 1676419205111000000); // 深度行情的交易所时间戳早于第一笔交易
    EXPECT_GT(1676419207212527000, 1676419207212385000); // 但深度行情的本地时间戳晚于第一笔交易

    // 校正事件顺序
    preprocessor_->correct_event_order();

    // 验证事件顺序
    EXPECT_TRUE(preprocessor_->validate_event_order());

    // 验证记录数量增加
    // 注意：实际数量可能因算法实现而异，这里我们只验证数量增加
    EXPECT_GT(preprocessor_->get_records().size(), 5);

    // 验证结果符合文档中描述的格式
    // 1. 深度行情应该被分成两部分：一部分只有EXCH_EVENT标志，另一部分只有LOCAL_EVENT标志
    // 2. 交易数据应该同时具有EXCH_EVENT和LOCAL_EVENT标志

    // 查找只有EXCH_EVENT标志的深度行情记录
    bool found_depth_exch_only = false;
    // 查找只有LOCAL_EVENT标志的深度行情记录
    bool found_depth_local_only = false;
    // 查找同时具有EXCH_EVENT和LOCAL_EVENT标志的交易记录
    bool found_trade_both_flags = false;

    for (const auto& record : preprocessor_->get_records()) {
        // 检查深度行情记录
        if (record.header.record_type == DEPTH_MARKET_DATA) {
            // 只有EXCH_EVENT标志
            if ((record.header.flags & EXCH_EVENT) && !(record.header.flags & LOCAL_EVENT)) {
                found_depth_exch_only = true;
            }
            // 只有LOCAL_EVENT标志
            else if (!(record.header.flags & EXCH_EVENT) && (record.header.flags & LOCAL_EVENT)) {
                found_depth_local_only = true;
            }
        }
        // 检查交易记录
        else if (record.header.record_type == TRANSACTION_DATA) {
            // 同时具有EXCH_EVENT和LOCAL_EVENT标志
            if ((record.header.flags & EXCH_EVENT) && (record.header.flags & LOCAL_EVENT)) {
                found_trade_both_flags = true;
            }
        }
    }

    // 验证找到了所有期望的记录类型
    EXPECT_TRUE(found_depth_exch_only) << "未找到只有EXCH_EVENT标志的深度行情记录";
    EXPECT_TRUE(found_depth_local_only) << "未找到只有LOCAL_EVENT标志的深度行情记录";
    EXPECT_TRUE(found_trade_both_flags) << "未找到同时具有EXCH_EVENT和LOCAL_EVENT标志的交易记录";

    // 验证深度行情在交易所事件中排在最前面（因为交易所时间戳最早）
    bool found_depth_first = false;
    for (const auto& record : preprocessor_->get_records()) {
        if (record.header.flags & EXCH_EVENT) {
            if (record.header.record_type == DEPTH_MARKET_DATA) {
                found_depth_first = true;
                break;
            } else {
                // 如果第一个EXCH_EVENT不是深度行情，则测试失败
                found_depth_first = false;
                break;
            }
        }
    }
    EXPECT_TRUE(found_depth_first) << "深度行情没有在交易所事件中排在最前面";

    // 验证按交易所时间戳排序的事件
    int64_t last_exch_ts = 0;
    bool first_exch = true;
    for (const auto& record : preprocessor_->get_records()) {
        if (record.header.flags & EXCH_EVENT) {
            if (!first_exch) {
                EXPECT_GE(record.header.exch_timestamp, last_exch_ts)
                    << "交易所事件没有按照交易所时间戳排序";
            }
            first_exch = false;
            last_exch_ts = record.header.exch_timestamp;
        }
    }

    // 验证按本地时间戳排序的事件
    int64_t last_local_ts = 0;
    bool first_local = true;
    for (const auto& record : preprocessor_->get_records()) {
        if (record.header.flags & LOCAL_EVENT) {
            if (!first_local) {
                EXPECT_GE(record.header.local_timestamp, last_local_ts)
                    << "本地事件没有按照本地时间戳排序";
            }
            first_local = false;
            last_local_ts = record.header.local_timestamp;
        }
    }
}
