#include <gtest/gtest.h>
#include <fast_trader_elite/cpp_backtest/models/latency_model.h>
#include <fast_trader_elite/cpp_backtest/types.h>
#include "mock_types.h"

using namespace fast_trader_elite::cpp_backtest;

/**
 * @brief 测试固定延迟模型
 * 测试场景：
 * 1. 测试订单提交延迟
 * 2. 测试订单响应延迟
 * 3. 测试市场数据延迟
 * 4. 测试零延迟
 * 5. 测试不同订单类型的延迟
 */
class ConstantLatencyModelTest : public ::testing::Test {
protected:
    void SetUp() override {
        // 创建不同延迟配置的模型用于测试
        standard_model_ = new constant_latency_model(100, 200, 50);  // 标准延迟：入场100ns，响应200ns，市场50ns
        zero_model_ = new constant_latency_model(0, 0, 0);           // 零延迟
        high_latency_model_ = new constant_latency_model(1000, 2000, 500); // 高延迟

        // 创建测试用的订单
        test_order_.order_id = 1;
        test_order_.price = 100.0;
        test_order_.quantity = 10.0;
        test_order_.side = side_type::buy;
        test_order_.type = order_type::limit;
    }

    void TearDown() override {
        delete standard_model_;
        delete zero_model_;
        delete high_latency_model_;
    }

    constant_latency_model* standard_model_;
    constant_latency_model* zero_model_;
    constant_latency_model* high_latency_model_;
    internal_order test_order_;
};

// 测试场景1：测试订单提交延迟
TEST_F(ConstantLatencyModelTest, EntryLatency) {
    int64_t timestamp = 1000000;

    // 测试标准延迟
    EXPECT_EQ(100, standard_model_->entry(timestamp, test_order_));

    // 测试零延迟
    EXPECT_EQ(0, zero_model_->entry(timestamp, test_order_));

    // 测试高延迟
    EXPECT_EQ(1000, high_latency_model_->entry(timestamp, test_order_));

    // 验证时间戳不影响结果
    EXPECT_EQ(100, standard_model_->entry(2000000, test_order_));
}

// 测试场景2：测试订单响应延迟
TEST_F(ConstantLatencyModelTest, ResponseLatency) {
    int64_t timestamp = 1000000;

    // 测试标准延迟
    EXPECT_EQ(200, standard_model_->response(timestamp, test_order_));

    // 测试零延迟
    EXPECT_EQ(0, zero_model_->response(timestamp, test_order_));

    // 测试高延迟
    EXPECT_EQ(2000, high_latency_model_->response(timestamp, test_order_));

    // 验证时间戳不影响结果
    EXPECT_EQ(200, standard_model_->response(2000000, test_order_));
}

// 测试场景3：测试市场数据延迟
TEST_F(ConstantLatencyModelTest, MarketLatency) {
    int64_t timestamp = 1000000;

    // 测试标准延迟
    EXPECT_EQ(50, standard_model_->market(timestamp));

    // 测试零延迟
    EXPECT_EQ(0, zero_model_->market(timestamp));

    // 测试高延迟
    EXPECT_EQ(500, high_latency_model_->market(timestamp));

    // 验证时间戳不影响结果
    EXPECT_EQ(50, standard_model_->market(2000000));
}

// 测试场景4：测试不同订单类型的延迟
TEST_F(ConstantLatencyModelTest, DifferentOrderTypes) {
    int64_t timestamp = 1000000;

    // 测试限价单
    test_order_.type = order_type::limit;
    EXPECT_EQ(100, standard_model_->entry(timestamp, test_order_));
    EXPECT_EQ(200, standard_model_->response(timestamp, test_order_));

    // 测试市价单
    test_order_.type = order_type::market;
    EXPECT_EQ(100, standard_model_->entry(timestamp, test_order_));
    EXPECT_EQ(200, standard_model_->response(timestamp, test_order_));

    // 测试不支持的订单类型
    test_order_.type = order_type::unsupported;
    EXPECT_EQ(100, standard_model_->entry(timestamp, test_order_));
    EXPECT_EQ(200, standard_model_->response(timestamp, test_order_));
}

// 测试场景5：测试不同方向订单的延迟
TEST_F(ConstantLatencyModelTest, DifferentOrderSides) {
    int64_t timestamp = 1000000;

    // 测试买单
    test_order_.side = side_type::buy;
    EXPECT_EQ(100, standard_model_->entry(timestamp, test_order_));
    EXPECT_EQ(200, standard_model_->response(timestamp, test_order_));

    // 测试卖单
    test_order_.side = side_type::sell;
    EXPECT_EQ(100, standard_model_->entry(timestamp, test_order_));
    EXPECT_EQ(200, standard_model_->response(timestamp, test_order_));
}
