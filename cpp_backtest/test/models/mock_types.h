#pragma once

#include <fast_trader_elite/cpp_backtest/types.h>
#include <fast_trader_elite/cpp_backtest/models/queue_model.h>

namespace fast_trader_elite {
namespace cpp_backtest {

// 为测试提供internal_order的构造函数和析构函数实现
inline internal_order::internal_order()
    : order_id(0)
    , price(0.0)
    , quantity(0.0)
    , leaves_quantity(0.0)
    , executed_quantity(0.0)
    , executed_price(0.0)
    , side(side_type::none)
    , type(order_type::limit)
    , time_in_force(time_in_force_type::gtc)
    , status(order_status::none)
    , request(order_status::none)
    , exchange_timestamp(0)
    , local_timestamp(0)
    , instrument_idx(0)
    , is_maker(false)
    , fee(0.0) {
    // 队列数据已在结构体定义中初始化
}

inline internal_order::~internal_order() {
    // 不需要释放内存，因为队列数据现在是结构体的一部分
}

} // namespace cpp_backtest
} // namespace fast_trader_elite
