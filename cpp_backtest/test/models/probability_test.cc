#include <gtest/gtest.h>
#include <fast_trader_elite/cpp_backtest/models/queue_model.h>
#include <cmath>

using namespace fast_trader_elite::cpp_backtest;

/**
 * @brief 测试概率函数
 * 测试场景：
 * 1. 测试幂函数概率队列函数
 * 2. 测试对数概率队列函数
 * 3. 测试对数概率队列函数2
 * 4. 测试幂函数概率队列函数2
 * 5. 测试幂函数概率队列函数3
 * 6. 测试边界条件
 */
class ProbabilityTest : public ::testing::Test {
protected:
    void SetUp() override {
        // 创建不同参数的概率函数用于测试
        power_prob1_ = new power_prob_queue_func(1.0);  // 线性
        power_prob2_ = new power_prob_queue_func(2.0);  // 平方
        log_prob1_ = new log_prob_queue_func();
        log_prob2_ = new log_prob_queue_func2();
        power_prob2_1_ = new power_prob_queue_func2(1.0);
        power_prob2_2_ = new power_prob_queue_func2(2.0);
        power_prob3_ = new power_prob_queue_func3(1.0);
    }

    void TearDown() override {
        delete power_prob1_;
        delete power_prob2_;
        delete log_prob1_;
        delete log_prob2_;
        delete power_prob2_1_;
        delete power_prob2_2_;
        delete power_prob3_;
    }

    power_prob_queue_func* power_prob1_;
    power_prob_queue_func* power_prob2_;
    log_prob_queue_func* log_prob1_;
    log_prob_queue_func2* log_prob2_;
    power_prob_queue_func2* power_prob2_1_;
    power_prob_queue_func2* power_prob2_2_;
    power_prob_queue_func3* power_prob3_;
};

// 测试场景1：测试幂函数概率队列函数
TEST_F(ProbabilityTest, PowerProbQueueFunc) {
    // 测试线性函数 (n=1)
    // 当前后队列相等时，概率应为0.5
    EXPECT_DOUBLE_EQ(0.5, power_prob1_->prob(100.0, 100.0));
    
    // 当后方队列为前方队列的2倍时，概率应为2/3
    EXPECT_DOUBLE_EQ(2.0/3.0, power_prob1_->prob(100.0, 200.0));
    
    // 当前方队列为后方队列的2倍时，概率应为1/3
    EXPECT_DOUBLE_EQ(1.0/3.0, power_prob1_->prob(200.0, 100.0));
    
    // 测试平方函数 (n=2)
    // 当前后队列相等时，概率应为0.5
    EXPECT_DOUBLE_EQ(0.5, power_prob2_->prob(100.0, 100.0));
    
    // 当后方队列为前方队列的2倍时，概率应为4/5
    double expected = 4.0/5.0;  // (200^2) / (200^2 + 100^2) = 40000 / 50000 = 4/5
    EXPECT_DOUBLE_EQ(expected, power_prob2_->prob(100.0, 200.0));
    
    // 当前方队列为后方队列的2倍时，概率应为1/5
    expected = 1.0/5.0;  // (100^2) / (100^2 + 200^2) = 10000 / 50000 = 1/5
    EXPECT_DOUBLE_EQ(expected, power_prob2_->prob(200.0, 100.0));
}

// 测试场景2：测试对数概率队列函数
TEST_F(ProbabilityTest, LogProbQueueFunc) {
    // 当前后队列相等时
    double front = 100.0;
    double back = 100.0;
    double front_log = std::log(1.0 + front);
    double back_log = std::log(1.0 + back);
    double expected = back_log / (front_log + back_log);
    
    EXPECT_DOUBLE_EQ(expected, log_prob1_->prob(front, back));
    
    // 当后方队列为前方队列的2倍时
    front = 100.0;
    back = 200.0;
    front_log = std::log(1.0 + front);
    back_log = std::log(1.0 + back);
    expected = back_log / (front_log + back_log);
    
    EXPECT_DOUBLE_EQ(expected, log_prob1_->prob(front, back));
    
    // 当前方队列为后方队列的2倍时
    front = 200.0;
    back = 100.0;
    front_log = std::log(1.0 + front);
    back_log = std::log(1.0 + back);
    expected = back_log / (front_log + back_log);
    
    EXPECT_DOUBLE_EQ(expected, log_prob1_->prob(front, back));
}

// 测试场景3：测试对数概率队列函数2
TEST_F(ProbabilityTest, LogProbQueueFunc2) {
    // 当前后队列相等时
    double front = 100.0;
    double back = 100.0;
    double expected = std::log(1.0 + back) / std::log(1.0 + back + front);
    
    EXPECT_DOUBLE_EQ(expected, log_prob2_->prob(front, back));
    
    // 当后方队列为前方队列的2倍时
    front = 100.0;
    back = 200.0;
    expected = std::log(1.0 + back) / std::log(1.0 + back + front);
    
    EXPECT_DOUBLE_EQ(expected, log_prob2_->prob(front, back));
    
    // 当前方队列为后方队列的2倍时
    front = 200.0;
    back = 100.0;
    expected = std::log(1.0 + back) / std::log(1.0 + back + front);
    
    EXPECT_DOUBLE_EQ(expected, log_prob2_->prob(front, back));
}

// 测试场景4：测试幂函数概率队列函数2
TEST_F(ProbabilityTest, PowerProbQueueFunc2) {
    // 测试线性函数 (n=1)
    // 当前后队列相等时
    double front = 100.0;
    double back = 100.0;
    double expected = back / (back + front);  // 100 / 200 = 0.5
    
    EXPECT_DOUBLE_EQ(expected, power_prob2_1_->prob(front, back));
    
    // 当后方队列为前方队列的2倍时
    front = 100.0;
    back = 200.0;
    expected = back / (back + front);  // 200 / 300 = 2/3
    
    EXPECT_DOUBLE_EQ(expected, power_prob2_1_->prob(front, back));
    
    // 测试平方函数 (n=2)
    // 当前后队列相等时
    front = 100.0;
    back = 100.0;
    expected = std::pow(back, 2.0) / std::pow(back + front, 2.0);  // 10000 / 40000 = 0.25
    
    EXPECT_DOUBLE_EQ(expected, power_prob2_2_->prob(front, back));
    
    // 当后方队列为前方队列的2倍时
    front = 100.0;
    back = 200.0;
    expected = std::pow(back, 2.0) / std::pow(back + front, 2.0);  // 40000 / 90000 = 4/9
    
    EXPECT_DOUBLE_EQ(expected, power_prob2_2_->prob(front, back));
}

// 测试场景5：测试幂函数概率队列函数3
TEST_F(ProbabilityTest, PowerProbQueueFunc3) {
    // 测试线性函数 (n=1)
    // 当前后队列相等时
    double front = 100.0;
    double back = 100.0;
    double expected = 1.0 - std::pow(front / (front + back), 1.0);  // 1 - 0.5 = 0.5
    
    EXPECT_DOUBLE_EQ(expected, power_prob3_->prob(front, back));
    
    // 当后方队列为前方队列的2倍时
    front = 100.0;
    back = 200.0;
    expected = 1.0 - std::pow(front / (front + back), 1.0);  // 1 - 1/3 = 2/3
    
    EXPECT_DOUBLE_EQ(expected, power_prob3_->prob(front, back));
    
    // 当前方队列为后方队列的2倍时
    front = 200.0;
    back = 100.0;
    expected = 1.0 - std::pow(front / (front + back), 1.0);  // 1 - 2/3 = 1/3
    
    EXPECT_DOUBLE_EQ(expected, power_prob3_->prob(front, back));
}

// 测试场景6：测试边界条件
TEST_F(ProbabilityTest, EdgeCases) {
    // 测试前方队列为0的情况
    EXPECT_DOUBLE_EQ(1.0, power_prob1_->prob(0.0, 100.0));
    EXPECT_DOUBLE_EQ(1.0, power_prob2_->prob(0.0, 100.0));
    EXPECT_DOUBLE_EQ(1.0, log_prob1_->prob(0.0, 100.0));
    EXPECT_DOUBLE_EQ(1.0, log_prob2_->prob(0.0, 100.0));
    EXPECT_DOUBLE_EQ(1.0, power_prob2_1_->prob(0.0, 100.0));
    EXPECT_DOUBLE_EQ(1.0, power_prob2_2_->prob(0.0, 100.0));
    EXPECT_DOUBLE_EQ(1.0, power_prob3_->prob(0.0, 100.0));
    
    // 测试后方队列为0的情况
    EXPECT_DOUBLE_EQ(0.0, power_prob1_->prob(100.0, 0.0));
    EXPECT_DOUBLE_EQ(0.0, power_prob2_->prob(100.0, 0.0));
    EXPECT_DOUBLE_EQ(0.0, log_prob1_->prob(100.0, 0.0));
    EXPECT_DOUBLE_EQ(0.0, log_prob2_->prob(100.0, 0.0));
    EXPECT_DOUBLE_EQ(0.0, power_prob2_1_->prob(100.0, 0.0));
    EXPECT_DOUBLE_EQ(0.0, power_prob2_2_->prob(100.0, 0.0));
    EXPECT_DOUBLE_EQ(0.0, power_prob3_->prob(100.0, 0.0));
    
    // 测试前后方队列都为0的情况
    // 注意：这种情况可能导致除以0，具体行为取决于实现
    // 在实际应用中应该避免这种情况
}
