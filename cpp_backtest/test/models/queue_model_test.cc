#include <gtest/gtest.h>
#include <fast_trader_elite/cpp_backtest/models/queue_model.h>
#include <fast_trader_elite/cpp_backtest/market/market_depth.h>
#include <memory>
#include "mock_types.h"

using namespace fast_trader_elite::cpp_backtest;

// 创建一个模拟的市场深度类用于测试
class MockMarketDepth : public market_depth {
public:
    MockMarketDepth(double tick_size = 0.01, double lot_size = 1.0)
        : tick_size_(tick_size), lot_size_(lot_size) {
        // 初始化买卖盘
        bid_prices_ = {10000, 9999, 9998};  // 100.00, 99.99, 99.98
        ask_prices_ = {10001, 10002, 10003};  // 100.01, 100.02, 100.03

        // 设置各价格的数量
        bid_quantities_[10000] = 100.0;
        bid_quantities_[9999] = 200.0;
        bid_quantities_[9998] = 300.0;

        ask_quantities_[10001] = 100.0;
        ask_quantities_[10002] = 200.0;
        ask_quantities_[10003] = 300.0;
    }

    int64_t best_bid_tick() const override {
        return bid_prices_.empty() ? 0 : bid_prices_[0];
    }

    int64_t best_ask_tick() const override {
        return ask_prices_.empty() ? 0 : ask_prices_[0];
    }

    double bid_qty(int64_t price_tick) const override {
        auto it = bid_quantities_.find(price_tick);
        return it != bid_quantities_.end() ? it->second : 0.0;
    }

    double ask_qty(int64_t price_tick) const override {
        auto it = ask_quantities_.find(price_tick);
        return it != ask_quantities_.end() ? it->second : 0.0;
    }

    const std::vector<int64_t>& bid_prices() const override {
        return bid_prices_;
    }

    const std::vector<int64_t>& ask_prices() const override {
        return ask_prices_;
    }

    const std::vector<double>& bid_qtys() const override {
        static std::vector<double> qtys;
        qtys.clear();
        for (const auto& price : bid_prices_) {
            qtys.push_back(bid_qty(price));
        }
        return qtys;
    }

    const std::vector<double>& ask_qtys() const override {
        static std::vector<double> qtys;
        qtys.clear();
        for (const auto& price : ask_prices_) {
            qtys.push_back(ask_qty(price));
        }
        return qtys;
    }

    double tick_size() const override {
        return tick_size_;
    }

    double lot_size() const override {
        return lot_size_;
    }

    void set_tick_size(double tick_size) override {
        tick_size_ = tick_size;
    }

    void set_lot_size(double lot_size) override {
        lot_size_ = lot_size;
    }

    void clear() override {
        bid_prices_.clear();
        ask_prices_.clear();
        bid_quantities_.clear();
        ask_quantities_.clear();
    }

    void clear_depth(side_type side, double clear_upto_price) override {
        int64_t price_tick = price_to_tick(clear_upto_price);

        if (side == side_type::buy) {
            auto it = bid_prices_.begin();
            while (it != bid_prices_.end()) {
                if (*it <= price_tick) {
                    bid_quantities_.erase(*it);
                    it = bid_prices_.erase(it);
                } else {
                    ++it;
                }
            }
        } else {
            auto it = ask_prices_.begin();
            while (it != ask_prices_.end()) {
                if (*it >= price_tick) {
                    ask_quantities_.erase(*it);
                    it = ask_prices_.erase(it);
                } else {
                    ++it;
                }
            }
        }
    }

    std::tuple<int64_t, int64_t, int64_t, double, double, int64_t>
    update_bid_depth(double price, double qty, int64_t timestamp) override {
        int64_t price_tick = price_to_tick(price);
        int64_t prev_best_bid = best_bid_tick();
        double prev_qty = bid_qty(price_tick);

        if (qty <= 0.0) {
            // 移除价格
            auto it = std::find(bid_prices_.begin(), bid_prices_.end(), price_tick);
            if (it != bid_prices_.end()) {
                bid_prices_.erase(it);
                bid_quantities_.erase(price_tick);
            }
        } else {
            // 更新或添加价格
            if (prev_qty <= 0.0) {
                // 新价格，需要插入并保持排序
                auto it = std::lower_bound(bid_prices_.begin(), bid_prices_.end(), price_tick, std::greater<int64_t>());
                bid_prices_.insert(it, price_tick);
            }
            bid_quantities_[price_tick] = qty;
        }

        return std::make_tuple(price_tick, prev_best_bid, best_bid_tick(), prev_qty, qty, timestamp);
    }

    std::tuple<int64_t, int64_t, int64_t, double, double, int64_t>
    update_ask_depth(double price, double qty, int64_t timestamp) override {
        int64_t price_tick = price_to_tick(price);
        int64_t prev_best_ask = best_ask_tick();
        double prev_qty = ask_qty(price_tick);

        if (qty <= 0.0) {
            // 移除价格
            auto it = std::find(ask_prices_.begin(), ask_prices_.end(), price_tick);
            if (it != ask_prices_.end()) {
                ask_prices_.erase(it);
                ask_quantities_.erase(price_tick);
            }
        } else {
            // 更新或添加价格
            if (prev_qty <= 0.0) {
                // 新价格，需要插入并保持排序
                auto it = std::lower_bound(ask_prices_.begin(), ask_prices_.end(), price_tick);
                ask_prices_.insert(it, price_tick);
            }
            ask_quantities_[price_tick] = qty;
        }

        return std::make_tuple(price_tick, prev_best_ask, best_ask_tick(), prev_qty, qty, timestamp);
    }

    int64_t price_to_tick(double price) const override {
        return static_cast<int64_t>(price / tick_size_ + 0.5);
    }

    double tick_to_price(int64_t tick) const override {
        return tick * tick_size_;
    }

private:
    double tick_size_;
    double lot_size_;
    std::vector<int64_t> bid_prices_;
    std::vector<int64_t> ask_prices_;
    std::unordered_map<int64_t, double> bid_quantities_;
    std::unordered_map<int64_t, double> ask_quantities_;
};

/**
 * @brief 测试保守队列位置模型
 * 测试场景：
 * 1. 测试新订单初始化
 * 2. 测试深度更新
 * 3. 测试成交更新
 * 4. 测试是否可成交
 */
class RiskAdverseQueueModelTest : public ::testing::Test {
protected:
    void SetUp() override {
        model_ = new risk_adverse_queue_model();
        depth_ = new MockMarketDepth(0.01, 1.0);

        // 创建测试用的买单
        buy_order_.order_id = 1;
        buy_order_.price = 100.0;
        buy_order_.quantity = 10.0;
        buy_order_.leaves_quantity = 10.0;
        buy_order_.side = side_type::buy;
        buy_order_.type = order_type::limit;
        buy_order_.status = order_status::new_order;

        // 创建测试用的卖单
        sell_order_.order_id = 2;
        sell_order_.price = 100.01;
        sell_order_.quantity = 10.0;
        sell_order_.leaves_quantity = 10.0;
        sell_order_.side = side_type::sell;
        sell_order_.type = order_type::limit;
        sell_order_.status = order_status::new_order;
    }

    void TearDown() override {
        delete model_;
        delete depth_;

        // 不需要清理队列数据，因为它现在是结构体的一部分
    }

    risk_adverse_queue_model* model_;
    MockMarketDepth* depth_;
    internal_order buy_order_;
    internal_order sell_order_;
};

// 测试场景1：测试新订单初始化
TEST_F(RiskAdverseQueueModelTest, NewOrder) {
    // 测试买单初始化
    model_->new_order(buy_order_, depth_);

    // 验证前方队列数量正确
    EXPECT_DOUBLE_EQ(100.0, buy_order_.queue_data.front_qty);

    // 测试卖单初始化
    model_->new_order(sell_order_, depth_);

    // 验证前方队列数量正确
    EXPECT_DOUBLE_EQ(100.0, sell_order_.queue_data.front_qty);
}

// 测试场景2：测试深度更新
TEST_F(RiskAdverseQueueModelTest, UpdateDepth) {
    // 初始化订单
    model_->new_order(buy_order_, depth_);

    // 更新深度 - 数量减少
    model_->update_depth(buy_order_, 100.0, 50.0, depth_);

    // 验证前方队列数量已更新
    EXPECT_DOUBLE_EQ(50.0, buy_order_.queue_data.front_qty);

    // 更新深度 - 数量增加
    model_->update_depth(buy_order_, 50.0, 150.0, depth_);

    // 验证前方队列数量不变（保守模型不会增加前方队列数量）
    EXPECT_DOUBLE_EQ(50.0, buy_order_.queue_data.front_qty);
}

// 测试场景3：测试成交更新
TEST_F(RiskAdverseQueueModelTest, Trade) {
    // 初始化订单
    model_->new_order(buy_order_, depth_);

    // 模拟成交
    model_->trade(buy_order_, 30.0, depth_);

    // 验证前方队列数量已减少
    EXPECT_DOUBLE_EQ(70.0, buy_order_.queue_data.front_qty);

    // 再次模拟成交
    model_->trade(buy_order_, 50.0, depth_);

    // 验证前方队列数量已减少
    EXPECT_DOUBLE_EQ(20.0, buy_order_.queue_data.front_qty);

    // 模拟成交超过前方队列数量
    model_->trade(buy_order_, 30.0, depth_);

    // 验证前方队列数量为负数，表示订单可以成交
    EXPECT_DOUBLE_EQ(-10.0, buy_order_.queue_data.front_qty);
}

// 测试场景4：测试是否可成交
TEST_F(RiskAdverseQueueModelTest, IsFilled) {
    // 初始化订单
    model_->new_order(buy_order_, depth_);

    // 初始状态下不可成交
    EXPECT_DOUBLE_EQ(0.0, model_->is_filled(buy_order_, depth_));

    // 模拟成交，但仍不足以使订单成交
    model_->trade(buy_order_, 90.0, depth_);
    EXPECT_DOUBLE_EQ(0.0, model_->is_filled(buy_order_, depth_));

    // 模拟成交，使订单可以成交
    model_->trade(buy_order_, 20.0, depth_);
    EXPECT_DOUBLE_EQ(10.0, model_->is_filled(buy_order_, depth_));
}

/**
 * @brief 测试概率队列模型
 * 测试场景：
 * 1. 测试新订单初始化
 * 2. 测试深度更新
 * 3. 测试成交更新
 * 4. 测试是否可成交
 */
class ProbQueueModelTest : public ::testing::Test {
protected:
    void SetUp() override {
        // 创建概率函数和队列模型
        auto prob = std::make_unique<power_prob_queue_func>(1.0);
        model_ = new prob_queue_model(std::move(prob));
        depth_ = new MockMarketDepth(0.01, 1.0);

        // 创建测试用的买单
        buy_order_.order_id = 1;
        buy_order_.price = 100.0;
        buy_order_.quantity = 10.0;
        buy_order_.leaves_quantity = 10.0;
        buy_order_.side = side_type::buy;
        buy_order_.type = order_type::limit;
        buy_order_.status = order_status::new_order;

        // 创建测试用的卖单
        sell_order_.order_id = 2;
        sell_order_.price = 100.01;
        sell_order_.quantity = 10.0;
        sell_order_.leaves_quantity = 10.0;
        sell_order_.side = side_type::sell;
        sell_order_.type = order_type::limit;
        sell_order_.status = order_status::new_order;
    }

    void TearDown() override {
        delete model_;
        delete depth_;

        // 不需要清理队列数据，因为它现在是结构体的一部分
    }

    prob_queue_model* model_;
    MockMarketDepth* depth_;
    internal_order buy_order_;
    internal_order sell_order_;
};

// 测试场景1：测试新订单初始化
TEST_F(ProbQueueModelTest, NewOrder) {
    // 测试买单初始化
    model_->new_order(buy_order_, depth_);

    // 验证前方队列数量正确
    EXPECT_DOUBLE_EQ(100.0, buy_order_.queue_data.front_qty);
    EXPECT_DOUBLE_EQ(0.0, buy_order_.queue_data.cum_trade_qty);

    // 测试卖单初始化
    model_->new_order(sell_order_, depth_);

    // 验证前方队列数量正确
    EXPECT_DOUBLE_EQ(100.0, sell_order_.queue_data.front_qty);
    EXPECT_DOUBLE_EQ(0.0, sell_order_.queue_data.cum_trade_qty);
}

// 测试场景2：测试成交更新
TEST_F(ProbQueueModelTest, Trade) {
    // 初始化订单
    model_->new_order(buy_order_, depth_);

    // 模拟成交
    model_->trade(buy_order_, 30.0, depth_);

    // 验证前方队列数量已减少，累计成交数量已增加
    EXPECT_DOUBLE_EQ(70.0, buy_order_.queue_data.front_qty);
    EXPECT_DOUBLE_EQ(30.0, buy_order_.queue_data.cum_trade_qty);

    // 再次模拟成交
    model_->trade(buy_order_, 50.0, depth_);

    // 验证前方队列数量已减少，累计成交数量已增加
    EXPECT_DOUBLE_EQ(20.0, buy_order_.queue_data.front_qty);
    EXPECT_DOUBLE_EQ(80.0, buy_order_.queue_data.cum_trade_qty);
}

// 测试场景3：测试深度更新
TEST_F(ProbQueueModelTest, UpdateDepth) {
    // 初始化订单
    model_->new_order(buy_order_, depth_);

    // 模拟成交
    model_->trade(buy_order_, 30.0, depth_);

    // 更新深度 - 数量减少
    model_->update_depth(buy_order_, 100.0, 50.0, depth_);

    // 验证前方队列数量已更新，累计成交数量已重置
    // 前方队列数量应该减少，但具体值取决于概率函数的实现
    EXPECT_LE(buy_order_.queue_data.front_qty, 50.0);
    EXPECT_DOUBLE_EQ(0.0, buy_order_.queue_data.cum_trade_qty);

    // 更新深度 - 数量增加
    double prev_front_qty = buy_order_.queue_data.front_qty;
    model_->update_depth(buy_order_, 50.0, 150.0, depth_);

    // 验证前方队列数量不变或减少（不会增加）
    EXPECT_LE(buy_order_.queue_data.front_qty, prev_front_qty);
}

// 测试场景4：测试是否可成交
TEST_F(ProbQueueModelTest, IsFilled) {
    // 初始化订单
    model_->new_order(buy_order_, depth_);

    // 初始状态下不可成交
    EXPECT_DOUBLE_EQ(0.0, model_->is_filled(buy_order_, depth_));

    // 模拟成交，但仍不足以使订单成交
    model_->trade(buy_order_, 90.0, depth_);
    EXPECT_DOUBLE_EQ(0.0, model_->is_filled(buy_order_, depth_));

    // 模拟成交，使订单可以成交
    model_->trade(buy_order_, 20.0, depth_);
    EXPECT_DOUBLE_EQ(10.0, model_->is_filled(buy_order_, depth_));
}
