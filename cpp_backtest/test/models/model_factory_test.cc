#include <gtest/gtest.h>
#include <fast_trader_elite/cpp_backtest/models/model_factory.h>

using namespace fast_trader_elite::cpp_backtest;

/**
 * @brief 测试模型工厂类
 * 测试场景：
 * 1. 测试延迟模型工厂
 * 2. 测试队列模型工厂
 * 3. 测试手续费模型工厂
 */
class ModelFactoryTest : public ::testing::Test {
protected:
    void SetUp() override {
        // 设置测试参数
        order_latency_ = 100;
        response_latency_ = 200;
        md_latency_ = 50;
        queue_power_ = 2.0;
        maker_fee_ = 0.0002;
        taker_fee_ = 0.0004;
    }

    int64_t order_latency_;
    int64_t response_latency_;
    int64_t md_latency_;
    double queue_power_;
    double maker_fee_;
    double taker_fee_;
};

// 测试场景1：测试延迟模型工厂
TEST_F(ModelFactoryTest, LatencyModelFactory) {
    // 测试创建常数延迟模型
    auto model = latency_model_factory::create("constant", order_latency_, response_latency_, md_latency_);
    ASSERT_NE(nullptr, model);
    
    // 验证模型行为
    internal_order test_order;
    test_order.order_id = 1;
    
    EXPECT_EQ(order_latency_, model->entry(0, test_order));
    EXPECT_EQ(response_latency_, model->response(0, test_order));
    EXPECT_EQ(md_latency_, model->market(0));
    
    // 测试空类型参数（应该默认为常数延迟模型）
    auto default_model = latency_model_factory::create("", order_latency_, response_latency_, md_latency_);
    ASSERT_NE(nullptr, default_model);
    
    EXPECT_EQ(order_latency_, default_model->entry(0, test_order));
    EXPECT_EQ(response_latency_, default_model->response(0, test_order));
    EXPECT_EQ(md_latency_, default_model->market(0));
    
    // 测试未知类型参数（应该默认为常数延迟模型）
    auto unknown_model = latency_model_factory::create("unknown", order_latency_, response_latency_, md_latency_);
    ASSERT_NE(nullptr, unknown_model);
    
    EXPECT_EQ(order_latency_, unknown_model->entry(0, test_order));
    EXPECT_EQ(response_latency_, unknown_model->response(0, test_order));
    EXPECT_EQ(md_latency_, unknown_model->market(0));
}

// 测试场景2：测试队列模型工厂
TEST_F(ModelFactoryTest, QueueModelFactory) {
    // 测试创建简单队列模型
    auto simple_model = queue_model_factory::create("simple", queue_power_);
    ASSERT_NE(nullptr, simple_model);
    
    // 测试创建概率队列模型
    auto prob_model = queue_model_factory::create("prob", queue_power_);
    ASSERT_NE(nullptr, prob_model);
    
    // 测试创建对数队列模型
    auto log_model = queue_model_factory::create("log", queue_power_);
    ASSERT_NE(nullptr, log_model);
    
    // 测试空类型参数（应该默认为简单队列模型）
    auto default_model = queue_model_factory::create("", queue_power_);
    ASSERT_NE(nullptr, default_model);
    
    // 测试未知类型参数（应该默认为简单队列模型）
    auto unknown_model = queue_model_factory::create("unknown", queue_power_);
    ASSERT_NE(nullptr, unknown_model);
}

// 测试场景3：测试手续费模型工厂
TEST_F(ModelFactoryTest, FeeModelFactory) {
    // 测试创建固定手续费模型
    auto model = fee_model_factory::create("fixed_fee", maker_fee_, taker_fee_);
    ASSERT_NE(nullptr, model);
    
    // 验证模型行为
    double price = 100.0;
    double quantity = 10.0;
    
    EXPECT_DOUBLE_EQ(price * quantity * maker_fee_, model->calculate_fee(price, quantity, true));
    EXPECT_DOUBLE_EQ(price * quantity * taker_fee_, model->calculate_fee(price, quantity, false));
    
    // 测试空类型参数（应该默认为固定手续费模型）
    auto default_model = fee_model_factory::create("", maker_fee_, taker_fee_);
    ASSERT_NE(nullptr, default_model);
    
    EXPECT_DOUBLE_EQ(price * quantity * maker_fee_, default_model->calculate_fee(price, quantity, true));
    EXPECT_DOUBLE_EQ(price * quantity * taker_fee_, default_model->calculate_fee(price, quantity, false));
    
    // 测试未知类型参数（应该默认为固定手续费模型）
    auto unknown_model = fee_model_factory::create("unknown", maker_fee_, taker_fee_);
    ASSERT_NE(nullptr, unknown_model);
    
    EXPECT_DOUBLE_EQ(price * quantity * maker_fee_, unknown_model->calculate_fee(price, quantity, true));
    EXPECT_DOUBLE_EQ(price * quantity * taker_fee_, unknown_model->calculate_fee(price, quantity, false));
}
