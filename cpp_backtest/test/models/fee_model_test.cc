#include <gtest/gtest.h>
#include <fast_trader_elite/cpp_backtest/models/fee_model.h>

using namespace fast_trader_elite::cpp_backtest;

/**
 * @brief 测试固定费率模型
 * 测试场景：
 * 1. 测试做市商费率计算
 * 2. 测试吃单方费率计算
 * 3. 测试零费率
 * 4. 测试负费率（返佣）
 * 5. 测试边界条件（零价格、零数量）
 */
class FixedFeeModelTest : public ::testing::Test {
protected:
    void SetUp() override {
        // 创建不同费率的模型用于测试
        standard_model_ = new fixed_fee_model(0.001, 0.002);  // 标准费率：0.1% 做市商，0.2% 吃单方
        zero_model_ = new fixed_fee_model(0.0, 0.0);          // 零费率
        rebate_model_ = new fixed_fee_model(-0.0001, 0.001);  // 做市商返佣 0.01%，吃单方 0.1%
    }

    void TearDown() override {
        delete standard_model_;
        delete zero_model_;
        delete rebate_model_;
    }

    fixed_fee_model* standard_model_;
    fixed_fee_model* zero_model_;
    fixed_fee_model* rebate_model_;
};

// 测试场景1：测试做市商费率计算
TEST_F(FixedFeeModelTest, MakerFeeCalculation) {
    // 测试标准费率
    double price = 100.0;
    double quantity = 10.0;
    bool is_maker = true;
    
    double expected_fee = price * quantity * 0.001;  // 100 * 10 * 0.001 = 1.0
    double actual_fee = standard_model_->calculate_fee(price, quantity, is_maker);
    
    EXPECT_DOUBLE_EQ(expected_fee, actual_fee);
}

// 测试场景2：测试吃单方费率计算
TEST_F(FixedFeeModelTest, TakerFeeCalculation) {
    double price = 100.0;
    double quantity = 10.0;
    bool is_maker = false;
    
    double expected_fee = price * quantity * 0.002;  // 100 * 10 * 0.002 = 2.0
    double actual_fee = standard_model_->calculate_fee(price, quantity, is_maker);
    
    EXPECT_DOUBLE_EQ(expected_fee, actual_fee);
}

// 测试场景3：测试零费率
TEST_F(FixedFeeModelTest, ZeroFeeCalculation) {
    double price = 100.0;
    double quantity = 10.0;
    
    // 测试做市商零费率
    EXPECT_DOUBLE_EQ(0.0, zero_model_->calculate_fee(price, quantity, true));
    
    // 测试吃单方零费率
    EXPECT_DOUBLE_EQ(0.0, zero_model_->calculate_fee(price, quantity, false));
}

// 测试场景4：测试负费率（返佣）
TEST_F(FixedFeeModelTest, RebateFeeCalculation) {
    double price = 100.0;
    double quantity = 10.0;
    
    // 测试做市商返佣
    double expected_rebate = price * quantity * (-0.0001);  // 100 * 10 * (-0.0001) = -0.1
    double actual_rebate = rebate_model_->calculate_fee(price, quantity, true);
    
    EXPECT_DOUBLE_EQ(expected_rebate, actual_rebate);
    
    // 测试吃单方正常费率
    double expected_fee = price * quantity * 0.001;  // 100 * 10 * 0.001 = 1.0
    double actual_fee = rebate_model_->calculate_fee(price, quantity, false);
    
    EXPECT_DOUBLE_EQ(expected_fee, actual_fee);
}

// 测试场景5：测试边界条件
TEST_F(FixedFeeModelTest, EdgeCases) {
    // 测试零价格
    EXPECT_DOUBLE_EQ(0.0, standard_model_->calculate_fee(0.0, 10.0, true));
    
    // 测试零数量
    EXPECT_DOUBLE_EQ(0.0, standard_model_->calculate_fee(100.0, 0.0, true));
    
    // 测试零价格和零数量
    EXPECT_DOUBLE_EQ(0.0, standard_model_->calculate_fee(0.0, 0.0, true));
    
    // 测试极大值
    double large_price = 1000000.0;
    double large_quantity = 1000000.0;
    double expected_large_fee = large_price * large_quantity * 0.001;
    
    EXPECT_DOUBLE_EQ(expected_large_fee, standard_model_->calculate_fee(large_price, large_quantity, true));
}
