cmake_minimum_required(VERSION 3.10)
project(fast_trader_elite_backtest)

set(CMAKE_CXX_STANDARD 20)
set(CMAKE_CXX_STANDARD_REQUIRED ON)
set(CMAKE_CXX_EXTENSIONS OFF)
set(CMAKE_EXPORT_COMPILE_COMMANDS ON)

# 设置输出目录
set(CMAKE_RUNTIME_OUTPUT_DIRECTORY ${CMAKE_BINARY_DIR}/bin)
set(CMAKE_LIBRARY_OUTPUT_DIRECTORY ${CMAKE_BINARY_DIR}/lib)
set(CMAKE_ARCHIVE_OUTPUT_DIRECTORY ${CMAKE_BINARY_DIR}/lib)
add_compile_options(-Wall -O3 -march=native -msse4 -g)

# 添加预处理器定义，用于条件编译
add_definitions(-DPERFORMANCE_MODE)

# 启用测试
enable_testing()

# 查找 nlohmann_json 库
# 直接包含头文件，而不是使用 find_package
# find_package(nlohmann_json REQUIRED)

# 添加include目录
include_directories(
    ${CMAKE_CURRENT_SOURCE_DIR}/include
    ${CMAKE_CURRENT_SOURCE_DIR}/..
    ${CMAKE_CURRENT_SOURCE_DIR}/../fast_trader_elite/data_model/include
    ${CMAKE_CURRENT_SOURCE_DIR}/../fast_trader_elite/api
    ${CMAKE_CURRENT_SOURCE_DIR}/../cpp_frame/include
    ${CMAKE_CURRENT_SOURCE_DIR}/../cpp_frame/thirdparty
)

# 查找源文件
file(GLOB_RECURSE SOURCES
    "src/*.cc"
)

# 手动添加源文件
list(APPEND SOURCES
    "${CMAKE_CURRENT_SOURCE_DIR}/src/state.cc"
    "${CMAKE_CURRENT_SOURCE_DIR}/src/backtest_data.cc"
    "${CMAKE_CURRENT_SOURCE_DIR}/src/position_cycle.cc"
    "${CMAKE_CURRENT_SOURCE_DIR}/src/export/csv_exporter.cc"
    "${CMAKE_CURRENT_SOURCE_DIR}/src/export/npz_exporter.cc"
    "${CMAKE_CURRENT_SOURCE_DIR}/src/export/exporter_factory.cc"
    "${CMAKE_CURRENT_SOURCE_DIR}/src/export/export_methods.cc"
)

# 查找 simdjson 库
find_library(SIMDJSON_LIBRARY simdjson)
if(NOT SIMDJSON_LIBRARY)
    message(STATUS "simdjson library not found, using source from bybit_depth_data_coverter")
    file(GLOB SIMDJSON_SOURCES
        "${CMAKE_CURRENT_SOURCE_DIR}/../fast_trader_elite/bybit_depth_data_coverter/simdjson.cpp"
    )
    add_library(simdjson STATIC ${SIMDJSON_SOURCES})
    set(SIMDJSON_LIBRARY simdjson)
endif()

# 查找 fmt 库
find_package(fmt REQUIRED)

# 创建库
add_library(fast_trader_elite_backtest STATIC ${SOURCES})
target_link_libraries(fast_trader_elite_backtest PRIVATE pthread dl ${SIMDJSON_LIBRARY} simdjson fmt::fmt)

# 创建示例
add_executable(interface_validation_strategy examples/interface_validation_strategy.cc)
target_include_directories(interface_validation_strategy PRIVATE ${CMAKE_CURRENT_SOURCE_DIR}/../fast_trader_elite/api)
target_link_libraries(interface_validation_strategy PRIVATE fast_trader_elite_backtest)

# 创建统一回测引擎示例
add_executable(backtest_demo examples/backtest_demo.cc)
target_link_libraries(backtest_demo PRIVATE fast_trader_elite_backtest)



# 创建基于配置文件的回测器
add_executable(config_backtest src/main.cc)
target_link_libraries(config_backtest PRIVATE fast_trader_elite_backtest dl)

# 创建工具
add_executable(generate_realistic_data tools/generate_realistic_data.cc)
target_link_libraries(generate_realistic_data PRIVATE fast_trader_elite_backtest)

add_executable(preprocess_data tools/preprocess_data.cc)
target_link_libraries(preprocess_data PRIVATE fast_trader_elite_backtest ${SIMDJSON_LIBRARY})


# 安装目标
install(TARGETS fast_trader_elite_backtest backtest_demo interface_validation_strategy generate_realistic_data preprocess_data
    RUNTIME DESTINATION bin
    LIBRARY DESTINATION lib
    ARCHIVE DESTINATION lib
)

# 添加测试目录
add_subdirectory(test)

# 添加参数优化器库
add_library(parameter_optimizer
    src/parameter_optimizer/optimizer.cc
    src/parameter_optimizer/parameter_space.cc
    src/parameter_optimizer/optimization_target.cc
    src/parameter_optimizer/metric_calculator.cc
    src/parameter_optimizer/parameter_config.cc
    src/parameter_optimizer/result_reporter.cc
    src/parameter_optimizer/optimization_controller.cc
    src/parameter_optimizer/grid_search_optimizer.cc
)

# 设置包含目录
target_include_directories(parameter_optimizer PUBLIC
    ${CMAKE_CURRENT_SOURCE_DIR}/include
)

# 链接依赖库
target_link_libraries(parameter_optimizer PUBLIC
    fast_trader_elite_backtest
)

# 添加参数优化器命令行工具
add_executable(parameter_optimizer_cli
    tools/parameter_optimizer_cli.cc
)

# 链接依赖库
target_link_libraries(parameter_optimizer_cli
    parameter_optimizer
)

# 安装参数优化器
install(TARGETS parameter_optimizer parameter_optimizer_cli
    RUNTIME DESTINATION bin
    LIBRARY DESTINATION lib
    ARCHIVE DESTINATION lib
)

# 安装参数优化器配置文件
install(DIRECTORY config/parameter_optimization/
    DESTINATION config/parameter_optimization
    FILES_MATCHING PATTERN "*.json"
)
