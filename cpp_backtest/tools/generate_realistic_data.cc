#include <iostream>
#include <fstream>
#include <vector>
#include <random>
#include <cstring>
#include <ctime>
#include <cmath>
#include <algorithm>
#include <map>
#include <memory>
#include <fast_trader_elite/cpp_backtest/types.h>

using namespace fast_trader_elite::cpp_backtest;

// 市场模拟器类，用于生成更真实的市场数据
class MarketSimulator {
public:
    // 构造函数，初始化市场参数
    MarketSimulator(
        double initial_price,
        double volatility,
        double mean_reversion,
        double trend,
        double min_spread,
        double max_spread,
        double volume_mean,
        double volume_std,
        double trade_intensity
    ) : initial_price_(initial_price),
        current_price_(initial_price),
        volatility_(volatility),
        mean_reversion_(mean_reversion),
        trend_(trend),
        min_spread_(min_spread),
        max_spread_(max_spread),
        volume_mean_(volume_mean),
        volume_std_(volume_std),
        trade_intensity_(trade_intensity),
        rd_(),
        gen_(rd_()),
        normal_dist_(0.0, 1.0),
        trade_size_dist_(volume_mean, volume_std),
        trade_dir_dist_(0, 1),
        trade_prob_dist_(0.0, 1.0) {

        // 初始化买卖盘
        for (int i = 0; i < 10; ++i) {
            bid_levels_[current_price_ - (i + 1) * min_spread_] = volume_mean_ * (1.0 - 0.05 * i);
            ask_levels_[current_price_ + (i + 1) * min_spread_] = volume_mean_ * (1.0 - 0.05 * i);
        }
    }

    // 更新市场状态
    void update(int64_t timestamp) {
        // 生成随机价格变动
        double price_change = volatility_ * normal_dist_(gen_);

        // 添加趋势和均值回归
        price_change += trend_ - mean_reversion_ * (current_price_ - initial_price_);

        // 更新当前价格
        current_price_ += price_change;

        // 确保价格为正
        current_price_ = std::max(current_price_, 0.1);

        // 更新买卖盘
        update_order_book();

        // 更新最新成交价格
        last_trade_price_ = current_price_ + normal_dist_(gen_) * min_spread_ * 0.5;

        // 更新时间戳
        current_timestamp_ = timestamp;
    }

    // 生成深度行情数据
    fast_trader_elite::depth_market_data_field generate_depth(uint16_t instrument_idx, const std::string& instrument_id, fast_trader_elite::exchange_type exchange) {
        fast_trader_elite::depth_market_data_field depth;
        std::memset(&depth, 0, sizeof(depth));

        // 设置基本信息
        depth.instrument_idx = instrument_idx;
        std::strcpy(depth.instrument_id, instrument_id.c_str());
        depth.exchange_id = exchange;
        depth.ins_type = fast_trader_elite::instrument_type::PerpetualFuture;
        depth.depth = 10;
        depth.exchange_timestamp = current_timestamp_;
        depth.local_timestamp = current_timestamp_ + 100000; // 100微秒延迟

        // 填充买盘
        int i = 0;
        for (auto it = bid_levels_.rbegin(); it != bid_levels_.rend() && i < 10; ++it, ++i) {
            depth.bid_price[i] = it->first;
            depth.bid_volume[i] = it->second;
        }

        // 填充卖盘
        i = 0;
        for (auto it = ask_levels_.begin(); it != ask_levels_.end() && i < 10; ++it, ++i) {
            depth.ask_price[i] = it->first;
            depth.ask_volume[i] = it->second;
        }

        return depth;
    }

    // 生成成交数据
    fast_trader_elite::transaction_field generate_transaction(uint16_t instrument_idx, const std::string& instrument_id, fast_trader_elite::exchange_type exchange) {
        fast_trader_elite::transaction_field trans;
        std::memset(&trans, 0, sizeof(trans));

        // 设置基本信息
        trans.instrument_idx = instrument_idx;
        std::strcpy(trans.instrument_id, instrument_id.c_str());
        trans.exchange_id = exchange;
        trans.ins_type = fast_trader_elite::instrument_type::PerpetualFuture;

        // 生成成交价格和数量
        trans.price = last_trade_price_;
        trans.volume = std::max(0.001, trade_size_dist_(gen_));
        trans.trade_cnt = 1;
        trans.is_maker = trade_dir_dist_(gen_) % 2 == 0; // 随机决定是否是maker
        trans.exchange_timestamp = current_timestamp_;
        trans.local_timestamp = current_timestamp_ + 100000; // 100微秒延迟

        return trans;
    }

    // 判断是否应该生成成交
    bool should_generate_trade() {
        return trade_prob_dist_(gen_) < trade_intensity_;
    }

    // 获取当前价格
    double current_price() const {
        return current_price_;
    }

    // 获取当前买一价
    double best_bid() const {
        if (!bid_levels_.empty()) {
            return bid_levels_.rbegin()->first;
        }
        return current_price_ - min_spread_;
    }

    // 获取当前卖一价
    double best_ask() const {
        if (!ask_levels_.empty()) {
            return ask_levels_.begin()->first;
        }
        return current_price_ + min_spread_;
    }

private:
    // 更新订单簿
    void update_order_book() {
        // 清空当前订单簿
        bid_levels_.clear();
        ask_levels_.clear();

        // 计算当前价差
        double spread = min_spread_ + (max_spread_ - min_spread_) * std::abs(normal_dist_(gen_));
        spread = std::max(min_spread_, spread);

        // 生成买卖盘
        double mid_price = current_price_;
        double best_bid = mid_price - spread / 2.0;
        double best_ask = mid_price + spread / 2.0;

        // 确保价格符合最小变动单位
        best_bid = std::floor(best_bid / 0.1) * 0.1;
        best_ask = std::ceil(best_ask / 0.1) * 0.1;

        // 生成买盘
        for (int i = 0; i < 10; ++i) {
            double price = best_bid - i * 0.1;
            double volume = volume_mean_ * (1.0 + normal_dist_(gen_) * 0.3) * std::exp(-0.2 * i);
            volume = std::max(0.001, volume); // 确保数量为正
            bid_levels_[price] = volume;
        }

        // 生成卖盘
        for (int i = 0; i < 10; ++i) {
            double price = best_ask + i * 0.1;
            double volume = volume_mean_ * (1.0 + normal_dist_(gen_) * 0.3) * std::exp(-0.2 * i);
            volume = std::max(0.001, volume); // 确保数量为正
            ask_levels_[price] = volume;
        }
    }

private:
    double initial_price_;    // 初始价格
    double current_price_;    // 当前价格
    double volatility_;       // 波动率
    double mean_reversion_;   // 均值回归系数
    double trend_;            // 趋势系数
    double min_spread_;       // 最小价差
    double max_spread_;       // 最大价差
    double volume_mean_;      // 平均交易量
    double volume_std_;       // 交易量标准差
    double trade_intensity_;  // 成交频率
    double last_trade_price_; // 最新成交价
    int64_t current_timestamp_; // 当前时间戳

    // 随机数生成器
    std::random_device rd_;
    std::mt19937 gen_;
    std::normal_distribution<> normal_dist_;
    std::normal_distribution<> trade_size_dist_;
    std::uniform_int_distribution<> trade_dir_dist_;
    std::uniform_real_distribution<> trade_prob_dist_;

    // 买卖盘
    std::map<double, double> bid_levels_; // 价格 -> 数量
    std::map<double, double> ask_levels_; // 价格 -> 数量
};

int main(int argc, char** argv) {
    if (argc < 2) {
        std::cerr << "Usage: " << argv[0] << " <output_file>" << std::endl;
        return 1;
    }

    std::string output_file = argv[1];

    // 打开输出文件
    std::ofstream out(output_file, std::ios::binary);
    if (!out) {
        std::cerr << "Failed to open output file: " << output_file << std::endl;
        return 1;
    }

    // 创建市场模拟器
    // 参数: 初始价格, 波动率, 均值回归系数, 趋势系数, 最小价差, 最大价差, 平均交易量, 交易量标准差, 成交频率
    MarketSimulator market_simulator(
        50000.0,  // 初始价格
        5.0,      // 波动率
        0.01,     // 均值回归系数
        0.05,     // 趋势系数
        0.1,      // 最小价差
        1.0,      // 最大价差
        5.0,      // 平均交易量
        2.0,      // 交易量标准差
        0.3       // 成交频率
    );

    // 创建文件头
    file_header header;
    std::memset(&header, 0, sizeof(header));
    std::strcpy(header.magic, "FTEBACK");
    header.version = 1;
    header.record_count = 10000; // 记录数量
    header.start_time = static_cast<int64_t>(std::time(nullptr)) * 1000000000;
    header.end_time = header.start_time + static_cast<int64_t>(10000) * 1000000000; // 10000秒 = 约 2.8 小时
    header.instrument_count = 1;
    std::cout<<"start_t:"<<header.start_time<<std::endl;
    std::cout<<"end_t:"<<header.end_time<<std::endl;

    // 写入文件头
    out.write(reinterpret_cast<const char*>(&header), sizeof(header));

    // 创建品种信息
    instrument_info info;
    std::memset(&info, 0, sizeof(info));
    std::strcpy(info.instrument_id, "BTCUSDT");
    info.instrument_idx = 0;
    info.exchange_id = static_cast<uint8_t>(fast_trader_elite::exchange_type::BYBIT);
    info.tick_size = 0.1;
    info.step_size = 0.001;

    // 写入品种信息
    out.write(reinterpret_cast<const char*>(&info), sizeof(info));

    // 生成记录
    uint32_t total_records = 0;
    uint32_t depth_records = 0;
    uint32_t trans_records = 0;

    // 记录价格范围
    double min_price = std::numeric_limits<double>::max();
    double max_price = std::numeric_limits<double>::min();
    double min_bid = std::numeric_limits<double>::max();
    double max_bid = std::numeric_limits<double>::min();
    double min_ask = std::numeric_limits<double>::max();
    double max_ask = std::numeric_limits<double>::min();

    std::cout << "\n====== 开始生成测试数据 ======" << std::endl;
    std::cout << "初始价格: 50000.0" << std::endl;
    std::cout << "波动率: 5.0" << std::endl;
    std::cout << "均值回归系数: 0.01" << std::endl;
    std::cout << "趋势系数: 0.05" << std::endl;
    std::cout << "成交频率: 0.3" << std::endl;

    for (uint32_t i = 0; i < header.record_count && total_records < header.record_count; ++i) {
        // 计算时间戳
        int64_t timestamp = header.start_time + i * 1000000000; // 1秒间隔

        // 更新市场状态
        market_simulator.update(timestamp);

        // 生成深度行情数据
        fast_trader_elite::depth_market_data_field depth =
            market_simulator.generate_depth(0, "BTCUSDT", fast_trader_elite::exchange_type::BYBIT);

        // 记录价格范围
        min_bid = std::min(min_bid, depth.bid_price[0]);
        max_bid = std::max(max_bid, depth.bid_price[0]);
        min_ask = std::min(min_ask, depth.ask_price[0]);
        max_ask = std::max(max_ask, depth.ask_price[0]);

        // 每100条记录输出一次当前价格
        if (i % 100 == 0) {
            std::cout << "[时间戳: " << timestamp << "] "
                      << "Bid/Ask: " << depth.bid_price[0] << "/" << depth.ask_price[0]
                      << " Spread: " << (depth.ask_price[0] - depth.bid_price[0])
                      << " Mid: " << (depth.bid_price[0] + depth.ask_price[0]) / 2.0
                      << std::endl;
        }

        // 创建记录头
        record_header rec_header;
        std::memset(&rec_header, 0, sizeof(rec_header));
        rec_header.record_type = DEPTH_MARKET_DATA;
        rec_header.flags = LOCAL_EVENT | EXCH_EVENT;
        rec_header.instrument_idx = 0;
        rec_header.exch_timestamp = timestamp;
        rec_header.local_timestamp = timestamp + 100000;
        rec_header.data_size = sizeof(depth);

        // 写入记录头和数据
        out.write(reinterpret_cast<const char*>(&rec_header), sizeof(rec_header));
        out.write(reinterpret_cast<const char*>(&depth), sizeof(depth));
        total_records++;
        depth_records++;

        // 生成成交数据
        if (market_simulator.should_generate_trade()) {
            fast_trader_elite::transaction_field trans =
                market_simulator.generate_transaction(0, "BTCUSDT", fast_trader_elite::exchange_type::BYBIT);

            // 记录成交价格范围
            min_price = std::min(min_price, trans.price);
            max_price = std::max(max_price, trans.price);

            // 每10条成交输出一次成交信息
            if (trans_records % 10 == 0) {
                std::cout << "[成交] 价格: " << trans.price
                          << " 数量: " << trans.volume
                          << " Maker: " << (trans.is_maker ? "true" : "false")
                          << std::endl;
            }

            // 创建记录头
            record_header trans_header;
            std::memset(&trans_header, 0, sizeof(trans_header));
            trans_header.record_type = TRANSACTION_DATA;
            trans_header.flags = LOCAL_EVENT | EXCH_EVENT;
            trans_header.instrument_idx = 0;
            trans_header.exch_timestamp = timestamp;
            trans_header.local_timestamp = timestamp + 100000;
            trans_header.data_size = sizeof(trans);

            // 写入记录头和数据
            out.write(reinterpret_cast<const char*>(&trans_header), sizeof(trans_header));
            out.write(reinterpret_cast<const char*>(&trans), sizeof(trans));
            total_records++;
            trans_records++;
        }

        // 显示进度
        if (i % 1000 == 0) {
            std::cout << "Generated " << i << " time steps, " << total_records << " records..." << std::endl;
        }
    }

    out.close();

    std::cout << "\n====== 测试数据生成完成 ======" << std::endl;
    std::cout << "生成文件: " << output_file << std::endl;
    std::cout << "总记录数: " << total_records << std::endl;
    std::cout << "深度行情记录数: " << depth_records << std::endl;
    std::cout << "成交记录数: " << trans_records << std::endl;
    std::cout << "价格范围统计:" << std::endl;
    std::cout << "  最低买一价: " << min_bid << std::endl;
    std::cout << "  最高买一价: " << max_bid << std::endl;
    std::cout << "  最低卖一价: " << min_ask << std::endl;
    std::cout << "  最高卖一价: " << max_ask << std::endl;
    std::cout << "  最低成交价: " << min_price << std::endl;
    std::cout << "  最高成交价: " << max_price << std::endl;
    std::cout << "  价格波动范围: " << (max_price - min_price) << std::endl;
    std::cout << "  平均价差: " << ((max_ask - min_bid) / 2.0) << std::endl;

    return 0;
}
