#include <fast_trader_elite/cpp_backtest/parameter_optimizer/optimization_controller.h>
#include <iostream>
#include <string>

namespace fast_trader_elite {
namespace cpp_backtest {

// 参数优化器命令行工具
class parameter_optimizer_cli {
public:
    parameter_optimizer_cli() = default;

    // 运行优化
    int run(int argc, char** argv) {
        // 解析命令行参数
        if (!parse_args(argc, argv)) {
            return 1;
        }

        // 创建优化控制器
        optimization_controller controller;

        // 设置进度回调函数
        controller.set_progress_callback([](int current, int total, const optimization_result& result) {
            std::cout << "Progress: " << current << "/" << total
                      << " (Score: " << result.score << ")" << std::endl;
        });

        // 加载配置
        if (!controller.load_config(config_path_)) {
            std::cerr << "Failed to load config: " << config_path_ << std::endl;
            return 1;
        }

        // 运行优化
        if (!controller.run()) {
            std::cerr << "Failed to run optimization" << std::endl;
            return 1;
        }

        // 输出最佳结果
        auto best_result = controller.get_best_result();
        if (best_result.valid) {
            std::cout << "\nBest result:" << std::endl;
            std::cout << "Score: " << best_result.score << std::endl;

            std::cout << "Parameters:" << std::endl;
            for (const auto& [name, value] : best_result.parameters) {
                std::cout << "  " << name << ": ";
                if (std::holds_alternative<int>(value)) {
                    std::cout << std::get<int>(value);
                } else if (std::holds_alternative<double>(value)) {
                    std::cout << std::get<double>(value);
                } else if (std::holds_alternative<bool>(value)) {
                    std::cout << (std::get<bool>(value) ? "true" : "false");
                } else if (std::holds_alternative<std::string>(value)) {
                    std::cout << std::get<std::string>(value);
                }
                std::cout << std::endl;
            }

            std::cout << "Metrics:" << std::endl;
            for (const auto& [name, value] : best_result.metrics) {
                std::cout << "  " << name << ": " << value << std::endl;
            }
        } else {
            std::cerr << "No valid results found" << std::endl;
        }

        return 0;
    }

private:
    // 命令行参数
    std::string config_path_;

    // 解析命令行参数
    bool parse_args(int argc, char** argv) {
        if (argc < 2) {
            std::cerr << "Usage: " << argv[0] << " --config=<config_path>" << std::endl;
            return false;
        }

        for (int i = 1; i < argc; ++i) {
            std::string arg = argv[i];

            if (arg.substr(0, 9) == "--config=") {
                config_path_ = arg.substr(9);
            } else {
                std::cerr << "Unknown argument: " << arg << std::endl;
                return false;
            }
        }

        if (config_path_.empty()) {
            std::cerr << "Config path is required" << std::endl;
            return false;
        }

        return true;
    }
};

} // namespace cpp_backtest
} // namespace fast_trader_elite

int main(int argc, char** argv) {
    fast_trader_elite::cpp_backtest::parameter_optimizer_cli optimizer;
    return optimizer.run(argc, argv);
}
