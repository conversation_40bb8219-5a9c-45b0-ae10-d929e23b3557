#!/bin/bash
# 根据优化结果生成配置文件的脚本

# 默认值
RESULTS_CSV="./output/optimization_results/results.csv"
OUTPUT_DIR="./output/generated_configs"
BASE_CONFIG="../examples/market_making_config.json"
METRICS="score,win_rate,return,sharpe_ratio,avg_position_time"

# 显示帮助信息
show_help() {
    echo "用法: $0 [选项]"
    echo
    echo "选项:"
    echo "  -h, --help                显示此帮助信息"
    echo "  -r, --results CSV_PATH    指定结果CSV文件路径 (默认: $RESULTS_CSV)"
    echo "  -o, --output DIR          指定输出目录 (默认: $OUTPUT_DIR)"
    echo "  -b, --base CONFIG_PATH    指定基础配置文件路径 (默认: $BASE_CONFIG)"
    echo "  -m, --metrics METRICS     指定要显示的指标，用逗号分隔 (默认: $METRICS)"
    echo
    echo "示例:"
    echo "  $0 -r ./output/my_results.csv -o ./my_configs"
    echo "  $0 -m score,win_rate,return,total_trades"
    exit 0
}

# 解析命令行参数
while [[ $# -gt 0 ]]; do
    case $1 in
        -h|--help)
            show_help
            ;;
        -r|--results)
            RESULTS_CSV="$2"
            shift 2
            ;;
        -o|--output)
            OUTPUT_DIR="$2"
            shift 2
            ;;
        -b|--base)
            BASE_CONFIG="$2"
            shift 2
            ;;
        -m|--metrics)
            METRICS="$2"
            shift 2
            ;;
        *)
            echo "未知选项: $1"
            show_help
            ;;
    esac
done

# 检查结果CSV文件是否存在
if [ ! -f "$RESULTS_CSV" ]; then
    echo "错误: 结果CSV文件不存在: $RESULTS_CSV"
    exit 1
fi

# 创建输出目录
mkdir -p "$OUTPUT_DIR"

# 列出所有结果
echo "列出优化结果..."
python3 $(dirname "$0")/list_optimization_results.py "$RESULTS_CSV" "$METRICS"

# 提示用户选择行
echo
echo -n "请输入要使用的行号 (1-N): "
read ROW_NUMBER

# 提示用户输入配置文件名
echo -n "请输入配置文件名 (不包括路径和扩展名): "
read CONFIG_NAME

# 如果用户没有输入配置文件名，使用默认名称
if [ -z "$CONFIG_NAME" ]; then
    CONFIG_NAME="config_from_row_${ROW_NUMBER}"
fi

# 生成完整的输出路径
OUTPUT_PATH="${OUTPUT_DIR}/${CONFIG_NAME}.json"

# 生成配置文件
echo "生成配置文件..."
python3 $(dirname "$0")/generate_config_from_result.py "$RESULTS_CSV" "$ROW_NUMBER" "$OUTPUT_PATH" "$BASE_CONFIG"

# 如果生成成功，显示成功信息
if [ $? -eq 0 ]; then
    echo
    echo "配置文件已成功生成: $OUTPUT_PATH"
    echo
    echo "要使用此配置文件运行回测，请执行:"
    echo "  cd $(dirname "$OUTPUT_DIR")"
    echo "  ./backtest_engine $(basename "$OUTPUT_PATH")"
fi
