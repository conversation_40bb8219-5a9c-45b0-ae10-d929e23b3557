#!/usr/bin/env python3
"""
列出优化结果CSV文件中的所有结果。
用法：
    python list_optimization_results.py <results_csv_path> [metrics_to_show]

参数：
    results_csv_path: 结果CSV文件路径
    metrics_to_show: 可选，要显示的指标列表，用逗号分隔，默认为 score,win_rate,return,sharpe_ratio
"""

import sys
import csv

def list_results(csv_path, metrics_to_show=None):
    """列出CSV文件中的所有结果"""
    if metrics_to_show is None:
        metrics_to_show = ['score', 'win_rate', 'return', 'sharpe_ratio']

    try:
        with open(csv_path, 'r') as f:
            reader = csv.reader(f)
            headers = next(reader)  # 读取标题行

            # 找出要显示的列的索引
            indices = [0]  # 行号列
            column_names = ['Row']

            for metric in metrics_to_show:
                if metric in headers:
                    indices.append(headers.index(metric))
                    column_names.append(metric)

            # 计算每列的最大宽度
            col_widths = [len(str(name)) for name in column_names]

            # 读取所有行
            rows = []
            for i, row in enumerate(reader, 1):
                if not row:  # 跳过空行
                    continue

                display_row = [str(i)]  # 行号
                for j, idx in enumerate(indices[1:], 1):  # 跳过行号列
                    value = row[idx] if idx < len(row) else ''
                    display_row.append(value)
                    # 更新列宽
                    col_widths[j] = max(col_widths[j], len(value))

                rows.append(display_row)

            # 打印表头
            header_line = ' | '.join(f"{column_names[i]:{col_widths[i]}}" for i in range(len(column_names)))
            print(header_line)
            print('-' * len(header_line))

            # 打印数据行
            for row in rows:
                line = ' | '.join(f"{row[i]:{col_widths[i]}}" for i in range(len(row)))
                print(line)

            print(f"\n总共 {len(rows)} 个结果")
            print("\n使用方法:")
            print(f"  python generate_config_from_result.py {csv_path} <行号> <输出配置文件路径>")

    except Exception as e:
        print(f"错误: {e}")
        sys.exit(1)

def main():
    if len(sys.argv) < 2:
        print(__doc__)
        sys.exit(1)

    results_csv_path = sys.argv[1]

    metrics_to_show = ['score', 'win_rate', 'return', 'sharpe_ratio']
    if len(sys.argv) > 2:
        metrics_to_show = sys.argv[2].split(',')

    list_results(results_csv_path, metrics_to_show)

if __name__ == "__main__":
    main()
