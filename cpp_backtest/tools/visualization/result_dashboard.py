#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
结果仪表盘生成器

这个脚本用于生成参数优化结果的交互式仪表盘，帮助用户分析优化结果。
"""

import os
import sys
import json
import argparse
import numpy as np
import pandas as pd
import plotly.graph_objects as go
import plotly.express as px
from plotly.subplots import make_subplots
import dash
from dash import dcc, html
from dash.dependencies import Input, Output

def load_results(results_file):
    """加载优化结果"""
    with open(results_file, 'r') as f:
        results = json.load(f)
    return results

def results_to_dataframe(results):
    """将优化结果转换为 DataFrame"""
    data = []
    for i, result in enumerate(results):
        row = {'iteration': i, 'score': result['score']}
        for param_name, param_value in result['parameters'].items():
            row[param_name] = param_value
        for metric_name, metric_value in result['metrics'].items():
            row[metric_name] = metric_value
        data.append(row)
    return pd.DataFrame(data)

def create_dashboard(df, output_dir):
    """创建交互式仪表盘"""
    # 获取参数列和指标列
    param_cols = [col for col in df.columns if col not in ['iteration', 'score'] and not col.startswith('metric')]
    metric_cols = [col for col in df.columns if col.startswith('metric')] + ['score']
    
    # 创建 Dash 应用
    app = dash.Dash(__name__)
    
    # 定义应用布局
    app.layout = html.Div([
        html.H1('Parameter Optimization Results Dashboard'),
        
        html.Div([
            html.Div([
                html.H3('Optimization Progress'),
                dcc.Graph(id='progress-chart')
            ], className='six columns'),
            
            html.Div([
                html.H3('Score Distribution'),
                dcc.Graph(id='score-distribution')
            ], className='six columns')
        ], className='row'),
        
        html.Div([
            html.Div([
                html.H3('Parameter Sensitivity'),
                html.Label('Select Parameter:'),
                dcc.Dropdown(
                    id='param-dropdown',
                    options=[{'label': param, 'value': param} for param in param_cols],
                    value=param_cols[0] if param_cols else None
                ),
                dcc.Graph(id='param-sensitivity')
            ], className='six columns'),
            
            html.Div([
                html.H3('Metric Correlation'),
                html.Label('Select Metric:'),
                dcc.Dropdown(
                    id='metric-dropdown',
                    options=[{'label': metric, 'value': metric} for metric in metric_cols],
                    value='score'
                ),
                dcc.Graph(id='metric-correlation')
            ], className='six columns')
        ], className='row'),
        
        html.Div([
            html.H3('Top Results'),
            html.Label('Number of Top Results:'),
            dcc.Slider(
                id='top-n-slider',
                min=5,
                max=50,
                step=5,
                value=10,
                marks={i: str(i) for i in range(5, 51, 5)}
            ),
            dcc.Graph(id='top-results')
        ]),
        
        html.Div([
            html.H3('Parameter Correlation Heatmap'),
            dcc.Graph(id='param-heatmap')
        ]),
        
        html.Div([
            html.H3('Metric Correlation Heatmap'),
            dcc.Graph(id='metric-heatmap')
        ])
    ])
    
    # 定义回调函数
    @app.callback(
        Output('progress-chart', 'figure'),
        [Input('param-dropdown', 'value')]
    )
    def update_progress_chart(selected_param):
        fig = make_subplots(specs=[[{"secondary_y": True}]])
        
        # 添加得分历史
        fig.add_trace(
            go.Scatter(x=df['iteration'], y=df['score'], mode='markers', name='Score'),
            secondary_y=False
        )
        
        # 添加最佳得分进度
        fig.add_trace(
            go.Scatter(x=df['iteration'], y=df['score'].cummax(), mode='lines', name='Best Score', line=dict(color='red')),
            secondary_y=False
        )
        
        # 如果选择了参数，添加参数值
        if selected_param:
            fig.add_trace(
                go.Scatter(x=df['iteration'], y=df[selected_param], mode='markers', name=selected_param),
                secondary_y=True
            )
        
        fig.update_layout(
            title='Optimization Progress',
            xaxis_title='Iteration',
            yaxis_title='Score',
            yaxis2_title='Parameter Value' if selected_param else None
        )
        
        return fig
    
    @app.callback(
        Output('score-distribution', 'figure'),
        [Input('top-n-slider', 'value')]
    )
    def update_score_distribution(top_n):
        fig = go.Figure()
        
        # 添加得分分布直方图
        fig.add_trace(
            go.Histogram(x=df['score'], nbinsx=20, name='Score Distribution')
        )
        
        # 添加最佳得分线
        fig.add_vline(x=df['score'].max(), line_dash='dash', line_color='red',
                     annotation_text=f'Max Score: {df["score"].max():.4f}',
                     annotation_position='top right')
        
        fig.update_layout(
            title='Score Distribution',
            xaxis_title='Score',
            yaxis_title='Count'
        )
        
        return fig
    
    @app.callback(
        Output('param-sensitivity', 'figure'),
        [Input('param-dropdown', 'value')]
    )
    def update_param_sensitivity(selected_param):
        if not selected_param:
            return go.Figure()
        
        fig = go.Figure()
        
        # 对参数值进行分组，计算每组的平均得分
        try:
            param_values = df[selected_param].unique()
            
            # 如果参数值太多，进行分箱
            if len(param_values) > 20 and df[selected_param].dtype in [np.float64, np.int64]:
                bins = 10
                df['param_bin'] = pd.cut(df[selected_param], bins=bins)
                grouped = df.groupby('param_bin')['score'].agg(['mean', 'count']).reset_index()
                
                # 使用箱子的中点作为 x 值
                x_values = [interval.mid for interval in grouped['param_bin']]
                
                fig.add_trace(
                    go.Bar(x=x_values, y=grouped['mean'], name='Mean Score',
                          text=grouped['count'], textposition='auto')
                )
            else:
                grouped = df.groupby(selected_param)['score'].agg(['mean', 'count']).reset_index()
                
                fig.add_trace(
                    go.Bar(x=grouped[selected_param], y=grouped['mean'], name='Mean Score',
                          text=grouped['count'], textposition='auto')
                )
        except:
            # 如果分组失败，使用散点图
            fig.add_trace(
                go.Scatter(x=df[selected_param], y=df['score'], mode='markers', name='Score')
            )
        
        fig.update_layout(
            title=f'Parameter Sensitivity: {selected_param}',
            xaxis_title=selected_param,
            yaxis_title='Score'
        )
        
        return fig
    
    @app.callback(
        Output('metric-correlation', 'figure'),
        [Input('metric-dropdown', 'value'),
         Input('param-dropdown', 'value')]
    )
    def update_metric_correlation(selected_metric, selected_param):
        if not selected_metric or not selected_param:
            return go.Figure()
        
        fig = go.Figure()
        
        # 添加散点图
        fig.add_trace(
            go.Scatter(x=df[selected_param], y=df[selected_metric], mode='markers', name=selected_metric)
        )
        
        # 添加趋势线
        if df[selected_param].dtype in [np.float64, np.int64]:
            try:
                z = np.polyfit(df[selected_param], df[selected_metric], 1)
                p = np.poly1d(z)
                x_range = np.linspace(df[selected_param].min(), df[selected_param].max(), 100)
                fig.add_trace(
                    go.Scatter(x=x_range, y=p(x_range), mode='lines', name='Trend Line',
                              line=dict(color='red', dash='dash'))
                )
            except:
                pass
        
        fig.update_layout(
            title=f'Correlation: {selected_param} vs {selected_metric}',
            xaxis_title=selected_param,
            yaxis_title=selected_metric
        )
        
        return fig
    
    @app.callback(
        Output('top-results', 'figure'),
        [Input('top-n-slider', 'value')]
    )
    def update_top_results(top_n):
        # 按得分排序，获取前 N 个结果
        df_top = df.sort_values('score', ascending=False).head(top_n)
        
        fig = go.Figure()
        
        # 添加得分条形图
        fig.add_trace(
            go.Bar(y=[f'Rank {i+1}' for i in range(len(df_top))], x=df_top['score'],
                  orientation='h', name='Score')
        )
        
        fig.update_layout(
            title=f'Top {top_n} Results',
            xaxis_title='Score',
            yaxis_title='Rank',
            height=max(400, 30 * top_n)
        )
        
        return fig
    
    @app.callback(
        Output('param-heatmap', 'figure'),
        [Input('param-dropdown', 'value')]
    )
    def update_param_heatmap(selected_param):
        if len(param_cols) <= 1:
            return go.Figure()
        
        # 计算参数之间的相关性
        corr = df[param_cols + ['score']].corr()
        
        fig = go.Figure(data=go.Heatmap(
            z=corr.values,
            x=corr.columns,
            y=corr.index,
            colorscale='RdBu_r',
            zmin=-1,
            zmax=1,
            text=np.round(corr.values, 2),
            texttemplate='%{text:.2f}'
        ))
        
        fig.update_layout(
            title='Parameter Correlation Heatmap',
            height=600,
            width=800
        )
        
        return fig
    
    @app.callback(
        Output('metric-heatmap', 'figure'),
        [Input('metric-dropdown', 'value')]
    )
    def update_metric_heatmap(selected_metric):
        if len(metric_cols) <= 1:
            return go.Figure()
        
        # 计算指标之间的相关性
        corr = df[metric_cols].corr()
        
        fig = go.Figure(data=go.Heatmap(
            z=corr.values,
            x=corr.columns,
            y=corr.index,
            colorscale='RdBu_r',
            zmin=-1,
            zmax=1,
            text=np.round(corr.values, 2),
            texttemplate='%{text:.2f}'
        ))
        
        fig.update_layout(
            title='Metric Correlation Heatmap',
            height=600,
            width=800
        )
        
        return fig
    
    # 保存仪表盘为 HTML 文件
    app_html = app.index_string
    
    os.makedirs(output_dir, exist_ok=True)
    with open(os.path.join(output_dir, 'dashboard.html'), 'w') as f:
        f.write(app_html)
    
    return app

def main():
    parser = argparse.ArgumentParser(description='Result Dashboard Generator')
    parser.add_argument('--results', required=True, help='Path to results.json file')
    parser.add_argument('--output', default='dashboard', help='Output directory for dashboard')
    parser.add_argument('--run', action='store_true', help='Run the dashboard server')
    args = parser.parse_args()
    
    # 加载优化结果
    results = load_results(args.results)
    
    # 转换为 DataFrame
    df = results_to_dataframe(results)
    
    # 创建仪表盘
    app = create_dashboard(df, args.output)
    
    print(f"Dashboard created. HTML file saved to {os.path.join(args.output, 'dashboard.html')}")
    
    # 如果指定了 --run 参数，运行仪表盘服务器
    if args.run:
        print("Starting dashboard server...")
        app.run_server(debug=True)

if __name__ == '__main__':
    main()
