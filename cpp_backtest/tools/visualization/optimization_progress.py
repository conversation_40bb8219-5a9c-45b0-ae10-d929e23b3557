#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
优化进度可视化工具

这个脚本用于实时可视化参数优化的进度，帮助用户了解优化过程中的结果变化。
"""

import os
import sys
import json
import time
import argparse
import numpy as np
import pandas as pd
import matplotlib.pyplot as plt
from matplotlib.animation import FuncAnimation
from matplotlib.ticker import MaxNLocator

class OptimizationProgressVisualizer:
    def __init__(self, results_file, update_interval=5):
        self.results_file = results_file
        self.update_interval = update_interval
        self.results = []
        self.fig, self.axes = plt.subplots(2, 2, figsize=(15, 10))
        self.fig.suptitle('Parameter Optimization Progress', fontsize=16)
        
        # 设置子图标题
        self.axes[0, 0].set_title('Score History')
        self.axes[0, 1].set_title('Best Score Progress')
        self.axes[1, 0].set_title('Score Distribution')
        self.axes[1, 1].set_title('Parameter Distribution (Top 10)')
        
        # 初始化子图
        self.score_line, = self.axes[0, 0].plot([], [], 'b-', alpha=0.5)
        self.score_scatter = self.axes[0, 0].scatter([], [], c='blue', alpha=0.5)
        self.best_score_line, = self.axes[0, 1].plot([], [], 'r-')
        self.score_hist = None
        self.param_bars = None
        
        # 设置坐标轴标签
        self.axes[0, 0].set_xlabel('Iteration')
        self.axes[0, 0].set_ylabel('Score')
        self.axes[0, 1].set_xlabel('Iteration')
        self.axes[0, 1].set_ylabel('Best Score')
        self.axes[1, 0].set_xlabel('Score')
        self.axes[1, 0].set_ylabel('Count')
        
        # 参数名称
        self.param_names = []
        
        # 紧凑布局
        self.fig.tight_layout(rect=[0, 0, 1, 0.95])
    
    def load_results(self):
        """加载优化结果"""
        try:
            with open(self.results_file, 'r') as f:
                self.results = json.load(f)
        except (json.JSONDecodeError, FileNotFoundError):
            # 文件可能正在写入或尚未创建
            pass
    
    def update(self, frame):
        """更新可视化"""
        self.load_results()
        
        if not self.results:
            return self.score_line, self.score_scatter, self.best_score_line
        
        # 转换为 DataFrame
        df = pd.DataFrame([
            {
                'iteration': i,
                'score': result['score'],
                **{f'param_{k}': v for k, v in result['parameters'].items()}
            }
            for i, result in enumerate(self.results)
        ])
        
        # 更新参数名称
        self.param_names = [col for col in df.columns if col.startswith('param_')]
        
        # 更新得分历史图
        x = df['iteration'].values
        y = df['score'].values
        self.score_line.set_data(x, y)
        self.score_scatter.set_offsets(np.column_stack([x, y]))
        self.axes[0, 0].relim()
        self.axes[0, 0].autoscale_view()
        
        # 更新最佳得分进度图
        best_scores = df['score'].cummax().values
        self.best_score_line.set_data(x, best_scores)
        self.axes[0, 1].relim()
        self.axes[0, 1].autoscale_view()
        
        # 更新得分分布图
        self.axes[1, 0].clear()
        self.axes[1, 0].set_title('Score Distribution')
        self.axes[1, 0].set_xlabel('Score')
        self.axes[1, 0].set_ylabel('Count')
        self.score_hist = self.axes[1, 0].hist(df['score'], bins=20, color='skyblue', edgecolor='black')
        self.axes[1, 0].axvline(df['score'].max(), color='red', linestyle='--', label=f'Max: {df["score"].max():.4f}')
        self.axes[1, 0].legend()
        
        # 更新参数分布图（最佳10个结果）
        self.axes[1, 1].clear()
        self.axes[1, 1].set_title('Parameter Distribution (Top 10)')
        
        if self.param_names:
            # 选择第一个参数进行可视化
            param_name = self.param_names[0]
            
            # 获取最佳10个结果
            top_df = df.sort_values('score', ascending=False).head(10)
            
            # 创建横向条形图
            y_pos = np.arange(len(top_df))
            self.param_bars = self.axes[1, 1].barh(y_pos, top_df[param_name], color='skyblue')
            self.axes[1, 1].set_yticks(y_pos)
            self.axes[1, 1].set_yticklabels([f'Rank {i+1} (Score: {score:.4f})' for i, score in enumerate(top_df['score'])])
            self.axes[1, 1].set_xlabel(param_name.replace('param_', ''))
            
            # 添加参数选择器
            param_selector = plt.axes([0.01, 0.01, 0.15, 0.03])
            param_selector.set_title('Parameter')
            param_selector.set_xticks([])
            param_selector.set_yticks([])
            
            for i, param in enumerate(self.param_names):
                param_button = plt.axes([0.01 + i * 0.15, 0.01, 0.14, 0.03])
                param_button.text(0.5, 0.5, param.replace('param_', ''), 
                                 ha='center', va='center', transform=param_button.transAxes)
                param_button.set_xticks([])
                param_button.set_yticks([])
        
        return self.score_line, self.score_scatter, self.best_score_line
    
    def run(self):
        """运行可视化"""
        ani = FuncAnimation(self.fig, self.update, interval=self.update_interval * 1000, blit=False)
        plt.show()

def main():
    parser = argparse.ArgumentParser(description='Optimization Progress Visualization Tool')
    parser.add_argument('--results', required=True, help='Path to results.json file')
    parser.add_argument('--interval', type=int, default=5, help='Update interval in seconds')
    args = parser.parse_args()
    
    visualizer = OptimizationProgressVisualizer(args.results, args.interval)
    visualizer.run()

if __name__ == '__main__':
    main()
