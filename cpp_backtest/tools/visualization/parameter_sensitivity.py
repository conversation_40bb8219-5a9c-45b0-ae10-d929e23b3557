#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
参数敏感性分析工具

这个脚本用于分析参数优化结果，生成参数敏感性图表，帮助用户理解参数对性能指标的影响。
"""

import os
import sys
import json
import argparse
import numpy as np
import pandas as pd
import matplotlib.pyplot as plt
import seaborn as sns
from matplotlib.ticker import MaxNLocator

def load_results(results_file):
    """加载优化结果"""
    with open(results_file, 'r') as f:
        data = json.load(f)

    # 检查JSON结构，提取results列表
    if isinstance(data, dict) and 'results' in data:
        return data['results']
    elif isinstance(data, list):
        return data
    else:
        raise ValueError(f"Unexpected JSON format in {results_file}")

def load_sensitivity_analysis(sensitivity_file):
    """加载参数敏感性分析结果"""
    with open(sensitivity_file, 'r') as f:
        sensitivity = json.load(f)
    return sensitivity

def results_to_dataframe(results):
    """将优化结果转换为 DataFrame"""
    data = []
    for result in results:
        row = {'score': result['score']}
        for param_name, param_value in result['parameters'].items():
            row[param_name] = param_value
        for metric_name, metric_value in result['metrics'].items():
            row[metric_name] = metric_value
        data.append(row)
    return pd.DataFrame(data)

def plot_parameter_sensitivity(sensitivity, output_dir):
    """绘制参数敏感性图表"""
    os.makedirs(output_dir, exist_ok=True)

    for param_name, param_data in sensitivity.items():
        plt.figure(figsize=(10, 6))

        # 转换为 DataFrame
        df = pd.DataFrame(param_data)

        # 将 value 转换为数值类型（如果可能）
        try:
            df['value'] = pd.to_numeric(df['value'])
            # 按值排序
            df = df.sort_values('value')
        except:
            # 如果无法转换为数值，保持原样
            pass

        # 绘制参数敏感性图表
        plt.bar(df['value'].astype(str), df['score'], color='skyblue')
        plt.xlabel(param_name)
        plt.ylabel('Score')
        plt.title(f'Parameter Sensitivity: {param_name}')
        plt.xticks(rotation=45)
        plt.tight_layout()

        # 添加样本数量标签
        for i, count in enumerate(df['count']):
            plt.text(i, df['score'].iloc[i] + 0.01, f'n={count}', ha='center')

        # 保存图表
        plt.savefig(os.path.join(output_dir, f'sensitivity_{param_name.replace("[", "_").replace("]", "_").replace(".", "_")}.png'))
        plt.close()

def plot_parameter_correlation(df, output_dir):
    """绘制参数相关性图表"""
    os.makedirs(output_dir, exist_ok=True)

    # 获取参数列和指标列
    param_cols = [col for col in df.columns if col not in ['score'] and col not in df.columns[df.columns.str.startswith('metric')]]
    metric_cols = [col for col in df.columns if col.startswith('metric')] + ['score']

    # 绘制参数与得分的散点图
    for param in param_cols:
        if df[param].dtype in [np.float64, np.int64]:
            plt.figure(figsize=(10, 6))
            plt.scatter(df[param], df['score'], alpha=0.5)
            plt.xlabel(param)
            plt.ylabel('Score')
            plt.title(f'Parameter vs Score: {param}')

            # 添加趋势线
            try:
                z = np.polyfit(df[param], df['score'], 1)
                p = np.poly1d(z)
                plt.plot(df[param], p(df[param]), "r--")
            except:
                pass

            plt.tight_layout()
            plt.savefig(os.path.join(output_dir, f'correlation_{param.replace("[", "_").replace("]", "_").replace(".", "_")}.png'))
            plt.close()

    # 绘制参数之间的相关性热图
    if len(param_cols) > 1:
        plt.figure(figsize=(12, 10))
        corr = df[param_cols + ['score']].corr()
        sns.heatmap(corr, annot=True, cmap='coolwarm', fmt='.2f')
        plt.title('Parameter Correlation')
        plt.tight_layout()
        plt.savefig(os.path.join(output_dir, 'parameter_correlation.png'))
        plt.close()

    # 绘制指标之间的相关性热图
    if len(metric_cols) > 1:
        plt.figure(figsize=(12, 10))
        corr = df[metric_cols].corr()
        sns.heatmap(corr, annot=True, cmap='coolwarm', fmt='.2f')
        plt.title('Metric Correlation')
        plt.tight_layout()
        plt.savefig(os.path.join(output_dir, 'metric_correlation.png'))
        plt.close()

def plot_optimization_progress(df, output_dir):
    """绘制优化进度图表"""
    os.makedirs(output_dir, exist_ok=True)

    # 按得分排序
    df = df.sort_values('score', ascending=False)

    # 绘制得分分布图
    plt.figure(figsize=(10, 6))
    plt.hist(df['score'], bins=20, color='skyblue', edgecolor='black')
    plt.xlabel('Score')
    plt.ylabel('Count')
    plt.title('Score Distribution')
    plt.axvline(df['score'].max(), color='red', linestyle='--', label=f'Max Score: {df["score"].max():.4f}')
    plt.legend()
    plt.tight_layout()
    plt.savefig(os.path.join(output_dir, 'score_distribution.png'))
    plt.close()

    # 绘制累积最大得分图
    plt.figure(figsize=(10, 6))
    max_scores = df['score'].sort_values(ascending=False).cummax()
    plt.plot(range(1, len(max_scores) + 1), max_scores)
    plt.xlabel('Iteration')
    plt.ylabel('Max Score')
    plt.title('Cumulative Maximum Score')
    plt.xscale('log')
    plt.grid(True)
    plt.tight_layout()
    plt.savefig(os.path.join(output_dir, 'cumulative_max_score.png'))
    plt.close()

def plot_top_results(df, output_dir, top_n=10):
    """绘制最佳结果图表"""
    os.makedirs(output_dir, exist_ok=True)

    # 按得分排序，获取前 N 个结果
    df_top = df.sort_values('score', ascending=False).head(top_n)

    # 绘制最佳结果的参数值
    param_cols = [col for col in df.columns if col not in ['score'] and col not in df.columns[df.columns.str.startswith('metric')]]

    for param in param_cols:
        plt.figure(figsize=(12, 6))

        # 创建横向条形图
        y_pos = np.arange(len(df_top))
        plt.barh(y_pos, df_top[param], color='skyblue')
        plt.yticks(y_pos, [f'Rank {i+1} (Score: {score:.4f})' for i, score in enumerate(df_top['score'])])
        plt.xlabel(param)
        plt.title(f'Top {top_n} Results: {param}')
        plt.tight_layout()
        plt.savefig(os.path.join(output_dir, f'top_results_{param.replace("[", "_").replace("]", "_").replace(".", "_")}.png'))
        plt.close()

    # 绘制最佳结果的指标值
    metric_cols = [col for col in df.columns if col.startswith('metric')]

    for metric in metric_cols:
        plt.figure(figsize=(12, 6))

        # 创建横向条形图
        y_pos = np.arange(len(df_top))
        plt.barh(y_pos, df_top[metric], color='skyblue')
        plt.yticks(y_pos, [f'Rank {i+1} (Score: {score:.4f})' for i, score in enumerate(df_top['score'])])
        plt.xlabel(metric)
        plt.title(f'Top {top_n} Results: {metric}')
        plt.tight_layout()
        plt.savefig(os.path.join(output_dir, f'top_results_{metric.replace("[", "_").replace("]", "_").replace(".", "_")}.png'))
        plt.close()

def generate_html_report(output_dir):
    """生成 HTML 报告"""
    html = """
    <!DOCTYPE html>
    <html>
    <head>
        <title>Parameter Optimization Results</title>
        <style>
            body { font-family: Arial, sans-serif; margin: 20px; }
            h1, h2, h3 { color: #333; }
            .section { margin-bottom: 30px; }
            .image-container { display: flex; flex-wrap: wrap; }
            .image-item { margin: 10px; text-align: center; }
            img { max-width: 100%; height: auto; border: 1px solid #ddd; }
        </style>
    </head>
    <body>
        <h1>Parameter Optimization Results</h1>

        <div class="section">
            <h2>Optimization Progress</h2>
            <div class="image-container">
                <div class="image-item">
                    <img src="score_distribution.png" alt="Score Distribution">
                    <p>Score Distribution</p>
                </div>
                <div class="image-item">
                    <img src="cumulative_max_score.png" alt="Cumulative Maximum Score">
                    <p>Cumulative Maximum Score</p>
                </div>
            </div>
        </div>

        <div class="section">
            <h2>Parameter Sensitivity</h2>
            <div class="image-container">
    """

    # 添加参数敏感性图表
    sensitivity_images = [f for f in os.listdir(output_dir) if f.startswith('sensitivity_') and f.endswith('.png')]
    for image in sensitivity_images:
        param_name = image[len('sensitivity_'):-4].replace('_', '.').replace('..', '[').replace('..', ']')
        html += f"""
                <div class="image-item">
                    <img src="{image}" alt="Parameter Sensitivity: {param_name}">
                    <p>Parameter Sensitivity: {param_name}</p>
                </div>
        """

    html += """
            </div>
        </div>

        <div class="section">
            <h2>Parameter Correlation</h2>
            <div class="image-container">
    """

    # 添加参数相关性图表
    correlation_images = [f for f in os.listdir(output_dir) if f.startswith('correlation_') and f.endswith('.png')]
    for image in correlation_images:
        param_name = image[len('correlation_'):-4].replace('_', '.').replace('..', '[').replace('..', ']')
        html += f"""
                <div class="image-item">
                    <img src="{image}" alt="Parameter Correlation: {param_name}">
                    <p>Parameter Correlation: {param_name}</p>
                </div>
        """

    # 添加参数相关性热图
    if os.path.exists(os.path.join(output_dir, 'parameter_correlation.png')):
        html += f"""
                <div class="image-item">
                    <img src="parameter_correlation.png" alt="Parameter Correlation Heatmap">
                    <p>Parameter Correlation Heatmap</p>
                </div>
        """

    # 添加指标相关性热图
    if os.path.exists(os.path.join(output_dir, 'metric_correlation.png')):
        html += f"""
                <div class="image-item">
                    <img src="metric_correlation.png" alt="Metric Correlation Heatmap">
                    <p>Metric Correlation Heatmap</p>
                </div>
        """

    html += """
            </div>
        </div>

        <div class="section">
            <h2>Top Results</h2>
            <div class="image-container">
    """

    # 添加最佳结果图表
    top_results_images = [f for f in os.listdir(output_dir) if f.startswith('top_results_') and f.endswith('.png')]
    for image in top_results_images:
        param_name = image[len('top_results_'):-4].replace('_', '.').replace('..', '[').replace('..', ']')
        html += f"""
                <div class="image-item">
                    <img src="{image}" alt="Top Results: {param_name}">
                    <p>Top Results: {param_name}</p>
                </div>
        """

    html += """
            </div>
        </div>
    </body>
    </html>
    """

    with open(os.path.join(output_dir, 'visualization_report.html'), 'w') as f:
        f.write(html)

def main():
    parser = argparse.ArgumentParser(description='Parameter Sensitivity Analysis Tool')
    parser.add_argument('--results', required=True, help='Path to results.json file')
    parser.add_argument('--sensitivity', help='Path to sensitivity_analysis.json file')
    parser.add_argument('--output', default='visualization', help='Output directory for visualization')
    args = parser.parse_args()

    # 加载优化结果
    results = load_results(args.results)

    # 转换为 DataFrame
    df = results_to_dataframe(results)

    # 创建输出目录
    os.makedirs(args.output, exist_ok=True)

    # 绘制优化进度图表
    plot_optimization_progress(df, args.output)

    # 绘制参数相关性图表
    plot_parameter_correlation(df, args.output)

    # 绘制最佳结果图表
    plot_top_results(df, args.output)

    # 如果提供了参数敏感性分析结果，绘制参数敏感性图表
    if args.sensitivity:
        sensitivity = load_sensitivity_analysis(args.sensitivity)
        plot_parameter_sensitivity(sensitivity, args.output)

    # 生成 HTML 报告
    generate_html_report(args.output)

    print(f"Visualization completed. Results saved to {args.output}")
    print(f"HTML report: {os.path.join(args.output, 'visualization_report.html')}")

if __name__ == '__main__':
    main()
