#!/usr/bin/env python3
"""
根据优化结果CSV文件中的一行生成配置文件。
用法：
    python generate_config_from_result.py <results_csv_path> <row_number> <output_config_path> [base_config_path]

参数：
    results_csv_path: 结果CSV文件路径
    row_number: 要使用的行号（从1开始，不包括标题行）
    output_config_path: 输出配置文件路径
    base_config_path: 可选，基础配置文件路径，默认为 ../examples/market_making_config.json
"""

import sys
import os
import json
import csv
import copy

def read_csv_row(csv_path, row_number):
    """读取CSV文件中的指定行"""
    with open(csv_path, 'r') as f:
        reader = csv.reader(f)
        headers = next(reader)  # 读取标题行
        
        # 跳过行直到达到指定行
        current_row = 1
        for row in reader:
            if current_row == row_number:
                return headers, row
            current_row += 1
    
    raise ValueError(f"行号 {row_number} 超出CSV文件范围")

def update_config_with_params(config, headers, values):
    """使用参数更新配置"""
    # 创建配置的深拷贝
    new_config = copy.deepcopy(config)
    
    # 创建参数字典
    params = {}
    for i, header in enumerate(headers):
        if i < len(values):
            # 尝试将值转换为适当的类型
            try:
                # 首先尝试转换为整数
                params[header] = int(values[i])
            except ValueError:
                try:
                    # 然后尝试转换为浮点数
                    params[header] = float(values[i])
                except ValueError:
                    # 如果都失败，保持为字符串
                    params[header] = values[i]
    
    # 更新配置
    for param_name, param_value in params.items():
        # 跳过得分和指标列
        if param_name == 'score' or param_name in ['sharpe_ratio', 'avg_position_time', 'return', 'win_rate', 
                                                  'total_trades', 'daily_trades', 'total_trading_volume']:
            continue
        
        # 处理嵌套参数（如 indicator_settings.tick_ema.span）
        if '.' in param_name:
            parts = param_name.split('.')
            current = new_config['symbol_configs'][0]
            
            # 遍历嵌套层级
            for i, part in enumerate(parts):
                if i == len(parts) - 1:
                    # 最后一部分是实际的参数名
                    current[part] = param_value
                else:
                    # 确保中间层级存在
                    if part not in current:
                        current[part] = {}
                    current = current[part]
        else:
            # 直接参数
            new_config['symbol_configs'][0][param_name] = param_value
    
    return new_config

def main():
    if len(sys.argv) < 4:
        print(__doc__)
        sys.exit(1)
    
    results_csv_path = sys.argv[1]
    row_number = int(sys.argv[2])
    output_config_path = sys.argv[3]
    
    # 默认基础配置文件路径
    base_config_path = "../examples/market_making_config.json"
    if len(sys.argv) > 4:
        base_config_path = sys.argv[4]
    
    # 确保基础配置文件路径是相对于脚本位置的
    script_dir = os.path.dirname(os.path.abspath(__file__))
    base_config_path = os.path.join(script_dir, base_config_path)
    
    try:
        # 读取基础配置
        with open(base_config_path, 'r') as f:
            base_config = json.load(f)
        
        # 读取CSV行
        headers, values = read_csv_row(results_csv_path, row_number)
        
        # 更新配置
        new_config = update_config_with_params(base_config, headers, values)
        
        # 写入新配置
        with open(output_config_path, 'w') as f:
            json.dump(new_config, f, indent=2)
        
        print(f"配置文件已生成: {output_config_path}")
        print(f"使用的参数来自结果CSV的第 {row_number} 行")
        
        # 打印参数摘要
        print("\n参数摘要:")
        for i, header in enumerate(headers):
            if i < len(values) and header != 'score' and header not in ['sharpe_ratio', 'avg_position_time', 'return', 'win_rate', 
                                                                       'total_trades', 'daily_trades', 'total_trading_volume']:
                print(f"  {header}: {values[i]}")
        
    except Exception as e:
        print(f"错误: {e}")
        sys.exit(1)

if __name__ == "__main__":
    main()
