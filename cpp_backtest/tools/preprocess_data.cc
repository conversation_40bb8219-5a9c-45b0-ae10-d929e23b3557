#include <fast_trader_elite/cpp_backtest/data_preprocessor/data_preprocessor.h>
#include <iostream>
#include <string>
#include <fstream>
#include <nlohmann_json/json.hpp>

using namespace fast_trader_elite::cpp_backtest;
using json = nlohmann::json;

void print_usage() {
    std::cout << "Usage: preprocess_data <config_file> <output_file>" << std::endl;
    std::cout << "Config file format (JSON):" << std::endl;
    std::cout << "For BYBIT exchange:" << std::endl;
    std::cout << R"({
    "exchange": "bybit",
    "instrument": {
        "symbol": "BTCUSDT",
        "tick_size": 0.1,
        "step_size": 0.001,
        "price_tick": 0.000001,
        "depth_level": 5
    },
    "depth_data_path": "path/to/depth_data.json",
    "transaction_data_path": "path/to/transaction_data.csv",
    "kline_data_path": "path/to/kline_data.csv",
    "latency": 1000000
})" << std::endl;
    std::cout << "\nFor SHM TAR files:" << std::endl;
    std::cout << R"({
    "exchange": "shm_tar",
    "shm_tar_path": "path/to/record_bybit.shm",
    "start_page": 0,
    "end_page": 10,
    "instrument": {
        "symbol": "BTCUSDT",
        "tick_size": 0.1,
        "step_size": 0.001
    }
})" << std::endl;
}

int main(int argc, char* argv[]) {
    if (argc < 3) {
        print_usage();
        return 1;
    }

    std::string config_path = argv[1];
    std::string output_file = argv[2];

    // 创建数据预处理器并从配置文件初始化
    data_preprocessor preprocessor;

    // 使用配置文件初始化
    if (!preprocessor.init_from_config(config_path)) {
        std::cerr << "Failed to initialize from config file: " << config_path << std::endl;
        return 1;
    }

    // 读取配置文件
    std::ifstream config_file(config_path);
    if (!config_file.is_open()) {
        std::cerr << "Failed to open config file: " << config_path << std::endl;
        return 1;
    }

    json config;
    try {
        config = json::parse(config_file);
    } catch (const std::exception& e) {
        std::cerr << "Failed to parse config file: " << e.what() << std::endl;
        return 1;
    }

    // 对于SHM TAR预处理器，我们需要显式调用read_depth_data方法
    if (config.contains("exchange") && config["exchange"].get<std::string>() == "shm_tar") {
        std::cout << "Reading SHM TAR data..." << std::endl;
        if (!preprocessor.read_depth_data("")) {
            std::cerr << "Failed to read SHM TAR data" << std::endl;
            return 1;
        }
    }
    // 对于其他预处理器，数据已经通过配置文件读取

    std::cout << "Processing data..." << std::endl;
    if (!preprocessor.process_data()) {
        std::cerr << "Error processing data" << std::endl;
        return 1;
    }

    std::cout << "Writing binary file: " << output_file << std::endl;
    if (!preprocessor.write_binary_file(output_file)) {
        std::cerr << "Error writing binary file" << std::endl;
        return 1;
    }

    std::cout << "Done!" << std::endl;
    std::cout << "Records: " << preprocessor.get_record_count() << std::endl;
    std::cout << "Instrument: " << preprocessor.get_instrument().instrument_id << std::endl;

    return 0;
}
