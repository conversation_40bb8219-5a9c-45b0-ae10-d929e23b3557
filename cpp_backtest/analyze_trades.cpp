#include <iostream>
#include <fstream>
#include <sstream>
#include <vector>
#include <string>
#include <cmath>
#include <iomanip>
#include <map>

// 定义交易记录结构
struct Trade {
    uint64_t timestamp;
    std::string ts;
    std::string order_id;
    double price;
    double quantity;
    std::string side;
    bool is_maker;
    double fee;
    double trading_value;
};

// 定义仓位周期结构
struct PositionCycle {
    std::vector<Trade> trades;
    double entry_price;
    double exit_price;
    double position_size;
    double pnl;
    bool is_win;
    std::string direction; // "long" 或 "short"
    uint64_t start_time;
    uint64_t end_time;
    double duration_seconds;
};

// 读取CSV文件
std::vector<Trade> read_trades_csv(const std::string& file_path) {
    std::vector<Trade> trades;
    std::ifstream file(file_path);

    if (!file.is_open()) {
        std::cerr << "无法打开文件: " << file_path << std::endl;
        return trades;
    }

    std::string line;
    // 跳过标题行
    std::getline(file, line);

    while (std::getline(file, line)) {
        if (line.empty()) continue;

        std::stringstream ss(line);
        std::string field;
        Trade trade;

        // 解析每个字段
        std::getline(ss, field, ',');
        trade.timestamp = std::stoull(field);

        std::getline(ss, trade.ts, ',');
        std::getline(ss, trade.order_id, ',');

        std::getline(ss, field, ',');
        trade.price = std::stod(field);

        std::getline(ss, field, ',');
        trade.quantity = std::stod(field);

        std::getline(ss, trade.side, ',');

        std::getline(ss, field, ',');
        trade.is_maker = (field == "true");

        std::getline(ss, field, ',');
        trade.fee = std::stod(field);

        std::getline(ss, field, ',');
        trade.trading_value = std::stod(field);

        trades.push_back(trade);
    }

    return trades;
}

// 分析交易记录，识别仓位周期
std::vector<PositionCycle> analyze_position_cycles(const std::vector<Trade>& trades) {
    std::vector<PositionCycle> cycles;

    // 跟踪仓位和平均开仓价格
    double current_position = 0.0;
    double avg_open_price = 0.0;
    double position_cost = 0.0;
    bool has_position = false;
    double position_direction = 0.0; // 1.0表示多头，-1.0表示空头
    PositionCycle current_cycle;

    // 使用小阈值判断仓位是否为零
    constexpr double POSITION_EPSILON = 1e-10;

    std::cout << "交易分析过程：" << std::endl;
    std::cout << std::setw(25) << "时间戳"
              << std::setw(10) << "方向"
              << std::setw(10) << "数量"
              << std::setw(10) << "价格"
              << std::setw(15) << "前仓位"
              << std::setw(15) << "后仓位"
              << std::setw(15) << "平均开仓价"
              << std::setw(15) << "交易盈亏"
              << std::setw(10) << "手续费"
              << std::setw(15) << "备注"
              << std::endl;

    for (const auto& trade : trades) {
        double prev_position = current_position;
        bool prev_has_position = has_position;
        double prev_direction = position_direction;
        double trade_pnl = 0.0;

        // 更新仓位
        if (trade.side == "buy") {
            current_position += trade.quantity;
        } else {
            current_position -= trade.quantity;
        }

        // 判断仓位状态，使用小阈值
        has_position = std::abs(current_position) > POSITION_EPSILON;

        // 更新仓位方向
        if (current_position > POSITION_EPSILON) {
            position_direction = 1.0; // 多头
        } else if (current_position < -POSITION_EPSILON) {
            position_direction = -1.0; // 空头
        } else {
            position_direction = 0.0; // 无仓位
        }

        // 处理仓位变化
        std::string note;
        if (!prev_has_position && has_position) {
            // 新开仓
            avg_open_price = trade.price;
            position_cost = std::abs(current_position) * avg_open_price;

            // 创建新的仓位周期
            current_cycle = PositionCycle();
            current_cycle.trades.push_back(trade);
            current_cycle.entry_price = avg_open_price;
            current_cycle.direction = (position_direction > 0) ? "long" : "short";
            current_cycle.start_time = trade.timestamp;

            note = "新开仓";
        } else if (prev_has_position) {
            // 添加交易到当前周期
            current_cycle.trades.push_back(trade);

            if (!has_position || (prev_direction * position_direction < 0)) {
                // 完全平仓或方向变化
                // 计算整个仓位周期的总仓位大小
                double total_position_size = 0.0;
                for (const auto& t : current_cycle.trades) {
                    if ((current_cycle.direction == "long" && t.side == "buy") ||
                        (current_cycle.direction == "short" && t.side == "sell")) {
                        total_position_size += t.quantity;
                    }
                }

                // 计算交易费用
                double total_fee = 0.0;
                for (const auto& t : current_cycle.trades) {
                    total_fee += t.fee;
                }

                // 计算平仓交易的加权平均价格
                double close_position = 0.0;
                double close_value = 0.0;

                for (const auto& t : current_cycle.trades) {
                    if ((current_cycle.direction == "long" && t.side == "sell") ||
                        (current_cycle.direction == "short" && t.side == "buy")) {
                        close_position += t.quantity;
                        close_value += t.quantity * t.price;
                    }
                }

                double avg_close_price = close_position > 0 ? close_value / close_position : trade.price;

                if (prev_position > 0) {
                    // 平多头 - 使用整个仓位周期的开仓价格和平均平仓价格
                    trade_pnl = (avg_close_price - current_cycle.entry_price) * total_position_size;
                } else {
                    // 平空头 - 使用整个仓位周期的开仓价格和平均平仓价格
                    trade_pnl = (current_cycle.entry_price - avg_close_price) * total_position_size;
                }

                // 完成当前仓位周期
                current_cycle.exit_price = trade.price;

                // 这里不需要重复计算total_position_size，因为上面已经计算过了

                current_cycle.position_size = total_position_size;
                current_cycle.pnl = trade_pnl;
                current_cycle.is_win = (trade_pnl > 0);
                current_cycle.end_time = trade.timestamp;
                current_cycle.duration_seconds = static_cast<double>(current_cycle.end_time - current_cycle.start_time) / 1e9;

                // 添加到周期列表
                if (!has_position) {
                    cycles.push_back(current_cycle);
                    note = "完全平仓";
                } else {
                    cycles.push_back(current_cycle);

                    // 创建新的仓位周期
                    current_cycle = PositionCycle();
                    current_cycle.trades.push_back(trade);
                    current_cycle.entry_price = trade.price;
                    current_cycle.direction = (position_direction > 0) ? "long" : "short";
                    current_cycle.start_time = trade.timestamp;

                    note = "方向变化";
                }

                // 如果还有仓位，设置新的平均开仓价格
                if (has_position) {
                    avg_open_price = trade.price;
                    position_cost = std::abs(current_position) * avg_open_price;
                } else {
                    avg_open_price = 0.0;
                    position_cost = 0.0;
                }
            } else if (prev_direction == position_direction) {
                // 同向变化仓位
                if ((position_direction > 0 && trade.side == "buy") ||
                    (position_direction < 0 && trade.side == "sell")) {
                    // 增加仓位 - 更新平均价格
                    double old_cost = position_cost;
                    double new_cost = trade.quantity * trade.price;
                    position_cost = old_cost + new_cost;
                    avg_open_price = position_cost / std::abs(current_position);

                    // 更新当前周期的开仓价格
                    current_cycle.entry_price = avg_open_price;

                    note = "增加仓位";
                } else {
                    // 减少仓位但未完全平仓
                    if (prev_position > 0) {
                        // 减少多头
                        trade_pnl = (trade.price - avg_open_price) * trade.quantity;
                    } else {
                        // 减少空头
                        trade_pnl = (avg_open_price - trade.price) * trade.quantity;
                    }

                    note = "减少仓位";
                }
            }
        }

        std::cout << std::setw(25) << trade.ts
                  << std::setw(10) << trade.side
                  << std::setw(10) << trade.quantity
                  << std::setw(10) << trade.price
                  << std::setw(15) << prev_position
                  << std::setw(15) << current_position
                  << std::setw(15) << avg_open_price
                  << std::setw(15) << trade_pnl
                  << std::setw(10) << trade.fee
                  << std::setw(15) << note
                  << std::endl;
    }

    return cycles;
}

// 计算交易统计信息
void calculate_statistics(const std::vector<PositionCycle>& cycles) {
    if (cycles.empty()) {
        std::cout << "没有完整的仓位周期" << std::endl;
        return;
    }

    int total_cycles = cycles.size();
    int win_cycles = 0;
    double total_pnl = 0.0;
    double total_win_pnl = 0.0;
    double total_loss_pnl = 0.0;
    double total_duration = 0.0;

    std::map<std::string, int> direction_count;
    std::map<std::string, double> direction_pnl;

    for (const auto& cycle : cycles) {
        if (cycle.is_win) {
            win_cycles++;
            total_win_pnl += cycle.pnl;
        } else {
            total_loss_pnl -= cycle.pnl;
        }

        total_pnl += cycle.pnl;
        total_duration += cycle.duration_seconds;

        direction_count[cycle.direction]++;
        direction_pnl[cycle.direction] += cycle.pnl;
    }

    double win_rate = static_cast<double>(win_cycles) / total_cycles;
    double avg_pnl = total_pnl / total_cycles;
    double avg_win_pnl = win_cycles > 0 ? total_win_pnl / win_cycles : 0.0;
    double avg_loss_pnl = (total_cycles - win_cycles) > 0 ? total_loss_pnl / (total_cycles - win_cycles) : 0.0;
    double profit_loss_ratio = avg_loss_pnl > 0 ? avg_win_pnl / avg_loss_pnl : 0.0;
    double avg_duration = total_duration / total_cycles;

    std::cout << "\n交易统计信息：" << std::endl;
    std::cout << "总仓位周期数: " << total_cycles << std::endl;
    std::cout << "盈利周期数: " << win_cycles << std::endl;
    std::cout << "亏损周期数: " << total_cycles - win_cycles << std::endl;
    std::cout << "胜率: " << win_rate * 100 << "%" << std::endl;
    std::cout << "总盈亏: " << total_pnl << std::endl;
    std::cout << "平均盈亏: " << avg_pnl << std::endl;
    std::cout << "平均盈利: " << avg_win_pnl << std::endl;
    std::cout << "平均亏损: " << avg_loss_pnl << std::endl;
    std::cout << "盈亏比: " << profit_loss_ratio << std::endl;
    std::cout << "平均持仓时间: " << avg_duration / 60 << " 分钟" << std::endl;

    std::cout << "\n方向统计：" << std::endl;
    for (const auto& [direction, count] : direction_count) {
        double dir_win_rate = 0.0;
        int dir_win_count = 0;

        for (const auto& cycle : cycles) {
            if (cycle.direction == direction && cycle.is_win) {
                dir_win_count++;
            }
        }

        dir_win_rate = static_cast<double>(dir_win_count) / count;

        std::cout << direction << " 方向: " << count << " 次, 胜率: " << dir_win_rate * 100
                  << "%, 总盈亏: " << direction_pnl[direction] << std::endl;
    }

    std::cout << "\n详细仓位周期：" << std::endl;
    std::cout << std::setw(5) << "序号"
              << std::setw(10) << "方向"
              << std::setw(15) << "开仓价"
              << std::setw(15) << "平仓价"
              << std::setw(15) << "仓位大小"
              << std::setw(15) << "盈亏"
              << std::setw(10) << "结果"
              << std::setw(15) << "持仓时间(分)"
              << std::endl;

    for (int i = 0; i < total_cycles; i++) {
        const auto& cycle = cycles[i];
        std::cout << std::setw(5) << i + 1
                  << std::setw(10) << cycle.direction
                  << std::setw(15) << cycle.entry_price
                  << std::setw(15) << cycle.exit_price
                  << std::setw(15) << cycle.position_size
                  << std::setw(15) << cycle.pnl
                  << std::setw(10) << (cycle.is_win ? "盈利" : "亏损")
                  << std::setw(15) << cycle.duration_seconds / 60
                  << std::endl;
    }
}

int main() {
    // 读取交易记录
    std::string file_path = "/home/<USER>/git/fast_trader_elite/cpp_backtest/build/output/export/trades_1.csv";
    auto trades = read_trades_csv(file_path);

    std::cout << "读取到 " << trades.size() << " 条交易记录" << std::endl;

    // 分析仓位周期
    auto cycles = analyze_position_cycles(trades);

    // 计算统计信息
    calculate_statistics(cycles);

    return 0;
}
