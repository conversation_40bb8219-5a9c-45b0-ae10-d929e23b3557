#pragma once

#include <vector>
#include <string>
#include <fast_trader_elite/cpp_backtest/types.h>

namespace fast_trader_elite {
namespace cpp_backtest {

// 仓位周期类，表示一个完整的交易周期（从开仓到平仓）
struct position_cycle {
    std::vector<internal_trade> trades;  // 该周期内的所有交易
    double entry_price;                  // 开仓价格
    double exit_price;                   // 平仓价格
    double position_size;                // 仓位大小（总交易量）
    double pnl;                          // 盈亏
    bool is_win;                         // 是否盈利
    std::string direction;               // 方向："long" 或 "short"
    int64_t start_time;                  // 开仓时间戳
    int64_t end_time;                    // 平仓时间戳
    double duration_seconds;             // 持仓时间（秒）
};

// 分析交易记录，识别仓位周期
std::vector<position_cycle> analyze_position_cycles(const std::vector<internal_trade>& trades);

// 计算仓位周期的统计信息
struct cycle_statistics {
    int total_cycles;                    // 总仓位周期数
    int win_cycles;                      // 盈利周期数
    double win_rate;                     // 胜率
    double total_pnl;                    // 总盈亏
    double avg_pnl;                      // 平均盈亏
    double avg_win_pnl;                  // 平均盈利
    double avg_loss_pnl;                 // 平均亏损
    double profit_loss_ratio;            // 盈亏比
    double avg_duration;                 // 平均持仓时间（秒）
    int long_cycles;                     // 多头周期数
    int short_cycles;                    // 空头周期数
    double long_win_rate;                // 多头胜率
    double short_win_rate;               // 空头胜率
    double long_pnl;                     // 多头总盈亏
    double short_pnl;                    // 空头总盈亏
};

// 计算仓位周期的统计信息
cycle_statistics calculate_cycle_statistics(const std::vector<position_cycle>& cycles);

} // namespace cpp_backtest
} // namespace fast_trader_elite
