#pragma once

#include <string>
#include <vector>
#include <functional>
#include <span>
#include <sys/mman.h>
#include <sys/stat.h>
#include <fcntl.h>
#include <unistd.h>
#include <fast_trader_elite/cpp_backtest/types.h>

namespace fast_trader_elite {
namespace cpp_backtest {

// 记录回调函数类型 - 使用std::span代替std::vector
using record_callback = std::function<void(const record_header&, std::span<const uint8_t>)>;

// 二进制数据读取器
class binary_reader {
public:
    binary_reader(const std::string& filename);
    ~binary_reader();

    // 打开二进制文件
    bool open(const std::string& filename);

    // 关闭文件
    void close();

    // 读取下一条记录 - 使用std::span代替std::vector，避免数据复制
    bool next_record(record_header& header, std::span<const uint8_t>& data);

    // 传统接口，为了兼容性保留
    bool next_record(record_header& header, std::vector<uint8_t>& data);

    // 流式读取所有记录，对每条记录调用回调函数
    void stream_records(const record_callback& callback);

    // 重置读取位置
    void reset();

    // 获取品种信息
    const std::vector<instrument_info>& get_instruments() const;

    // 获取单个品种信息（假设只有一个品种）
    const instrument_info& get_instrument() const;

    // 获取文件头信息
    const file_header& get_file_header() const;

    // 是否已打开
    bool is_open() const;

private:
    int fd_;
    void* mapped_data_;
    size_t file_size_;
    file_header file_header_;
    std::vector<instrument_info> instruments_;
    size_t current_record_;
    size_t data_start_pos_;
    size_t current_pos_;  // 当前读取位置
    std::span<const uint8_t> current_data_span_; // 当前数据的span视图
};

} // namespace cpp_backtest
} // namespace fast_trader_elite
