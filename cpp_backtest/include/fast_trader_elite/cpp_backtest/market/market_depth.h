#pragma once

#include <vector>
#include <tuple>
#include <fast_trader_elite/cpp_backtest/types.h>

namespace fast_trader_elite {
namespace cpp_backtest {

// 市场深度接口
class market_depth {
public:
    virtual ~market_depth() = default;

    // 获取最佳买价
    virtual int64_t best_bid_tick() const = 0;

    // 获取最佳卖价
    virtual int64_t best_ask_tick() const = 0;

    // 获取指定价格的买单数量
    virtual double bid_qty(int64_t price_tick) const = 0;

    // 获取指定价格的卖单数量
    virtual double ask_qty(int64_t price_tick) const = 0;

    // 获取买单价格列表
    virtual const std::vector<int64_t>& bid_prices() const = 0;

    // 获取卖单价格列表
    virtual const std::vector<int64_t>& ask_prices() const = 0;

    // 获取买单数量列表，与 bid_prices() 返回的价格列表对应
    virtual const std::vector<double>& bid_qtys() const = 0;

    // 获取卖单数量列表，与 ask_prices() 返回的价格列表对应
    virtual const std::vector<double>& ask_qtys() const = 0;

    // 获取tick大小
    virtual double tick_size() const = 0;

    // 获取lot大小
    virtual double lot_size() const = 0;

    // 设置tick大小
    virtual void set_tick_size(double tick_size) = 0;

    // 设置lot大小
    virtual void set_lot_size(double lot_size) = 0;

    // 清空深度
    virtual void clear() = 0;

    // 清除深度
    virtual void clear_depth(side_type side, double clear_upto_price) = 0;

    // 更新买单深度
    // 返回值：(price_tick, prev_best_bid_tick, best_bid_tick, prev_qty, new_qty, timestamp)
    virtual std::tuple<int64_t, int64_t, int64_t, double, double, int64_t>
    update_bid_depth(double price, double qty, int64_t timestamp) = 0;

    // 更新卖单深度
    // 返回值：(price_tick, prev_best_ask_tick, best_ask_tick, prev_qty, new_qty, timestamp)
    virtual std::tuple<int64_t, int64_t, int64_t, double, double, int64_t>
    update_ask_depth(double price, double qty, int64_t timestamp) = 0;

    // 价格转换为tick
    virtual int64_t price_to_tick(double price) const = 0;

    // tick转换为价格
    virtual double tick_to_price(int64_t tick) const = 0;
};

} // namespace cpp_backtest
} // namespace fast_trader_elite
