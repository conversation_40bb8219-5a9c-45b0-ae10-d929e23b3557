#pragma once

#include <array>
#include <limits>
#include <fast_trader_elite/cpp_backtest/market/market_depth.h>
#include <fast_trader_elite/data_model/field.h>

namespace fast_trader_elite {
namespace cpp_backtest {

// 超简单高效的订单簿实现，直接使用depth_market_data的数据
class order_book : public market_depth {
public:
    // 构造函数，默认支持10档深度
    order_book(double tick_size = 0.01, double lot_size = 0.001, size_t depth_levels = 10);
    virtual ~order_book() = default;

    // market_depth接口实现
    int64_t best_bid_tick() const override;
    int64_t best_ask_tick() const override;
    double bid_qty(int64_t price_tick) const override;
    double ask_qty(int64_t price_tick) const override;
    const std::vector<int64_t>& bid_prices() const override;
    const std::vector<int64_t>& ask_prices() const override;
    const std::vector<double>& bid_qtys() const override;
    const std::vector<double>& ask_qtys() const override;
    double tick_size() const override { return tick_size_; }
    double lot_size() const override { return lot_size_; }
    void set_tick_size(double tick_size) override { tick_size_ = tick_size; }
    void set_lot_size(double lot_size) override { lot_size_ = lot_size; }
    void clear() override;
    void clear_depth(side_type side, double clear_upto_price) override;

    std::tuple<int64_t, int64_t, int64_t, double, double, int64_t>
    update_bid_depth(double price, double qty, int64_t timestamp) override;

    std::tuple<int64_t, int64_t, int64_t, double, double, int64_t>
    update_ask_depth(double price, double qty, int64_t timestamp) override;

    int64_t price_to_tick(double price) const override;
    double tick_to_price(int64_t tick) const override;

    // 直接从深度行情快照更新订单簿
    void update_from_snapshot(const depth_market_data_field* depth, int64_t timestamp);

    // 获取当前深度数据
    const depth_market_data_field& get_depth() const { return depth_; }

private:
    double tick_size_;
    double lot_size_;
    size_t depth_levels_;
    depth_market_data_field depth_; // 直接存储深度行情数据
    int64_t timestamp_;

    // 缓存的价格和数量列表
    mutable std::vector<int64_t> bid_price_ticks_;
    mutable std::vector<int64_t> ask_price_ticks_;
    mutable std::vector<double> bid_quantities_;
    mutable std::vector<double> ask_quantities_;
    mutable bool cache_valid_; // 缓存是否有效

    // 更新缓存
    void update_cache() const;
};

} // namespace cpp_backtest
} // namespace fast_trader_elite
