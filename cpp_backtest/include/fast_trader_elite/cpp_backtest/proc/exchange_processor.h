#pragma once

#include <unordered_map>
#include <unordered_set>
#include <fast_trader_elite/cpp_backtest/proc/processor.h>
#include <fast_trader_elite/cpp_backtest/order_bus.h>
#include <fast_trader_elite/cpp_backtest/market/market_depth.h>
#include <fast_trader_elite/cpp_backtest/market/order_book.h>
#include <fast_trader_elite/cpp_backtest/models/fee_model.h>
#include <fast_trader_elite/cpp_backtest/models/queue_model.h>
#include <fast_trader_elite/cpp_backtest/models/latency_model.h>
#include <fast_trader_elite/cpp_backtest/state.h>
#include <span>

namespace fast_trader_elite {
namespace cpp_backtest {

// 交易所处理器基类
class exchange_processor : public processor {
public:
    exchange_processor(market_depth* depth, fee_model* fee_model, queue_model* queue_model, latency_model* order_latency);
    virtual ~exchange_processor() = default;

    // processor接口实现
    backtest_error process(const record_header& header, const std::vector<uint8_t>& data) override;
    backtest_error process_recv_order(int64_t timestamp) override;
    int64_t earliest_recv_order_timestamp() const override;
    int64_t earliest_send_order_timestamp() const override;

    // 设置订单总线
    void set_order_buses(order_bus* orders_to, order_bus* orders_from) {
        orders_to_ = orders_to;
        orders_from_ = orders_from;
    }

    // 获取订单总线
    order_bus* get_orders_to() { return orders_to_; }
    order_bus* get_orders_from() { return orders_from_; }

    // 获取市场深度
    market_depth* get_depth() const { return depth_; }

    // 获取所有订单
    const std::unordered_map<int64_t, internal_order>& get_orders() const { return orders_; }

    // 获取状态值
    const state_values& get_state_values(uint16_t instrument_idx) const { return state_.get_values(instrument_idx); }

    // 获取状态管理器
    state* get_state() { return &state_; }

protected:
    market_depth* depth_;
    fee_model* fee_model_;
    queue_model* queue_model_;
    latency_model* order_latency_;
    std::unordered_map<int64_t, internal_order> orders_;
    order_bus* orders_to_ = nullptr;
    order_bus* orders_from_ = nullptr;
    state state_{10000.0}; // 初始余额为10000

    // 处理深度行情
    virtual void process_depth(const depth_market_data_field* depth);

    // 处理交易所成交
    virtual void process_transaction(const transaction_field* trans) = 0;

    // 处理K线数据
    virtual void process_kline(const kline_market_data_field* kline);

    // 处理订单
    virtual backtest_error process_order(const internal_order& order) = 0;

    // 发送订单回报
    void send_order_report(const internal_order& order);

    // 尝试撮合订单
    virtual void try_match_orders() = 0;

    // 最佳买价更新处理
    virtual void on_best_bid_update(int64_t prev_best_tick, int64_t new_best_tick) = 0;

    // 最佳卖价更新处理
    virtual void on_best_ask_update(int64_t prev_best_tick, int64_t new_best_tick) = 0;

    // 买盘数量变化处理
    virtual void on_bid_qty_change(int64_t price_tick, double prev_qty, double new_qty) = 0;

    // 卖盘数量变化处理
    virtual void on_ask_qty_change(int64_t price_tick, double prev_qty, double new_qty) = 0;

    // 判断是否需要跟踪数量变化
    // 子类可以重写此方法以优化性能
    virtual bool needs_qty_change_tracking() const { return true; }

    // 抽象的成交方法，由子类实现
    virtual void fill(internal_order& order, double fill_price, double fill_quantity, bool is_maker) = 0;
};

} // namespace cpp_backtest
} // namespace fast_trader_elite
