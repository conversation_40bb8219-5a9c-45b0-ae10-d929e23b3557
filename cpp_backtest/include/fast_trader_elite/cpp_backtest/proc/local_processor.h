#pragma once

#include <unordered_map>
#include <fast_trader_elite/cpp_backtest/proc/processor.h>
#include <fast_trader_elite/cpp_backtest/order_bus.h>
#include <fast_trader_elite/cpp_backtest/market/market_depth.h>
#include <fast_trader_elite/cpp_backtest/market/order_book.h>
#include <fast_trader_elite/cpp_backtest/models/latency_model.h>
#include <fast_trader_elite/cpp_backtest/state.h>
#include <span>
#include <i_strategy.h>
#include <i_strategy_ctx.h>

namespace fast_trader_elite {
namespace cpp_backtest {

// 本地处理器
class local_processor : public processor {
public:
    local_processor(market_depth* depth, latency_model* order_latency);
    virtual ~local_processor() = default;

    // processor接口实现
    backtest_error process(const record_header& header, const std::vector<uint8_t>& data) override;
    backtest_error process_recv_order(int64_t timestamp) override;
    int64_t earliest_recv_order_timestamp() const override;
    int64_t earliest_send_order_timestamp() const override;

    // 提交订单
    int64_t submit_order(const order_input_field* input, int64_t current_timestamp);

    // 取消订单
    int64_t cancel_order(const order_action_field* action, int64_t current_timestamp);

    // 设置策略和上下文
    void set_strategy(fast_trader_elite::strategy::i_strategy* strategy,
                     fast_trader_elite::strategy::i_strategy_ctx* ctx);

    // 设置订单总线
    void set_order_buses(order_bus* orders_to, order_bus* orders_from) {
        orders_to_ = orders_to;
        orders_from_ = orders_from;
    }

    // 设置手续费模型
    void set_fee_model(fee_model* fee_model) {
        fee_model_ = fee_model;
        state_.set_fee_model(fee_model);
    }

    // 获取状态值
    const state_values& get_state_values(uint16_t instrument_idx) const {
        return state_.get_values(instrument_idx);
    }

    // 计算总权益（净值）
    double get_equity(uint16_t instrument_idx, double price) const {
        return state_.equity(instrument_idx, price);
    }

    // 获取状态管理器
    state* get_state() {
        return &state_;
    }

    // 获取订单总线
    order_bus* get_orders_to() { return orders_to_; }
    order_bus* get_orders_from() { return orders_from_; }

    // 获取市场深度
    market_depth* get_depth() const { return depth_; }

    // 获取所有订单
    const std::unordered_map<int64_t, internal_order>& get_orders() const { return orders_; }

    // 获取最近的交易
    const std::vector<internal_trade>& get_last_trades() const { return last_trades_; }

    // 清除最近的交易
    void clear_last_trades() { last_trades_.clear(); }

    // 处理K线请求
    void process_get_kline(const md_kline_req_field* f, int request_id);

    // 处理成交请求
    void process_get_recent_transaction(const transaction_req_field* f, int request_id);

    // 处理持仓请求
    void process_get_position(const position_req_field* f, int request_id);

    // 处理钱包余额请求
    void process_get_wallet_balance(const wallet_balance_req_field* f, int request_id);

private:
    market_depth* depth_;
    latency_model* order_latency_;
    fast_trader_elite::strategy::i_strategy* strategy_;
    fast_trader_elite::strategy::i_strategy_ctx* ctx_;
    std::unordered_map<int64_t, internal_order> orders_;
    int64_t next_order_id_;
    order_bus* orders_to_ = nullptr;
    order_bus* orders_from_ = nullptr;
    std::vector<internal_trade> last_trades_; // 最近的交易列表
    fee_model* fee_model_ = nullptr; // 手续费模型
    state state_{10000.0}; // 状态管理器，初始余额为10000

    // 处理深度行情
    void process_depth(const depth_market_data_field* depth);

    // 处理交易所成交
    void process_transaction(const transaction_field* trans);

    // 处理K线数据
    void process_kline(const kline_market_data_field* kline);

    // 处理订单回报
    void process_order_report(const internal_order& order);

    // 触发策略回调
    void trigger_order_callback(const internal_order& order);
    void trigger_trade_callback(const internal_trade& trade);
};

} // namespace cpp_backtest
} // namespace fast_trader_elite
