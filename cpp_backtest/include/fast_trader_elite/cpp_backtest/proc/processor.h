#pragma once

#include <optional>
#include <utility>
#include <vector>
#include <span>
#include <fast_trader_elite/cpp_backtest/types.h>

namespace fast_trader_elite {
namespace cpp_backtest {

// 处理器接口
class processor {
public:
    virtual ~processor() = default;

    // 处理记录 - 使用vector接口作为主要接口
    virtual backtest_error process(const record_header& header, const std::vector<uint8_t>& data) = 0;

    // 高效版本，使用std::span避免数据复制
    virtual backtest_error process(const record_header& header, std::span<const uint8_t> data) {
        // 默认实现：创建临时vector并调用vector版本
        // 注意：这不是最高效的实现，派生类应该覆盖此方法以避免复制
        std::vector<uint8_t> temp_data(data.begin(), data.end());
        return process(header, temp_data);
    }

    // 处理收到的订单
    virtual backtest_error process_recv_order(int64_t timestamp) = 0;

    // 返回此处理器将接收订单的最早时间戳
    virtual int64_t earliest_recv_order_timestamp() const = 0;

    // 返回此处理器发送的订单将被相应处理器接收的最早时间戳
    virtual int64_t earliest_send_order_timestamp() const = 0;

    // 获取当前时间戳
    int64_t current_timestamp() const { return current_timestamp_; }

    // 设置当前时间戳
    void set_current_timestamp(int64_t timestamp) { current_timestamp_ = timestamp; }

protected:
    int64_t current_timestamp_ = 0;
};

} // namespace cpp_backtest
} // namespace fast_trader_elite
