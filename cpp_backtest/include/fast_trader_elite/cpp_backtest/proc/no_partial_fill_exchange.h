#pragma once

#include <unordered_map>
#include <unordered_set>
#include <vector>
#include <fast_trader_elite/cpp_backtest/proc/exchange_processor.h>

namespace fast_trader_elite {
namespace cpp_backtest {

// 全部成交交易所
class no_partial_fill_exchange : public exchange_processor {
public:
    no_partial_fill_exchange(market_depth* depth, fee_model* fee_model, queue_model* queue_model, latency_model* order_latency);
    virtual ~no_partial_fill_exchange() = default;

protected:
    // 处理订单
    backtest_error process_order(const internal_order& order) override;

    // 尝试撮合订单
    void try_match_orders() override;

    // 处理交易所成交
    void process_transaction(const transaction_field* trans) override;

private:
    // 处理新订单
    backtest_error process_new_order(internal_order& order);

    // 处理取消订单
    backtest_error process_cancel_order(internal_order& order);

    // 处理修改订单
    backtest_error process_modify_order(internal_order& order);

    // 检查买单是否可以成交
    backtest_error check_if_buy_filled(internal_order& order, int64_t price_tick, double qty);

    // 检查卖单是否可以成交
    backtest_error check_if_sell_filled(internal_order& order, int64_t price_tick, double qty);

    // 实现抽象的成交方法
    void fill(internal_order& order, double fill_price, double fill_quantity, bool is_maker) override;

    // 最佳买价更新处理
    void on_best_bid_update(int64_t prev_best_tick, int64_t new_best_tick) override;

    // 最佳卖价更新处理
    void on_best_ask_update(int64_t prev_best_tick, int64_t new_best_tick) override;

    // 买盘数量变化处理
    void on_bid_qty_change(int64_t price_tick, double prev_qty, double new_qty) override;

    // 卖盘数量变化处理
    void on_ask_qty_change(int64_t price_tick, double prev_qty, double new_qty) override;

    // 全部成交交易所不需要跟踪数量变化
    bool needs_qty_change_tracking() const override { return false; }

    // 买单价格层
    std::unordered_map<int64_t, std::unordered_set<int64_t>> buy_orders_;

    // 卖单价格层
    std::unordered_map<int64_t, std::unordered_set<int64_t>> sell_orders_;

    // 已成交订单ID
    std::vector<int64_t> filled_orders_;

    // 移除已成交订单
    void remove_filled_orders();
};

} // namespace cpp_backtest
} // namespace fast_trader_elite
