#pragma once

#include <vector>
#include <string>
#include <unordered_map>
#include <optional>
#include <cmath>
#include <fast_trader_elite/cpp_backtest/types.h>
#include <fast_trader_elite/cpp_backtest/position_cycle.h>

namespace fast_trader_elite {
namespace cpp_backtest {

// 回测数据，用于存储和提供导出所需的所有数据
class backtest_data {
public:
    backtest_data();
    ~backtest_data() = default;

    // 添加记录项
    void add_record(uint16_t asset_no, const record_item& item);

    // 获取所有记录
    const std::unordered_map<uint16_t, std::vector<record_item>>& get_records() const;

    // 获取特定资产的记录
    const std::vector<record_item>& get_asset_records(uint16_t asset_no) const;

    // 添加成交记录
    void add_trade(uint16_t asset_no, const internal_trade& trade);

    // 获取所有成交记录
    const std::unordered_map<uint16_t, std::vector<internal_trade>>& get_trades() const;

    // 获取特定资产的成交记录
    const std::vector<internal_trade>& get_asset_trades(uint16_t asset_no) const;

    // 获取资产数量
    std::size_t num_assets() const;

    // 清空所有数据
    void clear();

    // 获取开始时间戳
    int64_t get_start_timestamp(uint16_t asset_no) const;

    // 获取结束时间戳
    int64_t get_end_timestamp(uint16_t asset_no) const;

    // 获取总交易天数
    double get_trading_days(uint16_t asset_no) const;

    // 获取初始权益
    double get_initial_equity(uint16_t asset_no) const;

    // 获取最终权益
    double get_final_equity(uint16_t asset_no) const;

    // 获取最大权益
    double get_max_equity(uint16_t asset_no) const;

    // 获取最小权益
    double get_min_equity(uint16_t asset_no) const;

    // 获取总收益率
    double get_return(uint16_t asset_no) const;

    // 获取年化收益率
    double get_annual_return(uint16_t asset_no) const;

    // 计算最大回撤
    double get_max_drawdown(uint16_t asset_no) const;

    // 计算夏普比率
    double get_sharpe_ratio(uint16_t asset_no, double risk_free_rate = 0.0) const;

    // 计算索提诺比率
    double get_sortino_ratio(uint16_t asset_no, double risk_free_rate = 0.0) const;

    // 计算收益回撤比
    double get_return_over_mdd(uint16_t asset_no) const;

    // 计算收益交易比
    double get_return_over_trade(uint16_t asset_no) const;

    // 计算平均持仓时间（秒）
    double get_avg_position_time(uint16_t asset_no) const;

    // 计算胜率
    double get_win_rate(uint16_t asset_no) const;

    // 计算盈亏比
    double get_profit_loss_ratio(uint16_t asset_no) const;

    // 计算日均交易金额（USDT）
    double get_daily_trading_value(uint16_t asset_no) const;

    // 计算最大杠杆
    double get_max_leverage(uint16_t asset_no) const;

    // 获取总交易次数
    int64_t get_total_trades(uint16_t asset_no) const;

    // 获取总交易金额（USDT）
    double get_total_trading_value(uint16_t asset_no) const;

    // 获取仓位周期
    std::vector<position_cycle> get_position_cycles(uint16_t asset_no) const;

    // 获取仓位周期数量
    int64_t get_position_cycles_count(uint16_t asset_no) const;

    // 获取仓位周期统计信息
    cycle_statistics get_cycle_statistics(uint16_t asset_no) const;

private:
    std::unordered_map<uint16_t, std::vector<record_item>> records_; // 记录数据，按资产编号索引
    std::unordered_map<uint16_t, std::vector<internal_trade>> trades_; // 成交记录，按资产编号索引
    mutable std::unordered_map<uint16_t, std::vector<position_cycle>> cycles_; // 仓位周期缓存，按资产编号索引
    mutable std::unordered_map<uint16_t, cycle_statistics> cycle_stats_; // 仓位周期统计信息缓存，按资产编号索引

    // 辅助函数：计算每日收益率序列
    std::vector<double> calculate_daily_returns(uint16_t asset_no) const;

    // 辅助函数：计算持仓时间序列
    std::vector<double> calculate_position_times(uint16_t asset_no) const;

    // 辅助函数：获取空的记录向量的引用
    static const std::vector<record_item>& get_empty_records();

    // 辅助函数：获取空的成交向量的引用
    static const std::vector<internal_trade>& get_empty_trades();
};

} // namespace cpp_backtest
} // namespace fast_trader_elite
