#pragma once

#include <fast_trader_elite/cpp_backtest/parameter_optimizer/optimizer.h>
#include <random>
#include <thread>
#include <queue>
#include <mutex>
#include <condition_variable>
#include <atomic>
#include <vector>
#include <algorithm>

namespace fast_trader_elite {
namespace cpp_backtest {

/**
 * @brief 树形Parzen估计器优化器（Tree-structured Parzen Estimator Optimizer）
 * 
 * TPE是一种基于概率模型的优化算法，特别适合评估成本高的场景。
 * 它通过构建两个概率分布（一个针对好的样本，一个针对差的样本），
 * 然后选择使得好样本概率与差样本概率比值最大的点作为下一个评估点。
 * 
 * 这是一个简化版的TPE实现，使用了基本的核密度估计（KDE）来建模概率分布。
 */
class tpe_optimizer : public optimizer {
public:
    /**
     * @brief 构造函数
     * 
     * @param space 参数空间
     * @param target 优化目标
     */
    tpe_optimizer(const parameter_space& space, 
                 const std::shared_ptr<optimization_target>& target);
    
    /**
     * @brief 析构函数
     */
    ~tpe_optimizer() override;
    
    /**
     * @brief 运行优化
     * 
     * @return std::vector<optimization_result> 优化结果列表
     */
    std::vector<optimization_result> run() override;
    
private:
    /**
     * @brief 工作线程函数
     */
    void worker_thread();
    
    /**
     * @brief 生成初始样本
     * 
     * @param n_initial_points 初始样本数量
     * @return std::vector<std::unordered_map<std::string, parameter_value>> 初始样本
     */
    std::vector<std::unordered_map<std::string, parameter_value>> generate_initial_points(int n_initial_points) const;
    
    /**
     * @brief 找到下一个评估点
     * 
     * @param gamma 分位数参数，用于将样本分为好样本和差样本
     * @return std::unordered_map<std::string, parameter_value> 下一个评估点
     */
    std::unordered_map<std::string, parameter_value> find_next_point(double gamma = 0.25);
    
    /**
     * @brief 计算核密度估计（KDE）
     * 
     * @param x 评估点
     * @param samples 样本点
     * @param bandwidth 带宽参数
     * @return double KDE值
     */
    double kde(const std::unordered_map<std::string, parameter_value>& x,
              const std::vector<std::unordered_map<std::string, parameter_value>>& samples,
              double bandwidth) const;
    
    /**
     * @brief 计算两个参数组合之间的距离
     * 
     * @param p1 参数组合1
     * @param p2 参数组合2
     * @return double 距离值
     */
    double distance(const std::unordered_map<std::string, parameter_value>& p1,
                   const std::unordered_map<std::string, parameter_value>& p2) const;
    
    /**
     * @brief 生成候选点
     * 
     * @param n_candidates 候选点数量
     * @return std::vector<std::unordered_map<std::string, parameter_value>> 候选点列表
     */
    std::vector<std::unordered_map<std::string, parameter_value>> generate_candidates(int n_candidates) const;
    
    // 停止标志
    std::atomic<bool> stop_{false};
    
    // 参数队列
    std::queue<std::unordered_map<std::string, parameter_value>> param_queue_;
    
    // 互斥锁
    std::mutex mutex_;
    
    // 条件变量
    std::condition_variable cv_;
    
    // 工作线程
    std::vector<std::thread> workers_;
    
    // 总任务数
    int total_tasks_ = 0;
    
    // 已完成任务数
    std::atomic<int> completed_tasks_{0};
    
    // 初始样本数量
    int n_initial_points_ = 10;
    
    // 候选点数量
    int n_candidates_ = 100;
    
    // 带宽参数
    double bandwidth_ = 0.1;
    
    // 随机数生成器
    mutable std::mt19937 gen_{std::random_device{}()};
};

} // namespace cpp_backtest
} // namespace fast_trader_elite
