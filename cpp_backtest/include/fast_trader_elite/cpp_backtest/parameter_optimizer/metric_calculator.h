#pragma once

#include <string>
#include <vector>
#include <functional>
#include <unordered_map>
#include <memory>
#include <fast_trader_elite/cpp_backtest/backtest_engine.h>
#include <fast_trader_elite/cpp_backtest/backtest_data.h>

namespace fast_trader_elite {
namespace cpp_backtest {

// 指标计算函数类型
using metric_function = std::function<double(const backtest_data&, uint16_t)>;

// 指标计算器类
class metric_calculator {
public:
    metric_calculator();

    // 注册内置指标
    void register_built_in_metrics();

    // 注册自定义指标
    void register_metric(const std::string& name, metric_function func);

    // 计算指定指标
    double calculate(const std::string& name, const backtest_data& data, uint16_t instrument_idx = 0) const;

    // 计算多个指标
    std::unordered_map<std::string, double> calculate_all(
        const std::vector<std::string>& metrics,
        const backtest_data& data,
        uint16_t instrument_idx = 0) const;

    // 检查指标是否存在
    bool has_metric(const std::string& name) const;

    // 获取所有可用指标名称
    std::vector<std::string> get_available_metrics() const;

private:
    // 指标函数映射
    std::unordered_map<std::string, metric_function> metrics_;

    // 计算回报率
    static double calculate_return(const backtest_data& data, uint16_t instrument_idx);

    // 计算夏普比率
    static double calculate_sharpe_ratio(const backtest_data& data, uint16_t instrument_idx);

    // 计算索提诺比率
    static double calculate_sortino_ratio(const backtest_data& data, uint16_t instrument_idx);

    // 计算最大回撤
    static double calculate_max_drawdown(const backtest_data& data, uint16_t instrument_idx);

    // 计算收益回撤比
    static double calculate_return_over_mdd(const backtest_data& data, uint16_t instrument_idx);

    // 计算收益交易比
    static double calculate_return_over_trade(const backtest_data& data, uint16_t instrument_idx);

    // 计算平均持仓时间
    static double calculate_avg_position_time(const backtest_data& data, uint16_t instrument_idx);

    // 计算胜率
    static double calculate_win_rate(const backtest_data& data, uint16_t instrument_idx);

    // 计算盈亏比
    static double calculate_profit_loss_ratio(const backtest_data& data, uint16_t instrument_idx);

    // 计算年化收益率
    static double calculate_annual_return(const backtest_data& data, uint16_t instrument_idx);

    // 计算日均交易金额（USDT）
    static double calculate_daily_trading_value(const backtest_data& data, uint16_t instrument_idx);

    // 计算最大杠杆
    static double calculate_max_leverage(const backtest_data& data, uint16_t instrument_idx);

    // 计算总交易次数
    static double calculate_total_trades(const backtest_data& data, uint16_t instrument_idx);

    // 计算日均交易次数
    static double calculate_daily_trades(const backtest_data& data, uint16_t instrument_idx);

    // 计算总交易金额（USDT）
    static double calculate_total_trading_value(const backtest_data& data, uint16_t instrument_idx);

    // 计算总交易周期数
    static double calculate_position_cycles(const backtest_data& data, uint16_t instrument_idx);
};

// 全局单例
metric_calculator& get_metric_calculator();

} // namespace cpp_backtest
} // namespace fast_trader_elite
