#pragma once

#include <string>
#include <vector>
#include <unordered_map>
#include <fast_trader_elite/cpp_backtest/parameter_optimizer/optimizer.h>

namespace fast_trader_elite {
namespace cpp_backtest {

// 结果报告生成器
class result_reporter {
public:
    result_reporter() = default;

    // 设置输出路径
    void set_output_path(const std::string& path);

    // 设置约束条件
    void set_constraints(const std::vector<constraint>& constraints);

    // 生成结果报告
    bool generate_report(const std::vector<optimization_result>& results);

    // 生成最佳参数配置文件
    bool generate_best_config(const optimization_result& best_result, const std::string& base_config_path);

    // 生成参数敏感性分析
    bool generate_sensitivity_analysis(const std::vector<optimization_result>& results);

private:
    // 输出路径
    std::string output_path_;

    // 约束条件
    std::vector<constraint> constraints_;

    // 生成CSV报告
    bool generate_csv_report(const std::vector<optimization_result>& results);

    // 生成JSON报告
    bool generate_json_report(const std::vector<optimization_result>& results);

    // 生成HTML报告
    bool generate_html_report(const std::vector<optimization_result>& results);
};

} // namespace cpp_backtest
} // namespace fast_trader_elite
