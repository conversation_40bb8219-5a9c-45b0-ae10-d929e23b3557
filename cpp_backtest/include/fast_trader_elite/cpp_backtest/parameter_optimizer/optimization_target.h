#pragma once

#include <string>
#include <vector>
#include <memory>
#include <unordered_map>
#include <functional>
#include <nlohmann_json/json.hpp>

namespace fast_trader_elite {
namespace cpp_backtest {

// 指标转换类型枚举
enum class transform_type {
    IDENTITY,   // 保持原值
    INVERSE,    // 取倒数 (1/x)
    NEGATIVE,   // 取负值 (-x)
    LOG,        // 取对数
    EXP,        // 取指数
    THRESHOLD   // 阈值函数
};

// 归一化类型枚举
enum class normalization_type {
    NONE,       // 不归一化
    MIN_MAX,    // Min-Max归一化
    Z_SCORE,    // Z-Score归一化
    PERCENTILE, // 百分位数归一化
    RANK        // 排名归一化
};

// 指标组件结构体
struct metric_component {
    // 指标名称
    std::string metric;

    // 权重
    double weight = 1.0;

    // 转换类型
    transform_type transform = transform_type::IDENTITY;

    // 转换参数（如阈值值）
    double transform_param = 0.0;

    // 描述
    std::string description;
};

// 约束条件结构体
struct constraint {
    // 指标名称
    std::string metric;

    // 操作符（"<", "<=", "==", ">=", ">"）
    std::string op;

    // 阈值
    double value;

    // 描述
    std::string description;

    // 检查是否满足约束
    bool check(double metric_value) const;
};

// 优化目标基类
class optimization_target {
public:
    virtual ~optimization_target() = default;

    // 计算目标函数得分
    virtual double calculate_score(const std::unordered_map<std::string, double>& metrics) const = 0;

    // 检查约束条件
    virtual bool check_constraints(const std::unordered_map<std::string, double>& metrics) const;

    // 获取所需的指标列表
    virtual std::vector<std::string> get_required_metrics() const = 0;

    // 添加约束条件
    void add_constraint(const constraint& c);

    // 获取约束条件列表
    const std::vector<constraint>& get_constraints() const;

    // 设置是否归一化
    void set_normalization(normalization_type type);

    // 获取归一化类型
    normalization_type get_normalization_type() const;

    // 更新归一化参数
    virtual void update_normalization_params(const std::vector<std::unordered_map<std::string, double>>& all_metrics);

protected:
    // 约束条件列表
    std::vector<constraint> constraints_;

    // 归一化类型
    normalization_type normalization_ = normalization_type::NONE;

    // 归一化参数
    std::unordered_map<std::string, std::pair<double, double>> normalization_params_;

    // 排名数据 - 用于RANK归一化
    // 键是指标名称，值是一个映射：从原始值到排名的映射
    std::unordered_map<std::string, std::unordered_map<double, double>> rank_data_;

    // 应用转换函数
    double apply_transform(double value, transform_type transform, double param = 0.0) const;

    // 应用归一化
    double normalize(const std::string& metric, double value) const;
};

// 单一指标优化目标
class single_metric_target : public optimization_target {
public:
    explicit single_metric_target(const std::string& metric_name);

    double calculate_score(const std::unordered_map<std::string, double>& metrics) const override;

    std::vector<std::string> get_required_metrics() const override;

private:
    std::string metric_name_;
};

// 组合指标优化目标
class composite_target : public optimization_target {
public:
    explicit composite_target(const std::vector<metric_component>& components);

    double calculate_score(const std::unordered_map<std::string, double>& metrics) const override;

    std::vector<std::string> get_required_metrics() const override;

private:
    std::vector<metric_component> components_;
};

// 从JSON配置创建优化目标
std::shared_ptr<optimization_target> create_target_from_config(const nlohmann::json& config);

} // namespace cpp_backtest
} // namespace fast_trader_elite
