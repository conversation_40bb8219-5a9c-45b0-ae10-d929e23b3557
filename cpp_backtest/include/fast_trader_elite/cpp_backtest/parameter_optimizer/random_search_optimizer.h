#pragma once

#include <fast_trader_elite/cpp_backtest/parameter_optimizer/optimizer.h>
#include <random>
#include <thread>
#include <queue>
#include <mutex>
#include <condition_variable>
#include <atomic>

namespace fast_trader_elite {
namespace cpp_backtest {

/**
 * @brief 随机搜索优化器
 * 
 * 随机搜索是一种简单但有效的优化算法，它从参数空间中随机采样点进行评估。
 * 相比网格搜索，随机搜索在高维参数空间中更有效，因为它不需要评估所有可能的参数组合。
 */
class random_search_optimizer : public optimizer {
public:
    /**
     * @brief 构造函数
     * 
     * @param space 参数空间
     * @param target 优化目标
     */
    random_search_optimizer(const parameter_space& space, 
                           const std::shared_ptr<optimization_target>& target);
    
    /**
     * @brief 析构函数
     */
    ~random_search_optimizer() override;
    
    /**
     * @brief 运行优化
     * 
     * @return std::vector<optimization_result> 优化结果列表
     */
    std::vector<optimization_result> run() override;
    
private:
    /**
     * @brief 工作线程函数
     */
    void worker_thread();
    
    // 停止标志
    std::atomic<bool> stop_{false};
    
    // 参数队列
    std::queue<std::unordered_map<std::string, parameter_value>> param_queue_;
    
    // 互斥锁
    std::mutex mutex_;
    
    // 条件变量
    std::condition_variable cv_;
    
    // 工作线程
    std::vector<std::thread> workers_;
    
    // 总任务数
    int total_tasks_ = 0;
    
    // 已完成任务数
    std::atomic<int> completed_tasks_{0};
};

} // namespace cpp_backtest
} // namespace fast_trader_elite
