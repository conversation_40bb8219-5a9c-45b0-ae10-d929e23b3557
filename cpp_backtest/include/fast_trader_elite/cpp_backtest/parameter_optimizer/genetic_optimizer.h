#pragma once

#include <fast_trader_elite/cpp_backtest/parameter_optimizer/optimizer.h>
#include <random>
#include <thread>
#include <queue>
#include <mutex>
#include <condition_variable>
#include <atomic>
#include <vector>
#include <algorithm>
#include <functional>

namespace fast_trader_elite {
namespace cpp_backtest {

/**
 * @brief 遗传算法优化器
 *
 * 遗传算法是一种基于自然选择和遗传学原理的优化算法，特别适合复杂参数空间和多目标优化问题。
 * 它通过模拟自然选择过程，包括选择、交叉和变异操作，来搜索最优解。
 */
class genetic_optimizer : public optimizer {
public:
    /**
     * @brief 构造函数
     *
     * @param space 参数空间
     * @param target 优化目标
     */
    genetic_optimizer(const parameter_space& space,
                     const std::shared_ptr<optimization_target>& target);

    /**
     * @brief 析构函数
     */
    ~genetic_optimizer() override;

    /**
     * @brief 运行优化
     *
     * @return std::vector<optimization_result> 优化结果列表
     */
    std::vector<optimization_result> run() override;

    /**
     * @brief 设置种群大小
     *
     * @param population_size 种群大小
     */
    void set_population_size(int population_size);

    /**
     * @brief 设置精英数量
     *
     * @param elite_count 精英数量
     */
    void set_elite_count(int elite_count);

    /**
     * @brief 设置交叉概率
     *
     * @param crossover_rate 交叉概率
     */
    void set_crossover_rate(double crossover_rate);

    /**
     * @brief 设置变异概率
     *
     * @param mutation_rate 变异概率
     */
    void set_mutation_rate(double mutation_rate);

    /**
     * @brief 设置最大无进展迭代次数
     *
     * @param max_stagnation_iterations 最大无进展迭代次数
     */
    void set_max_stagnation_iterations(int max_stagnation_iterations);

    /**
     * @brief 设置是否使用自适应变异率和交叉率
     *
     * @param use_adaptive_rates 是否使用自适应变异率和交叉率
     */
    void set_use_adaptive_rates(bool use_adaptive_rates);

private:
    /**
     * @brief 工作线程函数
     */
    void worker_thread();

    /**
     * @brief 生成初始种群
     *
     * @return std::vector<std::unordered_map<std::string, parameter_value>> 初始种群
     */
    std::vector<std::unordered_map<std::string, parameter_value>> generate_initial_population() const;

    /**
     * @brief 选择操作
     *
     * @param population 当前种群
     * @param scores 种群中每个个体的得分
     * @return std::vector<std::unordered_map<std::string, parameter_value>> 选择后的种群
     */
    std::vector<std::unordered_map<std::string, parameter_value>> selection(
        const std::vector<std::unordered_map<std::string, parameter_value>>& population,
        const std::vector<double>& scores) const;

    /**
     * @brief 交叉操作
     *
     * @param parent1 父代1
     * @param parent2 父代2
     * @return std::pair<std::unordered_map<std::string, parameter_value>, std::unordered_map<std::string, parameter_value>> 子代
     */
    std::pair<std::unordered_map<std::string, parameter_value>, std::unordered_map<std::string, parameter_value>> crossover(
        const std::unordered_map<std::string, parameter_value>& parent1,
        const std::unordered_map<std::string, parameter_value>& parent2) const;

    /**
     * @brief 变异操作
     *
     * @param individual 个体
     * @return std::unordered_map<std::string, parameter_value> 变异后的个体
     */
    std::unordered_map<std::string, parameter_value> mutation(
        const std::unordered_map<std::string, parameter_value>& individual) const;

    /**
     * @brief 生成随机参数值
     *
     * @param param_def 参数定义
     * @return parameter_value 随机参数值
     */
    parameter_value generate_random_value(const parameter_definition& param_def) const;

    /**
     * @brief 轮盘赌选择
     *
     * @param scores 得分列表
     * @return size_t 选中的索引
     */
    size_t roulette_wheel_selection(const std::vector<double>& scores) const;

    /**
     * @brief 将个体转换为字符串，用于去重
     *
     * @param individual 个体
     * @return std::string 个体的字符串表示
     */
    std::string individual_to_string(const std::unordered_map<std::string, parameter_value>& individual) const;

    /**
     * @brief 计算种群多样性
     *
     * @param population 种群
     * @return double 种群多样性，范围[0,1]
     */
    double calculate_population_diversity(const std::vector<std::unordered_map<std::string, parameter_value>>& population) const;

    /**
     * @brief 自适应调整变异率和交叉率
     *
     * @param diversity 种群多样性
     * @param stagnation_ratio 停滞比例（当前停滞代数/最大停滞代数）
     */
    void adapt_rates(double diversity, double stagnation_ratio);

    // 停止标志
    std::atomic<bool> stop_{false};

    // 参数队列
    std::queue<std::unordered_map<std::string, parameter_value>> param_queue_;

    // 互斥锁
    std::mutex mutex_;

    // 条件变量
    std::condition_variable cv_;

    // 工作线程
    std::vector<std::thread> workers_;

    // 总任务数
    int total_tasks_ = 0;

    // 已完成任务数
    std::atomic<int> completed_tasks_{0};

    // 种群大小
    int population_size_ = 50;

    // 精英数量
    int elite_count_ = 5;

    // 交叉概率
    double crossover_rate_ = 0.8;
    double initial_crossover_rate_ = 0.8;

    // 变异概率
    double mutation_rate_ = 0.1;
    double initial_mutation_rate_ = 0.1;

    // 是否使用自适应变异率和交叉率
    bool use_adaptive_rates_ = true;

    // 最大无进展迭代次数
    int max_stagnation_iterations_ = 30;

    // 当前无进展迭代次数
    int stagnation_iterations_ = 0;

    // 最佳得分
    double best_score_ = std::numeric_limits<double>::lowest();

    // 种群多样性
    double population_diversity_ = 1.0;

    // 随机数生成器
    mutable std::mt19937 gen_{std::random_device{}()};
};

} // namespace cpp_backtest
} // namespace fast_trader_elite
