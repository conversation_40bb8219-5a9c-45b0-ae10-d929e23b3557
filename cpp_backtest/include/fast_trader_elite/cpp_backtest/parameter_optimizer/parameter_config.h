#pragma once

#include <string>
#include <vector>
#include <memory>
#include <unordered_map>
#include <optional>
#include <nlohmann_json/json.hpp>
#include <fast_trader_elite/cpp_backtest/parameter_optimizer/parameter_space.h>
#include <fast_trader_elite/cpp_backtest/parameter_optimizer/optimization_target.h>

namespace fast_trader_elite {
namespace cpp_backtest {

// 遗传算法配置
struct genetic_config {
    int population_size = 50;    // 种群大小
    int elite_count = 5;         // 精英数量
    double crossover_rate = 0.8; // 交叉概率
    double mutation_rate = 0.1;  // 变异概率
};

// 参数优化配置类
class parameter_config {
public:
    parameter_config() = default;

    // 从文件加载配置
    bool load_from_file(const std::string& file_path);

    // 获取基础配置路径
    std::string get_base_config_path() const;

    // 获取回测配置路径
    std::string get_backtest_config_path() const;

    // 获取输出路径
    std::string get_output_path() const;

    // 获取优化算法
    std::string get_optimization_algorithm() const;

    // 获取最大迭代次数
    int get_max_iterations() const;

    // 获取并行任务数
    int get_parallel_jobs() const;

    // 获取参数空间
    parameter_space get_parameter_space() const;

    // 获取优化目标
    std::shared_ptr<optimization_target> get_optimization_target() const;

    // 获取原始配置
    const nlohmann::json& get_raw_config() const;

    // 获取遗传算法配置
    std::optional<genetic_config> get_genetic_config() const;

private:
    // 配置文件路径
    std::string file_path_;

    // 原始配置
    nlohmann::json config_;

    // 基础配置路径
    std::string base_config_path_;

    // 回测配置路径
    std::string backtest_config_path_;

    // 输出路径
    std::string output_path_;

    // 优化算法
    std::string optimization_algorithm_;

    // 最大迭代次数
    int max_iterations_ = 1000;

    // 并行任务数
    int parallel_jobs_ = 1;

    // 解析配置
    bool parse_config();
};

} // namespace cpp_backtest
} // namespace fast_trader_elite
