#pragma once

#include <string>
#include <memory>
#include <vector>
#include <functional>
#include <fast_trader_elite/cpp_backtest/parameter_optimizer/parameter_config.h>
#include <fast_trader_elite/cpp_backtest/parameter_optimizer/optimizer.h>
#include <fast_trader_elite/cpp_backtest/parameter_optimizer/result_reporter.h>
#include <fast_trader_elite/cpp_backtest/backtest_engine.h>

namespace fast_trader_elite {
namespace cpp_backtest {

// 优化控制器类
class optimization_controller {
public:
    optimization_controller();

    // 析构函数
    ~optimization_controller();

    // 加载配置
    bool load_config(const std::string& config_path);

    // 运行优化
    bool run();

    // 设置进度回调函数
    void set_progress_callback(std::function<void(int current, int total, const optimization_result&)> callback);

    // 获取最佳结果
    optimization_result get_best_result() const;

    // 获取所有结果
    const std::vector<optimization_result>& get_all_results() const;

private:
    // 参数配置
    parameter_config config_;

    // 优化器
    std::unique_ptr<optimizer> optimizer_;

    // 结果报告生成器
    result_reporter reporter_;

    // 优化结果
    std::vector<optimization_result> results_;

    // 进度回调函数
    std::function<void(int current, int total, const optimization_result&)> progress_callback_;

    // 评估参数组合
    optimization_result evaluate_parameters(const std::unordered_map<std::string, parameter_value>& params);

    // 创建优化器
    bool create_optimizer();

    // 清理临时文件
    void cleanup_temp_files();

    // 临时文件列表
    std::vector<std::string> temp_files_;
};

} // namespace cpp_backtest
} // namespace fast_trader_elite
