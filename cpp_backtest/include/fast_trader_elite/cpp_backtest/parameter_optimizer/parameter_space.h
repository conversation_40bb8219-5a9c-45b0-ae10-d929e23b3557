#pragma once

#include <string>
#include <vector>
#include <variant>
#include <unordered_map>
#include <nlohmann_json/json.hpp>

namespace fast_trader_elite {
namespace cpp_backtest {

// 参数值类型（支持多种类型）
using parameter_value = std::variant<int, double, bool, std::string>;

// 参数类型枚举
enum class parameter_type {
    INTEGER,
    FLOAT,
    BOOLEAN,
    STRING,
    ENUM
};

// 参数定义结构体
struct parameter_definition {
    // 参数名称（直接对应策略配置中的参数名）
    std::string name;

    // 参数类型
    parameter_type type;

    // 参数描述
    std::string description;

    // 数值型参数的最小值
    parameter_value min_value;

    // 数值型参数的最大值
    parameter_value max_value;

    // 数值型参数的步长
    parameter_value step;

    // 枚举型参数的可选值列表
    std::vector<parameter_value> values;

    // 是否需要优化（如果为false，则使用默认值）
    bool optimize = true;

    // 默认值
    parameter_value default_value;
};

// 参数空间类
class parameter_space {
public:
    parameter_space() = default;

    // 从JSON配置构造
    explicit parameter_space(const nlohmann::json& config);

    // 添加参数定义
    void add_parameter(const parameter_definition& param);

    // 获取所有参数定义
    const std::vector<parameter_definition>& get_parameters() const;

    // 获取指定参数定义
    const parameter_definition* get_parameter(const std::string& name) const;

    // 生成所有可能的参数组合（网格搜索用）
    std::vector<std::unordered_map<std::string, parameter_value>> generate_grid() const;

    // 生成随机参数组合（随机搜索用）
    std::unordered_map<std::string, parameter_value> generate_random() const;

    // 获取参数空间大小（所有可能组合的数量）
    size_t get_space_size() const;

    // 将参数值应用到JSON配置
    static nlohmann::json apply_parameters(
        const nlohmann::json& base_config,
        const std::unordered_map<std::string, parameter_value>& params);

    // 从JSON配置中提取参数值
    static std::unordered_map<std::string, parameter_value> extract_parameters(
        const nlohmann::json& config,
        const std::vector<std::string>& param_names);

private:
    // 参数定义列表
    std::vector<parameter_definition> parameters_;

    // 参数名称到索引的映射
    std::unordered_map<std::string, size_t> param_index_;

    // 递归设置JSON中的嵌套参数
    static void set_nested_parameter(
        nlohmann::json& config,
        const std::string& param_path,
        const parameter_value& value);

    // 递归获取JSON中的嵌套参数
    static parameter_value get_nested_parameter(
        const nlohmann::json& config,
        const std::string& param_path);
};

} // namespace cpp_backtest
} // namespace fast_trader_elite
