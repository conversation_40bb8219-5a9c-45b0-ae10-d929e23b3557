#pragma once

#include <fast_trader_elite/cpp_backtest/parameter_optimizer/optimizer.h>
#include <vector>
#include <unordered_map>
#include <string>
#include <memory>
#include <thread>
#include <mutex>
#include <condition_variable>
#include <queue>
#include <atomic>

namespace fast_trader_elite {
namespace cpp_backtest {

// 网格搜索优化器
class grid_search_optimizer : public optimizer {
public:
    grid_search_optimizer(const parameter_space& space, 
                         const std::shared_ptr<optimization_target>& target);
    
    ~grid_search_optimizer() override;
    
    // 运行优化
    std::vector<optimization_result> run() override;
    
private:
    // 工作线程函数
    void worker_thread();
    
    // 参数组合队列
    std::queue<std::unordered_map<std::string, parameter_value>> param_queue_;
    
    // 互斥锁
    std::mutex mutex_;
    
    // 条件变量
    std::condition_variable cv_;
    
    // 工作线程
    std::vector<std::thread> workers_;
    
    // 是否停止
    std::atomic<bool> stop_;
    
    // 已完成的任务数
    std::atomic<int> completed_tasks_;
    
    // 总任务数
    int total_tasks_;
};

} // namespace cpp_backtest
} // namespace fast_trader_elite
