#pragma once

#include <string>
#include <vector>
#include <memory>
#include <functional>
#include <unordered_map>
#include <fast_trader_elite/cpp_backtest/parameter_optimizer/parameter_space.h>
#include <fast_trader_elite/cpp_backtest/parameter_optimizer/optimization_target.h>

namespace fast_trader_elite {
namespace cpp_backtest {

// 优化结果结构体
struct optimization_result {
    // 参数组合
    std::unordered_map<std::string, parameter_value> parameters;
    
    // 性能指标
    std::unordered_map<std::string, double> metrics;
    
    // 目标函数得分
    double score;
    
    // 回测配置路径（如果有）
    std::string config_path;
    
    // 结果是否有效
    bool valid = true;
};

// 优化器基类
class optimizer {
public:
    optimizer(const parameter_space& space, 
              const std::shared_ptr<optimization_target>& target);
    
    virtual ~optimizer() = default;
    
    // 设置回调函数，用于执行回测并返回结果
    void set_evaluation_callback(
        std::function<optimization_result(const std::unordered_map<std::string, parameter_value>&)> callback);
    
    // 设置进度回调函数
    void set_progress_callback(
        std::function<void(int current, int total, const optimization_result&)> callback);
    
    // 设置最大迭代次数
    void set_max_iterations(int max_iterations);
    
    // 设置并行任务数
    void set_parallel_jobs(int parallel_jobs);
    
    // 运行优化
    virtual std::vector<optimization_result> run() = 0;
    
    // 获取最佳结果
    virtual optimization_result get_best_result() const;
    
    // 获取所有结果
    virtual const std::vector<optimization_result>& get_all_results() const;
    
    // 保存结果到文件
    virtual bool save_results(const std::string& file_path) const;
    
    // 加载结果从文件
    virtual bool load_results(const std::string& file_path);
    
protected:
    // 参数空间
    parameter_space space_;
    
    // 优化目标
    std::shared_ptr<optimization_target> target_;
    
    // 评估回调函数
    std::function<optimization_result(const std::unordered_map<std::string, parameter_value>&)> evaluation_callback_;
    
    // 进度回调函数
    std::function<void(int current, int total, const optimization_result&)> progress_callback_;
    
    // 最大迭代次数
    int max_iterations_ = 1000;
    
    // 并行任务数
    int parallel_jobs_ = 1;
    
    // 所有评估结果
    std::vector<optimization_result> results_;
    
    // 评估一组参数
    optimization_result evaluate(const std::unordered_map<std::string, parameter_value>& params);
};

} // namespace cpp_backtest
} // namespace fast_trader_elite
