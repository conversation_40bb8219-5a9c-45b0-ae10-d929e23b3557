#pragma once

#include <cstdint>
#include <string>
#include <vector>
#include <unordered_map>
#include <unordered_set>
#include <optional>
#include <fast_trader_elite/data_model/field.h>
#include <fast_trader_elite/data_model/type.h>

namespace fast_trader_elite {
namespace cpp_backtest {

// 错误类型
enum class backtest_error {
    none,
    order_id_exist,
    order_not_found,
    invalid_price,
    invalid_quantity,
    invalid_side,
    invalid_order_type,
    invalid_time_in_force,
    invalid_timestamp,
    invalid_asset_no,
    invalid_data,
    invalid_state,
    invalid_operation,
    invalid_parameter,
    not_implemented,
    end_of_data
};

// 交易方向
enum class side_type {
    none,
    buy,
    sell
};

// 订单类型
enum class order_type {
    limit,
    market,
    unsupported
};

// 订单有效期类型
enum class time_in_force_type {
    gtc,  // Good Till Cancel
    ioc,  // Immediate or Cancel
    fok,  // Fill or Kill
    gtx,  // Good Till Crossing
    unsupported
};

// 订单状态
enum class order_status {
    none,
    new_order,
    partially_filled,
    filled,
    canceled,
    expired,
    rejected,       // 插入订单失败
    cancel_rejected // 取消订单失败
};

// 内部订单结构
struct internal_order {
    int64_t order_id;                // 订单ID
    double price;                    // 价格
    double quantity;                 // 数量
    double leaves_quantity;          // 剩余数量
    double executed_quantity;        // 已执行数量
    double executed_price;           // 执行价格
    side_type side;                  // 方向
    order_type type;                 // 订单类型
    time_in_force_type time_in_force; // 有效期类型
    order_status status;             // 状态
    order_status request;            // 请求状态
    int64_t exchange_timestamp;      // 交易所时间戳
    int64_t local_timestamp;         // 本地时间戳
    uint16_t instrument_idx;         // 品种索引
    bool is_maker;                   // 是否是做市商
    double fee;                      // 手续费
    int error_id;                    // 错误ID

    // 队列数据，直接包含在结构体中
    struct {
        double front_qty = 0.0;       // 前方队列数量
        double cum_trade_qty = 0.0;   // 累计成交数量
    } queue_data;

    // 构造函数
    internal_order();

    // 析构函数
    ~internal_order();

    // 转换为FastTraderElite的order_field
    order_field to_order_field() const;

    // 从FastTraderElite的order_input_field创建
    static internal_order from_order_input(const order_input_field& input, int64_t order_id);

    // 更新订单
    void update(const internal_order& other);
};

// 内部成交结构
struct internal_trade {
    int64_t order_id;                // 订单ID
    double price;                    // 价格
    double quantity;                 // 数量
    side_type side;                  // 方向
    int64_t timestamp;               // 时间戳
    uint16_t instrument_idx;         // 品种索引
    bool is_maker;                   // 是否是做市商
    double fee;                      // 手续费
    double trading_value;            // 成交金额
    order_status status;             // 订单状态

    // 构造函数
    internal_trade();

    // 转换为FastTraderElite的trade_field
    trade_field to_trade_field() const;

    // 从internal_order创建
    static internal_trade from_order(const internal_order& order, double trade_quantity);
};

// 状态值
struct state_values {
    double position;              // 持仓
    double balance;               // 余额
    double fee;                   // 手续费
    double pnl;                   // 盈亏
    double realized_pnl;          // 已实现盈亏
    double unrealized_pnl;        // 未实现盈亏
    int64_t num_trades;           // 成交笔数
    double trading_volume;        // 成交量
    double trading_value;         // 成交金额
    double last_price;            // 最后价格，用于计算盈亏
    double cost;                  // 持仓成本

    // 构造函数
    state_values();
};

// 二进制文件头部结构
struct file_header {
    char magic[8];           // 魔数，用于识别文件类型，例如"FTEBACK"
    uint32_t version;        // 版本号
    uint32_t record_count;   // 记录数量
    int64_t start_time;      // 数据开始时间戳
    int64_t end_time;        // 数据结束时间戳
    uint32_t instrument_count; // 包含的品种数量
    char reserved[32];       // 保留字段，用于未来扩展
};

// 品种信息结构
struct instrument_info {
    char instrument_id[32];  // 品种ID
    uint16_t instrument_idx; // 品种索引
    uint8_t exchange_id;     // 交易所ID
    double tick_size;        // 最小价格变动单位
    double step_size;        // 最小数量变动单位
    char reserved[32];       // 保留字段
};

// 品种配置结构体
struct instrument_config {
    std::string exchange;    // 交易所名称
    std::string instrument;  // 品种名称
    uint16_t instrument_idx; // 品种索引
};

// 记录项结构，参考 hftbacktest 的 Record 结构
struct record_item {
    int64_t timestamp;        // 时间戳
    double price;             // 价格
    double position;          // 持仓
    double balance;           // 余额
    double equity;            // 净值（总权益）
    double fee;               // 手续费
    int64_t num_trades;       // 成交笔数
    double trading_volume;    // 成交量
    double trading_value;     // 成交金额
    double cost;              // 持仓成本
};

// 记录头部结构
struct record_header {
    uint8_t record_type;     // 记录类型（深度行情、交易等）
    uint8_t flags;           // 标志位（本地事件、交易所事件等）
    uint16_t instrument_idx; // 品种索引
    int64_t exch_timestamp;  // 交易所时间戳
    int64_t local_timestamp; // 本地时间戳
    uint32_t data_size;      // 数据大小
};

// 记录类型枚举
enum record_type {
    DEPTH_MARKET_DATA = 1,   // 深度行情
    TRANSACTION_DATA = 2,    // 交易所成交数据
    KLINE_DATA = 3,          // K线数据
};

// 标志位定义
enum record_flags:uint32_t {
    LOCAL_EVENT = 0b0001,      // 本地事件
    EXCH_EVENT = 0b0010       // 交易所事件
};

// 辅助函数：将FastTraderElite的方向类型转换为内部方向类型
side_type convert_direction(direction_type direction);

// 辅助函数：将内部方向类型转换为FastTraderElite的方向类型
direction_type convert_side(side_type side);

// 辅助函数：将FastTraderElite的订单类型转换为内部订单类型
order_type convert_order_price_type(price_type type);

// 辅助函数：将内部订单类型转换为FastTraderElite的订单类型
price_type convert_order_type(order_type type);

// 辅助函数：将FastTraderElite的订单状态转换为内部订单状态
order_status convert_order_status(order_status_type status);

// 辅助函数：将内部订单状态转换为FastTraderElite的订单状态
order_status_type convert_order_status_type(order_status status);

} // namespace cpp_backtest
} // namespace fast_trader_elite
