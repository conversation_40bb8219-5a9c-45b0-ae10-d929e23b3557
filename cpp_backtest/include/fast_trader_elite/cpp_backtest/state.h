#pragma once

#include <unordered_map>
#include <memory>
#include <fast_trader_elite/cpp_backtest/types.h>
#include <fast_trader_elite/cpp_backtest/models/fee_model.h>

namespace fast_trader_elite {
namespace cpp_backtest {

// 状态管理类
class state {
public:
    state(double initial_balance = 0.0, fee_model* fee_model = nullptr);
    ~state() = default;

    // 应用订单成交
    void apply_fill(const internal_order& order);

    // 获取状态值
    const state_values& get_values(uint16_t instrument_idx) const;

    // 计算总权益（净值）
    double equity(uint16_t instrument_idx, double price) const;

    // 重置状态
    void reset(double initial_balance = 0.0);

    // 设置手续费模型
    void set_fee_model(fee_model* fee_model) {
        fee_model_ = fee_model;
    }

private:
    std::unordered_map<uint16_t, state_values> values_;
    std::unordered_map<uint16_t, double> last_prices_;
    double initial_balance_;
    fee_model* fee_model_ = nullptr;
};

} // namespace cpp_backtest
} // namespace fast_trader_elite
