#pragma once

#include <memory>
#include <string>
#include <fast_trader_elite/cpp_backtest/export/exporter.h>

namespace fast_trader_elite {
namespace cpp_backtest {

// 导出器工厂类
class exporter_factory {
public:
    // 导出格式枚举
    enum class format {
        CSV,  // CSV格式
        NPZ   // NPZ格式（NumPy压缩格式）
    };

    // 创建导出器
    static std::shared_ptr<exporter> create(format fmt = format::CSV);

    // 根据文件扩展名创建导出器
    static std::shared_ptr<exporter> create_from_extension(const std::string& file_path);
};

} // namespace cpp_backtest
} // namespace fast_trader_elite
