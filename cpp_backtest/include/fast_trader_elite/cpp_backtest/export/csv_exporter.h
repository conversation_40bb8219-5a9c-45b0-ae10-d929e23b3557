#pragma once

#include <memory>
#include <fast_trader_elite/cpp_backtest/export/exporter.h>

namespace fast_trader_elite {
namespace cpp_backtest {

// CSV导出器
class csv_exporter : public exporter {
public:
    csv_exporter() = default;
    ~csv_exporter() override = default;

    // 创建CSV导出器的工厂方法
    static std::shared_ptr<csv_exporter> create() {
        return std::make_shared<csv_exporter>();
    }

    // 导出记录数据（主要导出方法，参考 hftbacktest 的 to_csv）
    bool export_records(const backtest_data& data, const std::string& file_path) override;

    // 导出成交数据
    bool export_trades(const backtest_data& data, const std::string& file_path) override;

    // 导出 PNL 数据
    bool export_pnl(const backtest_data& data, const std::string& file_path) override;

private:
    // 辅助函数：将时间戳转换为可读的日期时间字符串
    std::string format_timestamp(int64_t timestamp) const;
};

} // namespace cpp_backtest
} // namespace fast_trader_elite
