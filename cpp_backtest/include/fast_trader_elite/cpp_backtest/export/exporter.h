#pragma once

#include <string>
#include <vector>
#include <memory>
#include <fast_trader_elite/cpp_backtest/backtest_data.h>

namespace fast_trader_elite {
namespace cpp_backtest {

// 导出器接口
class exporter {
public:
    virtual ~exporter() = default;

    // 导出记录数据（主要导出方法，参考 hftbacktest 的 to_csv/to_npz）
    virtual bool export_records(const backtest_data& data, const std::string& file_path) = 0;

    // 导出成交数据
    virtual bool export_trades(const backtest_data& data, const std::string& file_path) = 0;

    // 导出 PNL 数据
    virtual bool export_pnl(const backtest_data& data, const std::string& file_path) = 0;
};

} // namespace cpp_backtest
} // namespace fast_trader_elite
