#pragma once

#include <memory>
#include <fast_trader_elite/cpp_backtest/export/exporter.h>

namespace fast_trader_elite {
namespace cpp_backtest {

// NPZ导出器
class npz_exporter : public exporter {
public:
    npz_exporter() = default;
    ~npz_exporter() override = default;

    // 创建NPZ导出器的工厂方法
    static std::shared_ptr<npz_exporter> create() {
        return std::make_shared<npz_exporter>();
    }

    // 导出记录数据（主要导出方法，参考 hftbacktest 的 to_npz）
    bool export_records(const backtest_data& data, const std::string& file_path) override;

    // 导出成交数据
    bool export_trades(const backtest_data& data, const std::string& file_path) override;

    // 导出 PNL 数据
    bool export_pnl(const backtest_data& data, const std::string& file_path) override;

private:
    // 写入NPY头部
    void write_npy_header(std::ofstream& file, size_t num_records);

    // 写入记录数据
    void write_record_data(std::ofstream& file, const std::vector<record_item>& records);
};

} // namespace cpp_backtest
} // namespace fast_trader_elite
