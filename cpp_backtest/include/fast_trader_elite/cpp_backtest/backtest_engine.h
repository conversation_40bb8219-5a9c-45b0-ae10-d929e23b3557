#pragma once

#include <string>
#include <memory>
#include <vector>
#include <nlohmann_json/json.hpp>
#include <fast_trader_elite/cpp_backtest/data/binary_reader.h>
#include <fast_trader_elite/cpp_backtest/proc/local_processor.h>
#include <fast_trader_elite/cpp_backtest/proc/exchange_processor.h>
#include <fast_trader_elite/cpp_backtest/proc/no_partial_fill_exchange.h>
#include <fast_trader_elite/cpp_backtest/proc/partial_fill_exchange.h>
#include <fast_trader_elite/cpp_backtest/market/order_book.h>
#include <fast_trader_elite/cpp_backtest/models/fee_model.h>
#include <fast_trader_elite/cpp_backtest/models/queue_model.h>
#include <fast_trader_elite/cpp_backtest/models/latency_model.h>
#include <fast_trader_elite/cpp_backtest/backtest_context.h>

#include <fast_trader_elite/cpp_backtest/backtest_data.h>
#include <fast_trader_elite/cpp_backtest/export/exporter.h>
#include <i_strategy.h>

namespace fast_trader_elite {
namespace cpp_backtest {

// 使用 instrument_config.h 中定义的 instrument_config 结构体

// 策略加载器
class strategy_loader {
public:
    strategy_loader();
    ~strategy_loader();

    // 加载策略
    bool load(const std::string& strategy_path);

    // 获取策略实例
    fast_trader_elite::strategy::i_strategy* get_strategy() const;

private:
    using create_strategy_func = fast_trader_elite::strategy::i_strategy* (*)();
    using destroy_strategy_func = void (*)(void*);

    void* handle_;
    create_strategy_func create_func_;
    destroy_strategy_func destroy_func_;
    fast_trader_elite::strategy::i_strategy* strategy_;
};



class backtest_engine {
public:
    // 运行模式枚举
    enum class run_mode {
        DIRECT,     // 直接模式，对应原 backtest_engine
        CONFIG      // 配置模式，对应原 config_based_backtest_engine
    };

    // 构造函数 - 直接模式
    backtest_engine(const std::string& data_file,
                           fast_trader_elite::strategy::i_strategy* strategy);

    // 构造函数 - 配置模式
    backtest_engine();

    // 析构函数
    ~backtest_engine();

    // 加载配置文件 (配置模式)
    bool load_config(const std::string& config_path);

    // 初始化引擎 (配置模式)
    bool init();

    // 设置模型 (直接模式)
    void set_fee_model(fee_model* model) { fee_model_ptr_ = model; }
    void set_queue_model(queue_model* model) { queue_model_ptr_ = model; }
    void set_latency_model(latency_model* model) { latency_model_ptr_ = model; }

    // 设置使用部分成交交易所 (直接模式)
    void use_partial_fill_exchange(bool use = true) { use_partial_fill_ = use; }

    // 运行回测 (两种模式)
    void run();

    // 输出结果 (配置模式)
    void print_results() const;

    // 获取初始余额
    double get_initial_balance() const { return initial_balance_; }

    // 获取最终余额
    double get_final_balance() const { return final_balance_; }

    // 获取开始时间
    int64_t get_start_time() const { return start_time_; }

    // 获取结束时间
    int64_t get_end_time() const { return end_time_; }

    // 获取当前时间戳 (直接模式)
    int64_t current_timestamp() const { return current_timestamp_; }

    // 获取资产数量 (直接模式)
    size_t num_assets() const { return 1; } // 目前只支持单资产

    // 获取最近的交易 (直接模式)
    const std::vector<internal_trade>& last_trades(size_t asset_no) const;

    // 清除最近的交易 (直接模式)
    void clear_last_trades(size_t asset_no);

    // 获取回测数据
    const backtest_data& get_data() const { return data_; }

    // 设置回测数据（仅用于测试）
    backtest_data& data() { return data_; }

    // 导出记录数据（参考 hftbacktest 的 to_csv/to_npz）
    bool export_records(const std::string& file_path) const;

    // 导出成交数据
    bool export_trades(const std::string& file_path) const;

    // 导出 PNL 数据
    bool export_pnl(const std::string& file_path) const;

    // 设置导出器
    void set_exporter(std::shared_ptr<exporter> exporter) { exporter_ = exporter; }



private:
    // 运行模式
    run_mode mode_;

    // 配置文件 (配置模式)
    nlohmann::json config_;

    // 回测配置 (配置模式)
    std::string data_file_;
    std::string output_path_;
    int64_t start_time_;
    int64_t end_time_;
    double initial_balance_;
    std::string exchange_type_;

    // 策略配置 (配置模式)
    std::string strategy_path_;
    std::string strategy_config_path_;

    // 延迟模型配置 (配置模式)
    std::string latency_type_;
    int64_t order_latency_;
    int64_t cancel_latency_;
    int64_t md_latency_;
    int64_t response_latency_;

    // 队列模型配置 (配置模式)
    std::string queue_type_;
    double queue_power_;

    // 手续费模型配置 (配置模式)
    std::string fee_type_;
    double maker_fee_;
    double taker_fee_;

    // 资产类型配置 (配置模式)
    std::string asset_type_;
    double contract_value_;

    // 品种配置 (配置模式)
    std::vector<instrument_config> instruments_;

    // 策略实例配置 (配置模式)
    strategy_instance_config strategy_config_;

    // 组件 (两种模式)
    // 重新排序成员变量，确保context_在strategy_loader_之前析构
    // 这样可以保证context在策略之前被销毁
    std::unique_ptr<binary_reader> reader_;
    std::unique_ptr<market_depth> depth_;
    std::unique_ptr<local_processor> local_;
    std::unique_ptr<exchange_processor> exchange_;
    std::unique_ptr<order_bus> local_to_exchange_bus_;
    std::unique_ptr<order_bus> exchange_to_local_bus_;
    std::unique_ptr<backtest_context> context_;
    strategy_loader strategy_loader_;
    fast_trader_elite::strategy::i_strategy* strategy_;

    // 模型 (两种模式)
    std::unique_ptr<fee_model> fee_model_;
    fee_model* fee_model_ptr_;
    std::unique_ptr<queue_model> queue_model_;
    queue_model* queue_model_ptr_;
    std::unique_ptr<latency_model> latency_model_;
    latency_model* latency_model_ptr_;

    bool use_partial_fill_;
    int64_t current_timestamp_;
    double final_balance_; // 最终余额
    double equity_value_; // 最终净值
    backtest_data data_; // 回测数据
    std::shared_ptr<exporter> exporter_; // 导出器

    // 解析配置 (配置模式)
    bool parse_config();

    // 创建模型 (配置模式)
    bool create_models();

    // 创建处理器 (两种模式)
    void create_processors();

    // 处理数据记录 (两种模式)
    bool process_record(const record_header& header, const std::vector<uint8_t>& data);


};

} // namespace cpp_backtest
} // namespace fast_trader_elite
