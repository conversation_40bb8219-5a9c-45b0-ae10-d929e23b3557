#pragma once

#include <fmt/core.h>
#include <fmt/chrono.h>
#include <fstream>
#include <string>
#include <mutex>
#include <ctime>
#include <iomanip>
#include <sstream>

namespace fast_trader_elite {
namespace cpp_backtest {

// 日志级别枚举
enum class log_level {
    TRACE = 0,
    DEBUG = 1,
    INFO = 2,
    WARN = 3,
    ERROR = 4,
    FATAL = 5
};

// 回测日志类
class backtest_logger {
public:
    // 获取单例实例
    static backtest_logger& get_instance() {
        static backtest_logger instance;
        return instance;
    }

    // 初始化日志
    void init(const std::string& log_file, log_level level = log_level::INFO) {
        std::lock_guard<std::mutex> lock(mutex_);
        
        // 设置日志级别
        level_ = level;
        
        // 关闭之前的文件（如果有）
        if (file_.is_open()) {
            file_.close();
        }
        
        // 打开新文件
        file_.open(log_file, std::ios::out | std::ios::app);
        if (!file_.is_open()) {
            fmt::print(stderr, "Failed to open log file: {}\n", log_file);
        }
    }

    // 设置日志级别
    void set_level(log_level level) {
        std::lock_guard<std::mutex> lock(mutex_);
        level_ = level;
    }

    // 设置日志级别（字符串形式）
    void set_level(const std::string& level_str) {
        if (level_str == "TRACE" || level_str == "trace") {
            set_level(log_level::TRACE);
        } else if (level_str == "DEBUG" || level_str == "debug") {
            set_level(log_level::DEBUG);
        } else if (level_str == "INFO" || level_str == "info") {
            set_level(log_level::INFO);
        } else if (level_str == "WARN" || level_str == "warn") {
            set_level(log_level::WARN);
        } else if (level_str == "ERROR" || level_str == "error") {
            set_level(log_level::ERROR);
        } else if (level_str == "FATAL" || level_str == "fatal") {
            set_level(log_level::FATAL);
        }
    }

    // 关闭日志
    void close() {
        std::lock_guard<std::mutex> lock(mutex_);
        if (file_.is_open()) {
            file_.close();
        }
    }

    // 日志记录函数
    template<typename... Args>
    void log(log_level level, const char* file, int line, const char* func, const std::string& format, Args&&... args) {
        if (level < level_) {
            return;
        }

        std::lock_guard<std::mutex> lock(mutex_);
        
        // 获取当前时间
        auto now = std::chrono::system_clock::now();
        auto time_t_now = std::chrono::system_clock::to_time_t(now);
        auto ms = std::chrono::duration_cast<std::chrono::milliseconds>(now.time_since_epoch()) % 1000;
        
        std::tm tm_now;
        localtime_r(&time_t_now, &tm_now);
        
        char time_buf[32];
        std::strftime(time_buf, sizeof(time_buf), "%Y-%m-%d %H:%M:%S", &tm_now);
        
        // 日志级别名称
        static const char* level_names[] = {"TRACE", "DEBUG", "INFO", "WARN", "ERROR", "FATAL"};
        
        // 提取文件名（不包含路径）
        const char* filename = file;
        const char* last_slash = strrchr(file, '/');
        if (last_slash) {
            filename = last_slash + 1;
        }
        
        // 格式化消息
        std::string message;
        try {
            message = fmt::format(fmt::runtime(format), std::forward<Args>(args)...);
        } catch (const std::exception& e) {
            message = fmt::format("Error formatting log message: {}", e.what());
        }
        
        // 构建完整日志行
        std::string log_line = fmt::format("[{}] [{}.{:03d}] [{}:{}] [{}] {}\n",
            level_names[static_cast<int>(level)],
            time_buf, ms.count(),
            filename, line,
            func,
            message);
        
        // 输出到文件或控制台
        if (file_.is_open()) {
            file_ << log_line;
            file_.flush();
        } else {
            fmt::print("{}", log_line);
            fflush(stdout);
        }
    }

private:
    // 私有构造函数（单例模式）
    backtest_logger() : level_(log_level::INFO) {}
    
    // 禁止拷贝和赋值
    backtest_logger(const backtest_logger&) = delete;
    backtest_logger& operator=(const backtest_logger&) = delete;
    
    std::ofstream file_;
    log_level level_;
    std::mutex mutex_;
};

} // namespace cpp_backtest
} // namespace fast_trader_elite

// 日志宏定义
#define BT_LOG_TRACE(format, ...) \
    fast_trader_elite::cpp_backtest::backtest_logger::get_instance().log( \
        fast_trader_elite::cpp_backtest::log_level::TRACE, __FILE__, __LINE__, __func__, format, ##__VA_ARGS__)

#define BT_LOG_DEBUG(format, ...) \
    fast_trader_elite::cpp_backtest::backtest_logger::get_instance().log( \
        fast_trader_elite::cpp_backtest::log_level::DEBUG, __FILE__, __LINE__, __func__, format, ##__VA_ARGS__)

#define BT_LOG_INFO(format, ...) \
    fast_trader_elite::cpp_backtest::backtest_logger::get_instance().log( \
        fast_trader_elite::cpp_backtest::log_level::INFO, __FILE__, __LINE__, __func__, format, ##__VA_ARGS__)

#define BT_LOG_WARN(format, ...) \
    fast_trader_elite::cpp_backtest::backtest_logger::get_instance().log( \
        fast_trader_elite::cpp_backtest::log_level::WARN, __FILE__, __LINE__, __func__, format, ##__VA_ARGS__)

#define BT_LOG_ERROR(format, ...) \
    fast_trader_elite::cpp_backtest::backtest_logger::get_instance().log( \
        fast_trader_elite::cpp_backtest::log_level::ERROR, __FILE__, __LINE__, __func__, format, ##__VA_ARGS__)

#define BT_LOG_FATAL(format, ...) \
    fast_trader_elite::cpp_backtest::backtest_logger::get_instance().log( \
        fast_trader_elite::cpp_backtest::log_level::FATAL, __FILE__, __LINE__, __func__, format, ##__VA_ARGS__)
