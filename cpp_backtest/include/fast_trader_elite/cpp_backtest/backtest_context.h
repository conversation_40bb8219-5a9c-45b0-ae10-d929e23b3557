#pragma once

#include <unordered_map>
#include <functional>
#include <vector>
#include <memory>
#include <string>
#include <i_strategy_ctx.h>
#include <i_strategy.h>
#include <fast_trader_elite/cpp_backtest/proc/local_processor.h>

namespace fast_trader_elite {
namespace cpp_backtest {

// 前向声明
class backtest_engine;

// 回测上下文
class backtest_context : public fast_trader_elite::strategy::i_strategy_ctx {
public:
    backtest_context(local_processor* local_processor);
    backtest_context(local_processor* local_processor, const strategy_instance_config& strategy_config);
    virtual ~backtest_context() {
        // 输出析构信息
        std::cout<<"ctx desc"<<std::endl;
    }

    // i_strategy_ctx接口实现
    void subscribe(fast_trader_elite::md_sub_code_field* f) override;
    void get_kline(fast_trader_elite::md_kline_req_field* f, int request_id) override;
    void get_recent_transaction(fast_trader_elite::transaction_req_field* f, int request_id) override;
    int64_t insert_order(fast_trader_elite::order_input_field* f) override;
    int64_t insert_order_list(fast_trader_elite::order_input_list_field* f) override;
    int64_t cancel_order(fast_trader_elite::order_action_field* f) override;
    int64_t cancel_order_list(fast_trader_elite::order_action_list_field* f) override;
    int64_t cancel_order_all(fast_trader_elite::order_action_field* f) override;
    int64_t close_order_all(fast_trader_elite::order_input_field* f) override;
    int get_postion(fast_trader_elite::position_req_field* f, int request_id) override;
    int get_wallet_banlance(fast_trader_elite::wallet_balance_req_field* f, int request_id) override;
    int set_leverage(fast_trader_elite::leverage_req_field* f, int request_id) override;
    int register_timer(uint32_t milsec, const timer_function&& f, bool repeat = true) override;
    void unregister_timer(int timer_id) override;

    // timer with timestamp
    int register_timer_with_timestamp(uint32_t milsec, const timer_function_with_timestamp&& f, bool repeat) override;
    void unregister_timer_with_timestamp(int timer_id) override;

    fast_trader_elite::instrument_field* get_instrument_filed(fast_trader_elite::exchange_type ex, uint16_t instrument_idx) override;
    fast_trader_elite::instrument_field* get_instrument_filed(fast_trader_elite::exchange_type ex, const char* instrument_id) override;
    std::vector<fast_trader_elite::instrument_field*> get_all_instruments(fast_trader_elite::exchange_type ex) override;
    void register_async_task(std::function<void()> func) override;
    int register_async_timer(uint32_t milsec, const timer_function&& f, bool repeat) override;
    void unregister_async_timer(int timer_id) override;

    // async timer with timestamp
    int register_async_timer_with_timestamp(uint32_t milsec, const timer_function_with_timestamp&& f, bool repeat) override;
    void unregister_async_timer_with_timestamp(int timer_id) override;
    strategy_instance_config get_strategy_config(const std::string& strategy_name) override;
    int send_email(email_field* f) override{return 0;}

    // 设置当前时间戳
    void set_current_timestamp(int64_t timestamp);

    // 获取当前时间戳
    int64_t get_current_timestamp() const override;

    // 处理定时器
    void process_timers();

    // 初始化品种信息
    void init_instruments(const std::vector<instrument_info>& instrument_infos);

private:
    local_processor* local_processor_;
    int64_t current_timestamp_;

    // 定时器管理
    struct timer_info {
      int64_t interval_ms;
        int64_t next_trigger_time;
        timer_function callback;
        bool repeat;
        bool active;
    };

    // 带时间戳的定时器管理
    struct timer_info_with_timestamp {
      int64_t interval_ms;
        int64_t next_trigger_time;
        timer_function_with_timestamp callback;
        bool repeat;
        bool active;
    };

    std::unordered_map<int, timer_info> timers_;
    std::unordered_map<int, timer_info_with_timestamp> timers_with_timestamp_;
    int next_timer_id_;
    int next_timer_with_timestamp_id_;

    // 品种信息
    std::unordered_map<std::string, fast_trader_elite::instrument_field> instruments_;
    std::unordered_map<uint16_t, fast_trader_elite::instrument_field> instrument_idx_map_;

    // 策略配置
    strategy_instance_config strategy_config_;
};

} // namespace cpp_backtest
} // namespace fast_trader_elite
