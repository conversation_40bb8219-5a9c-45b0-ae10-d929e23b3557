#pragma once

#include <fast_trader_elite/cpp_backtest/types.h>
#include <fast_trader_elite/cpp_backtest/market/market_depth.h>
#include <cmath>
#include <memory>
#include <random>

namespace fast_trader_elite {
namespace cpp_backtest {

// 队列位置信息现在直接定义在 internal_order 结构体中

/**
 * @brief 排队模型接口
 * 提供订单队列位置的估计
 */
class queue_model {
public:
    virtual ~queue_model() = default;

    /**
     * @brief 初始化新订单的队列位置
     * 当交易所模型接受新订单时调用此函数
     *
     * @param order 订单
     * @param depth 市场深度
     */
    virtual void new_order(internal_order& order, const market_depth* depth) = 0;

    /**
     * @brief 当市场深度在相同价格变化时调整估计值
     *
     * @param order 订单
     * @param prev_qty 之前的数量
     * @param new_qty 新的数量
     * @param depth 市场深度
     */
    virtual void update_depth(internal_order& order, double prev_qty, double new_qty, const market_depth* depth) = 0;

    /**
     * @brief 当相同价格发生市场成交时调整估计值
     *
     * @param order 订单
     * @param qty 成交数量
     * @param depth 市场深度
     */
    virtual void trade(internal_order& order, double qty, const market_depth* depth) = 0;

    /**
     * @brief 检查订单是否可以成交，返回可成交数量
     *
     * @param order 订单
     * @param depth 市场深度
     * @return double 可成交数量
     */
    virtual double is_filled(const internal_order& order, const market_depth* depth) const = 0;
};

/**
 * @brief 保守队列位置模型
 * 提供一个保守的队列位置模型，只有当相同价格发生成交时，订单的队列位置才会前进
 */
class risk_adverse_queue_model : public queue_model {
public:
    risk_adverse_queue_model() = default;

    void new_order(internal_order& order, const market_depth* depth) override;
    void update_depth(internal_order& order, double prev_qty, double new_qty, const market_depth* depth) override;
    void trade(internal_order& order, double qty, const market_depth* depth) override;
    double is_filled(const internal_order& order, const market_depth* depth) const override;
};

/**
 * @brief 概率接口
 * 提供基于订单前后数量的概率计算
 */
class probability {
public:
    virtual ~probability() = default;

    /**
     * @brief 基于订单前后数量计算概率
     *
     * @param front 前方数量
     * @param back 后方数量
     * @return double 概率值
     */
    virtual double prob(double front, double back) const = 0;
};

/**
 * @brief 固定概率类
 * 提供一个固定的概率值，不考虑队列前后数量
 */
class fixed_probability : public probability {
public:
    explicit fixed_probability(double prob_value) : prob_value_(prob_value) {}

    double prob(double front, double back) const override {
        return prob_value_;
    }

private:
    double prob_value_; // 固定概率值
};

/**
 * @brief 幂函数概率队列函数
 * 使用幂函数 f(x) = x^n 调整概率，计算为 f(back) / (f(back) + f(front))
 */
class power_prob_queue_func : public probability {
public:
    explicit power_prob_queue_func(double n) : n_(n) {}

    double prob(double front, double back) const override {
        return f(back) / (f(back) + f(front));
    }

private:
    double f(double x) const {
        return std::pow(x, n_);
    }

    double n_;
};

/**
 * @brief 对数概率队列函数
 * 使用对数函数 f(x) = log(1 + x) 调整概率，计算为 f(back) / (f(back) + f(front))
 */
class log_prob_queue_func : public probability {
public:
    log_prob_queue_func() = default;

    double prob(double front, double back) const override {
        return f(back) / (f(back) + f(front));
    }

private:
    double f(double x) const {
        return std::log(1.0 + x);
    }
};

/**
 * @brief 对数概率队列函数2
 * 使用对数函数 f(x) = log(1 + x) 调整概率，计算为 f(back) / f(back + front)
 */
class log_prob_queue_func2 : public probability {
public:
    log_prob_queue_func2() = default;

    double prob(double front, double back) const override {
        return f(back) / f(back + front);
    }

private:
    double f(double x) const {
        return std::log(1.0 + x);
    }
};

/**
 * @brief 幂函数概率队列函数2
 * 使用幂函数 f(x) = x^n 调整概率，计算为 f(back) / f(back + front)
 */
class power_prob_queue_func2 : public probability {
public:
    explicit power_prob_queue_func2(double n) : n_(n) {}

    double prob(double front, double back) const override {
        return f(back) / f(back + front);
    }

private:
    double f(double x) const {
        return std::pow(x, n_);
    }

    double n_;
};

/**
 * @brief 幂函数概率队列函数3
 * 使用幂函数 f(x) = x^n 调整概率，计算为 1 - f(front / (front + back))
 */
class power_prob_queue_func3 : public probability {
public:
    explicit power_prob_queue_func3(double n) : n_(n) {}

    double prob(double front, double back) const override {
        return 1.0 - f(front / (front + back));
    }

private:
    double f(double x) const {
        return std::pow(x, n_);
    }

    double n_;
};

/**
 * @brief 概率队列模型
 * 提供基于概率的队列位置模型，如下所述：
 * - https://quant.stackexchange.com/questions/3782/how-do-we-estimate-position-of-our-order-in-order-book
 * - https://rigtorp.se/2013/06/08/estimating-order-queue-position.html
 *
 * 当相同价格发生成交或者数量减少时，订单的队列位置会前进。队列位置的前进取决于基于相对队列位置的概率。
 * 为了避免重复计算由成交引起的数量减少，在数量变化之前发生的所有成交数量将从数量变化中减去。
 */
class prob_queue_model : public queue_model {
public:
    explicit prob_queue_model(std::unique_ptr<probability> prob) : prob_(std::move(prob)) {}

    void new_order(internal_order& order, const market_depth* depth) override;
    void update_depth(internal_order& order, double prev_qty, double new_qty, const market_depth* depth) override;
    void trade(internal_order& order, double qty, const market_depth* depth) override;
    double is_filled(const internal_order& order, const market_depth* depth) const override;

private:
    std::unique_ptr<probability> prob_;
};

/**
 * @brief 固定概率队列模型
 * 提供基于固定概率的队列模型，订单成交概率由固定值决定
 * 当订单到达队列前端时，根据固定概率决定是否成交
 */
class fixed_prob_queue_model : public queue_model {
public:
    explicit fixed_prob_queue_model(double prob_value) : prob_value_(prob_value) {}

    void new_order(internal_order& order, const market_depth* depth) override;
    void update_depth(internal_order& order, double prev_qty, double new_qty, const market_depth* depth) override;
    void trade(internal_order& order, double qty, const market_depth* depth) override;
    double is_filled(const internal_order& order, const market_depth* depth) const override;

private:
    double prob_value_; // 固定成交概率
    mutable std::mt19937 rng_{std::random_device{}()}; // 随机数生成器
};

} // namespace cpp_backtest
} // namespace fast_trader_elite
