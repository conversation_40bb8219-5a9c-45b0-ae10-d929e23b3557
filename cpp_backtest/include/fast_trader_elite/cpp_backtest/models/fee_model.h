#pragma once

namespace fast_trader_elite {
namespace cpp_backtest {

// 费率模型接口
class fee_model {
public:
    virtual ~fee_model() = default;
    
    // 计算费用
    virtual double calculate_fee(double price, double quantity, bool is_maker) const = 0;
};

// 固定费率模型
class fixed_fee_model : public fee_model {
public:
    fixed_fee_model(double maker_fee_rate, double taker_fee_rate);
    
    double calculate_fee(double price, double quantity, bool is_maker) const override;
    
private:
    double maker_fee_rate_;
    double taker_fee_rate_;
};

} // namespace cpp_backtest
} // namespace fast_trader_elite
