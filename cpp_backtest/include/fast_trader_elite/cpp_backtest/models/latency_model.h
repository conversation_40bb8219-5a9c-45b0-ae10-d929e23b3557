#pragma once

#include <cstdint>
#include <fast_trader_elite/cpp_backtest/types.h>

namespace fast_trader_elite {
namespace cpp_backtest {

// 延迟模型接口
class latency_model {
public:
    virtual ~latency_model() = default;
    
    // 计算订单提交延迟
    virtual int64_t entry(int64_t timestamp, const internal_order& order) const = 0;
    
    // 计算订单响应延迟
    virtual int64_t response(int64_t timestamp, const internal_order& order) const = 0;
    
    // 计算市场数据延迟
    virtual int64_t market(int64_t timestamp) const = 0;
};

// 固定延迟模型
class constant_latency_model : public latency_model {
public:
    constant_latency_model(int64_t entry_latency, int64_t response_latency, int64_t market_latency);
    
    int64_t entry(int64_t timestamp, const internal_order& order) const override;
    int64_t response(int64_t timestamp, const internal_order& order) const override;
    int64_t market(int64_t timestamp) const override;
    
private:
    int64_t entry_latency_;
    int64_t response_latency_;
    int64_t market_latency_;
};

} // namespace cpp_backtest
} // namespace fast_trader_elite
