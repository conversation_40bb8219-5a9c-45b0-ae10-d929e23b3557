#pragma once

#include <fast_trader_elite/cpp_backtest/models/latency_model.h>
#include <fast_trader_elite/cpp_backtest/models/queue_model.h>
#include <fast_trader_elite/cpp_backtest/models/fee_model.h>
#include <memory>
#include <string>

namespace fast_trader_elite {
namespace cpp_backtest {

/**
 * @brief 延迟模型工厂类
 * 用于创建不同类型的延迟模型
 */
class latency_model_factory {
public:
    /**
     * @brief 创建延迟模型
     * 
     * @param type 延迟模型类型
     * @param order_latency 订单延迟（纳秒）
     * @param response_latency 响应延迟（纳秒）
     * @param md_latency 行情延迟（纳秒）
     * @return std::unique_ptr<latency_model> 延迟模型指针
     */
    static std::unique_ptr<latency_model> create(
        const std::string& type,
        int64_t order_latency,
        int64_t response_latency,
        int64_t md_latency);
};

/**
 * @brief 队列模型工厂类
 * 用于创建不同类型的队列模型
 */
class queue_model_factory {
public:
    /**
     * @brief 创建队列模型
     * 
     * @param type 队列模型类型
     * @param queue_power 幂函数队列模型的幂参数
     * @return std::unique_ptr<queue_model> 队列模型指针
     */
    static std::unique_ptr<queue_model> create(
        const std::string& type,
        double queue_power);
};

/**
 * @brief 手续费模型工厂类
 * 用于创建不同类型的手续费模型
 */
class fee_model_factory {
public:
    /**
     * @brief 创建手续费模型
     * 
     * @param type 手续费模型类型
     * @param maker_fee 做市商费率
     * @param taker_fee 吃单方费率
     * @return std::unique_ptr<fee_model> 手续费模型指针
     */
    static std::unique_ptr<fee_model> create(
        const std::string& type,
        double maker_fee,
        double taker_fee);
};

} // namespace cpp_backtest
} // namespace fast_trader_elite
