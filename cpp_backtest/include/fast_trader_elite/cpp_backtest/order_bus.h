#pragma once

#include <deque>
#include <optional>
#include <utility>
#include <algorithm>
#include <fast_trader_elite/cpp_backtest/types.h>

namespace fast_trader_elite {
namespace cpp_backtest {

class order_bus {
public:

    order_bus() = default;
    ~order_bus() = default;
    std::optional<int64_t> earliest_timestamp() const;

    void append(const internal_order& order, int64_t timestamp);
    void reset();
    size_t size() const;
    bool empty() const;
    std::optional<std::pair<internal_order, int64_t>> pop_front();

private:
    std::deque<std::pair<internal_order, int64_t>> order_list_;
};

} // namespace cpp_backtest
} // namespace fast_trader_elite
