#pragma once

#include <string>
#include <vector>
#include <fstream>
#include <memory>
#include <limits>
#include <algorithm>
#include <cstring>
#include <fast_trader_elite/data_model/field.h>
#include <fast_trader_elite/data_model/type.h>
#include <fast_trader_elite/cpp_backtest/types.h>
#include <fast_trader_elite/cpp_backtest/data_preprocessor/exchange_preprocessor.h>
#include <nlohmann_json/json.hpp>

namespace fast_trader_elite {
namespace cpp_backtest {

// 数据预处理器类 - 只支持一个交易所和一个品种
class data_preprocessor {
public:
    data_preprocessor();
    ~data_preprocessor();

    // 从JSON配置文件初始化
    bool init_from_config(const std::string& config_path);

    // 设置交易所类型
    void set_exchange_type(exchange_type exchange);

    // 读取深度行情数据
    bool read_depth_data(const std::string& filename);

    // 读取成交数据
    bool read_transaction_data(const std::string& filename);

    // 读取K线数据
    bool read_kline_data(const std::string& filename);

    // 设置基础延迟（纳秒）
    void set_base_latency(int64_t latency);

    // 设置品种信息
    void set_instrument_info(const std::string& symbol, double tick_size, double step_size);

    // 处理数据（时间戳校正、排序等）
    bool process_data();

    // 写入二进制文件
    bool write_binary_file(const std::string& filename);

    // 获取处理后的记录数量
    size_t get_record_count() const;

    // 获取品种信息
    const instrument_info& get_instrument() const;

    // 清除所有数据
    void clear();

private:

    // 校正本地时间戳
    void correct_local_timestamp();

    // 校正事件顺序
    void correct_event_order();

    // 验证事件顺序
    bool validate_event_order() const;

    // 将字段转换为记录
    template<typename T>
    struct record {
        record_header header;
        std::vector<uint8_t> data;
    };

    record<void> create_record(uint8_t type, uint8_t flags,
                      int64_t exch_timestamp, int64_t local_timestamp, const void* data, size_t data_size);

private:
    std::vector<record<void>> records_;
    instrument_info instrument_;
    std::unique_ptr<exchange_preprocessor> preprocessor_;
    exchange_type exchange_type_;
    std::string instrument_symbol_;
    int64_t base_latency_;

    // 数据文件路径
    std::string depth_data_path_;
    std::string transaction_data_path_;
    std::string kline_data_path_;
};

} // namespace cpp_backtest
} // namespace fast_trader_elite
