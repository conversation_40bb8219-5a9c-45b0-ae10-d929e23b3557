#pragma once

#include <string>
#include <vector>
#include <memory>
#include <unordered_map>
#include <fast_trader_elite/data_model/field.h>
#include <fast_trader_elite/data_model/type.h>
#include <fast_trader_elite/cpp_backtest/types.h>

namespace fast_trader_elite {
namespace cpp_backtest {

// 交易所数据预处理器基类
class exchange_preprocessor {
public:
    virtual ~exchange_preprocessor() = default;

    // 读取深度行情数据
    virtual bool read_depth_data(const std::string& filename) = 0;

    // 读取成交数据
    virtual bool read_transaction_data(const std::string& filename) = 0;

    // 读取K线数据
    virtual bool read_kline_data(const std::string& filename) = 0;

    // 获取交易所类型
    virtual exchange_type get_exchange_type() const = 0;

    // 获取处理后的深度行情数据
    virtual const std::vector<depth_market_data_field>& get_depth_data() const = 0;

    // 获取处理后的成交数据
    virtual const std::vector<transaction_field>& get_transaction_data() const = 0;

    // 获取处理后的K线数据
    virtual const std::vector<kline_market_data_field>& get_kline_data() const = 0;

    // 获取品种信息
    virtual const instrument_info& get_instrument() const = 0;

    // 获取品种符号
    virtual const std::string& get_instrument_symbol() const = 0;

    // 从配置文件初始化
    virtual bool init_from_config(const std::string& config_path) = 0;

    // 清除所有数据
    virtual void clear() = 0;
};

// 创建交易所预处理器的工厂函数
std::unique_ptr<exchange_preprocessor> create_exchange_preprocessor(exchange_type exchange);

} // namespace cpp_backtest
} // namespace fast_trader_elite
