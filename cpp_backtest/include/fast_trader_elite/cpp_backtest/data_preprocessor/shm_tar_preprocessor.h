#pragma once

#include <cpp_frame/shm/shm_common.h>
#include <cpp_frame/shm/shm_config.h>
#include <cpp_frame/shm/shm_tar_page_reader.h>
#include <fast_trader_elite/cpp_backtest/data_preprocessor/exchange_preprocessor.h>
#include <fast_trader_elite/cpp_backtest/types.h>
#include <fast_trader_elite/data_model/field.h>
#include <fast_trader_elite/data_model/msgid.h>
#include <fast_trader_elite/data_model/type.h>
#include <memory>
#include <nlohmann_json/json.hpp>
#include <string>
#include <unordered_map>
#include <vector>

namespace fast_trader_elite {
namespace cpp_backtest {

// SHM TAR 数据预处理器
class shm_tar_preprocessor : public exchange_preprocessor {
public:
  shm_tar_preprocessor();
  ~shm_tar_preprocessor() override;

  // 从JSON配置文件初始化
  bool init_from_config(const std::string &config_path) override;

  // 读取深度行情数据
  bool read_depth_data(const std::string &filename) override;

  // 读取成交数据
  bool read_transaction_data(const std::string &filename) override;

  // 读取K线数据
  bool read_kline_data(const std::string &filename) override;

  // 获取交易所类型
  exchange_type get_exchange_type() const override;

  // 获取处理后的深度行情数据
  const std::vector<depth_market_data_field> &get_depth_data() const override;

  // 获取处理后的成交数据
  const std::vector<transaction_field> &get_transaction_data() const override;

  // 获取处理后的K线数据
  const std::vector<kline_market_data_field> &get_kline_data() const override;

  // 获取品种信息
  const instrument_info &get_instrument() const override;

  // 获取品种符号
  const std::string &get_instrument_symbol() const override;

  // 清除所有数据
  void clear() override;

private:
  // 从SHM TAR文件读取数据
  bool read_shm_tar_data(const std::string &filename);

  // 处理从SHM TAR读取的数据
  void process_frame(const cpp_frame::shm::frame_header *header);

  // 处理深度行情数据
  void process_depth_market_data(const cpp_frame::shm::frame_header *header);

  // 处理成交数据
  void process_transaction_data(const cpp_frame::shm::frame_header *header);

  // 处理K线数据
  void process_kline_market_data(const cpp_frame::shm::frame_header *header);

  // 添加品种信息
  void
  add_instrument(const std::string &symbol,
                 const nlohmann::json &instrument_config = nlohmann::json());

private:
  // 解析JSON配置
  bool parse_config(const nlohmann::json &config);

  std::vector<depth_market_data_field> depth_data_;
  std::vector<transaction_field> transaction_data_;
  std::vector<kline_market_data_field> kline_data_;
  instrument_info instrument_;
  std::string instrument_symbol_;
  exchange_type exchange_type_;
  nlohmann::json config_;    // 存储配置信息
  std::string shm_tar_path_; // SHM TAR文件路径
  int start_page_;           // 开始页
  int end_page_;             // 结束页
};

} // namespace cpp_backtest
} // namespace fast_trader_elite
