#pragma once

#include <fast_trader_elite/cpp_backtest/data_preprocessor/exchange_preprocessor.h>
#include <fstream>
#include <sstream>
#include <iostream>
#include <cstring>
#include <sys/mman.h>
#include <sys/stat.h>
#include <fcntl.h>
#include <unistd.h>
#include <simdjson.h>
#include <map>
#include <nlohmann_json/json.hpp>

namespace fast_trader_elite {
namespace cpp_backtest {

// Bybit订单簿类
class bybit_orderbook {
public:
    void init(double price_tick, int level = 5) {
        price_tick_ = price_tick;
        level_ = level;
        top_level_ask_.resize(level);
        top_level_bid_.resize(level);
    }

    int64_t convert_price(double price) { return int64_t(price / price_tick_); }

    void clear() {
        asks_.clear();
        bids_.clear();
    }

    void update_ask(double ask_p, double ask_v) {
        int64_t p = convert_price(ask_p);
        if (ask_v == 0) {
            asks_.erase(p);
        } else {
            asks_[p] = ask_v;
        }
    }

    void update_bid(double bid_p, double bid_v) {
        int64_t p = convert_price(bid_p);
        if (bid_v == 0) {
            bids_.erase(p);
        } else {
            bids_[p] = bid_v;
        }
    }

    std::vector<std::pair<double, double>>& get_asks() {
        size_t i = 0;
        for (auto it = asks_.begin(); it != asks_.end(); it++) {
            if (i < level_) {
                top_level_ask_[i].first = it->first * price_tick_;
                top_level_ask_[i].second = it->second;
                i++;
            } else {
                break;
            }
        }
        return top_level_ask_;
    }

    std::vector<std::pair<double, double>>& get_bids() {
        size_t i = 0;
        for (auto it = bids_.begin(); it != bids_.end(); it++) {
            if (i < level_) {
                top_level_bid_[i].first = it->first * price_tick_;
                top_level_bid_[i].second = it->second;
                i++;
            } else {
                break;
            }
        }
        return top_level_bid_;
    }

private:
    double price_tick_;
    size_t level_;
    std::map<int64_t, double, std::less<int>> asks_;
    std::map<int64_t, double, std::greater<int>> bids_;
    std::vector<std::pair<double, double>> top_level_ask_;
    std::vector<std::pair<double, double>> top_level_bid_;
};

// Bybit交易所数据预处理器
class bybit_preprocessor : public exchange_preprocessor {
public:
    bybit_preprocessor();
    ~bybit_preprocessor() override;

    // 从JSON配置文件初始化
    bool init_from_config(const std::string& config_path);

    // 读取深度行情数据
    bool read_depth_data(const std::string& filename) override;

    // 读取成交数据
    bool read_transaction_data(const std::string& filename) override;

    // 读取K线数据
    bool read_kline_data(const std::string& filename) override;

    // 获取交易所类型
    exchange_type get_exchange_type() const override;

    // 获取处理后的深度行情数据
    const std::vector<depth_market_data_field>& get_depth_data() const override;

    // 获取处理后的成交数据
    const std::vector<transaction_field>& get_transaction_data() const override;

    // 获取处理后的K线数据
    const std::vector<kline_market_data_field>& get_kline_data() const override;

    // 获取品种信息
    const instrument_info& get_instrument() const override;

    // 获取品种符号
    const std::string& get_instrument_symbol() const override;

    // 清除所有数据
    void clear() override;

private:
    // 解析Bybit订单簿数据
    bool parse_orderbook_data(const std::string& line, int64_t& timestamp);

    // 处理订单簿快照数据
    bool process_orderbook_snapshot(simdjson::ondemand::document& doc, int64_t timestamp);

    // 处理订单簿增量数据
    bool process_orderbook_delta(simdjson::ondemand::document& doc, int64_t timestamp);

    // 从订单簿生成深度行情数据
    void generate_depth_from_orderbook(const std::string& symbol, int64_t timestamp);

    // 解析Bybit成交CSV行
    bool parse_transaction_csv_line(const std::string& line);

    // 解析Bybit K线CSV行
    bool parse_kline_csv_line(const std::string& line);

    // 分割CSV行
    std::vector<std::string> split_csv_line(const std::string& line, char delimiter = ',');

    // 添加品种信息
    void add_instrument(const std::string& symbol, const nlohmann::json& instrument_config = nlohmann::json());

private:
    // 解析JSON配置
    bool parse_config(const nlohmann::json& config);

    std::vector<depth_market_data_field> depth_data_;
    std::vector<transaction_field> transaction_data_;
    std::vector<kline_market_data_field> kline_data_;
    instrument_info instrument_;
    std::string instrument_symbol_;
    uint16_t next_instrument_idx_;
    bybit_orderbook orderbook_;
    simdjson::ondemand::parser parser_;
    nlohmann::json config_; // 存储配置信息
    std::string depth_data_path_; // 深度数据文件路径
    std::string transaction_data_path_; // 成交数据文件路径
    std::string kline_data_path_; // K线数据文件路径
};

} // namespace cpp_backtest
} // namespace fast_trader_elite
