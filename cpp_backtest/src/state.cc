#include <fast_trader_elite/cpp_backtest/state.h>

namespace fast_trader_elite {
namespace cpp_backtest {

state::state(double initial_balance, fee_model *fee_model)
    : initial_balance_(initial_balance), fee_model_(fee_model) {}

void state::apply_fill(const internal_order &order) {
  uint16_t instrument_idx = order.instrument_idx;

  // 获取或创建状态值
  auto &values = values_[instrument_idx];

  // 如果是新创建的状态值，初始化余额
  if (values.balance == 0.0) {
    values.balance = initial_balance_;
  }

  // 更新持仓
  if (order.side == side_type::buy) {
    values.position += order.executed_quantity;
  } else if (order.side == side_type::sell) {
    values.position -= order.executed_quantity;
  }

  // 更新余额
  double amount = order.executed_quantity * order.executed_price;
  if (order.side == side_type::buy) {
    values.balance -= amount;
    values.cost += amount;
  } else if (order.side == side_type::sell) {
    values.balance += amount;
    values.cost -= amount;
  }

  // 计算并更新手续费
  double fee = 0.0;
  if (fee_model_) {
    fee = fee_model_->calculate_fee(order.executed_price,
                                    order.executed_quantity, order.is_maker);
  } else {
    fee = order.fee; // 如果没有手续费模型，使用订单中的手续费
  }

  values.fee += fee;
  // 注意：不从余额中扣除手续费，与 hftbacktest 保持一致

  // 更新交易统计数据
  values.num_trades += 1;
  values.trading_volume += order.executed_quantity;
  values.trading_value += amount;

  // 更新最后价格
  last_prices_[instrument_idx] = order.executed_price;
  values.last_price = order.executed_price;

  // 计算 PNL
  // PNL = 当前价格 * 持仓 - 成本
  values.pnl = values.last_price * values.position - (values.cost);
}

const state_values &state::get_values(uint16_t instrument_idx) const {
  static state_values empty_values;
  auto it = values_.find(instrument_idx);
  if (it != values_.end()) {
    return it->second;
  }
  return empty_values;
}

double state::equity(uint16_t instrument_idx, double price) const {
  auto it = values_.find(instrument_idx);
  if (it == values_.end()) {
    return initial_balance_;
  }

  const auto &values = it->second;

  // 计算总权益（净值）= 余额 + 持仓 * 当前价格 - 手续费
  // 这与 hftbacktest 中 LinearAsset 的 equity 计算方法一致
  // balance + self.contract_size * position * price - fee
  // 其中 contract_size = 1.0
  return values.balance + values.position * price - values.fee;
}

void state::reset(double initial_balance) {
  // 清空所有状态值和价格记录
  values_.clear();
  last_prices_.clear();

  // 设置初始余额
  initial_balance_ = initial_balance;

  // 注意：实际的状态值重置是通过清空 values_ 映射实现的
  // 当下一次调用 apply_fill 时，会创建新的状态值并初始化余额
  // 其他字段（position, fee, pnl, num_trades 等）在创建时会被初始化为零
}

} // namespace cpp_backtest
} // namespace fast_trader_elite
