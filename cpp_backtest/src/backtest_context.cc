#include <fast_trader_elite/cpp_backtest/backtest_context.h>
#include <fast_trader_elite/data_model/field.h>
#include <fast_trader_elite/cpp_backtest/backtest_logger.h>
#include <iostream>
#include <cstring>

namespace fast_trader_elite {
namespace cpp_backtest {

backtest_context::backtest_context(local_processor* local_processor)
    : local_processor_(local_processor)
    , current_timestamp_(0)
    , next_timer_id_(1)
    , next_timer_with_timestamp_id_(1) {
}

backtest_context::backtest_context(local_processor* local_processor, const strategy_instance_config& strategy_config)
    : local_processor_(local_processor)
    , current_timestamp_(0)
    , next_timer_id_(1)
    , next_timer_with_timestamp_id_(1)
    , strategy_config_(strategy_config) {
}

void backtest_context::subscribe(fast_trader_elite::md_sub_code_field* f) {
    // 回测环境中不需要实际订阅，因为数据已经预先加载
}

void backtest_context::get_kline(fast_trader_elite::md_kline_req_field* f, int request_id) {
    // 回测环境中不需要实际请求K线数据，因为数据已经预先加载

    // 调用local_processor处理K线请求
    if (local_processor_) {
        local_processor_->process_get_kline(f, request_id);
    }
}

void backtest_context::get_recent_transaction(fast_trader_elite::transaction_req_field* f, int request_id) {
    // 回测环境中不需要实际请求成交数据，因为数据已经预先加载
    // 调用local_processor处理成交请求
    if (local_processor_) {
        local_processor_->process_get_recent_transaction(f, request_id);
    }
}

int64_t backtest_context::insert_order(fast_trader_elite::order_input_field* f) {
    // 提交订单
    return local_processor_->submit_order(f, current_timestamp_);
}

int64_t backtest_context::insert_order_list(fast_trader_elite::order_input_list_field* f) {
    // 提交订单列表
    // 在实际实现中，我们需要遍历列表中的所有订单
    // 这里简化处理，直接返回一个订单ID
    return 1;
}

int64_t backtest_context::cancel_order(fast_trader_elite::order_action_field* f) {
    // 取消订单
    return local_processor_->cancel_order(f, current_timestamp_);
}

int64_t backtest_context::cancel_order_list(fast_trader_elite::order_action_list_field* f) {
    // 安全检查：确保f不为空
    if (f == nullptr) {
        BT_LOG_ERROR("cancel_order_list_failed reason:null_action_list_pointer");
        return -1;
    }

    // 安全检查：确保local_processor_不为空
    if (local_processor_ == nullptr) {
        BT_LOG_ERROR("cancel_order_list_failed reason:null_processor_pointer");
        return -1;
    }

    // 取消订单列表
    // 需要遍历列表中的所有订单，并为每个订单创建一个取消请求
    int64_t last_order_id = -1;

    // 检查订单数量
    if (f->count <= 0) {
        BT_LOG_WARN("cancel_order_list_failed reason:empty_order_list");
        return -1;
    }

    // 注意：在C++中，数组成员变量不能为nullptr，所以这里不需要检查f->order_ids

    // 遍历所有订单
    for (uint16_t i = 0; i < f->count; ++i) {
        // 安全检查：确保订单ID有效
        if (f->order_ids[i] <= 0) {
            BT_LOG_WARN("cancel_order_list_skipped order_id:{} reason:invalid_order_id", f->order_ids[i]);
            continue;
        }

        // 创建取消请求
        fast_trader_elite::order_action_field action;
        memset(&action, 0, sizeof(fast_trader_elite::order_action_field));

        // 复制取消信息
        strncpy(action.instrument_name, f->instrument_name, INSTRUMENT_ID_LEN - 1);
        strncpy(action.instrument_id, f->instrument_id, INSTRUMENT_ID_LEN - 1);
        action.instrument_idx = f->instrument_idx;
        action.exchange_id = f->exchange_id;
        action.ins_type = f->ins_type;
        action.order_id = f->order_ids[i];
        action.local_order_id = f->local_order_ids[i];
        action.trading_account_id = f->trading_account_id;
        action.strategy_instance_id = f->strategy_instance_id;

        // 取消订单
        last_order_id = cancel_order(&action);

        // 如果取消失败，继续处理下一个订单
        if (last_order_id < 0) {
            BT_LOG_WARN("Failed to cancel order: {}", f->order_ids[i]);
        }
    }

    // 返回最后一个取消的订单ID
    return last_order_id;
}

int64_t backtest_context::cancel_order_all(fast_trader_elite::order_action_field* f) {
    // 获取所有订单
    const auto& all_orders = local_processor_->get_orders();

    // 记录最后一个取消的订单ID
    int64_t last_order_id = -1;
    int cancel_count = 0;

    // 遍历所有订单，找出需要取消的订单
    for (const auto& [order_id, order] : all_orders) {
        // 检查订单是否属于指定的品种
        if (order.instrument_idx == f->instrument_idx &&
            (order.status == order_status::new_order || order.status == order_status::partially_filled)) {

            // 创建取消请求
            fast_trader_elite::order_action_field action;
            memset(&action, 0, sizeof(fast_trader_elite::order_action_field));

            // 复制取消信息
            strncpy(action.instrument_name, f->instrument_name, INSTRUMENT_ID_LEN - 1);
            strncpy(action.instrument_id, f->instrument_id, INSTRUMENT_ID_LEN - 1);
            action.instrument_idx = f->instrument_idx;
            action.exchange_id = f->exchange_id;
            action.ins_type = f->ins_type;
            action.order_id = order.order_id;
            action.local_order_id = 0; // 在回测中不需要本地订单ID
            action.trading_account_id = f->trading_account_id;
            action.strategy_instance_id = f->strategy_instance_id;

            // 取消订单
            int64_t result = cancel_order(&action);

            // 如果取消成功，更新最后一个取消的订单ID
            if (result >= 0) {
                last_order_id = result;
                cancel_count++;
            }
        }
    }

    std::cout << "Canceled " << cancel_count << " orders" << std::endl;
    return cancel_count > 0 ? last_order_id : 0;
}

int64_t backtest_context::close_order_all(fast_trader_elite::order_input_field* f) {
    return 0;
}

int backtest_context::get_postion(fast_trader_elite::position_req_field* f, int request_id) {
    // 调用local_processor处理持仓请求
    if (local_processor_) {
        local_processor_->process_get_position(f, request_id);
    }

    return request_id;
}

int backtest_context::get_wallet_banlance(fast_trader_elite::wallet_balance_req_field* f, int request_id) {
    // 调用local_processor处理钱包余额请求
    if (local_processor_) {
        local_processor_->process_get_wallet_balance(f, request_id);
    }

    return request_id;
}

int backtest_context::set_leverage(fast_trader_elite::leverage_req_field* f, int request_id) {
    // 回测环境中不需要实际设置杠杆
    return request_id;
}

int backtest_context::register_timer(uint32_t milsec, const timer_function&& f, bool repeat) {
    // 注册定时器
    int timer_id = next_timer_id_++;

    timer_info info;
    info.interval_ms = milsec;
    info.next_trigger_time = current_timestamp_ + milsec * 1000000; // 转换为纳秒，1毫秒=1000000纳秒
    info.callback = std::move(f);
    info.repeat = repeat;
    info.active = true;

    timers_[timer_id] = std::move(info);

    return timer_id;
}

void backtest_context::unregister_timer(int timer_id) {
    // 注销定时器
    auto it = timers_.find(timer_id);
    if (it != timers_.end()) {
        it->second.active = false;
    }
}

void backtest_context::init_instruments(const std::vector<instrument_info>& instrument_infos) {
    // 清空现有的品种信息
    instruments_.clear();
    instrument_idx_map_.clear();

    // 遍历所有品种信息，转换为instrument_field并存储
    for (const auto& info : instrument_infos) {
        fast_trader_elite::instrument_field field;

        // 复制品种信息
        std::strncpy(field.instrument_id, info.instrument_id, sizeof(field.instrument_id) - 1);
        field.instrument_id[sizeof(field.instrument_id) - 1] = '\0';

        // 使用instrument_id作为instrument_name（如果没有特殊需求）
        std::strncpy(field.instrument_name, info.instrument_id, sizeof(field.instrument_name) - 1);
        field.instrument_name[sizeof(field.instrument_name) - 1] = '\0';

        field.instrument_idx = info.instrument_idx;
        field.exchange_id = static_cast<fast_trader_elite::exchange_type>(info.exchange_id);
        field.tick_size = info.tick_size;
        field.step_size = info.step_size;
        field.contract_value = 1.0; // 默认值，可以根据需要调整

        // 存储到映射中
        instruments_[field.instrument_id] = field;
        instrument_idx_map_[field.instrument_idx] = field;
    }
}

fast_trader_elite::instrument_field* backtest_context::get_instrument_filed(fast_trader_elite::exchange_type ex, uint16_t instrument_idx) {
    // 获取品种信息
    auto it = instrument_idx_map_.find(instrument_idx);
    if (it != instrument_idx_map_.end() && it->second.exchange_id == ex) {
        return &it->second;
    }
    return nullptr;
}

fast_trader_elite::instrument_field* backtest_context::get_instrument_filed(fast_trader_elite::exchange_type ex, const char* instrument_id) {
    // 获取品种信息
    auto it = instruments_.find(instrument_id);
    if (it != instruments_.end() && it->second.exchange_id == ex) {
        return &it->second;
    }
    return nullptr;
}

std::vector<fast_trader_elite::instrument_field*> backtest_context::get_all_instruments(fast_trader_elite::exchange_type ex) {
    // 获取所有品种信息
    std::vector<fast_trader_elite::instrument_field*> result;
    for (auto& [id, instrument] : instruments_) {
        if (instrument.exchange_id == ex) {
            result.push_back(&instrument);
        }
    }
    return result;
}

void backtest_context::register_async_task(std::function<void()> func) {
    // 回测环境中不支持异步任务，直接执行
    func();
}

int backtest_context::register_async_timer(uint32_t milsec, const timer_function&& f, bool repeat) {
    // 回测环境中异步定时器与普通定时器相同
    return register_timer(milsec, std::move(f), repeat);
}

void backtest_context::unregister_async_timer(int timer_id) {
    // 注销异步定时器
    unregister_timer(timer_id);
}

int backtest_context::register_timer_with_timestamp(uint32_t milsec, const timer_function_with_timestamp&& f, bool repeat) {
    // 注册带时间戳的定时器
    int timer_id = next_timer_with_timestamp_id_++;

    timer_info_with_timestamp info;
    info.interval_ms = milsec;
    info.next_trigger_time = current_timestamp_ + milsec * 1000000; // 转换为纳秒，1毫秒=1000000纳秒
    info.callback = std::move(f);
    info.repeat = repeat;
    info.active = true;

    timers_with_timestamp_[timer_id] = std::move(info);

    return timer_id;
}

void backtest_context::unregister_timer_with_timestamp(int timer_id) {
    // 注销带时间戳的定时器
    auto it = timers_with_timestamp_.find(timer_id);
    if (it != timers_with_timestamp_.end()) {
        it->second.active = false;
    }
}

int backtest_context::register_async_timer_with_timestamp(uint32_t milsec, const timer_function_with_timestamp&& f, bool repeat) {
    // 回测环境中异步定时器与普通定时器相同
    return register_timer_with_timestamp(milsec, std::move(f), repeat);
}

void backtest_context::unregister_async_timer_with_timestamp(int timer_id) {
    // 注销异步带时间戳的定时器
    unregister_timer_with_timestamp(timer_id);
}

strategy_instance_config backtest_context::get_strategy_config(const std::string& strategy_name) {
    // 如果策略名称与配置中的名称匹配，或者配置中的名称为空（默认配置），返回配置
    if (strategy_name == strategy_config_.strategy_instance_name || strategy_config_.strategy_instance_name.empty()) {
        return strategy_config_;
    }

    // 如果策略名称不匹配，但我们有配置，返回它
    // 这是因为我们只支持一个策略，所以无论请求什么名称，都返回同一个配置
    if (!strategy_config_.strategy_instance_name.empty()) {
        return strategy_config_;
    }

    // 如果没有配置，返回默认配置
    strategy_instance_config default_config;
    default_config.strategy_instance_name = strategy_name;
    default_config.log_level = "INFO";
    default_config.log_file = "../log/" + strategy_name + ".log";
    default_config.strategy_config_path = "";
    return default_config;
}

void backtest_context::set_current_timestamp(int64_t timestamp) {
    current_timestamp_ = timestamp;
}

int64_t backtest_context::get_current_timestamp() const {
    return current_timestamp_;
}

void backtest_context::process_timers() {
    // 处理普通定时器
    for (auto it = timers_.begin(); it != timers_.end(); ) {
        if (!it->second.active) {
            it = timers_.erase(it);
            continue;
        }

        if (current_timestamp_ >= it->second.next_trigger_time) {
            // 执行回调
            it->second.callback();

            if (it->second.repeat) {
                // 更新下一次触发时间
                it->second.next_trigger_time = current_timestamp_ + it->second.interval_ms * 1000000; // 转换为纳秒，1毫秒=1000000纳秒
                ++it;
            } else {
                // 一次性定时器，执行后移除
                it = timers_.erase(it);
            }
        } else {
            ++it;
        }
    }

    // 处理带时间戳的定时器
    for (auto it = timers_with_timestamp_.begin(); it != timers_with_timestamp_.end(); ) {
        if (!it->second.active) {
            it = timers_with_timestamp_.erase(it);
            continue;
        }

        if (current_timestamp_ >= it->second.next_trigger_time) {
            // 执行回调，传递当前时间戳
            it->second.callback(current_timestamp_);

            if (it->second.repeat) {
                // 更新下一次触发时间
                it->second.next_trigger_time = current_timestamp_ + it->second.interval_ms * 1000000; // 转换为纳秒，1毫秒=1000000纳秒
                ++it;
            } else {
                // 一次性定时器，执行后移除
                it = timers_with_timestamp_.erase(it);
            }
        } else {
            ++it;
        }
    }
}
} // namespace cpp_backtest
} // namespace fast_trader_elite
