#include <fast_trader_elite/cpp_backtest/backtest_data.h>
#include <algorithm>
#include <numeric>
#include <cmath>
#include <limits>
#include <unordered_map>

namespace fast_trader_elite {
namespace cpp_backtest {

// 一天的纳秒数
constexpr int64_t NANOSECONDS_PER_DAY = 86400000000000LL;

backtest_data::backtest_data() {
    // 初始化为空
}

void backtest_data::add_record(uint16_t asset_no, const record_item& item) {
    records_[asset_no].push_back(item);
}

const std::unordered_map<uint16_t, std::vector<record_item>>& backtest_data::get_records() const {
    return records_;
}

const std::vector<record_item>& backtest_data::get_asset_records(uint16_t asset_no) const {
    auto it = records_.find(asset_no);
    if (it != records_.end()) {
        return it->second;
    }
    return get_empty_records();
}

void backtest_data::add_trade(uint16_t asset_no, const internal_trade& trade) {
    trades_[asset_no].push_back(trade);
}

const std::unordered_map<uint16_t, std::vector<internal_trade>>& backtest_data::get_trades() const {
    return trades_;
}

const std::vector<internal_trade>& backtest_data::get_asset_trades(uint16_t asset_no) const {
    auto it = trades_.find(asset_no);
    if (it != trades_.end()) {
        return it->second;
    }
    return get_empty_trades();
}

std::size_t backtest_data::num_assets() const {
    return records_.size();
}

void backtest_data::clear() {
    records_.clear();
    trades_.clear();
    cycles_.clear();
    cycle_stats_.clear();
}

int64_t backtest_data::get_start_timestamp(uint16_t asset_no) const {
    const auto& records = get_asset_records(asset_no);
    if (records.empty()) {
        return 0;
    }
    return records.front().timestamp;
}

int64_t backtest_data::get_end_timestamp(uint16_t asset_no) const {
    const auto& records = get_asset_records(asset_no);
    if (records.empty()) {
        return 0;
    }
    return records.back().timestamp;
}

double backtest_data::get_trading_days(uint16_t asset_no) const {
    int64_t start = get_start_timestamp(asset_no);
    int64_t end = get_end_timestamp(asset_no);
    if (start == 0 || end == 0) {
        return 0.0;
    }
    return static_cast<double>(end - start) / NANOSECONDS_PER_DAY;
}

double backtest_data::get_initial_equity(uint16_t asset_no) const {
    const auto& records = get_asset_records(asset_no);
    if (records.empty()) {
        return 0.0;
    }
    return records.front().equity;
}

double backtest_data::get_final_equity(uint16_t asset_no) const {
    const auto& records = get_asset_records(asset_no);
    if (records.empty()) {
        return 0.0;
    }
    return records.back().equity;
}

double backtest_data::get_max_equity(uint16_t asset_no) const {
    const auto& records = get_asset_records(asset_no);
    if (records.empty()) {
        return 0.0;
    }

    double max_equity = std::numeric_limits<double>::lowest();
    for (const auto& record : records) {
        max_equity = std::max(max_equity, record.equity);
    }
    return max_equity;
}

double backtest_data::get_min_equity(uint16_t asset_no) const {
    const auto& records = get_asset_records(asset_no);
    if (records.empty()) {
        return 0.0;
    }

    double min_equity = std::numeric_limits<double>::max();
    for (const auto& record : records) {
        min_equity = std::min(min_equity, record.equity);
    }
    return min_equity;
}

double backtest_data::get_return(uint16_t asset_no) const {
    // 直接使用净值计算回报率
    double initial = get_initial_equity(asset_no);
    double final_equity = get_final_equity(asset_no);

    if (initial <= 0.0) {
        return 0.0;
    }

    // 使用最终净值与初始净值的比值计算回报率
    return (final_equity - initial) / initial;
}

double backtest_data::get_annual_return(uint16_t asset_no) const {
    double total_return = get_return(asset_no);
    double days = get_trading_days(asset_no);

    if (days <= 0.0) {
        return 0.0;
    }

    // 假设一年有252个交易日
    return total_return * (252.0 / days);
}

double backtest_data::get_max_drawdown(uint16_t asset_no) const {
    const auto& records = get_asset_records(asset_no);
    if (records.empty()) {
        return 0.0;
    }

    double max_dd = 0.0;
    double peak = records[0].equity;

    for (const auto& record : records) {
        if (record.equity > peak) {
            peak = record.equity;
        }

        double dd = (peak - record.equity) / peak;
        max_dd = std::max(max_dd, dd);
    }

    return max_dd;
}

double backtest_data::get_sharpe_ratio(uint16_t asset_no, double risk_free_rate) const {
    std::vector<double> daily_returns = calculate_daily_returns(asset_no);
    if (daily_returns.empty()) {
        return 0.0;
    }

    // 计算日均收益率
    double mean_return = std::accumulate(daily_returns.begin(), daily_returns.end(), 0.0) / daily_returns.size();

    // 计算标准差
    double sum_squared_diff = 0.0;
    for (double ret : daily_returns) {
        double diff = ret - mean_return;
        sum_squared_diff += diff * diff;
    }

    double std_dev = std::sqrt(sum_squared_diff / daily_returns.size());
    if (std_dev <= 0.0) {
        return 0.0;
    }

    // 计算夏普比率（年化）
    double daily_risk_free = risk_free_rate / 252.0;
    double sharpe = (mean_return - daily_risk_free) / std_dev;

    // 年化夏普比率
    return sharpe * std::sqrt(252.0);
}

double backtest_data::get_sortino_ratio(uint16_t asset_no, double risk_free_rate) const {
    std::vector<double> daily_returns = calculate_daily_returns(asset_no);
    if (daily_returns.empty()) {
        return 0.0;
    }

    // 计算日均收益率
    double mean_return = std::accumulate(daily_returns.begin(), daily_returns.end(), 0.0) / daily_returns.size();

    // 计算下行标准差
    double sum_squared_neg_diff = 0.0;
    int neg_count = 0;

    for (double ret : daily_returns) {
        if (ret < 0) {
            double diff = ret;
            sum_squared_neg_diff += diff * diff;
            neg_count++;
        }
    }

    double downside_std_dev = 0.0;
    if (neg_count > 0) {
        downside_std_dev = std::sqrt(sum_squared_neg_diff / neg_count);
    }

    if (downside_std_dev <= 0.0) {
        return 0.0;
    }

    // 计算索提诺比率（年化）
    double daily_risk_free = risk_free_rate / 252.0;
    double sortino = (mean_return - daily_risk_free) / downside_std_dev;

    // 年化索提诺比率
    return sortino * std::sqrt(252.0);
}

double backtest_data::get_return_over_mdd(uint16_t asset_no) const {
    double ret = get_return(asset_no);
    double mdd = get_max_drawdown(asset_no);

    if (mdd <= 0.0) {
        return 0.0;
    }

    return ret / mdd;
}

double backtest_data::get_return_over_trade(uint16_t asset_no) const {
    double ret = get_return(asset_no);
    const auto& records = get_asset_records(asset_no);

    if (records.empty()) {
        return 0.0;
    }

    double trading_value = records.back().trading_value;
    if (trading_value <= 0.0) {
        return 0.0;
    }

    return ret / trading_value;
}

double backtest_data::get_avg_position_time(uint16_t asset_no) const {
    // 使用仓位周期统计信息计算平均持仓时间
    cycle_statistics stats = get_cycle_statistics(asset_no);
    return stats.avg_duration;
}

std::vector<position_cycle> backtest_data::get_position_cycles(uint16_t asset_no) const {
    // 检查缓存
    auto it = cycles_.find(asset_no);
    if (it != cycles_.end()) {
        return it->second;
    }

    // 计算仓位周期
    const auto& trades = get_asset_trades(asset_no);
    std::vector<position_cycle> cycles = analyze_position_cycles(trades);

    // 缓存结果
    cycles_[asset_no] = cycles;

    return cycles;
}

cycle_statistics backtest_data::get_cycle_statistics(uint16_t asset_no) const {
    // 检查缓存
    auto it = cycle_stats_.find(asset_no);
    if (it != cycle_stats_.end()) {
        return it->second;
    }

    // 获取仓位周期
    const auto& cycles = get_position_cycles(asset_no);

    // 计算统计信息
    cycle_statistics stats = calculate_cycle_statistics(cycles);

    // 缓存结果
    cycle_stats_[asset_no] = stats;

    return stats;
}

double backtest_data::get_win_rate(uint16_t asset_no) const {
    // 使用仓位周期统计信息计算胜率
    cycle_statistics stats = get_cycle_statistics(asset_no);
    return stats.win_rate;
}

double backtest_data::get_profit_loss_ratio(uint16_t asset_no) const {
    // 使用仓位周期统计信息计算盈亏比
    cycle_statistics stats = get_cycle_statistics(asset_no);
    return stats.profit_loss_ratio;
}

double backtest_data::get_daily_trading_value(uint16_t asset_no) const {
    const auto& records = get_asset_records(asset_no);
    if (records.empty()) {
        return 0.0;
    }

    double days = get_trading_days(asset_no);
    if (days <= 0.0) {
        return 0.0;
    }

    return records.back().trading_value / days;
}

double backtest_data::get_max_leverage(uint16_t asset_no) const {
    const auto& records = get_asset_records(asset_no);
    if (records.empty()) {
        return 0.0;
    }

    double max_leverage = 0.0;
    for (const auto& record : records) {
        double position_value = std::abs(record.position * record.price);
        if (record.equity > 0) {
            double leverage = position_value / record.equity;
            max_leverage = std::max(max_leverage, leverage);
        }
    }

    return max_leverage;
}

int64_t backtest_data::get_total_trades(uint16_t asset_no) const {
    const auto& records = get_asset_records(asset_no);
    if (records.empty()) {
        return 0;
    }

    // 返回最后一条记录中的成交笔数，这是累计值
    return records.back().num_trades;
}

double backtest_data::get_total_trading_value(uint16_t asset_no) const {
    const auto& records = get_asset_records(asset_no);
    if (records.empty()) {
        return 0.0;
    }

    // 返回最后一条记录中的交易金额，这是累计值
    return records.back().trading_value;
}

int64_t backtest_data::get_position_cycles_count(uint16_t asset_no) const {
    // 获取仓位周期
    const auto& cycles = get_position_cycles(asset_no);

    // 返回周期数量
    return static_cast<int64_t>(cycles.size());
}

std::vector<double> backtest_data::calculate_daily_returns(uint16_t asset_no) const {
    const auto& records = get_asset_records(asset_no);
    if (records.size() < 2) {
        return {};
    }

    std::vector<double> daily_returns;
    daily_returns.reserve(records.size() - 1);

    for (size_t i = 1; i < records.size(); ++i) {
        double prev_equity = records[i-1].equity;
        double curr_equity = records[i].equity;

        if (prev_equity > 0) {
            daily_returns.push_back((curr_equity - prev_equity) / prev_equity);
        } else {
            daily_returns.push_back(0.0);
        }
    }

    return daily_returns;
}

std::vector<double> backtest_data::calculate_position_times(uint16_t asset_no) const {
    // 获取仓位周期
    const auto& cycles = get_position_cycles(asset_no);

    // 提取持仓时间
    std::vector<double> position_times;
    position_times.reserve(cycles.size());

    for (const auto& cycle : cycles) {
        position_times.push_back(cycle.duration_seconds);
    }

    return position_times;
}

const std::vector<record_item>& backtest_data::get_empty_records() {
    static std::vector<record_item> empty;
    return empty;
}

const std::vector<internal_trade>& backtest_data::get_empty_trades() {
    static std::vector<internal_trade> empty;
    return empty;
}

} // namespace cpp_backtest
} // namespace fast_trader_elite
