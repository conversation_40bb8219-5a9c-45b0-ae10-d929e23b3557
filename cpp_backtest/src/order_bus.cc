#include <fast_trader_elite/cpp_backtest/order_bus.h>
#include <algorithm>
#include <limits>

namespace fast_trader_elite {
namespace cpp_backtest {

std::optional<int64_t> order_bus::earliest_timestamp() const {
    if (order_list_.empty()) {
        return std::nullopt;
    }
    return order_list_.front().second;
}

void order_bus::append(const internal_order& order, int64_t timestamp) {
    int64_t latest_timestamp = 0;
    if (!order_list_.empty()) {
        latest_timestamp = order_list_.back().second;
    }
    timestamp = std::max(timestamp, latest_timestamp);
    order_list_.push_back(std::make_pair(order, timestamp));
}

void order_bus::reset() {
    order_list_.clear();
}

size_t order_bus::size() const {
    return order_list_.size();
}

bool order_bus::empty() const {
    return order_list_.empty();
}

std::optional<std::pair<internal_order, int64_t>> order_bus::pop_front() {
    if (order_list_.empty()) {
        return std::nullopt;
    }
    auto result = order_list_.front();
    order_list_.pop_front();
    return result;
}

} // namespace cpp_backtest
} // namespace fast_trader_elite
