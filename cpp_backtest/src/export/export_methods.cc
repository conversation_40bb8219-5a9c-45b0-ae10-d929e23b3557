#include <fast_trader_elite/cpp_backtest/backtest_engine.h>
#include <fast_trader_elite/cpp_backtest/export/exporter_factory.h>
#include <iostream>

namespace fast_trader_elite {
namespace cpp_backtest {

bool backtest_engine::export_records(const std::string& file_path) const {
    if (!exporter_) {
        std::cerr << "No exporter set. Use set_exporter() to set an exporter." << std::endl;
        return false;
    }

    return exporter_->export_records(data_, file_path);
}

bool backtest_engine::export_trades(const std::string& file_path) const {
    if (!exporter_) {
        std::cerr << "No exporter set. Use set_exporter() to set an exporter." << std::endl;
        return false;
    }

    return exporter_->export_trades(data_, file_path);
}

bool backtest_engine::export_pnl(const std::string& file_path) const {
    if (!exporter_) {
        std::cerr << "No exporter set. Use set_exporter() to set an exporter." << std::endl;
        return false;
    }

    return exporter_->export_pnl(data_, file_path);
}

} // namespace cpp_backtest
} // namespace fast_trader_elite
