#include <fast_trader_elite/cpp_backtest/export/csv_exporter.h>
#include <fstream>
#include <iomanip>
#include <sstream>
#include <chrono>
#include <ctime>
#include <filesystem>
#include <iostream>
#include <algorithm>

namespace fast_trader_elite {
namespace cpp_backtest {

bool csv_exporter::export_records(const backtest_data& data, const std::string& file_path) {
    try {
        // 创建目录（如果不存在）
        std::filesystem::path path(file_path);
        std::filesystem::create_directories(path.parent_path());

        // 获取记录数据
        const auto& records = data.get_records();

        // 确保目录路径以斜杠结尾
        std::string dir = path.parent_path().string();
        if (!dir.empty() && dir.back() != '/' && dir.back() != '\\') {
            dir += '/';
        }

        // 获取文件名（不带扩展名）
        std::string filename = path.stem().string();

        // 为每个资产创建一个CSV文件
        for (const auto& [asset_no, asset_records] : records) {
            if (asset_records.empty()) {
                continue;
            }

            // 创建文件名
            std::string asset_file_path = dir + filename + "_" + std::to_string(asset_no) + ".csv";

            // 打开文件
            std::ofstream file(asset_file_path);
            if (!file.is_open()) {
                std::cerr << "Failed to open file: " << asset_file_path << std::endl;
                continue;
            }

            // 写入标题行（参考 hftbacktest 的格式）
            file << "timestamp,ts,price,position,balance,equity,fee,num_trades,trading_volume,trading_value,cost\n";

            // 写入记录数据
            for (const auto& record : asset_records) {
                file << record.timestamp << ","
                     << format_timestamp(record.timestamp) << ","
                     << record.price << ","
                     << record.position << ","
                     << record.balance << ","
                     << record.equity << ","
                     << record.fee << ","
                     << record.num_trades << ","
                     << record.trading_volume << ","
                     << record.trading_value << ","
                     << record.cost << "\n";
            }

            file.close();
            std::cout << "Exported " << asset_records.size() << " records to " << asset_file_path << std::endl;
        }

        return true;
    } catch (const std::exception& e) {
        std::cerr << "Error exporting records: " << e.what() << std::endl;
        return false;
    }
}

bool csv_exporter::export_trades(const backtest_data& data, const std::string& file_path) {
    try {
        // 创建目录（如果不存在）
        std::filesystem::path path(file_path);
        std::filesystem::create_directories(path.parent_path());

        // 获取成交记录
        const auto& trades = data.get_trades();

        // 确保目录路径以斜杠结尾
        std::string dir = path.parent_path().string();
        if (!dir.empty() && dir.back() != '/' && dir.back() != '\\') {
            dir += '/';
        }

        // 获取文件名（不带扩展名）
        std::string filename = path.stem().string();

        // 为每个资产创建一个CSV文件
        for (const auto& [asset_no, asset_trades] : trades) {
            if (asset_trades.empty()) {
                continue;
            }

            // 创建文件名
            std::string asset_file_path = dir + filename + "_" + std::to_string(asset_no) + ".csv";

            // 打开文件
            std::ofstream file(asset_file_path);
            if (!file.is_open()) {
                std::cerr << "Failed to open file: " << asset_file_path << std::endl;
                continue;
            }

            // 写入标题行
            file << "timestamp,ts,order_id,price,quantity,side,is_maker,fee,trading_value\n";

            // 写入成交记录
            for (const auto& trade : asset_trades) {
                file << trade.timestamp << ","
                     << format_timestamp(trade.timestamp) << ","
                     << trade.order_id << ","
                     << trade.price << ","
                     << trade.quantity << ","
                     << (trade.side == side_type::buy ? "buy" : "sell") << ","
                     << (trade.is_maker ? "true" : "false") << ","
                     << trade.fee << ","
                     << trade.trading_value << "\n";
            }

            file.close();
            std::cout << "Exported " << asset_trades.size() << " trades to " << asset_file_path << std::endl;
        }

        return true;
    } catch (const std::exception& e) {
        std::cerr << "Error exporting trades: " << e.what() << std::endl;
        return false;
    }
}

bool csv_exporter::export_pnl(const backtest_data& data, const std::string& file_path) {
    try {
        // 创建目录（如果不存在）
        std::filesystem::path path(file_path);
        std::filesystem::create_directories(path.parent_path());

        // 确保目录路径以斜杠结尾
        std::string dir = path.parent_path().string();
        if (!dir.empty() && dir.back() != '/' && dir.back() != '\\') {
            dir += '/';
        }

        // 获取文件名（不带扩展名）
        std::string filename = path.stem().string();

        // 获取成交数据
        const auto& trades = data.get_trades();

        // 为每个资产创建一个CSV文件
        for (const auto& [asset_no, asset_trades] : trades) {
            if (asset_trades.empty()) {
                continue;
            }

            // 创建文件名
            std::string asset_file_path = dir + filename + "_" + std::to_string(asset_no) + ".csv";

            // 打开文件
            std::ofstream file(asset_file_path);
            if (!file.is_open()) {
                std::cerr << "Failed to open file: " << asset_file_path << std::endl;
                continue;
            }

            // 写入标题行
            file << "timestamp,ts,pnl,cumulative_pnl\n";

            // 获取仓位周期
            std::vector<position_cycle> cycles = data.get_position_cycles(asset_no);

            // 按时间戳排序的PNL记录
            std::vector<std::pair<int64_t, double>> pnl_records;

            // 从仓位周期中提取PNL数据
            for (const auto& cycle : cycles) {
                // 找到平仓交易
                if (!cycle.trades.empty()) {
                    const auto& last_trade = cycle.trades.back();
                    pnl_records.push_back({last_trade.timestamp, cycle.pnl});
                }
            }

            // 按时间戳排序
            std::sort(pnl_records.begin(), pnl_records.end(),
                     [](const auto& a, const auto& b) { return a.first < b.first; });

            // 写入 PNL 数据
            double running_pnl = 0.0;
            for (const auto& [timestamp, pnl] : pnl_records) {
                running_pnl += pnl;
                file << timestamp << ","
                     << format_timestamp(timestamp) << ","
                     << pnl << ","
                     << running_pnl << "\n";
            }

            file.close();
            std::cout << "Exported " << pnl_records.size() << " PNL points to " << asset_file_path << std::endl;
        }

        return true;
    } catch (const std::exception& e) {
        std::cerr << "Error exporting PNL data: " << e.what() << std::endl;
        return false;
    }
}

std::string csv_exporter::format_timestamp(int64_t timestamp) const {
    // 将纳秒时间戳转换为秒
    time_t seconds = timestamp / 1000000000;

    // 获取毫秒部分
    int milliseconds = (timestamp / 1000000) % 1000;

    // 转换为本地时间
    struct tm timeinfo;
    #ifdef _WIN32
        localtime_s(&timeinfo, &seconds);
    #else
        localtime_r(&seconds, &timeinfo);
    #endif

    // 格式化为字符串
    std::ostringstream oss;
    oss << std::put_time(&timeinfo, "%Y-%m-%d %H:%M:%S");
    oss << "." << std::setfill('0') << std::setw(3) << milliseconds;

    return oss.str();
}

} // namespace cpp_backtest
} // namespace fast_trader_elite
