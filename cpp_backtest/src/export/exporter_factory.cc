#include <fast_trader_elite/cpp_backtest/export/exporter_factory.h>
#include <fast_trader_elite/cpp_backtest/export/csv_exporter.h>
#include <fast_trader_elite/cpp_backtest/export/npz_exporter.h>
#include <filesystem>
#include <iostream>

namespace fast_trader_elite {
namespace cpp_backtest {

std::shared_ptr<exporter> exporter_factory::create(format fmt) {
    switch (fmt) {
        case format::CSV:
            return csv_exporter::create();
        case format::NPZ:
            return npz_exporter::create();
        default:
            std::cerr << "Unknown exporter format, using CSV exporter" << std::endl;
            return csv_exporter::create();
    }
}

std::shared_ptr<exporter> exporter_factory::create_from_extension(const std::string& file_path) {
    std::filesystem::path path(file_path);
    std::string ext = path.extension().string();
    
    // 转换为小写
    for (auto& c : ext) {
        c = std::tolower(c);
    }
    
    if (ext == ".npz") {
        return create(format::NPZ);
    } else {
        // 默认使用CSV格式
        return create(format::CSV);
    }
}

} // namespace cpp_backtest
} // namespace fast_trader_elite
