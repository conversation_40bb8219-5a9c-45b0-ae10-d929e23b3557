#include <fast_trader_elite/cpp_backtest/export/npz_exporter.h>
#include <fast_trader_elite/cpp_backtest/types.h>
#include <fstream>
#include <iomanip>
#include <sstream>
#include <filesystem>
#include <iostream>
#include <vector>

namespace fast_trader_elite {
namespace cpp_backtest {

void npz_exporter::write_npy_header(std::ofstream& file, size_t num_records) {
    // NPY格式魔数
    const char magic[] = {'\x93', 'N', 'U', 'M', 'P', 'Y'};
    file.write(magic, 6);

    // 版本
    const char version[] = {1, 0};
    file.write(version, 2);

    // 构建头部字符串
    std::stringstream header;
    header << "{'descr': [('timestamp', '<i8'), ('price', '<f8'), ('position', '<f8'), "
           << "('balance', '<f8'), ('fee', '<f8'), ('num_trades', '<i8'), "
           << "('trading_volume', '<f8'), ('trading_value', '<f8')], "
           << "'fortran_order': False, 'shape': (" << num_records << ",)}";

    // 头部长度（包括填充）
    std::string header_str = header.str();
    uint16_t header_len = header_str.size() + 1; // +1 for newline
    while ((header_len + 10) % 16 != 0) {
        header_len++;
    }

    // 写入头部长度
    file.write(reinterpret_cast<char*>(&header_len), 2);

    // 写入头部
    file.write(header_str.c_str(), header_str.size());
    file.put('\n');

    // 填充
    int padding = header_len - header_str.size() - 1;
    for (int i = 0; i < padding; i++) {
        file.put(' ');
    }
}

void npz_exporter::write_record_data(std::ofstream& file, const std::vector<record_item>& records) {
    for (const auto& record : records) {
        file.write(reinterpret_cast<const char*>(&record.timestamp), sizeof(record.timestamp));
        file.write(reinterpret_cast<const char*>(&record.price), sizeof(record.price));
        file.write(reinterpret_cast<const char*>(&record.position), sizeof(record.position));
        file.write(reinterpret_cast<const char*>(&record.balance), sizeof(record.balance));
        file.write(reinterpret_cast<const char*>(&record.fee), sizeof(record.fee));
        file.write(reinterpret_cast<const char*>(&record.num_trades), sizeof(record.num_trades));
        file.write(reinterpret_cast<const char*>(&record.trading_volume), sizeof(record.trading_volume));
        file.write(reinterpret_cast<const char*>(&record.trading_value), sizeof(record.trading_value));
    }
}

bool npz_exporter::export_records(const backtest_data& data, const std::string& file_path) {
    try {
        // 创建目录（如果不存在）
        std::filesystem::path path(file_path);
        std::filesystem::create_directories(path.parent_path());

        // 确保文件扩展名为.npz
        std::string npz_path = path.string();
        if (path.extension() != ".npz") {
            npz_path += ".npz";
        }

        // 获取记录数据
        const auto& records = data.get_records();

        // 为每个资产创建一个临时NPY文件
        std::vector<std::string> temp_files;

        for (const auto& [asset_no, asset_records] : records) {
            if (asset_records.empty()) {
                continue;
            }

            // 创建临时文件
            std::string temp_file = path.parent_path().string() + "/temp_" + std::to_string(asset_no) + ".npy";
            std::ofstream file(temp_file, std::ios::binary);
            if (!file.is_open()) {
                std::cerr << "Failed to open temporary file: " << temp_file << std::endl;
                continue;
            }

            // 写入NPY头部
            write_npy_header(file, asset_records.size());

            // 写入记录数据
            write_record_data(file, asset_records);

            file.close();
            temp_files.push_back(temp_file);

            std::cout << "Created temporary NPY file for asset " << asset_no << " with " << asset_records.size() << " records" << std::endl;
        }

        // 创建NPZ文件（ZIP格式）
        std::ofstream npz_file(npz_path, std::ios::binary);
        if (!npz_file.is_open()) {
            std::cerr << "Failed to open NPZ file: " << npz_path << std::endl;

            // 清理临时文件
            for (const auto& temp_file : temp_files) {
                std::filesystem::remove(temp_file);
            }

            return false;
        }

        // 这里应该使用ZIP库来创建NPZ文件
        // 由于C++标准库没有内置的ZIP支持，这里只是一个简化的实现
        // 在实际项目中，应该使用如libzip或minizip等库

        std::cerr << "Warning: NPZ export is not fully implemented. Please use CSV export instead." << std::endl;
        npz_file.close();

        // 清理临时文件
        for (const auto& temp_file : temp_files) {
            std::filesystem::remove(temp_file);
        }

        return false;
    } catch (const std::exception& e) {
        std::cerr << "Error exporting NPZ records: " << e.what() << std::endl;
        return false;
    }
}

bool npz_exporter::export_trades(const backtest_data& data, const std::string& file_path) {
    // NPZ 格式导出暂未完全实现，使用 CSV 格式代替
    std::cerr << "Warning: NPZ export for trades is not fully implemented. Please use CSV export instead." << std::endl;
    return false;
}

bool npz_exporter::export_pnl(const backtest_data& data, const std::string& file_path) {
    // NPZ 格式导出暂未完全实现，使用 CSV 格式代替
    std::cerr << "Warning: NPZ export for PNL data is not fully implemented. Please use CSV export instead." << std::endl;
    return false;
}

} // namespace cpp_backtest
} // namespace fast_trader_elite
