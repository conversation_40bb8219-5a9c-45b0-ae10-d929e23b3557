#include <fast_trader_elite/cpp_backtest/parameter_optimizer/optimization_controller.h>
#include <fast_trader_elite/cpp_backtest/parameter_optimizer/grid_search_optimizer.h>
#include <fast_trader_elite/cpp_backtest/parameter_optimizer/random_search_optimizer.h>
#include <fast_trader_elite/cpp_backtest/parameter_optimizer/tpe_optimizer.h>
#include <fast_trader_elite/cpp_backtest/parameter_optimizer/genetic_optimizer.h>
#include <fast_trader_elite/cpp_backtest/parameter_optimizer/metric_calculator.h>
#include <fast_trader_elite/cpp_backtest/backtest_logger.h>
#include <iostream>
#include <fstream>
#include <filesystem>
#include <chrono>
#include <vector>
#include <sstream>
#include <iomanip>
#include <fmt/format.h>

namespace fs = std::filesystem;

namespace fast_trader_elite {
namespace cpp_backtest {

// 获取当前时间的字符串表示
std::string get_current_time_str() {
    auto now = std::chrono::system_clock::now();
    auto now_time_t = std::chrono::system_clock::to_time_t(now);
    auto now_tm = std::localtime(&now_time_t);

    std::stringstream ss;
    ss << std::put_time(now_tm, "%Y-%m-%d %H:%M:%S");
    return ss.str();
}

optimization_controller::optimization_controller() {
}

optimization_controller::~optimization_controller() {
    // 在析构函数中清理临时文件
    cleanup_temp_files();
}

bool optimization_controller::load_config(const std::string& config_path) {
    return config_.load_from_file(config_path);
}

bool optimization_controller::run() {
    BT_LOG_INFO("Starting parameter optimization");

    // 创建优化器
    if (!create_optimizer()) {
        BT_LOG_ERROR("Failed to create optimizer");
        return false;
    }

    // 设置评估回调函数
    optimizer_->set_evaluation_callback([this](const std::unordered_map<std::string, parameter_value>& params) {
        return evaluate_parameters(params);
    });

    // 设置进度回调函数
    if (progress_callback_) {
        optimizer_->set_progress_callback(progress_callback_);
    }

    // 设置最大迭代次数
    int max_iterations = config_.get_max_iterations();
    optimizer_->set_max_iterations(max_iterations);
    BT_LOG_INFO("Maximum iterations set to {}", max_iterations);

    // 设置并行任务数
    int parallel_jobs = config_.get_parallel_jobs();
    optimizer_->set_parallel_jobs(parallel_jobs);
    BT_LOG_INFO("Parallel jobs set to {}", parallel_jobs);

    // 运行优化
    BT_LOG_INFO("Running optimization");
    results_ = optimizer_->run();
    BT_LOG_INFO("Optimization completed. Evaluated {} parameter combinations", results_.size());

    // 设置结果报告生成器的输出路径
    std::string output_path = config_.get_output_path();
    reporter_.set_output_path(output_path);
    BT_LOG_INFO("Output path set to {}", output_path);

    // 设置约束条件
    auto target = config_.get_optimization_target();
    reporter_.set_constraints(target->get_constraints());
    BT_LOG_INFO("Constraints set for result reporter");

    // 生成结果报告
    BT_LOG_INFO("Generating reports");
    if (!reporter_.generate_report(results_)) {
        BT_LOG_ERROR("Failed to generate report");
        return false;
    }

    // 生成最佳参数配置文件
    auto best_result = get_best_result();
    if (best_result.valid) {
        if (!reporter_.generate_best_config(best_result, config_.get_base_config_path())) {
            BT_LOG_ERROR("Failed to generate best config");
            return false;
        }
        BT_LOG_INFO("Best configuration saved with score: {}", best_result.score);
    } else {
        BT_LOG_WARN("No valid results found");
    }

    // 清理临时文件
    BT_LOG_INFO("Cleaning up temporary files");
    cleanup_temp_files();

    // 查找并清理可能遗留的临时文件
    output_path = config_.get_output_path();
    try {
        for (const auto& entry : fs::directory_iterator(output_path)) {
            std::string filename = entry.path().filename().string();
            if (filename.find("temp_config_") == 0 || filename.find("temp_backtest_config_") == 0) {
                BT_LOG_INFO("Removing leftover temporary file: {}", entry.path().string());
                fs::remove(entry.path());
            }
        }
    } catch (const std::exception& e) {
        BT_LOG_ERROR("Error cleaning up leftover temporary files: {}", e.what());
    }

    return true;
}

void optimization_controller::set_progress_callback(
    std::function<void(int current, int total, const optimization_result&)> callback) {
    progress_callback_ = callback;
}

optimization_result optimization_controller::get_best_result() const {
    if (results_.empty()) {
        return optimization_result{};
    }

    auto best_it = std::max_element(results_.begin(), results_.end(),
        [](const optimization_result& a, const optimization_result& b) {
            return a.score < b.score;
        });

    return *best_it;
}

const std::vector<optimization_result>& optimization_controller::get_all_results() const {
    return results_;
}

optimization_result optimization_controller::evaluate_parameters(
    const std::unordered_map<std::string, parameter_value>& params) {

    optimization_result result;
    result.parameters = params;

    try {
        // 读取基础配置
        std::string base_config_path = config_.get_base_config_path();
        BT_LOG_DEBUG("Reading base config from: {}", base_config_path);

        std::ifstream base_file(base_config_path);
        if (!base_file.is_open()) {
            BT_LOG_ERROR("Failed to open base config file: {}", base_config_path);
            result.valid = false;
            return result;
        }

        nlohmann::json base_config;
        base_file >> base_config;
        base_file.close();

        // 应用参数到基础配置
        BT_LOG_DEBUG("Applying parameters to base config");
        nlohmann::json modified_config = parameter_space::apply_parameters(base_config, params);

        // 创建临时配置文件
        std::string output_path = config_.get_output_path();
        std::string temp_config_path = output_path + "/temp_config_" +
                                      std::to_string(std::chrono::system_clock::now().time_since_epoch().count()) + ".json";

        // 确保输出目录存在
        fs::create_directories(output_path);

        BT_LOG_DEBUG("Writing modified config to: {}", temp_config_path);
        std::ofstream temp_config_file(temp_config_path);
        temp_config_file << modified_config.dump(2);
        temp_config_file.close();

        // 添加到临时文件列表
        temp_files_.push_back(temp_config_path);

        // 读取回测配置
        std::string backtest_config_path = config_.get_backtest_config_path();
        BT_LOG_DEBUG("Reading backtest config from: {}", backtest_config_path);

        std::ifstream backtest_file(backtest_config_path);
        if (!backtest_file.is_open()) {
            BT_LOG_ERROR("Failed to open backtest config file: {}", backtest_config_path);
            result.valid = false;
            return result;
        }

        nlohmann::json backtest_config;
        backtest_file >> backtest_config;
        backtest_file.close();

        // 修改回测配置中的策略配置路径
        backtest_config["strategy"]["config_path"] = temp_config_path;

        std::string temp_backtest_config_path = output_path + "/temp_backtest_config_" +
                                              std::to_string(std::chrono::system_clock::now().time_since_epoch().count()) + ".json";

        BT_LOG_DEBUG("Writing modified backtest config to: {}", temp_backtest_config_path);
        std::ofstream temp_backtest_config_file(temp_backtest_config_path);
        temp_backtest_config_file << backtest_config.dump(2);
        temp_backtest_config_file.close();

        // 添加到临时文件列表
        temp_files_.push_back(temp_backtest_config_path);

        // 创建回测引擎
        BT_LOG_DEBUG("Creating backtest engine");
        backtest_engine engine;

        // 加载配置
        BT_LOG_DEBUG("Loading backtest config");
        if (!engine.load_config(temp_backtest_config_path)) {
            BT_LOG_ERROR("Failed to load backtest config");
            result.valid = false;
            return result;
        }

        // 初始化引擎
        BT_LOG_DEBUG("Initializing backtest engine");
        if (!engine.init()) {
            BT_LOG_ERROR("Failed to initialize backtest engine");
            result.valid = false;
            return result;
        }

        // 运行回测
        BT_LOG_DEBUG("Running backtest");
        engine.run();

        // 计算指标
        BT_LOG_DEBUG("Calculating metrics");
        const auto& data = engine.get_data();
        auto& calculator = get_metric_calculator();

        // 获取所需的指标列表
        auto target = config_.get_optimization_target();
        auto required_metrics = target->get_required_metrics();

        // 获取 instrument_idx
        uint16_t instrument_idx = 1; // 默认使用第一个资产

        // 从配置中获取 instrument_idx
        if (backtest_config.contains("instruments") && backtest_config["instruments"].is_array() && !backtest_config["instruments"].empty()) {
            if (backtest_config["instruments"][0].contains("instrument_idx") && backtest_config["instruments"][0]["instrument_idx"].is_number()) {
                instrument_idx = backtest_config["instruments"][0]["instrument_idx"].get<uint16_t>();
            }
        }
        BT_LOG_DEBUG("Using instrument_idx: {}", instrument_idx);

        // 计算所有指标
        result.metrics = calculator.calculate_all(required_metrics, data, instrument_idx);

        // 计算得分
        result.score = target->calculate_score(result.metrics);
        BT_LOG_DEBUG("Calculated score: {}", result.score);

        // 检查约束条件
        if (!target->check_constraints(result.metrics)) {
            BT_LOG_DEBUG("Constraints not satisfied, adjusting score based on constraint violations");

            // 记录哪些约束条件未满足，并计算违反程度
            const auto& constraints = target->get_constraints();
            double violation_penalty = 0.0;
            int violation_count = 0;

            for (const auto& c : constraints) {
                auto it = result.metrics.find(c.metric);
                if (it != result.metrics.end()) {
                    if (!c.check(it->second)) {
                        BT_LOG_DEBUG("Constraint not satisfied: {} {} {} (actual value: {})",
                                    c.metric, c.op, c.value, it->second);

                        // 计算违反程度
                        double violation = 0.0;
                        if (c.op == "<") {
                            violation = it->second - c.value;
                        } else if (c.op == "<=") {
                            violation = it->second - c.value;
                        } else if (c.op == "==") {
                            violation = std::abs(it->second - c.value);
                        } else if (c.op == ">=") {
                            violation = c.value - it->second;
                        } else if (c.op == ">") {
                            violation = c.value - it->second;
                        }

                        if (violation > 0.0) {
                            // 根据违反程度增加惩罚
                            violation_penalty += violation;
                            violation_count++;
                        }
                    }
                }
            }

            // 根据违反程度调整得分
            if (violation_count > 0) {
                // 检查是否使用RANK归一化
                if (target->get_normalization_type() == normalization_type::RANK) {
                    // 对于RANK归一化，我们不使用固定惩罚，而是降低排名
                    // 保留原始得分，但添加一个小的惩罚因子，确保违反约束的结果排名低于满足约束的结果
                    double penalty_factor = 0.5 + (violation_penalty / (violation_count * 10.0));
                    if (penalty_factor > 1.0) penalty_factor = 1.0;
                    result.score = result.score * (1.0 - penalty_factor);

                    BT_LOG_DEBUG("Using rank-based penalty: original score = {}, penalty factor = {}, adjusted score = {}",
                                result.score / (1.0 - penalty_factor), penalty_factor, result.score);
                } else {
                    // 对于其他归一化方法，使用原始得分减去惩罚
                    double penalty_factor = 1.0 + (violation_penalty / violation_count);
                    result.score = result.score / penalty_factor - 1000.0; // 添加一个基础惩罚

                    // 确保得分不会太低，以便轮盘赌选择仍然可以工作
                    if (result.score < -10000.0) {
                        result.score = -10000.0;
                    }

                    BT_LOG_DEBUG("Using standard penalty: original score = {}, penalty factor = {}, adjusted score = {}",
                                result.score * penalty_factor + 1000.0, penalty_factor, result.score);
                }
            } else {
                // 如果无法计算违反程度
                if (target->get_normalization_type() == normalization_type::RANK) {
                    // 对于RANK归一化，将得分降低但不使用固定值
                    result.score = result.score * 0.1; // 降低90%
                    BT_LOG_DEBUG("Using rank-based default penalty: original score = {}, adjusted score = {}",
                                result.score / 0.1, result.score);
                } else {
                    // 对于其他归一化方法，使用固定惩罚
                    result.score = -1000.0;
                    BT_LOG_DEBUG("Using standard default penalty: score = {}", result.score);
                }
            }
        }

        // 保存配置路径
        result.config_path = temp_config_path;

        // 删除临时回测配置文件
        BT_LOG_DEBUG("Removing temporary backtest config file: {}", temp_backtest_config_path);
        fs::remove(temp_backtest_config_path);

        // 从临时文件列表中移除已删除的文件
        temp_files_.erase(std::remove(temp_files_.begin(), temp_files_.end(), temp_backtest_config_path), temp_files_.end());

        // 删除临时策略配置文件（除非是最佳结果）
        if (result.score < 0.0) {  // 如果得分为负，肯定不是最佳结果
            BT_LOG_DEBUG("Removing temporary strategy config file: {}", temp_config_path);
            fs::remove(temp_config_path);
            temp_files_.erase(std::remove(temp_files_.begin(), temp_files_.end(), temp_config_path), temp_files_.end());
        }

        return result;
    } catch (const std::exception& e) {
        BT_LOG_ERROR("Error evaluating parameters: {}", e.what());
        result.valid = false;
        return result;
    }
}

bool optimization_controller::create_optimizer() {
    try {
        // 获取优化算法
        std::string algorithm = config_.get_optimization_algorithm();

        // 获取参数空间
        parameter_space space = config_.get_parameter_space();

        // 获取优化目标
        auto target = config_.get_optimization_target();

        // 创建优化器
        if (algorithm == "grid_search") {
            // 网格搜索优化器
            optimizer_ = std::make_unique<grid_search_optimizer>(space, target);
            return true;
        } else if (algorithm == "random_search") {
            // 随机搜索优化器
            BT_LOG_INFO("Creating random search optimizer");
            optimizer_ = std::make_unique<random_search_optimizer>(space, target);
            return true;
        } else if (algorithm == "bayesian") {
            // 贝叶斯优化器（使用TPE实现）
            BT_LOG_INFO("Creating TPE optimizer (Bayesian optimization)");
            optimizer_ = std::make_unique<tpe_optimizer>(space, target);
            return true;
        } else if (algorithm == "genetic") {
            // 遗传算法优化器
            BT_LOG_INFO("Creating genetic optimizer");
            auto genetic_opt = std::make_unique<genetic_optimizer>(space, target);

            // 从配置中获取遗传算法参数
            auto genetic_config = config_.get_genetic_config();
            if (genetic_config) {
                if (genetic_config->population_size > 0) {
                    genetic_opt->set_population_size(genetic_config->population_size);
                }
                if (genetic_config->elite_count >= 0) {
                    genetic_opt->set_elite_count(genetic_config->elite_count);
                }
                if (genetic_config->crossover_rate >= 0.0 && genetic_config->crossover_rate <= 1.0) {
                    genetic_opt->set_crossover_rate(genetic_config->crossover_rate);
                }
                if (genetic_config->mutation_rate >= 0.0 && genetic_config->mutation_rate <= 1.0) {
                    genetic_opt->set_mutation_rate(genetic_config->mutation_rate);
                }
            }

            // 设置最大无进展迭代次数
            genetic_opt->set_max_stagnation_iterations(30); // 如果30代没有进展，提前结束

            // 设置是否使用自适应变异率和交叉率
            genetic_opt->set_use_adaptive_rates(true); // 使用自适应变异率和交叉率

            optimizer_ = std::move(genetic_opt);
            return true;
        } else {
            std::cerr << "Unknown optimization algorithm: " << algorithm << std::endl;
            return false;
        }
    } catch (const std::exception& e) {
        std::cerr << "Error creating optimizer: " << e.what() << std::endl;
        return false;
    }
}

void optimization_controller::cleanup_temp_files() {
    if (temp_files_.empty()) {
        BT_LOG_INFO("No temporary files to clean up");
        return;
    }

    BT_LOG_INFO("Cleaning up {} temporary files", temp_files_.size());
    int removed_count = 0;

    for (const auto& file : temp_files_) {
        try {
            if (fs::exists(file)) {
                BT_LOG_DEBUG("Removing temporary file: {}", file);
                fs::remove(file);
                removed_count++;
            }
        } catch (const std::exception& e) {
            BT_LOG_ERROR("Failed to remove temporary file {}: {}", file, e.what());
        }
    }

    BT_LOG_INFO("Removed {} temporary files", removed_count);
    temp_files_.clear();
}

} // namespace cpp_backtest
} // namespace fast_trader_elite
