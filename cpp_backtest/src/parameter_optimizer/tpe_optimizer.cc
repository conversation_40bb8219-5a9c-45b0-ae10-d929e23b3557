#include <fast_trader_elite/cpp_backtest/parameter_optimizer/tpe_optimizer.h>
#include <fast_trader_elite/cpp_backtest/backtest_logger.h>
#include <iostream>
#include <algorithm>
#include <cmath>
#include <limits>
#include <numeric>

namespace fast_trader_elite {
namespace cpp_backtest {

tpe_optimizer::tpe_optimizer(
    const parameter_space& space, 
    const std::shared_ptr<optimization_target>& target)
    : optimizer(space, target) {
}

tpe_optimizer::~tpe_optimizer() {
    // 设置停止标志
    stop_ = true;
    
    // 通知所有等待的线程
    cv_.notify_all();
    
    // 等待所有线程结束
    for (auto& worker : workers_) {
        if (worker.joinable()) {
            worker.join();
        }
    }
}

std::vector<optimization_result> tpe_optimizer::run() {
    BT_LOG_INFO("Starting TPE optimization");
    
    // 清空结果
    results_.clear();
    
    // 设置总任务数
    total_tasks_ = max_iterations_;
    
    BT_LOG_INFO("Will evaluate {} parameter combinations", total_tasks_);
    
    // 重置计数器
    completed_tasks_ = 0;
    stop_ = false;
    
    // 创建工作线程
    int num_threads = std::min(parallel_jobs_, max_iterations_);
    if (num_threads <= 0) {
        num_threads = 1;
    }
    
    BT_LOG_INFO("Starting {} worker threads", num_threads);
    
    // 创建工作线程
    for (int i = 0; i < num_threads; ++i) {
        workers_.emplace_back(&tpe_optimizer::worker_thread, this);
    }
    
    // 生成初始样本
    int n_initial = std::min(n_initial_points_, max_iterations_);
    auto initial_points = generate_initial_points(n_initial);
    
    BT_LOG_INFO("Generated {} initial points", initial_points.size());
    
    // 将初始样本加入队列
    {
        std::unique_lock<std::mutex> lock(mutex_);
        
        for (const auto& point : initial_points) {
            param_queue_.push(point);
        }
        
        // 通知所有等待的线程
        cv_.notify_all();
    }
    
    // 等待初始样本评估完成
    while (true) {
        {
            std::unique_lock<std::mutex> lock(mutex_);
            if (param_queue_.empty() || stop_ || completed_tasks_ >= n_initial) {
                break;
            }
        }
        
        // 避免频繁加锁，减少竞争
        std::this_thread::sleep_for(std::chrono::milliseconds(100));
    }
    
    BT_LOG_INFO("Initial points evaluated");
    
    // 如果已经达到最大迭代次数，直接返回
    if (completed_tasks_ >= max_iterations_ || stop_) {
        goto cleanup;
    }
    
    // TPE优化主循环
    while (completed_tasks_ < max_iterations_ && !stop_) {
        // 找到下一个评估点
        auto next_point = find_next_point();
        
        // 将下一个评估点加入队列
        {
            std::unique_lock<std::mutex> lock(mutex_);
            param_queue_.push(next_point);
            cv_.notify_all();
        }
        
        // 等待评估完成
        while (true) {
            {
                std::unique_lock<std::mutex> lock(mutex_);
                if (param_queue_.empty() || stop_) {
                    break;
                }
            }
            
            // 避免频繁加锁，减少竞争
            std::this_thread::sleep_for(std::chrono::milliseconds(100));
        }
        
        // 找到当前最佳得分
        double best_score = std::numeric_limits<double>::lowest();
        for (const auto& result : results_) {
            if (result.score > best_score) {
                best_score = result.score;
            }
        }
        
        BT_LOG_INFO("Completed iteration {}/{}, best score: {}", 
                   completed_tasks_, max_iterations_, best_score);
    }
    
cleanup:
    // 设置停止标志
    stop_ = true;
    
    // 通知所有等待的线程
    cv_.notify_all();
    
    // 等待所有线程结束
    BT_LOG_INFO("Waiting for worker threads to finish");
    for (auto& worker : workers_) {
        if (worker.joinable()) {
            worker.join();
        }
    }
    
    workers_.clear();
    BT_LOG_INFO("All worker threads finished");
    
    // 按得分排序
    BT_LOG_INFO("Sorting results by score");
    std::sort(results_.begin(), results_.end(), [](const optimization_result& a, const optimization_result& b) {
        return a.score > b.score;
    });
    
    BT_LOG_INFO("Optimization completed. Evaluated {} parameter combinations", results_.size());
    
    return results_;
}

void tpe_optimizer::worker_thread() {
    BT_LOG_DEBUG("Worker thread started");
    
    while (!stop_) {
        // 获取参数组合
        std::unordered_map<std::string, parameter_value> params;
        bool has_task = false;
        
        {
            std::unique_lock<std::mutex> lock(mutex_);
            
            // 检查队列是否为空
            if (param_queue_.empty()) {
                // 如果队列为空，等待条件变量通知或者停止信号
                if (!stop_) {
                    BT_LOG_DEBUG("Worker thread waiting for tasks");
                    cv_.wait_for(lock, std::chrono::seconds(1), [this] {
                        return !param_queue_.empty() || stop_;
                    });
                    
                    // 再次检查队列是否为空
                    if (param_queue_.empty()) {
                        continue;
                    }
                } else {
                    BT_LOG_DEBUG("Worker thread stopping");
                    break;
                }
            }
            
            // 获取参数组合
            params = param_queue_.front();
            param_queue_.pop();
            has_task = true;
            BT_LOG_DEBUG("Worker thread got a task, remaining tasks in queue: {}", param_queue_.size());
        }
        
        if (has_task) {
            // 评估参数组合
            BT_LOG_DEBUG("Evaluating parameter combination");
            auto result = evaluate(params);
            
            // 添加到结果
            if (result.valid) {
                std::unique_lock<std::mutex> lock(mutex_);
                results_.push_back(result);
                BT_LOG_DEBUG("Added valid result with score: {}", result.score);
            } else {
                BT_LOG_DEBUG("Evaluation result is invalid");
            }
            
            // 更新进度
            int completed = ++completed_tasks_;
            BT_LOG_DEBUG("Completed tasks: {}/{}", completed, total_tasks_);
            
            // 调用进度回调
            if (progress_callback_) {
                progress_callback_(completed, total_tasks_, result);
            }
        }
    }
    
    BT_LOG_DEBUG("Worker thread exited");
}

std::vector<std::unordered_map<std::string, parameter_value>> tpe_optimizer::generate_initial_points(
    int n_initial_points) const {
    
    std::vector<std::unordered_map<std::string, parameter_value>> points;
    
    // 使用拉丁超立方采样生成初始点
    const auto& param_defs = space_.get_parameters();
    int n_dims = param_defs.size();
    
    // 创建每个维度的划分
    std::vector<std::vector<double>> grid(n_dims);
    for (int i = 0; i < n_dims; ++i) {
        grid[i].resize(n_initial_points);
        for (int j = 0; j < n_initial_points; ++j) {
            grid[i][j] = (j + 0.5) / n_initial_points;
        }
    }
    
    // 对每个维度的划分进行随机排列
    for (int i = 0; i < n_dims; ++i) {
        std::shuffle(grid[i].begin(), grid[i].end(), gen_);
    }
    
    // 生成样本点
    for (int i = 0; i < n_initial_points; ++i) {
        std::unordered_map<std::string, parameter_value> params;
        
        for (int j = 0; j < n_dims; ++j) {
            const auto& param_def = param_defs[j];
            double normalized_val = grid[j][i];  // 归一化值 [0, 1]
            
            // 将归一化值转换为参数值
            if (param_def.type == parameter_type::INTEGER) {
                int min_val = std::get<int>(param_def.min_value);
                int max_val = std::get<int>(param_def.max_value);
                int step = std::get<int>(param_def.step);
                
                if (step > 1) {
                    // 如果有步长，生成符合步长的值
                    int num_steps = (max_val - min_val) / step + 1;
                    int step_idx = static_cast<int>(std::round(normalized_val * (num_steps - 1)));
                    params[param_def.name] = min_val + step_idx * step;
                } else {
                    // 否则，直接生成整数值
                    params[param_def.name] = min_val + static_cast<int>(std::round(normalized_val * (max_val - min_val)));
                }
            } else if (param_def.type == parameter_type::FLOAT) {
                double min_val = std::get<double>(param_def.min_value);
                double max_val = std::get<double>(param_def.max_value);
                double step = std::get<double>(param_def.step);
                
                if (step > 0.0) {
                    // 如果有步长，生成符合步长的值
                    int num_steps = static_cast<int>((max_val - min_val) / step + 0.5) + 1;
                    int step_idx = static_cast<int>(std::round(normalized_val * (num_steps - 1)));
                    params[param_def.name] = min_val + step_idx * step;
                } else {
                    // 否则，直接生成浮点值
                    params[param_def.name] = min_val + normalized_val * (max_val - min_val);
                }
            } else if (param_def.type == parameter_type::BOOLEAN) {
                params[param_def.name] = normalized_val >= 0.5;
            } else if (param_def.type == parameter_type::STRING || param_def.type == parameter_type::ENUM) {
                // 对于字符串和枚举类型，使用预定义值
                if (!param_def.values.empty()) {
                    size_t idx = static_cast<size_t>(std::round(normalized_val * (param_def.values.size() - 1)));
                    idx = std::min(idx, param_def.values.size() - 1);
                    params[param_def.name] = param_def.values[idx];
                } else {
                    params[param_def.name] = param_def.default_value;
                }
            }
        }
        
        points.push_back(params);
    }
    
    return points;
}

std::unordered_map<std::string, parameter_value> tpe_optimizer::find_next_point(double gamma) {
    // 如果没有足够的样本，使用随机采样
    if (results_.size() < 5) {
        return generate_initial_points(1)[0];
    }
    
    // 按得分排序
    std::vector<optimization_result> sorted_results = results_;
    std::sort(sorted_results.begin(), sorted_results.end(), [](const optimization_result& a, const optimization_result& b) {
        return a.score > b.score;
    });
    
    // 分割为好样本和差样本
    size_t n_good = std::max(static_cast<size_t>(gamma * sorted_results.size()), size_t(1));
    
    std::vector<std::unordered_map<std::string, parameter_value>> good_samples;
    std::vector<std::unordered_map<std::string, parameter_value>> bad_samples;
    
    for (size_t i = 0; i < sorted_results.size(); ++i) {
        if (i < n_good) {
            good_samples.push_back(sorted_results[i].parameters);
        } else {
            bad_samples.push_back(sorted_results[i].parameters);
        }
    }
    
    // 生成候选点
    auto candidates = generate_candidates(n_candidates_);
    
    // 计算每个候选点的得分（好样本KDE / 差样本KDE）
    double best_score = -std::numeric_limits<double>::infinity();
    std::unordered_map<std::string, parameter_value> best_candidate;
    
    for (const auto& candidate : candidates) {
        double l_kde = kde(candidate, good_samples, bandwidth_);
        double g_kde = kde(candidate, bad_samples, bandwidth_);
        
        // 避免除以零
        g_kde = std::max(g_kde, 1e-10);
        
        double score = l_kde / g_kde;
        
        if (score > best_score) {
            best_score = score;
            best_candidate = candidate;
        }
    }
    
    return best_candidate;
}

double tpe_optimizer::kde(
    const std::unordered_map<std::string, parameter_value>& x,
    const std::vector<std::unordered_map<std::string, parameter_value>>& samples,
    double bandwidth) const {
    
    if (samples.empty()) {
        return 0.0;
    }
    
    // 计算核密度估计
    double kde_sum = 0.0;
    
    for (const auto& sample : samples) {
        double dist = distance(x, sample);
        double kernel = std::exp(-0.5 * dist * dist / (bandwidth * bandwidth));
        kde_sum += kernel;
    }
    
    return kde_sum / samples.size();
}

double tpe_optimizer::distance(
    const std::unordered_map<std::string, parameter_value>& p1,
    const std::unordered_map<std::string, parameter_value>& p2) const {
    
    const auto& param_defs = space_.get_parameters();
    double dist_sum = 0.0;
    
    for (const auto& param_def : param_defs) {
        const auto& name = param_def.name;
        
        // 检查参数是否存在
        auto it1 = p1.find(name);
        auto it2 = p2.find(name);
        
        if (it1 == p1.end() || it2 == p2.end()) {
            continue;
        }
        
        // 计算归一化距离
        if (param_def.type == parameter_type::INTEGER) {
            int val1 = std::get<int>(it1->second);
            int val2 = std::get<int>(it2->second);
            int min_val = std::get<int>(param_def.min_value);
            int max_val = std::get<int>(param_def.max_value);
            
            double range = static_cast<double>(max_val - min_val);
            if (range > 0) {
                double norm_dist = std::abs(val1 - val2) / range;
                dist_sum += norm_dist * norm_dist;
            }
        } else if (param_def.type == parameter_type::FLOAT) {
            double val1 = std::get<double>(it1->second);
            double val2 = std::get<double>(it2->second);
            double min_val = std::get<double>(param_def.min_value);
            double max_val = std::get<double>(param_def.max_value);
            
            double range = max_val - min_val;
            if (range > 0) {
                double norm_dist = std::abs(val1 - val2) / range;
                dist_sum += norm_dist * norm_dist;
            }
        } else if (param_def.type == parameter_type::BOOLEAN) {
            bool val1 = std::get<bool>(it1->second);
            bool val2 = std::get<bool>(it2->second);
            
            if (val1 != val2) {
                dist_sum += 1.0;
            }
        } else if (param_def.type == parameter_type::STRING || param_def.type == parameter_type::ENUM) {
            std::string val1 = std::get<std::string>(it1->second);
            std::string val2 = std::get<std::string>(it2->second);
            
            if (val1 != val2) {
                dist_sum += 1.0;
            }
        }
    }
    
    return std::sqrt(dist_sum);
}

std::vector<std::unordered_map<std::string, parameter_value>> tpe_optimizer::generate_candidates(
    int n_candidates) const {
    
    // 简单地使用随机采样生成候选点
    return generate_initial_points(n_candidates);
}

} // namespace cpp_backtest
} // namespace fast_trader_elite
