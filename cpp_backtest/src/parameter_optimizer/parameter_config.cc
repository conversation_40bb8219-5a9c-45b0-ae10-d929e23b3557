#include <fast_trader_elite/cpp_backtest/parameter_optimizer/parameter_config.h>
#include <fstream>
#include <iostream>

namespace fast_trader_elite {
namespace cpp_backtest {

bool parameter_config::load_from_file(const std::string& file_path) {
    file_path_ = file_path;

    try {
        // 打开配置文件
        std::ifstream file(file_path);
        if (!file.is_open()) {
            std::cerr << "Failed to open config file: " << file_path << std::endl;
            return false;
        }

        // 解析JSON
        file >> config_;
        file.close();

        // 解析配置
        return parse_config();
    } catch (const std::exception& e) {
        std::cerr << "Error loading config: " << e.what() << std::endl;
        return false;
    }
}

std::string parameter_config::get_base_config_path() const {
    return base_config_path_;
}

std::string parameter_config::get_backtest_config_path() const {
    return backtest_config_path_;
}

std::string parameter_config::get_output_path() const {
    return output_path_;
}

std::string parameter_config::get_optimization_algorithm() const {
    return optimization_algorithm_;
}

int parameter_config::get_max_iterations() const {
    return max_iterations_;
}

int parameter_config::get_parallel_jobs() const {
    return parallel_jobs_;
}

parameter_space parameter_config::get_parameter_space() const {
    return parameter_space(config_);
}

std::shared_ptr<optimization_target> parameter_config::get_optimization_target() const {
    if (!config_.contains("optimization_target")) {
        throw std::runtime_error("Missing optimization_target in config");
    }

    auto target = create_target_from_config(config_["optimization_target"]);

    // 添加约束条件
    if (config_.contains("constraints") && config_["constraints"].is_array()) {
        for (const auto& constraint_json : config_["constraints"]) {
            constraint c;

            if (constraint_json.contains("metric") && constraint_json["metric"].is_string()) {
                c.metric = constraint_json["metric"].get<std::string>();
            } else {
                std::cerr << "Constraint missing or invalid metric" << std::endl;
                continue;
            }

            if (constraint_json.contains("operator") && constraint_json["operator"].is_string()) {
                c.op = constraint_json["operator"].get<std::string>();
            } else {
                std::cerr << "Constraint missing or invalid operator" << std::endl;
                continue;
            }

            if (constraint_json.contains("value") && constraint_json["value"].is_number()) {
                c.value = constraint_json["value"].get<double>();
            } else {
                std::cerr << "Constraint missing or invalid value" << std::endl;
                continue;
            }

            if (constraint_json.contains("description") && constraint_json["description"].is_string()) {
                c.description = constraint_json["description"].get<std::string>();
            }

            target->add_constraint(c);
            std::cout << "Added constraint: " << c.metric << " " << c.op << " " << c.value;
            if (!c.description.empty()) {
                std::cout << " (" << c.description << ")";
            }
            std::cout << std::endl;
        }
    }

    return target;
}

const nlohmann::json& parameter_config::get_raw_config() const {
    return config_;
}

std::optional<genetic_config> parameter_config::get_genetic_config() const {
    // 如果配置中没有遗传算法配置，返回空
    if (!config_.contains("genetic_config")) {
        return std::nullopt;
    }

    try {
        const auto& gc = config_["genetic_config"];
        genetic_config config;

        if (gc.contains("population_size") && gc["population_size"].is_number()) {
            config.population_size = gc["population_size"].get<int>();
        }

        if (gc.contains("elite_count") && gc["elite_count"].is_number()) {
            config.elite_count = gc["elite_count"].get<int>();
        }

        if (gc.contains("crossover_rate") && gc["crossover_rate"].is_number()) {
            config.crossover_rate = gc["crossover_rate"].get<double>();
        }

        if (gc.contains("mutation_rate") && gc["mutation_rate"].is_number()) {
            config.mutation_rate = gc["mutation_rate"].get<double>();
        }

        return config;
    } catch (const std::exception& e) {
        std::cerr << "Error parsing genetic_config: " << e.what() << std::endl;
        return std::nullopt;
    }
}

bool parameter_config::parse_config() {
    try {
        // 解析基本配置
        if (!config_.contains("base_config_path") || !config_["base_config_path"].is_string()) {
            std::cerr << "Missing or invalid base_config_path" << std::endl;
            return false;
        }
        base_config_path_ = config_["base_config_path"].get<std::string>();

        if (!config_.contains("backtest_config_path") || !config_["backtest_config_path"].is_string()) {
            std::cerr << "Missing or invalid backtest_config_path" << std::endl;
            return false;
        }
        backtest_config_path_ = config_["backtest_config_path"].get<std::string>();

        if (!config_.contains("output_path") || !config_["output_path"].is_string()) {
            std::cerr << "Missing or invalid output_path" << std::endl;
            return false;
        }
        output_path_ = config_["output_path"].get<std::string>();

        if (!config_.contains("optimization_algorithm") || !config_["optimization_algorithm"].is_string()) {
            std::cerr << "Missing or invalid optimization_algorithm" << std::endl;
            return false;
        }
        optimization_algorithm_ = config_["optimization_algorithm"].get<std::string>();

        if (config_.contains("max_iterations") && config_["max_iterations"].is_number()) {
            max_iterations_ = config_["max_iterations"].get<int>();
        }

        if (config_.contains("parallel_jobs") && config_["parallel_jobs"].is_number()) {
            parallel_jobs_ = config_["parallel_jobs"].get<int>();
        }

        return true;
    } catch (const std::exception& e) {
        std::cerr << "Error parsing config: " << e.what() << std::endl;
        return false;
    }
}

} // namespace cpp_backtest
} // namespace fast_trader_elite
