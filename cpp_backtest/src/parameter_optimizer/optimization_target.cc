#include <fast_trader_elite/cpp_backtest/parameter_optimizer/optimization_target.h>
#include <cmath>
#include <algorithm>
#include <numeric>
#include <iostream>
#include <unordered_set>

namespace fast_trader_elite {
namespace cpp_backtest {

bool constraint::check(double metric_value) const {
    if (op == "<") {
        return metric_value < value;
    } else if (op == "<=") {
        return metric_value <= value;
    } else if (op == "==") {
        return std::abs(metric_value - value) < 1e-6;
    } else if (op == ">=") {
        return metric_value >= value;
    } else if (op == ">") {
        return metric_value > value;
    } else {
        std::cerr << "Unknown operator: " << op << std::endl;
        return false;
    }
}

bool optimization_target::check_constraints(const std::unordered_map<std::string, double>& metrics) const {
    // 如果没有约束条件，则认为满足约束
    if (constraints_.empty()) {
        return true;
    }

    for (const auto& c : constraints_) {
        auto it = metrics.find(c.metric);
        if (it == metrics.end()) {
            std::cerr << "Metric not found for constraint: " << c.metric << std::endl;
            return false;
        }

        if (!c.check(it->second)) {
            std::cerr << "Constraint not satisfied: " << c.metric << " " << c.op << " " << c.value
                      << " (actual value: " << it->second << ")" << std::endl;
            return false;
        }
    }

    return true;
}

void optimization_target::add_constraint(const constraint& c) {
    constraints_.push_back(c);
}

const std::vector<constraint>& optimization_target::get_constraints() const {
    return constraints_;
}

void optimization_target::set_normalization(normalization_type type) {
    normalization_ = type;
}

normalization_type optimization_target::get_normalization_type() const {
    return normalization_;
}

void optimization_target::update_normalization_params(
    const std::vector<std::unordered_map<std::string, double>>& all_metrics) {

    if (normalization_ == normalization_type::NONE) {
        return;
    }

    // 获取所有指标名称
    std::unordered_set<std::string> metric_names;
    for (const auto& metrics : all_metrics) {
        for (const auto& [name, _] : metrics) {
            metric_names.insert(name);
        }
    }

    // 计算每个指标的归一化参数
    for (const auto& name : metric_names) {
        // 收集所有值
        std::vector<double> values;
        for (const auto& metrics : all_metrics) {
            auto it = metrics.find(name);
            if (it != metrics.end()) {
                values.push_back(it->second);
            }
        }

        if (values.empty()) {
            continue;
        }

        if (normalization_ == normalization_type::MIN_MAX) {
            // Min-Max 归一化
            double min_val = *std::min_element(values.begin(), values.end());
            double max_val = *std::max_element(values.begin(), values.end());

            if (std::abs(max_val - min_val) < 1e-6) {
                // 避免除以零
                normalization_params_[name] = {min_val, 1.0};
            } else {
                normalization_params_[name] = {min_val, max_val - min_val};
            }
        } else if (normalization_ == normalization_type::Z_SCORE) {
            // Z-Score 归一化
            double mean = std::accumulate(values.begin(), values.end(), 0.0) / values.size();
            double sum_squared_diff = 0.0;

            for (double val : values) {
                double diff = val - mean;
                sum_squared_diff += diff * diff;
            }

            double std_dev = std::sqrt(sum_squared_diff / values.size());

            if (std_dev < 1e-6) {
                // 避免除以零
                normalization_params_[name] = {mean, 1.0};
            } else {
                normalization_params_[name] = {mean, std_dev};
            }
        } else if (normalization_ == normalization_type::PERCENTILE) {
            // 百分位数归一化
            // 对数据进行排序
            std::sort(values.begin(), values.end());

            // 计算25%和75%百分位数
            size_t n = values.size();
            size_t q1_idx = n / 4;
            size_t q3_idx = 3 * n / 4;

            double q1 = n > 0 ? values[q1_idx] : 0.0;
            double q3 = n > 0 ? values[q3_idx] : 0.0;

            // 计算四分位距（IQR）
            double iqr = q3 - q1;

            if (std::abs(iqr) < 1e-6) {
                // 如果IQR接近零，使用标准差作为替代
                double mean = std::accumulate(values.begin(), values.end(), 0.0) / values.size();
                double sum_squared_diff = 0.0;

                for (double val : values) {
                    double diff = val - mean;
                    sum_squared_diff += diff * diff;
                }

                double std_dev = std::sqrt(sum_squared_diff / values.size());

                if (std_dev < 1e-6) {
                    // 避免除以零
                    normalization_params_[name] = {mean, 1.0};
                } else {
                    normalization_params_[name] = {mean, std_dev};
                }
            } else {
                // 使用中位数和IQR进行归一化
                double median = n % 2 == 0 ? (values[n/2-1] + values[n/2]) / 2.0 : values[n/2];
                normalization_params_[name] = {median, iqr};
            }
        } else if (normalization_ == normalization_type::RANK) {
            // 排名归一化
            if (values.empty()) {
                continue;
            }

            // 创建值到排名的映射
            std::unordered_map<double, double> value_to_rank;

            // 创建值和索引的对
            std::vector<std::pair<double, size_t>> value_indices;
            for (size_t i = 0; i < values.size(); ++i) {
                value_indices.push_back({values[i], i});
            }

            // 按值排序（升序）
            std::sort(value_indices.begin(), value_indices.end(),
                     [](const std::pair<double, size_t>& a, const std::pair<double, size_t>& b) {
                         return a.first < b.first;
                     });

            // 计算排名（从0到1）
            for (size_t i = 0; i < value_indices.size(); ++i) {
                double rank = static_cast<double>(i) / (value_indices.size() - 1);
                value_to_rank[value_indices[i].first] = rank;
            }

            // 存储排名数据
            rank_data_[name] = value_to_rank;

            // 设置归一化参数（不会直接使用，但为了兼容性）
            normalization_params_[name] = {0.0, 1.0};

            std::cout << "Created rank mapping for metric '" << name << "' with "
                      << value_to_rank.size() << " unique values" << std::endl;
        }
    }
}

double optimization_target::apply_transform(double value, transform_type transform, double param) const {
    switch (transform) {
        case transform_type::IDENTITY:
            return value;

        case transform_type::INVERSE:
            if (std::abs(value) < 1e-6) {
                // 避免除以零
                return 0.0;
            }
            return 1.0 / value;

        case transform_type::NEGATIVE:
            return -value;

        case transform_type::LOG:
            if (value <= 0.0) {
                // 避免对负数或零取对数
                return 0.0;
            }
            return std::log(value);

        case transform_type::EXP:
            return std::exp(value);

        case transform_type::THRESHOLD:
            return value > param ? value : 0.0;

        default:
            std::cerr << "Unknown transform type" << std::endl;
            return value;
    }
}

double optimization_target::normalize(const std::string& metric, double value) const {
    if (normalization_ == normalization_type::NONE) {
        return value;
    }

    auto it = normalization_params_.find(metric);
    if (it == normalization_params_.end()) {
        return value;
    }

    double param1 = it->second.first;
    double param2 = it->second.second;

    if (normalization_ == normalization_type::MIN_MAX) {
        // Min-Max 归一化
        if (std::abs(param2) < 1e-6) {
            return 0.0;
        }
        return (value - param1) / param2;
    } else if (normalization_ == normalization_type::Z_SCORE) {
        // Z-Score 归一化
        if (std::abs(param2) < 1e-6) {
            return 0.0;
        }
        return (value - param1) / param2;
    } else if (normalization_ == normalization_type::PERCENTILE) {
        // 百分位数归一化
        // param1是中位数，param2是IQR（或标准差）
        if (std::abs(param2) < 1e-6) {
            return 0.0;
        }
        // 使用中位数和IQR进行归一化，类似于Z-score但更稳健
        return (value - param1) / param2;
    } else if (normalization_ == normalization_type::RANK) {
        // 排名归一化
        auto it = rank_data_.find(metric);
        if (it == rank_data_.end()) {
            return value; // 如果没有排名数据，返回原始值
        }

        // 查找值的排名
        const auto& value_to_rank = it->second;
        auto rank_it = value_to_rank.find(value);

        if (rank_it != value_to_rank.end()) {
            // 找到精确匹配
            return rank_it->second;
        } else {
            // 没有精确匹配，找最接近的值
            double closest_distance = std::numeric_limits<double>::max();
            double closest_rank = 0.5; // 默认中间值

            for (const auto& [val, rank] : value_to_rank) {
                double distance = std::abs(val - value);
                if (distance < closest_distance) {
                    closest_distance = distance;
                    closest_rank = rank;
                }
            }

            // 使用最接近值的排名
            if (closest_distance < std::numeric_limits<double>::max()) {
                return closest_rank;
            }
        }

        // 如果无法找到合适的排名，返回0.5（中间值）
        return 0.5;
    }

    return value;
}

single_metric_target::single_metric_target(const std::string& metric_name)
    : metric_name_(metric_name) {
}

double single_metric_target::calculate_score(const std::unordered_map<std::string, double>& metrics) const {
    auto it = metrics.find(metric_name_);
    if (it == metrics.end()) {
        std::cerr << "Metric not found: " << metric_name_ << std::endl;
        return 0.0;
    }

    return normalize(metric_name_, it->second);
}

std::vector<std::string> single_metric_target::get_required_metrics() const {
    std::vector<std::string> metrics = {metric_name_};

    // 添加约束条件中的指标
    for (const auto& constraint : constraints_) {
        // 检查是否已经包含该指标
        if (std::find(metrics.begin(), metrics.end(), constraint.metric) == metrics.end()) {
            metrics.push_back(constraint.metric);
        }
    }

    return metrics;
}

composite_target::composite_target(const std::vector<metric_component>& components)
    : components_(components) {
}

double composite_target::calculate_score(const std::unordered_map<std::string, double>& metrics) const {
    double score = 0.0;
    double total_weight = 0.0;

    for (const auto& component : components_) {
        auto it = metrics.find(component.metric);
        if (it == metrics.end()) {
            std::cerr << "Metric not found: " << component.metric << std::endl;
            continue;
        }

        double value = it->second;
        double transformed = apply_transform(value, component.transform, component.transform_param);
        double normalized = normalize(component.metric, transformed);

        score += component.weight * normalized;
        total_weight += component.weight;
    }

    if (total_weight > 0.0) {
        score /= total_weight;
    }

    return score;
}

std::vector<std::string> composite_target::get_required_metrics() const {
    std::vector<std::string> metrics;
    metrics.reserve(components_.size() + constraints_.size());

    // 添加组件中的指标
    for (const auto& component : components_) {
        metrics.push_back(component.metric);
    }

    // 添加约束条件中的指标
    for (const auto& constraint : constraints_) {
        // 检查是否已经包含该指标
        if (std::find(metrics.begin(), metrics.end(), constraint.metric) == metrics.end()) {
            metrics.push_back(constraint.metric);
        }
    }

    return metrics;
}

std::shared_ptr<optimization_target> create_target_from_config(const nlohmann::json& config) {
    if (!config.contains("type") || !config["type"].is_string()) {
        throw std::invalid_argument("Optimization target must have a type");
    }

    std::string type = config["type"].get<std::string>();

    if (type == "single") {
        // 单一指标优化目标
        if (!config.contains("metric") || !config["metric"].is_string()) {
            throw std::invalid_argument("Single metric target must have a metric");
        }

        std::string metric = config["metric"].get<std::string>();
        auto target = std::make_shared<single_metric_target>(metric);

        // 设置归一化类型
        if (config.contains("normalize")) {
            if (config["normalize"].is_boolean() && config["normalize"].get<bool>()) {
                target->set_normalization(normalization_type::MIN_MAX);
            } else if (config["normalize"].is_string()) {
                std::string norm_type = config["normalize"].get<std::string>();

                if (norm_type == "min_max") {
                    target->set_normalization(normalization_type::MIN_MAX);
                } else if (norm_type == "z_score") {
                    target->set_normalization(normalization_type::Z_SCORE);
                } else if (norm_type == "percentile") {
                    target->set_normalization(normalization_type::PERCENTILE);
                } else if (norm_type == "rank") {
                    target->set_normalization(normalization_type::RANK);
                }
            }
        }

        return target;
    } else if (type == "composite") {
        // 组合指标优化目标
        if (!config.contains("components") || !config["components"].is_array()) {
            throw std::invalid_argument("Composite target must have components");
        }

        std::vector<metric_component> components;

        for (const auto& comp_config : config["components"]) {
            metric_component component;

            if (!comp_config.contains("metric") || !comp_config["metric"].is_string()) {
                throw std::invalid_argument("Component must have a metric");
            }
            component.metric = comp_config["metric"].get<std::string>();

            if (comp_config.contains("weight") && comp_config["weight"].is_number()) {
                component.weight = comp_config["weight"].get<double>();
            }

            if (comp_config.contains("transform") && comp_config["transform"].is_string()) {
                std::string transform = comp_config["transform"].get<std::string>();

                if (transform == "identity") {
                    component.transform = transform_type::IDENTITY;
                } else if (transform == "inverse") {
                    component.transform = transform_type::INVERSE;
                } else if (transform == "negative") {
                    component.transform = transform_type::NEGATIVE;
                } else if (transform == "log") {
                    component.transform = transform_type::LOG;
                } else if (transform == "exp") {
                    component.transform = transform_type::EXP;
                } else if (transform == "threshold") {
                    component.transform = transform_type::THRESHOLD;

                    if (comp_config.contains("transform_param") && comp_config["transform_param"].is_number()) {
                        component.transform_param = comp_config["transform_param"].get<double>();
                    }
                }
            }

            if (comp_config.contains("description") && comp_config["description"].is_string()) {
                component.description = comp_config["description"].get<std::string>();
            }

            components.push_back(component);
        }

        auto target = std::make_shared<composite_target>(components);

        // 设置归一化类型
        if (config.contains("normalize")) {
            if (config["normalize"].is_boolean() && config["normalize"].get<bool>()) {
                target->set_normalization(normalization_type::MIN_MAX);
            } else if (config["normalize"].is_string()) {
                std::string norm_type = config["normalize"].get<std::string>();

                if (norm_type == "min_max") {
                    target->set_normalization(normalization_type::MIN_MAX);
                } else if (norm_type == "z_score") {
                    target->set_normalization(normalization_type::Z_SCORE);
                } else if (norm_type == "percentile") {
                    target->set_normalization(normalization_type::PERCENTILE);
                } else if (norm_type == "rank") {
                    target->set_normalization(normalization_type::RANK);
                }
            }
        }

        return target;
    } else {
        throw std::invalid_argument("Unknown optimization target type: " + type);
    }
}

} // namespace cpp_backtest
} // namespace fast_trader_elite
