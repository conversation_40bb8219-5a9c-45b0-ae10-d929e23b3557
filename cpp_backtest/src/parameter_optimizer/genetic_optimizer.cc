#include <fast_trader_elite/cpp_backtest/parameter_optimizer/genetic_optimizer.h>
#include <fast_trader_elite/cpp_backtest/backtest_logger.h>
#include <iostream>
#include <algorithm>
#include <cmath>
#include <limits>
#include <numeric>
#include <unordered_set>

namespace fast_trader_elite {
namespace cpp_backtest {

genetic_optimizer::genetic_optimizer(
    const parameter_space& space,
    const std::shared_ptr<optimization_target>& target)
    : optimizer(space, target) {
}

genetic_optimizer::~genetic_optimizer() {
    // 设置停止标志
    stop_ = true;

    // 通知所有等待的线程
    cv_.notify_all();

    // 等待所有线程结束
    for (auto& worker : workers_) {
        if (worker.joinable()) {
            worker.join();
        }
    }
}

void genetic_optimizer::set_population_size(int population_size) {
    population_size_ = population_size;
}

void genetic_optimizer::set_elite_count(int elite_count) {
    elite_count_ = elite_count;
}

void genetic_optimizer::set_crossover_rate(double crossover_rate) {
    crossover_rate_ = crossover_rate;
}

void genetic_optimizer::set_mutation_rate(double mutation_rate) {
    mutation_rate_ = mutation_rate;
    initial_mutation_rate_ = mutation_rate;
}

void genetic_optimizer::set_max_stagnation_iterations(int max_stagnation_iterations) {
    max_stagnation_iterations_ = max_stagnation_iterations;
}

void genetic_optimizer::set_use_adaptive_rates(bool use_adaptive_rates) {
    use_adaptive_rates_ = use_adaptive_rates;
}

std::vector<optimization_result> genetic_optimizer::run() {
    BT_LOG_INFO("Starting genetic optimization");

    // 清空结果
    results_.clear();

    // 设置总任务数
    total_tasks_ = max_iterations_;

    BT_LOG_INFO("Will evaluate {} parameter combinations", total_tasks_);

    // 重置计数器
    completed_tasks_ = 0;
    stop_ = false;

    // 创建工作线程
    int num_threads = std::min(parallel_jobs_, max_iterations_);
    if (num_threads <= 0) {
        num_threads = 1;
    }

    BT_LOG_INFO("Starting {} worker threads", num_threads);

    // 创建工作线程
    for (int i = 0; i < num_threads; ++i) {
        workers_.emplace_back(&genetic_optimizer::worker_thread, this);
    }

    // 生成初始种群
    auto population = generate_initial_population();

    BT_LOG_INFO("Generated initial population of size {}", population.size());

    // 将初始种群加入队列
    {
        std::unique_lock<std::mutex> lock(mutex_);

        for (const auto& individual : population) {
            param_queue_.push(individual);
        }

        // 通知所有等待的线程
        cv_.notify_all();
    }

    // 等待初始种群评估完成
    while (true) {
        {
            std::unique_lock<std::mutex> lock(mutex_);
            if (param_queue_.empty() || stop_ || completed_tasks_ >= population_size_) {
                break;
            }
        }

        // 避免频繁加锁，减少竞争
        std::this_thread::sleep_for(std::chrono::milliseconds(100));
    }

    BT_LOG_INFO("Initial population evaluated");

    // 如果使用RANK归一化，更新所有结果的排名
    if (target_->get_normalization_type() == normalization_type::RANK) {
        BT_LOG_INFO("Initializing rank normalization parameters");

        // 收集所有指标值
        std::vector<std::unordered_map<std::string, double>> all_metrics;
        for (const auto& result : results_) {
            all_metrics.push_back(result.metrics);
        }

        // 更新归一化参数
        target_->update_normalization_params(all_metrics);

        // 重新计算所有结果的分数
        for (auto& result : results_) {
            result.score = target_->calculate_score(result.metrics);
        }

        BT_LOG_INFO("Rank normalization parameters initialized");
    }

    // 如果已经达到最大迭代次数，直接返回
    if (completed_tasks_ >= max_iterations_ || stop_) {
        goto cleanup;
    }

    // 遗传算法主循环
    {
        int generation = 1;
        int remaining_iterations = max_iterations_ - population_size_;

        while (remaining_iterations > 0 && !stop_) {
            BT_LOG_INFO("Starting generation {}", generation);

            // 获取当前种群的得分
            std::vector<double> scores;
            std::vector<std::unordered_map<std::string, parameter_value>> current_population;

            for (const auto& result : results_) {
                scores.push_back(result.score);
                current_population.push_back(result.parameters);
            }

            // 选择操作
            auto selected = selection(current_population, scores);

            // 创建新一代种群
            std::vector<std::unordered_map<std::string, parameter_value>> new_population;

            // 用于检查重复个体的集合
            std::unordered_set<std::string> unique_individuals;

            // 保留精英
            std::vector<size_t> elite_indices(scores.size());
            std::iota(elite_indices.begin(), elite_indices.end(), 0);
            std::sort(elite_indices.begin(), elite_indices.end(), [&scores](size_t i, size_t j) {
                return scores[i] > scores[j];
            });

            for (int i = 0; i < std::min(elite_count_, static_cast<int>(elite_indices.size())); ++i) {
                auto elite = current_population[elite_indices[i]];

                // 将精英个体转换为字符串，用于检查重复
                std::string elite_str = individual_to_string(elite);

                // 如果这个精英个体还没有出现过，添加到新种群中
                if (unique_individuals.find(elite_str) == unique_individuals.end()) {
                    unique_individuals.insert(elite_str);
                    new_population.push_back(elite);
                }
            }

            // 交叉和变异
            int attempts = 0;
            while (new_population.size() < static_cast<size_t>(population_size_) && attempts < population_size_ * 3) {
                attempts++;

                // 选择两个父代
                size_t parent1_idx = roulette_wheel_selection(scores);
                size_t parent2_idx = roulette_wheel_selection(scores);

                // 确保选择不同的父代
                while (parent2_idx == parent1_idx && scores.size() > 1) {
                    parent2_idx = roulette_wheel_selection(scores);
                }

                auto parent1 = current_population[parent1_idx];
                auto parent2 = current_population[parent2_idx];

                // 交叉
                std::unordered_map<std::string, parameter_value> child1 = parent1;
                std::unordered_map<std::string, parameter_value> child2 = parent2;

                if (std::uniform_real_distribution<double>(0.0, 1.0)(gen_) < crossover_rate_) {
                    auto [c1, c2] = crossover(parent1, parent2);
                    child1 = c1;
                    child2 = c2;
                }

                // 变异
                child1 = mutation(child1);
                child2 = mutation(child2);

                // 将子代转换为字符串，用于检查重复
                std::string child1_str = individual_to_string(child1);

                // 如果这个子代还没有出现过，添加到新种群中
                if (unique_individuals.find(child1_str) == unique_individuals.end()) {
                    unique_individuals.insert(child1_str);
                    new_population.push_back(child1);
                }

                // 如果新种群还没有满，尝试添加第二个子代
                if (new_population.size() < static_cast<size_t>(population_size_)) {
                    std::string child2_str = individual_to_string(child2);

                    // 如果这个子代还没有出现过，添加到新种群中
                    if (unique_individuals.find(child2_str) == unique_individuals.end()) {
                        unique_individuals.insert(child2_str);
                        new_population.push_back(child2);
                    }
                }
            }

            // 如果生成的个体不足，使用已有个体填充
            if (new_population.size() < static_cast<size_t>(population_size_)) {
                BT_LOG_WARN("Could not generate enough unique individuals for new generation. Generated {} out of {}",
                           new_population.size(), population_size_);

                // 复制已有个体并强制变异
                while (new_population.size() < static_cast<size_t>(population_size_)) {
                    size_t idx = std::uniform_int_distribution<size_t>(0, new_population.size() - 1)(gen_);
                    auto new_individual = new_population[idx];

                    // 强制变异多个参数
                    for (const auto& param_def : space_.get_parameters()) {
                        if (std::uniform_real_distribution<double>(0.0, 1.0)(gen_) < 0.5) {
                            new_individual[param_def.name] = generate_random_value(param_def);
                        }
                    }

                    new_population.push_back(new_individual);
                }
            }

            // 将新一代种群加入队列
            int batch_size = std::min(remaining_iterations, static_cast<int>(new_population.size()));

            {
                std::unique_lock<std::mutex> lock(mutex_);

                for (int i = 0; i < batch_size; ++i) {
                    param_queue_.push(new_population[i]);
                }

                // 通知所有等待的线程
                cv_.notify_all();
            }

            // 等待新一代种群评估完成
            while (true) {
                {
                    std::unique_lock<std::mutex> lock(mutex_);
                    if (param_queue_.empty() || stop_) {
                        break;
                    }
                }

                // 避免频繁加锁，减少竞争
                std::this_thread::sleep_for(std::chrono::milliseconds(100));
            }

            // 更新剩余迭代次数
            remaining_iterations -= batch_size;
            generation++;

            // 如果使用RANK归一化，更新所有结果的排名
            if (target_->get_normalization_type() == normalization_type::RANK) {
                BT_LOG_INFO("Updating rank normalization parameters");

                // 收集所有指标值
                std::vector<std::unordered_map<std::string, double>> all_metrics;
                for (const auto& result : results_) {
                    all_metrics.push_back(result.metrics);
                }

                // 更新归一化参数
                target_->update_normalization_params(all_metrics);

                // 重新计算所有结果的分数
                for (auto& result : results_) {
                    result.score = target_->calculate_score(result.metrics);
                }

                BT_LOG_INFO("Rank normalization parameters updated");
            }

            // 找到当前最佳得分
            double current_best_score = std::numeric_limits<double>::lowest();
            for (const auto& result : results_) {
                if (result.score > current_best_score) {
                    current_best_score = result.score;
                }
            }

            // 计算种群多样性
            population_diversity_ = calculate_population_diversity(current_population);

            // 检查是否有进展
            if (current_best_score > best_score_) {
                best_score_ = current_best_score;
                stagnation_iterations_ = 0; // 重置无进展计数器
            } else {
                stagnation_iterations_++; // 增加无进展计数器
            }

            // 计算停滞比例
            double stagnation_ratio = static_cast<double>(stagnation_iterations_) / max_stagnation_iterations_;

            // 自适应调整变异率和交叉率
            adapt_rates(population_diversity_, stagnation_ratio);

            BT_LOG_INFO("Generation {} completed, best score: {}, diversity: {:.2f}, mutation rate: {:.2f}, crossover rate: {:.2f}, remaining iterations: {}, stagnation: {}/{}",
                       generation, current_best_score, population_diversity_, mutation_rate_, crossover_rate_,
                       remaining_iterations, stagnation_iterations_, max_stagnation_iterations_);

            // 如果连续多代没有进展，提前结束
            if (stagnation_iterations_ >= max_stagnation_iterations_) {
                BT_LOG_WARN("Optimization stopped early due to lack of progress for {} generations", max_stagnation_iterations_);
                break;
            }
        }
    }

    // 最终更新排名（如果使用RANK归一化）
    if (target_->get_normalization_type() == normalization_type::RANK) {
        BT_LOG_INFO("Final update of rank normalization parameters");

        // 收集所有指标值
        std::vector<std::unordered_map<std::string, double>> all_metrics;
        for (const auto& result : results_) {
            all_metrics.push_back(result.metrics);
        }

        // 更新归一化参数
        target_->update_normalization_params(all_metrics);

        // 重新计算所有结果的分数
        for (auto& result : results_) {
            result.score = target_->calculate_score(result.metrics);
        }

        BT_LOG_INFO("Rank normalization parameters finalized");
    }

cleanup:
    // 设置停止标志
    stop_ = true;

    // 通知所有等待的线程
    cv_.notify_all();

    // 等待所有线程结束
    BT_LOG_INFO("Waiting for worker threads to finish");
    for (auto& worker : workers_) {
        if (worker.joinable()) {
            worker.join();
        }
    }

    workers_.clear();
    BT_LOG_INFO("All worker threads finished");

    // 按得分排序
    BT_LOG_INFO("Sorting results by score");
    std::sort(results_.begin(), results_.end(), [](const optimization_result& a, const optimization_result& b) {
        return a.score > b.score;
    });

    BT_LOG_INFO("Optimization completed. Evaluated {} parameter combinations", results_.size());

    return results_;
}

void genetic_optimizer::worker_thread() {
    BT_LOG_DEBUG("Worker thread started");

    while (!stop_) {
        // 获取参数组合
        std::unordered_map<std::string, parameter_value> params;
        bool has_task = false;

        {
            std::unique_lock<std::mutex> lock(mutex_);

            // 检查队列是否为空
            if (param_queue_.empty()) {
                // 如果队列为空，等待条件变量通知或者停止信号
                if (!stop_) {
                    BT_LOG_DEBUG("Worker thread waiting for tasks");
                    cv_.wait_for(lock, std::chrono::seconds(1), [this] {
                        return !param_queue_.empty() || stop_;
                    });

                    // 再次检查队列是否为空
                    if (param_queue_.empty()) {
                        continue;
                    }
                } else {
                    BT_LOG_DEBUG("Worker thread stopping");
                    break;
                }
            }

            // 获取参数组合
            params = param_queue_.front();
            param_queue_.pop();
            has_task = true;
            BT_LOG_DEBUG("Worker thread got a task, remaining tasks in queue: {}", param_queue_.size());
        }

        if (has_task) {
            // 评估参数组合
            BT_LOG_DEBUG("Evaluating parameter combination");
            auto result = evaluate(params);

            // 添加到结果
            if (result.valid) {
                std::unique_lock<std::mutex> lock(mutex_);
                results_.push_back(result);
                BT_LOG_DEBUG("Added valid result with score: {}", result.score);
            } else {
                BT_LOG_DEBUG("Evaluation result is invalid");
            }

            // 更新进度
            int completed = ++completed_tasks_;
            BT_LOG_DEBUG("Completed tasks: {}/{}", completed, total_tasks_);

            // 调用进度回调
            if (progress_callback_) {
                progress_callback_(completed, total_tasks_, result);
            }
        }
    }

    BT_LOG_DEBUG("Worker thread exited");
}

std::vector<std::unordered_map<std::string, parameter_value>> genetic_optimizer::generate_initial_population() const {
    std::vector<std::unordered_map<std::string, parameter_value>> population;

    // 用于检查重复个体的集合
    std::unordered_set<std::string> unique_individuals;

    // 生成随机个体
    int attempts = 0;
    while (population.size() < static_cast<size_t>(population_size_) && attempts < population_size_ * 3) {
        attempts++;

        std::unordered_map<std::string, parameter_value> individual;

        for (const auto& param_def : space_.get_parameters()) {
            individual[param_def.name] = generate_random_value(param_def);
        }

        // 将个体转换为字符串，用于检查重复
        std::string individual_str = individual_to_string(individual);

        // 如果这个个体还没有出现过，添加到种群中
        if (unique_individuals.find(individual_str) == unique_individuals.end()) {
            unique_individuals.insert(individual_str);
            population.push_back(individual);
        }
    }

    // 如果生成的个体不足，使用已有个体填充
    if (population.size() < static_cast<size_t>(population_size_)) {
        BT_LOG_WARN("Could not generate enough unique individuals. Generated {} out of {}",
                   population.size(), population_size_);

        // 复制已有个体并稍微变异
        while (population.size() < static_cast<size_t>(population_size_)) {
            size_t idx = std::uniform_int_distribution<size_t>(0, population.size() - 1)(gen_);
            auto new_individual = mutation(population[idx]);
            population.push_back(new_individual);
        }
    }

    return population;
}

parameter_value genetic_optimizer::generate_random_value(const parameter_definition& param_def) const {
    if (!param_def.values.empty()) {
        // 如果有预定义的值列表，随机选择一个
        std::uniform_int_distribution<size_t> dist(0, param_def.values.size() - 1);
        return param_def.values[dist(gen_)];
    } else if (param_def.type == parameter_type::INTEGER) {
        // 整数类型参数
        int min_val = std::get<int>(param_def.min_value);
        int max_val = std::get<int>(param_def.max_value);
        int step = std::get<int>(param_def.step);

        if (step > 1) {
            // 如果有步长，生成符合步长的随机值
            int num_steps = (max_val - min_val) / step + 1;
            std::uniform_int_distribution<int> step_dist(0, num_steps - 1);
            int random_step = step_dist(gen_);
            return min_val + random_step * step;
        } else {
            // 否则，直接生成随机整数
            std::uniform_int_distribution<int> dist(min_val, max_val);
            return dist(gen_);
        }
    } else if (param_def.type == parameter_type::FLOAT) {
        // 浮点类型参数
        double min_val = std::get<double>(param_def.min_value);
        double max_val = std::get<double>(param_def.max_value);
        double step = std::get<double>(param_def.step);

        if (step > 0.0) {
            // 如果有步长，生成符合步长的随机值
            int num_steps = static_cast<int>((max_val - min_val) / step + 0.5) + 1;
            std::uniform_int_distribution<int> step_dist(0, num_steps - 1);
            int random_step = step_dist(gen_);
            return min_val + random_step * step;
        } else {
            // 否则，直接生成随机浮点数
            std::uniform_real_distribution<double> dist(min_val, max_val);
            return dist(gen_);
        }
    } else if (param_def.type == parameter_type::BOOLEAN) {
        // 布尔类型参数
        std::uniform_int_distribution<int> dist(0, 1);
        return (dist(gen_) == 1);
    } else {
        // 默认返回默认值
        return param_def.default_value;
    }
}

std::vector<std::unordered_map<std::string, parameter_value>> genetic_optimizer::selection(
    const std::vector<std::unordered_map<std::string, parameter_value>>& population,
    const std::vector<double>& scores) const {

    // 使用轮盘赌选择
    std::vector<std::unordered_map<std::string, parameter_value>> selected;

    for (size_t i = 0; i < population.size(); ++i) {
        size_t idx = roulette_wheel_selection(scores);
        selected.push_back(population[idx]);
    }

    return selected;
}

size_t genetic_optimizer::roulette_wheel_selection(const std::vector<double>& scores) const {
    // 随机决定使用哪种选择策略
    double selection_strategy = std::uniform_real_distribution<double>(0.0, 1.0)(gen_);

    // 20%的概率使用锦标赛选择
    if (selection_strategy < 0.2) {
        // 锦标赛选择：随机选择k个个体，选择其中最好的一个
        int k = std::max(2, static_cast<int>(scores.size() / 5));
        std::vector<size_t> tournament;

        // 随机选择k个个体
        for (int i = 0; i < k; ++i) {
            size_t idx = std::uniform_int_distribution<size_t>(0, scores.size() - 1)(gen_);
            tournament.push_back(idx);
        }

        // 找出最好的个体
        size_t best_idx = tournament[0];
        for (size_t i = 1; i < tournament.size(); ++i) {
            if (scores[tournament[i]] > scores[best_idx]) {
                best_idx = tournament[i];
            }
        }

        return best_idx;
    }
    // 10%的概率使用随机选择（增加多样性）
    else if (selection_strategy < 0.3) {
        return std::uniform_int_distribution<size_t>(0, scores.size() - 1)(gen_);
    }
    // 70%的概率使用轮盘赌选择
    else {
        // 计算总得分
        double total_score = 0.0;
        double min_score = std::numeric_limits<double>::max();

        for (double score : scores) {
            min_score = std::min(min_score, score);
        }

        // 如果有负得分，将所有得分平移到非负区间
        std::vector<double> adjusted_scores = scores;
        if (min_score < 0.0) {
            for (double& score : adjusted_scores) {
                score -= min_score - 1.0;  // 确保最小得分为1.0
            }
        }

        // 应用非线性变换，增强选择压力
        double power = 1.5; // 指数大于1会增强选择压力
        for (double& score : adjusted_scores) {
            score = std::pow(score, power);
        }

        for (double score : adjusted_scores) {
            total_score += score;
        }

        // 如果总得分为0，使用均匀选择
        if (total_score <= 0.0) {
            std::uniform_int_distribution<size_t> dist(0, scores.size() - 1);
            return dist(gen_);
        }

        // 轮盘赌选择
        std::uniform_real_distribution<double> dist(0.0, total_score);
        double r = dist(gen_);

        double cumulative_score = 0.0;
        for (size_t i = 0; i < adjusted_scores.size(); ++i) {
            cumulative_score += adjusted_scores[i];
            if (r <= cumulative_score) {
                return i;
            }
        }

        // 如果没有选中任何个体（浮点数精度问题），返回最后一个
        return adjusted_scores.size() - 1;
    }
}

std::pair<std::unordered_map<std::string, parameter_value>, std::unordered_map<std::string, parameter_value>> genetic_optimizer::crossover(
    const std::unordered_map<std::string, parameter_value>& parent1,
    const std::unordered_map<std::string, parameter_value>& parent2) const {

    std::unordered_map<std::string, parameter_value> child1 = parent1;
    std::unordered_map<std::string, parameter_value> child2 = parent2;

    // 对每个参数进行交叉
    for (const auto& param_def : space_.get_parameters()) {
        // 随机决定是否交换该参数
        if (std::uniform_real_distribution<double>(0.0, 1.0)(gen_) < 0.5) {
            auto it1 = parent1.find(param_def.name);
            auto it2 = parent2.find(param_def.name);

            if (it1 != parent1.end() && it2 != parent2.end()) {
                child1[param_def.name] = it2->second;
                child2[param_def.name] = it1->second;
            }
        }
    }

    return {child1, child2};
}

std::unordered_map<std::string, parameter_value> genetic_optimizer::mutation(
    const std::unordered_map<std::string, parameter_value>& individual) const {

    std::unordered_map<std::string, parameter_value> mutated = individual;

    // 确保至少有一个参数发生变异，以增加多样性
    bool has_mutation = false;

    // 对每个参数进行变异
    for (const auto& param_def : space_.get_parameters()) {
        // 随机决定是否变异该参数
        if (std::uniform_real_distribution<double>(0.0, 1.0)(gen_) < mutation_rate_) {
            mutated[param_def.name] = generate_random_value(param_def);
            has_mutation = true;
        }
    }

    // 如果没有任何参数发生变异，随机选择一个参数进行变异
    if (!has_mutation && !space_.get_parameters().empty()) {
        size_t param_idx = std::uniform_int_distribution<size_t>(0, space_.get_parameters().size() - 1)(gen_);
        const auto& param_def = space_.get_parameters()[param_idx];
        mutated[param_def.name] = generate_random_value(param_def);
    }

    return mutated;
}

std::string genetic_optimizer::individual_to_string(const std::unordered_map<std::string, parameter_value>& individual) const {
    std::string result;

    for (const auto& [name, value] : individual) {
        result += name + ":";
        if (std::holds_alternative<int>(value)) {
            result += std::to_string(std::get<int>(value));
        } else if (std::holds_alternative<double>(value)) {
            result += std::to_string(std::get<double>(value));
        } else if (std::holds_alternative<bool>(value)) {
            result += std::get<bool>(value) ? "true" : "false";
        } else if (std::holds_alternative<std::string>(value)) {
            result += std::get<std::string>(value);
        }
        result += ";";
    }

    return result;
}

double genetic_optimizer::calculate_population_diversity(const std::vector<std::unordered_map<std::string, parameter_value>>& population) const {
    if (population.empty() || population.size() == 1) {
        return 0.0;
    }

    // 计算每个参数的标准差，然后取平均值
    std::unordered_map<std::string, double> param_sum;
    std::unordered_map<std::string, double> param_sum_sq;
    std::unordered_map<std::string, int> param_count;

    // 收集所有参数名称
    std::unordered_set<std::string> param_names;
    for (const auto& individual : population) {
        for (const auto& [name, _] : individual) {
            param_names.insert(name);
        }
    }

    // 计算每个参数的和和平方和
    for (const auto& individual : population) {
        for (const auto& param_name : param_names) {
            auto it = individual.find(param_name);
            if (it != individual.end()) {
                double value = 0.0;
                if (std::holds_alternative<int>(it->second)) {
                    value = static_cast<double>(std::get<int>(it->second));
                } else if (std::holds_alternative<double>(it->second)) {
                    value = std::get<double>(it->second);
                } else if (std::holds_alternative<bool>(it->second)) {
                    value = std::get<bool>(it->second) ? 1.0 : 0.0;
                } else {
                    // 对于字符串参数，跳过
                    continue;
                }

                param_sum[param_name] += value;
                param_sum_sq[param_name] += value * value;
                param_count[param_name]++;
            }
        }
    }

    // 计算每个参数的标准差
    double total_std_dev = 0.0;
    int num_params = 0;

    for (const auto& param_name : param_names) {
        if (param_count[param_name] > 1) {
            double mean = param_sum[param_name] / param_count[param_name];
            double variance = (param_sum_sq[param_name] / param_count[param_name]) - (mean * mean);
            if (variance > 0.0) {
                double std_dev = std::sqrt(variance);

                // 获取参数的范围
                double range = 1.0;
                for (const auto& param_def : space_.get_parameters()) {
                    if (param_def.name == param_name) {
                        try {
                            if (param_def.type == parameter_type::INTEGER) {
                                if (std::holds_alternative<int>(param_def.max_value) && std::holds_alternative<int>(param_def.min_value)) {
                                    range = static_cast<double>(std::get<int>(param_def.max_value) - std::get<int>(param_def.min_value));
                                }
                            } else if (param_def.type == parameter_type::FLOAT) {
                                if (std::holds_alternative<double>(param_def.max_value) && std::holds_alternative<double>(param_def.min_value)) {
                                    range = std::get<double>(param_def.max_value) - std::get<double>(param_def.min_value);
                                }
                            } else if (param_def.type == parameter_type::BOOLEAN) {
                                range = 1.0;
                            }
                        } catch (const std::bad_variant_access&) {
                            // 如果类型不匹配，使用默认范围
                            BT_LOG_WARN("Type mismatch for parameter {}, using default range", param_name);
                        }
                        break;
                    }
                }

                // 归一化标准差
                double normalized_std_dev = std_dev / range;
                total_std_dev += normalized_std_dev;
                num_params++;
            }
        }
    }

    // 计算平均标准差
    double avg_std_dev = num_params > 0 ? total_std_dev / num_params : 0.0;

    // 将多样性定义为标准差的函数，范围[0,1]
    // 多样性越高，标准差越大
    double diversity = std::min(1.0, avg_std_dev * 2.0);

    return diversity;
}

void genetic_optimizer::adapt_rates(double diversity, double stagnation_ratio) {
    if (!use_adaptive_rates_) {
        return;
    }

    // 当多样性低时，增加变异率以增加探索
    // 当多样性高时，降低变异率以增加利用
    mutation_rate_ = initial_mutation_rate_ * (1.0 + (1.0 - diversity) * 0.8);

    // 当停滞比例高时，更激进地增加变异率和降低交叉率，以跳出局部最优
    if (stagnation_ratio > 0.3) {  // 降低阈值，更早开始调整
        // 更激进的变异率增加
        mutation_rate_ = std::min(0.7, mutation_rate_ * (1.0 + stagnation_ratio * 1.0));

        // 更温和的交叉率降低
        crossover_rate_ = initial_crossover_rate_ * (1.0 - stagnation_ratio * 0.2);

        // 记录调整信息
        BT_LOG_DEBUG("Adapting rates due to stagnation: mutation_rate={:.3f}, crossover_rate={:.3f}, stagnation_ratio={:.3f}",
                    mutation_rate_, crossover_rate_, stagnation_ratio);
    } else {
        crossover_rate_ = initial_crossover_rate_;
    }

    // 确保变异率和交叉率在合理范围内，但允许更高的变异率上限
    mutation_rate_ = std::max(0.05, std::min(0.7, mutation_rate_));
    crossover_rate_ = std::max(0.6, std::min(0.95, crossover_rate_));
}

} // namespace cpp_backtest
} // namespace fast_trader_elite
