#include <fast_trader_elite/cpp_backtest/parameter_optimizer/metric_calculator.h>
#include <unordered_map>
#include <string>
#include <functional>
#include <iostream>

namespace fast_trader_elite {
namespace cpp_backtest {

// 全局单例
metric_calculator& get_metric_calculator() {
    static metric_calculator instance;
    return instance;
}

metric_calculator::metric_calculator() {
    // 注册内置指标
    register_built_in_metrics();
}

void metric_calculator::register_built_in_metrics() {
    // 注册基本指标
    register_metric("return", &metric_calculator::calculate_return);
    register_metric("annual_return", &metric_calculator::calculate_annual_return);
    register_metric("sharpe_ratio", &metric_calculator::calculate_sharpe_ratio);
    register_metric("sortino_ratio", &metric_calculator::calculate_sortino_ratio);
    register_metric("max_drawdown", &metric_calculator::calculate_max_drawdown);
    register_metric("return_over_mdd", &metric_calculator::calculate_return_over_mdd);
    register_metric("return_over_trade", &metric_calculator::calculate_return_over_trade);
    register_metric("avg_position_time", &metric_calculator::calculate_avg_position_time);
    register_metric("win_rate", &metric_calculator::calculate_win_rate);
    register_metric("profit_loss_ratio", &metric_calculator::calculate_profit_loss_ratio);
    register_metric("daily_trading_value", &metric_calculator::calculate_daily_trading_value);
    register_metric("max_leverage", &metric_calculator::calculate_max_leverage);

    // 注册新增指标
    register_metric("total_trades", &metric_calculator::calculate_total_trades);
    register_metric("daily_trades", &metric_calculator::calculate_daily_trades);
    register_metric("total_trading_value", &metric_calculator::calculate_total_trading_value);
    register_metric("position_cycles", &metric_calculator::calculate_position_cycles);
}

void metric_calculator::register_metric(const std::string& name, metric_function func) {
    metrics_[name] = func;
}

double metric_calculator::calculate(const std::string& name, const backtest_data& data, uint16_t instrument_idx) const {
    auto it = metrics_.find(name);
    if (it == metrics_.end()) {
        std::cerr << "Metric not found: " << name << std::endl;
        return 0.0;
    }

    return it->second(data, instrument_idx);
}

std::unordered_map<std::string, double> metric_calculator::calculate_all(
    const std::vector<std::string>& metrics,
    const backtest_data& data,
    uint16_t instrument_idx) const {

    std::unordered_map<std::string, double> results;

    for (const auto& metric : metrics) {
        results[metric] = calculate(metric, data, instrument_idx);
    }

    return results;
}

bool metric_calculator::has_metric(const std::string& name) const {
    return metrics_.find(name) != metrics_.end();
}

std::vector<std::string> metric_calculator::get_available_metrics() const {
    std::vector<std::string> result;
    result.reserve(metrics_.size());

    for (const auto& pair : metrics_) {
        result.push_back(pair.first);
    }

    return result;
}

// 以下是各个指标的计算函数实现
double metric_calculator::calculate_return(const backtest_data& data, uint16_t instrument_idx) {
    return data.get_return(instrument_idx);
}

double metric_calculator::calculate_annual_return(const backtest_data& data, uint16_t instrument_idx) {
    return data.get_annual_return(instrument_idx);
}

double metric_calculator::calculate_sharpe_ratio(const backtest_data& data, uint16_t instrument_idx) {
    return data.get_sharpe_ratio(instrument_idx);
}

double metric_calculator::calculate_sortino_ratio(const backtest_data& data, uint16_t instrument_idx) {
    return data.get_sortino_ratio(instrument_idx);
}

double metric_calculator::calculate_max_drawdown(const backtest_data& data, uint16_t instrument_idx) {
    return data.get_max_drawdown(instrument_idx);
}

double metric_calculator::calculate_return_over_mdd(const backtest_data& data, uint16_t instrument_idx) {
    return data.get_return_over_mdd(instrument_idx);
}

double metric_calculator::calculate_return_over_trade(const backtest_data& data, uint16_t instrument_idx) {
    return data.get_return_over_trade(instrument_idx);
}

double metric_calculator::calculate_avg_position_time(const backtest_data& data, uint16_t instrument_idx) {
    return data.get_avg_position_time(instrument_idx);
}

double metric_calculator::calculate_win_rate(const backtest_data& data, uint16_t instrument_idx) {
    return data.get_win_rate(instrument_idx);
}

double metric_calculator::calculate_profit_loss_ratio(const backtest_data& data, uint16_t instrument_idx) {
    return data.get_profit_loss_ratio(instrument_idx);
}

double metric_calculator::calculate_daily_trading_value(const backtest_data& data, uint16_t instrument_idx) {
    return data.get_daily_trading_value(instrument_idx);
}

double metric_calculator::calculate_max_leverage(const backtest_data& data, uint16_t instrument_idx) {
    return data.get_max_leverage(instrument_idx);
}

double metric_calculator::calculate_total_trades(const backtest_data& data, uint16_t instrument_idx) {
    return static_cast<double>(data.get_total_trades(instrument_idx));
}

double metric_calculator::calculate_daily_trades(const backtest_data& data, uint16_t instrument_idx) {
    double total_trades = static_cast<double>(data.get_total_trades(instrument_idx));
    double days = data.get_trading_days(instrument_idx);

    if (days <= 0.0) {
        return 0.0;
    }

    return total_trades / days;
}

double metric_calculator::calculate_total_trading_value(const backtest_data& data, uint16_t instrument_idx) {
    return data.get_total_trading_value(instrument_idx);
}

double metric_calculator::calculate_position_cycles(const backtest_data& data, uint16_t instrument_idx) {
    return static_cast<double>(data.get_position_cycles_count(instrument_idx));
}

} // namespace cpp_backtest
} // namespace fast_trader_elite
