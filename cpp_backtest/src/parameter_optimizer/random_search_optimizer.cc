#include <fast_trader_elite/cpp_backtest/parameter_optimizer/random_search_optimizer.h>
#include <fast_trader_elite/cpp_backtest/backtest_logger.h>
#include <iostream>
#include <algorithm>

namespace fast_trader_elite {
namespace cpp_backtest {

random_search_optimizer::random_search_optimizer(
    const parameter_space& space, 
    const std::shared_ptr<optimization_target>& target)
    : optimizer(space, target) {
}

random_search_optimizer::~random_search_optimizer() {
    // 设置停止标志
    stop_ = true;
    
    // 通知所有等待的线程
    cv_.notify_all();
    
    // 等待所有线程结束
    for (auto& worker : workers_) {
        if (worker.joinable()) {
            worker.join();
        }
    }
}

std::vector<optimization_result> random_search_optimizer::run() {
    BT_LOG_INFO("Starting random search optimization");
    
    // 清空结果
    results_.clear();
    
    // 设置总任务数
    total_tasks_ = max_iterations_;
    
    BT_LOG_INFO("Will evaluate {} random parameter combinations", total_tasks_);
    
    // 重置计数器
    completed_tasks_ = 0;
    stop_ = false;
    
    // 创建工作线程
    int num_threads = std::min(parallel_jobs_, max_iterations_);
    if (num_threads <= 0) {
        num_threads = 1;
    }
    
    BT_LOG_INFO("Starting {} worker threads", num_threads);
    
    // 创建工作线程
    for (int i = 0; i < num_threads; ++i) {
        workers_.emplace_back(&random_search_optimizer::worker_thread, this);
    }
    
    // 将参数组合分批加入队列，避免一次性加入过多导致内存占用过大
    const int batch_size = 10;
    int remaining_tasks = total_tasks_;
    
    BT_LOG_INFO("Adding parameter combinations to queue in batches of {}", batch_size);
    
    while (remaining_tasks > 0 && !stop_) {
        // 计算当前批次的大小
        int current_batch_size = std::min(batch_size, remaining_tasks);
        
        // 生成当前批次的参数组合
        {
            std::unique_lock<std::mutex> lock(mutex_);
            
            for (int i = 0; i < current_batch_size; ++i) {
                // 生成随机参数组合
                auto params = space_.generate_random();
                param_queue_.push(params);
            }
            
            // 通知所有等待的线程
            cv_.notify_all();
        }
        
        remaining_tasks -= current_batch_size;
        BT_LOG_DEBUG("Added batch of {} combinations, remaining: {}", 
                    current_batch_size, remaining_tasks);
        
        // 等待队列中的任务被处理完
        while (true) {
            {
                std::unique_lock<std::mutex> lock(mutex_);
                if (param_queue_.empty() || stop_) {
                    break;
                }
            }
            
            // 避免频繁加锁，减少竞争
            std::this_thread::sleep_for(std::chrono::milliseconds(100));
        }
    }
    
    BT_LOG_INFO("All parameter combinations added to queue");
    
    // 设置停止标志
    stop_ = true;
    
    // 通知所有等待的线程
    cv_.notify_all();
    
    // 等待所有线程结束
    BT_LOG_INFO("Waiting for worker threads to finish");
    for (auto& worker : workers_) {
        if (worker.joinable()) {
            worker.join();
        }
    }
    
    workers_.clear();
    BT_LOG_INFO("All worker threads finished");
    
    // 更新归一化参数
    BT_LOG_INFO("Updating normalization parameters");
    std::vector<std::unordered_map<std::string, double>> all_metrics;
    all_metrics.reserve(results_.size());
    
    for (const auto& result : results_) {
        all_metrics.push_back(result.metrics);
    }
    
    target_->update_normalization_params(all_metrics);
    
    // 重新计算得分
    BT_LOG_INFO("Recalculating scores with normalized metrics");
    for (auto& result : results_) {
        result.score = target_->calculate_score(result.metrics);
    }
    
    // 按得分排序
    BT_LOG_INFO("Sorting results by score");
    std::sort(results_.begin(), results_.end(), [](const optimization_result& a, const optimization_result& b) {
        return a.score > b.score;
    });
    
    BT_LOG_INFO("Optimization completed. Evaluated {} parameter combinations", results_.size());
    
    return results_;
}

void random_search_optimizer::worker_thread() {
    BT_LOG_DEBUG("Worker thread started");
    
    while (!stop_) {
        // 获取参数组合
        std::unordered_map<std::string, parameter_value> params;
        bool has_task = false;
        
        {
            std::unique_lock<std::mutex> lock(mutex_);
            
            // 检查队列是否为空
            if (param_queue_.empty()) {
                // 如果队列为空，等待条件变量通知或者停止信号
                if (!stop_) {
                    BT_LOG_DEBUG("Worker thread waiting for tasks");
                    cv_.wait_for(lock, std::chrono::seconds(1), [this] {
                        return !param_queue_.empty() || stop_;
                    });
                    
                    // 再次检查队列是否为空
                    if (param_queue_.empty()) {
                        continue;
                    }
                } else {
                    BT_LOG_DEBUG("Worker thread stopping");
                    break;
                }
            }
            
            // 获取参数组合
            params = param_queue_.front();
            param_queue_.pop();
            has_task = true;
            BT_LOG_DEBUG("Worker thread got a task, remaining tasks in queue: {}", param_queue_.size());
        }
        
        if (has_task) {
            // 评估参数组合
            BT_LOG_DEBUG("Evaluating parameter combination");
            auto result = evaluate(params);
            
            // 添加到结果
            if (result.valid) {
                std::unique_lock<std::mutex> lock(mutex_);
                results_.push_back(result);
                BT_LOG_DEBUG("Added valid result with score: {}", result.score);
            } else {
                BT_LOG_DEBUG("Evaluation result is invalid");
            }
            
            // 更新进度
            int completed = ++completed_tasks_;
            BT_LOG_DEBUG("Completed tasks: {}/{}", completed, total_tasks_);
            
            // 调用进度回调
            if (progress_callback_) {
                progress_callback_(completed, total_tasks_, result);
            }
        }
    }
    
    BT_LOG_DEBUG("Worker thread exited");
}

} // namespace cpp_backtest
} // namespace fast_trader_elite
