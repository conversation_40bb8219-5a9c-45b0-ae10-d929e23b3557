#include <fast_trader_elite/cpp_backtest/parameter_optimizer/parameter_space.h>
#include <stdexcept>
#include <random>
#include <algorithm>
#include <iostream>

namespace fast_trader_elite {
namespace cpp_backtest {

parameter_space::parameter_space(const nlohmann::json& config) {
    if (!config.contains("parameters") || !config["parameters"].is_array()) {
        throw std::invalid_argument("Invalid parameter space configuration: 'parameters' array is missing");
    }

    for (const auto& param_config : config["parameters"]) {
        parameter_definition param;

        // 必需字段
        if (!param_config.contains("name") || !param_config["name"].is_string()) {
            throw std::invalid_argument("Parameter must have a name");
        }
        param.name = param_config["name"].get<std::string>();

        // 参数类型
        if (!param_config.contains("type") || !param_config["type"].is_string()) {
            throw std::invalid_argument("Parameter must have a type");
        }

        std::string type_str = param_config["type"].get<std::string>();
        if (type_str == "int" || type_str == "integer") {
            param.type = parameter_type::INTEGER;
        } else if (type_str == "float" || type_str == "double") {
            param.type = parameter_type::FLOAT;
        } else if (type_str == "bool" || type_str == "boolean") {
            param.type = parameter_type::BOOLEAN;
        } else if (type_str == "string") {
            param.type = parameter_type::STRING;
        } else if (type_str == "enum") {
            param.type = parameter_type::ENUM;
        } else {
            throw std::invalid_argument("Invalid parameter type: " + type_str);
        }

        // 描述（可选）
        if (param_config.contains("description") && param_config["description"].is_string()) {
            param.description = param_config["description"].get<std::string>();
        }

        // 是否需要优化（可选）
        if (param_config.contains("optimize") && param_config["optimize"].is_boolean()) {
            param.optimize = param_config["optimize"].get<bool>();
        }

        // 根据参数类型处理特定字段
        if (param.type == parameter_type::INTEGER) {
            // 整数类型参数
            if (param_config.contains("values") && param_config["values"].is_array()) {
                // 使用离散值列表
                for (const auto& value : param_config["values"]) {
                    param.values.push_back(value.get<int>());
                }
            } else {
                // 使用范围和步长
                if (!param_config.contains("min") || !param_config["min"].is_number()) {
                    throw std::invalid_argument("Integer parameter must have a min value");
                }
                if (!param_config.contains("max") || !param_config["max"].is_number()) {
                    throw std::invalid_argument("Integer parameter must have a max value");
                }

                param.min_value = param_config["min"].get<int>();
                param.max_value = param_config["max"].get<int>();

                if (param_config.contains("step") && param_config["step"].is_number()) {
                    param.step = param_config["step"].get<int>();
                } else {
                    param.step = 1;
                }
            }

            // 默认值（可选）
            if (param_config.contains("default") && param_config["default"].is_number()) {
                param.default_value = param_config["default"].get<int>();
            } else if (!param.values.empty()) {
                param.default_value = std::get<int>(param.values[0]);
            } else {
                param.default_value = std::get<int>(param.min_value);
            }
        } else if (param.type == parameter_type::FLOAT) {
            // 浮点类型参数
            if (param_config.contains("values") && param_config["values"].is_array()) {
                // 使用离散值列表
                for (const auto& value : param_config["values"]) {
                    param.values.push_back(value.get<double>());
                }
            } else {
                // 使用范围和步长
                if (!param_config.contains("min") || !param_config["min"].is_number()) {
                    throw std::invalid_argument("Float parameter must have a min value");
                }
                if (!param_config.contains("max") || !param_config["max"].is_number()) {
                    throw std::invalid_argument("Float parameter must have a max value");
                }

                param.min_value = param_config["min"].get<double>();
                param.max_value = param_config["max"].get<double>();

                if (param_config.contains("step") && param_config["step"].is_number()) {
                    param.step = param_config["step"].get<double>();
                } else {
                    param.step = 0.1;
                }
            }

            // 默认值（可选）
            if (param_config.contains("default") && param_config["default"].is_number()) {
                param.default_value = param_config["default"].get<double>();
            } else if (!param.values.empty()) {
                param.default_value = std::get<double>(param.values[0]);
            } else {
                param.default_value = std::get<double>(param.min_value);
            }
        } else if (param.type == parameter_type::BOOLEAN) {
            // 布尔类型参数
            param.values = {false, true};

            // 默认值（可选）
            if (param_config.contains("default") && param_config["default"].is_boolean()) {
                param.default_value = param_config["default"].get<bool>();
            } else {
                param.default_value = false;
            }
        } else if (param.type == parameter_type::STRING || param.type == parameter_type::ENUM) {
            // 字符串或枚举类型参数
            if (!param_config.contains("values") || !param_config["values"].is_array()) {
                throw std::invalid_argument("String/Enum parameter must have a values array");
            }

            for (const auto& value : param_config["values"]) {
                param.values.push_back(value.get<std::string>());
            }

            // 默认值（可选）
            if (param_config.contains("default") && param_config["default"].is_string()) {
                param.default_value = param_config["default"].get<std::string>();
            } else if (!param.values.empty()) {
                param.default_value = std::get<std::string>(param.values[0]);
            } else {
                param.default_value = "";
            }
        }

        // 添加参数定义
        add_parameter(param);
    }
}

void parameter_space::add_parameter(const parameter_definition& param) {
    // 检查参数名是否已存在
    if (param_index_.find(param.name) != param_index_.end()) {
        throw std::invalid_argument("Parameter with name '" + param.name + "' already exists");
    }

    // 添加参数
    param_index_[param.name] = parameters_.size();
    parameters_.push_back(param);
}

const std::vector<parameter_definition>& parameter_space::get_parameters() const {
    return parameters_;
}

const parameter_definition* parameter_space::get_parameter(const std::string& name) const {
    auto it = param_index_.find(name);
    if (it == param_index_.end()) {
        return nullptr;
    }
    return &parameters_[it->second];
}

std::vector<std::unordered_map<std::string, parameter_value>> parameter_space::generate_grid() const {
    std::vector<std::unordered_map<std::string, parameter_value>> result;

    // 如果没有参数，返回空结果
    if (parameters_.empty()) {
        return result;
    }

    // 计算每个参数的可能值
    std::vector<std::vector<parameter_value>> param_values;
    for (const auto& param : parameters_) {
        if (!param.optimize) {
            // 如果参数不需要优化，只使用默认值
            param_values.push_back({param.default_value});
            continue;
        }

        std::vector<parameter_value> values;
        if (!param.values.empty()) {
            // 如果有预定义的值列表，使用它
            values = param.values;
        } else {
            // 否则，根据范围和步长生成值
            if (param.type == parameter_type::INTEGER) {
                int min_val = std::get<int>(param.min_value);
                int max_val = std::get<int>(param.max_value);
                int step = std::get<int>(param.step);

                for (int val = min_val; val <= max_val; val += step) {
                    values.push_back(val);
                }
            } else if (param.type == parameter_type::FLOAT) {
                double min_val = std::get<double>(param.min_value);
                double max_val = std::get<double>(param.max_value);
                double step = std::get<double>(param.step);

                for (double val = min_val; val <= max_val; val += step) {
                    values.push_back(val);
                }
            } else if (param.type == parameter_type::BOOLEAN) {
                values = {false, true};
            }
        }

        param_values.push_back(values);
    }

    // 生成所有可能的组合
    std::vector<size_t> indices(parameters_.size(), 0);
    bool done = false;

    while (!done) {
        // 创建当前组合
        std::unordered_map<std::string, parameter_value> combination;
        for (size_t i = 0; i < parameters_.size(); ++i) {
            combination[parameters_[i].name] = param_values[i][indices[i]];
        }

        // 添加到结果
        result.push_back(combination);

        // 更新索引
        size_t idx = parameters_.size() - 1;
        while (true) {
            indices[idx]++;
            if (indices[idx] < param_values[idx].size()) {
                break;
            }

            indices[idx] = 0;
            if (idx == 0) {
                done = true;
                break;
            }

            idx--;
        }
    }

    return result;
}

std::unordered_map<std::string, parameter_value> parameter_space::generate_random() const {
    std::unordered_map<std::string, parameter_value> result;

    // 随机数生成器
    std::random_device rd;
    std::mt19937 gen(rd());

    for (const auto& param : parameters_) {
        if (!param.optimize) {
            // 如果参数不需要优化，使用默认值
            result[param.name] = param.default_value;
            continue;
        }

        if (!param.values.empty()) {
            // 如果有预定义的值列表，随机选择一个
            std::uniform_int_distribution<size_t> dist(0, param.values.size() - 1);
            result[param.name] = param.values[dist(gen)];
        } else if (param.type == parameter_type::INTEGER) {
            // 整数类型参数
            int min_val = std::get<int>(param.min_value);
            int max_val = std::get<int>(param.max_value);
            int step = std::get<int>(param.step);

            if (step > 1) {
                // 如果有步长，生成符合步长的随机值
                int num_steps = (max_val - min_val) / step + 1;
                std::uniform_int_distribution<int> step_dist(0, num_steps - 1);
                int random_step = step_dist(gen);
                result[param.name] = min_val + random_step * step;
            } else {
                // 否则，直接生成随机整数
                std::uniform_int_distribution<int> dist(min_val, max_val);
                result[param.name] = dist(gen);
            }
        } else if (param.type == parameter_type::FLOAT) {
            // 浮点类型参数
            double min_val = std::get<double>(param.min_value);
            double max_val = std::get<double>(param.max_value);
            double step = std::get<double>(param.step);

            if (step > 0.0) {
                // 如果有步长，生成符合步长的随机值
                int num_steps = static_cast<int>((max_val - min_val) / step + 0.5) + 1;
                std::uniform_int_distribution<int> step_dist(0, num_steps - 1);
                int random_step = step_dist(gen);
                result[param.name] = min_val + random_step * step;
            } else {
                // 否则，直接生成随机浮点数
                std::uniform_real_distribution<double> dist(min_val, max_val);
                result[param.name] = dist(gen);
            }
        } else if (param.type == parameter_type::BOOLEAN) {
            // 布尔类型参数
            std::uniform_int_distribution<int> dist(0, 1);
            result[param.name] = (dist(gen) == 1);
        }
    }

    return result;
}

size_t parameter_space::get_space_size() const {
    size_t size = 1;

    for (const auto& param : parameters_) {
        if (!param.optimize) {
            // 如果参数不需要优化，只有一个可能的值
            continue;
        }

        if (!param.values.empty()) {
            // 如果有预定义的值列表，使用它的大小
            size *= param.values.size();
        } else if (param.type == parameter_type::INTEGER) {
            // 整数类型参数
            int min_val = std::get<int>(param.min_value);
            int max_val = std::get<int>(param.max_value);
            int step = std::get<int>(param.step);

            size_t num_values = (max_val - min_val) / step + 1;
            size *= num_values;
        } else if (param.type == parameter_type::FLOAT) {
            // 浮点类型参数
            double min_val = std::get<double>(param.min_value);
            double max_val = std::get<double>(param.max_value);
            double step = std::get<double>(param.step);

            size_t num_values = static_cast<size_t>((max_val - min_val) / step) + 1;
            size *= num_values;
        } else if (param.type == parameter_type::BOOLEAN) {
            // 布尔类型参数
            size *= 2;
        }
    }

    return size;
}

nlohmann::json parameter_space::apply_parameters(
    const nlohmann::json& base_config,
    const std::unordered_map<std::string, parameter_value>& params) {

    // 创建配置的副本
    nlohmann::json result = base_config;

    // 确保 symbol_configs 数组存在且至少有一个元素
    if (!result.contains("symbol_configs") || !result["symbol_configs"].is_array() || result["symbol_configs"].empty()) {
        result["symbol_configs"] = nlohmann::json::array();
        result["symbol_configs"].push_back(nlohmann::json::object());
    }

    // 应用每个参数
    for (const auto& [param_name, param_value] : params) {
        set_nested_parameter(result, param_name, param_value);
    }

    return result;
}

std::unordered_map<std::string, parameter_value> parameter_space::extract_parameters(
    const nlohmann::json& config,
    const std::vector<std::string>& param_names) {

    std::unordered_map<std::string, parameter_value> result;

    // 确保 symbol_configs 数组存在且至少有一个元素
    if (!config.contains("symbol_configs") || !config["symbol_configs"].is_array() || config["symbol_configs"].empty()) {
        return result; // 返回空结果
    }

    for (const auto& param_name : param_names) {
        try {
            result[param_name] = get_nested_parameter(config, param_name);
        } catch (const std::exception& e) {
            // 如果参数不存在，跳过
            continue;
        }
    }

    return result;
}

void parameter_space::set_nested_parameter(
    nlohmann::json& config,
    const std::string& param_name,
    const parameter_value& value) {

    // 确保 symbol_configs 数组存在且至少有一个元素
    if (!config.contains("symbol_configs") || !config["symbol_configs"].is_array() || config["symbol_configs"].empty()) {
        config["symbol_configs"] = nlohmann::json::array();
        config["symbol_configs"].push_back(nlohmann::json::object());
    }

    // 解析参数路径
    std::vector<std::string> path_parts;
    std::string current_part;
    size_t pos = 0;
    std::string delimiter = ".";
    std::string param_path = param_name;

    while ((pos = param_path.find(delimiter)) != std::string::npos) {
        path_parts.push_back(param_path.substr(0, pos));
        param_path.erase(0, pos + delimiter.length());
    }
    path_parts.push_back(param_path); // 添加最后一部分

    // 递归设置参数值
    nlohmann::json* current = &(config["symbol_configs"][0]);

    for (size_t i = 0; i < path_parts.size() - 1; ++i) {
        const auto& part = path_parts[i];
        if (!current->contains(part)) {
            (*current)[part] = nlohmann::json::object();
        }
        current = &((*current)[part]);
    }

    // 设置最终参数值
    const auto& last_part = path_parts.back();
    if (std::holds_alternative<int>(value)) {
        (*current)[last_part] = std::get<int>(value);
    } else if (std::holds_alternative<double>(value)) {
        (*current)[last_part] = std::get<double>(value);
    } else if (std::holds_alternative<bool>(value)) {
        (*current)[last_part] = std::get<bool>(value);
    } else if (std::holds_alternative<std::string>(value)) {
        (*current)[last_part] = std::get<std::string>(value);
    }
}

parameter_value parameter_space::get_nested_parameter(
    const nlohmann::json& config,
    const std::string& param_name) {

    // 检查 symbol_configs 是否存在
    if (!config.contains("symbol_configs") || !config["symbol_configs"].is_array() || config["symbol_configs"].empty()) {
        throw std::out_of_range("symbol_configs not found or empty");
    }

    // 解析参数路径
    std::vector<std::string> path_parts;
    std::string current_part;
    size_t pos = 0;
    std::string delimiter = ".";
    std::string param_path = param_name;

    while ((pos = param_path.find(delimiter)) != std::string::npos) {
        path_parts.push_back(param_path.substr(0, pos));
        param_path.erase(0, pos + delimiter.length());
    }
    path_parts.push_back(param_path); // 添加最后一部分

    // 递归获取参数值
    const nlohmann::json* current = &(config["symbol_configs"][0]);

    for (const auto& part : path_parts) {
        if (!current->contains(part)) {
            throw std::out_of_range("Parameter path part not found: " + part + " in " + param_name);
        }
        current = &((*current)[part]);
    }

    // 转换为参数值
    if (current->is_number_integer()) {
        return current->get<int>();
    } else if (current->is_number_float()) {
        return current->get<double>();
    } else if (current->is_boolean()) {
        return current->get<bool>();
    } else if (current->is_string()) {
        return current->get<std::string>();
    } else {
        throw std::invalid_argument("Unsupported parameter type: " + param_name);
    }
}

} // namespace cpp_backtest
} // namespace fast_trader_elite
