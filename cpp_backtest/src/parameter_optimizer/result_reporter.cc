#include <fast_trader_elite/cpp_backtest/parameter_optimizer/result_reporter.h>
#include <fast_trader_elite/cpp_backtest/parameter_optimizer/parameter_space.h>
#include <fast_trader_elite/cpp_backtest/parameter_optimizer/optimization_target.h>
#include <fstream>
#include <iostream>
#include <algorithm>
#include <filesystem>
#include <unordered_set>
#include <limits>
#include <nlohmann_json/json.hpp>

namespace fs = std::filesystem;

namespace fast_trader_elite {
namespace cpp_backtest {

// 辅助函数：将参数转换为字符串，用于去重
std::string params_to_string(const std::unordered_map<std::string, parameter_value>& params) {
    std::string param_str;
    for (const auto& [name, value] : params) {
        param_str += name + ":";
        if (std::holds_alternative<int>(value)) {
            param_str += std::to_string(std::get<int>(value));
        } else if (std::holds_alternative<double>(value)) {
            param_str += std::to_string(std::get<double>(value));
        } else if (std::holds_alternative<bool>(value)) {
            param_str += std::get<bool>(value) ? "true" : "false";
        } else if (std::holds_alternative<std::string>(value)) {
            param_str += std::get<std::string>(value);
        }
        param_str += ";";
    }
    return param_str;
}

// 辅助函数：将约束条件转换为JSON对象
nlohmann::json constraint_to_json(const constraint& c) {
    nlohmann::json constraint_json;
    constraint_json["metric"] = c.metric;
    constraint_json["operator"] = c.op;
    constraint_json["value"] = c.value;
    if (!c.description.empty()) {
        constraint_json["description"] = c.description;
    }
    return constraint_json;
}

// 辅助函数：将约束条件转换为HTML格式
std::string constraint_to_html(const constraint& c) {
    std::stringstream ss;
    ss << "      <li><strong>" << c.metric << " " << c.op << " " << c.value << "</strong>";

    if (!c.description.empty()) {
        ss << " - " << c.description;
    }

    ss << "</li>\n";
    return ss.str();
}

// 辅助函数：检查结果是否满足约束条件
bool check_result_constraints(const optimization_result& result, const std::vector<constraint>& constraints) {
    if (!result.valid) {
        return false;
    }

    for (const auto& c : constraints) {
        auto it = result.metrics.find(c.metric);
        if (it == result.metrics.end()) {
            // 如果找不到指标，则认为不满足约束
            return false;
        }

        double metric_value = it->second;
        bool check_result = false;

        // 检查约束条件
        if (c.op == "<") {
            check_result = metric_value < c.value;
        } else if (c.op == "<=") {
            check_result = metric_value <= c.value;
        } else if (c.op == "==") {
            check_result = std::abs(metric_value - c.value) < 1e-6;
        } else if (c.op == ">=") {
            check_result = metric_value >= c.value;
        } else if (c.op == ">") {
            check_result = metric_value > c.value;
        }

        if (!check_result) {
            return false;
        }
    }

    return true;
}

void result_reporter::set_output_path(const std::string& path) {
    output_path_ = path;
}

void result_reporter::set_constraints(const std::vector<constraint>& constraints) {
    constraints_ = constraints;
}

bool result_reporter::generate_report(const std::vector<optimization_result>& results) {
    // 创建输出目录
    try {
        fs::create_directories(output_path_);
    } catch (const std::exception& e) {
        std::cerr << "Error creating output directory: " << e.what() << std::endl;
        return false;
    }

    // 生成各种格式的报告
    bool csv_ok = generate_csv_report(results);
    bool json_ok = generate_json_report(results);
    bool html_ok = generate_html_report(results);

    // 生成参数敏感性分析
    bool sensitivity_ok = generate_sensitivity_analysis(results);

    return csv_ok && json_ok && html_ok && sensitivity_ok;
}

bool result_reporter::generate_best_config(const optimization_result& best_result, const std::string& base_config_path) {
    try {
        // 读取基础配置
        std::ifstream base_file(base_config_path);
        if (!base_file.is_open()) {
            std::cerr << "Failed to open base config file: " << base_config_path << std::endl;
            return false;
        }

        nlohmann::json base_config;
        base_file >> base_config;
        base_file.close();

        // 应用最佳参数
        nlohmann::json best_config = parameter_space::apply_parameters(base_config, best_result.parameters);

        // 保存最佳配置
        std::string best_config_path = output_path_ + "/best_config.json";
        std::ofstream best_file(best_config_path);
        if (!best_file.is_open()) {
            std::cerr << "Failed to open best config file for writing: " << best_config_path << std::endl;
            return false;
        }

        best_file << best_config.dump(2);
        best_file.close();

        std::cout << "Best configuration saved to: " << best_config_path << std::endl;
        return true;
    } catch (const std::exception& e) {
        std::cerr << "Error generating best config: " << e.what() << std::endl;
        return false;
    }
}

bool result_reporter::generate_sensitivity_analysis(const std::vector<optimization_result>& results) {
    try {
        // 获取所有参数名称
        std::unordered_set<std::string> param_names;
        for (const auto& result : results) {
            for (const auto& [name, _] : result.parameters) {
                param_names.insert(name);
            }
        }

        // 对每个参数进行敏感性分析
        nlohmann::json sensitivity_json = nlohmann::json::object();

        for (const auto& param_name : param_names) {
            // 收集该参数的所有值和对应的得分
            std::map<std::string, std::vector<double>> param_scores;

            for (const auto& result : results) {
                auto it = result.parameters.find(param_name);
                if (it == result.parameters.end()) {
                    continue;
                }

                std::string param_value_str;
                if (std::holds_alternative<int>(it->second)) {
                    param_value_str = std::to_string(std::get<int>(it->second));
                } else if (std::holds_alternative<double>(it->second)) {
                    param_value_str = std::to_string(std::get<double>(it->second));
                } else if (std::holds_alternative<bool>(it->second)) {
                    param_value_str = std::get<bool>(it->second) ? "true" : "false";
                } else if (std::holds_alternative<std::string>(it->second)) {
                    param_value_str = std::get<std::string>(it->second);
                }

                param_scores[param_value_str].push_back(result.score);
            }

            // 计算每个参数值的平均得分
            nlohmann::json param_json = nlohmann::json::array();

            for (const auto& [value_str, scores] : param_scores) {
                double avg_score = 0.0;
                if (!scores.empty()) {
                    avg_score = std::accumulate(scores.begin(), scores.end(), 0.0) / scores.size();
                }

                nlohmann::json point;
                point["value"] = value_str;
                point["score"] = avg_score;
                point["count"] = scores.size();

                param_json.push_back(point);
            }

            sensitivity_json[param_name] = param_json;
        }

        // 保存敏感性分析结果
        std::string sensitivity_path = output_path_ + "/sensitivity_analysis.json";
        std::ofstream sensitivity_file(sensitivity_path);
        if (!sensitivity_file.is_open()) {
            std::cerr << "Failed to open sensitivity analysis file for writing: " << sensitivity_path << std::endl;
            return false;
        }

        sensitivity_file << sensitivity_json.dump(2);
        sensitivity_file.close();

        std::cout << "Sensitivity analysis saved to: " << sensitivity_path << std::endl;
        return true;
    } catch (const std::exception& e) {
        std::cerr << "Error generating sensitivity analysis: " << e.what() << std::endl;
        return false;
    }
}

bool result_reporter::generate_csv_report(const std::vector<optimization_result>& results) {
    try {
        // 首先过滤出满足约束条件的有效结果
        std::vector<optimization_result> filtered_results;

        // 用于去重的集合，存储参数的字符串表示
        std::unordered_set<std::string> unique_params;

        for (const auto& result : results) {
            if (check_result_constraints(result, constraints_)) {
                // 将参数转换为字符串，用于去重
                std::string param_str = params_to_string(result.parameters);

                // 如果这个参数组合还没有出现过，添加到结果中
                if (unique_params.find(param_str) == unique_params.end()) {
                    unique_params.insert(param_str);
                    filtered_results.push_back(result);
                }
            }
        }

        // 如果没有满足约束条件的结果，输出警告
        if (filtered_results.empty()) {
            std::cerr << "Warning: No results satisfy the constraints!" << std::endl;
        }

        // 获取所有参数名称和指标名称
        std::vector<std::string> param_names;
        std::vector<std::string> metric_names;

        for (const auto& result : filtered_results) {
            for (const auto& [name, _] : result.parameters) {
                // 检查是否已经存在
                if (std::find(param_names.begin(), param_names.end(), name) == param_names.end()) {
                    param_names.push_back(name);
                }
            }

            for (const auto& [name, _] : result.metrics) {
                // 检查是否已经存在
                if (std::find(metric_names.begin(), metric_names.end(), name) == metric_names.end()) {
                    metric_names.push_back(name);
                }
            }
        }

        // 创建CSV文件
        std::string csv_path = output_path_ + "/results.csv";
        std::ofstream csv_file(csv_path);
        if (!csv_file.is_open()) {
            std::cerr << "Failed to open CSV file for writing: " << csv_path << std::endl;
            return false;
        }

        // 写入标题行
        csv_file << "score";

        for (const auto& param_name : param_names) {
            csv_file << "," << param_name;
        }

        for (const auto& metric_name : metric_names) {
            csv_file << "," << metric_name;
        }

        csv_file << std::endl;

        // 写入数据行
        for (const auto& result : filtered_results) {
            csv_file << result.score;

            for (const auto& param_name : param_names) {
                csv_file << ",";

                auto it = result.parameters.find(param_name);
                if (it != result.parameters.end()) {
                    if (std::holds_alternative<int>(it->second)) {
                        csv_file << std::get<int>(it->second);
                    } else if (std::holds_alternative<double>(it->second)) {
                        csv_file << std::get<double>(it->second);
                    } else if (std::holds_alternative<bool>(it->second)) {
                        csv_file << (std::get<bool>(it->second) ? "true" : "false");
                    } else if (std::holds_alternative<std::string>(it->second)) {
                        csv_file << std::get<std::string>(it->second);
                    }
                }
            }

            for (const auto& metric_name : metric_names) {
                csv_file << ",";

                auto it = result.metrics.find(metric_name);
                if (it != result.metrics.end()) {
                    csv_file << it->second;
                }
            }

            csv_file << std::endl;
        }

        csv_file.close();

        std::cout << "CSV report saved to: " << csv_path << std::endl;
        return true;
    } catch (const std::exception& e) {
        std::cerr << "Error generating CSV report: " << e.what() << std::endl;
        return false;
    }
}

bool result_reporter::generate_json_report(const std::vector<optimization_result>& results) {
    try {
        // 创建JSON对象
        nlohmann::json report_json;

        // 添加约束条件信息
        if (!constraints_.empty()) {
            nlohmann::json constraints_json = nlohmann::json::array();
            for (const auto& c : constraints_) {
                constraints_json.push_back(constraint_to_json(c));
            }
            report_json["constraints"] = constraints_json;
        }

        // 首先过滤出满足约束条件的有效结果
        std::vector<optimization_result> filtered_results;

        // 用于去重的集合，存储参数的字符串表示
        std::unordered_set<std::string> unique_params;

        for (const auto& result : results) {
            if (check_result_constraints(result, constraints_)) {
                // 将参数转换为字符串，用于去重
                std::string param_str = params_to_string(result.parameters);

                // 如果这个参数组合还没有出现过，添加到结果中
                if (unique_params.find(param_str) == unique_params.end()) {
                    unique_params.insert(param_str);
                    filtered_results.push_back(result);
                }
            }
        }

        // 如果没有满足约束条件的结果，输出警告
        if (filtered_results.empty()) {
            std::cerr << "Warning: No results satisfy the constraints!" << std::endl;
        }

        // 创建结果数组
        nlohmann::json results_json = nlohmann::json::array();

        for (const auto& result : filtered_results) {
            nlohmann::json result_json;
            result_json["score"] = result.score;

            nlohmann::json params_json = nlohmann::json::object();
            for (const auto& [name, value] : result.parameters) {
                if (std::holds_alternative<int>(value)) {
                    params_json[name] = std::get<int>(value);
                } else if (std::holds_alternative<double>(value)) {
                    params_json[name] = std::get<double>(value);
                } else if (std::holds_alternative<bool>(value)) {
                    params_json[name] = std::get<bool>(value);
                } else if (std::holds_alternative<std::string>(value)) {
                    params_json[name] = std::get<std::string>(value);
                }
            }
            result_json["parameters"] = params_json;

            nlohmann::json metrics_json = nlohmann::json::object();
            for (const auto& [name, value] : result.metrics) {
                metrics_json[name] = value;
            }
            result_json["metrics"] = metrics_json;

            if (!result.config_path.empty()) {
                result_json["config_path"] = result.config_path;
            }

            results_json.push_back(result_json);
        }

        // 将结果数组添加到报告对象
        report_json["results"] = results_json;

        // 保存JSON文件
        std::string json_path = output_path_ + "/results.json";
        std::ofstream json_file(json_path);
        if (!json_file.is_open()) {
            std::cerr << "Failed to open JSON file for writing: " << json_path << std::endl;
            return false;
        }

        json_file << report_json.dump(2);
        json_file.close();

        std::cout << "JSON report saved to: " << json_path << std::endl;
        return true;
    } catch (const std::exception& e) {
        std::cerr << "Error generating JSON report: " << e.what() << std::endl;
        return false;
    }
}

bool result_reporter::generate_html_report(const std::vector<optimization_result>& results) {
    try {
        // 创建HTML文件
        std::string html_path = output_path_ + "/results.html";
        std::ofstream html_file(html_path);
        if (!html_file.is_open()) {
            std::cerr << "Failed to open HTML file for writing: " << html_path << std::endl;
            return false;
        }

        // 写入HTML头部
        html_file << "<!DOCTYPE html>\n"
                  << "<html>\n"
                  << "<head>\n"
                  << "  <title>Parameter Optimization Results</title>\n"
                  << "  <style>\n"
                  << "    body { font-family: Arial, sans-serif; margin: 20px; }\n"
                  << "    h1 { color: #333; }\n"
                  << "    table { border-collapse: collapse; width: 100%; }\n"
                  << "    th, td { border: 1px solid #ddd; padding: 8px; text-align: left; }\n"
                  << "    th { background-color: #f2f2f2; }\n"
                  << "    tr:nth-child(even) { background-color: #f9f9f9; }\n"
                  << "    tr:hover { background-color: #f5f5f5; }\n"
                  << "    .constraints { margin-bottom: 20px; padding: 10px; background-color: #f8f9fa; border: 1px solid #ddd; }\n"
                  << "  </style>\n"
                  << "</head>\n"
                  << "<body>\n"
                  << "  <h1>Parameter Optimization Results</h1>\n";

        // 首先过滤出满足约束条件的有效结果
        std::vector<optimization_result> filtered_results;

        // 用于去重的集合，存储参数的字符串表示
        std::unordered_set<std::string> unique_params;

        for (const auto& result : results) {
            if (check_result_constraints(result, constraints_)) {
                // 将参数转换为字符串，用于去重
                std::string param_str = params_to_string(result.parameters);

                // 如果这个参数组合还没有出现过，添加到结果中
                if (unique_params.find(param_str) == unique_params.end()) {
                    unique_params.insert(param_str);
                    filtered_results.push_back(result);
                }
            }
        }

        // 如果没有满足约束条件的结果，输出警告
        if (filtered_results.empty()) {
            std::cerr << "Warning: No results satisfy the constraints!" << std::endl;
            html_file << "  <div style=\"color: red; font-weight: bold; margin: 20px 0;\">\n"
                      << "    No results satisfy the constraints!\n"
                      << "  </div>\n";
        }

        // 获取所有参数名称和指标名称
        std::unordered_set<std::string> param_names;
        std::unordered_set<std::string> metric_names;

        for (const auto& result : filtered_results) {
            for (const auto& [name, _] : result.parameters) {
                param_names.insert(name);
            }

            for (const auto& [name, _] : result.metrics) {
                metric_names.insert(name);
            }
        }

        // 添加约束条件信息
        if (!filtered_results.empty() && !filtered_results[0].metrics.empty() && !constraints_.empty()) {
            html_file << "  <div class=\"constraints\">\n"
                      << "    <h2>Constraints</h2>\n"
                      << "    <ul>\n";

            for (const auto& c : constraints_) {
                html_file << constraint_to_html(c);
            }

            html_file << "    </ul>\n"
                      << "  </div>\n";
        }

        // 写入结果表格
        html_file << "  <h2>Results</h2>\n"
                  << "  <table>\n"
                  << "    <tr>\n"
                  << "      <th>Rank</th>\n"
                  << "      <th>Score</th>\n";

        for (const auto& param_name : param_names) {
            html_file << "      <th>" << param_name << "</th>\n";
        }

        for (const auto& metric_name : metric_names) {
            html_file << "      <th>" << metric_name << "</th>\n";
        }

        html_file << "    </tr>\n";

        // 对结果按得分排序
        std::vector<const optimization_result*> sorted_results;
        for (const auto& result : filtered_results) {
            sorted_results.push_back(&result);
        }

        std::sort(sorted_results.begin(), sorted_results.end(),
                 [](const optimization_result* a, const optimization_result* b) {
                     return a->score > b->score;
                 });

        // 写入数据行
        for (size_t i = 0; i < sorted_results.size(); ++i) {
            const auto& result = *sorted_results[i];

            html_file << "    <tr>\n"
                      << "      <td>" << (i + 1) << "</td>\n"
                      << "      <td>" << result.score << "</td>\n";

            for (const auto& param_name : param_names) {
                html_file << "      <td>";

                auto it = result.parameters.find(param_name);
                if (it != result.parameters.end()) {
                    if (std::holds_alternative<int>(it->second)) {
                        html_file << std::get<int>(it->second);
                    } else if (std::holds_alternative<double>(it->second)) {
                        html_file << std::get<double>(it->second);
                    } else if (std::holds_alternative<bool>(it->second)) {
                        html_file << (std::get<bool>(it->second) ? "true" : "false");
                    } else if (std::holds_alternative<std::string>(it->second)) {
                        html_file << std::get<std::string>(it->second);
                    }
                }

                html_file << "</td>\n";
            }

            for (const auto& metric_name : metric_names) {
                html_file << "      <td>";

                auto it = result.metrics.find(metric_name);
                if (it != result.metrics.end()) {
                    html_file << it->second;
                }

                html_file << "</td>\n";
            }

            html_file << "    </tr>\n";
        }

        html_file << "  </table>\n";

        // 写入HTML尾部
        html_file << "</body>\n"
                  << "</html>\n";

        html_file.close();

        std::cout << "HTML report saved to: " << html_path << std::endl;
        return true;
    } catch (const std::exception& e) {
        std::cerr << "Error generating HTML report: " << e.what() << std::endl;
        return false;
    }
}

} // namespace cpp_backtest
} // namespace fast_trader_elite
