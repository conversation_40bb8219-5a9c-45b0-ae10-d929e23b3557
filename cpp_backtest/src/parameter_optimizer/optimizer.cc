#include <fast_trader_elite/cpp_backtest/parameter_optimizer/optimizer.h>
#include <algorithm>
#include <fstream>
#include <iostream>
#include <nlohmann_json/json.hpp>

namespace fast_trader_elite {
namespace cpp_backtest {

optimizer::optimizer(const parameter_space& space,
                   const std::shared_ptr<optimization_target>& target)
    : space_(space)
    , target_(target)
    , max_iterations_(1000)
    , parallel_jobs_(1) {
}

void optimizer::set_evaluation_callback(
    std::function<optimization_result(const std::unordered_map<std::string, parameter_value>&)> callback) {
    evaluation_callback_ = callback;
}

void optimizer::set_progress_callback(
    std::function<void(int current, int total, const optimization_result&)> callback) {
    progress_callback_ = callback;
}

void optimizer::set_max_iterations(int max_iterations) {
    max_iterations_ = max_iterations;
}

void optimizer::set_parallel_jobs(int parallel_jobs) {
    parallel_jobs_ = parallel_jobs;
}

optimization_result optimizer::get_best_result() const {
    if (results_.empty()) {
        return optimization_result{};
    }

    auto best_it = std::max_element(results_.begin(), results_.end(),
        [](const optimization_result& a, const optimization_result& b) {
            return a.score < b.score;
        });

    return *best_it;
}

const std::vector<optimization_result>& optimizer::get_all_results() const {
    return results_;
}

bool optimizer::save_results(const std::string& file_path) const {
    try {
        nlohmann::json results_json = nlohmann::json::array();

        for (const auto& result : results_) {
            if (!result.valid) {
                continue;
            }

            nlohmann::json result_json;
            result_json["score"] = result.score;

            nlohmann::json params_json = nlohmann::json::object();
            for (const auto& [name, value] : result.parameters) {
                if (std::holds_alternative<int>(value)) {
                    params_json[name] = std::get<int>(value);
                } else if (std::holds_alternative<double>(value)) {
                    params_json[name] = std::get<double>(value);
                } else if (std::holds_alternative<bool>(value)) {
                    params_json[name] = std::get<bool>(value);
                } else if (std::holds_alternative<std::string>(value)) {
                    params_json[name] = std::get<std::string>(value);
                }
            }
            result_json["parameters"] = params_json;

            nlohmann::json metrics_json = nlohmann::json::object();
            for (const auto& [name, value] : result.metrics) {
                metrics_json[name] = value;
            }
            result_json["metrics"] = metrics_json;

            if (!result.config_path.empty()) {
                result_json["config_path"] = result.config_path;
            }

            results_json.push_back(result_json);
        }

        std::ofstream file(file_path);
        if (!file.is_open()) {
            std::cerr << "Failed to open file for writing: " << file_path << std::endl;
            return false;
        }

        file << results_json.dump(2);
        file.close();

        return true;
    } catch (const std::exception& e) {
        std::cerr << "Error saving results: " << e.what() << std::endl;
        return false;
    }
}

bool optimizer::load_results(const std::string& file_path) {
    try {
        std::ifstream file(file_path);
        if (!file.is_open()) {
            std::cerr << "Failed to open file for reading: " << file_path << std::endl;
            return false;
        }

        nlohmann::json results_json;
        file >> results_json;
        file.close();

        if (!results_json.is_array()) {
            std::cerr << "Invalid results file format" << std::endl;
            return false;
        }

        results_.clear();

        for (const auto& result_json : results_json) {
            optimization_result result;

            if (result_json.contains("score") && result_json["score"].is_number()) {
                result.score = result_json["score"].get<double>();
            }

            if (result_json.contains("parameters") && result_json["parameters"].is_object()) {
                for (auto it = result_json["parameters"].begin(); it != result_json["parameters"].end(); ++it) {
                    const std::string& name = it.key();

                    if (it.value().is_number_integer()) {
                        result.parameters[name] = it.value().get<int>();
                    } else if (it.value().is_number_float()) {
                        result.parameters[name] = it.value().get<double>();
                    } else if (it.value().is_boolean()) {
                        result.parameters[name] = it.value().get<bool>();
                    } else if (it.value().is_string()) {
                        result.parameters[name] = it.value().get<std::string>();
                    }
                }
            }

            if (result_json.contains("metrics") && result_json["metrics"].is_object()) {
                for (auto it = result_json["metrics"].begin(); it != result_json["metrics"].end(); ++it) {
                    const std::string& name = it.key();

                    if (it.value().is_number()) {
                        result.metrics[name] = it.value().get<double>();
                    }
                }
            }

            if (result_json.contains("config_path") && result_json["config_path"].is_string()) {
                result.config_path = result_json["config_path"].get<std::string>();
            }

            result.valid = true;
            results_.push_back(result);
        }

        return true;
    } catch (const std::exception& e) {
        std::cerr << "Error loading results: " << e.what() << std::endl;
        return false;
    }
}

optimization_result optimizer::evaluate(const std::unordered_map<std::string, parameter_value>& params) {
    if (!evaluation_callback_) {
        std::cerr << "No evaluation callback set" << std::endl;
        return optimization_result{};
    }

    return evaluation_callback_(params);
}

} // namespace cpp_backtest
} // namespace fast_trader_elite
