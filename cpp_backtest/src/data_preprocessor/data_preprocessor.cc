#include <algorithm>
#include <cstring>
#include <fast_trader_elite/cpp_backtest/data_preprocessor/bybit_preprocessor.h>
#include <fast_trader_elite/cpp_backtest/data_preprocessor/data_preprocessor.h>
#include <fast_trader_elite/cpp_backtest/data_preprocessor/exchange_preprocessor.h>
#include <fast_trader_elite/cpp_backtest/data_preprocessor/shm_tar_preprocessor.h>
#include <fcntl.h>
#include <fstream>
#include <iostream>
#include <limits>
#include <sys/mman.h>
#include <sys/stat.h>
#include <unistd.h>

namespace fast_trader_elite {
namespace cpp_backtest {

data_preprocessor::data_preprocessor()
    : exchange_type_(exchange_type::UNKNOWN),
      base_latency_(1000000) { // 默认延迟1毫秒
}

data_preprocessor::~data_preprocessor() { clear(); }

bool data_preprocessor::init_from_config(const std::string &config_path) {
  try {
    // 读取配置文件
    std::ifstream config_file(config_path);
    if (!config_file.is_open()) {
      std::cerr << "Failed to open config file: " << config_path << std::endl;
      return false;
    }

    // 解析JSON配置
    auto config = nlohmann::json::parse(config_file);

    // 获取交易所类型
    if (config.contains("exchange")) {
      std::string exchange_str = config["exchange"].get<std::string>();
      if (exchange_str == "bybit") {
        set_exchange_type(exchange_type::BYBIT);
      } else if (exchange_str == "binance") {
        set_exchange_type(exchange_type::BINANCE);
      } else if (exchange_str == "shm_tar") {
        set_exchange_type(exchange_type::SHM_TAR);
      } else {
        std::cerr << "Unknown exchange type: " << exchange_str << std::endl;
        return false;
      }
    } else {
      // 默认使用Bybit预处理器
      set_exchange_type(exchange_type::BYBIT);
    }

    // 使用预处理器接口解析配置
    if (!preprocessor_ || !preprocessor_->init_from_config(config_path)) {
      std::cerr << "Failed to initialize from config: " << config_path
                << std::endl;
      return false;
    }

    // 获取品种信息
    instrument_ = preprocessor_->get_instrument();
    instrument_symbol_ = preprocessor_->get_instrument_symbol();

    if (instrument_symbol_.empty()) {
      std::cerr << "No instrument found in config" << std::endl;
      return false;
    }

    return true;
  } catch (const std::exception &e) {
    std::cerr << "Exception in init_from_config: " << e.what() << std::endl;
    return false;
  }
}

void data_preprocessor::set_exchange_type(exchange_type exchange) {
  exchange_type_ = exchange;
  preprocessor_ = create_exchange_preprocessor(exchange);
}

void data_preprocessor::set_instrument_info(const std::string &symbol,
                                            double tick_size,
                                            double step_size) {
  instrument_symbol_ = symbol;

  // 设置品种信息
  std::strncpy(instrument_.instrument_id, symbol.c_str(),
               sizeof(instrument_.instrument_id) - 1);
  instrument_.instrument_idx = 0; // 只有一个品种，索引为0
  instrument_.exchange_id = static_cast<uint8_t>(exchange_type_);
  instrument_.tick_size = tick_size;
  instrument_.step_size = step_size;
}

bool data_preprocessor::read_depth_data(const std::string &filename) {
  if (!preprocessor_) {
    std::cerr << "Error: Exchange preprocessor not initialized" << std::endl;
    return false;
  }
  depth_data_path_ = filename;
  return preprocessor_->read_depth_data(filename);
}

bool data_preprocessor::read_transaction_data(const std::string &filename) {
  // 确保交易所预处理器存在
  if (!preprocessor_) {
    std::cerr << "Error: Exchange preprocessor not initialized" << std::endl;
    return false;
  }
  transaction_data_path_ = filename;
  return preprocessor_->read_transaction_data(filename);
}

bool data_preprocessor::read_kline_data(const std::string &filename) {
  // 确保交易所预处理器存在
  if (!preprocessor_) {
    std::cerr << "Error: Exchange preprocessor not initialized" << std::endl;
    return false;
  }
  kline_data_path_ = filename;
  return preprocessor_->read_kline_data(filename);
}

void data_preprocessor::set_base_latency(int64_t latency) {
  base_latency_ = latency;
}

bool data_preprocessor::process_data() {
  // 清空之前的记录
  records_.clear();

  // 确保预处理器已初始化
  if (!preprocessor_) {
    std::cerr << "Error: Exchange preprocessor not initialized" << std::endl;
    return false;
  }

  // 处理深度行情数据
  const auto &depth_data = preprocessor_->get_depth_data();

  // 预分配空间以提高性能
  records_.reserve(depth_data.size());

  for (const auto &depth : depth_data) {
    // 创建记录
    auto record = create_record(DEPTH_MARKET_DATA,
                                0, // 标志将在correct_event_order中设置
                                depth.exchange_timestamp, depth.local_timestamp,
                                &depth, sizeof(depth));

    records_.push_back(record);
  }

  // 处理成交数据
  const auto &transaction_data = preprocessor_->get_transaction_data();

  // 预分配空间以提高性能
  records_.reserve(records_.size() + transaction_data.size());

  for (const auto &trans : transaction_data) {
    // 创建记录
    auto record = create_record(TRANSACTION_DATA,
                                0, // 标志将在correct_event_order中设置
                                trans.exchange_timestamp, trans.local_timestamp,
                                &trans, sizeof(trans));

    records_.push_back(record);
  }

  // 处理K线数据
  const auto &kline_data = preprocessor_->get_kline_data();

  // 预分配空间以提高性能
  records_.reserve(records_.size() + kline_data.size());

  for (const auto &kline : kline_data) {
    // 创建记录
    auto record = create_record(KLINE_DATA,
                                0, // 标志将在correct_event_order中设置
                                kline.exchange_timestamp, kline.local_timestamp,
                                &kline, sizeof(kline));

    records_.push_back(record);
  }

  // 校正本地时间戳
  correct_local_timestamp();

  // 校正事件顺序
  correct_event_order();

  // 验证事件顺序
  if (!validate_event_order()) {
    std::cerr << "Event order validation failed" << std::endl;
    return false;
  }

  std::cout << "Processed " << records_.size()
            << " records for instrument: " << instrument_.instrument_id
            << std::endl;
  return true;
}

bool data_preprocessor::write_binary_file(const std::string &filename) {
  // 计算文件总大小
  size_t total_size = sizeof(file_header) + sizeof(instrument_info);
  for (const auto &record : records_) {
    total_size += sizeof(record_header) + record.data.size();
  }

  // 创建文件
  int fd = open(filename.c_str(), O_RDWR | O_CREAT | O_TRUNC, 0644);
  if (fd == -1) {
    std::cerr << "Failed to create file: " << filename << std::endl;
    return false;
  }

  // 设置文件大小
  if (ftruncate(fd, total_size) == -1) {
    std::cerr << "Failed to set file size" << std::endl;
    close(fd);
    return false;
  }

  // 映射文件到内存
  char* data = static_cast<char*>(mmap(nullptr, total_size, PROT_READ | PROT_WRITE, MAP_SHARED, fd, 0));
  if (data == MAP_FAILED) {
    std::cerr << "Failed to mmap file for writing" << std::endl;
    close(fd);
    return false;
  }

  // 准备文件头
  file_header header;
  std::memset(&header, 0, sizeof(header));
  std::strcpy(header.magic, "FTEBACK");
  header.version = 1;
  header.record_count = static_cast<uint32_t>(records_.size());
  header.instrument_count = 1; // 只有一个品种

  // 计算开始和结束时间
  if (!records_.empty()) {
    header.start_time = std::numeric_limits<int64_t>::max();
    header.end_time = std::numeric_limits<int64_t>::min();

    for (const auto &record : records_) {
      header.start_time =
          std::min(header.start_time, record.header.exch_timestamp);
      header.end_time = std::max(header.end_time, record.header.exch_timestamp);
    }
  }

  // 写入数据到内存映射区域
  char* ptr = data;

  // 写入文件头
  std::memcpy(ptr, &header, sizeof(header));
  ptr += sizeof(header);

  // 写入品种信息
  std::memcpy(ptr, &instrument_, sizeof(instrument_info));
  ptr += sizeof(instrument_info);

  // 写入记录
  for (const auto &record : records_) {
    std::memcpy(ptr, &record.header, sizeof(record_header));
    ptr += sizeof(record_header);
    std::memcpy(ptr, record.data.data(), record.data.size());
    ptr += record.data.size();
  }

  // 确保数据写入磁盘
  if (msync(data, total_size, MS_SYNC) == -1) {
    std::cerr << "Failed to sync mmap data to disk" << std::endl;
  }

  // 解除映射并关闭文件
  munmap(data, total_size);
  close(fd);

  std::cout << "Wrote " << records_.size() << " records to " << filename
            << " using memory mapping" << std::endl;
  return true;
}

size_t data_preprocessor::get_record_count() const { return records_.size(); }

const instrument_info &data_preprocessor::get_instrument() const {
  return instrument_;
}

void data_preprocessor::clear() {
  records_.clear();
  // 重置品种信息
  std::memset(&instrument_, 0, sizeof(instrument_));
  instrument_symbol_.clear();
  depth_data_path_.clear();
  transaction_data_path_.clear();
  kline_data_path_.clear();
  // 不清除preprocessor_，因为它可能会被重用
}

void data_preprocessor::correct_local_timestamp() {
  // 找到最小延迟
  int64_t min_latency = std::numeric_limits<int64_t>::max();
  for (const auto &record : records_) {
    int64_t latency =
        record.header.local_timestamp - record.header.exch_timestamp;
    min_latency = std::min(min_latency, latency);
  }

  // 如果有负延迟，调整本地时间戳
  if (min_latency < 0) {
    int64_t local_timestamp_offset = -min_latency + base_latency_;
    std::cout << "local_timestamp is ahead of exch_timestamp by "
              << -min_latency << std::endl;
    for (auto &record : records_) {
      record.header.local_timestamp += local_timestamp_offset;
    }
  } else if (min_latency == 0) {
    // 如果最小延迟为0，添加基础延迟
    for (auto &record : records_) {
      record.header.local_timestamp += base_latency_;
    }
  }
}

void data_preprocessor::correct_event_order() {
  if (records_.empty()) {
    return;
  }

  // 按交易所时间戳排序
  std::vector<size_t> sorted_exch_index(records_.size());
  for (size_t i = 0; i < records_.size(); i++) {
    sorted_exch_index[i] = i;
  }
  std::sort(sorted_exch_index.begin(), sorted_exch_index.end(),
            [this](size_t a, size_t b) {
              return records_[a].header.exch_timestamp <
                     records_[b].header.exch_timestamp;
            });

  // 按本地时间戳排序
  std::vector<size_t> sorted_local_index(records_.size());
  for (size_t i = 0; i < records_.size(); i++) {
    sorted_local_index[i] = i;
  }
  std::sort(sorted_local_index.begin(), sorted_local_index.end(),
            [this](size_t a, size_t b) {
              return records_[a].header.local_timestamp <
                     records_[b].header.local_timestamp;
            });

  // 创建新的记录数组
  // 注意：我们不预先分配大小，而是让它根据需要动态增长
  std::vector<record<void>> sorted_final;

  size_t exch_idx = 0;
  size_t local_idx = 0;

  while (exch_idx < records_.size() || local_idx < records_.size()) {
    if (exch_idx < records_.size() && local_idx < records_.size()) {
      const auto &exch_record = records_[sorted_exch_index[exch_idx]];
      const auto &local_record = records_[sorted_local_index[local_idx]];

      // 如果交易所时间戳和本地时间戳都相同
      if (exch_record.header.exch_timestamp ==
              local_record.header.exch_timestamp &&
          exch_record.header.local_timestamp ==
              local_record.header.local_timestamp) {

        // 创建新记录，设置两个标志
        record<void> new_record = exch_record;
        new_record.header.flags |= (EXCH_EVENT | LOCAL_EVENT);
        sorted_final.push_back(new_record);

        exch_idx++;
        local_idx++;
      }
      // 如果交易所时间戳相同但本地时间戳更小，或者交易所时间戳更小
      else if ((exch_record.header.exch_timestamp ==
                    local_record.header.exch_timestamp &&
                exch_record.header.local_timestamp <
                    local_record.header.local_timestamp) ||
               (exch_record.header.exch_timestamp <
                local_record.header.exch_timestamp)) {
        // 创建新记录，只设置交易所标志
        record<void> new_record = exch_record;
        new_record.header.flags |= EXCH_EVENT;
        sorted_final.push_back(new_record);

        exch_idx++;
      }
      // 如果交易所时间戳相同但本地时间戳更大，或者还有本地事件
      else if ((exch_record.header.exch_timestamp ==
                    local_record.header.exch_timestamp &&
                exch_record.header.local_timestamp >
                    local_record.header.local_timestamp) ||
               (local_idx < records_.size())) {
        // 创建新记录，只设置本地标志
        record<void> new_record = local_record;
        new_record.header.flags |= LOCAL_EVENT;
        sorted_final.push_back(new_record);

        local_idx++;
      }
    }
    // 如果还有交易所事件未处理
    else if (exch_idx < records_.size()) {
      const auto &exch_record = records_[sorted_exch_index[exch_idx]];

      // 创建新记录，只设置交易所标志
      record<void> new_record = exch_record;
      new_record.header.flags |= EXCH_EVENT;
      sorted_final.push_back(new_record);

      exch_idx++;
    }
    // 如果还有本地事件未处理
    else if (local_idx < records_.size()) {
      const auto &local_record = records_[sorted_local_index[local_idx]];

      // 创建新记录，只设置本地标志
      record<void> new_record = local_record;
      new_record.header.flags |= LOCAL_EVENT;
      sorted_final.push_back(new_record);

      local_idx++;
    }
  }
  std::cout << "records size:" << records_.size()
            << " sorted_final:" << sorted_final.size() << std::endl;

  // 替换原来的记录数组
  records_ = std::move(sorted_final);

  // // 按本地时间戳排序
  // std::sort(records_.begin(), records_.end(), [](const record<void>& a, const
  // record<void>& b) {
  //     return a.header.local_timestamp < b.header.local_timestamp;
  // });
}

bool data_preprocessor::validate_event_order() const {
  // 验证交易所事件顺序
  int64_t last_exch_timestamp = 0;
  bool first_exch = true;

  // 验证本地事件顺序
  int64_t last_local_timestamp = 0;
  bool first_local = true;

  for (const auto &record : records_) {
    // 检查交易所事件
    if (record.header.flags & EXCH_EVENT) {
      if (!first_exch && record.header.exch_timestamp < last_exch_timestamp) {
        std::cerr << "Exchange events are out of order" << std::endl;
        return false;
      }
      first_exch = false;
      last_exch_timestamp = record.header.exch_timestamp;
    }

    // 检查本地事件
    if (record.header.flags & LOCAL_EVENT) {
      if (!first_local &&
          record.header.local_timestamp < last_local_timestamp) {
        std::cerr << "Local events are out of order" << std::endl;
        return false;
      }
      first_local = false;
      last_local_timestamp = record.header.local_timestamp;
    }
  }

  return true;
}

// 私有方法，不再需要

data_preprocessor::record<void> data_preprocessor::create_record(
    uint8_t type, uint8_t flags, int64_t exch_timestamp,
    int64_t local_timestamp, const void *data, size_t data_size) {
  record<void> rec;

  // 设置记录头
  rec.header.record_type = type;
  rec.header.flags = flags;
  rec.header.instrument_idx = 0; // 只有一个品种，索引始终为0
  rec.header.exch_timestamp = exch_timestamp;
  rec.header.local_timestamp = local_timestamp;
  rec.header.data_size = static_cast<uint32_t>(data_size);

  // 复制数据
  rec.data.resize(data_size);
  std::memcpy(rec.data.data(), data, data_size);

  return rec;
}

} // namespace cpp_backtest
} // namespace fast_trader_elite
