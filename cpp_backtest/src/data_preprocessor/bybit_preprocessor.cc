#include <fast_trader_elite/cpp_backtest/data_preprocessor/bybit_preprocessor.h>
#include <iostream>
#include <fstream>
#include <sstream>
#include <algorithm>
#include <cmath>
#include <ctime>
#include <iomanip>
#include <simdjson.h>
#include <nlohmann_json/json.hpp>

namespace fast_trader_elite {
namespace cpp_backtest {

bybit_preprocessor::bybit_preprocessor()
    : next_instrument_idx_(0) {
    // 初始化订单簿，设置价格精度为0.000001
    orderbook_.init(0.000001, 5);
}

bybit_preprocessor::~bybit_preprocessor() {
    clear();
}

bool bybit_preprocessor::init_from_config(const std::string& config_path) {
    try {
        // 打开配置文件
        std::ifstream file(config_path);
        if (!file.is_open()) {
            std::cerr << "Failed to open config file: " << config_path << std::endl;
            return false;
        }

        // 解析JSON
        file >> config_;
        file.close();

        // 解析配置
        return parse_config(config_);
    } catch (const std::exception& e) {
        std::cerr << "Error loading config: " << e.what() << std::endl;
        return false;
    }
}

bool bybit_preprocessor::parse_config(const nlohmann::json& config) {
    try {
        // 清除现有数据
        clear();

        // 获取数据文件路径
        if (config.contains("depth_data_path")) {
            depth_data_path_ = config["depth_data_path"].get<std::string>();
        }

        if (config.contains("transaction_data_path")) {
            transaction_data_path_ = config["transaction_data_path"].get<std::string>();
        }

        if (config.contains("kline_data_path")) {
            kline_data_path_ = config["kline_data_path"].get<std::string>();
        }

        // 获取合约信息
        if (config.contains("instrument")) {
            const auto& instrument_config = config["instrument"];
            std::string symbol = instrument_config["symbol"].get<std::string>();
            add_instrument(symbol, instrument_config);

            // 设置订单簿价格精度
            double price_tick = 0.000001; // 默认值
            if (instrument_config.contains("price_tick")) {
                price_tick = instrument_config["price_tick"].get<double>();
            }
            int depth_level = 5; // 默认值
            if (instrument_config.contains("depth_level")) {
                depth_level = instrument_config["depth_level"].get<int>();
            }
            orderbook_.init(price_tick, depth_level);
        }

        // 如果配置了数据文件路径，自动读取数据
        if (!depth_data_path_.empty()) {
            if (!read_depth_data(depth_data_path_)) {
                std::cerr << "Failed to read depth data from " << depth_data_path_ << std::endl;
                return false;
            }
        }

        if (!transaction_data_path_.empty()) {
            if (!read_transaction_data(transaction_data_path_)) {
                std::cerr << "Failed to read transaction data from " << transaction_data_path_ << std::endl;
                return false;
            }
        }

        if (!kline_data_path_.empty()) {
            if (!read_kline_data(kline_data_path_)) {
                std::cerr << "Failed to read kline data from " << kline_data_path_ << std::endl;
                return false;
            }
        }

        return true;
    } catch (const std::exception& e) {
        std::cerr << "Error parsing config: " << e.what() << std::endl;
        return false;
    }
}

bool bybit_preprocessor::read_depth_data(const std::string& filename) {
    std::cout << "Reading Bybit depth data from: " << filename << std::endl;

    // 打开文件
    std::ifstream file(filename);
    if (!file.is_open()) {
        std::cerr << "Failed to open file: " << filename << std::endl;
        return false;
    }

    std::string line;
    int line_count = 0;

    while (std::getline(file, line)) {
        int64_t timestamp = 0;
        if (parse_orderbook_data(line, timestamp)) {
            line_count++;
        }
    }

    file.close();

    std::cout << "Processed " << line_count << " orderbook messages, generated "
              << depth_data_.size() << " depth records" << std::endl;
    return true;
}

bool bybit_preprocessor::read_transaction_data(const std::string& filename) {
    std::cout << "Reading Bybit transaction data from: " << filename << std::endl;

    std::ifstream file(filename);
    if (!file.is_open()) {
        std::cerr << "Failed to open file: " << filename << std::endl;
        return false;
    }

    std::string line;
    bool header_skipped = false;

    while (std::getline(file, line)) {
        // 跳过CSV头
        if (!header_skipped) {
            header_skipped = true;
            continue;
        }

        // 解析CSV行
        if (!parse_transaction_csv_line(line)) {
            std::cerr << "Failed to parse transaction line: " << line << std::endl;
        }
    }

    file.close();

    std::cout << "Processed " << transaction_data_.size() << " transaction records" << std::endl;
    return true;
}

bool bybit_preprocessor::read_kline_data(const std::string& filename) {
    std::cout << "Reading Bybit kline data from: " << filename << std::endl;

    std::ifstream file(filename);
    if (!file.is_open()) {
        std::cerr << "Failed to open file: " << filename << std::endl;
        return false;
    }

    std::string line;
    bool header_skipped = false;

    while (std::getline(file, line)) {
        // 跳过CSV头
        if (!header_skipped) {
            header_skipped = true;
            continue;
        }

        // 解析CSV行
        if (!parse_kline_csv_line(line)) {
            std::cerr << "Failed to parse kline line: " << line << std::endl;
        }
    }

    file.close();

    std::cout << "Processed " << kline_data_.size() << " kline records" << std::endl;
    return true;
}

exchange_type bybit_preprocessor::get_exchange_type() const {
    return exchange_type::BYBIT;
}

const std::vector<depth_market_data_field>& bybit_preprocessor::get_depth_data() const {
    return depth_data_;
}

const std::vector<transaction_field>& bybit_preprocessor::get_transaction_data() const {
    return transaction_data_;
}

const std::vector<kline_market_data_field>& bybit_preprocessor::get_kline_data() const {
    return kline_data_;
}

const instrument_info& bybit_preprocessor::get_instrument() const {
    return instrument_;
}

const std::string& bybit_preprocessor::get_instrument_symbol() const {
    return instrument_symbol_;
}

void bybit_preprocessor::clear() {
    depth_data_.clear();
    transaction_data_.clear();
    kline_data_.clear();
    std::memset(&instrument_, 0, sizeof(instrument_));
    instrument_symbol_.clear();
    next_instrument_idx_ = 0;
    orderbook_.clear();
}

bool bybit_preprocessor::parse_orderbook_data(const std::string& line, int64_t& timestamp) {
    try {
        // 使用simdjson解析JSON
        simdjson::padded_string padded_line(line);
        simdjson::ondemand::document doc;
        auto error = parser_.iterate(padded_line).get(doc);

        if (error) {
            std::cerr << "JSON parsing error: " << error << std::endl;
            return false;
        }

        // 获取消息类型
        std::string_view type;
        error = doc["type"].get(type);
        if (error) {
            std::cerr << "Failed to get message type" << std::endl;
            return false;
        }

        // 获取时间戳
        uint64_t ts;
        error = doc["ts"].get(ts);
        if (error) {
            std::cerr << "Failed to get timestamp" << std::endl;
            return false;
        }
        // 将毫秒转换为纳秒
        timestamp = static_cast<int64_t>(ts * 1000000);

        // 根据消息类型处理数据
        if (type == "snapshot") {
            return process_orderbook_snapshot(doc, timestamp);
        } else if (type == "delta") {
            return process_orderbook_delta(doc, timestamp);
        } else {
            std::cerr << "Unknown message type: " << type << std::endl;
            return false;
        }
    } catch (const std::exception& e) {
        std::cerr << "Error parsing orderbook data: " << e.what() << std::endl;
        return false;
    }
}

bool bybit_preprocessor::process_orderbook_snapshot(simdjson::ondemand::document& doc, int64_t timestamp) {
    try {
        // 清空订单簿
        orderbook_.clear();

        // 获取品种
        auto data = doc["data"];
        std::string_view symbol;
        auto error = data["s"].get(symbol);
        if (error) {
            std::cerr << "Failed to get symbol" << std::endl;
            return false;
        }

        // 添加品种信息
        std::string symbol_str(symbol);

        // 处理买盘
        for (auto bid_pv : data["b"]) {
            auto itr = bid_pv.get_array().begin();
            double price = (*itr).get_double_in_string();
            ++itr;
            double volume = (*itr).get_double_in_string();
            orderbook_.update_bid(price, volume);
        }

        // 处理卖盘
        for (auto ask_pv : data["a"]) {
            auto itr = ask_pv.get_array().begin();
            double price = (*itr).get_double_in_string();
            ++itr;
            double volume = (*itr).get_double_in_string();
            orderbook_.update_ask(price, volume);
        }

        // 生成深度行情数据
        generate_depth_from_orderbook(symbol_str, timestamp);

        return true;
    } catch (const std::exception& e) {
        std::cerr << "Error processing orderbook snapshot: " << e.what() << std::endl;
        return false;
    }
}

bool bybit_preprocessor::process_orderbook_delta(simdjson::ondemand::document& doc, int64_t timestamp) {
    try {
        // 获取品种
        auto data = doc["data"];
        std::string_view symbol;
        auto error = data["s"].get(symbol);
        if (error) {
            std::cerr << "Failed to get symbol" << std::endl;
            return false;
        }

        std::string symbol_str(symbol);

        // 处理买盘
        for (auto bid_pv : data["b"]) {
            auto itr = bid_pv.get_array().begin();
            double price = (*itr).get_double_in_string();
            ++itr;
            double volume = (*itr).get_double_in_string();
            orderbook_.update_bid(price, volume);
        }

        // 处理卖盘
        for (auto ask_pv : data["a"]) {
            auto itr = ask_pv.get_array().begin();
            double price = (*itr).get_double_in_string();
            ++itr;
            double volume = (*itr).get_double_in_string();
            orderbook_.update_ask(price, volume);
        }

        // 生成深度行情数据
        generate_depth_from_orderbook(symbol_str, timestamp);

        return true;
    } catch (const std::exception& e) {
        std::cerr << "Error processing orderbook delta: " << e.what() << std::endl;
        return false;
    }
}

void bybit_preprocessor::generate_depth_from_orderbook(const std::string& symbol, int64_t timestamp) {
    // 获取买卖盘数据
    const auto& bids = orderbook_.get_bids();
    const auto& asks = orderbook_.get_asks();

    // 创建深度行情数据
    depth_market_data_field depth;
    std::memset(&depth, 0, sizeof(depth));

    // 设置基本信息
    std::strncpy(depth.instrument_id, symbol.c_str(), sizeof(depth.instrument_id) - 1);
    depth.instrument_idx = 0; // 只有一个品种，索引始终为0
    depth.exchange_id = exchange_type::BYBIT;
    depth.ins_type = instrument_type::PerpetualFuture; // 默认为现货
    depth.exchange_timestamp = timestamp;
    depth.local_timestamp = timestamp + 1000000; // 本地时间戳比交易所时间戳晚1毫秒

    // 设置深度
    depth.depth = std::min(static_cast<int>(std::min(bids.size(), asks.size())), 5);

    // 设置买盘
    for (int i = 0; i < depth.depth && i < static_cast<int>(bids.size()); ++i) {
        depth.bid_price[i] = bids[i].first;
        depth.bid_volume[i] = bids[i].second;
    }

    // 设置卖盘
    for (int i = 0; i < depth.depth && i < static_cast<int>(asks.size()); ++i) {
        depth.ask_price[i] = asks[i].first;
        depth.ask_volume[i] = asks[i].second;
    }

    // 添加到深度数据列表
    depth_data_.push_back(depth);
}

bool bybit_preprocessor::parse_transaction_csv_line(const std::string& line) {
    auto fields = split_csv_line(line);

    // 检查字段数量
    if (fields.size() < 11) {
        return false;
    }

    try {
        // 解析时间戳
        double timestamp_sec = std::stof(fields[0]);
        int64_t timestamp = static_cast<int64_t>(timestamp_sec * 1000000000); // 转换为纳秒

        // 获取品种
        std::string symbol = fields[1];
        // 创建成交数据
        transaction_field trans;
        std::memset(&trans, 0, sizeof(trans));

        // 设置基本信息
        std::strncpy(trans.instrument_id, symbol.c_str(), sizeof(trans.instrument_id) - 1);
        trans.instrument_idx = 0; // 只有一个品种，索引始终为0
        trans.exchange_id = exchange_type::BYBIT;
        trans.ins_type = instrument_type::SPOT; // 默认为现货
        trans.exchange_timestamp = timestamp;
        trans.local_timestamp = timestamp + 1000000; // 本地时间戳比交易所时间戳晚1毫秒

        // 解析方向
        std::string side = fields[2];
        trans.is_maker = false ? side == "Buy" : true; // 默认为taker

        // 解析价格和数量
        trans.price = std::stod(fields[4]);
        trans.volume = std::stod(fields[3]);

        // 添加到成交数据列表
        transaction_data_.push_back(trans);
        return true;
    } catch (const std::exception& e) {
        std::cerr << "Error parsing transaction CSV line: " << e.what() << std::endl;
        return false;
    }
}

bool bybit_preprocessor::parse_kline_csv_line(const std::string& line) {
    // 目前没有K线数据样本，暂不实现
    return false;
}

std::vector<std::string> bybit_preprocessor::split_csv_line(const std::string& line, char delimiter) {
    std::vector<std::string> fields;
    std::stringstream ss(line);
    std::string field;

    while (std::getline(ss, field, delimiter)) {
        fields.push_back(field);
    }

    return fields;
}

void bybit_preprocessor::add_instrument(const std::string& symbol, const nlohmann::json& instrument_config) {
    // 直接创建新品种，不需要检查是否已存在
    // 因为我们只会在初始化时调用一次
    instrument_symbol_ = symbol;

    // 创建品种信息
    std::memset(&instrument_, 0, sizeof(instrument_));
    std::strncpy(instrument_.instrument_id, symbol.c_str(), sizeof(instrument_.instrument_id) - 1);
    instrument_.instrument_idx = 0; // 因为只有一个品种，所以索引始终为0
    instrument_.exchange_id = static_cast<uint8_t>(exchange_type::BYBIT);

    // 从配置中读取合约信息
    if (!instrument_config.is_null()) {
        if (instrument_config.contains("tick_size")) {
            instrument_.tick_size = instrument_config["tick_size"].get<double>();
        }
        if (instrument_config.contains("step_size")) {
            instrument_.step_size = instrument_config["step_size"].get<double>();
        }
    }

    // 重置索引计数器，因为我们只有一个品种
    next_instrument_idx_ = 1;
}

} // namespace cpp_backtest
} // namespace fast_trader_elite
