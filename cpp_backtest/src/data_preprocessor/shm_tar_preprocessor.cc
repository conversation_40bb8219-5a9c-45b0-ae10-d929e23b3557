#include <algorithm>
#include <cmath>
#include <cstring>
#include <ctime>
#include <fast_trader_elite/cpp_backtest/data_preprocessor/shm_tar_preprocessor.h>
#include <filesystem>
#include <fstream>
#include <iomanip>
#include <iostream>
#include <sstream>

namespace fast_trader_elite {
namespace cpp_backtest {
static constexpr size_t INSTRUMENT_IDX =1; 

shm_tar_preprocessor::shm_tar_preprocessor()
    : exchange_type_(exchange_type::UNKNOWN), start_page_(0), end_page_(0) {}

shm_tar_preprocessor::~shm_tar_preprocessor() { clear(); }

bool shm_tar_preprocessor::init_from_config(const std::string &config_path) {
  try {
    // 读取配置文件
    std::ifstream config_file(config_path);
    if (!config_file.is_open()) {
      std::cerr << "Failed to open config file: " << config_path << std::endl;
      return false;
    }

    // 解析JSON配置
    config_ = nlohmann::json::parse(config_file);

    // 解析配置
    if (!parse_config(config_)) {
      std::cerr << "Failed to parse config" << std::endl;
      return false;
    }

    return true;
  } catch (const std::exception &e) {
    std::cerr << "Exception in init_from_config: " << e.what() << std::endl;
    return false;
  }
}

bool shm_tar_preprocessor::parse_config(const nlohmann::json &config) {
  try {
    // 获取交易所类型
    // if (config.contains("exchange")) {
    //     std::string exchange_str = config["exchange"].get<std::string>();
    //     if (exchange_str == "bybit") {
    //         exchange_type_ = exchange_type::BYBIT;
    //     } else if (exchange_str == "binance") {
    //         exchange_type_ = exchange_type::BINANCE;
    //     } else {
    //         // std::cerr << "Unknown exchange type: " << exchange_str <<
    //         std::endl; return false;
    //     }
    // } else {
    //     std::cerr << "Exchange type not specified in config" << std::endl;
    //     return false;
    // }
    exchange_type_ = exchange_type::BYBIT;

    // 获取SHM TAR文件路径
    if (config.contains("shm_tar_path")) {
      shm_tar_path_ = config["shm_tar_path"].get<std::string>();
    } else {
      std::cerr << "SHM TAR path not specified in config" << std::endl;
      return false;
    }

    // 获取页范围
    if (config.contains("start_page")) {
      start_page_ = config["start_page"].get<int>();
    }
    if (config.contains("end_page")) {
      end_page_ = config["end_page"].get<int>();
    }

    // 获取品种信息
    if (config.contains("instrument")) {
      auto instrument = config["instrument"];
      if (instrument.contains("symbol")) {
        instrument_symbol_ = instrument["symbol"].get<std::string>();
        add_instrument(instrument_symbol_, instrument);
      } else {
        std::cerr << "Instrument symbol not specified in config" << std::endl;
        return false;
      }
    } else {
      std::cerr << "Instrument not specified in config" << std::endl;
      return false;
    }

    return true;
  } catch (const std::exception &e) {
    std::cerr << "Exception in parse_config: " << e.what() << std::endl;
    return false;
  }
}

void shm_tar_preprocessor::add_instrument(
    const std::string &symbol, const nlohmann::json &instrument_config) {
  // 创建品种信息
  instrument_symbol_ = symbol;
  std::memset(&instrument_, 0, sizeof(instrument_));
  std::strncpy(instrument_.instrument_id, symbol.c_str(),
               sizeof(instrument_.instrument_id) - 1);
  instrument_.instrument_idx = INSTRUMENT_IDX; // 因为只有一个品种，所以索引始终为0
  instrument_.exchange_id = static_cast<uint8_t>(exchange_type_);

  // 从配置中读取合约信息
  if (!instrument_config.is_null()) {
    if (instrument_config.contains("tick_size")) {
      instrument_.tick_size = instrument_config["tick_size"].get<double>();
    }
    if (instrument_config.contains("step_size")) {
      instrument_.step_size = instrument_config["step_size"].get<double>();
    }
  }
}

bool shm_tar_preprocessor::read_depth_data(const std::string &filename) {
  return read_shm_tar_data(shm_tar_path_);
}

bool shm_tar_preprocessor::read_transaction_data(const std::string &filename) {
  // 在SHM TAR预处理器中，我们使用read_shm_tar_data方法读取所有数据
  // 这个方法只是为了兼容接口，实际上所有数据都在read_depth_data中读取
  return true;
}

bool shm_tar_preprocessor::read_kline_data(const std::string &filename) {
  // 在SHM TAR预处理器中，我们使用read_shm_tar_data方法读取所有数据
  // 这个方法只是为了兼容接口，实际上所有数据都在read_depth_data中读取
  return true;
}

bool shm_tar_preprocessor::read_shm_tar_data(const std::string &filename) {
  try {
    // 清除之前的数据
    clear();

    // 创建SHM TAR页面读取器
    cpp_frame::shm::shm_tar_page_reader<SHM_PAGE_SIZE> reader(
        shm_tar_path_.c_str());
    reader.init(shm_tar_path_.c_str());
    reader.set_index_filter(start_page_, end_page_);

    cpp_frame::shm::frame_header *header = nullptr;
    while ((header = reader.read()) != nullptr) {
      process_frame(header);
      reader.pass_frame(header->size);
    }

    std::cout << "Read " << depth_data_.size() << " depth records, "
              << transaction_data_.size() << " transaction records, "
              << kline_data_.size() << " kline records from " << filename
              << std::endl;

    return true;
  } catch (const std::exception &e) {
    std::cerr << "Exception in read_shm_tar_data: " << e.what() << std::endl;
    return false;
  }
}

void shm_tar_preprocessor::process_frame(
    const cpp_frame::shm::frame_header *header) {
  // 根据消息ID处理不同类型的数据
  switch (header->msg_id) {
  case MSG_DEPTH_MD:
    process_depth_market_data(header);
    break;
  case MSG_TRANS_MD:
    process_transaction_data(header);
    break;
  case MSG_KLINE_MD:
    process_kline_market_data(header);
    break;
  default:
    // 忽略其他类型的消息
    break;
  }
}

void shm_tar_preprocessor::process_depth_market_data(
    const cpp_frame::shm::frame_header *header) {
  const depth_market_data_field *depth =
      reinterpret_cast<const depth_market_data_field *>(header + 1);

  if (std::string(depth->instrument_name) == instrument_symbol_) {
    depth_market_data_field depth_copy = *depth;
    depth_copy.exchange_timestamp = depth->exchange_timestamp * 1000000;
    depth_copy.instrument_idx = INSTRUMENT_IDX;
    depth_data_.push_back(depth_copy);
  }
}

void shm_tar_preprocessor::process_transaction_data(
    const cpp_frame::shm::frame_header *header) {
  const transaction_field *trans =
      reinterpret_cast<const transaction_field *>(header + 1);
  if (std::string(trans->instrument_name) == instrument_symbol_) {
    transaction_field trans_copy = *trans;
    trans_copy.exchange_timestamp = trans->exchange_timestamp * 1000000;
    trans_copy.instrument_idx = INSTRUMENT_IDX;
    transaction_data_.push_back(trans_copy);
  }
}

void shm_tar_preprocessor::process_kline_market_data(
    const cpp_frame::shm::frame_header *header) {
  const kline_market_data_field *kline =
      reinterpret_cast<const kline_market_data_field *>(header + 1);
  if (std::string(kline->instrument_name) == instrument_symbol_) {
    kline_market_data_field kline_copy = *kline;
    kline_copy.exchange_timestamp = kline->exchange_timestamp * 1000000;
    kline_copy.instrument_idx = INSTRUMENT_IDX;
    kline_data_.push_back(kline_copy);
  }
}

exchange_type shm_tar_preprocessor::get_exchange_type() const {
  return exchange_type_;
}

const std::vector<depth_market_data_field> &
shm_tar_preprocessor::get_depth_data() const {
  return depth_data_;
}

const std::vector<transaction_field> &
shm_tar_preprocessor::get_transaction_data() const {
  return transaction_data_;
}

const std::vector<kline_market_data_field> &
shm_tar_preprocessor::get_kline_data() const {
  return kline_data_;
}

const instrument_info &shm_tar_preprocessor::get_instrument() const {
  return instrument_;
}

const std::string &shm_tar_preprocessor::get_instrument_symbol() const {
  return instrument_symbol_;
}

void shm_tar_preprocessor::clear() {
  depth_data_.clear();
  transaction_data_.clear();
  kline_data_.clear();
}

} // namespace cpp_backtest
} // namespace fast_trader_elite
