#include <fast_trader_elite/cpp_backtest/data_preprocessor/exchange_preprocessor.h>
#include <fast_trader_elite/cpp_backtest/data_preprocessor/bybit_preprocessor.h>
#include <fast_trader_elite/cpp_backtest/data_preprocessor/shm_tar_preprocessor.h>
#include <iostream>

namespace fast_trader_elite {
namespace cpp_backtest {

std::unique_ptr<exchange_preprocessor> create_exchange_preprocessor(exchange_type exchange) {
    switch (exchange) {
        case exchange_type::BYBIT:
            return std::make_unique<bybit_preprocessor>();
        case exchange_type::SHM_TAR:
            return std::make_unique<shm_tar_preprocessor>();
        // 可以添加其他交易所的预处理器
        default:
            std::cerr << "Unsupported exchange type: " << static_cast<int>(exchange) << std::endl;
            return nullptr;
    }
}

} // namespace cpp_backtest
} // namespace fast_trader_elite
