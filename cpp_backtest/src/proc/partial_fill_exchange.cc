#include <fast_trader_elite/cpp_backtest/proc/partial_fill_exchange.h>
#include <fast_trader_elite/cpp_backtest/backtest_logger.h>
#include <iostream>
#include <limits>
#include <algorithm>

namespace fast_trader_elite {
namespace cpp_backtest {

partial_fill_exchange::partial_fill_exchange(market_depth* depth, fee_model* fee_model, queue_model* queue_model, latency_model* order_latency)
    : exchange_processor(depth, fee_model, queue_model, order_latency) {
}

void partial_fill_exchange::process_transaction(const transaction_field* trans) {
    // 获取成交价格的tick
    int64_t price_tick = depth_->price_to_tick(trans->price);
    double qty = trans->volume;

    // 确定交易方向
    bool is_buy_trade = !trans->is_maker; // EXCH_BUY_TRADE_EVENT

    if (is_buy_trade) {
        // 买方发起成交，检查卖单
        // 选择更高效的计算路径
        if (depth_->best_bid_tick() == std::numeric_limits<int64_t>::min() ||
            static_cast<int64_t>(orders_.size()) < price_tick - depth_->best_bid_tick()) {
            // 遍历所有订单 - 当订单数量较少或价格范围较大时更高效
            for (auto& [order_id, order] : orders_) {
                if (order.side == side_type::sell &&
                    (order.status == order_status::new_order || order.status == order_status::partially_filled)) {
                    check_if_sell_filled(order, price_tick, qty);
                }
            }
        } else {
            // 只遍历可能成交的价格层 - 当订单数量较多且价格范围较小时更高效
            for (int64_t t = depth_->best_bid_tick() + 1; t <= price_tick; ++t) {
                auto it = sell_orders_.find(t);
                if (it != sell_orders_.end()) {
                    // 使用引用避免不必要的复制
                    for (const int64_t& order_id : it->second) {
                        auto order_it = orders_.find(order_id);
                        if (order_it != orders_.end() &&
                            (order_it->second.status == order_status::new_order ||
                             order_it->second.status == order_status::partially_filled)) {
                            check_if_sell_filled(order_it->second, price_tick, qty);
                        }
                    }
                }
            }
        }
    } else {
        // 卖方发起成交，检查买单
        // 选择更高效的计算路径
        if (depth_->best_ask_tick() == std::numeric_limits<int64_t>::max() ||
            static_cast<int64_t>(orders_.size()) < depth_->best_ask_tick() - price_tick) {
            // 遍历所有订单 - 当订单数量较少或价格范围较大时更高效
            for (auto& [order_id, order] : orders_) {
                if (order.side == side_type::buy &&
                    (order.status == order_status::new_order || order.status == order_status::partially_filled)) {
                    check_if_buy_filled(order, price_tick, qty);
                }
            }
        } else {
            // 只遍历可能成交的价格层 - 当订单数量较多且价格范围较小时更高效
            // 注意：这里使用从高到低的顺序遍历价格层，与买单的遍历方向相反
            for (int64_t t = price_tick; t < depth_->best_ask_tick(); ++t) {
                auto it = buy_orders_.find(t);
                if (it != buy_orders_.end()) {
                    // 使用引用避免不必要的复制
                    for (const int64_t& order_id : it->second) {
                        auto order_it = orders_.find(order_id);
                        if (order_it != orders_.end() &&
                            (order_it->second.status == order_status::new_order ||
                             order_it->second.status == order_status::partially_filled)) {
                            check_if_buy_filled(order_it->second, price_tick, qty);
                        }
                    }
                }
            }
        }
    }

    // 移除已成交订单
    remove_filled_orders();
}

backtest_error partial_fill_exchange::process_order(const internal_order& order) {
    // 复制订单
    internal_order order_copy = order;

    // 设置交易所时间戳
    order_copy.exchange_timestamp = current_timestamp_;

    // 记录订单处理信息
    BT_LOG_DEBUG("exchange_process_order order_id:{} instrument_idx:{} side:{} type:{} price:{} quantity:{} request:{} timestamp:{}",
                order_copy.order_id, order_copy.instrument_idx,
                static_cast<int>(order_copy.side), static_cast<int>(order_copy.type),
                order_copy.price, order_copy.quantity,
                static_cast<int>(order_copy.request), current_timestamp_);

    // 根据请求类型处理
    switch (order.request) {
        case order_status::new_order:
            return process_new_order(order_copy);
        case order_status::canceled:
            return process_cancel_order(order_copy);
        case order_status::none:
            // 已经处理过的订单，不需要再处理
            return backtest_error::none;
        default:
            // 处理无效操作
            order_copy.status = order_status::rejected;
            order_copy.error_id = static_cast<int>(backtest_error::invalid_operation);
            BT_LOG_WARN("exchange_order_rejected order_id:{} instrument_idx:{} reason:invalid_operation timestamp:{}",
                       order_copy.order_id, order_copy.instrument_idx, current_timestamp_);
            send_order_report(order_copy);
            return backtest_error::none;
    }
}

void partial_fill_exchange::try_match_orders() {
    // 遍历所有活跃订单
    for (auto& [order_id, order] : orders_) {
        if (order.status != order_status::new_order && order.status != order_status::partially_filled) {
            continue;
        }

        bool matched = false;

        // 检查是否可以成交
        if (order.side == side_type::buy) {
            // 买单检查卖盘
            if (depth_->best_ask_tick() != std::numeric_limits<int64_t>::max()) {
                int64_t order_price_tick = depth_->price_to_tick(order.price);

                // 检查价格是否可以成交
                if ((order.type == order_type::market) ||
                    (order.type == order_type::limit && order_price_tick >= depth_->best_ask_tick())) {

                    // 市价单直接成交，限价单需要检查队列位置
                    double filled_qty = 0.0;
                    if (order.type == order_type::market) {
                        filled_qty = std::min(depth_->ask_qty(depth_->best_ask_tick()),
                                             order.quantity - order.executed_quantity);
                    } else {
                        filled_qty = queue_model_->is_filled(order, depth_);
                    }

                    if (filled_qty > 0.0) {
                        // 成交订单
                        double market_price = depth_->tick_to_price(depth_->best_ask_tick());
                        double exec_qty = std::min(filled_qty, order.quantity - order.executed_quantity);

                        fill(order, market_price, exec_qty, order.type == order_type::limit);
                        send_order_report(order);
                        matched = true;
                    }
                }
            }
        } else if (order.side == side_type::sell) {
            // 卖单检查买盘
            if (depth_->best_bid_tick() != std::numeric_limits<int64_t>::min()) {
                int64_t order_price_tick = depth_->price_to_tick(order.price);

                // 检查价格是否可以成交
                if ((order.type == order_type::market) ||
                    (order.type == order_type::limit && order_price_tick <= depth_->best_bid_tick())) {

                    // 市价单直接成交，限价单需要检查队列位置
                    double filled_qty = 0.0;
                    if (order.type == order_type::market) {
                        filled_qty = std::min(depth_->bid_qty(depth_->best_bid_tick()),
                                             order.quantity - order.executed_quantity);
                    } else {
                        filled_qty = queue_model_->is_filled(order, depth_);
                    }

                    if (filled_qty > 0.0) {
                        // 成交订单
                        double market_price = depth_->tick_to_price(depth_->best_bid_tick());
                        double exec_qty = std::min(filled_qty, order.quantity - order.executed_quantity);

                        fill(order, market_price, exec_qty, order.type == order_type::limit);
                        send_order_report(order);
                        matched = true;
                    }
                }
            }
        }

        // 如果订单未完全成交且是IOC类型，则取消剩余部分
        if (order.status == order_status::partially_filled &&
            order.time_in_force == time_in_force_type::ioc) {
            order.status = order_status::canceled;
            send_order_report(order);
        }
        // 如果订单未成交且是FOK类型，则取消订单
        else if (!matched && order.status == order_status::new_order &&
                 order.time_in_force == time_in_force_type::fok) {
            order.status = order_status::canceled;
            send_order_report(order);
        }
    }
}

backtest_error partial_fill_exchange::process_new_order(internal_order& order) {
    // 检查订单ID是否已存在
    if (orders_.find(order.order_id) != orders_.end()) {
        order.status = order_status::rejected;
        order.error_id = static_cast<int>(backtest_error::order_id_exist);
        send_order_report(order);
        return backtest_error::none;
    }

    // 检查价格是否有效
    if (order.price < 0.0) {
        order.status = order_status::rejected;
        order.error_id = static_cast<int>(backtest_error::invalid_price);
        send_order_report(order);
        return backtest_error::none;
    }

    // 检查数量是否有效
    if (order.quantity <= 0.0) {
        order.status = order_status::rejected;
        order.error_id = static_cast<int>(backtest_error::invalid_quantity);
        send_order_report(order);
        return backtest_error::none;
    }

    // 检查方向是否有效
    if (order.side != side_type::buy && order.side != side_type::sell) {
        order.status = order_status::rejected;
        order.error_id = static_cast<int>(backtest_error::invalid_side);
        send_order_report(order);
        return backtest_error::none;
    }

    // 检查订单类型是否有效
    if (order.type != order_type::limit && order.type != order_type::market) {
        order.status = order_status::rejected;
        order.error_id = static_cast<int>(backtest_error::invalid_order_type);
        send_order_report(order);
        return backtest_error::none;
    }

    // 检查有效期类型是否有效
    if (order.time_in_force != time_in_force_type::gtc &&
        order.time_in_force != time_in_force_type::gtx &&
        order.time_in_force != time_in_force_type::ioc &&
        order.time_in_force != time_in_force_type::fok) {
        order.status = order_status::rejected;
        order.error_id = static_cast<int>(backtest_error::invalid_time_in_force);
        send_order_report(order);
        return backtest_error::none;
    }

    // 处理买单
    if (order.side == side_type::buy) {
        if (order.type == order_type::limit) {
            // 检查买单价格是否大于等于当前最佳卖价
            if (depth_->price_to_tick(order.price) >= depth_->best_ask_tick()) {
                // 如果是GTX订单，则拒绝
                if (order.time_in_force == time_in_force_type::gtx) {
                    order.status = order_status::rejected;
                    send_order_report(order);
                    return backtest_error::none;
                }

                // 如果是FOK订单，需要检查是否有足够的市场深度来完全成交订单
                if (order.time_in_force == time_in_force_type::fok) {
                    bool execute = false;
                    double cum_qty = 0.0;
                    int64_t order_price_tick = depth_->price_to_tick(order.price);

                    // 获取卖单价格和数量列表
                    const std::vector<int64_t>& ask_price_ticks = depth_->ask_prices();
                    const std::vector<double>& ask_quantities = depth_->ask_qtys();

                    // 遍历卖单深度，但只考虑价格不高于订单价格的档位
                    for (size_t i = 0; i < ask_price_ticks.size(); ++i) {
                        int64_t t = ask_price_ticks[i];
                        if (t > order_price_tick) {
                            break; // 超过订单价格，不再考虑
                        }
                        cum_qty += ask_quantities[i];
                        if (cum_qty >= order.quantity) {
                            execute = true;
                            break;
                        }
                    }

                    if (!execute) {
                        // 如果没有足够的市场深度，则取消订单
                        order.status = order_status::canceled;
                        send_order_report(order);
                        return backtest_error::none;
                    }
                }

                // 如果是IOC订单，尝试在多个价格层成交
                if (order.time_in_force == time_in_force_type::ioc) {
                    int64_t order_price_tick = depth_->price_to_tick(order.price);
                    const std::vector<int64_t>& ask_price_ticks = depth_->ask_prices();
                    const std::vector<double>& ask_quantities = depth_->ask_qtys();

                    for (size_t i = 0; i < ask_price_ticks.size(); ++i) {
                        int64_t t = ask_price_ticks[i];
                        if (t > order_price_tick) {
                            break; // 超过订单价格，不再考虑
                        }
                        double qty = ask_quantities[i];
                        if (qty > 0.0) {
                            double exec_qty = std::min(qty, order.quantity - order.executed_quantity);
                            fill(order, depth_->tick_to_price(t), exec_qty, false);

                            if (order.status == order_status::filled) {
                                break;
                            }
                        }
                    }

                    // 如果未完全成交，则取消剩余部分
                    if (order.status != order_status::filled) {
                        order.status = order_status::canceled;
                    }

                    // 保存订单并发送回报
                    orders_[order.order_id] = order;
                    send_order_report(order);
                    return backtest_error::none;
                }

                // 对于GTC订单，尝试部分成交
                double market_price = depth_->tick_to_price(depth_->best_ask_tick());
                double market_quantity = depth_->ask_qty(depth_->best_ask_tick());
                double fillable_quantity = calculate_fillable_quantity(order, market_quantity);

                if (fillable_quantity > 0.0) {
                    // 部分成交
                    fill(order, market_price, fillable_quantity, false);

                    // 如果未完全成交且不是IOC或FOK订单，则挂单
                    if (order.status == order_status::partially_filled &&
                        order.time_in_force != time_in_force_type::ioc &&
                        order.time_in_force != time_in_force_type::fok) {
                        queue_model_->new_order(order, depth_);
                        buy_orders_[depth_->price_to_tick(order.price)].insert(order.order_id);
                    }

                    // 保存订单
                    orders_[order.order_id] = order;

                    // 发送订单回报
                    send_order_report(order);
                    return backtest_error::none;
                } else {
                    // 如果是IOC或FOK订单，则取消订单
                    if (order.time_in_force == time_in_force_type::ioc ||
                        order.time_in_force == time_in_force_type::fok) {
                        order.status = order_status::canceled;
                        send_order_report(order);
                        return backtest_error::none;
                    }

                    // 挂单
                    order.status = order_status::new_order;
                    queue_model_->new_order(order, depth_);
                    orders_[order.order_id] = order;
                    buy_orders_[depth_->price_to_tick(order.price)].insert(order.order_id);
                    send_order_report(order);
                    return backtest_error::none;
                }
            } else {
                // 如果是IOC或FOK订单，则拒绝
                if (order.time_in_force == time_in_force_type::ioc ||
                    order.time_in_force == time_in_force_type::fok) {
                    order.status = order_status::canceled;
                    send_order_report(order);
                    return backtest_error::none;
                }

                // 挂单
                order.status = order_status::new_order;
                queue_model_->new_order(order, depth_);
                orders_[order.order_id] = order;
                buy_orders_[depth_->price_to_tick(order.price)].insert(order.order_id);
                send_order_report(order);
                return backtest_error::none;
            }
        } else if (order.type == order_type::market) {
            // 如果是FOK订单，需要检查是否有足够的市场深度来完全成交订单
            if (order.time_in_force == time_in_force_type::fok) {
                bool execute = false;
                double cum_qty = 0.0;

                // 获取卖单价格和数量列表
                const std::vector<int64_t>& ask_price_ticks = depth_->ask_prices();
                const std::vector<double>& ask_quantities = depth_->ask_qtys();

                // 遍历卖单深度
                for (size_t i = 0; i < ask_price_ticks.size(); ++i) {
                    cum_qty += ask_quantities[i];
                    if (cum_qty >= order.quantity) {
                        execute = true;
                        break;
                    }
                }

                if (!execute) {
                    // 如果没有足够的市场深度，则取消订单
                    order.status = order_status::canceled;
                    send_order_report(order);
                    return backtest_error::none;
                }
            }

            // 在多个价格层尝试成交
            const std::vector<int64_t>& ask_price_ticks = depth_->ask_prices();
            const std::vector<double>& ask_quantities = depth_->ask_qtys();

            for (size_t i = 0; i < ask_price_ticks.size(); ++i) {
                int64_t t = ask_price_ticks[i];
                double qty = ask_quantities[i];
                if (qty > 0.0) {
                    double exec_qty = std::min(qty, order.quantity - order.executed_quantity);
                    fill(order, depth_->tick_to_price(t), exec_qty, false);

                    if (order.status == order_status::filled) {
                        break;
                    }
                }
            }

            // 如果是FOK订单且未完全成交，则取消订单
            if (order.time_in_force == time_in_force_type::fok && order.status != order_status::filled) {
                order.status = order_status::canceled;
            }

            // 如果未完全成交且是IOC订单，则取消剩余部分
            if (order.status != order_status::filled &&
                (order.time_in_force == time_in_force_type::ioc || order.time_in_force == time_in_force_type::fok)) {
                order.status = order_status::canceled;
            }

            // 如果没有成交，则取消订单
            if (order.executed_quantity <= 0.0) {
                order.status = order_status::canceled;
            }

            // 保存订单
            orders_[order.order_id] = order;

            // 发送订单回报
            send_order_report(order);
            return backtest_error::none;
        }
    }
    // 处理卖单
    else if (order.side == side_type::sell) {
        if (order.type == order_type::limit) {
            // 检查卖单价格是否小于等于当前最佳买价
            if (depth_->price_to_tick(order.price) <= depth_->best_bid_tick()) {
                // 如果是GTX订单，则拒绝
                if (order.time_in_force == time_in_force_type::gtx) {
                    order.status = order_status::rejected;
                    send_order_report(order);
                    return backtest_error::none;
                }

                // 如果是FOK订单，需要检查是否有足够的市场深度来完全成交订单
                if (order.time_in_force == time_in_force_type::fok) {
                    bool execute = false;
                    double cum_qty = 0.0;
                    int64_t order_price_tick = depth_->price_to_tick(order.price);

                    // 获取买单价格和数量列表
                    const std::vector<int64_t>& bid_price_ticks = depth_->bid_prices();
                    const std::vector<double>& bid_quantities = depth_->bid_qtys();

                    // 遍历买单深度，但只考虑价格不低于订单价格的档位
                    for (size_t i = 0; i < bid_price_ticks.size(); ++i) {
                        int64_t t = bid_price_ticks[i];
                        if (t < order_price_tick) {
                            break; // 低于订单价格，不再考虑
                        }
                        cum_qty += bid_quantities[i];
                        if (cum_qty >= order.quantity) {
                            execute = true;
                            break;
                        }
                    }

                    if (!execute) {
                        // 如果没有足够的市场深度，则取消订单
                        order.status = order_status::canceled;
                        send_order_report(order);
                        return backtest_error::none;
                    }
                }

                // 如果是IOC订单，尝试在多个价格层成交
                if (order.time_in_force == time_in_force_type::ioc) {
                    int64_t order_price_tick = depth_->price_to_tick(order.price);
                    const std::vector<int64_t>& bid_price_ticks = depth_->bid_prices();
                    const std::vector<double>& bid_quantities = depth_->bid_qtys();

                    for (size_t i = 0; i < bid_price_ticks.size(); ++i) {
                        int64_t t = bid_price_ticks[i];
                        if (t < order_price_tick) {
                            break; // 低于订单价格，不再考虑
                        }
                        double qty = bid_quantities[i];
                        if (qty > 0.0) {
                            double exec_qty = std::min(qty, order.quantity - order.executed_quantity);
                            fill(order, depth_->tick_to_price(t), exec_qty, false);

                            if (order.status == order_status::filled) {
                                break;
                            }
                        }
                    }

                    // 如果未完全成交，则取消剩余部分
                    if (order.status != order_status::filled) {
                        order.status = order_status::canceled;
                    }

                    // 保存订单并发送回报
                    orders_[order.order_id] = order;
                    send_order_report(order);
                    return backtest_error::none;
                }

                // 对于GTC订单，尝试部分成交
                double market_price = depth_->tick_to_price(depth_->best_bid_tick());
                double market_quantity = depth_->bid_qty(depth_->best_bid_tick());
                double fillable_quantity = calculate_fillable_quantity(order, market_quantity);

                if (fillable_quantity > 0.0) {
                    // 部分成交
                    fill(order, market_price, fillable_quantity, false);

                    // 如果未完全成交且不是IOC或FOK订单，则挂单
                    if (order.status == order_status::partially_filled &&
                        order.time_in_force != time_in_force_type::ioc &&
                        order.time_in_force != time_in_force_type::fok) {
                        queue_model_->new_order(order, depth_);
                        sell_orders_[depth_->price_to_tick(order.price)].insert(order.order_id);
                    }

                    // 保存订单
                    orders_[order.order_id] = order;

                    // 发送订单回报
                    send_order_report(order);
                    return backtest_error::none;
                } else {
                    // 如果是IOC或FOK订单，则取消订单
                    if (order.time_in_force == time_in_force_type::ioc ||
                        order.time_in_force == time_in_force_type::fok) {
                        order.status = order_status::canceled;
                        send_order_report(order);
                        return backtest_error::none;
                    }

                    // 挂单
                    order.status = order_status::new_order;
                    queue_model_->new_order(order, depth_);
                    orders_[order.order_id] = order;
                    sell_orders_[depth_->price_to_tick(order.price)].insert(order.order_id);
                    send_order_report(order);
                    return backtest_error::none;
                }
            } else {
                // 如果是IOC或FOK订单，则拒绝
                if (order.time_in_force == time_in_force_type::ioc ||
                    order.time_in_force == time_in_force_type::fok) {
                    order.status = order_status::canceled;
                    send_order_report(order);
                    return backtest_error::none;
                }

                // 挂单
                order.status = order_status::new_order;
                queue_model_->new_order(order, depth_);
                orders_[order.order_id] = order;
                sell_orders_[depth_->price_to_tick(order.price)].insert(order.order_id);
                send_order_report(order);
                return backtest_error::none;
            }
        } else if (order.type == order_type::market) {
            // 如果是FOK订单，需要检查是否有足够的市场深度来完全成交订单
            if (order.time_in_force == time_in_force_type::fok) {
                bool execute = false;
                double cum_qty = 0.0;

                // 获取买单价格和数量列表
                const std::vector<int64_t>& bid_price_ticks = depth_->bid_prices();
                const std::vector<double>& bid_quantities = depth_->bid_qtys();

                // 遍历买单深度
                for (size_t i = 0; i < bid_price_ticks.size(); ++i) {
                    cum_qty += bid_quantities[i];
                    if (cum_qty >= order.quantity) {
                        execute = true;
                        break;
                    }
                }

                if (!execute) {
                    // 如果没有足够的市场深度，则取消订单
                    order.status = order_status::canceled;
                    send_order_report(order);
                    return backtest_error::none;
                }
            }

            // 在多个价格层尝试成交
            const std::vector<int64_t>& bid_price_ticks = depth_->bid_prices();
            const std::vector<double>& bid_quantities = depth_->bid_qtys();

            for (size_t i = 0; i < bid_price_ticks.size(); ++i) {
                int64_t t = bid_price_ticks[i];
                double qty = bid_quantities[i];
                if (qty > 0.0) {
                    double exec_qty = std::min(qty, order.quantity - order.executed_quantity);
                    fill(order, depth_->tick_to_price(t), exec_qty, false);

                    if (order.status == order_status::filled) {
                        break;
                    }
                }
            }

            // 如果是FOK订单且未完全成交，则取消订单
            if (order.time_in_force == time_in_force_type::fok && order.status != order_status::filled) {
                order.status = order_status::canceled;
            }

            // 如果未完全成交且是IOC订单，则取消剩余部分
            if (order.status != order_status::filled &&
                (order.time_in_force == time_in_force_type::ioc || order.time_in_force == time_in_force_type::fok)) {
                order.status = order_status::canceled;
            }

            // 如果没有成交，则取消订单
            if (order.executed_quantity <= 0.0) {
                order.status = order_status::canceled;
            }

            // 保存订单
            orders_[order.order_id] = order;

            // 发送订单回报
            send_order_report(order);
            return backtest_error::none;
        }
    }

    return backtest_error::invalid_operation;
}

backtest_error partial_fill_exchange::process_cancel_order(internal_order& order) {
    // 查找订单
    auto it = orders_.find(order.order_id);
    if (it == orders_.end()) {
        order.status = order_status::cancel_rejected;
        order.error_id = static_cast<int>(backtest_error::order_not_found);
        send_order_report(order);
        return backtest_error::none;
    }

    // 获取订单
    internal_order& existing_order = it->second;

    // 检查订单状态
    if (existing_order.status != order_status::new_order &&
        existing_order.status != order_status::partially_filled) {
        order.status = order_status::cancel_rejected;
        order.error_id = static_cast<int>(backtest_error::invalid_state);
        send_order_report(order);
        return backtest_error::none;
    }

    // 从价格层中移除订单
    if (existing_order.side == side_type::buy) {
        auto price_it = buy_orders_.find(depth_->price_to_tick(existing_order.price));
        if (price_it != buy_orders_.end()) {
            price_it->second.erase(existing_order.order_id);
            if (price_it->second.empty()) {
                buy_orders_.erase(price_it);
            }
        }
    } else {
        auto price_it = sell_orders_.find(depth_->price_to_tick(existing_order.price));
        if (price_it != sell_orders_.end()) {
            price_it->second.erase(existing_order.order_id);
            if (price_it->second.empty()) {
                sell_orders_.erase(price_it);
            }
        }
    }

    // 更新订单状态
    existing_order.status = order_status::canceled;
    existing_order.exchange_timestamp = current_timestamp_;

    // 发送订单回报
    send_order_report(existing_order);

    return backtest_error::none;
}

backtest_error partial_fill_exchange::process_modify_order(internal_order& order) {
    // 查找订单
    auto it = orders_.find(order.order_id);
    if (it == orders_.end()) {
        order.status = order_status::rejected;
        order.error_id = static_cast<int>(backtest_error::order_not_found);
        send_order_report(order);
        return backtest_error::none;
    }

    // 获取订单
    internal_order& existing_order = it->second;

    // 检查订单状态
    if (existing_order.status != order_status::new_order &&
        existing_order.status != order_status::partially_filled) {
        order.status = order_status::rejected;
        order.error_id = static_cast<int>(backtest_error::invalid_state);
        send_order_report(order);
        return backtest_error::none;
    }

    // 从价格层中移除订单
    if (existing_order.side == side_type::buy) {
        auto price_it = buy_orders_.find(depth_->price_to_tick(existing_order.price));
        if (price_it != buy_orders_.end()) {
            price_it->second.erase(existing_order.order_id);
            if (price_it->second.empty()) {
                buy_orders_.erase(price_it);
            }
        }
    } else {
        auto price_it = sell_orders_.find(depth_->price_to_tick(existing_order.price));
        if (price_it != sell_orders_.end()) {
            price_it->second.erase(existing_order.order_id);
            if (price_it->second.empty()) {
                sell_orders_.erase(price_it);
            }
        }
    }

    // 更新订单
    existing_order.price = order.price;
    existing_order.quantity = order.quantity;
    existing_order.leaves_quantity = order.quantity - existing_order.executed_quantity;
    existing_order.exchange_timestamp = current_timestamp_;

    // 重新初始化队列位置
    queue_model_->new_order(existing_order, depth_);

    // 添加到价格层
    if (existing_order.side == side_type::buy) {
        buy_orders_[depth_->price_to_tick(existing_order.price)].insert(existing_order.order_id);
    } else {
        sell_orders_[depth_->price_to_tick(existing_order.price)].insert(existing_order.order_id);
    }

    // 发送订单回报
    send_order_report(existing_order);

    return backtest_error::none;
}

void partial_fill_exchange::fill(internal_order& order, double fill_price, double fill_quantity, bool is_maker) {
    // 记录成交前信息
    BT_LOG_DEBUG("exchange_fill_order_start order_id:{} instrument_idx:{} side:{} fill_price:{} fill_qty:{} is_maker:{} timestamp:{}",
                order.order_id, order.instrument_idx,
                static_cast<int>(order.side), fill_price, fill_quantity,
                is_maker, current_timestamp_);

    // 更新订单状态
    double old_executed_quantity = order.executed_quantity;
    order.executed_quantity += fill_quantity;
    order.leaves_quantity = order.quantity - order.executed_quantity;

    if (order.executed_quantity >= order.quantity) {
        order.status = order_status::filled;
        order.leaves_quantity = 0.0;
    } else {
        order.status = order_status::partially_filled;
    }

    // 计算成交价格（可能是加权平均价格）
    if (old_executed_quantity == 0.0) {
        order.executed_price = fill_price;
    } else {
        // 加权平均价格
        order.executed_price = (old_executed_quantity * order.executed_price +
                               fill_quantity * fill_price) / order.executed_quantity;
    }

    order.is_maker = is_maker;
    order.exchange_timestamp = current_timestamp_;

    // 计算手续费
    double additional_fee = fee_model_->calculate_fee(fill_price, fill_quantity, is_maker);
    order.fee += additional_fee;

    // 记录成交信息
    BT_LOG_DEBUG("exchange_order_filled order_id:{} instrument_idx:{} side:{} fill_price:{} fill_qty:{} total_executed_qty:{} fee:{} is_maker:{} status:{} timestamp:{}",
               order.order_id, order.instrument_idx,
               static_cast<int>(order.side), fill_price, fill_quantity,
               order.executed_quantity, additional_fee,
               is_maker, static_cast<int>(order.status), current_timestamp_);

    // 更新状态
    internal_order temp_order = order;
    temp_order.executed_quantity = fill_quantity;
    temp_order.executed_price = fill_price;
    temp_order.fee = additional_fee;
    state_.apply_fill(temp_order);
}

// 注意：此方法已经被队列模型的is_filled方法替代
double partial_fill_exchange::calculate_fillable_quantity(const internal_order& order, double available_quantity) {
    // 使用排队模型计算可成交数量
    double fillable = queue_model_->is_filled(order, depth_);

    // 如果有可成交数量，则返回可成交数量
    if (fillable > 0.0) {
        return std::min(order.quantity - order.executed_quantity, fillable);
    }

    return 0.0;
}

void partial_fill_exchange::check_if_buy_filled(internal_order& order, int64_t price_tick, double qty) {
    int64_t order_price_tick = depth_->price_to_tick(order.price);

    // 使用比较运算符进行价格比较，更清晰的逻辑
    if (order_price_tick > price_tick) {
        // 如果订单价格大于成交价格，直接成交全部剩余数量
        filled_orders_.push_back(order.order_id);
        fill(order, order.price, order.quantity - order.executed_quantity, true);
        send_order_report(order);
    } else if (order_price_tick == price_tick) {
        // 如果订单价格等于成交价格，更新队列位置
        queue_model_->trade(order, qty, depth_);
        double filled_qty = queue_model_->is_filled(order, depth_);
        if (filled_qty > 0.0) {
            // 计算实际可成交数量：队列模型返回的可成交数量、市场成交数量和订单剩余数量的最小值
            double exec_qty = std::min(filled_qty, std::min(qty, order.quantity - order.executed_quantity));
            filled_orders_.push_back(order.order_id);
            fill(order, order.price, exec_qty, true);
            send_order_report(order);
        }
    }
    // 如果订单价格小于成交价格，不会成交
}

void partial_fill_exchange::check_if_sell_filled(internal_order& order, int64_t price_tick, double qty) {
    int64_t order_price_tick = depth_->price_to_tick(order.price);

    // 使用比较运算符进行价格比较，更清晰的逻辑
    if (order_price_tick < price_tick) {
        // 如果订单价格小于成交价格，直接成交全部剩余数量
        filled_orders_.push_back(order.order_id);
        fill(order, order.price, order.quantity - order.executed_quantity, true);
        send_order_report(order);
    } else if (order_price_tick == price_tick) {
        // 如果订单价格等于成交价格，更新队列位置
        queue_model_->trade(order, qty, depth_);
        double filled_qty = queue_model_->is_filled(order, depth_);
        if (filled_qty > 0.0) {
            // 计算实际可成交数量：队列模型返回的可成交数量、市场成交数量和订单剩余数量的最小值
            double exec_qty = std::min(filled_qty, std::min(qty, order.quantity - order.executed_quantity));
            filled_orders_.push_back(order.order_id);
            fill(order, order.price, exec_qty, true);
            send_order_report(order);
        }
    }
    // 如果订单价格大于成交价格，不会成交
}

void partial_fill_exchange::remove_filled_orders() {
    for (int64_t order_id : filled_orders_) {
        auto it = orders_.find(order_id);
        if (it != orders_.end() && it->second.status == order_status::filled) {
            // 从价格层中移除订单
            if (it->second.side == side_type::buy) {
                auto price_it = buy_orders_.find(depth_->price_to_tick(it->second.price));
                if (price_it != buy_orders_.end()) {
                    price_it->second.erase(order_id);
                    if (price_it->second.empty()) {
                        buy_orders_.erase(price_it);
                    }
                }
            } else {
                auto price_it = sell_orders_.find(depth_->price_to_tick(it->second.price));
                if (price_it != sell_orders_.end()) {
                    price_it->second.erase(order_id);
                    if (price_it->second.empty()) {
                        sell_orders_.erase(price_it);
                    }
                }
            }
        }
    }

    filled_orders_.clear();
}

void partial_fill_exchange::on_best_bid_update(int64_t prev_best_tick, int64_t new_best_tick) {
    // 如果最佳买价上升，检查是否有卖单可以成交
    if (new_best_tick > prev_best_tick) {
        // 选择更高效的计算路径
        if (prev_best_tick == std::numeric_limits<int64_t>::min() ||
            orders_.size() < new_best_tick - prev_best_tick) {
            // 遍历所有订单
            for (auto& [order_id, order] : orders_) {
                if (order.side == side_type::sell &&
                    (order.status == order_status::new_order || order.status == order_status::partially_filled) &&
                    depth_->price_to_tick(order.price) <= new_best_tick) {
                    filled_orders_.push_back(order.order_id);
                    fill(order, order.price, order.quantity - order.executed_quantity, true);
                    send_order_report(order);
                }
            }
        } else {
            // 只遍历可能成交的价格层
            for (int64_t t = prev_best_tick + 1; t <= new_best_tick; ++t) {
                auto it = sell_orders_.find(t);
                if (it != sell_orders_.end()) {
                    for (int64_t order_id : it->second) {
                        auto order_it = orders_.find(order_id);
                        if (order_it != orders_.end() &&
                            (order_it->second.status == order_status::new_order ||
                             order_it->second.status == order_status::partially_filled)) {
                            filled_orders_.push_back(order_id);
                            fill(order_it->second, order_it->second.price,
                                 order_it->second.quantity - order_it->second.executed_quantity, true);
                            send_order_report(order_it->second);
                        }
                    }
                }
            }
        }

        // 移除已成交订单
        remove_filled_orders();
    }
}

void partial_fill_exchange::on_best_ask_update(int64_t prev_best_tick, int64_t new_best_tick) {
    // 如果最佳卖价下降，检查是否有买单可以成交
    if (new_best_tick < prev_best_tick) {
        // 选择更高效的计算路径
        if (prev_best_tick == std::numeric_limits<int64_t>::max() ||
            orders_.size() < prev_best_tick - new_best_tick) {
            // 遍历所有订单
            for (auto& [order_id, order] : orders_) {
                if (order.side == side_type::buy &&
                    (order.status == order_status::new_order || order.status == order_status::partially_filled) &&
                    depth_->price_to_tick(order.price) >= new_best_tick) {
                    filled_orders_.push_back(order.order_id);
                    fill(order, order.price, order.quantity - order.executed_quantity, true);
                    send_order_report(order);
                }
            }
        } else {
            // 只遍历可能成交的价格层
            for (int64_t t = new_best_tick; t < prev_best_tick; ++t) {
                auto it = buy_orders_.find(t);
                if (it != buy_orders_.end()) {
                    for (int64_t order_id : it->second) {
                        auto order_it = orders_.find(order_id);
                        if (order_it != orders_.end() &&
                            (order_it->second.status == order_status::new_order ||
                             order_it->second.status == order_status::partially_filled)) {
                            filled_orders_.push_back(order_id);
                            fill(order_it->second, order_it->second.price,
                                 order_it->second.quantity - order_it->second.executed_quantity, true);
                            send_order_report(order_it->second);
                        }
                    }
                }
            }
        }

        // 移除已成交订单
        remove_filled_orders();
    }
}

void partial_fill_exchange::on_bid_qty_change(int64_t price_tick, double prev_qty, double new_qty) {
    // 更新买单队列位置
    auto it = buy_orders_.find(price_tick);
    if (it != buy_orders_.end()) {
        for (int64_t order_id : it->second) {
            auto order_it = orders_.find(order_id);
            if (order_it != orders_.end() &&
                (order_it->second.status == order_status::new_order ||
                 order_it->second.status == order_status::partially_filled)) {
                queue_model_->update_depth(order_it->second, prev_qty, new_qty, depth_);
            }
        }
    }
}

void partial_fill_exchange::on_ask_qty_change(int64_t price_tick, double prev_qty, double new_qty) {
    // 更新卖单队列位置
    auto it = sell_orders_.find(price_tick);
    if (it != sell_orders_.end()) {
        for (int64_t order_id : it->second) {
            auto order_it = orders_.find(order_id);
            if (order_it != orders_.end() &&
                (order_it->second.status == order_status::new_order ||
                 order_it->second.status == order_status::partially_filled)) {
                queue_model_->update_depth(order_it->second, prev_qty, new_qty, depth_);
            }
        }
    }
}

} // namespace cpp_backtest
} // namespace fast_trader_elite
