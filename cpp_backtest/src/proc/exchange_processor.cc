#include <fast_trader_elite/cpp_backtest/proc/exchange_processor.h>
#include <fast_trader_elite/cpp_backtest/proc/partial_fill_exchange.h>
#include <fast_trader_elite/cpp_backtest/backtest_logger.h>
#include <iostream>
#include <cstring>
#include <limits>

namespace fast_trader_elite {
namespace cpp_backtest {

// 前向声明
class partial_fill_exchange;

exchange_processor::exchange_processor(market_depth* depth, fee_model* fee_model, queue_model* queue_model, latency_model* order_latency)
    : depth_(depth)
    , fee_model_(fee_model)
    , queue_model_(queue_model)
    , order_latency_(order_latency)
    , state_(10000.0) { // 初始余额为10000
}


backtest_error exchange_processor::process(const record_header& header, const std::vector<uint8_t>& data) {
    // 更新当前时间戳
    set_current_timestamp(header.exch_timestamp);

    // 根据记录类型处理
    switch (header.record_type) {
        case DEPTH_MARKET_DATA: {
            if (data.size() < sizeof(depth_market_data_field)) {
                return backtest_error::invalid_data;
            }
            // 直接使用span数据指针，避免复制
            const auto* depth = reinterpret_cast<const depth_market_data_field*>(data.data());
            process_depth(depth);
            break;
        }
        case TRANSACTION_DATA: {
            if (data.size() < sizeof(transaction_field)) {
                return backtest_error::invalid_data;
            }
            // 直接使用span数据指针，避免复制
            const auto* trans = reinterpret_cast<const transaction_field*>(data.data());
            process_transaction(trans);
            break;
        }
        case KLINE_DATA: {
            if (data.size() < sizeof(kline_market_data_field)) {
                return backtest_error::invalid_data;
            }
            // 直接使用span数据指针，避免复制
            const auto* kline = reinterpret_cast<const kline_market_data_field*>(data.data());
            process_kline(kline);
            break;
        }
        default:
            return backtest_error::invalid_data;
    }

    return backtest_error::none;
}

backtest_error exchange_processor::process_recv_order(int64_t timestamp) {

    // 更新当前时间戳
    set_current_timestamp(timestamp);

    // 处理订单
    if (orders_from_ != nullptr) {
        while (!orders_from_->empty()) {
            auto earliest_ts = orders_from_->earliest_timestamp();
            if (!earliest_ts.has_value() || earliest_ts.value() > timestamp) {
                break;
            }

            auto order_pair = orders_from_->pop_front();
            if (!order_pair.has_value()) {
                break;
            }

            const auto& [order, order_ts] = order_pair.value();

            // 记录订单信息
            BT_LOG_DEBUG("exchange_recv_order order_id:{} instrument_idx:{} side:{} type:{} price:{} quantity:{} request:{} timestamp:{}",
                        order.order_id, order.instrument_idx,
                        static_cast<int>(order.side), static_cast<int>(order.type),
                        order.price, order.quantity,
                        static_cast<int>(order.request), order_ts);

            // 处理订单，不再直接返回错误
            process_order(order);
        }
    }

    return backtest_error::none;
}

int64_t exchange_processor::earliest_recv_order_timestamp() const {
    if (orders_from_ == nullptr) {
        return std::numeric_limits<int64_t>::max();
    }
    return orders_from_->earliest_timestamp().value_or(std::numeric_limits<int64_t>::max());
}

int64_t exchange_processor::earliest_send_order_timestamp() const {
    if (orders_to_ == nullptr) {
        return std::numeric_limits<int64_t>::max();
    }
    return orders_to_->earliest_timestamp().value_or(std::numeric_limits<int64_t>::max());
}

void exchange_processor::process_depth(const depth_market_data_field* depth) {
    // 保存旧的最佳价格
    int64_t prev_best_bid_tick = depth_->best_bid_tick();
    int64_t prev_best_ask_tick = depth_->best_ask_tick();

    // 只有在需要跟踪数量变化时才保存旧的价格层数量
    std::unordered_map<int64_t, double> prev_bid_qty;
    std::unordered_map<int64_t, double> prev_ask_qty;

    bool track_qty_changes = needs_qty_change_tracking();

    if (track_qty_changes) {
        // 获取当前所有价格层的数量
        for (int i = 0; i < depth->depth; ++i) {
            if (depth->bid_volume[i] > 0.0) {
                int64_t price_tick = depth_->price_to_tick(depth->bid_price[i]);
                prev_bid_qty[price_tick] = depth_->bid_qty(price_tick);
            }
            if (depth->ask_volume[i] > 0.0) {
                int64_t price_tick = depth_->price_to_tick(depth->ask_price[i]);
                prev_ask_qty[price_tick] = depth_->ask_qty(price_tick);
            }
        }
    }

    // 更新深度数据
    auto* order_book_ptr = dynamic_cast<order_book*>(depth_);
    if (order_book_ptr) {
        order_book_ptr->update_from_snapshot(depth, current_timestamp_);
    }

    // 检查最佳价格是否变化
    int64_t new_best_bid_tick = depth_->best_bid_tick();
    int64_t new_best_ask_tick = depth_->best_ask_tick();

    // 如果最佳价格变化，触发相应事件
    if (prev_best_bid_tick != new_best_bid_tick) {
        on_best_bid_update(prev_best_bid_tick, new_best_bid_tick);
    }

    if (prev_best_ask_tick != new_best_ask_tick) {
        on_best_ask_update(prev_best_ask_tick, new_best_ask_tick);
    }

    // 只有在需要跟踪数量变化时才检查价格层数量变化
    if (track_qty_changes) {
        // 检查买盘数量变化
        for (int i = 0; i < depth->depth; ++i) {
            if (depth->bid_volume[i] > 0.0) {
                int64_t price_tick = depth_->price_to_tick(depth->bid_price[i]);
                double new_qty = depth->bid_volume[i];
                double prev_qty = prev_bid_qty.count(price_tick) ? prev_bid_qty[price_tick] : 0.0;

                if (prev_qty != new_qty) {
                    on_bid_qty_change(price_tick, prev_qty, new_qty);
                }
            }
        }

        // 检查卖盘数量变化
        for (int i = 0; i < depth->depth; ++i) {
            if (depth->ask_volume[i] > 0.0) {
                int64_t price_tick = depth_->price_to_tick(depth->ask_price[i]);
                double new_qty = depth->ask_volume[i];
                double prev_qty = prev_ask_qty.count(price_tick) ? prev_ask_qty[price_tick] : 0.0;

                if (prev_qty != new_qty) {
                    on_ask_qty_change(price_tick, prev_qty, new_qty);
                }
            }
        }
    }
}



void exchange_processor::process_kline(const kline_market_data_field* kline) {
    // 交易所处理器不需要处理K线数据
}

void exchange_processor::send_order_report(const internal_order& order) {
    // 计算订单响应延迟
    int64_t response_latency = order_latency_->response(current_timestamp_, order);

    // 发送订单回报
    int64_t local_recv_timestamp = current_timestamp_ + response_latency;

    // 记录订单回报信息
    BT_LOG_DEBUG("exchange_send_order_report order_id:{} instrument_idx:{} status:{} executed_qty:{} executed_price:{} fee:{} is_maker:{} timestamp:{}",
                order.order_id, order.instrument_idx,
                static_cast<int>(order.status), order.executed_quantity,
                order.executed_price, order.fee,
                order.is_maker, local_recv_timestamp);

    if (order.status == order_status::filled || order.status == order_status::partially_filled) {
        BT_LOG_DEBUG("exchange_order_filled order_id:{} instrument_idx:{} side:{} executed_qty:{} executed_price:{} fee:{} is_maker:{} timestamp:{}",
                   order.order_id, order.instrument_idx,
                   static_cast<int>(order.side), order.executed_quantity,
                   order.executed_price, order.fee,
                   order.is_maker, local_recv_timestamp);
    }

    if (orders_to_ != nullptr) {
        orders_to_->append(order, local_recv_timestamp);
    }
}

} // namespace cpp_backtest
} // namespace fast_trader_elite
