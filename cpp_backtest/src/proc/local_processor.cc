#include <fast_trader_elite/cpp_backtest/proc/local_processor.h>
#include <fast_trader_elite/cpp_backtest/backtest_logger.h>
#include <iostream>
#include <cstring>
#include <limits>

namespace fast_trader_elite {
namespace cpp_backtest {

local_processor::local_processor(market_depth* depth, latency_model* order_latency)
    : depth_(depth)
    , order_latency_(order_latency)
    , strategy_(nullptr)
    , ctx_(nullptr)
    , next_order_id_(1) {
}


backtest_error local_processor::process(const record_header& header, const std::vector<uint8_t>& data) {
    // 更新当前时间戳
    set_current_timestamp(header.local_timestamp);

    // 根据记录类型处理
    switch (header.record_type) {
        case DEPTH_MARKET_DATA: {
            if (data.size() < sizeof(depth_market_data_field)) {
                return backtest_error::invalid_data;
            }
            // 直接使用span数据指针，避免复制
            const auto* depth = reinterpret_cast<const depth_market_data_field*>(data.data());
            process_depth(depth);
            break;
        }
        case TRANSACTION_DATA: {
            if (data.size() < sizeof(transaction_field)) {
                return backtest_error::invalid_data;
            }
            // 直接使用span数据指针，避免复制
            const auto* trans = reinterpret_cast<const transaction_field*>(data.data());
            process_transaction(trans);
            break;
        }
        case KLINE_DATA: {
            if (data.size() < sizeof(kline_market_data_field)) {
                return backtest_error::invalid_data;
            }
            // 直接使用span数据指针，避免复制
            const auto* kline = reinterpret_cast<const kline_market_data_field*>(data.data());
            process_kline(kline);
            break;
        }
        default:
            return backtest_error::invalid_data;
    }

    return backtest_error::none;
}

backtest_error local_processor::process_recv_order(int64_t timestamp) {

    // 更新当前时间戳
    set_current_timestamp(timestamp);

    // 处理订单回报
    if (orders_from_ != nullptr) {
        while (!orders_from_->empty()) {
            auto earliest_ts = orders_from_->earliest_timestamp();
            if (!earliest_ts.has_value() || earliest_ts.value() > timestamp) {
                break;
            }

            auto order_pair = orders_from_->pop_front();
            if (!order_pair.has_value()) {
                break;
            }

            const auto& [order, order_ts] = order_pair.value();
            process_order_report(order);
        }
    }

    return backtest_error::none;
}

int64_t local_processor::earliest_recv_order_timestamp() const {
    if (orders_from_ == nullptr) {
        return std::numeric_limits<int64_t>::max();
    }
    return orders_from_->earliest_timestamp().value_or(std::numeric_limits<int64_t>::max());
}

int64_t local_processor::earliest_send_order_timestamp() const {
    if (orders_to_ == nullptr) {
        return std::numeric_limits<int64_t>::max();
    }
    return orders_to_->earliest_timestamp().value_or(std::numeric_limits<int64_t>::max());
}

int64_t local_processor::submit_order(const order_input_field* input, int64_t current_timestamp) {
    // 更新当前时间戳
    set_current_timestamp(current_timestamp);

    // 生成订单ID
    int64_t order_id = next_order_id_++;

    // 创建内部订单
    internal_order order = internal_order::from_order_input(*input, order_id);
    order.local_timestamp = current_timestamp;

    // 记录订单提交信息
    BT_LOG_DEBUG("local_submit_order order_id:{} instrument_idx:{} side:{} type:{} price:{} quantity:{} timestamp:{}",
                order_id, order.instrument_idx,
                static_cast<int>(order.side), static_cast<int>(order.type),
                order.price, order.quantity, current_timestamp);

    // 保存订单
    orders_[order_id] = order;

    // 计算订单延迟
    int64_t order_entry_latency = order_latency_->entry(current_timestamp, order);

    // 负延迟表示订单因技术原因被拒绝
    if (order_entry_latency < 0) {
        // 拒绝订单
        order.request = order_status::rejected;
        int64_t rej_recv_timestamp = current_timestamp - order_entry_latency;
        BT_LOG_WARN("local_order_rejected order_id:{} instrument_idx:{} reason:latency timestamp:{}",
                   order_id, order.instrument_idx, rej_recv_timestamp);
        if (orders_from_ != nullptr) {
            orders_from_->append(order, rej_recv_timestamp);
        }
    } else {
        // 发送订单到交易所
        int64_t exch_recv_timestamp = current_timestamp + order_entry_latency;
        if (orders_to_ != nullptr) {
            orders_to_->append(order, exch_recv_timestamp);
        }
    }

    return order_id;
}

int64_t local_processor::cancel_order(const order_action_field* action, int64_t current_timestamp) {
    // 安全检查：确保action不为空
    if (action == nullptr) {
        BT_LOG_ERROR("local_cancel_order_failed reason:null_action_pointer");
        return -1;
    }

    // 更新当前时间戳
    set_current_timestamp(current_timestamp);

    // 安全检查：确保order_id有效
    if (action->order_id <= 0) {
        BT_LOG_WARN("local_cancel_order_failed order_id:{} reason:invalid_order_id", action->order_id);
        return -1;
    }

    // 查找订单
    auto it = orders_.find(action->order_id);
    if (it == orders_.end()) {
        BT_LOG_WARN("local_cancel_order_failed order_id:{} reason:order_not_found", action->order_id);
        return -1;
    }

    // 获取订单
    internal_order& order = it->second;

    // 检查订单状态
    if (order.status == order_status::filled ||
        order.status == order_status::canceled ||
        order.status == order_status::rejected) {
        BT_LOG_WARN("local_cancel_order_failed order_id:{} reason:invalid_status status:{}",
                   action->order_id, static_cast<int>(order.status));
        return -1;
    }

    // 记录取消订单信息
    BT_LOG_DEBUG("local_cancel_order order_id:{} instrument_idx:{} timestamp:{}",
                action->order_id, order.instrument_idx, current_timestamp);

    // 设置取消请求
    order.request = order_status::canceled;

    // 安全检查：确保order_latency_不为空
    if (order_latency_ == nullptr) {
        BT_LOG_ERROR("local_cancel_order_failed order_id:{} reason:null_latency_model", action->order_id);
        return -1;
    }

    // 计算订单延迟
    int64_t order_entry_latency = order_latency_->entry(current_timestamp, order);

    // 负延迟表示订单因技术原因被拒绝
    if (order_entry_latency < 0) {
        // 拒绝订单
        order.request = order_status::rejected;
        int64_t rej_recv_timestamp = current_timestamp - order_entry_latency;
        BT_LOG_WARN("local_cancel_rejected order_id:{} reason:latency timestamp:{}",
                   action->order_id, rej_recv_timestamp);
        if (orders_from_ != nullptr) {
            orders_from_->append(order, rej_recv_timestamp);
        }
    } else {
        // 发送订单到交易所
        int64_t exch_recv_timestamp = current_timestamp + order_entry_latency;
        if (orders_to_ != nullptr) {
            orders_to_->append(order, exch_recv_timestamp);
        }
    }

    return action->order_id;
}

void local_processor::set_strategy(fast_trader_elite::strategy::i_strategy* strategy,
                                 fast_trader_elite::strategy::i_strategy_ctx* ctx) {
    strategy_ = strategy;
    ctx_ = ctx;
}

void local_processor::process_depth(const depth_market_data_field* depth) {
    // 触发策略回调
    if (strategy_ && ctx_) {
        strategy_->on_depth_data(ctx_, const_cast<depth_market_data_field*>(depth));
    }
}

void local_processor::process_transaction(const transaction_field* trans) {
    // 触发策略回调
    if (strategy_ && ctx_) {
        strategy_->on_transaction_data(ctx_, const_cast<transaction_field*>(trans));
    }
}

void local_processor::process_kline(const kline_market_data_field* kline) {
    // 触发策略回调
    if (strategy_ && ctx_) {
        strategy_->on_kline_data(ctx_, const_cast<kline_market_data_field*>(kline));
    }
}

void local_processor::process_order_report(const internal_order& order) {
    // 记录订单回报信息
    BT_LOG_DEBUG("local_process_order_report order_id:{} instrument_idx:{} status:{} executed_qty:{} executed_price:{} timestamp:{}",
                order.order_id, order.instrument_idx,
                static_cast<int>(order.status), order.executed_quantity,
                order.executed_price, current_timestamp_);

    // 查找订单
    auto it = orders_.find(order.order_id);
    if (it == orders_.end()) {
        // 如果订单不存在，则添加
        orders_[order.order_id] = order;
    } else {
        // 更新订单
        it->second.update(order);
    }

    // 触发订单回调
    trigger_order_callback(order);

    // 如果订单成交，触发成交回调并添加到最近交易列表
    if (order.status == order_status::filled || order.status == order_status::partially_filled) {
        // 创建成交记录
        internal_trade trade = internal_trade::from_order(order, order.executed_quantity);

        // 获取当前状态值（成交前）
        const state_values& values_before = state_.get_values(order.instrument_idx);

        // 应用成交到状态
        state_.apply_fill(order);

        // 获取更新后的状态值（成交后）
        const state_values& values_after = state_.get_values(order.instrument_idx);

        // 设置交易金额
        trade.trading_value = order.executed_quantity * order.executed_price;

        // 记录成交信息
        BT_LOG_DEBUG("local_trade_executed order_id:{} instrument_idx:{} side:{} executed_qty:{} executed_price:{} fee:{} position_before:{} position_after:{} timestamp:{}",
                   order.order_id, order.instrument_idx,
                   static_cast<int>(order.side), order.executed_quantity,
                   order.executed_price, order.fee,
                   values_before.position, values_after.position,
                   current_timestamp_);

        // 添加到最近交易列表
        last_trades_.push_back(trade);

        // 如果列表过长，移除最旧的交易
        const size_t MAX_TRADES_CAPACITY = 1000;
        if (last_trades_.size() > MAX_TRADES_CAPACITY) {
            last_trades_.erase(last_trades_.begin());
        }

        // 触发成交回调
        trigger_trade_callback(trade);
    }
}

void local_processor::trigger_order_callback(const internal_order& order) {
    if (strategy_ && ctx_) {
        order_field order_field = order.to_order_field();
        strategy_->on_order(ctx_, &order_field);
    }
}

void local_processor::trigger_trade_callback(const internal_trade& trade) {
    if (strategy_ && ctx_) {
        trade_field trade_field = trade.to_trade_field();
        strategy_->on_trade(ctx_, &trade_field);
    }
}

void local_processor::process_get_kline(const md_kline_req_field* f, int request_id) {
    // 创建一个空的K线数据结构体
    kline_market_data_field kline;
    memset(&kline, 0, sizeof(kline));

    // 复制品种信息
    strncpy(kline.instrument_name, f->instrument_name, INSTRUMENT_ID_LEN - 1);
    strncpy(kline.instrument_id, f->instrument_id, INSTRUMENT_ID_LEN - 1);
    kline.instrument_idx = f->instrument_idx;
    kline.exchange_id = f->exchange_id;
    kline.ins_type = f->ins_type;

    // 设置时间戳
    kline.local_timestamp = current_timestamp_;

    // 如果策略存在，调用HTTP K线回调
    if (strategy_ && ctx_) {
        strategy_->on_http_kline_data(ctx_, &kline, true);
    }

    // 创建HTTP响应
    http_rsp_field http_rsp;
    memset(&http_rsp, 0, sizeof(http_rsp));
    http_rsp.request_id = request_id;
    http_rsp.req_type = req_type_type::REQ_GET_KLINE;
    http_rsp.error_id = 0;

    // 如果策略存在，调用HTTP响应回调
    if (strategy_ && ctx_) {
        strategy_->on_http_rsp(ctx_, &http_rsp);
    }
}

void local_processor::process_get_recent_transaction(const transaction_req_field* f, int request_id) {
    // 创建一个空的成交数据结构体
    transaction_field trans;
    memset(&trans, 0, sizeof(trans));

    // 复制品种信息
    strncpy(trans.instrument_name, f->instrument_name, INSTRUMENT_ID_LEN - 1);
    strncpy(trans.instrument_id, f->instrument_id, INSTRUMENT_ID_LEN - 1);
    trans.instrument_idx = f->instrument_idx;
    trans.exchange_id = f->exchange_id;
    trans.ins_type = f->ins_type;

    // 设置时间戳
    trans.local_timestamp = current_timestamp_;

    // 如果策略存在，调用HTTP成交回调
    if (strategy_ && ctx_) {
        strategy_->on_http_transaction_data(ctx_, &trans, true);
    }

    // 创建HTTP响应
    http_rsp_field http_rsp;
    memset(&http_rsp, 0, sizeof(http_rsp));
    http_rsp.request_id = request_id;
    http_rsp.req_type = req_type_type::REQ_GET_TRANS;
    http_rsp.error_id = 0;

    // 如果策略存在，调用HTTP响应回调
    if (strategy_ && ctx_) {
        strategy_->on_http_rsp(ctx_, &http_rsp);
    }
}

void local_processor::process_get_position(const position_req_field* f, int request_id) {
    // 创建一个空的持仓数据结构体
    position_field position;
    memset(&position, 0, sizeof(position));

    // 复制品种信息
    strncpy(position.instrument_name, f->instrument_name, INSTRUMENT_ID_LEN - 1);
    strncpy(position.instrument_id, f->instrument_id, INSTRUMENT_ID_LEN - 1);
    position.instrument_idx = f->instrument_idx;
    position.ins_type = f->ins_type;
    position.trading_account_id = f->trading_account_id;

    // 设置持仓数据
    position.long_position = 0;
    position.short_position = 0;
    position.frozen_position = 0;
    position.net_position = 0;
    position.avg_price = 0;

    // 如果策略存在，调用持仓回调
    if (strategy_ && ctx_) {
        strategy_->on_rtn_position(ctx_, &position);
    }

    // 创建HTTP响应
    http_rsp_field http_rsp;
    memset(&http_rsp, 0, sizeof(http_rsp));
    http_rsp.request_id = request_id;
    http_rsp.req_type = req_type_type::REQ_POS;
    http_rsp.error_id = 0;

    // 如果策略存在，调用HTTP响应回调
    if (strategy_ && ctx_) {
        strategy_->on_http_rsp(ctx_, &http_rsp);
    }
}

void local_processor::process_get_wallet_balance(const wallet_balance_req_field* f, int request_id) {
    // 创建一个空的钱包余额数据结构体
    wallet_balance_field wallet_balance;
    memset(&wallet_balance, 0, sizeof(wallet_balance));

    // 设置账户ID
    wallet_balance.trading_account_id = f->trading_account_id;

    // 设置钱包余额数据
    wallet_balance.wallet_balance = 10000.0; // 默认余额
    wallet_balance.equity = 10000.0;         // 默认权益

    // 如果策略存在，调用钱包余额回调
    if (strategy_ && ctx_) {
        strategy_->on_rtn_wallet_balance(ctx_, &wallet_balance);
    }

    // 创建HTTP响应
    http_rsp_field http_rsp;
    memset(&http_rsp, 0, sizeof(http_rsp));
    http_rsp.request_id = request_id;
    http_rsp.req_type = req_type_type::REQ_WALLET_BALANCE;
    http_rsp.error_id = 0;

    // 如果策略存在，调用HTTP响应回调
    if (strategy_ && ctx_) {
        strategy_->on_http_rsp(ctx_, &http_rsp);
    }
}

} // namespace cpp_backtest
} // namespace fast_trader_elite
