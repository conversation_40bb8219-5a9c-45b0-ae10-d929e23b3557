#include <fast_trader_elite/cpp_backtest/models/model_factory.h>

namespace fast_trader_elite {
namespace cpp_backtest {

std::unique_ptr<latency_model> latency_model_factory::create(
    const std::string& type,
    int64_t order_latency,
    int64_t response_latency,
    int64_t md_latency) {
    
    // 目前只支持常数延迟模型，未来可以扩展其他类型
    if (type == "constant" || type.empty()) {
        return std::make_unique<constant_latency_model>(
            order_latency, response_latency, md_latency);
    }
    
    // 默认返回常数延迟模型
    return std::make_unique<constant_latency_model>(
        order_latency, response_latency, md_latency);
}

std::unique_ptr<queue_model> queue_model_factory::create(
    const std::string& type,
    double queue_power) {
    
    if (type == "simple") {
        return std::make_unique<risk_adverse_queue_model>();
    } else if (type == "prob") {
        auto prob = std::make_unique<power_prob_queue_func>(queue_power);
        return std::make_unique<prob_queue_model>(std::move(prob));
    } else if (type == "log") {
        auto prob = std::make_unique<log_prob_queue_func>();
        return std::make_unique<prob_queue_model>(std::move(prob));
    }
    
    // 默认使用简单队列模型
    return std::make_unique<risk_adverse_queue_model>();
}

std::unique_ptr<fee_model> fee_model_factory::create(
    const std::string& type,
    double maker_fee,
    double taker_fee) {
    
    // 目前只支持固定手续费模型，未来可以扩展其他类型
    if (type == "fixed_fee" || type.empty()) {
        return std::make_unique<fixed_fee_model>(maker_fee, taker_fee);
    }
    
    // 默认返回固定手续费模型
    return std::make_unique<fixed_fee_model>(maker_fee, taker_fee);
}

} // namespace cpp_backtest
} // namespace fast_trader_elite
