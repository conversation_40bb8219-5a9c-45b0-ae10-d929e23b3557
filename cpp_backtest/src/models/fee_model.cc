#include <fast_trader_elite/cpp_backtest/models/fee_model.h>

namespace fast_trader_elite {
namespace cpp_backtest {

fixed_fee_model::fixed_fee_model(double maker_fee_rate, double taker_fee_rate)
    : maker_fee_rate_(maker_fee_rate)
    , taker_fee_rate_(taker_fee_rate) {
}

double fixed_fee_model::calculate_fee(double price, double quantity, bool is_maker) const {
    double fee_rate = is_maker ? maker_fee_rate_ : taker_fee_rate_;
    return price * quantity * fee_rate;
}

} // namespace cpp_backtest
} // namespace fast_trader_elite
