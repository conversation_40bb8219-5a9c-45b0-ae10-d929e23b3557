#include <fast_trader_elite/cpp_backtest/models/queue_model.h>
#include <cmath>
#include <random>
#include <cassert>

namespace fast_trader_elite {
namespace cpp_backtest {

// risk_adverse_queue_model 实现

void risk_adverse_queue_model::new_order(internal_order& order, const market_depth* depth) {
    double front_qty = 0.0;
    if (order.side == side_type::buy) {
        front_qty = depth->bid_qty(depth->price_to_tick(order.price));
    } else {
        front_qty = depth->ask_qty(depth->price_to_tick(order.price));
    }

    // 存储前方队列数量
    order.queue_data.front_qty = front_qty;
    order.queue_data.cum_trade_qty = 0.0;
}

void risk_adverse_queue_model::update_depth(internal_order& order, double prev_qty, double new_qty, const market_depth* depth) {
    // 取前方队列数量和新数量的最小值
    order.queue_data.front_qty = std::min(order.queue_data.front_qty, new_qty);
}

void risk_adverse_queue_model::trade(internal_order& order, double qty, const market_depth* depth) {
    // 减去成交数量
    order.queue_data.front_qty -= qty;
}

double risk_adverse_queue_model::is_filled(const internal_order& order, const market_depth* depth) const {
    if ((order.queue_data.front_qty / depth->lot_size()) < 0.0) {
        return std::floor(-order.queue_data.front_qty / depth->lot_size()) * depth->lot_size();
    } else {
        return 0.0;
    }
}

// prob_queue_model 实现

void prob_queue_model::new_order(internal_order& order, const market_depth* depth) {
    if (order.side == side_type::buy) {
        order.queue_data.front_qty = depth->bid_qty(depth->price_to_tick(order.price));
    } else {
        order.queue_data.front_qty = depth->ask_qty(depth->price_to_tick(order.price));
    }

    order.queue_data.cum_trade_qty = 0.0;
}

void prob_queue_model::trade(internal_order& order, double qty, const market_depth* depth) {
    // 减去成交数量并累计成交数量
    order.queue_data.front_qty -= qty;
    order.queue_data.cum_trade_qty += qty;
}

void prob_queue_model::update_depth(internal_order& order, double prev_qty, double new_qty, const market_depth* depth) {
    // 计算数量变化
    double chg = prev_qty - new_qty;

    // 为了避免重复调整队列位置，减去由成交引起的队列位置变化
    chg -= order.queue_data.cum_trade_qty;

    // 重置累计成交数量，因为成交引起的数量变化应该已经反映在数量中
    order.queue_data.cum_trade_qty = 0.0;

    // 对于数量增加，前方队列不会因为数量变化而变化
    if (chg < 0.0) {
        order.queue_data.front_qty = std::min(order.queue_data.front_qty, new_qty);
        return;
    }

    // 计算前后队列数量
    double front = order.queue_data.front_qty;
    double back = prev_qty - front;

    // 计算概率
    double prob_val = prob_->prob(front, back);
    if (std::isinf(prob_val)) {
        prob_val = 1.0;
    }

    // 估计新的前方队列数量
    double est_front = front - (1.0 - prob_val) * chg + std::min(back - prob_val * chg, 0.0);
    order.queue_data.front_qty = std::min(est_front, new_qty);
}

double prob_queue_model::is_filled(const internal_order& order, const market_depth* depth) const {
    if ((order.queue_data.front_qty / depth->lot_size()) < 0.0) {
        return std::floor(-order.queue_data.front_qty / depth->lot_size()) * depth->lot_size();
    } else {
        return 0.0;
    }
}

// fixed_prob_queue_model 实现

void fixed_prob_queue_model::new_order(internal_order& order, const market_depth* depth) {
    if (order.side == side_type::buy) {
        order.queue_data.front_qty = depth->bid_qty(depth->price_to_tick(order.price));
    } else {
        order.queue_data.front_qty = depth->ask_qty(depth->price_to_tick(order.price));
    }

    order.queue_data.cum_trade_qty = 0.0;
}

void fixed_prob_queue_model::update_depth(internal_order& order, double prev_qty, double new_qty, const market_depth* depth) {
    // 取前方队列数量和新数量的最小值
    order.queue_data.front_qty = std::min(order.queue_data.front_qty, new_qty);
}

void fixed_prob_queue_model::trade(internal_order& order, double qty, const market_depth* depth) {
    // 减去成交数量
    order.queue_data.front_qty -= qty;
}

double fixed_prob_queue_model::is_filled(const internal_order& order, const market_depth* depth) const {
    // 如果前方队列数量小于0，表示订单到达队列前端
    if (order.queue_data.front_qty <= 0.0) {
        // 生成一个0-1之间的随机数
        std::uniform_real_distribution<double> dist(0.0, 1.0);
        double random_value = dist(rng_);

        // 如果随机数小于成交概率，则订单成交
        if (random_value < prob_value_) {
            return order.leaves_quantity;
        }
    }

    return 0.0;
}

} // namespace cpp_backtest
} // namespace fast_trader_elite
