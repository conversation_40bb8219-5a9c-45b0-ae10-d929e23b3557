#include <fast_trader_elite/cpp_backtest/models/latency_model.h>

namespace fast_trader_elite {
namespace cpp_backtest {

constant_latency_model::constant_latency_model(int64_t entry_latency, int64_t response_latency, int64_t market_latency)
    : entry_latency_(entry_latency)
    , response_latency_(response_latency)
    , market_latency_(market_latency) {
}

int64_t constant_latency_model::entry(int64_t timestamp, const internal_order& order) const {
    return entry_latency_;
}

int64_t constant_latency_model::response(int64_t timestamp, const internal_order& order) const {
    return response_latency_;
}

int64_t constant_latency_model::market(int64_t timestamp) const {
    return market_latency_;
}

} // namespace cpp_backtest
} // namespace fast_trader_elite
