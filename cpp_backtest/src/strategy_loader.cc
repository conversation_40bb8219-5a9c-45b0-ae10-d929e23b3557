#include <fast_trader_elite/cpp_backtest/backtest_engine.h>
#include <iostream>
#include <dlfcn.h>

namespace fast_trader_elite {
namespace cpp_backtest {

strategy_loader::strategy_loader()
    : handle_(nullptr)
    , create_func_(nullptr)
    , destroy_func_(nullptr)
    , strategy_(nullptr) {
}

strategy_loader::~strategy_loader() {
    if (strategy_ && destroy_func_) {
        destroy_func_(strategy_);
        strategy_ = nullptr;
    }

    if (handle_) {
        dlclose(handle_);
        handle_ = nullptr;
    }
}

bool strategy_loader::load(const std::string& strategy_path) {
    // 如果已经加载了策略，先卸载
    if (handle_) {
        if (strategy_ && destroy_func_) {
            destroy_func_(strategy_);
            strategy_ = nullptr;
        }

        dlclose(handle_);
        handle_ = nullptr;
        create_func_ = nullptr;
        destroy_func_ = nullptr;
    }

    // 加载策略插件
    handle_ = dlopen(strategy_path.c_str(), RTLD_LAZY);
    if (!handle_) {
        std::cerr << "Failed to load strategy: " << dlerror() << std::endl;
        return false;
    }

    // 获取创建函数
    create_func_ = (create_strategy_func)dlsym(handle_, "create_strategy");
    if (!create_func_) {
        // 尝试使用旧的函数名
        create_func_ = (create_strategy_func)dlsym(handle_, "create");
        if (!create_func_) {
            std::cerr << "Failed to get create_strategy function: " << dlerror() << std::endl;
            dlclose(handle_);
            handle_ = nullptr;
            return false;
        }
    }

    // 获取销毁函数
    destroy_func_ = (destroy_strategy_func)dlsym(handle_, "destroy_strategy");
    if (!destroy_func_) {
        // 尝试使用旧的函数名
        destroy_func_ = (destroy_strategy_func)dlsym(handle_, "destroy");
        if (!destroy_func_) {
            std::cerr << "Failed to get destroy_strategy function: " << dlerror() << std::endl;
            // 不是致命错误，可以继续
        }
    }

    // 创建策略实例
    strategy_ = create_func_();
    if (!strategy_) {
        std::cerr << "Failed to create strategy instance" << std::endl;
        dlclose(handle_);
        handle_ = nullptr;
        create_func_ = nullptr;
        destroy_func_ = nullptr;
        return false;
    }

    std::cout << "Strategy loaded successfully: " << strategy_path << std::endl;
    return true;
}

fast_trader_elite::strategy::i_strategy* strategy_loader::get_strategy() const {
    return strategy_;
}

} // namespace cpp_backtest
} // namespace fast_trader_elite
