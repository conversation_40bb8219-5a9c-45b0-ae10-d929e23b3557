#include <fast_trader_elite/cpp_backtest/position_cycle.h>
#include <cmath>
#include <numeric>

namespace fast_trader_elite {
namespace cpp_backtest {

std::vector<position_cycle> analyze_position_cycles(const std::vector<internal_trade>& trades) {
    std::vector<position_cycle> cycles;

    // 跟踪仓位和方向
    double current_position = 0.0;
    bool has_position = false;
    double position_direction = 0.0; // 1.0表示多头，-1.0表示空头
    position_cycle current_cycle;

    // 使用小阈值判断仓位是否为零
    constexpr double POSITION_EPSILON = 1e-10;

    for (const auto& trade : trades) {
        if (trade.status != order_status::filled &&
            trade.status != order_status::partially_filled) {
            continue;
        }

        bool prev_has_position = has_position;
        double prev_direction = position_direction;
        double trade_pnl = 0.0;

        // 更新仓位
        if (trade.side == side_type::buy) {
            current_position += trade.quantity;
        } else {
            current_position -= trade.quantity;
        }

        // 判断仓位状态，使用小阈值
        has_position = std::abs(current_position) > POSITION_EPSILON;

        // 更新仓位方向
        if (current_position > POSITION_EPSILON) {
            position_direction = 1.0; // 多头
        } else if (current_position < -POSITION_EPSILON) {
            position_direction = -1.0; // 空头
        } else {
            position_direction = 0.0; // 无仓位
        }

        // 处理仓位变化
        if (!prev_has_position && has_position) {
            // 新开仓
            // 创建新的仓位周期
            current_cycle = position_cycle();
            current_cycle.trades.push_back(trade);
            current_cycle.entry_price = trade.price;
            current_cycle.direction = (position_direction > 0) ? "long" : "short";
            current_cycle.start_time = trade.timestamp;
        } else if (prev_has_position) {
            // 添加交易到当前周期
            current_cycle.trades.push_back(trade);

            // 检查是否完全平仓或方向变化
            bool cycle_completed = false;
            bool direction_changed = false;

            // 如果仓位变为0或方向变化，则完成当前周期
            if (!has_position) {
                // 仓位变为0
                cycle_completed = true;
            } else if (prev_direction * position_direction < 0) {
                // 方向变化
                cycle_completed = true;
                direction_changed = true;
            }

            if (cycle_completed) {
                // 计算整个仓位周期的总仓位大小
                double total_position_size = 0.0;

                // 计算总开仓量和总平仓量
                double total_open_quantity = 0.0;

                for (const auto& t : current_cycle.trades) {
                    if ((current_cycle.direction == "long" && t.side == side_type::buy) ||
                        (current_cycle.direction == "short" && t.side == side_type::sell)) {
                        // 开仓交易
                        total_open_quantity += t.quantity;
                    } 
                }

                // 根据不同情况计算仓位大小
                if (direction_changed) {
                    // 方向变化，需要特殊处理
                    // 当从多头变为空头或从空头变为多头时，实际上只有一部分交易量是新方向的仓位
                    // 例如：从多头1变为空头1，实际上是平掉1个多头，再开1个空头
                    // 对于方向变化的情况，仓位大小应该是当前方向的实际仓位大小
                    // 例如：从多头1变为空头1，空头周期的仓位大小应该是1
                    total_position_size = std::abs(current_position);

                    std::cout << "方向变化后的仓位大小: " << total_position_size << std::endl;
                } else {
                    // 正常情况，使用总开仓量
                    total_position_size = total_open_quantity;
                }

                // 计算平均开仓价格和平均平仓价格
                double open_position = 0.0;
                double open_value = 0.0;
                double close_position = 0.0;
                double close_value = 0.0;

                for (const auto& t : current_cycle.trades) {
                    if ((current_cycle.direction == "long" && t.side == side_type::buy) ||
                        (current_cycle.direction == "short" && t.side == side_type::sell)) {
                        open_position += t.quantity;
                        open_value += t.quantity * t.price;
                    } else {
                        close_position += t.quantity;
                        close_value += t.quantity * t.price;
                    }
                }

                double avg_open_price = open_position > 0 ? open_value / open_position : trade.price;
                double avg_close_price = close_position > 0 ? close_value / close_position : trade.price;

                // 计算PNL
                if (current_cycle.direction == "long") {
                    // 多头周期：平仓价格 - 开仓价格
                    trade_pnl = (avg_close_price - avg_open_price) * total_position_size;
                } else {
                    // 空头周期：开仓价格 - 平仓价格
                    trade_pnl = (avg_open_price - avg_close_price) * total_position_size;
                }
                // 完成当前仓位周期
                current_cycle.exit_price = avg_close_price;

                // 设置仓位大小和PNL
                current_cycle.position_size = total_position_size;
                current_cycle.pnl = trade_pnl;

                current_cycle.is_win = (current_cycle.pnl > 0);
                current_cycle.end_time = trade.timestamp;
                current_cycle.duration_seconds = static_cast<double>(current_cycle.end_time - current_cycle.start_time) / 1e9;

                // 添加到周期列表
                cycles.push_back(current_cycle);

                // 如果还有仓位，创建新的仓位周期
                if (has_position) {
                    // 创建新周期
                    current_cycle = position_cycle();
                    current_cycle.trades.push_back(trade);
                    current_cycle.entry_price = trade.price;
                    current_cycle.direction = (position_direction > 0) ? "long" : "short";
                    current_cycle.start_time = trade.timestamp;
                }
            }
        }
    }

    return cycles;
}

cycle_statistics calculate_cycle_statistics(const std::vector<position_cycle>& cycles) {
    cycle_statistics stats = {};

    if (cycles.empty()) {
        return stats;
    }

    stats.total_cycles = cycles.size();

    double total_win_pnl = 0.0;
    double total_loss_pnl = 0.0;
    double total_duration = 0.0;

    for (const auto& cycle : cycles) {
        if (cycle.is_win) {
            stats.win_cycles++;
            total_win_pnl += cycle.pnl;
        } else {
            total_loss_pnl -= cycle.pnl;
        }

        stats.total_pnl += cycle.pnl;
        total_duration += cycle.duration_seconds;

        if (cycle.direction == "long") {
            stats.long_cycles++;
            stats.long_pnl += cycle.pnl;
            if (cycle.is_win) {
                stats.long_win_rate += 1.0;
            }
        } else {
            stats.short_cycles++;
            stats.short_pnl += cycle.pnl;
            if (cycle.is_win) {
                stats.short_win_rate += 1.0;
            }
        }
    }

    stats.win_rate = static_cast<double>(stats.win_cycles) / stats.total_cycles;
    stats.avg_pnl = stats.total_pnl / stats.total_cycles;
    stats.avg_win_pnl = stats.win_cycles > 0 ? total_win_pnl / stats.win_cycles : 0.0;
    stats.avg_loss_pnl = (stats.total_cycles - stats.win_cycles) > 0 ?
                         total_loss_pnl / (stats.total_cycles - stats.win_cycles) : 0.0;
    stats.profit_loss_ratio = stats.avg_loss_pnl > 0 ? stats.avg_win_pnl / stats.avg_loss_pnl : 0.0;
    stats.avg_duration = total_duration / stats.total_cycles;

    stats.long_win_rate = stats.long_cycles > 0 ? stats.long_win_rate / stats.long_cycles : 0.0;
    stats.short_win_rate = stats.short_cycles > 0 ? stats.short_win_rate / stats.short_cycles : 0.0;

    return stats;
}

} // namespace cpp_backtest
} // namespace fast_trader_elite
