#include <fast_trader_elite/cpp_backtest/data/binary_reader.h>
#include <iostream>
#include <cstring>
#include <span>

namespace fast_trader_elite {
namespace cpp_backtest {

binary_reader::binary_reader(const std::string& filename)
    : fd_(-1)
    , mapped_data_(nullptr)
    , file_size_(0)
    , current_record_(0)
    , data_start_pos_(0)
    , current_pos_(0) {
    open(filename);
}

binary_reader::~binary_reader() {
    close();
}

bool binary_reader::open(const std::string& filename) {
    // 关闭已打开的文件
    close();

    // 打开文件
    fd_ = ::open(filename.c_str(), O_RDONLY);
    if (fd_ == -1) {
        std::cerr << "Failed to open file: " << filename << std::endl;
        return false;
    }

    // 获取文件大小
    struct stat st;
    if (fstat(fd_, &st) == -1) {
        std::cerr << "Failed to get file size" << std::endl;
        close();
        return false;
    }
    file_size_ = st.st_size;

    // 内存映射文件
    mapped_data_ = mmap(nullptr, file_size_, PROT_READ, MAP_PRIVATE, fd_, 0);
    if (mapped_data_ == MAP_FAILED) {
        std::cerr << "Failed to mmap file" << std::endl;
        close();
        return false;
    }

    // 预取数据到CPU缓存，提高读取性能
    if (madvise(mapped_data_, file_size_, MADV_WILLNEED) != 0) {
        // 仅记录警告，不影响功能
        std::cerr << "Warning: madvise failed" << std::endl;
    }

    // 读取文件头
    if (file_size_ < sizeof(file_header)) {
        std::cerr << "File too small to contain header" << std::endl;
        close();
        return false;
    }
    std::memcpy(&file_header_, mapped_data_, sizeof(file_header));

    // 检查魔数
    if (std::strncmp(file_header_.magic, "FTEBACK", 7) != 0) {
        std::cerr << "Invalid file format" << std::endl;
        close();
        return false;
    }

    // 读取品种信息
    size_t pos = sizeof(file_header);
    instruments_.resize(file_header_.instrument_count);
    for (uint32_t i = 0; i < file_header_.instrument_count; ++i) {
        if (pos + sizeof(instrument_info) > file_size_) {
            std::cerr << "File too small to contain instrument info" << std::endl;
            close();
            return false;
        }
        std::memcpy(&instruments_[i], static_cast<char*>(mapped_data_) + pos, sizeof(instrument_info));
        pos += sizeof(instrument_info);
    }

    // 记录数据开始位置
    data_start_pos_ = pos;
    current_pos_ = pos;  // 初始化当前位置指针
    current_record_ = 0;

    return true;
}

void binary_reader::close() {
    if (mapped_data_ != nullptr && mapped_data_ != MAP_FAILED) {
        munmap(mapped_data_, file_size_);
        mapped_data_ = nullptr;
    }

    if (fd_ != -1) {
        ::close(fd_);
        fd_ = -1;
    }

    file_size_ = 0;
    current_record_ = 0;
    data_start_pos_ = 0;
    current_pos_ = 0;  // 重置当前位置指针
    instruments_.clear();
    current_data_span_ = std::span<const uint8_t>(); // 清空span
}

// 新的高效实现，使用std::span避免数据复制
bool binary_reader::next_record(record_header& header, std::span<const uint8_t>& data) {
    if (!is_open() || current_record_ >= file_header_.record_count) {
        return false;
    }

    // 直接使用当前位置指针，不需要重新计算
    size_t pos = current_pos_;

    // 读取记录头
    if (pos + sizeof(record_header) > file_size_) {
        std::cerr << "File too small to contain record header" << std::endl;
        return false;
    }
    std::memcpy(&header, static_cast<char*>(mapped_data_) + pos, sizeof(record_header));
    pos += sizeof(record_header);

    // 创建数据的span视图，不复制数据
    if (pos + header.data_size > file_size_) {
        std::cerr << "File too small to contain record data" << std::endl;
        return false;
    }

    // 创建指向内存映射区域的span
    data = std::span<const uint8_t>(
        static_cast<const uint8_t*>(mapped_data_) + pos,
        header.data_size
    );

    // 保存当前span以便后续使用
    current_data_span_ = data;

    // 更新当前位置指针和当前记录
    current_pos_ = pos + header.data_size;
    current_record_++;

    return true;
}

// 兼容旧接口，使用std::vector
bool binary_reader::next_record(record_header& header, std::vector<uint8_t>& data) {
    // 使用std::span版本实现
    std::span<const uint8_t> data_span;
    if (!next_record(header, data_span)) {
        return false;
    }

    // 将span中的数据复制到vector
    data.resize(data_span.size());
    std::memcpy(data.data(), data_span.data(), data_span.size());

    return true;
}

void binary_reader::stream_records(const record_callback& callback) {
    if (!is_open()) {
        return;
    }

    // 保存当前记录位置
    size_t saved_record = current_record_;
    size_t saved_pos = current_pos_;

    // 重置到开始位置
    reset();

    // 读取所有记录并调用回调函数
    record_header header;
    std::span<const uint8_t> data;

    while (next_record(header, data)) {
        callback(header, data);
    }

    // 恢复原来的记录位置
    current_record_ = saved_record;
    current_pos_ = saved_pos;
}

void binary_reader::reset() {
    current_record_ = 0;
    current_pos_ = data_start_pos_;  // 重置当前位置指针
}

const std::vector<instrument_info>& binary_reader::get_instruments() const {
    return instruments_;
}

const instrument_info& binary_reader::get_instrument() const {
    // 假设至少有一个品种
    static instrument_info empty_info{};
    if (instruments_.empty()) {
        return empty_info;
    }
    return instruments_[0]; // 返回第一个品种信息
}

const file_header& binary_reader::get_file_header() const {
    return file_header_;
}

bool binary_reader::is_open() const {
    return fd_ != -1 && mapped_data_ != nullptr && mapped_data_ != MAP_FAILED;
}

} // namespace cpp_backtest
} // namespace fast_trader_elite
