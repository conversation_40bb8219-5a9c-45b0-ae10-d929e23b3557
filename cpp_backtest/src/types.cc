#include <fast_trader_elite/cpp_backtest/types.h>
#include <fast_trader_elite/cpp_backtest/models/queue_model.h>
#include <cstring>

namespace fast_trader_elite {
namespace cpp_backtest {

// 内部订单结构构造函数
internal_order::internal_order()
    : order_id(0)
    , price(0.0)
    , quantity(0.0)
    , leaves_quantity(0.0)
    , executed_quantity(0.0)
    , executed_price(0.0)
    , side(side_type::none)
    , type(order_type::limit)
    , time_in_force(time_in_force_type::gtc)
    , status(order_status::none)
    , request(order_status::none)
    , exchange_timestamp(0)
    , local_timestamp(0)
    , instrument_idx(0)
    , is_maker(false)
    , fee(0.0)
    , error_id(0) {
    // 队列数据已在结构体定义中初始化
}

// 析构函数
internal_order::~internal_order() {
    // 不需要释放内存，因为队列数据现在是结构体的一部分
}

// 转换为FastTraderElite的order_field
order_field internal_order::to_order_field() const {
    order_field result;
    std::memset(&result, 0, sizeof(result));

    result.order_id = order_id;
    result.instrument_idx = instrument_idx;
    result.limit_price = price;
    result.volume = quantity;
    result.direction = convert_side(side);
    result.offset_flag = offset_type::NON;  // 使用NON作为默认值
    result.order_price_type = convert_order_type(type);

    // 设置订单状态
    result.order_status = convert_order_status_type(status);

    result.volume_traded = executed_quantity;
    result.volume_left = leaves_quantity;
    result.timestamp = exchange_timestamp;  // 使用交易所时间戳作为订单时间戳
    result.local_timestamp = local_timestamp;  // 使用本地时间戳作为本地时间戳
    result.error_id = error_id;

    return result;
}

// 从FastTraderElite的order_input_field创建
internal_order internal_order::from_order_input(const order_input_field& input, int64_t order_id) {
    internal_order result;

    result.order_id = order_id;
    result.price = input.price;
    result.quantity = input.volume;
    result.leaves_quantity = input.volume;
    result.executed_quantity = 0.0;
    result.executed_price = 0.0;
    result.side = convert_direction(input.direction);
    result.type = convert_order_price_type(input.pricetype);
    result.time_in_force = time_in_force_type::gtc;  // 默认为GTC
    result.status = order_status::new_order;
    result.request = order_status::new_order;
    result.instrument_idx = input.instrument_idx;
    result.is_maker = false;
    result.fee = 0.0;
    result.error_id = 0;

    return result;
}

// 更新订单
void internal_order::update(const internal_order& other) {
    status = other.status;
    request = other.request;
    leaves_quantity = other.leaves_quantity;
    executed_quantity = other.executed_quantity;
    executed_price = other.executed_price;
    exchange_timestamp = other.exchange_timestamp;
    is_maker = other.is_maker;
    fee = other.fee;
    error_id = other.error_id;

    // 复制队列数据
    queue_data = other.queue_data;
}

// 内部成交结构构造函数
internal_trade::internal_trade()
    : order_id(0)
    , price(0.0)
    , quantity(0.0)
    , side(side_type::none)
    , timestamp(0)
    , instrument_idx(0)
    , is_maker(false)
    , fee(0.0)
    , trading_value(0.0)
    , status(order_status::filled) {  // 默认设置为已成交状态
}

// 转换为FastTraderElite的trade_field
trade_field internal_trade::to_trade_field() const {
    trade_field result;
    std::memset(&result, 0, sizeof(result));

    result.order_id = order_id;
    result.instrument_idx = instrument_idx;
    result.limit_price = price;
    result.last_price = price;  // 设置成交价格
    result.volume = quantity;
    result.direction = convert_side(side);
    result.offset_flag = offset_type::NON;  // 使用NON作为默认值
    result.timestamp = timestamp;  // 使用交易所时间戳
    result.local_timestamp = timestamp;  // 本地时间戳也使用交易所时间戳
    result.trade_id = 0;  // 需要生成唯一ID
    result.fee = fee;  // 设置手续费
    result.order_status = convert_order_status_type(status);  // 使用状态转换函数
    result.is_maker = is_maker;  // 设置是否是做市商



    return result;
}

// 从internal_order创建
internal_trade internal_trade::from_order(const internal_order& order, double trade_quantity) {
    internal_trade result;

    result.order_id = order.order_id;
    result.price = order.executed_price;
    result.quantity = trade_quantity;
    result.side = order.side;
    result.timestamp = order.exchange_timestamp;
    result.instrument_idx = order.instrument_idx;
    result.is_maker = order.is_maker;

    // 计算手续费（按比例分配）
    if (order.executed_quantity > 0) {
        result.fee = order.fee * (trade_quantity / order.executed_quantity);
    } else {
        result.fee = 0.0;
    }

    // 设置订单状态
    result.status = order.status;  // 从原始订单复制状态

    // 设置成交金额
    result.trading_value = trade_quantity * order.executed_price;

    return result;
}

// 状态值构造函数
state_values::state_values()
    : position(0.0)
    , balance(0.0)
    , fee(0.0)
    , pnl(0.0)
    , realized_pnl(0.0)
    , unrealized_pnl(0.0)
    , num_trades(0)
    , trading_volume(0.0)
    , trading_value(0.0)
    , last_price(0.0)
    , cost(0.0) {
}

// 辅助函数：将FastTraderElite的方向类型转换为内部方向类型
side_type convert_direction(direction_type direction) {
    switch (direction) {
        case direction_type::BUY:
            return side_type::buy;
        case direction_type::SELL:
            return side_type::sell;
        default:
            return side_type::none;
    }
}

// 辅助函数：将内部方向类型转换为FastTraderElite的方向类型
direction_type convert_side(side_type side) {
    switch (side) {
        case side_type::buy:
            return direction_type::BUY;
        case side_type::sell:
            return direction_type::SELL;
        default:
            return direction_type::BUY;  // 默认值
    }
}

// 辅助函数：将FastTraderElite的订单类型转换为内部订单类型
order_type convert_order_price_type(price_type type) {
    switch (type) {
        case price_type::LIMIT:
            return order_type::limit;
        case price_type::ANY:
            return order_type::market;
        default:
            return order_type::unsupported;
    }
}

// 辅助函数：将内部订单类型转换为FastTraderElite的订单类型
price_type convert_order_type(order_type type) {
    switch (type) {
        case order_type::limit:
            return price_type::LIMIT;
        case order_type::market:
            return price_type::ANY;
        default:
            return price_type::LIMIT;  // 默认值
    }
}

// 辅助函数：将FastTraderElite的订单状态转换为内部订单状态
order_status convert_order_status(order_status_type status) {
    switch (status) {
        case order_status_type::PENDING:
            return order_status::new_order;
        case order_status_type::PARTIAL_FILLED_ACTIVE:
            return order_status::partially_filled;
        case order_status_type::FILLED:
            return order_status::filled;
        case order_status_type::CANCELLED:
            return order_status::canceled;
        case order_status_type::ERROR_INSERT:
            return order_status::rejected;
        case order_status_type::ERROR_CANCEL:
            return order_status::cancel_rejected;
        default:
            return order_status::none;
    }
}

// 辅助函数：将内部订单状态转换为FastTraderElite的订单状态
order_status_type convert_order_status_type(order_status status) {
    switch (status) {
        case order_status::new_order:
            return order_status_type::PENDING;
        case order_status::partially_filled:
            return order_status_type::PARTIAL_FILLED_ACTIVE;
        case order_status::filled:
            return order_status_type::FILLED;
        case order_status::canceled:
            return order_status_type::CANCELLED;
        case order_status::expired:
            return order_status_type::CANCELLED;  // 过期订单映射为取消状态
        case order_status::rejected:
            return order_status_type::ERROR_INSERT;
        case order_status::cancel_rejected:
            return order_status_type::ERROR_CANCEL;
        case order_status::none:
            return order_status_type::UNKNOWN;
        default:
            return order_status_type::UNKNOWN;
    }
}

} // namespace cpp_backtest
} // namespace fast_trader_elite
