#include <algorithm>
#include <cmath>
#include <ctime>
#include <fast_trader_elite/cpp_backtest/backtest_engine.h>
#include <fast_trader_elite/cpp_backtest/models/model_factory.h>
#include <fast_trader_elite/cpp_backtest/export/exporter_factory.h>
#include <fast_trader_elite/cpp_backtest/backtest_logger.h>
#include <filesystem>
#include <fstream>
#include <iomanip>
#include <iostream>
#include <sstream>

namespace fast_trader_elite {
namespace cpp_backtest {

// 直接模式构造函数
backtest_engine::backtest_engine(
    const std::string &data_file,
    fast_trader_elite::strategy::i_strategy *strategy)
    : mode_(run_mode::DIRECT), data_file_(data_file), output_path_(""), start_time_(0),
      end_time_(0), initial_balance_(10000.0), exchange_type_(""),
      strategy_path_(""), strategy_config_path_(""), latency_type_(""),
      order_latency_(100), cancel_latency_(100), md_latency_(50),
      response_latency_(50), queue_type_(""), queue_power_(2.0), fee_type_(""),
      maker_fee_(0.0002), taker_fee_(0.0004), asset_type_(""),
      contract_value_(1.0),
      reader_(std::make_unique<binary_reader>(data_file)),
      strategy_loader_(), strategy_(strategy),
      fee_model_ptr_(nullptr), queue_model_ptr_(nullptr),
      latency_model_ptr_(nullptr), use_partial_fill_(false),
      current_timestamp_(0), final_balance_(10000.0), exporter_(nullptr) {
}

// 配置模式构造函数
backtest_engine::backtest_engine()
    : mode_(run_mode::CONFIG), data_file_(""), output_path_(""), start_time_(0),
      end_time_(0), initial_balance_(10000.0), exchange_type_(""),
      strategy_path_(""), strategy_config_path_(""), latency_type_(""),
      order_latency_(100), cancel_latency_(100), md_latency_(50),
      response_latency_(50), queue_type_(""), queue_power_(2.0), fee_type_(""),
      maker_fee_(0.0002), taker_fee_(0.0004), asset_type_(""),
      contract_value_(1.0),
      strategy_loader_(), strategy_(nullptr),
      fee_model_ptr_(nullptr), queue_model_ptr_(nullptr),
      latency_model_ptr_(nullptr), use_partial_fill_(false),
      current_timestamp_(0), final_balance_(10000.0), exporter_(nullptr) {}

backtest_engine::~backtest_engine() {
}

bool backtest_engine::load_config(const std::string &config_path) {
  if (mode_ != run_mode::CONFIG) {
    std::cerr << "Cannot load config in direct mode" << std::endl;
    return false;
  }

  try {
    // 打开配置文件
    std::ifstream file(config_path);
    if (!file.is_open()) {
      std::cerr << "Failed to open config file: " << config_path << std::endl;
      return false;
    }

    // 解析JSON
    file >> config_;
    file.close();

    // 解析配置
    return parse_config();
  } catch (const std::exception &e) {
    std::cerr << "Error loading config: " << e.what() << std::endl;
    return false;
  }
}

// 辅助函数：将日期时间字符串转换为毫秒时间戳
int64_t parse_datetime_to_timestamp(const std::string &datetime_str) {
  struct tm tm = {};
  std::istringstream ss(datetime_str);
  ss >> std::get_time(&tm, "%Y-%m-%d %H:%M:%S");

  if (ss.fail()) {
    throw std::runtime_error("Failed to parse datetime string: " +
                             datetime_str);
  }

  // 设置为UTC时间
  tm.tm_isdst = 0;

  // 转换为时间戳（秒）
  time_t timestamp = std::mktime(&tm);

  // 获取本地时区与UTC的差异（秒）
  time_t now = time(nullptr);
  struct tm utc_tm;
  struct tm local_tm;
  gmtime_r(&now, &utc_tm);
  localtime_r(&now, &local_tm);

  // 计算时区差异（秒）
  time_t utc_time = mktime(&utc_tm);
  time_t local_time = mktime(&local_tm);
  int timezone_offset = difftime(local_time, utc_time);

  // 调整时间戳为UTC时间
  timestamp -= timezone_offset;

  // 调整为UTC+8时区（增加8小时）
  timestamp += 8 * 3600;

  // 转换为毫秒
  return static_cast<int64_t>(timestamp) * 1000000000;
}

bool backtest_engine::parse_config() {
  try {
    // 解析回测配置
    if (config_.contains("backtest")) {
      auto &backtest_config = config_["backtest"];

      if (backtest_config.contains("start_time")) {
        std::string start_time_str = backtest_config["start_time"];
        start_time_ = parse_datetime_to_timestamp(start_time_str);
      }

      if (backtest_config.contains("end_time")) {
        std::string end_time_str = backtest_config["end_time"];
        end_time_ = parse_datetime_to_timestamp(end_time_str);
      }

      if (backtest_config.contains("data_file")) {
        data_file_ = backtest_config["data_file"];
      }

      if (backtest_config.contains("output_path")) {
        output_path_ = backtest_config["output_path"];
      }

      if (backtest_config.contains("initial_balance")) {
        initial_balance_ = backtest_config["initial_balance"];
      }

      if (backtest_config.contains("exchange")) {
        exchange_type_ = backtest_config["exchange"];
        use_partial_fill_ = (exchange_type_ == "partial_fill");
      }

      // 解析延迟模型配置
      if (backtest_config.contains("latency")) {
        auto &latency_config = backtest_config["latency"];

        if (latency_config.contains("type")) {
          latency_type_ = latency_config["type"];
        }

        if (latency_config.contains("order_latency")) {
          order_latency_ = latency_config["order_latency"];
        }

        if (latency_config.contains("cancel_latency")) {
          cancel_latency_ = latency_config["cancel_latency"];
        }

        if (latency_config.contains("md_latency")) {
          md_latency_ = latency_config["md_latency"];
        }

        if (latency_config.contains("response_latency")) {
          response_latency_ = latency_config["response_latency"];
        }
      }

      // 解析队列模型配置
      if (backtest_config.contains("queue")) {
        auto &queue_config = backtest_config["queue"];

        if (queue_config.contains("type")) {
          queue_type_ = queue_config["type"];
        }

        if (queue_config.contains("power")) {
          queue_power_ = queue_config["power"];
        }
      }

      // 解析手续费模型配置
      if (backtest_config.contains("fee")) {
        auto &fee_config = backtest_config["fee"];

        if (fee_config.contains("type")) {
          fee_type_ = fee_config["type"];
        }

        if (fee_config.contains("maker_fee")) {
          maker_fee_ = fee_config["maker_fee"];
        }

        if (fee_config.contains("taker_fee")) {
          taker_fee_ = fee_config["taker_fee"];
        }
      }

      // 解析资产类型配置
      if (backtest_config.contains("asset")) {
        auto &asset_config = backtest_config["asset"];

        if (asset_config.contains("type")) {
          asset_type_ = asset_config["type"];
        }

        if (asset_config.contains("contract_value")) {
          contract_value_ = asset_config["contract_value"];
        }
      }
    }

    // 解析品种配置
    if (config_.contains("instruments")) {
      auto &instruments_config = config_["instruments"];
      for (const auto &instr_config : instruments_config) {
        instrument_config config_item;
        config_item.exchange = instr_config.value("exchange", "");
        config_item.instrument = instr_config.value("instrument", "");
        config_item.instrument_idx = instr_config.value("instrument_idx", 0);
        instruments_.push_back(config_item);
      }
    }

    // 解析策略配置
    if (config_.contains("strategy")) {
      auto &strategy_config = config_["strategy"];

      if (strategy_config.contains("path")) {
        strategy_path_ = strategy_config["path"];
      }

      if (strategy_config.contains("config_path")) {
        strategy_config_path_ = strategy_config["config_path"];
      }

      // 从策略路径中提取策略名称
      std::string strategy_name = "strategy";
      if (!strategy_path_.empty()) {
        size_t last_slash = strategy_path_.find_last_of("/\\");
        if (last_slash != std::string::npos) {
          std::string filename = strategy_path_.substr(last_slash + 1);
          size_t lib_prefix = filename.find("lib");
          size_t so_suffix = filename.find(".so");
          if (lib_prefix != std::string::npos &&
              so_suffix != std::string::npos && lib_prefix < so_suffix) {
            strategy_name =
                filename.substr(lib_prefix + 3, so_suffix - lib_prefix - 3);
          }
        }
      }

      // 设置策略名称
      strategy_config_.strategy_instance_name = strategy_name;

      // 设置策略的日志级别（从 strategy.log_level 读取）
      if (strategy_config.contains("log_level")) {
        strategy_config_.log_level = strategy_config["log_level"].get<std::string>();
      } else {
        strategy_config_.log_level = "INFO"; // 默认日志级别
      }

      // 设置策略的日志文件路径
      if (strategy_config.contains("log_file")) {
        // 如果策略配置中有 log_file，直接使用
        strategy_config_.log_file = strategy_config["log_file"].get<std::string>();
      } else {
        // 否则使用默认路径
        strategy_config_.log_file = "../log/strategy/" + strategy_name + ".log";
      }

      // 确保日志目录存在
      std::filesystem::path log_path(strategy_config_.log_file);
      std::filesystem::create_directories(log_path.parent_path());

      // 设置策略配置路径
      strategy_config_.strategy_config_path = strategy_config_path_;
    }

    return true;
  } catch (const std::exception &e) {
    std::cerr << "Error parsing config: " << e.what() << std::endl;
    return false;
  }
}

bool backtest_engine::create_models() {
  try {
    // 使用工厂创建延迟模型
    latency_model_ = latency_model_factory::create(
        latency_type_, order_latency_, response_latency_, md_latency_);
    latency_model_ptr_ = latency_model_.get();

    // 使用工厂创建队列模型
    queue_model_ = queue_model_factory::create(queue_type_, queue_power_);
    queue_model_ptr_ = queue_model_.get();

    // 使用工厂创建手续费模型
    fee_model_ = fee_model_factory::create(fee_type_, maker_fee_, taker_fee_);
    fee_model_ptr_ = fee_model_.get();

    return true;
  } catch (const std::exception &e) {
    std::cerr << "Error creating models: " << e.what() << std::endl;
    return false;
  }
}

bool backtest_engine::init() {
  if (mode_ != run_mode::CONFIG) {
    std::cerr << "Cannot initialize in direct mode" << std::endl;
    return false;
  }

  try {
    // 初始化日志系统
    std::string log_dir = "../log/backtest";
    if (config_.contains("common") && config_["common"].contains("log_dir")) {
      log_dir = config_["common"]["log_dir"].get<std::string>();
    }

    // 创建日志目录
    std::filesystem::create_directories(log_dir);

    // 设置日志文件路径
    std::string log_file = log_dir + "/backtest.log";

    // 设置回测引擎的日志级别（从 common.log_level 读取）
    std::string log_level = "INFO";
    if (config_.contains("common") && config_["common"].contains("log_level")) {
      log_level = config_["common"]["log_level"].get<std::string>();
    }

    // 初始化日志系统
    backtest_logger::get_instance().init(log_file);
    backtest_logger::get_instance().set_level(log_level);

    BT_LOG_INFO("backtest_engine_init config_loaded log_level:{} data_file:{} output_path:{} start_time:{} end_time:{}",
               log_level, data_file_, output_path_, start_time_, end_time_);

    // 创建模型
    if (!create_models()) {
      BT_LOG_ERROR("create_models failed");
      return false;
    }

    // 创建导出器
    exporter_ = exporter_factory::create(exporter_factory::format::CSV);
    if (!exporter_) {
      BT_LOG_ERROR("create_exporter failed");
      std::cerr << "Failed to create exporter" << std::endl;
      return false;
    }



    // 加载策略
    if (!strategy_loader_.load(strategy_path_)) {
      BT_LOG_ERROR("load_strategy_failed path:{}", strategy_path_);
      std::cerr << "Failed to load strategy: " << strategy_path_ << std::endl;
      return false;
    }

    strategy_ = strategy_loader_.get_strategy();
    if (!strategy_) {
      BT_LOG_ERROR("get_strategy_instance_failed");
      std::cerr << "Failed to get strategy instance" << std::endl;
      return false;
    }

    BT_LOG_INFO("strategy_loaded path:{}", strategy_path_);

    // 检查数据文件
    if (instruments_.empty()) {
      BT_LOG_ERROR("no_instruments_configured");
      std::cerr << "No instruments configured" << std::endl;
      return false;
    }

    std::string instruments_info;
    for (const auto& instrument : instruments_) {
      instruments_info += fmt::format("{}:{},", instrument.instrument, instrument.instrument_idx);
    }
    BT_LOG_INFO("instruments_configured list:{}", instruments_info);

    // 使用配置中的数据文件路径
    // 创建二进制读取器
    reader_ = std::make_unique<binary_reader>(data_file_);

    // 创建处理器
    create_processors();

    // 初始化最终余额为初始余额
    final_balance_ = initial_balance_;

    // 创建输出目录
    if (!output_path_.empty()) {
      std::filesystem::create_directories(output_path_);
    }

    return true;
  } catch (const std::exception &e) {
    std::cerr << "Error initializing backtest engine: " << e.what()
              << std::endl;
    return false;
  }
}

void backtest_engine::create_processors() {
  // 创建市场深度，使用order_book实现
  auto ins = reader_->get_instrument();

  depth_ = std::make_unique<order_book>(ins.tick_size, ins.step_size, 5);

  // 创建 OrderBus
  local_to_exchange_bus_ = std::make_unique<order_bus>();
  exchange_to_local_bus_ = std::make_unique<order_bus>();

  // 创建本地处理器
  local_ = std::make_unique<local_processor>(depth_.get(), latency_model_ptr_);

  // 根据配置创建交易所处理器
  if (use_partial_fill_) {
    exchange_ = std::make_unique<partial_fill_exchange>(
        depth_.get(), fee_model_ptr_, queue_model_ptr_, latency_model_ptr_);
  } else {
    exchange_ = std::make_unique<no_partial_fill_exchange>(
        depth_.get(), fee_model_ptr_, queue_model_ptr_, latency_model_ptr_);
  }

  // 设置 OrderBus 连接
  local_->set_order_buses(local_to_exchange_bus_.get(),
                          exchange_to_local_bus_.get());
  exchange_->set_order_buses(exchange_to_local_bus_.get(),
                             local_to_exchange_bus_.get());

  // 设置手续费模型
  local_->set_fee_model(fee_model_ptr_);

  if (!strategy_config_.strategy_instance_name.empty()) {
    // 如果有策略配置，使用带配置的构造函数
    context_ =
        std::make_unique<backtest_context>(local_.get(), strategy_config_);
  } else {
    // 否则使用默认构造函数
    context_ = std::make_unique<backtest_context>(local_.get());
  }

  // 初始化品种信息
  context_->init_instruments(reader_->get_instruments());

  // 设置策略和上下文
  local_->set_strategy(strategy_, context_.get());
}

void backtest_engine::run() {
  BT_LOG_INFO("backtest_run_started");

  // 创建处理器（如果尚未创建）
  if (!local_ || !exchange_ || !context_) {
    BT_LOG_INFO("creating_processors");
    create_processors();
  }

  // 重置状态
  if (exchange_) {
    BT_LOG_INFO("resetting_exchange_state initial_balance:{}", initial_balance_);
    exchange_->get_state()->reset(initial_balance_);
  }

  if (local_) {
    BT_LOG_INFO("resetting_local_state initial_balance:{}", initial_balance_);
    local_->get_state()->reset(initial_balance_);
  }

  // 启动策略
  BT_LOG_INFO("starting_strategy");
  strategy_->on_start(context_.get(), "");

  // 重置读取器
  reader_->reset();
  record_header header;
  std::vector<uint8_t> data; // 暂时使用vector，后续可以考虑更高效的方式

  // 处理数据
  while (reader_->next_record(header, data)) {
    // 检查时间范围（仅在配置模式下）
    if (mode_ == run_mode::CONFIG) {
      if (start_time_ > 0 && header.local_timestamp < start_time_) {
        continue;
      }

      if (end_time_ > 0 && header.local_timestamp > end_time_) {
        break;
      }
    }

    // 处理记录
    if (header.flags & LOCAL_EVENT) {
      current_timestamp_ = header.local_timestamp;
      context_->set_current_timestamp(current_timestamp_);

      // 根据回报发送
      local_->process_recv_order(current_timestamp_);

      // 回调行情等接口
      backtest_error error = local_->process(header, data);
      if (error != backtest_error::none) {
        BT_LOG_ERROR("process_local_event_failed error:{} timestamp:{}", static_cast<int>(error), current_timestamp_);
        break;
      }

      context_->process_timers();
    }
    if (header.flags & EXCH_EVENT) {
      // 更新数据 撮合已有的订单
      current_timestamp_ = header.exch_timestamp;

      // 处理交易所事件
      backtest_error error = exchange_->process(header, data);
      if (error != backtest_error::none) {
        BT_LOG_ERROR("process_exchange_event_failed error:{} timestamp:{}", static_cast<int>(error), current_timestamp_);
        break;
      }

      // 撮合新指令
      exchange_->process_recv_order(current_timestamp_);
    }

    // 记录状态（仅在配置模式下）
    if (mode_ == run_mode::CONFIG) {
      for (const auto &instrument : instruments_) {
        const state_values &values =
            local_->get_state_values(instrument.instrument_idx);

        // 记录到 backtest_data
        double mid_price = 0.0;
        if (depth_) {
          double best_bid = depth_->tick_to_price(depth_->best_bid_tick());
          double best_ask = depth_->tick_to_price(depth_->best_ask_tick());
          if (best_bid > 0.0 && best_ask > 0.0) {
            mid_price = (best_bid + best_ask) / 2.0;
          } else if (best_bid > 0.0) {
            mid_price = best_bid;
          } else if (best_ask > 0.0) {
            mid_price = best_ask;
          }
        }

        // 计算总权益（净值）
        equity_value_ = local_->get_equity(instrument.instrument_idx, mid_price);

        // 创建记录项
        record_item item;
        item.timestamp = current_timestamp_;
        item.price = mid_price;
        item.position = values.position;
        item.balance = values.balance;
        item.equity = equity_value_;  // 使用计算的净值
        item.fee = values.fee;
        item.num_trades = values.num_trades;
        item.trading_volume = values.trading_volume;
        item.trading_value = values.trading_value;
        item.cost = values.cost;

        // 添加到 backtest_data
        data_.add_record(instrument.instrument_idx, item);

        // 记录成交数据
        const auto& trades = local_->get_last_trades();
        for (const auto& trade : trades) {
          if (trade.instrument_idx == instrument.instrument_idx) {
            data_.add_trade(instrument.instrument_idx, trade);
          }
        }

        // 清除最近的交易，避免重复记录
        local_->clear_last_trades();
      }
    }
  }

  // 停止策略
  BT_LOG_INFO("stopping_strategy");
  strategy_->on_stop(context_.get());

  // 导出数据（仅在配置模式下）
  if (mode_ == run_mode::CONFIG) {
    // 更新最终余额
    if (!instruments_.empty() && local_) {
      const state_values &values = local_->get_state_values(instruments_[0].instrument_idx);
      final_balance_ = values.balance;
    }

    // 导出数据（如果设置了输出路径和导出器）
    if (!output_path_.empty() && exporter_) {
      // 创建导出目录
      std::string export_dir = output_path_ + "/export/";
      std::filesystem::create_directories(export_dir);
      BT_LOG_INFO("exporting_data to_directory:{}", export_dir);

      // // 导出记录数据
      // if (export_records(export_dir + "records")) {
      //   BT_LOG_INFO("records_exported path:{}", export_dir + "records");
      //   std::cout << "Records exported to " << export_dir << "records" << std::endl;
      // } else {
      //   BT_LOG_ERROR("records_export_failed path:{}", export_dir + "records");
      //   std::cerr << "Failed to export records to " << export_dir << "records" << std::endl;
      // }

      // 导出成交数据
      if (export_trades(export_dir + "trades")) {
        BT_LOG_INFO("trades_exported path:{}", export_dir + "trades");
        std::cout << "Trades exported to " << export_dir << "trades" << std::endl;
      } else {
        BT_LOG_ERROR("trades_export_failed path:{}", export_dir + "trades");
        std::cerr << "Failed to export trades to " << export_dir << "trades" << std::endl;
      }

      // 导出 PNL 数据
      if (export_pnl(export_dir + "pnl")) {
        BT_LOG_INFO("pnl_exported path:{}", export_dir + "pnl");
        std::cout << "PNL data exported to " << export_dir << "pnl" << std::endl;
      } else {
        BT_LOG_ERROR("pnl_export_failed path:{}", export_dir + "pnl");
        std::cerr << "Failed to export PNL data to " << export_dir << "pnl" << std::endl;
      }
    } else {
      BT_LOG_WARN("export_skipped reason:{}", output_path_.empty() ? "output_path_empty" : "exporter_not_set");
      std::cerr << "Export skipped: " << (output_path_.empty() ? "output_path is empty" : "exporter is not set") << std::endl;
    }
  }

  BT_LOG_INFO("backtest_run_completed");
}

bool backtest_engine::process_record(const record_header &header,
                                     const std::vector<uint8_t> &data) {
  // 根据记录类型处理
  if (header.flags & LOCAL_EVENT) {
    // 本地事件
    backtest_error error = local_->process(header, data);
    if (error != backtest_error::none) {
      std::cerr << "Error processing local event: " << static_cast<int>(error)
                << std::endl;
      return false;
    }
  } else if (header.flags & EXCH_EVENT) {
    // 交易所事件
    backtest_error error = exchange_->process(header, data);
    if (error != backtest_error::none) {
      std::cerr << "Error processing exchange event: "
                << static_cast<int>(error) << std::endl;
      return false;
    }
  }

  return true;
}



void backtest_engine::print_results() const {
  if (mode_ != run_mode::CONFIG) {
    std::cerr << "Results only available in config mode" << std::endl;
    return;
  }

  std::cout << "===== Backtest Results =====" << std::endl;

  // 基本信息
  std::cout << "\n--- Basic Info ---" << std::endl;
  std::cout << "Initial Balance: " << initial_balance_ << std::endl;
  std::cout << "Final equity: " << equity_value_ << std::endl;
  std::cout << "Start Time: " << start_time_ << std::endl;
  std::cout << "End Time: " << end_time_ << std::endl;
  std::cout << "Number of assets: " << data_.num_assets() << std::endl;

  // 打印每个资产的详细指标
  for (const auto& instrument : instruments_) {
    uint16_t asset_no = instrument.instrument_idx;
    std::cout << "\n--- Asset: " << instrument.instrument << " (ID: " << asset_no << ") ---" << std::endl;

    // 收益相关指标
    std::cout << "Return: " << data_.get_return(asset_no) * 100 << "%" << std::endl;
    std::cout << "Annual Return: " << data_.get_annual_return(asset_no) * 100 << "%" << std::endl;
    std::cout << "Max Drawdown: " << data_.get_max_drawdown(asset_no) * 100 << "%" << std::endl;
    std::cout << "Return/MDD: " << data_.get_return_over_mdd(asset_no) << std::endl;

    // 风险调整指标
    std::cout << "Sharpe Ratio: " << data_.get_sharpe_ratio(asset_no) << std::endl;
    std::cout << "Sortino Ratio: " << data_.get_sortino_ratio(asset_no) << std::endl;

    // 交易相关指标
    std::cout << "Win Rate: " << data_.get_win_rate(asset_no) * 100 << "%" << std::endl;
    std::cout << "Profit/Loss Ratio: " << data_.get_profit_loss_ratio(asset_no) << std::endl;
    std::cout << "Avg Position Time: " << data_.get_avg_position_time(asset_no) / 60 << " minutes" << std::endl;
    std::cout << "Total Trades: " << data_.get_total_trades(asset_no) << std::endl;
    std::cout << "Trading Value USDT: " << data_.get_total_trading_value(asset_no) << std::endl;
    std::cout << "Daily Trading Value USDT: " << data_.get_daily_trading_value(asset_no) << std::endl;
    std::cout << "Max Leverage: " << data_.get_max_leverage(asset_no) << "x" << std::endl;

    // 最终状态
    if (local_) {
      const state_values& values = local_->get_state_values(asset_no);
      std::cout << "Final Position: " << values.position << std::endl;
      std::cout << "Final Balance: " << values.balance << std::endl;
      std::cout << "Total Fee: " << values.fee << std::endl;
      std::cout << "Total pnl: " << values.pnl << std::endl;

    }
  }

  std::cout << "\nData saved for further analysis" << std::endl;
  std::cout << "\n===========================" << std::endl;
}

const std::vector<internal_trade> &
backtest_engine::last_trades(size_t asset_no) const {
  // 从 local_processor 获取最近的交易
  if (local_) {
    return local_->get_last_trades();
  }

  // 如果 local_processor 不存在，返回空列表
  static const std::vector<internal_trade> empty_trades;
  return empty_trades;
}

void backtest_engine::clear_last_trades(size_t asset_no) {
  // 调用 local_processor 的 clear_last_trades 方法
  if (local_) {
    local_->clear_last_trades();
  }
}

} // namespace cpp_backtest
} // namespace fast_trader_elite
