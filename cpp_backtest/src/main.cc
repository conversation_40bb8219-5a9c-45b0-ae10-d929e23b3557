#include <fast_trader_elite/cpp_backtest/backtest_engine.h>
#include <iostream>
#include <string>
#include <filesystem>

int main(int argc, char** argv) {
    // 解析命令行参数
    std::string config_path = "../config/backtest_config.json";

    if (argc > 1) {
        config_path = argv[1];
    }

    std::cout << "Using config file: " << config_path << std::endl;

    // 创建统一回测引擎
    fast_trader_elite::cpp_backtest::backtest_engine engine;

    // 加载配置
    if (!engine.load_config(config_path)) {
        std::cerr << "Failed to load config: " << config_path << std::endl;
        return 1;
    }

    // 初始化引擎
    if (!engine.init()) {
        std::cerr << "Failed to initialize backtest engine" << std::endl;
        return 1;
    }

    // 运行回测
    engine.run();

    // 输出结果
    engine.print_results();

    return 0;
}
