#include <fast_trader_elite/cpp_backtest/market/order_book.h>
#include <cmath>
#include <cstring>

namespace fast_trader_elite {
namespace cpp_backtest {

order_book::order_book(double tick_size, double lot_size, size_t depth_levels)
    : tick_size_(tick_size)
    , lot_size_(lot_size)
    , depth_levels_(depth_levels)
    , timestamp_(0)
    , cache_valid_(false) {
    // 初始化深度数据
    std::memset(&depth_, 0, sizeof(depth_));
}

int64_t order_book::best_bid_tick() const {
    if (depth_.depth == 0 || depth_.bid_volume[0] <= 0.0) {
        return std::numeric_limits<int64_t>::min();
    }
    return price_to_tick(depth_.bid_price[0]);
}

int64_t order_book::best_ask_tick() const {
    if (depth_.depth == 0 || depth_.ask_volume[0] <= 0.0) {
        return std::numeric_limits<int64_t>::max();
    }
    return price_to_tick(depth_.ask_price[0]);
}

double order_book::bid_qty(int64_t price_tick) const {
    double price = tick_to_price(price_tick);
    for (int i = 0; i < depth_.depth; ++i) {
        if (std::abs(depth_.bid_price[i] - price) < tick_size_ * 0.1) {
            return depth_.bid_volume[i];
        }
    }
    return 0.0;
}

double order_book::ask_qty(int64_t price_tick) const {
    double price = tick_to_price(price_tick);
    for (int i = 0; i < depth_.depth; ++i) {
        if (std::abs(depth_.ask_price[i] - price) < tick_size_ * 0.1) {
            return depth_.ask_volume[i];
        }
    }
    return 0.0;
}

void order_book::update_cache() const {
    if (cache_valid_) {
        return;
    }

    // 清空缓存
    bid_price_ticks_.clear();
    ask_price_ticks_.clear();
    bid_quantities_.clear();
    ask_quantities_.clear();

    // 填充买单缓存
    bid_price_ticks_.reserve(depth_.depth);
    bid_quantities_.reserve(depth_.depth);
    for (int i = 0; i < depth_.depth; ++i) {
        if (depth_.bid_volume[i] > 0.0) {
            bid_price_ticks_.push_back(price_to_tick(depth_.bid_price[i]));
            bid_quantities_.push_back(depth_.bid_volume[i]);
        }
    }

    // 填充卖单缓存
    ask_price_ticks_.reserve(depth_.depth);
    ask_quantities_.reserve(depth_.depth);
    for (int i = 0; i < depth_.depth; ++i) {
        if (depth_.ask_volume[i] > 0.0) {
            ask_price_ticks_.push_back(price_to_tick(depth_.ask_price[i]));
            ask_quantities_.push_back(depth_.ask_volume[i]);
        }
    }

    cache_valid_ = true;
}

const std::vector<int64_t>& order_book::bid_prices() const {
    update_cache();
    return bid_price_ticks_;
}

const std::vector<int64_t>& order_book::ask_prices() const {
    update_cache();
    return ask_price_ticks_;
}

const std::vector<double>& order_book::bid_qtys() const {
    update_cache();
    return bid_quantities_;
}

const std::vector<double>& order_book::ask_qtys() const {
    update_cache();
    return ask_quantities_;
}

void order_book::clear() {
    std::memset(&depth_, 0, sizeof(depth_));
    cache_valid_ = false;
}

void order_book::clear_depth(side_type side, double clear_upto_price) {
    if (side == side_type::buy || side == side_type::none) {
        // 清除买单深度
        if (clear_upto_price <= 0.0) {
            for (int i = 0; i < depth_.depth; ++i) {
                depth_.bid_price[i] = 0.0;
                depth_.bid_volume[i] = 0.0;
            }
        } else {
            for (int i = 0; i < depth_.depth; ++i) {
                if (depth_.bid_price[i] >= clear_upto_price) {
                    depth_.bid_price[i] = 0.0;
                    depth_.bid_volume[i] = 0.0;
                }
            }
        }
    }

    if (side == side_type::sell || side == side_type::none) {
        // 清除卖单深度
        if (clear_upto_price <= 0.0) {
            for (int i = 0; i < depth_.depth; ++i) {
                depth_.ask_price[i] = 0.0;
                depth_.ask_volume[i] = 0.0;
            }
        } else {
            for (int i = 0; i < depth_.depth; ++i) {
                if (depth_.ask_price[i] <= clear_upto_price) {
                    depth_.ask_price[i] = 0.0;
                    depth_.ask_volume[i] = 0.0;
                }
            }
        }
    }

    // 使缓存失效
    cache_valid_ = false;
}

std::tuple<int64_t, int64_t, int64_t, double, double, int64_t>
order_book::update_bid_depth(double price, double qty, int64_t timestamp) {
    int64_t price_tick = price_to_tick(price);
    int64_t prev_best_bid_tick = best_bid_tick();
    double prev_qty = bid_qty(price_tick);

    // 查找价格对应的档位
    int index = -1;
    for (int i = 0; i < depth_.depth; ++i) {
        if (std::abs(depth_.bid_price[i] - price) < tick_size_ * 0.1) {
            index = i;
            break;
        }
    }

    if (qty <= 0.0) {
        // 移除价格级别
        if (index >= 0) {
            // 将后面的档位向前移动
            for (int i = index; i < depth_.depth - 1; ++i) {
                depth_.bid_price[i] = depth_.bid_price[i + 1];
                depth_.bid_volume[i] = depth_.bid_volume[i + 1];
            }
            // 清空最后一个档位
            depth_.bid_price[depth_.depth - 1] = 0.0;
            depth_.bid_volume[depth_.depth - 1] = 0.0;
        }
    } else {
        if (index >= 0) {
            // 更新现有档位
            depth_.bid_volume[index] = qty;
        } else {
            // 添加新的档位，需要找到正确的位置
            index = depth_.depth; // 默认添加到最后
            for (int i = 0; i < depth_.depth; ++i) {
                if (depth_.bid_volume[i] <= 0.0 || price > depth_.bid_price[i]) {
                    index = i;
                    break;
                }
            }

            if (index < static_cast<int>(depth_levels_)) {
                // 将后面的档位向后移动
                for (int i = static_cast<int>(depth_.depth) - 1; i > index; --i) {
                    depth_.bid_price[i] = depth_.bid_price[i - 1];
                    depth_.bid_volume[i] = depth_.bid_volume[i - 1];
                }
                // 插入新的档位
                depth_.bid_price[index] = price;
                depth_.bid_volume[index] = qty;
                // 更新深度
                if (depth_.depth < depth_levels_) {
                    depth_.depth++;
                }
            }
        }
    }

    int64_t curr_best_bid_tick = best_bid_tick();
    timestamp_ = timestamp;

    // 使缓存失效
    cache_valid_ = false;

    return std::make_tuple(price_tick, prev_best_bid_tick, curr_best_bid_tick, prev_qty, qty, timestamp);
}

std::tuple<int64_t, int64_t, int64_t, double, double, int64_t>
order_book::update_ask_depth(double price, double qty, int64_t timestamp) {
    int64_t price_tick = price_to_tick(price);
    int64_t prev_best_ask_tick = best_ask_tick();
    double prev_qty = ask_qty(price_tick);

    // 查找价格对应的档位
    int index = -1;
    for (int i = 0; i < depth_.depth; ++i) {
        if (std::abs(depth_.ask_price[i] - price) < tick_size_ * 0.1) {
            index = i;
            break;
        }
    }

    if (qty <= 0.0) {
        // 移除价格级别
        if (index >= 0) {
            // 将后面的档位向前移动
            for (int i = index; i < depth_.depth - 1; ++i) {
                depth_.ask_price[i] = depth_.ask_price[i + 1];
                depth_.ask_volume[i] = depth_.ask_volume[i + 1];
            }
            // 清空最后一个档位
            depth_.ask_price[depth_.depth - 1] = 0.0;
            depth_.ask_volume[depth_.depth - 1] = 0.0;
        }
    } else {
        if (index >= 0) {
            // 更新现有档位
            depth_.ask_volume[index] = qty;
        } else {
            // 添加新的档位，需要找到正确的位置
            index = depth_.depth; // 默认添加到最后
            for (int i = 0; i < depth_.depth; ++i) {
                if (depth_.ask_volume[i] <= 0.0 || price < depth_.ask_price[i]) {
                    index = i;
                    break;
                }
            }

            if (index < static_cast<int>(depth_levels_)) {
                // 将后面的档位向后移动
                for (int i = static_cast<int>(depth_.depth) - 1; i > index; --i) {
                    depth_.ask_price[i] = depth_.ask_price[i - 1];
                    depth_.ask_volume[i] = depth_.ask_volume[i - 1];
                }
                // 插入新的档位
                depth_.ask_price[index] = price;
                depth_.ask_volume[index] = qty;
                // 更新深度
                if (depth_.depth < depth_levels_) {
                    depth_.depth++;
                }
            }
        }
    }

    int64_t curr_best_ask_tick = best_ask_tick();
    timestamp_ = timestamp;

    // 使缓存失效
    cache_valid_ = false;

    return std::make_tuple(price_tick, prev_best_ask_tick, curr_best_ask_tick, prev_qty, qty, timestamp);
}

int64_t order_book::price_to_tick(double price) const {
    return static_cast<int64_t>(std::round(price / tick_size_));
}

double order_book::tick_to_price(int64_t tick) const {
    return tick * tick_size_;
}

void order_book::update_from_snapshot(const depth_market_data_field* depth, int64_t timestamp) {
    // 直接复制深度数据
    std::memcpy(&depth_, depth, sizeof(depth_market_data_field));

    // 更新时间戳
    timestamp_ = timestamp;

    // 使缓存失效
    cache_valid_ = false;
}

} // namespace cpp_backtest
} // namespace fast_trader_elite
